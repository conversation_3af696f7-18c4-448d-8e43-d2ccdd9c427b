import 'dart:convert';
import 'dart:io';
import 'dart:async';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_net_signature/flutter_net_signature_interceptor.dart';
import 'package:niim_login/login_plugin/login_plugin_api.dart';
import 'package:niim_login/login_plugin/macro/constant.dart';
import 'package:niimbot_dio_http_cache/dio_http_cache.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:text/business/user/user_login_helper.dart';
import 'package:text/network/http_api.dart';
import 'package:text/network/interceptor/encrypt_interceptor.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';

import '../app_config.dart';
import '../application.dart';
import '../network_connectivity.dart';
import 'entity/base_entity.dart';
import 'interceptor/error_handle.dart';
import 'interceptor/interceptor.dart';
import 'package:pool/pool.dart';

enum Method { get, post, put, patch, delete, head }

class DioUtils {
  static DioUtils? _singleton;
  static BuildContext? _context;
  static bool hasInitContext = false;
  static String middleUrl = "https://homo.jc-saas.com";
  static bool proxy = true;
  static bool productProxy = true;

  //
  // static late BuildContext _dialogContext;

  static DioUtils get instance => DioUtils();

  static final Pool _pool = Pool(5, timeout: Duration(seconds: 5));

  /// 初始化传一个context（避免每次请求传入，直接初始化一个）
  static initContext(BuildContext? context) {
    _context = context;
    hasInitContext = true;
  }

  /// 初始化传一个context（避免每次请求传入，直接初始化一个）
  // static initdialogContext(BuildContext context) {
  //   _dialogContext = context;
  // }

  factory DioUtils() {
    if (_singleton == null) {
      _singleton = DioUtils._internal(context: _context);
    }
    return _singleton!;
  }

  static late Dio _dio;

  Dio getDio() {
    return _dio;
  }

  configCustomHeaders(Map<String, String> headers) {
    _dio.options.headers.addAll(headers);
  }

  setAuthorization(String accessToken) {
    _dio.options.headers["Authorization"] = "Bearer $accessToken";
  }

  removeAuthorization() {
    if (_dio.options.headers.containsKey("Authorization")) {
      _dio.options.headers.remove("Authorization");
    }
  }

  DioUtils._internal({required BuildContext? context}) {
    var apiBaseUrl = "https://print.niimbot.com/api";
    if (Application.appEnv != AppEnv.production) {
      apiBaseUrl = "https://print.jc-test.cn/api";
    }
    var options = BaseOptions(
      // baseUrl: AppConfig.of(context)!.apiBaseUrl,
      baseUrl: apiBaseUrl,
      connectTimeout: Duration(seconds: 5),
      receiveTimeout: Duration(seconds: 5),
      sendTimeout: Duration(seconds: 5),
      contentType: ContentType.json.value,
      responseType: ResponseType.plain,
      validateStatus: (status) {
        // 不使用http状态码判断状态，使用AdapterInterceptor来处理（适用于标准REST风格）
        return true;
      },
    );
    _dio = Dio(options);

    (_dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate = (client) {
      client.idleTimeout = Duration(minutes: 5); // 设置 keep-alive 的超时时间
      client.connectionTimeout = Duration(seconds: 5);
      client.maxConnectionsPerHost = 5; // 每个主机最大连接数
      if (proxy || productProxy) {
        // if (AppConfig.of(context)!.proxy || AppConfig.of(context)!.productProxy) {
        //debug和product有一个开启代理,那么代理生效
        SharedPreferences.getInstance().then((sp) {
          // String? proxyIp = "************:9090";
          String? proxyIp = "";
          if (null != sp.get("proxy")) {
            proxyIp = sp.get("proxy") as String;
          }

          ///设置代理用来调试应用
          if (proxyIp.isNotEmpty) {
            client.findProxy = (url) {
              return 'PROXY $proxyIp';
            };

            ///抓Https包设置
            client.badCertificateCallback = (X509Certificate cert, String host, int port) => Platform.isAndroid;
          } else if (kDebugMode) {
            client.badCertificateCallback = (X509Certificate cert, String host, int port) => true;
          }
        });
      }
      return client;
    };

    /// 防抖拦截器，防止短时间内重复请求
    _dio.interceptors.add(RequestDeduplicationInterceptor());
    /// 统一添加请求头、
    _dio.interceptors..add(AuthInterceptor(context));

    /// 打印Log(生产模式去除)
    if (!Constant.inProduction) {
      _dio.interceptors.add(LoggingInterceptor());
    }
    _dio.interceptors.add(DioCacheManager(CacheConfig()).interceptor);

    _dio.interceptors.add(EncryptInterceptor());

    /// 签名拦截
    _dio.interceptors.add(FlutterNetSignatureInterceptor());

    _dio.interceptors.add(ServerTimeInterceptor());

    /// 网关熔断拦截器
    _dio.interceptors.add(OutOfServiceInterceptor());

  }


  static Future<BaseEntity<T>> _request<T>(String method, String url,
      {dynamic data,
      Map<String, dynamic>? queryParameters,
      String? baseUrl,
      bool? isShopApi,
      CancelToken? cancelToken,
      Options? options,
      bool? needLogin,
      bool needEncrypt = false,
      bool useServerTime = false}) async {
    final key = _makeRequestKey(url, queryParameters ?? data);
    // 去重逻辑已迁移到拦截器，这里无需处理
    cancelToken ??= CancelToken();

    if (isShopApi == true) {
      debugPrint("使用商城基础域名");
      _dio.options.baseUrl = Application.shopMallBaseUrl;
    } else if (baseUrl != null && baseUrl.isNotEmpty && baseUrl.startsWith("http")) {
      _dio.options.baseUrl = baseUrl;
    } else {
      var apiBaseUrl = "https://print.niimbot.com/api";
      if (Application.appEnv != AppEnv.production) {
        apiBaseUrl = "https://print.jc-test.cn/api";
      }
      _dio.options.baseUrl = apiBaseUrl;
      // _dio.options.baseUrl = AppConfig.of(_context)!.apiBaseUrl;
      // Log.d("baseUrl:${AppConfig.of(_context)!.apiBaseUrl} Url:$url NeedLogin:$needLogin");
    }
    _dio.options.extra = {
      'needLogin': needLogin,
      "isShopApi": isShopApi,
      "needEncrypt": needEncrypt,
      "useServerTime": useServerTime
    };
    // Log.d("请求方法:$method, " +
    //     "\n请求地址:" +
    //     "${_dio.options.baseUrl}$url," +
    //     "\n请求data: $data, " +
    //     "\n请求param： $queryParameters, " +
    //     "\n请求options： $options, " +
    //     "\n请求header：${_dio.options.headers}");

    var response = await _dio.request(
        url,
        data: data,
        queryParameters: queryParameters,
        options: _checkOptions(method, options),
        cancelToken: cancelToken,
      );
    try {
      //线号机模板导入Excel的情况下：线号机获取模板详情fetchTemplateDetailById、通过分享码decode接口shareDecode返回的response会很大
      //控制台输出response会卡住主线程
      // debugPrint("响应值：${response.data.toString()}");
      debugPrint("reponse headers：${response.headers}");

      /// 集成测试无法使用 isolate
      // Map<String, dynamic> _map =
      //     Constant.isTest ? parseData(response.data.toString()) : await compute(parseData, response.data.toString());
      var result =
          Constant.isTest ? parseData(response.data.toString()) : await compute(parseData, response.data.toString());
      // Log.d("baseUrl: $baseUrl, oss: ${AppConfig.of(_context)?.middleUrl}");
      debugPrint("baseUrl: $baseUrl, oss: ${middleUrl}");
      if (baseUrl == middleUrl) {
        return BaseEntity(200, "", response.data);
      }
      return BaseEntity<T>.fromJson(result);
    } catch (e) {
      debugPrint(e.toString());
      if (e is TypeError) {
        debugPrint(e.toString());
      }
      return BaseEntity(ExceptionHandle.parse_error, /*loginIntl("cxy000512")*/ intlanguage("app00629", "数据错误"), null);
    }
  }

  static String _makeRequestKey(String url, dynamic params) {
    return '$url:${jsonEncode(params ?? {})}';
  }

  static Options _checkOptions(method, options) {
    options ??= Options();
    options.method = method;
    return options;
  }

  Future? requestNetwork<T>(Method method, Map<String, dynamic> urlObject,
      {Function(T? t)? onSuccess,
      Function(List<T> list)? onSuccessList,
      Function(int code, String msg)? onError,
      Function(int code, String msg)? onNoFilterError,
      String? baseUrl,
      bool isShopApi = false,
      dynamic params,
      bool needLogin = true, //如果此处需要登录,那么忽略接口配置的登录选项,因为有些接口需要token和不需要token是公用的,比如我的云生活和他的云生活
      Map<String, dynamic>? queryParameters,
      CancelToken? cancelToken,
      Options? options,
      bool needEncrypt = false,
      bool isList = false,
      bool useServerTime = false}) {
    if (NetworkConnectivity.instance.isReachable) {
      debugPrint("请求参数$urlObject");
      bool needLogin_ = urlObject['needLogin'] as bool;
      if (needLogin_) {
        if (!Application.hasToken() || null == urlObject['path']) {
          debugPrint("请求:" + "${urlObject['path']}" + "需要token,但是用户没有登录!");
          if (onError != null) {
            onError(401, "");
          }
          return null;
        }
      }

      String path = urlObject['path'] as String;
      String m = _getRequestMethod(method);
      return _request<T>(m, path,
              data: params,
              queryParameters: queryParameters,
              options: options,
              isShopApi: isShopApi,
              baseUrl: urlObject['baseUrl'] as String?,
              cancelToken: cancelToken,
              needLogin: needLogin_,
              needEncrypt: needEncrypt,
              useServerTime: useServerTime)
          .then((BaseEntity<T> result) {
        if (path == MachineAliasApi.modifyAlias['path']) {
          String? errorCode = result.errorCode?.toLowerCase();
          if (errorCode == "machine_alias_violation_err") {
            _onError(result.code, "app100002004", onError, onNoFilterError);
            return;
          }
        }
        if (result.code == 200 || result.code == 1 || result.code == 0) {
          if (isList) {
            if (onSuccessList != null) {
              onSuccessList(result.listData);
            }
          } else {
            if (onSuccess != null) {
              onSuccess(result.data);
            }
          }
        } else {
          debugPrint('请求数据Code不匹配}');
          if (path == MachineAliasApi.modifyAlias['path']) {
            _onError(result.code, "app100002003", onError, onNoFilterError);
          } else {
            _onError(result.code, result.message ??= "", onError, onNoFilterError);
          }
        }
      }, onError: (e, _) {
        _cancelLogPrint(e, urlObject['path'] as String);
        // if (!CancelToken.isCancel(e)) {
        //   NetError error = ExceptionHandle.handleException(e);
        //   _onError(error.code, error.msg, onError, onNoFilterError);
        // }
        NetError error = ExceptionHandle.handleException(e);
        _onError(error.code, error.msg, onError, onNoFilterError);
      });
    } else {
      if (onError != null) {
        onError(9001, /*loginIntl("cxy000494")*/ intlanguage("app100000354", "网络异常"));
      }
    }
    return null;
  }

  _cancelLogPrint(dynamic e, String url) {
    if (e is DioError && CancelToken.isCancel(e)) {
      debugPrint('取消请求接口： $url');
    }
  }

  _onError(
      int code, String msg, Function(int code, String mag)? onError, Function(int code, String msg)? onNoFilterError) {
    debugPrint('接口请求异常： code: $code, msg: $msg');
    if (code == 401 || code == 403 || code == 5001 || code == 9999) {
      if (code == 401) {
        msg = /*loginIntl("cxy000632")*/ intlanguage('app100000342', '您的登录信息异常，请重新登录后再试。');
        Application.clearUserInfo().then((value) {
          ToNativeMethodChannel().logout(1);
          LoginPluginApi.loginOutClearCache();
          String title = intlanguage('app100000342', '您的登录信息异常，请重新登录后再试。');
          String cancelDes = intlanguage('app00030', '取消');
          String confirmDes = intlanguage('app01191', '立即登录');
          if (_context != null) {
            UserLoginHelper().confirmLogin(_context!, title, cancelDes, confirmDes, loginSucceed: () {});
          }
        });
      }
      if (onError != null) {
        onError(code, "");
      }
      if (onNoFilterError != null) {
        onNoFilterError(code, msg);
      }
      if (Application.hasToken()) {
        // TODO: 2022/8/31 Ice_Liu 注册登录修改待完成
        // loginInvalid(msg: msg);
      }
    } else {
      if (onError != null) {
        onError(code, msg);
      }
      if (onNoFilterError != null) {
        onNoFilterError(code, msg);
      }
    }
  }

  static bool canShowed = true;

  String _getRequestMethod(Method method) {
    String m;
    switch (method) {
      case Method.get:
        m = 'GET';
        break;
      case Method.post:
        m = 'POST';
        break;
      case Method.put:
        m = 'PUT';
        break;
      case Method.patch:
        m = 'PATCH';
        break;
      case Method.delete:
        m = 'DELETE';
        break;
      case Method.head:
        m = 'HEAD';
        break;
    }
    return m;
  }
}

dynamic parseData(String data) {
  return json.decode(data);
}
