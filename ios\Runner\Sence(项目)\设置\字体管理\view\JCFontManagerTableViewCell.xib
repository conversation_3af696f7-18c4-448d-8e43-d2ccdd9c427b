<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="101" id="KGk-i7-Jjw" customClass="JCFontManagerTableViewCell">
            <rect key="frame" x="0.0" y="0.0" width="400" height="101"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="400" height="101"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <button opaque="NO" tag="1" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6aG-fr-UFI">
                        <rect key="frame" x="360" y="30.5" width="40" height="40"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="40" id="BXt-lI-mTX"/>
                            <constraint firstAttribute="width" constant="40" id="e7D-ac-bjg"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                        <state key="normal">
                            <color key="titleColor" red="0.40000000000000002" green="0.40000000000000002" blue="0.40000000000000002" alpha="1" colorSpace="calibratedRGB"/>
                        </state>
                        <connections>
                            <action selector="fontOperateEvent:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="VCl-c4-iQ5"/>
                        </connections>
                    </button>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Z1x-pH-Frb">
                        <rect key="frame" x="0.0" y="100" width="400" height="1"/>
                        <color key="backgroundColor" red="0.92941176470588238" green="0.92941176470588238" blue="0.92941176470588238" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="AM7-vB-0WK"/>
                        </constraints>
                    </view>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1xg-6B-n4d">
                        <rect key="frame" x="0.0" y="0.0" width="400" height="1"/>
                        <color key="backgroundColor" red="0.92941176470588238" green="0.92941176470588238" blue="0.92941176470588238" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="pOM-c3-NbN"/>
                        </constraints>
                    </view>
                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillProportionally" alignment="top" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="ssn-B6-dHU">
                        <rect key="frame" x="17" y="30" width="243" height="41.5"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" alignment="center" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="A5S-aR-CVg">
                                <rect key="frame" x="0.0" y="0.0" width="243" height="24"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="pnd-MM-6YX">
                                        <rect key="frame" x="0.0" y="3" width="128" height="18"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="128" id="azt-d0-TFZ"/>
                                            <constraint firstAttribute="height" constant="18" id="y6X-29-lZk"/>
                                        </constraints>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="1000" verticalHuggingPriority="251" horizontalCompressionResistancePriority="1000" text="adsad" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xp7-xh-nMM">
                                        <rect key="frame" x="133" y="0.5" width="46" height="22.5"/>
                                        <fontDescription key="fontDescription" name="PingFangSC-Medium" family="PingFang SC" pointSize="16"/>
                                        <color key="textColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="fontVipYellow" translatesAutoresizingMaskIntoConstraints="NO" id="lb3-rc-WVX">
                                        <rect key="frame" x="184" y="0.0" width="24" height="24"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="24" id="CIZ-VM-Pe8"/>
                                            <constraint firstAttribute="height" constant="24" id="bef-nc-PQY"/>
                                        </constraints>
                                    </imageView>
                                    <button opaque="NO" tag="2" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Jzi-9d-RoA">
                                        <rect key="frame" x="213" y="2" width="30" height="20"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="30" id="i26-ox-BWo"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                        <state key="normal" image="fontInfo">
                                            <color key="titleColor" red="0.40000000000000002" green="0.40000000000000002" blue="0.40000000000000002" alpha="1" colorSpace="calibratedRGB"/>
                                        </state>
                                        <connections>
                                            <action selector="fontOperateEvent:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="OID-i4-5qz"/>
                                        </connections>
                                    </button>
                                </subviews>
                            </stackView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="adsad" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lcV-Ya-ODt">
                                <rect key="frame" x="0.0" y="26" width="31.5" height="15.5"/>
                                <fontDescription key="fontDescription" name="PingFangSC-Medium" family="PingFang SC" pointSize="11"/>
                                <color key="textColor" red="0.23529411764705882" green="0.23529411764705882" blue="0.23529411764705882" alpha="0.59999999999999998" colorSpace="custom" customColorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                    </stackView>
                </subviews>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="Z1x-pH-Frb" secondAttribute="bottom" id="1YD-JX-Tng"/>
                    <constraint firstItem="ssn-B6-dHU" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="8QP-Zg-PYI"/>
                    <constraint firstAttribute="trailing" secondItem="6aG-fr-UFI" secondAttribute="trailing" id="DjV-sW-fd3"/>
                    <constraint firstAttribute="trailing" secondItem="1xg-6B-n4d" secondAttribute="trailing" id="FkW-xg-NWa"/>
                    <constraint firstAttribute="trailing" secondItem="Z1x-pH-Frb" secondAttribute="trailing" id="IXL-sY-1UZ"/>
                    <constraint firstItem="1xg-6B-n4d" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="Qhb-O6-q5M"/>
                    <constraint firstItem="6aG-fr-UFI" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="ssn-B6-dHU" secondAttribute="trailing" constant="10" id="Ue2-GT-Y4d"/>
                    <constraint firstItem="1xg-6B-n4d" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="Z0e-9P-j9K"/>
                    <constraint firstItem="Z1x-pH-Frb" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="fbm-mT-L7k"/>
                    <constraint firstItem="1xg-6B-n4d" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="hBk-1b-Vm1"/>
                    <constraint firstItem="6aG-fr-UFI" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="nja-Pa-K1t"/>
                    <constraint firstItem="ssn-B6-dHU" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="17" id="rpD-3g-VcR"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="bottomLine" destination="Z1x-pH-Frb" id="E1i-cI-TtE"/>
                <outlet property="fontInfoBtn" destination="Jzi-9d-RoA" id="kq0-Ni-U32"/>
                <outlet property="fontNameLabel" destination="xp7-xh-nMM" id="j7s-5I-j4d"/>
                <outlet property="fontOperateBtn" destination="6aG-fr-UFI" id="Jf1-vV-r3o"/>
                <outlet property="fontThumbnailImage" destination="pnd-MM-6YX" id="hAR-UT-eIy"/>
                <outlet property="operateBtnWidth" destination="e7D-ac-bjg" id="kTj-UC-z6v"/>
                <outlet property="subFontNameLabel" destination="lcV-Ya-ODt" id="tbg-Bx-Bap"/>
                <outlet property="thumbnaiImageWidth" destination="azt-d0-TFZ" id="o73-Px-o6L"/>
                <outlet property="topLine" destination="1xg-6B-n4d" id="2on-yf-Gvo"/>
                <outlet property="topStackView" destination="A5S-aR-CVg" id="2mu-5K-kxV"/>
                <outlet property="vipStateImage" destination="lb3-rc-WVX" id="hoe-gd-Zfw"/>
                <outlet property="vipStateWidth" destination="CIZ-VM-Pe8" id="IWf-YF-C3U"/>
            </connections>
            <point key="canvasLocation" x="-371" y="-127"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="fontInfo" width="20" height="20"/>
        <image name="fontVipYellow" width="23" height="15"/>
    </resources>
</document>
