/**
 * 模版版本号相关常量
 */
class TemplateVersion {
  /**
   * 基准模版保存时的版本号
   */
  static const String BASE_SAVE_TEMPLATE_VERSION = "1.7.0.0";

  /**
   * 实时时间模版保存时的版本号
   */
  static const String REALTIME_SAVE_TEMPLATE_VERSION = "1.7.0.1";

  /**
   * 商品库新增自定义字段的模版保存时的版本号
   */
  static const String GOOD_ADD_FIELD_SAVE_TEMPLATE_VERSION = "1.7.0.2";

  /**
   * 新excel解析库保存模版时的版本号
   */
  static const String EXCEL_XLS_TEMPLATE_VERSION = "1.7.0.3";

  /**
   * app支持的最大模版号，取上面常量中的最大值
   * @return
   */
  static String getAppMaxSupportTemplateVersion() {
    return "1.7.0.3";
  }
}
