class Constant {
  /// debug开关，上线需要关闭
  /// App运行在Release环境时，inProduction为true；当App运行在Debug和Profile环境时，inProduction为false
  static const bool inProduction = bool.fromEnvironment('dart.vm.product');

  static bool isTest = true;

  static const String iosOneKey =
      'tQqgKNk5e3cBTdxtda5Pi3NPyjo1xJ1MNWGhAeufUfrZy0pUG3lPCe93AYZ9YL7Xy1T2JPzH/ZmXN+1ci2s1HRcB4nkrSKOJ2b33UGU01+ss1oZKB2VegqCLgPteCcXlSr6kN7Jna+mkly1NnHxoi8rpzKDhZdkzuzqsHtq5sykYQwQ0bc/Vvin9UzVRUGF6GL0APgMby2SmvdnzgnQmN1K29RmPy9h/rLFtMpJoxkYPKpQEL1SshRPuLn6THxD9szX2xFqXUMc=';
  static const String androidOneKey =
      'Ivgq7tIyiOF8sup/61NtQOAifsTfMK9Lshg0D350osZZRe6zv2LcAm7gJiZP72N90fNeFjH3N0BG7El++YY85Ey/tLVsIoO9QS5Hhdf+8qLidTErrV3/buPMzY1en0Q1fW7bf3Y2Mjvj8UI0dkcOz6Pt+2DggP0JdmgqLpHwugin1VI0kTIHvnAAbgxHvjXs/ITTgmU2YbmdEC8Mv+PT0Y0IWB2aKZF6XQzSC5TbitwDIs1jRF/q/U8QhhuQbAxAKC1qkti2VpZNftGGrzPQX3t5b2Mqvhv/gajdnpnZA6afJypbYllAoGllRhbUHl3y';
  static const String success = 'success';
  static const String responseCode = 'status';
  static const String responseMsg = 'error';
  static const String result = 'result';

  static const String data = 'data';
  static const String message = 'message';
  static const String code = 'code';

  static const String accessToken = 'Authorization';

  static const String theme = 'AppTheme';

  /// OSS 相关
  /// oss 上传图片质量，75% 约压缩为原图 30% 大小
  static const int oss_image_upload_quality = 75;
  static const int oss_image_upload_max_width = 1200;

  /// oss 上传图片限制大小，暂定 2M，单个文件限制在中台，'/image-upload/aliyun/oss/policy' 接口中可查配置
  static const int oss_image_upload_max_bytes = 2 * 1024 * 1024;

  /// 超出大小 toast 显示用

  /// 缩略图尾缀
  static const String oss_config_image_thumbnail_side_200 = '?x-oss-process=image/resize,m_lfit,h_200,w_200';
  static const String oss_config_image_thumbnail_side_400 = '?x-oss-process=image/resize,m_lfit,h_400,w_400';
  static const String oss_config_image_thumbnail_side_800 = '?x-oss-process=image/resize,m_lfit,h_800,w_800';

  /// 分辨率2048的缩略图尾缀
  static const String oss_config_image_thumbnail_side_2048 = '?x-oss-process=image/resize,m_lfit,h_2048,w_2048';

  /// 搜索关键字最大容量
  static const int search_history_keys_max_count = 15;

  /// 名称最大长度
  static const int nickName_max_count = 25;
}

class ConstantKey {
  //首次登录
  static const String AcceptProtocol_key = "AcceptProtocol_key";

  static const String HasJumppedLogin_key = 'HasJumppedLogin_key';
  //首次登录
  static const String FirstLogin_key = "FirstLogin_key";

  //显示评分反馈弹窗
  static const String ShowMarkFeedback_key = "ShowMarkFeedback_key";

  //搜索历史
  static const String Search_key = "jc_current_Search_key";

  /// 印迹搜索历史
  static const String PrintHistorySearchKey = "PrintHistorySearchKey";

  /// 云生活发布保留
  static const String FeedPublishSaveKey = "FeedPublishSaveKey";

  /// 云生活编辑保留
  static const String FeedEditSaveKey = "FeedEditSaveKey";

  static const String JCToken_key = "JCToken_key";

  static const String AUTHORIZATION = "Authorization";

  static const String JCUSER_AGENT = "niimbot-user-agent";

  //设备列表
  static const String Device_list_key = "JC_Device_list_key";

  //设备item
  static const String Device_item_key = "JC_Device_item_key";

//PrinterChannel KEY
  static const String JC_Event_Channel = "com.niimbot.printer/Event/Printer";

//BasicMesageChannel KEY
  static const String JC_Basic_Message_Channel = "com.niimbot.printer/basicMessage/channel";

//MehtodChannel KEY
  static const String JC_Method_Channel = "com.niimbot.printer/Method/Channel";

  static const String Mall_Method_Channel = "com.niimbot.printer.flutterplugin.mallview/method/channel";

  static const String Mall_Event_Channel = "com.niimbot.printer.flutterplugin.mallview/event/channel";

  //防丢器列表
  static const String Anti_list_key = "Anti_list_key";

  static const String Anti_permission_remind_key = "Anti_permission_remind_key";

  static const String Anti_permission_setting_key = "Anti_permission_setting_key";

  static const String Notification_permission_remind_key = "Notification_permission_remind_key";

  static const String Location_permission_remind_key = "Location_permission_remind_key";

  static const String Collection_guide_key = "Collection_guide_key";

  static const String Collection_Color_category_key = "Collection_Color_category_key"; //收纳固定的颜色分类

  static const String User_Chose_Region = "User_Chose_Region"; //用户选择臣小印的使用区域

  static const String Used_Language_Key = "used_Language_key"; //用户选择臣小印的使用区域

  static const String Used_Language_Config_Key =
      "Used_Language_Config_Key"; //用户使用的语言配置we年更新的时间戳,初始值为用户第一次使用配置文件的当前utc时间

  static const String Facebook_Login_Switch_key = "Facebook_Login_Switch_key"; //海外登录是否显示facebook登录入口

  static const String Has_Request_Device_List_key = "Has_Request_Device_List_Key"; //成功请求设备列表

  static const String Local_App_version = "Local_App_version"; //app的版本号

  //是否已经提示过登录
  static const String Guide_Login_key = "Guide_Login_key";

  static const String User_UnionId = "User_UnionId";

  /// 打印机型号列表缓存
  static const String hardwareList = 'hardwareList';

  /// 新建场景上次选择的打印机名称
  static const String latestHardwareName = 'latestHardwareNameOnCreate';

  /// 上次选择的打印机系列的 id
  static const String latestHardwareSeriesId = 'latestHardwareSeriesId';

  /// 上次选择的打印机id
  static const String latestHardwareId = 'latestHardwareId';

  /// 上次选择的 线缆标签
  static const String lateCableLabel = 'lateCableLabel';

  /// 模板场景缓存
  static const String local_industry_template = 'LocalIndustryTemplate';

  /// 上次选择的电子价签模板
  static const String latestEtagTemplate = 'latestEtagTemplate';

  /// 上次选择的电子价签模板类型
  static const String latestEtagTemplateSort = 'latestEtagTemplateSort';

  /// 上次选择的电子价签模板尺寸
  static const String latestEtagTemplateSize = 'latestEtagTemplateSize';

  ///批量打印引导提示
  static const String firstBatchPrintToast = 'firstBatchPrintToast';

  ///成员管理引导提示
  static const String membermanagerToast = 'membermanagerToast';

  /// rfid 实际数量
  static const String paperActualNum = 'paperActualNum';

  ///  Ribbon 实际数量
  static const String ribbonActualNum = 'ribbonActualNum';

  ///非法rfid号
  static const String tagIllegalRfidNos = 'tagIllegalRfidNos';
  static const String tagIllegalRfidList = 'tagIllegalRfidList';

  ///sentry-flutter错误采样率
  static const String sentrySampleRate = 'CloudPrintFlutterSentrySampleRate';

  ///sentry-flutter性能采样率
  static const String sentryTracesSampleRate = 'CloudPrintFlutterSentryTracesSampleRate';

  /// 机器别名缓存
  static const String allMachineAliasList = 'allMachineAliasList';

  /// 不支持的机器别名列表
  static const String notSupportAliasList = 'notSupportAliasList';
}
