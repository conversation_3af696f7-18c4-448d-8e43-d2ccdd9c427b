import 'dart:convert';
import 'dart:math';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:flutter_canvas_plugins_interface/utils/precision_num.dart';
import 'package:niimbot_excel/models/data_bind_modify.dart';
import 'package:niimbot_excel/models/data_source.dart';
import 'package:niimbot_excel/models/interface.dart';
import 'package:niimbot_excel/models/range.dart';
import 'package:niimbot_excel/niimbot_data_source_utils.dart';
import 'package:niimbot_excel/niimbot_excel_utils.dart';

import '/niimbot_flutter_canvas.dart';
import '/src/localization/localization_public.dart';
import '/src/model/element/text_element.dart';
import '/src/model/excel/column_preview_manager.dart';
import '/src/utils/input_utils.dart';

Logger _logger = Logger("EscapeUtils", on: kDebugMode);

/// 渲染时 转义过程工具
class EscapeUtils {
  static DataSource? getDataSource(TemplateData? templateData) {
    if (templateData?.dataSource?.isEmpty ?? true) {
      return null;
    }
    return templateData?.dataSource?.firstOrNull;
  }

  static String getExcelFilePath(
      TemplateData? templateData, String? documentPath) {
    DataSource dataSource =
        ArgumentError.checkNotNull(templateData?.dataSource?.firstOrNull);
    String filePath = NiimbotDataSourceUtils.getLocalDataSourcePath(
        documentPath ?? "", dataSource.hash);
    return filePath;
  }

  static List<String>? getElementDataBind(
      TemplateData? templateData, String? documentPath, String? sheetName) {
    final dataSource = getDataSource(templateData);
    if (dataSource == null) {
      return null;
    }
    List<String>? dataBind = null;
    if (dataSource.type == DataSourceType.commodity) {
      dataBind = ["", ""];
    } else {
      String sheet = "";
      if (sheetName?.isEmpty ?? true) {
        sheetName = getSheetName(templateData, documentPath);
      } else {
        sheet = sheetName!;
      }
      dataBind = [dataSource.hash, sheet];
    }
    return dataBind;
  }

  static String getSheetName(TemplateData? templateData, String? documentPath) {
    String filePath = getExcelFilePath(templateData, documentPath);
    List<String> sheets = getSheetsFix(filePath);
    String sheetName = sheets.length > 0 ? sheets.first : "Sheet1";
    return sheetName;
  }

  static List<String> getSheetsFix(String filePath) {
    try {
      return NiimbotExcelUtils.getSheets(filePath);
    }  catch (e, s) {

    }
    return ['Sheet1'];
  }

  ///获取元素修改信息
  static DataBindModify? getElementModify(TemplateData templateData,
      List<String> headers, List<List<String>> rowData, JsonElement jsonElement,
      {int? currentExcelRow}) {
    if (!jsonElement.isBindingElement()) {
      return null;
    }
    final modify = templateData.modify;
    if (modify == null || modify.isEmpty) {
      return null;
    }
    String elementId = jsonElement.id;
    if (jsonElement.isMirrorElement()) {
      elementId = jsonElement.mirrorId;
    }
    final modifyMap = modify[elementId];
    if (modifyMap != null) {
      final globalModify = modifyMap['0'];
      CellAddress cellAddress =
          NiimbotExcelUtils.decodeCellIndex(jsonElement.value!);
      final rowModify = modifyMap[
          (currentExcelRow ?? getCurrentExcelRow(templateData, rowData))
              .toString()];
      bool useTitle = rowModify?.useTitle ?? globalModify?.useTitle ?? false;
      String delimiter = rowModify?.delimiter ?? globalModify?.delimiter ?? "：";
      String title = "";
      if (useTitle || jsonElement.isBindingCommodity()) {
        title = rowModify?.title ??
            globalModify?.title ??
            (headers.length > cellAddress.c
                ? getExcelHeaderByColumnIndex(headers, cellAddress.c)
                : "");
      }
      String fullTitle = useTitle ? "$title$delimiter" : "";
      String prefix = rowModify?.prefix ?? globalModify?.prefix ?? "";
      String? val = rowModify?.value ?? globalModify?.value ?? null;
      String suffix = rowModify?.suffix ?? globalModify?.suffix ?? "";

      DataBindModify realModify = DataBindModify(
          useTitle: useTitle,
          delimiter: delimiter,
          title: title,
          value: val,
          prefix: prefix,
          suffix: suffix);
      return realModify;
    }
    return null;
  }

  static String getExcelHeaderByColumnIndex(
      List<String> headers, int columnIndex) {
    List<String> excelHeaders = headers;
    if (excelHeaders.length > columnIndex) {
      String? header = excelHeaders[columnIndex];
      if (header.isEmpty ?? true) {
        String columnLetter = NiimbotExcelUtils.indexToLetters(columnIndex + 1);
        header = "${intlanguage("app100001121", "列")}${columnLetter}";
      }
      return header;
    } else {
      return "";
    }
  }

  ///当前页
  static int getCurrentPage(TemplateData templateData) {
    final pageInfo = templateData.bindInfo;
    int row = 1;
    if (pageInfo != null) {
      row = pageInfo.page ?? 1;
      if (row == 0) {
        row = 1;
      }
    }
    return row;
  }

  ///当前页在原始Excel的行号（非自定义表头的情况，行号从表头开始计算）
  static int getCurrentExcelRow(
      TemplateData templateData, List<List<String>> rowData) {
    int currentPage = getCurrentPage(templateData);
    return getExcelRow(templateData, rowData, currentPage - 1);
  }

  /// 是否为自定义表头
  /// "headers": {
  ///         // 为数据源中的表自定义表头
  ///         "Sheet1": ["名称", "规格", "主条码"],
  ///         // 指定第一行为表头
  ///         "Sheet2": 1
  ///       }
  static bool isCustomHeader(TemplateData templateData) {
    final dataSource = getDataSource(templateData);
    if (dataSource?.headers?.isEmpty ?? true) {
      return false;
    }

    // dynamic header = dataSource?.headers?.values.first;
    // if (header == null || header.runtimeType == int) {
    //   return false;
    // }
    return false;
  }

  /// 选择行范围
  static List<Range>? getSelectRanges(
      TemplateData templateData, List<List<String>>? rowData) {
    final dataSource = getDataSource(templateData);
    if (dataSource == null || (rowData?.isEmpty ?? true)) {
      return [];
    }
    if (dataSource.range?.isEmpty ?? true) {
      if (isCustomHeader(templateData)) {
        return [Range(s: 1, e: rowData!.length)];
      } else {
        return [Range(s: 2, e: rowData!.length + 1)];
      }
    }
    return dataSource.range;
  }

  /// 通过画板显示页码的索引（从0开始），获取在原始Excel的行号（非自定义表头从2开始，自定义表头从1开始）
  static int getExcelRow(
      TemplateData templateData, List<List<String>> rowData, int subPageIndex) {
    final ranges = getSelectRanges(templateData, rowData);
    List<int> rows = transformRangesToRows(ranges ?? []);
    if (subPageIndex < rows.length) {
      return rows[subPageIndex];
    }
    return subPageIndex + 1;
  }

  /// Range列表格式转化为离散行号的数组
  /// [range] 已选行的range数组
  static List<int> transformRangesToRows(List<Range> ranges) {
    List<int> array = [];
    for (int i = 0; i < ranges.length; i++) {
      Range item = ranges[i];
      for (int row = item.s; row <= item.e; row++) {
        array.add(row);
      }
    }
    return array;
  }

  ///获取标准单元格占位符---A4
  static String getCellPlaceHolder(int bindingColumn) {
    if (bindingColumn <= -1) {
      return "";
    }
    String columnLetter = NiimbotExcelUtils.indexToLetters(bindingColumn + 1);
    return "${columnLetter}0";
  }

  ///选列预览时，临时更改标题是否显示
  static void changeElementUseTitleModify(
      TemplateData? templateData, JsonElement? element, bool? displayHeader) {
    if (element == null) {
      return;
    }
    Map<String, DataBindModify>? modifyMap =
        getElementOriginModify(templateData, element.id);
    final globalModify =
        modifyMap?.putIfAbsent("0", () => DataBindModify(delimiter: "："));
    globalModify?.useTitle = displayHeader;
  }

  ///获取元素原始的修改信息
  static Map<String, DataBindModify>? getElementOriginModify(
      TemplateData? templateData, String elementId) {
    if (templateData?.modify?.isEmpty ?? true) {
      templateData?.modify = {};
    }
    final modify = templateData?.modify;
    final modifyMap = modify?.putIfAbsent(elementId, () => {});
    return modifyMap;
  }

  ///删除元素绑定关系
  static void delElementModify(
      TemplateData? templateData, JsonElement? element) {
    if (element == null) {
      return;
    }
    Map<String, DataBindModify>? modifyMap =
        getElementOriginModify(templateData, element.id);
    modifyMap?.clear();
  }

  ///克隆excel数据源
  static DataSource cloneDataSource(DataSource? dataSource) {
    DataSource cloneObj =
        DataSource.fromJson(jsonDecode(jsonEncode(dataSource?.toJson())));
    return cloneObj;
  }

  ///克隆模板数据
  static TemplateData cloneTemplateData(TemplateData templateData) {
    TemplateData cloneObj =
        TemplateData.fromJson(jsonDecode(jsonEncode(templateData.toJson())));
    return cloneObj;
  }

  ///判断元素是否超出画板
  static bool isElementOverflow(TemplateData? templateData) {
    if (templateData != null && templateData.canvasElements.isNotEmpty) {
      double maxY = 0;
      for (var element in templateData.canvasElements) {
        var elementHeight = element.data.height;
        if (element.data.type == ElementItemType.text &&
            element.data.height == 8) {
          TextElement textElement = element.data as TextElement;
          String? value = ColumnPreviewManager()
              .escapeBindingValue(textElement, templateData.currentPageIndex);
          // Size textSize = InputUtils.boundingTextSize(value, TextStyle(fontSize: textElement.fontSize.mm2dp()),maxWidth: textElement.width.mm2dp());
          int lines = InputUtils.calculateLines(
              value, TextStyle(fontSize: textElement.fontSize.mm2dp().toDouble()),
              maxWidth: textElement.width.mm2dp().toDouble());
          elementHeight = lines * 3.76;
        }
        maxY = max(maxY, element.data.y.toDouble() + elementHeight);
        if (templateData.height!= null && maxY > templateData.height!) {
          return true;
        }
      }
    }
    return false;
  }
}
