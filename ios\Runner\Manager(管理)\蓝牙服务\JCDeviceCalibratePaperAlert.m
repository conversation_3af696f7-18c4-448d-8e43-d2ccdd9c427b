//
//  JCDeviceCalibratePaperAlert.m
//  Runner
//
//  Created by Chance on 2024/8/28.
//

#import "JCDeviceCalibratePaperAlert.h"
#import "JCPrintSettingTableViewCell1.h"
#import "YLButton.h"

@interface JCCalibratePaperCell : UITableViewCell

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UIView *calibrateView;

@property (nonatomic, strong) UILabel *calibrateLabel;

@end


@implementation JCCalibratePaperCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setUp];
    }
    return self;
}

- (void)setUp {
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.calibrateView];
    [self.calibrateView addSubview:self.calibrateLabel];

    [self setUpConstraints];
}

- (void)setUpConstraints {
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(16);
        make.centerY.equalTo(self.contentView);
    }];
    
    [self.calibrateView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.contentView).inset(16);
        make.centerY.equalTo(self.contentView);
    }];
    
    [self.calibrateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.calibrateView).insets(UIEdgeInsetsMake(4, 12, 4, 12));
    }];
}


- (void)setTitle:(NSString *)title {
    self.titleLabel.text = title;
}

// MARK: -- Lazy Load --

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = HEX_RGB(0x161616);
        _titleLabel.font = MY_FONT_Regular(15);
    }
    return _titleLabel;
}

- (UIView *)calibrateView {
    if (!_calibrateView) {
        _calibrateView = [[UIView alloc] initWithFrame:CGRectZero];
        _calibrateView.backgroundColor = HEX_RGB(0xF7F7FA);
        _calibrateView.layer.cornerRadius = 14;
    }
    return _calibrateView;
}

- (UILabel *)calibrateLabel {
    if (!_calibrateLabel) {
        // 断开
        UILabel *calibrateLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        calibrateLabel.textColor = HEX_RGB(0xFB4B42);
        calibrateLabel.font = MY_FONT_Regular(13);
        calibrateLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app00931", @"校准");
        _calibrateLabel = calibrateLabel;
    }
    return _calibrateLabel;
}
@end


@interface JCDeviceCalibratePaperAlert ()<GroupShadowTableViewDelegate,
GroupShadowTableViewDataSource>

@property(nonatomic,copy) NSArray *paperArr;

@property(nonatomic,strong) NSMutableArray *dataArr;

@property (strong, nonatomic) GroupShadowTableView *groupShadowTableView;

@end

@implementation JCDeviceCalibratePaperAlert

- (instancetype)initWithPaperArray:(NSArray *)paperArray
{
    if (self == [self init]) {
        self.paperArr = paperArray;
    }
    return self;
}

- (instancetype)init{
    self = [super initWithAlertSize:CGSizeMake(SCREEN_WIDTH, 400 + (iPhoneX ? 62 : 30)) navHeight:48];
    if (self) {
        XYWeakSelf
        [self initTableView];
        [self showAlertTitleWith: XY_LANGUAGE_TITLE_NAMED(@"app00900", @"走纸校准")];
        [self showLeftButtonWithImage:XY_IMAGE_NAMED(@"closeAlert")];
        [self setLeftBtnClickBlock:^{
            [weakSelf hiddenContentAlert];
        }];
        self.tapEmptyClose = YES;
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(printerStatusNotification:) name:PrinterStatusNotification object:nil];
    }
    return self;
}

-(void)initTableView
{
    _groupShadowTableView = [[GroupShadowTableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
    _groupShadowTableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    _groupShadowTableView.backgroundColor = HEX_RGB(0xF5F5F5);
    _groupShadowTableView.tableFooterView = [UIView new];
    [self.contentView addSubview:_groupShadowTableView];
    XYWeakSelf
    [self.groupShadowTableView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.leading.trailing.bottom.equalTo(weakSelf.contentView);
    }];
    self.groupShadowTableView.bounces = NO;
    [self.groupShadowTableView registerClass:[JCCalibratePaperCell class] forCellReuseIdentifier:@"JCCalibratePaperCell"];
    self.groupShadowTableView.groupShadowDelegate = self;
    self.groupShadowTableView.groupShadowDataSource = self;
    self.groupShadowTableView.showSeparator = YES;
    self.groupShadowTableView.addShadow = NO;
    self.groupShadowTableView.separatorColor = HEX_RGB(0xEBEBEB);
}

-(void)setPaperArr:(NSArray *)paperArr
{
    _paperArr = paperArr;
    [self.dataArr removeAllObjects];
    //后台 1 间隙纸 2.订孔纸 3 连续纸 4 黑标纸 5 透明纸
    //(1—间隙纸,2—黑标纸,3—连续纸,4-定孔纸，5-透明纸（4，目前还没有）
    if ([paperArr containsObject:@"1"]) {
        [self.dataArr addObject:@{@"type":@"1",@"name":XY_LANGUAGE_TITLE_NAMED(@"app00280",@"间隙纸")}];
    }
    if ([paperArr containsObject:@"2"]) {
         [self.dataArr addObject:@{@"type":@"2",@"name":XY_LANGUAGE_TITLE_NAMED(@"app00283",@"黑标纸")}];
    }
    if ([paperArr containsObject:@"3"]) {
         [self.dataArr addObject:@{@"type":@"3",@"name":XY_LANGUAGE_TITLE_NAMED(@"app00282",@"连续纸")}];
    }
    if ([paperArr containsObject:@"4"]) {
         [self.dataArr addObject:@{@"type":@"4",@"name":XY_LANGUAGE_TITLE_NAMED(@"app00281",@"定孔纸")}];
        
    }
    if ([paperArr containsObject:@"5"]) {
         [self.dataArr addObject:@{@"type":@"5",@"name":XY_LANGUAGE_TITLE_NAMED(@"app00818",@"透明纸")}];
    }
    if ([paperArr containsObject:@"6"]) {
         [self.dataArr addObject:@{@"type":@"6",@"name":XY_LANGUAGE_TITLE_NAMED(@"app00989",@"标牌纸")}];
    }
    if ([paperArr containsObject:@"10"]) {
         [self.dataArr addObject:@{@"type":@"10",@"name":XY_LANGUAGE_TITLE_NAMED(@"",@"黑标间隙纸")}];
    }
    if ([paperArr containsObject:@"11"]) {
         [self.dataArr addObject:@{@"type":@"11",@"name":XY_LANGUAGE_TITLE_NAMED(@"app100000744",@"热缩管")}];
    }
    
    
}

- (void)printerStatusNotification:(NSNotification *)noti {
    XYWeakSelf
    NSString *state = noti.object;
    BOOL isBlueOpen = ([state isEqualToString:@"1"] ? YES : NO);
    [self endEditing:YES];
    self.maskView.alpha = 0;
    [UIView animateWithDuration:0.3
                          delay:0
                        options:UIViewAnimationOptionCurveEaseIn | UIViewAnimationOptionBeginFromCurrentState
                     animations:^{
        [self.backView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(weakSelf).offset(SCREEN_HEIGHT);
            make.leading.equalTo(weakSelf).offset(0);
        }];
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

- (void)dealloc
{
    JCNCRemoveOb(self, PrinterStatusNotification, nil);
}

#pragma mark - data source / delegate

- (CGFloat)groupShadowTableView:(GroupShadowTableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 55;
}


- (UIView *)groupShadowTableView:(GroupShadowTableView *)tableView viewForHeaderInSection:(NSInteger)section{
    return nil;
}

- (CGFloat)groupShadowTableView:(GroupShadowTableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return 12;
}

- (NSInteger)numberOfSectionsInGroupShadowTableView:(GroupShadowTableView *)tableView
{
    return 1;
}

- (NSInteger)groupShadowTableView:(GroupShadowTableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.dataArr.count;
}

- (UIView *)groupShadowTableView:(GroupShadowTableView *)tableView viewForFooterInSection:(NSInteger)section{
    UIView *footerView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 44)];
    footerView.backgroundColor = [UIColor clearColor];
    UILabel *tipLabel = [[UILabel alloc] initWithFrame:CGRectMake(32, 0, SCREEN_WIDTH - 47, 44)];
    tipLabel.font = MY_FONT_Regular(12);
    tipLabel.textColor = HEX_RGB(0x999999);
    tipLabel.textAlignment = NSTextAlignmentNatural;
    tipLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app00932", @"校准前，请确认打印机内已放置对应类型的纸张");
    tipLabel.numberOfLines = 0;
    [footerView addSubview:tipLabel];
    return footerView;
}

- (CGFloat)groupShadowTableView:(GroupShadowTableView *)tableView heightForFooterInSection:(NSInteger)section{
    return 64;
}


- (UITableViewCell *)groupShadowTableView:(GroupShadowTableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    JCCalibratePaperCell *cell = [tableView dequeueReusableCellWithIdentifier:@"JCCalibratePaperCell"];
    NSDictionary *dic = self.dataArr[indexPath.row];
    NSString *name = [dic objectForKey:@"name"];
    [cell setTitle:name];
    return cell;
}

- (void)groupShadowTableView:(GroupShadowTableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
     //1—间隙纸, 2—黑标纸, 3—连续纸，4-定孔纸，5-透明纸（4，目前还没有） 10 -黑标间隙纸
    NSDictionary *dic = self.dataArr[indexPath.row];
    NSInteger type = [[dic objectForKey:@"type"] integerValue];
    NSLog(@"SDK升级流程：设置纸张校准");
    [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"setPositioningCalibration"
                                                           arguments:@(type) result:^(NSNumber   * _Nullable code) {
        NSString *message = @"";
        if (code.integerValue == 0){
            message = XY_LANGUAGE_TITLE_NAMED(@"app00929", @"纸张校准成功");
        }else if (code.integerValue == -1){
            message = XY_LANGUAGE_TITLE_NAMED(@"app00901", @"纸张校准失败");
        }else if (code.integerValue == -2){
            message = XY_LANGUAGE_TITLE_NAMED(@"app01200", @"打印机忙碌");
        }else if (code.integerValue == -3){
            message = XY_LANGUAGE_TITLE_NAMED(@"app100000464", @"不支持校准");
        }else{
            message = XY_LANGUAGE_TITLE_NAMED(@"app00901", @"纸张校准失败");
        }
        if (code.integerValue == 0) {
            [MBProgressHUD showSuccessToast:message icon:@"copySuccess"];
        } else {
            [MBProgressHUD showError:message];
        }
    }];
    
}

- (NSMutableArray *)dataArr
{
    if (!_dataArr) {
        _dataArr =[NSMutableArray array];
    }
    return _dataArr;
}

@end
