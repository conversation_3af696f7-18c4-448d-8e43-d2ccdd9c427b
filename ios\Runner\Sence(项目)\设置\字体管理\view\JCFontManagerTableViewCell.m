//
//  JCFontManagerTableViewCell.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2018/6/16.
//  Copyright © 2018年 xiaoyao. All rights reserved.
//

#import "JCFontManagerTableViewCell.h"
#import "JCProgressView.h"
@interface JCFontManagerTableViewCell()
@property (weak, nonatomic) IBOutlet UIView *bottomLine;
@property (weak, nonatomic) IBOutlet UIView *topLine;
@property (weak, nonatomic) IBOutlet UILabel *fontNameLabel;
@property (weak, nonatomic) IBOutlet UILabel *subFontNameLabel;

@property (weak, nonatomic) IBOutlet UIButton *fontOperateBtn;
@property (weak, nonatomic) IBOutlet UIButton *fontInfoBtn;
//@property (weak, nonatomic) IBOutlet NSLayoutConstraint *fontInfoLeading;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *operateBtnWidth;
@property (weak, nonatomic) IBOutlet  UIImageView *fontThumbnailImage;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *thumbnaiImageWidth;
@property (weak, nonatomic) IBOutlet  UIImageView *vipStateImage;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *vipStateWidth;
@property (nonatomic, strong) JCProgressView *progressView;
@property (weak, nonatomic) IBOutlet UIStackView *topStackView;

@property (nonatomic,strong) JCFontModel *dataModel;

@end

@implementation JCFontManagerTableViewCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    [self.contentView addSubview:self.progressView];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(downLoadPressNotification:) name:JCNOTICATION_DOWNLOADPRESS object:nil];
}

-(void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)showTopLine:(BOOL)showTopLine bottomLine:(BOOL)showBottomLine{
    self.topLine.hidden = !showTopLine;
    self.bottomLine.hidden = !showBottomLine;
}

- (IBAction)fontOperateEvent:(UIButton *)sender {
    if(sender.tag == 1){
        if(sender.selected) return;
        sender.selected = YES;
        if(self.clickCellBtnBlock){
            BOOL isDownLoaded = [self.dataModel fontHasDownload];
            if(isDownLoaded && !m_user_vip){
                self.clickCellBtnBlock(@"1");
                sender.selected = NO;
            }else{
                self.clickCellBtnBlock(@"2");
            }
        }
    }else{
        if(self.clickCellBtnBlock){
            self.clickCellBtnBlock(@"3");
        }
    }
}

- (void)setModel:(id)model {
  XYWeakSelf
  JCFontModel *fontModel = (JCFontModel *)model;
  BOOL isDownLoaded = [fontModel fontHasDownload];
  
  // 清理所有 UI 初始状态，避免复用错乱
  self.fontThumbnailImage.hidden = YES;
  self.fontThumbnailImage.alpha = 1;
  self.fontNameLabel.hidden = YES;
  self.subFontNameLabel.hidden = YES;
  [self.fontOperateBtn setImage:nil forState:UIControlStateNormal];
  [self.fontOperateBtn setTitle:@"" forState:UIControlStateNormal];
  self.fontOperateBtn.hidden = NO;
  self.fontOperateBtn.userInteractionEnabled = YES;
  self.progressView.hidden = YES;
  self.fontThumbnailImage.image = nil; // 避免显示上一个 cell 的图
  self.thumbnaiImageWidth.constant = 0;
  
  self.fontNameLabel.text = fontModel.name;
  self.subFontNameLabel.text = fontModel.fileName;
  
  self.operateBtnWidth.constant = 40;
  BOOL hasThumbnailUrl = !STR_IS_NIL(fontModel.thumbnailUrl);
  BOOL showFontName = fontModel.showSystemName;
  BOOL isVIP = fontModel.isVip.integerValue == 1;
  BOOL isDefault = [fontModel.fontCode isEqualToString:@"ZT001"] || [fontModel.fontCode isEqualToString:@"ZT063"];
  
    if (isDownLoaded) {
        self.fontThumbnailImage.alpha = 1;
        self.subFontNameLabel.alpha = 1.0;
        if (isVIP && !m_user_vip) {
            [self.fontOperateBtn setImage:XY_IMAGE_NAMED(@"锁定_normal") forState:UIControlStateNormal];
            self.fontOperateBtn.userInteractionEnabled = YES;
        } else {
            [self.fontOperateBtn setImage:[UIImage new] forState:UIControlStateNormal];
            self.fontOperateBtn.userInteractionEnabled = NO;
        }
        self.progressView.hidden = YES;
        fontModel.percentForDownLoad = @"0";
    } else {
        self.fontThumbnailImage.alpha = 0.3;
        self.subFontNameLabel.alpha = 0.5;
        if ([fontModel.typeDownloadState isEqualToString:@"1"]) {
            self.fontOperateBtn.hidden = YES;
            self.progressView.hidden = NO;
            self.progressView.progress = fontModel.percentForDownLoad.floatValue;
        } else {
            [self.fontOperateBtn setImage:XY_IMAGE_NAMED(@"下载") forState:UIControlStateNormal];
            [self.fontOperateBtn setTitleColor:XY_HEX_RGB(0x537FB7) forState:UIControlStateNormal];
            [self.fontOperateBtn setTitleColor:XY_HEX_RGB(0x666666) forState:UIControlStateHighlighted];
            self.progressView.hidden = YES;
            fontModel.percentForDownLoad = @"0";
        }
    }

    self.fontInfoBtn.hidden = STR_IS_NIL(fontModel.copyright);
    self.vipStateImage.hidden = !isVIP;
    self.subFontNameLabel.hidden = !showFontName;

    // 设置图片和字体名称显示逻辑
    if (!hasThumbnailUrl || isDefault) {
        self.fontNameLabel.hidden = NO;
        self.subFontNameLabel.hidden = YES;
        self.fontThumbnailImage.hidden = YES;
        [self.fontNameLabel sizeToFit];
        NSLog(@"加载默认字体%@ 字体名称：%@", fontModel.fontCode, fontModel.name);
    } else {
        self.fontThumbnailImage.hidden = NO;
        NSURL *imageURL = XY_URLWithString(fontModel.thumbnailUrl);
        [self.fontThumbnailImage sd_setImageWithURL:imageURL placeholderImage:nil options:SDWebImageRetryFailed completed:^(UIImage *image, NSError *error, SDImageCacheType cacheType, NSURL *url) {
            if (image) {
//                if ([weakSelf.fontNameLabel.text isEqualToString:XY_LANGUAGE_TITLE_NAMED(@"app01533", @"鸿蒙")]) {
//                    weakSelf.fontNameLabel.hidden = NO;
//                } else {
//                    weakSelf.fontNameLabel.hidden = YES;
//                }
                weakSelf.thumbnaiImageWidth.constant = image.size.width * 18 / image.size.height;
                fontModel.fontThumbnailSuccess = @"1";
                weakSelf.fontNameLabel.hidden = YES;
                weakSelf.fontThumbnailImage.hidden = NO;
            } else {
                weakSelf.fontNameLabel.hidden = NO;
                weakSelf.fontThumbnailImage.hidden = YES;
            }

            if (!weakSelf.fontNameLabel.hidden) {
                weakSelf.subFontNameLabel.hidden = YES;
            }
        }];
    }

    self.dataModel = fontModel;
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

-(void)reset{
    self.dataModel.typeDownloadState = @"0"; //清理成非下载状态
    [self setModel:self.dataModel];
}

- (void)downLoadPressNotification:(NSNotification *)notification{
    NSArray *arr = notification.object;
    NSString *tagString = [arr safeObjectAtIndex:0];
    if([self.dataModel.fontCode isEqualToString:tagString]) {
        NSString *pressString = [arr safeObjectAtIndex:1];
        NSString *typeDownloadState = [arr safeObjectAtIndex:2];
        XYWeakSelf
        dispatch_async(dispatch_get_main_queue(), ^{
            self.dataModel.percentForDownLoad = [NSString stringWithFormat:@"%f",pressString.floatValue];
            self.dataModel.typeDownloadState = typeDownloadState;
            self.fontOperateBtn.userInteractionEnabled = NO;
            self.fontOperateBtn.hidden = YES;
            self.progressView.hidden = NO;
            self.progressView.progress = self.dataModel.percentForDownLoad.floatValue;
            if(pressString.integerValue == 1){
                weakSelf.dataModel.fontThumbnailSuccess = @"0";
                [weakSelf setModel:weakSelf.dataModel];
                weakSelf.progressView.progress = 0;
            }
        });
    }
}

- (JCProgressView *)progressView {
    if (!_progressView) {
        _progressView = [[JCProgressView alloc] initWithFrame:(CGRect){kSCREEN_WIDTH - 30 - 32,15.5,24,24}];
        _progressView.hidden = YES;
    }
    return _progressView;
}
@end
