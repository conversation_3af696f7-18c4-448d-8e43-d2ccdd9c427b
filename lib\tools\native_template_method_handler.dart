import 'dart:convert';

import 'package:common_utils/common_utils.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:text/template/util/template_detail_db_utils.dart';

import '../template/model/template_detail_result.dart';
import '../template/model/template_list_model.dart';
import '../template/template_manager.dart';
import '../template/util/template_transform_utils.dart';
import 'package:niimbot_template/models/template_data.dart' as NiimbotTemplateData;

/**
 * 原生桥接的模版相关方法分发
 */
class NativeTemplateMethodHandler {
  static Future<dynamic> handleTemplateMethod(String method, dynamic params) async {
    if (method == NativeTemplateMethod.nativeGetTemplateDetail.methodName) {
      bool needUpdateDb = params['needUpdateDb'] as bool? ?? false;
      bool fromLocal = params['fromLocal'] as bool? ?? false;
      String templateId = params['templateId'];
      LogUtil.d("_nativeGetTemplateDetail needUpdateDb=$needUpdateDb templateId=$templateId");
      NiimbotTemplateData.TemplateData? nmTemplateData =
          await TemplateManager().getTemplateDetail(templateId, needUpdateDb: needUpdateDb, fromLocal: fromLocal);
      if (nmTemplateData == null) {
        return null;
      }
      TemplateData canvasTemplate = await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(nmTemplateData);
      Map<String, dynamic> json = canvasTemplate.toJson(handleTemplateType: false);
      return jsonEncode(json);
    } else if (method == NativeTemplateMethod.nativeGetTemplateDetailWithParams.methodName) {
      bool isPersonalTemplate = params['isPersonalTemplate'] as bool? ?? true;
      bool isShare = params['isShare'] as bool? ?? false;
      bool isFolderShare = params['isFolderShare'] as bool? ?? false;
      String templateId = params['templateId'];
      LogUtil.d(
          "_nativeGetTemplateDetailWithParams isPersonalTemplate=$isPersonalTemplate templateId=$templateId isShare=$isShare isFolderShare=$isFolderShare");
      TemplateDetailResult result = await TemplateManager().getTemplateDetailWithParams(templateId,
          isPersonalTemplate: isPersonalTemplate, isShare: isShare, isFolderShare: isFolderShare);
      return await result.toJsonMap();
    } else if (method == NativeTemplateMethod.nativeGetMyGoodTemplateList.methodName) {
      try {
        int commodityTemplate = params['commodityTemplate'] as int? ?? -1;
        int page = params['page'] as int? ?? 1;
        int limit = params['limit'] as int? ?? 10;
        String searchKey = params['searchKey'] ?? "";
        LogUtil.d(
            "_nativeGetMyGoodTemplateList commodityTemplate=$commodityTemplate page=$page limit=$limit searchKey=$searchKey");
        TemplateListModel result =
            await TemplateManager().getMyGoodTemplateList(commodityTemplate, page, limit, searchKey: searchKey);
        String templateInfoStr = jsonEncode(await result.toJsonMap());
        return templateInfoStr;
      } catch (e, s) {
        LogUtil.e("_nativeGetMyGoodTemplateList error");
        return null;
      }
    } else if (method == NativeTemplateMethod.nativeGetMyTemplateList.methodName) {
      try {
        int page = params['page'] as int? ?? 1;
        int limit = params['limit'] as int? ?? 10;
        bool fromLocal = params['fromLocal'] as bool? ?? false;
        bool onlyNetwork = params['fromLocal'] as bool? ?? false;
        LogUtil.d("_nativeGetMyTemplateList  page=$page limit=$limit fromLocal=$fromLocal");
        TemplateListModel result =
            await TemplateManager().getMyTemplateList(page, limit, fromLocal: fromLocal, onlyNetwork: onlyNetwork);
        String templateInfoStr = jsonEncode(await result.toJsonMap());
        return templateInfoStr;
      } catch (e, stack) {
        return "";
      }
    } else if (method == NativeTemplateMethod.nativeGetCloudTempalteByScanCode.methodName) {
      bool needUpdateDb = params['needUpdateDb'] as bool? ?? false;
      String oneCode = params['oneCode'];
      LogUtil.d("_nativeGetCloudTempalteByScanCode needUpdateDb=$needUpdateDb oneCode=$oneCode");
      TemplateDetailResult result =
          await TemplateManager().getTemplateDetailByScanCode(oneCode, needUpdateDb: needUpdateDb);
      return await result.toJsonMap();
    } else if (method == NativeTemplateMethod.nativeGetCloudTempalteByScanCodeOrLabelId.methodName) {
      bool needUpdateDb = params['needUpdateDb'] as bool? ?? false;
      String oneCode = params['oneCode'];
      String labelId = params['labelId'];
      bool fromLocal = params['fromLocal'] as bool? ?? false;
      LogUtil.d("_nativeGetCloudTempalteByScanCode needUpdateDb=$needUpdateDb oneCode=$oneCode labelId=$labelId");
      TemplateDetailResult result =
          await TemplateManager().getTemplateDetailByScanCodeOrLabelId(oneCode, labelId, needUpdateDb: needUpdateDb, fromLocal: fromLocal);
      return await result.toJsonMap();
    } else if (method == NativeTemplateMethod.deleteIndustryTemplate.methodName) {
      TemplateDetailDBUtils.deleteIndustryTemplate();
    } else if (method == NativeTemplateMethod.insertOrUpdateTemplate.methodName) {
      String templateStr = params['template'] ?? "";
      NiimbotTemplateData.TemplateData templateData =
          await TemplateTransformUtils.canvasJsonToNiimbotTemplate(templateStr);
      await TemplateDetailDBUtils.insertTemplate(templateData);
      return true;
    } else if (method == NativeTemplateMethod.nativeQueryRfidTemplateByScanCode.methodName) {
      String oneCode = params['oneCode'];
      LogUtil.d("_nativeQueryRfidTemplateByScanCode oneCode=$oneCode");
      TemplateDetailResult result = await TemplateManager().queryLableDetailByScanCode(oneCode);
      return await result.toJsonMap();
    } else if (method == NativeTemplateMethod.nativeQueryTemplateById.methodName) {
      String templateId = params['templateId'];
      LogUtil.d("_nativeQueryTemplateById templateId=$templateId");
      TemplateDetailResult result = await TemplateManager().queryTemplateById(templateId);
      return await result.toJsonMap();
    } else if (method == NativeTemplateMethod.nativeDownloadTemplateResAndUpdateTemplate.methodName) {
      String templateStr = params['template'] ?? "";
      Map<String, dynamic> jsonMap = jsonDecode(templateStr);
      NiimbotTemplateData.TemplateData templateData =
          await TemplateTransformUtils.canvasJsonToNiimbotTemplate(templateStr);
      var (isSuccess, remoteTemplate) =
          await TemplateManager().downloadTemplateResAndUpdateTemplate(templateData, needUpdateDb: false);
      NiimbotTemplateData.TemplateData resultTemplateData = remoteTemplate ?? templateData;
      TemplateData canvasTemplate = await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(resultTemplateData);
      Map<String, dynamic> json = canvasTemplate.toJson(handleTemplateType: false);
      if (jsonMap['externalData'] != null) {
        json['externalData'] = jsonMap['externalData'];
      }
      return jsonEncode(json);
    }
  }
}

enum NativeTemplateMethod {
  nativeGetTemplateDetail(methodName: "nativeGetTemplateDetail"),
  nativeGetTemplateDetailWithParams(methodName: "nativeGetTemplateDetailWithParams"),
  nativeGetMyGoodTemplateList(methodName: "nativeGetMyGoodTemplateList"),
  nativeGetMyTemplateList(methodName: "nativeGetMyTemplateList"),
  deleteIndustryTemplate(methodName: "deleteIndustryTemplate"),
  //wifi码更新模版场景
  insertOrUpdateTemplate(methodName: "insertOrUpdateTemplate"),
  nativeQueryRfidTemplateByScanCode(methodName: "nativeQueryRfidTemplateByScanCode"),
  nativeQueryTemplateById(methodName: "nativeQueryTemplateById"),
  nativeGetCloudTempalteByScanCode(methodName: "nativeGetCloudTempalteByScanCode"),
  nativeGetCloudTempalteByScanCodeOrLabelId(methodName: "nativeGetCloudTempalteByScanCodeOrLabelId"),

  // 下载模版资源并更新入库
  nativeDownloadTemplateResAndUpdateTemplate(methodName: "nativeDownloadTemplateResAndUpdateTemplate");

  final String methodName;

  const NativeTemplateMethod({required this.methodName});

  static bool matchAny(String methodName) {
    return NativeTemplateMethod.values.any((it) => it.methodName == methodName);
  }
}
