import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element.dart';
import 'package:niimbot_lego/niimbot_lego.dart' as niimbot_lego;
import 'package:niimbot_template/models/template_data.dart' as niimbotTemplateData;
import 'package:niimbot_template/template_parse.dart';
import 'package:text/cap/model/LayoutJsonList.dart';
import 'package:text/cap/model/Preview_with_layouts_params.dart';
import 'package:text/cap/model/column_name_catering.dart';
import 'package:text/cap/model/meeting_params.dart';
import 'package:text/utils/layout_scheme_form_item.dart';

import 'layout_scheme_value_rules.dart';

/**
 * 版式模版生成工具类
 */
Logger _logger = Logger("LayoutSchemeTemplateUtils", on: kDebugMode);

class LayoutSchemeTemplateUtils {
  ///根据版式集合生成静态模版集合
  static Future<List<String>> parseLayoutsToListCanvasJson(
      PreviewWithLayoutsParams params, String contentJson, TemplateData? labelData) async {
    List<LayoutJsonList> layoutSchemes = params.layoutJsonList!;
    List<String> canvasJsonList = [];
    for (int i = 0; i < layoutSchemes.length; i++) {
      String canvasJson = niimbot_lego.parseLayoutToCanvasJson(layoutSchemes[i].layoutJson!, contentJson);
      TemplateData mixData = TemplateData.fromJson(jsonDecode(canvasJson));
      mixData = fillTemplateWithCapRowData(mixData, params, params.rowData!);
      final resultData = await OcrUtils.generateIndustryReplaceTemplate(mixData, labelData!);
      String result = resultData.generateCanvasJson();
      canvasJsonList.add(result);
    }
    return canvasJsonList;
  }

  static TemplateData fillTemplateWithCapRowData(
      TemplateData templateData, PreviewWithLayoutsParams params, List<String> rowData) {
    if (params.isCateringCap == true) {
      templateData.elements.forEach((element) {
        String? originValue = element.value;
        int index = -1;
        for (int i = 0; i < params.cateringColumnNames!.length; i++) {
          if (params.cateringColumnNames![i].name == originValue) {
            index = i;
            break;
          }
        }
        if (index != -1) {
          String alias = params.cateringColumnNames![index].alias ?? "";
          if (alias.isNotEmpty) {
            element.value = "${alias}：${rowData[index]}";
          } else {
            element.value = rowData[index];
          }
        }
      });
    } else {
      templateData.elements.forEach((element) {
        String? originValue = element.value;
        int index = params.colNames!.indexOf(originValue ?? "");
        if (index != -1) {
          element.value = rowData[index];
        }
      });
    }
    return templateData;
  }

  ///根据版式和数据源列生成模版
  static Future<String> parseLayoutToCanvasJson(String layoutScheme, String contentJson, TemplateData? labelData) async {
    String canvasJson = niimbot_lego.parseLayoutToCanvasJson(layoutScheme,contentJson);
    _logger.log("=========parseLayoutToCanvasJson==========> ${canvasJson}");
    final mixData = TemplateData.fromJson(jsonDecode(canvasJson));
    final resultData = await OcrUtils.generateIndustryReplaceTemplate(mixData, labelData!);

    //将此模版加工成类似商品库的数据源模版
    return resultData.generateCanvasJson();
  }

  ///根据版式和数据源列生成模版
  static Map<String, dynamic> parseAreaToTemplate(String areaJson, double labelWidth, double labelHeight) {
    _logger.log("=========parseAreaToTemplate==========> areaJson: $areaJson, labelWidth: $labelWidth, labelHeight: $labelHeight");
    String canvasJson = niimbot_lego.parseAreaToTemplate(areaJson, labelWidth, labelHeight);
    _logger.log("=========parseAreaToTemplate==========> ${canvasJson}");
    return jsonDecode(canvasJson);
  }

  static Future<String> scaleCanvasJson(String canvasJson, MeetingParams meetingParams, List<List<String>> rowData, TemplateData? labelData) async {
    final mixData = TemplateData.fromJson(jsonDecode(canvasJson));
    List<String> headers = meetingParams.getHeaders();
    List<String> data = rowData.firstOrNull ?? [];
    if(headers.isEmpty || headers.length != data.length) {
      final resultData = await OcrUtils.generateIndustryReplaceTemplate(mixData, labelData!);
      //将此模版加工成类似商品库的数据源模版
      return resultData.generateCanvasJson();
    }
    Map<int, int> map = {};
    for (int i = 0; i < mixData.elements.length; i++) {
      if (mixData.elements[i] is! TextElement) continue;

      final value = mixData.elements[i].value ?? '';
      if (value.isEmpty) continue;

      final columnIndex = meetingParams.getCateringColumnIndex(value);
      if(columnIndex == -1) continue;
      ColumnNameCatering columnNameCatering = meetingParams.cateringColumnNames![columnIndex];
      String alias = columnNameCatering.alias ?? "";
      if(alias.isEmpty) {
        mixData.elements[i].value = data[columnIndex];
      }
      else{
        mixData.elements[i].value = "$alias：${data[columnIndex]}";
      }
      map[i] = columnIndex;
    }
    final resultData = await OcrUtils.generateIndustryReplaceTemplate(mixData, labelData!);
    for (var entry in map.entries) {
      mixData.elements[entry.key].value = headers[entry.value];
    }
    //将此模版加工成类似商品库的数据源模版
    return resultData.generateCanvasJson();
  }

  static Future<String> generateSchemeTemplate(String layoutScheme, String formData, bool isPreview) async {
    _logger.log("=========initLegoJsonData========Init layoutJson==> ${layoutScheme}");

    Map<String, dynamic> jsonMap;
    jsonMap = json.decode(layoutScheme);
    num _width = jsonMap['width'] ?? 0;
    num _height = jsonMap['height'] ?? 0;
    String bgImageUrl = jsonMap['image'] ?? "";
    String schemeTemplateJson =
        niimbot_lego.staticParseLayoutStyle2CanvasJson(layoutScheme, _width.toDouble(), _height.toDouble(), 2);
    _logger.log("=========initLegoJsonData========Init canvasJsonData==> ${schemeTemplateJson}");
    //生成的空版式模版里面没有背景图信息，从scheme中取出来填充下
    // String templateJson = fillSchemeTemplateData(schemeTemplateJson, formData, bgImageUrl);
    String templateJson = await fillSchemeTemplateDataV2(schemeTemplateJson, formData, bgImageUrl, isPreview);
    return templateJson;
  }

  static Future<String> generateSchemeDataSourceTemplate(
      String layoutScheme, List<String> colNames, List<String> rowDataIds, bool isPreview) async {
    _logger.log("=========initLegoJsonData========Init layoutJson==> ${layoutScheme}");

    Map<String, dynamic> jsonMap;
    jsonMap = json.decode(layoutScheme);
    num _width = jsonMap['width'] ?? 0;
    num _height = jsonMap['height'] ?? 0;
    String bgImageUrl = jsonMap['image'] ?? "";
    String schemeTemplateJson =
        niimbot_lego.staticParseLayoutStyle2CanvasJson(layoutScheme, _width.toDouble(), _height.toDouble(), 2);
    _logger.log("=========initLegoJsonData========Init canvasJsonData==> ${schemeTemplateJson}");
    //生成的空版式模版里面没有背景图信息，从scheme中取出来填充下
    // String templateJson = fillSchemeTemplateData(schemeTemplateJson, formData, bgImageUrl);
    String templateJson =
        await fillSchemeDataSourceTemplateDataV2(schemeTemplateJson, colNames, rowDataIds, bgImageUrl, isPreview);
    return templateJson;
  }

  static String fillSchemeTemplateData(String canvasTemplateJson, String formData, String bgImageUrl) {
    Map<String, dynamic> jsonMap = jsonDecode(canvasTemplateJson);
    jsonMap["backgroundImage"] = bgImageUrl;
    Map<String, dynamic> valueRulesMap = jsonMap["valueRules"] ?? {};
    LayoutSchemeValueRules valueRules = LayoutSchemeValueRules.fromJson(valueRulesMap);
    //根据元素id--找到rules label---在从labelValueMap中根据label找到value
    Map<String, dynamic> elementIdToLabelMap = {};
    valueRules.rules.forEach((rule) => elementIdToLabelMap[rule.elementId] = rule.inputLabel);

    TemplateData _templateData = TemplateData.fromJson(jsonMap);

    List<dynamic> formJson = jsonDecode(formData);
    List<LayoutSchemeFormItem> formItems = formJson.map((e) => LayoutSchemeFormItem.fromJson(e)).toList();
    Map<String, String> labelValueMap = {};
    formItems.forEach((formItem) => labelValueMap[formItem.label] = formItem.value);
    _templateData.elements.forEach((element) {
      if (elementIdToLabelMap.containsKey(element.id)) {
        String? label = elementIdToLabelMap[element.id]?.toString();
        if (label != null && labelValueMap.containsKey(label)) {
          String? value = labelValueMap[label]?.toString();
          element.value = value;
          debugPrint("element value=$value");
        }
      }
    });
    String resultJson = jsonEncode(_templateData.toJson());
    return resultJson;
  }

  static Future<String> fillSchemeTemplateDataV2(
      String canvasTemplateJson, String formData, String bgImageUrl, bool isPreview) async {
    Map<String, dynamic> jsonMap = jsonDecode(canvasTemplateJson);
    jsonMap["backgroundImage"] = bgImageUrl;
    jsonMap["id"] = DateTime.now().microsecondsSinceEpoch.toString();
    List<dynamic> formDataJson = [];
    try {
      var formJson = jsonDecode(formData);
      if (formJson is List) {
        formDataJson = formJson;
      }
    } catch (e) {
      return canvasTemplateJson;
    }
    niimbotTemplateData.TemplateData templateData =
        await TemplateParse.parseLayoutSchemaFormData(jsonMap, formDataJson, isPreview);
    String resultJson = jsonEncode(templateData.toJson());
    return resultJson;
  }

  static Future<String> fillSchemeDataSourceTemplateDataV2(String canvasTemplateJson, List<String> colNames,
      List<String> rowDataIds, String bgImageUrl, bool isPreview) async {
    Map<String, dynamic> jsonMap = jsonDecode(canvasTemplateJson);
    jsonMap["backgroundImage"] = bgImageUrl;
    jsonMap["id"] = DateTime.now().microsecondsSinceEpoch.toString();
    List<dynamic> formDataJson = [];
    niimbotTemplateData.TemplateData templateData =
        await TemplateParse.parseLayoutSchemaFormData(jsonMap, formDataJson, isPreview);
    String resultJson = jsonEncode(templateData.toJson());
    return resultJson;
  }

  static String defaultFormDataJson =
      "[{\"label\": \"废物类别\", 		\"name\": \"category\", 		\"value\": \"HW01 医疗废物\" 	}, 	{ 		\"label\": \"废物代码\", 		\"name\": \"code\", 		\"value\": \"841-001-01\" 	}, 	{ 		\"label\": \"废物名称\", 		\"name\": \"name\", 		\"value\": \"废电池\" 	}, 	{ 		\"label\": \"废物形态\", 		\"name\": \"form\", 		\"value\": \"固态\" 	}, 	{ 		\"label\": \"主要成份\", 		\"name\": \"mainComponents\", 		\"value\": \"铅、镉、汞\" 	}, 	{ 		\"label\": \"有害成分\", 		\"name\": \"hazardousComponents\", 		\"value\": \"重金属\" 	}, 	{ 		\"label\": \"毒性\", 		\"name\": \"toxicity\", 		\"value\": true 	}, 	{ 		\"label\": \"易燃\", 		\"name\": \"combustible\", 		\"value\": true 	}, 	{ 		\"label\": \"反应性\", 		\"name\": \"reactivity\", 		\"value\": false 	}, 	{ 		\"label\": \"腐蚀性\", 		\"name\": \"corrosiveness\", 		\"value\": false 	}, 	{ 		\"label\": \"感染性\", 		\"name\": \"infectious\", 		\"value\": false 	}, 	{ 		\"label\": \"产生/收集单位\", 		\"name\": \"company\", 		\"value\": \"某电池制造厂\" 	}, 	{ 		\"label\": \"注意事项\", 		\"name\": \"precautions\", 		\"value\": \"不要用手摸\" 	}, 	{ 		\"label\": \"联系人和联系方式\", 		\"name\": \"contactInfoStr\", 		\"value\": \"张三 18062567777\" 	}, 	{ 		\"label\": \"废物重量\", 		\"name\": \"weight\", 		\"value\": \"100g\" 	}, 	{ 		\"label\": \"产生日期\", 		\"name\": \"generationDate\", 		\"value\": \"2023-10-01\" 	}, 	{ 		\"label\": \"备注\", 		\"name\": \"remarks\", 		\"value\": \"需特殊处理\" 	}, 	{ 		\"label\": \"二维码\", 		\"name\": \"externalLinks\", 		\"value\": \"https://shorty.jc-test.cn/SPJxOxcACLOJ\" 	}, 	{ 		\"label\": \"数字识别码\", 		\"name\": \"digitalIdentificationCode\", 		\"value\": \"91420117MA4F2R0Q5F8410010120241016001\" 	} ]";
  static String defaultLayoutScheme =
      "{\"id\":\"9a8718ae-213b-4a37-b5dc-801d1ba6087d\",\"version\":\"1.0.0\",\"image\":\"https://oss-print-fat.jc-test.cn/public_resources/labels/DN202406170008-7d241c33e74d53e9740aed64da1b4571.png\",\"width\":100.0,\"height\":100.0,\"scalable\":true,\"padding\":[0.0,0.0,0.0,0.0],\"areas\":[{\"id\":\"be66796784f346d4bbb35fdafa744f92\",\"label\":\"废物名称\",\"x\":18.671371459960938,\"y\":14.465399742126465,\"width\":48.42789077758789,\"height\":5.975086688995361,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"text\",\"typeProperty\":{\"fontSize\":1.5,\"fontCode\":\"ZT001\",\"fontColor\":0,\"layout\":0,\"boxMode\":4,\"xAlign\":0,\"yAlign\":2},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0},{\"id\":\"4ccdf7fb858943a695d16242c7018f33\",\"label\":\"废物类别\",\"x\":18.67131233215332,\"y\":21.385496139526367,\"width\":48.428009033203125,\"height\":5.974822998046875,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"text\",\"typeProperty\":{\"fontSize\":1.5,\"fontCode\":\"ZT001\",\"fontColor\":0,\"layout\":0,\"boxMode\":4,\"xAlign\":0,\"yAlign\":2},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0},{\"id\":\"e1b0365620eb4ea79fc85432d76d2596\",\"label\":\"废物代码\",\"x\":18.671371459960938,\"y\":27.989744186401367,\"width\":19.182437896728516,\"height\":5.974822998046875,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"text\",\"typeProperty\":{\"fontSize\":1.5,\"fontCode\":\"ZT001\",\"fontColor\":0,\"layout\":0,\"boxMode\":4,\"xAlign\":0,\"yAlign\":2},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0},{\"id\":\"9e250987c8d34bfa845865411c76258a\",\"label\":\"废物形态\",\"x\":52.4781608581543,\"y\":27.989744186401367,\"width\":14.620854377746582,\"height\":5.974822998046875,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"text\",\"typeProperty\":{\"fontSize\":1.5,\"fontCode\":\"ZT001\",\"fontColor\":0,\"layout\":0,\"boxMode\":4,\"xAlign\":0,\"yAlign\":2},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0},{\"id\":\"69a3dde2811f4c7b92d88121c757c0f3\",\"label\":\"主要成分\",\"x\":18.67131233215332,\"y\":34.5911750793457,\"width\":48.428009033203125,\"height\":9.276723861694336,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"text\",\"typeProperty\":{\"fontSize\":1.5,\"fontCode\":\"ZT001\",\"fontColor\":0,\"layout\":0,\"boxMode\":4,\"xAlign\":0,\"yAlign\":1},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0},{\"id\":\"a4f2a27fb0ea46bb92d3aa9bbc6aa0d5\",\"label\":\"有害成分\",\"x\":18.67131233215332,\"y\":44.65578842163086,\"width\":48.428009033203125,\"height\":9.26097583770752,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"text\",\"typeProperty\":{\"fontSize\":1.5,\"fontCode\":\"ZT001\",\"fontColor\":0,\"layout\":0,\"boxMode\":4,\"xAlign\":0,\"yAlign\":1},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0},{\"id\":\"d832c1e2470f44a89767df505c0d16a6\",\"label\":\"注意事项\",\"x\":18.67131233215332,\"y\":54.877532958984375,\"width\":76.57233428955078,\"height\":9.26097583770752,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"text\",\"typeProperty\":{\"fontSize\":1.5,\"fontCode\":\"ZT001\",\"fontColor\":0,\"layout\":0,\"boxMode\":4,\"xAlign\":0,\"yAlign\":1},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0},{\"id\":\"d156caed3b764d4f8655b15c3603e704\",\"label\":\"数字识别码\",\"x\":21.73157501220703,\"y\":65.09940338134766,\"width\":73.51207733154297,\"height\":5.026336193084717,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"text\",\"typeProperty\":{\"fontSize\":1.5,\"fontCode\":\"ZT001\",\"fontColor\":0,\"layout\":0,\"boxMode\":4,\"xAlign\":0,\"yAlign\":2},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0},{\"id\":\"4067e2c217104c15a45efefa96b5fb99\",\"label\":\"产生/收集单位\",\"x\":25.5057430267334,\"y\":71.7049331665039,\"width\":45.68132019042969,\"height\":5.018851280212402,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"text\",\"typeProperty\":{\"fontSize\":1.5,\"fontCode\":\"ZT001\",\"fontColor\":0,\"layout\":0,\"boxMode\":4,\"xAlign\":0,\"yAlign\":2},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0},{\"id\":\"cde57f9a6c9d4aa9abd8a1596b5c0e90\",\"label\":\"联系人和联系方式\",\"x\":30.381349563598633,\"y\":77.83868408203125,\"width\":40.80571365356445,\"height\":5.018851280212402,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"text\",\"typeProperty\":{\"fontSize\":1.5,\"fontCode\":\"ZT001\",\"fontColor\":0,\"layout\":0,\"boxMode\":4,\"xAlign\":0,\"yAlign\":2},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0},{\"id\":\"3a70d10e51644b5dab132faa088898b2\",\"label\":\"产生日期\",\"x\":18.671371459960938,\"y\":84.12798309326172,\"width\":18.86791229248047,\"height\":5.018851280212402,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"text\",\"typeProperty\":{\"fontSize\":1.5,\"fontCode\":\"ZT001\",\"fontColor\":0,\"layout\":0,\"boxMode\":4,\"xAlign\":0,\"yAlign\":2},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0},{\"id\":\"722a23d27adb485097eaeac046fac036\",\"label\":\"废物重量\",\"x\":52.4781608581543,\"y\":84.12798309326172,\"width\":18.70890235900879,\"height\":5.018851280212402,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"text\",\"typeProperty\":{\"fontSize\":1.5,\"fontCode\":\"ZT001\",\"fontColor\":0,\"layout\":0,\"boxMode\":4,\"xAlign\":0,\"yAlign\":2},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0},{\"id\":\"6d140bdf900241e4b993baa471ea7831\",\"label\":\"备注\",\"x\":14.032658576965332,\"y\":90.1035385131836,\"width\":57.22945022583008,\"height\":5.018851280212402,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"text\",\"typeProperty\":{\"fontSize\":1.5,\"fontCode\":\"ZT001\",\"fontColor\":0,\"layout\":0,\"boxMode\":4,\"xAlign\":0,\"yAlign\":2},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0},{\"id\":\"d6adb31b873440e5af6dc57ba1900fb2\",\"label\":\"毒性\",\"x\":69.3002700805664,\"y\":30.974822998046875,\"width\":11.006529808044434,\"height\":6.60401725769043,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"text\",\"typeProperty\":{\"fontSize\":1.5,\"fontCode\":\"ZT001\",\"fontColor\":0,\"layout\":0,\"boxMode\":4,\"xAlign\":1,\"yAlign\":2},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0},{\"id\":\"c31b3b8435cb4782ad71bf0c21cd9592\",\"label\":\"易燃\",\"x\":82.82300567626953,\"y\":30.974708557128906,\"width\":11.007081031799316,\"height\":6.604248523712158,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"text\",\"typeProperty\":{\"fontSize\":1.5,\"fontCode\":\"ZT001\",\"fontColor\":0,\"layout\":0,\"boxMode\":4,\"xAlign\":1,\"yAlign\":2},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0},{\"id\":\"21b3a97711384c6e86df035cc2b62961\",\"label\":\"反应性\",\"x\":69.29999542236328,\"y\":47.95639419555664,\"width\":11.007081031799316,\"height\":6.604248523712158,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"text\",\"typeProperty\":{\"fontSize\":1.5,\"fontCode\":\"ZT001\",\"fontColor\":0,\"layout\":0,\"boxMode\":4,\"xAlign\":1,\"yAlign\":2},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0},{\"id\":\"6829ed444e3b4c63afee45afec6aa13d\",\"label\":\"腐蚀性\",\"x\":82.82300567626953,\"y\":47.95639419555664,\"width\":11.007081031799316,\"height\":6.604248523712158,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"text\",\"typeProperty\":{\"fontSize\":1.5,\"fontCode\":\"ZT001\",\"fontColor\":0,\"layout\":0,\"boxMode\":4,\"xAlign\":1,\"yAlign\":2},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0},{\"id\":\"597caa930f3c474698668433633ddd95\",\"label\":\"感染性\",\"x\":75.5903091430664,\"y\":6.1320719718933105,\"width\":11.007081031799316,\"height\":6.604248523712158,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"text\",\"typeProperty\":{\"fontSize\":1.5,\"fontCode\":\"ZT001\",\"fontColor\":0,\"layout\":0,\"boxMode\":4,\"xAlign\":1,\"yAlign\":2},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0},{\"id\":\"a119b2b0b0d9432680adbdadc23e420c\",\"label\":\"\",\"x\":72.13060760498047,\"y\":71.6978759765625,\"width\":23.28874969482422,\"height\":23.28874969482422,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":null,\"copyProperty\":null,\"type\":\"qrcode\",\"typeProperty\":{\"correctLevel\":1,\"codeType\":31},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0.0}]}";
}
