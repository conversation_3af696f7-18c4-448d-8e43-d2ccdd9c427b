import 'dart:async';

import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/model/excel_bind_info.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element.dart';
import 'package:niimbot_flutter_canvas/src/model/template_data.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/escape_utils.dart';
import 'package:uuid/uuid.dart';
import 'package:niimbot_excel/models/data_source.dart';
import 'package:text/network/dio_utils.dart';
import 'package:text/network/http_api.dart';
import 'package:flutter/foundation.dart';

import '../nlp/nlp_util.dart';

///
/// 商品库模板相关字段：
/// commodityTemplate = true
///"dataSourceBindInfo": {
/// 		"page": 1,
/// 		"total": 1
/// 	}
/// 	"dataSourceModifies": {
/// 		"f97efaac83cd46f9b503bad27f2770c1": {
/// 			"0": {
/// 				"delimiter": "：",
/// 				"prefix": null,
/// 				"suffix": null,
/// 				"title": null,
/// 				"useTitle": false,
/// 				"value": null
/// 			}
/// 		}
/// 	},
/// 	"dataSources": [
/// 		{
/// 			"hash": "61c7611679bd42a69f73eaf2a53d1609",
///			"headers": {},
/// 			"name": "",
/// 			"params": {
/// 				"ids": [
/// 					"10111432"
/// 				]
/// 			},
/// 			"range": [],
/// 			"type": "commodity",
/// 			"uri": ""
/// 		}
/// 	]
/// element中的不同：
///   contentTitle = 品名
///   "dataBind": [
/// 				"",
/// 				"commodity"
/// 			]
///   "value": "B0"
///
///
///
class TemplateConvertUtils {

  static Future<Map<String, dynamic>> convert2DynamicTemplate(
      Map<String, dynamic> sourceTemplate, List<String> headers, List<TextSimilarityResult> similarityResults,
      {DataSourceType dataSourceType = DataSourceType.commodity}) async {
    final targetTemplate =
        _staticTemplateToDataSourceTemplate(sourceTemplate, headers, similarityResults, dataSourceType);
    return targetTemplate.toJson();
  }

  /// 静态模板转成商品库模板
  /// [sourceTemplate] 源模板数据
  /// [headers] 商品库表头列表
  /// [similarityResults] 文本相似度结果列表
  static TemplateData _staticTemplateToDataSourceTemplate(Map<String, dynamic> sourceTemplate, List<String> headers,
      List<TextSimilarityResult> similarityResults, DataSourceType dataSourceType) {
    // 1. 复制模板数据
    TemplateData _templateData = EscapeUtils.cloneTemplateData(TemplateData.fromJson(sourceTemplate));
    _templateData.profile.extrain.userId = '-1';
    _templateData.profile.extrain.templateType = -1;
    bool hasCommodity = false;
    // 2. 处理元素
    _templateData.elements.forEach((element) {
      if (element is TextElement) {
        var sourceElement = sourceTemplate['elements'].firstWhere(
          (e) => e['id'] == element.id,
          orElse: () => null,
        );
        if (sourceElement != null) {
          element.contentTitle = sourceElement['name'];
          element.fieldName = sourceElement['name'];
        }

        // 根据相似度结果处理value和dataBind
        String? originValue = element.contentTitle;
        if (originValue != null && originValue.isNotEmpty) {
          // 在相似度结果中查找对应的绑定关系
          TextSimilarityResult? matchedResult;
          try {
            matchedResult = similarityResults.firstWhere(
              (result) => result.queryText == originValue,
            );
          } catch (e) {
            matchedResult = null;
          }

          if (matchedResult != null) {
            // 查找referenceText在headers中的索引
            int index = headers.indexOf(matchedResult.referenceText);
            if (index >= 0 && index < headers.length) {
              element.contentTitle = matchedResult.referenceText;
              element.fieldName = matchedResult.referenceText;
              // 设置value为数据绑定格式
              element.value = '\${0⊙$index}';
              // 设置dataBind为商品库类型
              element.dataBind = ["", dataSourceType.getStringValue()];

              // 设置元素修改信息
              if (_templateData.modify == null) {
                _templateData.modify = {};
              }
              if (!_templateData.modify!.containsKey(element.id)) {
                _templateData.modify![element.id] = {};
              }
              // // 设置全局修改信息
              // _templateData.modify![element.id]!['0'] = DataBindModify(
              //     useTitle: true, delimiter: "：", title: headers[index], prefix: null, suffix: null, value: null);
              hasCommodity = true;
            }
          } else {
            element.dataBind = null;
          }
        }
      }
    });

    if (!hasCommodity) {
      return _templateData;
    }
    // 3. 设置绑定信息
    ExcelPageInfo pageInfo = ExcelPageInfo(page: 1, total: 1);
    _templateData.bindInfo = pageInfo;

    // 4. 创建数据源
    String dataSourceHash = Uuid().v4().replaceAll("-", "");
    DataSource dataSource = DataSource(type: dataSourceType, uri: "", name: "", headers: {}, hash: dataSourceHash);
    _templateData.dataSource = [dataSource];

    // 5. 设置分页信息
    if (_templateData.bindInfo != null) {
      _templateData.currentPageIndex = _templateData.bindInfo!.page == null ? 0 : _templateData.bindInfo!.page! - 1;
      _templateData.totalPage = _templateData.bindInfo!.total == null ? 1 : _templateData.bindInfo!.total!;
    }

    return _templateData;
  }
}
