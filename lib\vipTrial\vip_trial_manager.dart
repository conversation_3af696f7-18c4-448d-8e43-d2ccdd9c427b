import 'package:flutter/material.dart';
import 'package:text/application.dart';
import 'package:text/network_connectivity.dart';
import 'package:text/vipTrial/trial_activity.dart';
import 'package:text/vipTrial/vip_trial_business.dart';
import 'package:tuple/tuple.dart';

import '../business/user/user_login_helper.dart';
import '../routers/custom_navigation.dart';
import '../tools/to_Native_Method_Channel.dart';
import '../utils/common_fun.dart';
import '../utils/toast_util.dart';

/// vip试用权益管理
class VipTrialManager {
  static VipTrialManager? _instance;

  VipTrialManager._();

  factory VipTrialManager() {
    return _instance ??= VipTrialManager._();
  }

  /// 是否需要再次请求试用，在未登录下登录返回后，等所有权益获取完毕后需要再次请求使用
  bool isReTrialActivity = false;

  /// 权益上下文
  BuildContext? context;

  /// 权益类型
  TrialActivityType? trialActivityType;

  /// 权益Action
  VoidCallback? trailActivity;

  /// 所有试用权益列表
  Map<TrialActivityType, TrialActivity>? trialActivities;

  /// 获取所有试用场景
  void getAllTrialActivities({ValueChanged<Tuple2>? result}) {
    if (!NetworkConnectivity.instance.isReachable) {
      return;
    }

    VipTrialBusiness.requestAllVipTrailActivityPrivileges((_trialActivities) {
      // 筛选未启用的权益
      _trialActivities.removeWhere((key, value) => value.activityStatus == TrialActivityStatus.NotEnabled);
      trialActivities = _trialActivities;
      result?.call(Tuple2(trialActivities, null));
      if (isReTrialActivity && context != null && trialActivityType != null) {
        // 需要再次试用，登录返回后调用
        this.isReTrialActivity = false;
        // 重试试用
        trialActivity(context: context!, trialActivityType: trialActivityType!, trailActivity: trailActivity);
        this
          ..context = null
          ..trialActivityType = null;
      }
    });
  }

  /// 试用功能
  /// BuildContext context
  /// bool isShowAlertFirst是否优先展示试用弹窗，再进行登录购买操作，默认值为YES
  /// trailActivity试用对应功能
  void trialActivity(
      {required BuildContext context,
      required TrialActivityType trialActivityType,
      bool isShowAlertFirst = true,
      bool needBackPressPop = false,
      bool needCancelPop = true,
      String vipType = "",
      VoidCallback? cancelAction,
      VoidCallback? trailActivity,
      VoidCallback? dismissCallback,
      Widget? extraWidget}) {
    // 次数弹窗&VIP弹窗
    ValueChanged<TrialActivity?> privilegeCountAlert = (TrialActivity? activity) {
      if ((activity?.count ?? 0) > 0) {
        // 存在试用次数
        TrialVipGuideAlert alert = TrialVipGuideAlert().build(trialActivityType, activity);

        showCustomOpenVipGuideAlert(
          context,
          headImagePath: alert.headImagePath,
          title: alert.title,
          titleColor: alert.titleColor,
          subTile: alert.subTile,
          cancelDes: alert.cancelDes,
          confirmDes: alert.confirmDes,
          confirmDesColor: alert.confirmDesColor,
          confirmDesTitleColor: alert.confirmDesTitleColor,
          isShowClose: true,
          useRootNavigator: false,
          extraWidget: extraWidget,
          closeCallBack: () {
            cancelAction?.call();
          },
          cancelCallBack: () {
            ToNativeMethodChannel().sendTrackingToNative({
              "track": "click",
              "posCode": "007_271_247",
              "ext": {"source": trialActivityType.privilegeCode}
            });
            // 检查网络是否异常
            if (!NetworkConnectivity.instance.isReachable) {
              showToast(msg: intlanguage("app100000354", "网络异常"));
              return;
            }
            // 接口扣除次数，本地保存返回次数
            VipTrialBusiness.usePrivilege(trialActivityType, success: (availableCount) {
              // 回写可使用次数
              activity?.count = availableCount;
              // 继续功能
              trailActivity?.call();
            }, fail: (error) {
              // 此处服务端返回400，按道理应该返回200，服务可到达只是返回不可用
              if (error.item1 == 50101) {
                showToast(msg: intlanguage('app100001385', 'VIP试用次数已用完'));
                // 回写可使用次数
                activity?.count = 0;
              } else {
                showToast(msg: intlanguage('app100000844', '服务异常'));
              }
            });
          },
          confirmCallBack: () {
            // 开通会员
            cancelAction?.call();
            ToNativeMethodChannel().sendTrackingToNative({
              "track": "click",
              "posCode": "007_271_246",
              "ext": {"source": trialActivityType.privilegeCode}
            });
            CustomNavigation.gotoNextPage('ToVipPage', {"vipType": vipType}).then((value) {
              if (value is Map && value['result'] is int && value['result'] > 0) {
                // 继续功能
                trailActivity?.call();
              }
            });
          },
          needBackPressPop: needBackPressPop,
          needCancelPop: needCancelPop,
        ).then((value) {
          dismissCallback?.call();
        });
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "show",
          "posCode": "007_271",
          "ext": {"source": trialActivityType.privilegeCode}
        });
      } else {
        // 不存在试用次数
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "show",
          "posCode": "108_083",
          "ext": {"pop_type": "2", "source": trialActivityType.privilegeCode}
        });
        TrialVipGuideAlert alert = TrialVipGuideAlert().build(trialActivityType, activity);
        showNimmbotDialog(context,
            title: alert.vipOverTitle,
            cancelDes: intlanguage('app00030', '取消'),
            confirmDes: alert.vipOverConfirmDes,
            confirmDesColor: alert.vipOverConfirmDesColor,
            useRootNavigator: false, cancelAction: () {
          cancelAction?.call();
          ToNativeMethodChannel().sendTrackingToNative({
            "track": "click",
            "posCode": "108_083_110",
            "ext": {"pop_type": "2", "source": trialActivityType.privilegeCode}
          });
        }, confirmAction: () {
          if (!NetworkConnectivity.instance.isReachable) {
            showToast(msg: intlanguage("app100000354", "网络异常"));
            return;
          }
          // 开通会员
          cancelAction?.call();
          ToNativeMethodChannel().sendTrackingToNative({
            "track": "click",
            "posCode": "108_083_107",
            "ext": {"pop_type": "2", "source": trialActivityType.privilegeCode}
          });
          CustomNavigation.gotoNextPage('ToVipPage', {"vipType": vipType}).then((value) {
            if (value is Map && value['result'] is int && value['result'] > 0) {
              // 继续功能
              trailActivity?.call();
            }
          });
        }).then((value) {
          dismissCallback?.call();
        });
      }
    };

    // 弹窗展示逻辑
    VoidCallback activityLogic = () {
      // 已登录,查看当前用户是否VIP
      if (_isVip(trialActivityType)) {
        // 继续VIP功能
        trailActivity?.call();
      } else {
        // 是否已领用活动
        TrialActivity? activity = trialActivities?[trialActivityType];
        if (activity != null) {
          // 权益是否处于有效期内，有效期外不可领用
          if (DateTime.now().isBefore(DateTime.fromMillisecondsSinceEpoch(activity.activityDeadline?.toInt() ?? 0))) {
            // 有效期内，查看是否可领用
            switch (activity.activityStatus) {
              case TrialActivityStatus.ActivatedNotReceive:
                {
                  // 有效未领用
                  VipTrialBusiness.probationPrivilege(trialActivityType, success: (isSuccess) {
                    // if (isSuccess) {
                    // 成功领用，重新拉取所有的权益
                    getAllTrialActivities(result: (_) {
                      // 已领用活动后，拿取新返回的权益，查看次数
                      privilegeCountAlert(trialActivities?[trialActivityType]);
                    });
                    // }
                  });
                }
                break;
              case TrialActivityStatus.ActivatedReceived:
              case TrialActivityStatus.Removed:
                {
                  // 已领用活动，查看次数
                  privilegeCountAlert(activity);
                }
                break;
              default:
                break;
            }
          } else {
            // 有效期外，查看是否有次数
            privilegeCountAlert(activity);
          }
        } else {
          ToNativeMethodChannel().sendTrackingToNative({
            "track": "show",
            "posCode": "108_083",
            "ext": {"pop_type": "2", "source": trialActivityType.privilegeCode}
          });
          TrialVipGuideAlert alert = TrialVipGuideAlert().build(trialActivityType, activity);
          showNimmbotDialog(context,
              title: alert.vipOverTitle,
              cancelDes: intlanguage('app00030', '取消'),
              confirmDes: alert.vipOverConfirmDes,
              confirmDesColor: alert.vipOverConfirmDesColor,
              useRootNavigator: false, cancelAction: () {
            cancelAction?.call();
            ToNativeMethodChannel().sendTrackingToNative({
              "track": "click",
              "posCode": "108_083_110",
              "ext": {"pop_type": "2", "source": trialActivityType.privilegeCode}
            });
          }, confirmAction: () {
            if (!NetworkConnectivity.instance.isReachable) {
              showToast(msg: intlanguage("app100000354", "网络异常"));
              return;
            }
            // 开通会员
            cancelAction?.call();
            ToNativeMethodChannel().sendTrackingToNative({
              "track": "click",
              "posCode": "108_083_107",
              "ext": {"pop_type": "2", "source": trialActivityType.privilegeCode}
            });
            CustomNavigation.gotoNextPage('ToVipPage', {"vipType": vipType}).then((value) {
              if (value is Map && value['result'] is int && value['result'] > 0) {
                // 继续功能
                trailActivity?.call();
              }
            });
          }).then((value) {
            dismissCallback?.call();
          });
        }
      }
    };

    // 登录Action
    VoidCallback loginAction = () {
      UserLoginHelper().gotoLogin(context, loginFailed: () {
        // 在未登录下登录返回后，等所有权益获取完毕后需要再次请求使用
        cancelAction?.call();
        // 置空所有值，防止回调错误问题
        this
          ..isReTrialActivity = false
          ..context = null
          ..trialActivityType = null;
      }, loginSucceed: () {
        cancelAction?.call();
        // 在未登录下登录返回后，等所有权益获取完毕后需要再次请求使用
        isReTrialActivity = true;
        this
          ..context = context
          ..trialActivityType = trialActivityType
          ..trailActivity = trailActivity;
      });
    };

    VoidCallback loginActionWithCompletion({
      VoidCallback? onLoginSucceed,
      VoidCallback? onLoginFailed,
    }) {
      return () {
        UserLoginHelper().gotoLogin(
          context,
          loginFailed: () {
            cancelAction?.call();
            // 置空所有值，防止回调错误问题
            this
              ..isReTrialActivity = false
              ..context = null
              ..trialActivityType = null;

            onLoginFailed?.call(); // 调用外部传入的失败闭包
          },
          loginSucceed: () {
            cancelAction?.call();
            isReTrialActivity = true;
            this
              ..context = context
              ..trialActivityType = trialActivityType
              ..trailActivity = trailActivity;

            onLoginSucceed?.call(); // 调用外部传入的成功闭包
          },
        );
      };
    }

    // 置空所有值，防止回调错误问题
    this
      ..isReTrialActivity = false
      ..context = null
      ..trialActivityType = null;

    // 查看试用类型是否存在权益试用，不存在则直接返回
    TrialActivity? activity = trialActivities?[trialActivityType];
    if (activity == null) {
      // 是否登录
      if (!Application.isLogin) {
        // 未登录检查网络状态
        if (!NetworkConnectivity.instance.isReachable) {
          showToast(msg: intlanguage('app100000625', '当前网络状态异常'));
        } else {
          showNimmbotDialog(context,
              title: intlanguage('app00210', '当前未登录，请先登录！'),
              cancelDes: intlanguage('app00030', '取消'),
              confirmDes: intlanguage('app01191', '立即登录'),
              useRootNavigator: false, cancelAction: () {
            cancelAction?.call();
          }, confirmAction: () {
            // cancelAction?.call();
            loginAction();
          }).then((value) {
            dismissCallback?.call();
          });
        }
      } else {
        if (_isVip(trialActivityType)) {
          // 继续VIP功能
          trailActivity?.call();
        } else {
          // 展示VIP购买
          ToNativeMethodChannel().sendTrackingToNative({
            "track": "show",
            "posCode": "108_083",
            "ext": {"pop_type": "2", "source": trialActivityType.privilegeCode}
          });

          TrialVipGuideAlert alert = TrialVipGuideAlert().build(trialActivityType, activity);
          showNimmbotDialog(context,
              title: alert.vipOverTitle,
              cancelDes: intlanguage('app00030', '取消'),
              confirmDes: alert.vipOverConfirmDes,
              confirmDesColor: alert.vipOverConfirmDesColor,
              useRootNavigator: false, cancelAction: () {
            cancelAction?.call();
            ToNativeMethodChannel().sendTrackingToNative({
              "track": "click",
              "posCode": "108_083_110",
              "ext": {"pop_type": "2", "source": trialActivityType.privilegeCode}
            });
          }, confirmAction: () {
            if (!NetworkConnectivity.instance.isReachable) {
              showToast(msg: intlanguage("app100000354", "网络异常"));
              return;
            }
            // 开通会员
            cancelAction?.call();
            ToNativeMethodChannel().sendTrackingToNative({
              "track": "click",
              "posCode": "108_083_107",
              "ext": {"pop_type": "2", "source": trialActivityType.privilegeCode}
            });
            CustomNavigation.gotoNextPage('ToVipPage', {"vipType": vipType}).then((value) {
              if (value is Map && value['result'] is int && value['result'] > 0) {
                // 继续功能
                trailActivity?.call();
              }
            });
          }).then((value) {
            dismissCallback?.call();
          });
        }
      }
      return;
    }

    // 是否登录
    if (!Application.isLogin) {
      // 未登录无网络
      if (!NetworkConnectivity.instance.isReachable) {
        showToast(msg: intlanguage('app100000625', '当前网络状态异常'));
        return;
      }
      // 未登录有网络
      if (isShowAlertFirst) {
        TrialVipGuideAlert alert = TrialVipGuideAlert().build(trialActivityType, activity);
        // 展示试一试+购买弹窗
        showCustomOpenVipGuideAlert(context,
                headImagePath: alert.headImagePath,
                title: alert.title,
                titleColor: alert.titleColor,
                cancelDes: alert.cancelDes,
                confirmDes: alert.confirmDes,
                confirmDesColor: alert.confirmDesColor,
                confirmDesTitleColor: alert.confirmDesTitleColor,
                isShowClose: true,
                extraWidget: extraWidget,
                useRootNavigator: false, closeCallBack: () {
          cancelAction?.call();
        }, cancelCallBack: () {
          ToNativeMethodChannel().sendTrackingToNative({
            "track": "click",
            "posCode": "007_271_247",
            "ext": {"source": trialActivityType.privilegeCode}
          });
          // loginActionWithCompletion(onLoginSucceed: () {
          //   // 继续功能
          //   trailActivity?.call();
          // }, onLoginFailed: () {
          //     // print("登录失败也处理下");
          // })();
          loginAction();
        }, confirmCallBack: () {
          // 开通会员
          cancelAction?.call();
          ToNativeMethodChannel().sendTrackingToNative({
            "track": "click",
            "posCode": "007_271_246",
            "ext": {"source": trialActivityType.privilegeCode}
          });
          CustomNavigation.gotoNextPage('ToVipPage', {"vipType": vipType}).then((value) {
            if (value is Map && value['result'] is int && value['result'] > 0) {
              // 继续功能
              trailActivity?.call();
            }
          });
        }, needBackPressPop: needBackPressPop, needCancelPop: needCancelPop)
            .then((value) {
          dismissCallback?.call();
        });
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "show",
          "posCode": "007_271",
          "ext": {"source": trialActivityType.privilegeCode}
        });
      } else {
        showNimmbotDialog(context,
            title: intlanguage('app00210', '当前未登录，请先登录！'),
            cancelDes: intlanguage('app00030', '取消'),
            confirmDes: intlanguage('app01191', '立即登录'),
            useRootNavigator: false, cancelAction: () {
          cancelAction?.call();
        }, confirmAction: () {
          loginAction();
        }).then((value) {
          dismissCallback?.call();
        });
      }
    } else {
      // 已登录无网络
      if (!NetworkConnectivity.instance.isReachable) {
        if (_isVip(trialActivityType)) {
          trailActivity?.call();
          return;
        }
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "show",
          "posCode": "108_083",
          "ext": {"pop_type": "2", "source": trialActivityType.privilegeCode}
        });
        TrialVipGuideAlert alert = TrialVipGuideAlert().build(trialActivityType, activity);
        showNimmbotDialog(context,
            title: alert.vipOverTitle,
            cancelDes: intlanguage('app00030', '取消'),
            confirmDes: alert.vipOverConfirmDes,
            confirmDesColor: alert.vipOverConfirmDesColor,
            useRootNavigator: false, cancelAction: () {
          cancelAction?.call();
          ToNativeMethodChannel().sendTrackingToNative({
            "track": "click",
            "posCode": "108_083_110",
            "ext": {"pop_type": "2", "source": trialActivityType.privilegeCode}
          });
        }, confirmAction: () {
          if (!NetworkConnectivity.instance.isReachable) {
            showToast(msg: intlanguage("app100000354", "网络异常"));
            return;
          }
          // 开通会员
          cancelAction?.call();
          ToNativeMethodChannel().sendTrackingToNative({
            "track": "click",
            "posCode": "108_083_107",
            "ext": {"pop_type": "2", "source": trialActivityType.privilegeCode}
          });
          CustomNavigation.gotoNextPage('ToVipPage', {"vipType": vipType}).then((value) {
            if (value is Map && value['result'] is int && value['result'] > 0) {
              // 继续功能
              trailActivity?.call();
            }
          });
        }).then((value) {
          dismissCallback?.call();
        });
        return;
      }
      // 已登录有网络
      activityLogic();
    }
  }

  _isVip(TrialActivityType trialActivityType) {
    switch (trialActivityType) {
      case TrialActivityType.cablePrint:
        return Application.user!.cableVip;
      default:
        return Application.user!.isVip;
    }
  }
}
