//
//  JCPipeLineCalibrationView.m
//  Runner
//
//  Created by Chance on 2024/7/9.
//

#import "JCPipeLineCalibrationView.h"

@interface JCPipeLineCalibrationView() <UITextFieldDelegate>

/// 标题
@property (nonatomic, strong) UILabel *titleLabel;

/// 副标题
@property (nonatomic, strong) UILabel *subtitleLabel;

/// 输入框容器
@property (nonatomic, strong) UIView *inputContainerView;

/// 输入框
@property (nonatomic, strong) UITextField *inputTextField;

/// 单位标签
@property (nonatomic, strong) UILabel *unitLabel;

/// 顶部分割线
@property (nonatomic, strong) UIView *topSeparatorView;

/// 取消按钮
@property (nonatomic, strong) UIButton *cancelButton;

/// 中间分割线
@property (nonatomic, strong) UIView *middleSeparatorView;

/// 确定按钮
@property (nonatomic, strong) UIButton *confirmButton;

@end

@implementation JCPipeLineCalibrationView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setUp];
    }
    return self;
}

- (void)setUp {
    [self addSubview:self.titleLabel];
    [self addSubview:self.subtitleLabel];
    [self addSubview:self.inputContainerView];
    [self.inputContainerView addSubview:self.inputTextField];
    [self.inputContainerView addSubview:self.unitLabel];
    [self addSubview:self.topSeparatorView];
    [self addSubview:self.cancelButton];
    [self addSubview:self.middleSeparatorView];
    [self addSubview:self.confirmButton];
    
    [self setUpConstraints];
    
    [self mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(270);
        make.left.equalTo(self.attachedView).offset((SCREEN_WIDTH - 300)/2);
    }];
    self.attachedView.mm_dimBackgroundView.backgroundColor = HEX_RGBA(0x000000, 0.35);
    self.backgroundColor = HEX_RGB(0xFFFFFF);
    self.layer.cornerRadius = 12;
    self.type = MMPopupTypeCustom;
    self.withKeyboard = YES;
}

- (void)setUpConstraints {
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(20);
        make.centerX.equalTo(self);
        make.width.lessThanOrEqualTo(self).offset(-20);
    }];
    
    [self.subtitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(6);
        make.centerX.equalTo(self);
        make.width.lessThanOrEqualTo(self).offset(-20);
    }];
    
    [self.inputContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.subtitleLabel.mas_bottom).offset(6);
        make.leading.trailing.equalTo(self).inset(18);
        make.height.mas_equalTo(36);
    }];
    
    [self.inputTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.inputContainerView);
        make.leading.equalTo(self.inputContainerView).offset(10);
    }];
    
    [self.unitLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.inputContainerView).inset(10);
        make.centerY.equalTo(self.inputContainerView);
        make.leading.equalTo(self.inputTextField.mas_trailing).offset(10);
    }];
    
    [self.topSeparatorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.inputContainerView.mas_bottom).offset(20);
        make.leading.trailing.equalTo(self);
        make.height.mas_equalTo(0.5);
    }];
    
    [self.cancelButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.topSeparatorView.mas_bottom);
        make.bottom.leading.equalTo(self);
        make.height.mas_equalTo(50);
        make.trailing.equalTo(self.mas_centerX);
    }];
    
    [self.middleSeparatorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.cancelButton);
        make.leading.equalTo(self.cancelButton.mas_trailing);
        make.width.mas_equalTo(0.5);
        make.top.equalTo(self.topSeparatorView.mas_bottom);
    }];
    
    [self.confirmButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.topSeparatorView.mas_bottom);
        make.bottom.trailing.equalTo(self);
        make.height.mas_equalTo(50);
        make.leading.equalTo(self.mas_centerX);
    }];
}

// MARK: -- Lazy load --

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        _titleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app100001465",@"走管校准设置");
        _titleLabel.font = MY_FONT_Bold(16);
        _titleLabel.textColor = HEX_RGB(0x000000);
        _titleLabel.numberOfLines = 0;
        _titleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _titleLabel;
}

- (UILabel *)subtitleLabel {
    if (!_subtitleLabel) {
        _subtitleLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        _subtitleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app100001466",@"填写实际测量长度（50-150mm）");
        _subtitleLabel.font = MY_FONT_Regular(14);
        _subtitleLabel.textColor = HEX_RGB(0x000000);
        _subtitleLabel.numberOfLines = 0;
        _subtitleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _subtitleLabel;
}

- (UIView *)inputContainerView {
    if (!_inputContainerView) {
        _inputContainerView = [[UIView alloc] initWithFrame:CGRectZero];
        _inputContainerView.layer.borderColor = HEX_RGB(0xDDDDDD).CGColor;
        _inputContainerView.layer.borderWidth = 0.5;
        _inputContainerView.layer.cornerRadius = 8.0;
    }
    return _inputContainerView;
}

- (UITextField *)inputTextField {
    if (!_inputTextField) {
        _inputTextField = [[UITextField alloc] initWithFrame:CGRectZero];
        _inputTextField.placeholder = XY_LANGUAGE_TITLE_NAMED(@"app100001467",@"请输入");
        _inputTextField.font = MY_FONT_Regular(13);
        _inputTextField.tintColor = HEX_RGB(0xFB4B42);
        _inputTextField.keyboardType = UIKeyboardTypeDecimalPad; // 设置为数字键盘
        _inputTextField.delegate = self;
    }
    return _inputTextField;
}

- (UILabel *)unitLabel {
    if (!_unitLabel) {
        _unitLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        _unitLabel.text = @"mm";
        _unitLabel.font = MY_FONT_Regular(14);
        _unitLabel.textColor = HEX_RGB(0x999999);
        [_unitLabel setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    }
    return _unitLabel;
}

- (UIView *)topSeparatorView {
    if (!_topSeparatorView) {
        _topSeparatorView = [[UIView alloc] initWithFrame:CGRectZero];
        _topSeparatorView.backgroundColor = HEX_RGB(0xD9D9D9);
    }
    return _topSeparatorView;
}

- (UIButton *)cancelButton {
    if (!_cancelButton) {
        _cancelButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_cancelButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000692",@"取消") forState:UIControlStateNormal];
        [_cancelButton setTitleColor:HEX_RGB(0x595959) forState:UIControlStateNormal];
        _cancelButton.titleLabel.font = MY_FONT_Regular(17);
        [_cancelButton addTarget:self action:@selector(cancelButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _cancelButton;
}

- (UIView *)middleSeparatorView {
    if (!_middleSeparatorView) {
        _middleSeparatorView = [[UIView alloc] initWithFrame:CGRectZero];
        _middleSeparatorView.backgroundColor = HEX_RGB(0xD9D9D9);
    }
    return _middleSeparatorView;
}

- (UIButton *)confirmButton {
    if (!_confirmButton) {
        _confirmButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_confirmButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00048",@"确定") forState:UIControlStateNormal];
        [_confirmButton setTitleColor:HEX_RGB(0x537FB7) forState:UIControlStateNormal];
        _confirmButton.titleLabel.font = MY_FONT_Regular(17);
        [_confirmButton addTarget:self action:@selector(confirmButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _confirmButton;
}

// MARK: - Actions

- (void)cancelButtonTapped:(UIButton *)sender {
    [self.inputTextField resignFirstResponder];
    [self hide];
}

- (void)confirmButtonTapped:(UIButton *)sender {
    [self textFieldDidEndEditing:self.inputTextField];
}

// MARK: - custom Animation --
- (MMPopupBlock)customShowAnimation
{
    MMWeakify(self);
    MMPopupBlock block = ^(MMPopupView *popupView){
        MMStrongify(self);
        
        if ( !self.superview )
        {
            [self.attachedView.mm_dimBackgroundView addSubview:self];
            [self mas_updateConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.attachedView).offset(SCREEN_HEIGHT);
                make.left.equalTo(self.attachedView).offset((SCREEN_WIDTH - 270) / 2);
            }];
            [self layoutIfNeeded];
            [self.superview layoutIfNeeded];
        }
        [UIView animateWithDuration:self.animationDuration
                              delay:0
             usingSpringWithDamping:0.8
              initialSpringVelocity:1.5
                            options:UIViewAnimationOptionCurveEaseOut | UIViewAnimationOptionBeginFromCurrentState
                         animations:^{
                             
                             [self mas_updateConstraints:^(MASConstraintMaker *make) {
                                 make.top.equalTo(self.attachedView).offset((SCREEN_HEIGHT - self.height) / 2 - (self.withKeyboard ? 100 : 0));
                                 make.left.equalTo(self.attachedView).offset((SCREEN_WIDTH - 270)/2);
                             }];
                             
                             [self.superview layoutIfNeeded];
                             
                         } completion:^(BOOL finished) {
                             [MMPopupWindow sharedWindow].touchWildToHide = NO;
                             if ( self.showCompletionBlock )
                             {
                                 self.showCompletionBlock(self, finished);
                             }
                             
                         }];
        
    };
    
    return block;
}

- (MMPopupBlock)customHideAnimation
{
    MMWeakify(self);
    MMPopupBlock block = ^(MMPopupView *popupView){
        MMStrongify(self);
        [UIView animateWithDuration:0.3
                              delay:0
                            options:UIViewAnimationOptionCurveEaseIn | UIViewAnimationOptionBeginFromCurrentState
                         animations:^{
                             [self mas_updateConstraints:^(MASConstraintMaker *make) {
                                 make.top.equalTo(self.attachedView).offset(SCREEN_HEIGHT);
                             }];
                             [self.superview layoutIfNeeded];
                         } completion:^(BOOL finished) {
                             
                             if ( finished )
                             {
                                 [self removeFromSuperview];
                             }
                             [MMPopupWindow sharedWindow].touchWildToHide = NO;
                             if ( self.hideCompletionBlock )
                             {
                                 self.hideCompletionBlock(self, finished);
                             }
                         }];
        
    };
    
    return block;
}

#pragma mark - UITextFieldDelegate

- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string {
    if (string.length == 0) { return YES; }
    NSString *newString = [textField.text stringByReplacingCharactersInRange:range withString:string];
    
    // 使用正则表达式来限制输入
    NSString *regex = @"^(0|[1-9]\\d{0,2})(\\.\\d{0,2})?$";
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", regex];
    if (![predicate evaluateWithObject:newString]) {
        return NO;
    }
    
    // 允许临时输入不在范围内的数值，后续再做判断
    return YES;
}

// 在文本框内容改变后再进行范围检查
- (void)textFieldDidEndEditing:(UITextField *)textField {
    double value = [textField.text doubleValue];
    if (value < 50.00 || value > 150.00) {
        // 清空文本框内容或显示错误提示
        textField.text = @"";
        // 提示用户
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100001671", @"走管校准范围为50-150mm")];
        return;
    }
    [textField resignFirstResponder];
    [self hide];
    if (self.clickViewBtnBlock1) {
        self.clickViewBtnBlock1(nil,self.inputTextField.text);
    }
}
@end
