import 'dart:async';

import 'package:flutter/cupertino.dart';

import '../../../network/dio_utils.dart';

/// 文本相似度结果模型
class TextSimilarityResult {
  final String queryText;
  final String referenceText;
  final double similarity;

  TextSimilarityResult({
    required this.queryText,
    required this.referenceText,
    required this.similarity,
  });

  factory TextSimilarityResult.fromJson(Map<String, dynamic> json) {
    return TextSimilarityResult(
      queryText: json['queryText'] ?? '',
      referenceText: json['referenceText'] ?? '',
      similarity: (json['similarity'] ?? 0.0).toDouble(),
    );
  }
}

class NLPUtils {

  static final commodityFields = ['商品条码', '品名', '产地', '单位', '规格', '等级', '零售价', '促销价', '物价员'];


  /// 获取最佳文本相似度
  /// [queryTexts] 查询文本列表
  /// [referenceTexts] 参考文本列表
  /// 返回文本相似度结果列表
  static Future<List<TextSimilarityResult>> getTextSimilarity(
      List<String> queryTexts, List<String> referenceTexts) async {
    Completer<List<TextSimilarityResult>> completer = Completer<List<TextSimilarityResult>>();
    if (queryTexts.isEmpty) {
      return [];
    }
    final Map<String, dynamic> params = {
      "queryTexts": queryTexts,
      "referenceTexts": referenceTexts,
    };

    DioUtils.instance.requestNetwork<Map<String, dynamic>>(
      Method.post,
      isList: true,
      {
        'path': '/system/similarity',
        'needLogin': false,
      },
      params: params,
      onSuccessList: (data) {
        try {
          if (data != null) {
            final List<dynamic> resultList = data;
            final List<TextSimilarityResult> results =
            resultList.map((item) => TextSimilarityResult.fromJson(item)).toList();
            completer.complete(results);
          } else {
            completer.complete([]);
          }
        } catch (e) {
          debugPrint('解析相似度结果异常: $e');
          completer.complete([]);
        }
      },
      onError: (int code, String msg) {
        debugPrint('获取文本相似度失败: $code $msg');
        completer.complete([]);
      },
    );

    return completer.future;
  }

  /// 获取最佳匹配的文本
  /// [sourceText] 源文本
  /// [targetTexts] 目标文本列表
  /// 返回最佳匹配的文本和相似度
  static Future<Map<String, dynamic>?> getBestMatchText(String sourceText, List<String> targetTexts) async {
    if (sourceText.isEmpty || targetTexts.isEmpty) {
      return null;
    }

    final results = await getTextSimilarity([sourceText], targetTexts);
    if (results.isEmpty) {
      return null;
    }

    // 找出相似度最高的结果
    TextSimilarityResult bestMatch = results.reduce((a, b) => a.similarity > b.similarity ? a : b);

    return {
      'text': bestMatch.referenceText,
      'similarity': bestMatch.similarity,
      'index': targetTexts.indexOf(bestMatch.referenceText),
    };
  }
}
