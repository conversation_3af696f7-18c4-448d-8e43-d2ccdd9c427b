//
//  JCFontManagerController.m
//  Runner
//
//  Created by j c on 2019/4/30.
//  Copyright © 2019年 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCFontManagerController.h"
#import "JCFontDetailViewController.h"
#import "JCFontManagerTableViewCell.h"
#import "JCCategoryCollectionViewCell.h"
#import "JCFontCategoryModel.h"
#import "JCFontManager.h"

@interface JCFontManagerController ()<UITableViewDelegate,UITableViewDataSource,UICollectionViewDelegate,UICollectionViewDataSource>

@property (nonatomic, strong) NSMutableArray *categoryInfoArr;

@property (nonatomic, strong) NSMutableArray *fontArr;

@property (nonatomic, strong) NSMutableArray *downloadingArr;

@property (nonatomic, strong) UICollectionView *categoryCollectionView;

@property (nonatomic, strong) UITableView *fontTableView;

@property (nonatomic, strong) JCFontCategoryModel *selectFontCategoryModel;

@property (nonatomic, copy) XYNormalBlock dismissBlock;
@end

@implementation JCFontManagerController

- (instancetype)initWithDismiss:(XYNormalBlock)dismissBlock{
    self = [super init];
    if(self){
        self.dismissBlock = dismissBlock;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [JCFontManager sharedManager];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshVipOperateStatus:) name:JCNOTICATION_VIP_OPERATE_SUCCESS object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(fontMegerSuccessRefreshStatus:) name:JCNOTICATION_FONT_MEGER_SUCCESS object:nil];

    // Do any additional setup after loading the view from its nib.
}

- (void)initUI{
    XYWeakSelf
    [super initUI];
    self.view.backgroundColor = HEX_RGB(0xF5F5F5);
    self.title = XY_LANGUAGE_TITLE_NAMED(@"app00201",@"字体管理");
    self.fontTableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
    self.fontTableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.fontTableView.backgroundColor = HEX_RGB(0xF5F5F5);
    self.fontTableView.showsVerticalScrollIndicator = NO;
    self.fontTableView.tableFooterView = [UIView new];
    self.fontTableView.delegate = self;
    self.fontTableView.dataSource = self;
    self.fontTableView.tableHeaderView = [[UIView alloc] initWithFrame: CGRectMake(0, 0, SCREEN_WIDTH, 15)];
    self.fontTableView.tableFooterView = [[UIView alloc] initWithFrame: CGRectMake(0, 0, SCREEN_WIDTH, 45)];
    [self.fontTableView registerNib:@"JCFontManagerTableViewCell"];
    [self.view addSubview:self.categoryCollectionView];
    [self.view addSubview:self.fontTableView];
    [self.categoryCollectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(weakSelf.view);
        make.top.equalTo(weakSelf.view);
        make.width.mas_equalTo(@(SCREEN_WIDTH));
        make.height.mas_equalTo(@50);
    }];
    [self.fontTableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(weakSelf.view).offset(15);
        make.trailing.equalTo(weakSelf.view).offset(-15);
        make.top.equalTo(weakSelf.categoryCollectionView.mas_bottom);
        make.bottom.equalTo(weakSelf.view);
    }];
}

- (void)initNet{
    [super initNet];
    [self getFontCategoryInfo];
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    if(self.dismissBlock){
        self.dismissBlock();
    }
}

- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)getFontCategoryInfo{
    XYWeakSelf
    [self reloadCategoryViewWithCategoryId:@"-1"];
    if (self.categoryInfoArr.count > 2) {
        // App启动的时候已经获取过，此处仅仅作为兜底
        return;
    }
    [@{@"cachePolicy":@"2"} java_postWithModelType:[JCFontCategoryModel class] Path:J_font_category_list hud:nil Success:^(__kindof YTKBaseRequest *request, NSArray *categoryModelArr) {
        [[JCFMDB shareDatabase:DB_DEFAULT] jc_deleteAllDataFromTable:TABLE_FONT_CATEGORY_INFO];
        if(categoryModelArr.count > 0){
            [[JCFMDB shareDatabase:DB_DEFAULT] jc_insertTable:TABLE_FONT_CATEGORY_INFO dicOrModelArray:categoryModelArr];
        }
        [weakSelf reloadCategoryViewWithCategoryId:@"-1"];
    } failure:^(NSString *msg, id model) {
        
    }];
}

- (void)reloadCategoryViewWithCategoryId:(NSString *)categoryId{
    NSMutableArray *fontCategoryArr = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_FONT_CATEGORY_INFO dicOrModel:[JCFontCategoryModel class] whereFormat:nil].mutableCopy;
    JCFontCategoryModel *myModel = [[JCFontCategoryModel alloc] init];
    myModel.xyid = @"-1";
    myModel.name = XY_LANGUAGE_TITLE_NAMED(@"app00838", @"我的");
    [fontCategoryArr insertObject:myModel atIndex:0];
    self.categoryInfoArr = fontCategoryArr;
    JCFontCategoryModel *currentLanguageCategory = nil;
    JCFontCategoryModel *enCategory = nil;
    for (JCFontCategoryModel *obj in self.categoryInfoArr) {
        if([obj.xyid isEqualToString:categoryId]){
            obj.isSelect = @"1";
            self.selectFontCategoryModel = obj;
        }else{
            obj.isSelect = @"0";
        }
        if([obj.languageCode isEqualToString:XY_JC_LANGUAGE_REAL]){
            currentLanguageCategory = obj;
        }
        if([obj.languageCode isEqualToString:@"en"]){
            enCategory = obj;
        }
    }
    if(currentLanguageCategory != nil){
        [self.categoryInfoArr removeObject:currentLanguageCategory];
        [self.categoryInfoArr insertObject:currentLanguageCategory atIndex:1];
    }else if (enCategory != nil){
        [self.categoryInfoArr removeObject:enCategory];
        [self.categoryInfoArr insertObject:enCategory atIndex:1];
    }
    [self.categoryCollectionView reloadData];
    if(xy_isLogin){
        NSDictionary *requestDic = @{@"userId":m_userModel.userid,@"classifyId":@"-1"};
        [[JCFontManager sharedManager] requestUserFontWithCacheInfo:^(NSArray * _Nonnull categoryFonts) {
            if(![self.selectFontCategoryModel.xyid isEqualToString:@"-1"]) return;
            [self reloadCollectionWithFonts:categoryFonts];
        } params:requestDic isNeedRequest:YES updateInfo:^(NSArray * _Nonnull categoryFonts) {
            if(![self.selectFontCategoryModel.xyid isEqualToString:@"-1"]) return;
            [self reloadCollectionWithFonts:categoryFonts];
            [[JCFontManager sharedManager] uploadUserFontWithCacheInfo:categoryFonts updateInfo:^(NSNumber *successNumber){
                                            
            }];
        }];
    }else{
        [self showLocalFont];
    }
}

- (void)fontMegerSuccessRefreshStatus:(NSNotification *)notification{
    dispatch_async(dispatch_get_main_queue(), ^{
        if([self.selectFontCategoryModel.xyid isEqualToString:@"-1"]){
            NSString *whereStr = [NSString stringWithFormat:@"where userId = '%@'",m_userModel.userid];
            NSArray *fontArr = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_USER_FONTINFO dicOrModel:[JCFontModel class] whereFormat:whereStr];
            [self reloadCollectionWithFonts:fontArr];
        }else{
            [self.fontTableView reloadData];
        }
    });
}

- (void)showLocalFont{
    NSArray *fontArr = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_USER_FONTINFO dicOrModel:[JCFontModel class] whereFormat:nil];
    NSMutableArray *needShowFonts = [NSMutableArray array];
    NSArray *hasDownLoadFontCodeArr = [JCFontModel getDownLoadFontCodeArr];
    for (JCFontModel *fontModel in fontArr) {
        if([hasDownLoadFontCodeArr containsObject:fontModel.fontCode]){
            [needShowFonts addObject:fontModel];
        }
    }
    [self reloadCollectionWithFonts:needShowFonts];
}

- (void)uploadUserFonts{
    NSString *userId = m_userModel.userid;
    NSString *whereStr = [NSString stringWithFormat:@"where userId = '%@' or userId = ''",userId];
    NSArray *fontArr = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_USER_FONTINFO dicOrModel:[JCFontModel class] whereFormat:whereStr];
    if(fontArr.count > 0){
        [[JCFontManager sharedManager] uploadUserFontWithCacheInfo:fontArr updateInfo:^(NSNumber *successNumber){
                                        
        }];
    }
}

- (UICollectionView *)categoryCollectionView{
    if(!_categoryCollectionView){
        UICollectionViewFlowLayout *flowLayout = [[UICollectionViewFlowLayout alloc] init];
        flowLayout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
        flowLayout.minimumLineSpacing = 5;
        flowLayout.minimumInteritemSpacing = 5;
        flowLayout.sectionInset = UIEdgeInsetsMake(0, 15, 0, 15);
        CGRect collectionViewFrame = CGRectMake(0, 0, SCREEN_WIDTH, 50);
        UICollectionView *collectionView = [[UICollectionView alloc] initWithFrame:collectionViewFrame collectionViewLayout:flowLayout];
        [collectionView registerClass:[NSClassFromString(@"JCCategoryCollectionViewCell") class] forCellWithReuseIdentifier:@"JCCategoryCollectionViewCell"];
        collectionView.backgroundColor = COLOR_WHITE;
        collectionView.showsHorizontalScrollIndicator = NO;
        collectionView.tag = 1;
        collectionView.delegate = self;
        collectionView.dataSource = self;
        _categoryCollectionView = collectionView;
        
    }
    return _categoryCollectionView;
}

- (void)refreshVipOperateStatus:(NSNotification *)notification{
    [self.fontTableView reloadData];
}

-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return 1;
}

-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{

    return self.fontArr.count;
}

-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 58;
}

-(CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return 1.f;
}

-(UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    UIView *footerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 1)];
    footerView.backgroundColor = [UIColor clearColor];
    return footerView;
}


-(UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section{
    UIView *footerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 15)];
    footerView.backgroundColor = [UIColor clearColor];
    return footerView;
}

-(CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    if(section == 0){
        return 0.1f;
    }
    return 15.1f;
}



- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath
{
    // 圆角角度
    CGFloat radius = 8.f;
    // 设置cell 背景色为透明
    cell.backgroundColor = UIColor.clearColor;
    // 创建两个layer
    CAShapeLayer *normalLayer = [[CAShapeLayer alloc] init];
    CAShapeLayer *selectLayer = [[CAShapeLayer alloc] init];
    // 获取显示区域大小
    CGRect bounds = CGRectInset(cell.bounds, 0, 0);
    // 获取每组行数
    NSInteger rowNum = [tableView numberOfRowsInSection:indexPath.section];
    // 贝塞尔曲线
    UIBezierPath *bezierPath = nil;
    if (rowNum == 1) {
        // 一组只有一行（四个角全部为圆角）
        bezierPath = [UIBezierPath bezierPathWithRoundedRect:bounds
                                    byRoundingCorners:UIRectCornerAllCorners
                                          cornerRadii:CGSizeMake(radius, radius)];
    } else {
    if (indexPath.row == 0) {
        // 每组第一行（添加左上和右上的圆角）
        bezierPath = [UIBezierPath bezierPathWithRoundedRect:bounds
                                        byRoundingCorners:(UIRectCornerTopLeft|UIRectCornerTopRight)
                                              cornerRadii:CGSizeMake(radius, radius)];
    } else if (indexPath.row == rowNum - 1) {
        // 每组最后一行（添加左下和右下的圆角）
        bezierPath = [UIBezierPath bezierPathWithRoundedRect:bounds
                                        byRoundingCorners:(UIRectCornerBottomLeft|UIRectCornerBottomRight)
                                              cornerRadii:CGSizeMake(radius, radius)];
    } else {
            // 每组不是首位的行不设置圆角
            bezierPath = [UIBezierPath bezierPathWithRect:bounds];
        }
    }
    /// 不需要圆角的特殊情况
    if (indexPath.section == 1 && indexPath.row == rowNum - 1) {
        bezierPath = [UIBezierPath bezierPathWithRect:bounds];
    }
    if (indexPath.section == 2 && indexPath.row == 0) {
        bezierPath = [UIBezierPath bezierPathWithRect:bounds];
    }
    // 把已经绘制好的贝塞尔曲线路径赋值给图层，然后图层根据path进行图像渲染render
    normalLayer.path = bezierPath.CGPath;
    selectLayer.path = bezierPath.CGPath;
    UIView *nomarBgView = [[UIView alloc] initWithFrame:bounds];
    // 设置填充颜色
    normalLayer.fillColor = [UIColor colorWithWhite:1 alpha:1.0].CGColor;
    // 添加图层到nomarBgView中
    [nomarBgView.layer insertSublayer:normalLayer atIndex:0];
    nomarBgView.backgroundColor = UIColor.clearColor;
    cell.backgroundView = nomarBgView;
}



-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    XYWeakSelf
    JCFontModel *model = [self.fontArr safeObjectAtIndex:indexPath.row];
    JCFontManagerTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"JCFontManagerTableViewCell"];
    cell.indexPath = indexPath;
    [cell showTopLine:(indexPath.row != 0) bottomLine:NO];
    [cell setModel:model];
    cell.clickCellBtnBlock = ^(NSString  *operateType) {
        if(operateType.integerValue == 1){
            JC_TrackWithparms(@"click",@"030_084_108",(@{}));
            [JCIAPHelper openViewWithAlert:self needOpenTip:NO isUseVipSource:YES success:^{
                [tableView reloadData];
            } failure:^(NSString *msg, id model) {
                [MBProgressHUD showToastWithMessageDarkColor:msg];
            } sourceInfo:@{@"sourcePage":@"030"}];
        }else if(operateType.integerValue == 2){
            [weakSelf downLoadFontRequestWithUrlString:model tag:model.fontCode];
        }else if(operateType.integerValue == 3){
            [weakSelf showFontAgreementView:model.fileName fontRightStr:model.copyright agreementUrl:model.agreementUrl agreementName:model.agreementName];
        }
    };
    return cell;//
}

- (void)showFontAgreementView:(NSString *)fontName fontRightStr:(NSString *)fontRightStr agreementUrl:(NSString *)agreementUrl agreementName:(NSString *)agreementName{
    NSString *title1 = [XY_LANGUAGE_TITLE_NAMED(@"app100000251", @"查看《$》") stringByReplacingOccurrencesOfString:@"$" withString:agreementName];
    fontRightStr = [NSString stringWithFormat:@"copyright©%@",fontRightStr];
    NSString *text = [NSString stringWithFormat:@"%@\n%@",fontRightStr,title1];
    UIFont *labelFont = MY_FONT_Regular(13);
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    [paragraphStyle setLineSpacing:1.25];
    NSRange rang1 = [text rangeOfString:title1];
    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:text attributes:@{NSFontAttributeName:labelFont,NSForegroundColorAttributeName:HEX_RGB(0x595959)}];
    [attrStr addAttributes:@{NSParagraphStyleAttributeName:paragraphStyle} range:NSMakeRange(0, text.length)];
    [attrStr addAttribute:NSForegroundColorAttributeName value:HEX_RGB(0x5C88C1) range:rang1];
    [attrStr addAttribute:NSLinkAttributeName value:@"messageProtocol1://" range:rang1];
    [JCAlert showAlertView:fontName attributemessage:attrStr descrpTip:@"" cancelButtonTitle:@"" sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707", @"我知道了") cancelBlock:nil sureBlock:^{
            
    } messageUrlBlock:^(id x) {
        if(STR_IS_NIL(agreementUrl)){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"", @"无效地址")];
        }else{
            NSURL *URL = [NSURL URLWithString:XY_Check_UrlString(agreementUrl)];
            [[UIApplication sharedApplication] openURL:URL options:@{} completionHandler:^(BOOL success) {
                  //  回调
            }];
        }
    } alertType:1];
}

- (void)downLoadFontRequestWithUrlString:(JCFontModel *)fileModel tag:(NSString *)tag{
    XYWeakSelf
    if(NETWORK_STATE_ERROR){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
        return ;
    }
    if(!self.downloadingArr){
        self.downloadingArr = [NSMutableArray array];
    }
    if(![self.downloadingArr containsObject:tag]){
        if(self.downloadingArr.count >= 3){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000539", @"最多进行三个任务")];
            return;
        }else{
            [self.downloadingArr addObject:tag];
        }
    }else{
        return;
    }
    NSString *saveUrlString = [NSString stringWithFormat:@"%@/font/%@.%@",DocumentsFontPath,fileModel.fontCode,fileModel.url.pathExtension];
    if([fileModel.url rangeOfString:ServerURL].location != NSNotFound){
        fileModel.url = [fileModel.url stringByReplacingOccurrencesOfString:ServerURL withString:@""];
    }
    [self xy_downLoadFileWithValues:@{} UrlString:fileModel.url savePath:saveUrlString tag:tag Success:^(__kindof YTKBaseRequest *request, id model) {
        [JCFontModel saveTime:[XYTool getNowTimeTimestamp] fontCode:fileModel.fontCode];
        NSInteger row = -1;
        for (NSInteger index = 0; index < self.fontArr.count; index ++ ) {
            JCFontModel *fontModel = [self.fontArr safeObjectAtIndex:index];
            if([fontModel.fontCode isEqualToString:tag]){
                row = index;
            }
        }
        if(row >= 0){
            fileModel.fontThumbnailSuccess = @"0";
            NSIndexPath *indexPath = [NSIndexPath indexPathForRow:row inSection:0];
            [weakSelf.fontTableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationNone];
        }
        if([weakSelf.downloadingArr containsObject:tag]){
            [weakSelf.downloadingArr removeObject:tag];
        }
        [[NSNotificationCenter defaultCenter] postNotificationName:LABEL_FONT_DOWNLOAD_SUCCESS object:nil];
    } failure:^(NSString *msg, id model) {
        NSInteger row = -1;
        for (NSInteger index = 0; index < self.fontArr.count; index ++ ) {
            JCFontModel *fontModel = [self.fontArr safeObjectAtIndex:index];
            if([fontModel.fontCode isEqualToString:tag]){
                row = index;
            }
        }
        if(row >= 0){
            NSIndexPath *indexPath = [NSIndexPath indexPathForRow:row inSection:0];
            [weakSelf.fontTableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationNone];
        }
        if([weakSelf.downloadingArr containsObject:tag]){
            [weakSelf.downloadingArr removeObject:tag];
        }
    } downloadBlock:^(__kindof YTKBaseRequest *request) {
        
    }];
}

- (UISwipeActionsConfiguration *)tableView:(UITableView *)tableView trailingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath API_AVAILABLE(ios(11.0)){
    JCFontModel *fontModel = [self.fontArr safeObjectAtIndex:indexPath.row];
    if (@available(iOS 11.0, *)) {
        UIContextualActionStyle style = UIContextualActionStyleDestructive;
        if([fontModel.fontCode isEqualToString:@"ZT001"] || [fontModel.fontCode isEqualToString:@"ZT063"]){
            style = UIContextualActionStyleNormal;
        }
        UIContextualAction *operateAction = [UIContextualAction contextualActionWithStyle:style title:XY_LANGUAGE_TITLE_NAMED(@"app01059",@"删除") handler:^(UIContextualAction * _Nonnull action, __kindof UIView * _Nonnull sourceView, void (^ _Nonnull completionHandler)(BOOL)) {
            [self deleteDownloadedUserFont:fontModel];
            // 这句很重要，退出编辑模式，隐藏左滑菜单
            [tableView setEditing:NO animated:YES];
        }];
        UISwipeActionsConfiguration *actions = [UISwipeActionsConfiguration configurationWithActions:@[operateAction]];
        // 禁止侧滑无线拉伸
        actions.performsFirstActionWithFullSwipe = NO;
        return actions;
    }else{
        return nil;
    }
}

- (NSArray<UITableViewRowAction *> *)tableView:(UITableView *)tableView editActionsForRowAtIndexPath:(NSIndexPath *)indexPath{
    JCFontModel *fontModel = [self.fontArr safeObjectAtIndex:indexPath.row];
    if(![self.selectFontCategoryModel.xyid isEqualToString:@"-1"]){
        return nil;
    }else{
        UITableViewRowActionStyle style = UITableViewRowActionStyleDestructive;
        if([fontModel.fontCode isEqualToString:@"ZT001"] || [fontModel.fontCode isEqualToString:@"ZT063"]){
            style = UITableViewRowActionStyleNormal;
        }
        UITableViewRowAction *rowAction = [UITableViewRowAction rowActionWithStyle:style title:XY_LANGUAGE_TITLE_NAMED(@"app01059",@"删除") handler:^(UITableViewRowAction *action, NSIndexPath *indexPath) {
            [self deleteDownloadedUserFont:fontModel];
        }];
        return @[rowAction];
    }
}

- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(nonnull NSIndexPath *)indexPath{
    if(![self.selectFontCategoryModel.xyid isEqualToString:@"-1"]){
        return NO;
    }else{
        return YES;
    }
}

- (void)deleteDownloadedUserFont:(JCFontModel *)fontModel{
    if([fontModel.fontCode isEqualToString:@"ZT001"] || [fontModel.fontCode isEqualToString:@"ZT063"]){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000253", @"默认字体无法删除")];
        [self.fontTableView reloadData];
        return;
    }
    if(xy_isLogin){
        NSString *graphqlStr = @"mutation removeUserFont($id: String!) { removeUserFont(id: $id) { id  code deleted usageDatetime gmtCreated gmtModified} }";
        [@{@"id":UN_NIL(fontModel.xyid)} jc_graphQLRequestWith:graphqlStr hud:@"" graphQLType:@"query" Success:^(__kindof YTKBaseRequest *request, id model) {
            for (JCFontModel *model in self.fontArr) {
                if([model.fontCode isEqualToString:fontModel.fontCode]){
                    [self.fontArr removeObject:model];
                    break;
                }
            }
            [self.fontTableView reloadData];
            NSString *whereStr = [NSString stringWithFormat:@"where fontCode = '%@'",fontModel.fontCode];
            [[JCFMDB shareDatabase:DB_DEFAULT] jc_deleteTable:TABLE_USER_FONTINFO whereFormat:whereStr];
            
            ///删除同时删除本地
            NSString *filePath = [NSString stringWithFormat:@"%@/font/%@.%@",DocumentsFontPath,fontModel.fontCode,fontModel.url.pathExtension];
            NSFileManager *fileManager = [NSFileManager defaultManager];
            // 检查文件是否存在
            if ([fileManager fileExistsAtPath:filePath]) {
                NSError *error = nil;
                // 删除文件
                BOOL success = [fileManager removeItemAtPath:filePath error:&error];
                if (success) {
                    NSLog(@"文件删除成功: %@", filePath);
                } else {
                    NSLog(@"文件删除失败: %@", error.localizedDescription);
                }
            } else {
                NSLog(@"文件不存在: %@", filePath);
            }
          
        } failure:^(NSString *msg, id model) {
            [MBProgressHUD showToastWithMessageDarkColor:msg];
        }];
    }else{
        for (JCFontModel *model in self.fontArr) {
            if([model.fontCode isEqualToString:fontModel.fontCode]){
                [self.fontArr removeObject:model];
                break;
            }
        }
        [self.fontTableView reloadData];
        NSString *whereStr = [NSString stringWithFormat:@"where fontCode = '%@'",fontModel.fontCode];
        [[JCFMDB shareDatabase:DB_DEFAULT] jc_deleteTable:TABLE_USER_FONTINFO whereFormat:whereStr];
    }
}

-(NSMutableArray *)fontArr
{
    if (!_fontArr) {
        _fontArr = [NSMutableArray array];
    }
    return _fontArr;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.categoryInfoArr.count;
}


- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    JCFontCategoryModel *langModel = [self.categoryInfoArr safeObjectAtIndex:indexPath.row];
    JCCategoryCollectionViewCell *cell =  [collectionView dequeueReusableCellWithReuseIdentifier:@"JCCategoryCollectionViewCell" forIndexPath:indexPath];
    [cell setModel:langModel];
    return cell;
}

- (CGSize)collectionView:(UICollectionView*)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath*)indexPath{
    JCFontCategoryModel *langModel = [self.categoryInfoArr safeObjectAtIndex:indexPath.row];
    CGSize titleSize = [langModel.name jk_sizeWithFont:[UIFont fontWithName:@"PingFang-SC-Medium" size:15] constrainedToHeight:1000];
    return CGSizeMake(titleSize.width + 26, 50);
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    XYWeakSelf
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    for (NSInteger index = 0; index < self.categoryInfoArr.count; index++) {
        JCFontCategoryModel *model = [self.categoryInfoArr safeObjectAtIndex:index];
        if(index == indexPath.row){
            model.isSelect = @"1";
            self.selectFontCategoryModel = model;
        }else{
            model.isSelect = @"0";
        }
    }
    [collectionView scrollToItemAtIndexPath:indexPath atScrollPosition:UICollectionViewScrollPositionCenteredHorizontally animated:YES];
    [collectionView reloadData];
    if([self.selectFontCategoryModel.xyid isEqualToString:@"-1"]){
        if(xy_isLogin){
            NSDictionary *requestDic = @{@"userId":m_userModel.userid,@"classifyId":self.selectFontCategoryModel.xyid};
            [[JCFontManager sharedManager] requestUserFontWithCacheInfo:^(NSArray * _Nonnull categoryFonts) {
                NSString *classifyId = requestDic[@"classifyId"];
                if(![classifyId isEqualToString:self.selectFontCategoryModel.xyid]) return;
                [weakSelf reloadCollectionWithFonts:categoryFonts];
            } params:requestDic isNeedRequest:NO updateInfo:^(NSArray * _Nonnull categoryFonts) {
                NSString *classifyId = requestDic[@"classifyId"];
                if(![classifyId isEqualToString:self.selectFontCategoryModel.xyid]) return;
                [weakSelf reloadCollectionWithFonts:categoryFonts];
            }];
        }else{
            [self showLocalFont];
        }
    }else{
        NSDictionary *requestDic = nil;
        if(jc_is_support_vip){
            requestDic = @{@"platformCodes":@"CP001Mobile",@"status":@"2",@"queryVersion":@2,@"classifyId":self.selectFontCategoryModel.xyid,@"needDesRequest":@"1",@"cachePolicy":@"1"};
        }else{
            requestDic = @{@"platformCodes":@"CP001Mobile",@"status":@"2",@"isVip":@0,@"queryVersion":@2,@"classifyId":self.selectFontCategoryModel.xyid,@"needDesRequest":@"1",@"cachePolicy":@"1"};
        }
        [[JCFontManager sharedManager] requestFontWithCacheInfo:^(NSArray * _Nonnull categoryFonts) {
            NSString *classifyId = requestDic[@"classifyId"];
            if(![classifyId isEqualToString:self.selectFontCategoryModel.xyid]) return;
            [self reloadCollectionWithFonts:categoryFonts];
        } params:requestDic updateInfo:^(NSArray * _Nonnull categoryFonts) {
            NSString *classifyId = requestDic[@"classifyId"];
            if(![classifyId isEqualToString:self.selectFontCategoryModel.xyid]) return;
            [self reloadCollectionWithFonts:categoryFonts];
        }];
    }
    if(self.fontArr.count > 0){
        NSIndexPath *indexPathTable = [NSIndexPath indexPathForRow:0 inSection:0];
        [self.fontTableView scrollToRowAtIndexPath:indexPathTable atScrollPosition:UITableViewScrollPositionTop animated:NO];
    }
}

- (void)reloadCollectionWithFonts:(NSArray *)fonts {
    NSMutableArray *temp = [NSMutableArray array];
    [temp addObjectsFromArray:fonts];
    self.fontArr = temp;
    for (NSString *fontCode in self.downloadingArr) {
        for (JCFontModel *obj in self.fontArr) {
            if([fontCode isEqualToString:obj.fontCode]){
                obj.isDownLoading = YES;
            }
        }
    }
    /** 标记已经下载的model */
    [self.fontArr each:^(JCFontModel *obj) {
        obj.hasDownLoad = [obj fontHasDownload]?YES:NO;
    }];
    [self sortDatas];
}

- (void)sortDatas {
    BOOL hasDefaultFont1 = NO;
    BOOL hasDefaultFont2 = NO;
    BOOL hasDefaultFontJapanese = NO;
    NSArray *font1s = [[JCFMDB shareDatabase:DB_DEFAULT]  jc_lookupTable:TABLE_FONTINFO dicOrModel:[JCFontModel class] whereFormat:@"where fontCode = 'ZT063'"];
    JCFontModel *defaultModel1 = [font1s safeObjectAtIndex:0];
    for (JCFontModel *obj in self.fontArr) {
        if([obj.fontCode isEqualToString:@"ZT063"]){
            hasDefaultFont1 = YES;
            obj.thumbnailUrl = defaultModel1.thumbnailUrl;
            obj.name = XY_LANGUAGE_TITLE_NAMED(@"app01417", @"ar");
        }
        if([obj.fontCode isEqualToString:@"ZT001"]){
            hasDefaultFont2 = YES;
            obj.name = XY_LANGUAGE_TITLE_NAMED(@"app01533", @"鸿蒙");
        }
        if([obj.fontCode isEqualToString:@"ZT825"]){
            hasDefaultFontJapanese = YES;
            obj.name = XY_LANGUAGE_TITLE_NAMED(@"app100001929", @"日语默认字体");
        }
    }
    if([self.selectFontCategoryModel.xyid isEqualToString:@"-1"]){
        if(!hasDefaultFont2){
            JCFontModel *model = [[JCFontModel alloc] init];
            model.name = XY_LANGUAGE_TITLE_NAMED(@"app01533", @"鸿蒙");
            model.font_type = @"1000";
            model.langType = @"zh";
            model.xyid = @"10000001";
            model.fontCode = @"ZT001";
            model.hasDownLoad = YES;
            model.usageDatetime = @"0000-00-00 00:00:00";
            [self.fontArr insertObject:model atIndex:0];
        }
        if(!hasDefaultFont1){
            if(defaultModel1 != nil){
                defaultModel1.name = XY_LANGUAGE_TITLE_NAMED(@"app01417", @"ar");
                defaultModel1.hasDownLoad = YES;
                defaultModel1.usageDatetime = @"0000-00-00 00:00:00";
                [self.fontArr insertObject:defaultModel1 atIndex:0];
            }else{
                JCFontModel *model = [[JCFontModel alloc] init];
                model.name = XY_LANGUAGE_TITLE_NAMED(@"app01417", @"ar");;
                model.font_type = @"1000";
                model.xyid = @"10000002";
                model.langType = @"en";
                model.fontCode = @"ZT063";
                model.hasDownLoad = YES;
                model.usageDatetime = @"0000-00-00 00:00:00";
                [self.fontArr insertObject:model atIndex:0];
            }
        }
        // 添加日语默认字体
        if(!hasDefaultFontJapanese){
            JCFontModel *model = [[JCFontModel alloc] init];
            model.name = XY_LANGUAGE_TITLE_NAMED(@"app100001929", @"日语默认字体");
            model.font_type = @"1000";
            model.xyid = @"10000003";
            model.langType = @"ja";
            model.fontCode = @"ZT825";
            model.hasDownLoad = YES;
            model.usageDatetime = @"0000-00-00 00:00:00";
            [self.fontArr insertObject:model atIndex:0];
        }
        /** 对下载过的字体根据下载时间排序 */
        NSSortDescriptor*sorter=[[NSSortDescriptor alloc]initWithKey:@"usageDatetime" ascending:NO];
        NSMutableArray *sortDescriptors=[[NSMutableArray alloc]initWithObjects:&sorter count:1];
        NSArray *sortArray=[self.fontArr sortedArrayUsingDescriptors:sortDescriptors];
        // 转换
        self.fontArr = [NSMutableArray arrayWithArray:sortArray];
    }else{
        [self.fontArr sortUsingComparator:^NSComparisonResult(JCFontModel *obj1, JCFontModel *obj2) {
            return obj1.priority.integerValue > obj2.priority.integerValue;
        }];
        for (JCFontModel *obj in self.fontArr.reverseObjectEnumerator) {
            if([obj.status isEqualToString:@"0"]){
                [self.fontArr removeObject:obj];
            }
        }
    }
    [self.fontTableView reloadData];
}


- (NSArray *)getHasDownloadFont{
    NSMutableArray *myFonts = @[].mutableCopy;
    NSMutableArray *fontTagArr = @[].mutableCopy;
    NSArray *hadDownLoadFontCode = [self getDownLoadFontCodeArr];
    for (NSString *fontCode in hadDownLoadFontCode) {
        NSLog(@"222");
        NSString *whereStr = @"";
        if(jc_is_support_vip){
            whereStr = [NSString stringWithFormat:@"where fontCode = '%@'",fontCode];
        }else{
            whereStr = [NSString stringWithFormat:@"where fontCode = '%@' and isVip != '1'",fontCode];
        }
        if([fontCode isEqualToString:@"ZT001"]){
            whereStr = [NSString stringWithFormat:@"%@ and language = '%@'",whereStr,XY_JC_LANGUAGE_REAL];
        }
        NSArray *fonts = [[JCFMDB shareDatabase:DB_DEFAULT]  jc_lookupTable:TABLE_FONTINFO dicOrModel:[JCFontModel class] whereFormat:whereStr];
        if(fonts.count > 0){
            for (JCFontModel *obj in fonts) {
                NSString *fontTag = [NSString stringWithFormat:@"%@_%@",obj.name,obj.fontCode];
                if(![fontTagArr containsObject:fontTag]){
                    [myFonts addObject:obj];
                    [fontTagArr addObject:fontTag];
                }
            }
        }
    }
    return myFonts;
}

- (NSMutableArray *)getDownLoadFontCodeArr {
    NSMutableArray *arr = @[].mutableCopy;
    NSFileManager* fm=[NSFileManager defaultManager];
    NSString *fontPath = [NSString stringWithFormat:@"%@/font",DocumentsFontPath];
    if(![fm fileExistsAtPath:fontPath]){
        [fm createDirectoryAtPath:fontPath withIntermediateDirectories:YES attributes:nil error:nil];
    }else{
        NSArray *files = [fm contentsOfDirectoryAtPath:fontPath error:nil];
        for (NSString *fileName in files) {
            NSArray *tempArr = [fileName componentsSeparatedByString:@"."];
            NSString *codeName = [tempArr safeObjectAtIndex:0];
            if(!STR_IS_NIL(codeName) && [codeName hasPrefix:@"ZT"]){
                [arr addObject:codeName];
            }
        }
    }
    return arr;
}
@end
