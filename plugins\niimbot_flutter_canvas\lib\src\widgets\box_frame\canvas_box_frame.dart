import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_boost/flutter_boost.dart' as flutter_boost;
import 'package:flutter_canvas_plugins_interface/config/element_create_required_model.dart';
import 'package:flutter_canvas_plugins_interface/config/template_config.dart';
import 'package:flutter_canvas_plugins_interface/config/toolkit_button.dart';
import 'package:flutter_canvas_plugins_interface/plugin/advance_qr_code_interface.dart';
import 'package:flutter_canvas_plugins_interface/plugin/advance_qr_code_model.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_config_interface.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/plugin/excel_data.dart';
import 'package:flutter_canvas_plugins_interface/plugin/goods_model.dart';
import 'package:flutter_canvas_plugins_interface/shared/canvas_font_data.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_canvas_plugins_interface/user_center/canvas_user_center.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:flutter_canvas_plugins_interface/utils/display_util.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:niimbot_excel/models/data_source.dart';
import 'package:niimbot_excel/models/range.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/localization/localization_public.dart';
import 'package:niimbot_flutter_canvas/src/model/canvas_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/date_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/image_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/layout_schema_model.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_cell_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element_bo.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/excel_transform_manager.dart';
import 'package:niimbot_flutter_canvas/src/model/font/font_item.dart';
import 'package:niimbot_flutter_canvas/src/model/material/material_item.dart';
import 'package:niimbot_flutter_canvas/src/model/material_badge_helper.dart';
import 'package:niimbot_flutter_canvas/src/model/stack/stack_manager.dart';
import 'package:niimbot_flutter_canvas/src/model/toolkit_buttons.dart';
import 'package:niimbot_flutter_canvas/src/notification/attribute_panel_refresh_notify.dart';
import 'package:niimbot_flutter_canvas/src/notification/attribute_panel_state_notification.dart';
import 'package:niimbot_flutter_canvas/src/pages/canvas_industry_template/canvas_industry_template_page.dart';
import 'package:niimbot_flutter_canvas/src/pages/canvas_industry_template/canvas_industry_template_state.dart';
import 'package:niimbot_flutter_canvas/src/provider/elements_data_changed_notifier.dart';
import 'package:niimbot_flutter_canvas/src/provider/floating_bar_visible_notifier.dart';
import 'package:niimbot_flutter_canvas/src/provider/material_recent_changed_notifier.dart';
import 'package:niimbot_flutter_canvas/src/provider/smart_tips_notifier.dart';
import 'package:niimbot_flutter_canvas/src/provider/stack_changed_notifier.dart';
import 'package:niimbot_flutter_canvas/src/provider/template_changed_notifier.dart';
import 'package:niimbot_flutter_canvas/src/utils/canvas_helper.dart';
import 'package:niimbot_flutter_canvas/src/utils/debounce_util.dart';
import 'package:niimbot_flutter_canvas/src/utils/loading_mix.dart';
import 'package:niimbot_flutter_canvas/src/utils/print_channel.dart';
import 'package:niimbot_flutter_canvas/src/utils/rfid_util.dart';
import 'package:niimbot_flutter_canvas/src/utils/template_utils.dart';
import 'package:niimbot_flutter_canvas/src/utils/theme_color.dart';
import 'package:niimbot_flutter_canvas/src/utils/track_utils.dart';
import 'package:niimbot_flutter_canvas/src/widgets/assist/ocr/photo_crop_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/attr_widget_builder.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/attr_icon_button.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/element_attribute_panel.dart';
import 'package:niimbot_flutter_canvas/src/widgets/box_frame/keyboard_sensitive_editor_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/box_frame/template_data_binding_toolbar.dart';
import 'package:niimbot_flutter_canvas/src/widgets/canvas/canvas_data_mix.dart';
import 'package:niimbot_flutter_canvas/src/widgets/canvas/canvas_data_mix_excel_extension.dart';
import 'package:niimbot_flutter_canvas/src/widgets/canvas/floatbar/floating_bar_helper.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/custom_dialog.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/stick/keyboard_attachable.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/svg_icon.dart';
import 'package:niimbot_flutter_canvas/src/widgets/excel/import_excel_helper.dart';
import 'package:niimbot_flutter_canvas/src/widgets/im/drawboard_im.dart';
import 'package:niimbot_flutter_canvas/src/widgets/loading/loading_widget.dart';
import 'package:nimbot_state_manager/controller_adapters/state_manage_controller.dart';
import 'package:nimbot_state_manager/controller_adapters/state_management_adapter.dart';
import 'package:popover/popover.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../pages/canvas_industry_template/canvas_industry_template_logic.dart';

Logger _logger = Logger("CanvasItemBoxScreen", on: kDebugMode);

int animatedDurationMilliseconds = 200;

final GlobalKey<CanvasDataMixState> canvasKey = GlobalKey();

/// 定义当前画板焦点状态
enum ElementFocusState {
  /// 未选中
  None,

  /// 选中状态
  Selection,

  /// 编辑内容
  Editor,

  /// 素材选择面板
  Material,

  //素材边框面板
  Material_boder,
}

/// 共享数据
class CanvasObjectSharedWidget extends InheritedWidget {
  final TemplateData? canvasData;
  final String printColor;
  final CanvasFontConfig fontConfig;
  final int consumablesType;

  CanvasObjectSharedWidget({
    super.key,
    required this.canvasData,
    required this.fontConfig,
    required this.printColor,
    required this.consumablesType,
    required Widget child,
  }) : super(child: child);

  static TemplateData? canvasDataOf(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<CanvasObjectSharedWidget>()!.canvasData;
  }

  static String printColorOf(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<CanvasObjectSharedWidget>()!.printColor;
  }

  static int consumablesTypeOf(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<CanvasObjectSharedWidget>()!.consumablesType;
  }

  static CanvasFontConfig fontConfigOf(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<CanvasObjectSharedWidget>()!.fontConfig;
  }

  @override
  bool updateShouldNotify(CanvasObjectSharedWidget oldWidget) {
    return oldWidget.canvasData != canvasData ||
        oldWidget.fontConfig != fontConfig ||
        oldWidget.printColor != printColor;
  }
}

class CanvasBoxFrame extends StatefulWidget {
  final Map? singleColorPrintInfo;
  final String jsonData;
  final int? consumablesType;
  final CanvasFontConfig fontConfig;
  final List<ToolkitButton> toolkitButtons;
  final Future<bool> Function(BuildContext buildContext, String? canvasJson, bool showOtherSave, bool isEtagSave,
      Function resetElementFocusStateFun)? onNavigatorPop;
  final Future<TemplateData> Function(
      String? canvasJson, TemplateConfig? config, BuildContext context, String templateName,
      {String templateId, bool isOtherSave})? onSave;
  final Future<bool> Function(String? canvasJson)? canvasInitData;
  final void Function(String? canvasJson, String? dataBindingMode, int? currentPage, TemplateConfig? config,
      List<Map<String, dynamic>>? pdfBindInfos, BuildContext context) onPrint;
  final Future<TemplateData?> Function(String? canvasJson, TemplateConfig? config, BuildContext context)? onConfig;
  final Future<TemplateData?> Function(String? canvasJson, TemplateConfig? config, BuildContext context)? onLabelChange;
  final void Function(String canvasJson, BuildContext context)? onBack;
  final Future<List<bool>> Function(BuildContext context, String? canvasJson) onCheckSmartTypesettingOpen;
  final Future<int> Function(
          String? canvasJson, String? dataBindingMode, int? currentPage, TemplateConfig? config, BuildContext context)
      onSmartTypesetting;
  final Future<TemplateData?> Function(
          String canvasJson, String dataBindingMode, int currentPage, TemplateConfig? config, BuildContext context)
      onSmartTypesettingFresh;
  final String Function(String stringCode, String defaultStr)? getI18nString;
  final Function(String imLink)? onIMClick;
  final bool needDownloadFonts;

  ///默认选中元素类型
  final String defaultSelectType;

  const CanvasBoxFrame(
      {super.key,
      required this.jsonData,
      required this.fontConfig,
      required this.toolkitButtons,
      this.onNavigatorPop,
      this.onSave,
      required this.onPrint,
      required this.onCheckSmartTypesettingOpen,
      required this.onSmartTypesetting,
      required this.onSmartTypesettingFresh,
      this.onLabelChange,
      this.onBack,
      this.onConfig,
      this.getI18nString,
      this.singleColorPrintInfo,
      this.consumablesType,
      required this.needDownloadFonts,
      required this.defaultSelectType,
      this.canvasInitData,
      this.onIMClick});

  @override
  CanvasBoxFrameState createState() => CanvasBoxFrameState();
}

class CanvasBoxFrameState extends State<CanvasBoxFrame> with SingleTickerProviderStateMixin {
  static Logger _logger = Logger("CanvasBoxFrameState", on: kDebugMode);

  bool replaceNeedDownloadFonts = false;

  /// 当前画板焦点状态
  ElementFocusState _elementFocusState = ElementFocusState.None;

  /// 当前选中组件对象
  List<CanvasElement> _focusedCanvasElements = [];

  // List<CanvasElement> _fakeMaterialCanvasElements = [ImageElement.createFakeMaterial().toCanvasElement()];

  CanvasElement? _previousfocusedCanvasElement;

  /// 编辑框组件对象
  /// 表格组件为 table cell
  /// 文本类组件与 focusedCanvasElements 相同
  CanvasElement? _cursorCanvasElement;

  /// 模板配置
  TemplateConfig? _templateConfig;

  /// 写入键盘输入框焦点index
  int? _inputCursorIndex;

  /// 是否可以通过indicator滑动面板，在序列号中输入的时候防止键盘下拉
  bool _isCanDrag = true;

  /// VIP横条显示控制，此变量和用户是否为VIP一起控制
  ValueNotifier<bool> vipVisible = ValueNotifier<bool>(true);

  /// 是否显示一键排版
  bool _showSmartTypesetting = false;
  bool _showSmartTypesettingGuide = false;

  //是否显示过一键排版按钮 显示过后不在重复埋曝光埋点
  bool hasShowSmartTypesetting = false;
  bool _isTransparent = false;

  ///画板默认缩放
  double? canvasDefaultScale;

  SharedPreferences? sp;

  /// 是否响应IM，当点击后防止重复点击
  bool isEnableIM = true;

  void reset() {
    _logger.log("reset()");
    canvasKey.currentState?.reset();
    resetAttributePanelHeight(AttrPanelState.normal);
  }

  resetAttributePanelHeight(AttrPanelState state, {isClick = false}) {
    _attrPanelState = state;
    _updateSmartTipsFrame();
    if (state == AttrPanelState.normal || state == AttrPanelState.highest) {
      _componentsPanelFolded = false;
      if (_isIndustryTemplate && state == AttrPanelState.normal && !isClick) {
        _componentsPanelFolded = true;
      }
    } else {
      _componentsPanelFolded = true;
    }
  }

  closeAttributePannel() {
    _componentsPanelFolded = false;
    if (_componentsPanelFolded) {
      _attrPanelState = AttrPanelState.shortest;
    } else {
      _attrPanelState = AttrPanelState.normal;
    }
    canvasKey.currentState?.cancelElementFocused();
    resetAttributePanelHeight(AttrPanelState.normal);
  }

  Future<String> asyncPrintPreView() async {
    reset();
    await Future.delayed(Duration(milliseconds: 200));
    return "1";
  }

  void setTemplateConfig(TemplateConfig templateConfig) {
    _templateConfig = templateConfig;

    /// 宽高重置
    _templateData?.width = templateConfig.templateWidth.toDouble();
    _templateData?.height = templateConfig.templateHeight.toDouble();
    _logger.log('模板宽高重置为：(${_templateData?.width}, ${_templateData?.height})');

    /// 倍率重置
    DisplayUtil.generateDesignRatio(_templateData?.width?.toDouble(), _templateData?.height?.toDouble());

    /// 清空图像库缓存
    _templateData?.resetImageCache();
  }

  TemplateData? _templateData;
  String _printColor = "";
  int _consumablesType = 0;
  GlobalKey _magicKey = GlobalKey();
  var isInit = true;

  @override
  void initState() {
    _logger.log("=========canvas_box_frame的initState方法被调用");
    super.initState();
    final canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
    if (canvasConfigIml?.currentConfigMode() == CanvasCurrentConfigMode.etag) {
      toolbarTitles = [intlanguage("app100000756", "添加元素"), intlanguage("app100000757", "数据")];
    }
    //行业模板控制器
    canvasIndustryTemplateAdapter = StateManagerController(CanvasIndustryTemplateLogic()).adapter;
    if (widget.singleColorPrintInfo != null && widget.singleColorPrintInfo!.isNotEmpty) {
      String colorPrint = widget.singleColorPrintInfo?["currentPrintColor"];
      // 多色下默认使用首个
      _printColor = colorPrint.split(',').first;
      _consumablesType = widget.singleColorPrintInfo?["consumablesType"];
      RfidUtil.supportSixteenGrayPrint = widget.singleColorPrintInfo?['supportSixteenGrayPrint'] ?? false;
    }
    CanvasEventBus.getDefault().register(this, (data) {
      _logger.log("打印画板EventBus消息：$data");
      if (data is Map && data.containsKey("action")) {
        final action = data['action'];
        switch (action) {
          /// 打印机连接识别到单色标签纸/碳带
          case "currentPrintColor":
            _logger.log("_printColor： $_printColor, _consumablesType: $_consumablesType");
            if (_printColor != data['currentPrintColor'] || _consumablesType != data['consumablesType']) {
              String colorPrint = data['currentPrintColor'];
              if (colorPrint.contains(',')) {
                //双色碳带
                _printColor = '';
              } else {
                _printColor = colorPrint;
              }
              _consumablesType = data['consumablesType'];
              RfidUtil.supportSixteenGrayPrint = data['supportSixteenGrayPrint'] ?? false;
              TemplateChangedNotifier().consumablesType = _consumablesType;
              TemplateChangedNotifier().updateTemplatePrintColor(_templateData?.generateCanvasJson(), _printColor);
              _logger.log("打印机连接识别到单色标签纸/碳带$data");
              _templateData?.resetImageCache();
              _resetPanelState();
            }
            break;

          /// 打印机连接识别到多色标签纸/碳带
          case "paperSupportColors":
            List<String> currentSinglePrintColor = List<String>.from(data['paperSupportColors']);
            if (currentSinglePrintColor.isNotEmpty) {
              _refreshElementColor(currentSinglePrintColor);
              _templateData?.resetImageCache();
              _resetPanelState();
            }
            _logger.log("打印机连接识别到多色标签纸/碳带：$data");
            break;

          /// RFID模板替换
          case "replaceTemplate":
            // var target = TemplateData.fromJson(data['targetTemplate']);
            var target = data['targetTemplate'];
            _replaceByRFIDTemplate(target);
            _resetPanelState();
            break;

          /// 小程序操作回调
          case "advanceQRCode":
            Map codeOperateInfo = data["advanceQRCodeInfo"];
            Map codeInfo = codeOperateInfo["data"];
            AdvanceQRCodeModel codeInfoModel = AdvanceQRCodeModel.fromJson(Map<String, dynamic>.from(codeInfo));
            codeOperateInfo["data"] = codeInfoModel;
            canvasKey.currentState?.operateAdvanceQRCode(codeOperateInfo);
            break;
          case "saveUpdateCanvasData":
            String newCanvasJsonStr = data["saveSuccessData"];
            TemplateUtils.transformTemplateJsonData(newCanvasJsonStr, showLoading: false).then((newCanvasJson) {
              TemplateData saveData = TemplateData.fromJson(json.decode(newCanvasJson));
              if ((saveData.id == _templateData?.id || data["oldId"] == _templateData?.id)) {
                CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                  "track": "click",
                  "posCode": "108_071_100",
                  "ext": {
                    "b_name": "保存",
                    "temp_id": saveData.id.length > 10 ? "" : saveData.id,
                    "industry_temp_id": saveData.cloudTemplateId,
                    "tag_id": saveData.profile.extrain.labelId
                  }
                });
                _updateSaveTemplateData(saveData);
              }
            });
            break;
          case "printSaveCanvasData":
            String canvasJsonStr = data["templateData"];
            String oldId = data["oldId"];
            TemplateData templateData = TemplateData.fromJson(json.decode(canvasJsonStr));
            if(templateData.id == _templateData?.id || oldId == _templateData?.id) {
              _updateSaveTemplateData(templateData);
              Future.delayed(Duration(milliseconds: 200)).then((value) {
                String? canvasJsonData = _templateData?.generateCanvasJson(savePrintTemplate: true);
                widget.canvasInitData?.call(canvasJsonData);
              });
            }
          case "printStatusChange":
            break;
        }
      }
    });

    // 由于初始化时机问题，挪动到项目外层Application中初始化
    // NetalPlugin().init();

    TemplateChangedNotifier().templateDataJsonData = widget.jsonData;

    /// 解析模板数据
    parseTemplateJsonData(widget.jsonData, init: true, isSortInit: true);
    if (_templateData?.dataSource?.isNotEmpty ?? false) {
      DataSource dataSource = _templateData!.dataSource!.first;
      if (dataSource.type == DataSourceType.excel) {
        int rowCount = ExcelTransformManager.sharedInstance().rowData?.length ?? 1;
        if (rowCount == 0) {
          rowCount = 1;
        }
        if (_templateData!.totalPage > rowCount) {
          _templateData!.totalPage = rowCount;
        }
        if (_templateData!.bindInfo?.total != null) {
          if (_templateData!.bindInfo!.total! > rowCount) {
            _templateData!.bindInfo!.total = rowCount;
          }
        }
      }
    }

    /// 监听模板数据被替换
    TemplateChangedNotifier().addListener(() {
      if (this.mounted) {
        /// 2024/8/27 Ice_Liu 初始化时模板数据的变化监听会导致行业模板接口被调用，所以对初始化做处理
        /// 解析模板数据
        parseTemplateJsonData(TemplateChangedNotifier().templateDataJsonData, clearStack: false, init: isInit);
        isInit = false;
        setState(() {});
      }
    });

    checkUnDownloadFonts();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      String? canvasJsonData = _templateData?.generateCanvasJson(savePrintTemplate: true);
      widget.canvasInitData?.call(canvasJsonData);

      /// 初始化智能提示的位置
      SmartTipsNotifier().bottomOffset = _getAttrPanelHeight();

      ///首次渲染，刷新下模版json数据，防止渲染误差导致的不改动也要提示保存的情况
      TemplateChangedNotifier().templateDataJsonData = _templateData?.generateCanvasJson();

      CanvasPluginManager().canvasConfigImpl?.addEntranceShowBurialPoint();

      Future.delayed(Duration(milliseconds: 200)).then((value) {
        if (widget.defaultSelectType != null && widget.defaultSelectType.isNotEmpty) {
          canvasKey.currentState?.selectDefaultElementWithType(widget.defaultSelectType);
        }
      });
    });
    canvasIndustryTemplateAdapter?.getController().initIndustryTemplate(_templateData);
  }

  @override
  void setState(VoidCallback fn) {
    try {
      if (mounted) {
        super.setState(fn);
      }
    } catch (e, s) {
      debugPrint('调用栈信息:\n $s');
    }
  }

  void parseTemplateJsonData(String? jsonData, {bool clearStack = true, bool init = false, bool isSortInit = false}) {
    List<GoodsModel>? keepGoodsList;
    try {
      /// 清除图像缓存
      if (_templateData != null) {
        _templateData?.resetImageCache();
      }

      /// 保留已导入的商品
      final keepGoodsList = _templateData?.goodsData;
    } catch (e, stack) {
      _logger.log("error==$stack");
    }

    try {
      /// 解析模板数据
      Map<String, dynamic> jsonMap = jsonDecode(jsonData ?? "");
      _templateData = TemplateData.fromJson(jsonMap);
      ExcelTransformManager.sharedInstance().templateData = _templateData;

      /// 清空, 在 build 时将重新计算显示倍率
      //TODO 意义不明 DisplayUtil.deviceSize = null;

      if (clearStack) {
        /// 清空撤销恢复栈
        StackManager().reset();
        StackManager().snapCanvasElements(_templateData?.canvasElements);
      }
      // 重置状态
      canvasKey.currentState?.clearMultiSelectAndFocused(needFresh: true);
      _elementFocusState = ElementFocusState.None;
      canvasIndustryTemplateAdapter?.getController().refreshSize(_templateData, isInit: init, isSortInit: isSortInit);
      _logger.log("==============parseTemplateJsonData");
      FloatingBarHelper().dismissFloatingBar();
      String? languageType = CanvasPluginManager().hostMethodImpl?.getCurrentLanguageType();
      if (_templateData?.layoutSchema != null &&
          _templateData?.layoutSchema != "" &&
          languageType == "zh-cn" &&
          _templateData?.supportedEditors.first == "cable") {
        isCable = true;
      } else {
        isCable = false;
      }
      setState(() {});

      /// 扫描指定路径下字体文件
      // _readFontFiles();
    } catch (e, stack) {
      _logger.log("error==$stack");
    }
  }

  /**
   * 检查模板是否需要下载字体
   */
  Future<bool> checkUnDownloadFonts({bool needScaleChangedTip = false}) async {
    List<String>? unSaveFontCodes = await _templateData?.checkNotDownloadFontCodes();
    _logger.log("checkUnDownloadFonts unSaveFontCodes${unSaveFontCodes}");

    if (unSaveFontCodes == null || unSaveFontCodes.isEmpty) {
      return Future.value(false);
    }

    ///如果不需要下载缺失字体的话，则重置成默认字体
    if (!widget.needDownloadFonts && !replaceNeedDownloadFonts) {
      _templateData?.resetElementFont(unSaveFontCodes);
      setState(() {});
      return Future.value(false);
    }
    if (!(CanvasPluginManager().fontPanelImpl?.isNetReachable() ?? false)) {
      _templateData?.resetElementFont(unSaveFontCodes);
      setState(() {});
      return Future.value(false);
    }
    bool checkFontList = await FontManager.sharedInstance().checkFontList();
    if (!checkFontList) {
      _templateData?.resetElementFont(unSaveFontCodes);
      setState(() {});
      return Future.value(false);
    }
    List<String> unFindFontCodes = [];
    List<FontItem> downloadFonts = [];
    List<FontItem?> fonts = FontManager().getConstructAllFontList();
    unSaveFontCodes.forEach((fontCode) {
      FontItem? item = fonts.singleWhereOrNull((font) => font?.code == fontCode && (font?.path.isNotEmpty ?? false));
      if (item == null) {
        unFindFontCodes.add(fontCode);
      }
      if (item != null) {
        downloadFonts.add(item);
      }
    });

    //全部不能下载
    if (downloadFonts.isEmpty) {
      String title = intlanguage("app01218", "字体缺失");
      String message = intlanguage("app01248", "因版权原因，该字体已停止使用");
      String rightFunStr = intlanguage("app00707", "我知道了");
      showCustomDialog(context, title, message, justSureButton: true, rightFunStr: rightFunStr, rightFunCall: () {
        _templateData?.resetElementFont(unSaveFontCodes);
        _changIndustryTemplateChangedTip(needScaleChangedTip: needScaleChangedTip);
      });
      return Future.value(true);
    }

    String content;
    if (downloadFonts.length == unSaveFontCodes?.length) {
      //全部可下载
      content = intlanguage("app100001034", "字体缺失，是否需要下载", param: [downloadFonts.length.toString()]);
    } else {
      //部分可以下载
      content = intlanguage("app100001035", "部分字体需下载，部分字体因版权原因已停用", param: [
        downloadFonts.length.toString(),
        ((unSaveFontCodes?.length ?? 0) - (downloadFonts.length ?? 0)).toString()
      ]);
    }
    _downloadFonts(content, unSaveFontCodes, downloadFonts, unFindFontCodes);
    return Future.value(true);
  }

  /**
   * 下载字体弹窗
   */
  _downloadFonts(
      String content, List<String>? unSaveFontCodes, List<FontItem> downloadFonts, List<String> unFindFontCodes,
      {bool needScaleChangedTip = false}) {
    showDownloadFontsDialog(context, title: "", fonts: downloadFonts, templateData: _templateData, cancelAction: () {
      _templateData?.resetElementFont(unSaveFontCodes);
      _templateData?.resetElementFont(unFindFontCodes);
      _changIndustryTemplateChangedTip(needScaleChangedTip: needScaleChangedTip);
    }, finishAction: (fontCodes) {
      // Future.delayed(const Duration(microseconds: 300), () {
      if (fontCodes.isNotEmpty) {
        _templateData?.resetElementFont(fontCodes);
      }
      if (unSaveFontCodes?.isNotEmpty ?? false) {
        _templateData?.resetElementFont(unFindFontCodes);
      }
      _templateData?.resetImageCache();
      setState(() {});
      _changIndustryTemplateChangedTip(needScaleChangedTip: needScaleChangedTip);
      // });
    });
  }

  @override
  void dispose() {
    CanvasEventBus.getDefault().unregister(this);
    canvasIndustryTemplateAdapter?.dispose();
    CanvasHelper.canvasContext = null;
    super.dispose();
  }

  _readFontFiles() async {
    Stream<FileSystemEntity> fileList = Directory(widget.fontConfig.fontPath).list();
    Map<String, String> usedFonts = {'fontDefault': widget.fontConfig.fontDefaultFile};
    List<String> fileFormat = ['ttf', 'otf', 'ttc'];
    await for (FileSystemEntity fileSystemEntity in fileList) {
      String path = fileSystemEntity.path;
      List<String> segments = path.split('/');
      if (segments.length > 1) {
        String fileName = segments.last;
        segments = fileName.split('.');
        if (segments.length == 2 && fileFormat.contains(segments.last.toLowerCase())) {
          usedFonts[segments.first] = fileName;
        }
      }
    }
    _logger.log('字体配置 ${usedFonts.toString()}');
    _templateData?.usedFonts = usedFonts;

    /// 清除图像缓存
    setState(() {
      _templateData?.resetImageCache();
    });
  }

  _resetPanelState() {
    if (_focusedCanvasElements.isEmpty) {
      _elementFocusState = ElementFocusState.None;
    }
  }

  /// 替换RFID模板
  _replaceByRFIDTemplate(TemplateData target) async {
    // _templateData = await OcrUtils.generateRfidReplaceTemplate(_templateData!, target);
    _templateData = target;
    ExcelTransformManager.sharedInstance().templateData = _templateData;
    TemplateChangedNotifier().templateDataJsonData = _templateData?.generateCanvasJson();
    CanvasPluginManager()
        .nativeMethodImpl
        ?.getCableDetail(
            _templateData!.profile.extrain.barcodeCategoryMap ?? {}, _templateData!.profile.extrain.labelId ?? "")
        .then((value) {
      if (value != "") {
        String? languageType = CanvasPluginManager().hostMethodImpl?.getCurrentLanguageType();
        Map<String, dynamic> jsonMap = json.decode(value);
        // 如果解析出的JSON数据包含layoutSchema，使用该数据并保存
        if (jsonMap["layoutSchema"] != null &&
            jsonMap["layoutSchema"] != "" &&
            languageType == "zh-cn" &&
            (jsonMap["supportedEditors"] as List).first == "cable") {
          isCable = true;
        } else {
          isCable = false;
        }
        setState(() {});
      }
    });
    setState(() {});
  }

  ///替换行业模板
  _replaceIndustryTemplate(Map<String, dynamic>? industyTemplateInfo) async {
    setState(() {
      //替换行业模板时收起模板面板高度至标准高度
      if (_isIndustryTemplate) {
        _attrPanelState = AttrPanelState.normal;
        _componentsPanelFolded = true;
      }
    });
    // EasyLoading.show();
    MagicLoading.instance.show(canvasKey.currentState?.context, text: intlanguage("app100001821", '正在智能排版...'));
    //从原生获取模板详情并下载相关资源后 返回数据库中模板数据
    String templateStr = await CanvasPluginManager()
            .nativeMethodImpl
            ?.getIndustryTemplateDetail(industyTemplateInfo)
            .timeout(Duration(milliseconds: 10000), onTimeout: () => '') ??
        "";
    if (templateStr.isEmpty || templateStr == null) {
      MagicLoading.instance.hide(canvasKey.currentState?.context);
      return;
    }

    Map<String, dynamic> jsonMap = jsonDecode(templateStr);
    if (jsonMap["templateVersion"] != null && jsonMap["templateVersion"].isNotEmpty) {
      String isCanOpen =
          await CanvasPluginManager().nativeMethodImpl?.isCanOpenTemplate(jsonMap["templateVersion"]) ?? "";
      if (isCanOpen == "0") {
        MagicLoading.instance.hide(canvasKey.currentState?.context);
        LoadingMix.showToast(intlanguage("app100000343", "您的软件版本过低，请升级"));
        return;
      }
    }
    //转换前清空原始数据源数据
    ExcelManager.sharedInstance().dataSourceWrapper = null;
    //此处避免解析行业模板时 数据源datasource 管理类ExcelTransformManager 将画板datasource覆盖，先保留画板模板datasource
    DataSource? originalDataSource = _templateData?.cloneDataSource();
    //存在excel绑定或商品库数据时转换
    templateStr = await TemplateUtils.transformTemplateJsonData(templateStr, showLoading: false);
    //还原画板datasource
    _templateData?.dataSource = originalDataSource == null ? null : [originalDataSource];
    // EasyLoading.dismiss();
    MagicLoading.instance.hide(canvasKey.currentState?.context);
    //图片预加载及元素镜像处理
    templateStr = await TemplateData.loadImageData(templateStr);
    Map<String, dynamic> industryJsonMap = jsonDecode(templateStr);
    //检查高级二维码状态
    AdvanceQRCodeManager().initAdvanceQRCodeCache(TemplateData.fromJson(industryJsonMap));

    ///行业模板与画板数据融合并刷新，并检查字体资源下载
    Function(TemplateData, bool) mergeIndustryTemplateCallback =
        (TemplateData industryTemplateData, bool needScaleChangedTip) async {
      industryTemplateId = industryTemplateData.id;
      TemplateData originalData = TemplateData.fromJson(_templateData!.toJson());
      //行业模板与画板数据融合
      _templateData = await OcrUtils.generateIndustryReplaceTemplate(industryTemplateData, _templateData!);
      TemplateData replaceData = TemplateData.fromJson(_templateData!.toJson());

      ExcelTransformManager.sharedInstance().templateData = _templateData;
      TemplateChangedNotifier().templateDataJsonData = _templateData?.generateCanvasJson();
      replaceNeedDownloadFonts = true;
      //检查字体下载
      bool needDownloadFonts = await checkUnDownloadFonts(needScaleChangedTip: needScaleChangedTip);
      if (!needDownloadFonts) {
        _changIndustryTemplateChangedTip(needScaleChangedTip: needScaleChangedTip);
      }

      Future.delayed(Duration(seconds: 1)).then((value) {
        setState(() {
          // 更新为null值，防止实时时间导致的重复刷新scale系数被重置
          canvasDefaultScale = null;
        });
      });
      StackManager().industryTemplateReplace(replaceData, originalData, context);
    };
    TemplateData industryTemplateData = TemplateData.fromJson(industryJsonMap);
    // //对比行业模板与画板数据 尺寸，不符合时弹窗提示
    // if (industryTemplateData.width != _templateData?.width || industryTemplateData.height != _templateData?.height) {
    //   //根据行业模板中元素布局 计算替换后缩放范围
    //   canvasDefaultScale = canvasTemplateOversideScale(industryTemplateData);
    //   mergeIndustryTemplateCallback(industryTemplateData, true);
    // } else {
    //   canvasDefaultScale = 1;
    //   mergeIndustryTemplateCallback(industryTemplateData, false);
    // }
    mergeIndustryTemplateCallback(industryTemplateData, false);
  }

  static String industryTemplateId = "";

  _changIndustryTemplateChangedTip({bool needScaleChangedTip = false}) async {
    if (!needScaleChangedTip) return;
    if (sp == null) sp = await SharedPreferences.getInstance();
    bool? hasShowReplaceSizeTip = await sp?.getBool("hasShowReplaceSizeTip");
    Future.delayed(Duration(milliseconds: 300)).then((value) {
      if (hasShowReplaceSizeTip == null) {
        CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
          "track": "show",
          "posCode": "108_176",
          "ext": {"temp_id": _templateData?.id, "industry_temp_id": industryTemplateId}
        });
        // showToast(msg: intlanguage('app100001545', '标签纸尺寸不一致，请手动微调'));
        sp?.setBool("hasShowReplaceSizeTip", true);
      }
    });
  }

  ///获取替换行业模板后 画板缩放比例
  double canvasTemplateOversideScale(TemplateData industryTemplateData) {
    double xMax = 0; //元素最左边
    double yMax = 0; //画板元素最顶边
    double rightMax = 0; //元素最右边
    double bottomMax = 0; //元素最底边
    for (var element in industryTemplateData.elements) {
      if (element.x < 0 && element.x.toDouble().abs() > xMax) {
        xMax = element.x.toDouble();
      }
      double? elementRight = element.x.toDouble() + element.width.toDouble();
      if (_templateData?.width != null && elementRight > _templateData!.width!.toDouble() && elementRight > rightMax) {
        rightMax = elementRight;
      }
      if (element.y < 0 && element.y.toDouble().abs() > yMax) {
        yMax = element.y.toDouble();
      }
      double elementBottom = element.y.toDouble() + element.height.toDouble();
      if (elementBottom > (_templateData?.height?.toDouble() ?? 0) && elementBottom > bottomMax) {
        bottomMax = elementBottom;
      }
    }
    //获取上下左右超出最大值与画板宽高最比例后取最大值
    double maxScale = (_templateData?.width != null && _templateData?.height != null)
        ? max(max(xMax.abs() / _templateData!.width!.toDouble(), yMax.abs() / _templateData!.height!.toDouble()),
            max(rightMax / _templateData!.width!.toDouble(), bottomMax / _templateData!.height!.toDouble()))
        : 0;
    maxScale = maxScale > 1 ? maxScale : 1;
    return maxScale <= 1 ? 1 : ((1 / maxScale)) * 1.1;
  }

  //一键排版曝光埋点
  _smartTypesShowBuriedPoint() {
    if (hasShowSmartTypesetting == false && _showSmartTypesetting) {
      CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
        "track": "show",
        "posCode": "108_230",
      });
      hasShowSmartTypesetting = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    final canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
    CanvasHelper.canvasContext = context;
    _logger.log("=========canvas_box_frame的build方法被调用");

    /// 计算倍率
    // if (DisplayUtil.deviceSize == null) {
    DisplayUtil.init(context);
    DisplayUtil.generateDesignRatio(_templateData?.width?.toDouble(), _templateData?.height?.toDouble());
    // }
    //一键排版曝光埋点
    _smartTypesShowBuriedPoint();
    //处理画板皮肤背景
    String canvasBgUrl = intlanguage("app100001258", "");
    Widget placeHoder = SizedBox.shrink();
    Widget canvasBgImg = placeHoder;
    if (canvasBgUrl.isNotEmpty && canvasBgUrl.startsWith("http")) {
      Widget placeHoder = SizedBox.shrink();
      canvasBgImg = CachedNetworkImage(
        width: MediaQuery.sizeOf(context).width,
        height: MediaQuery.sizeOf(context).height,
        fit: BoxFit.cover,
        imageUrl: canvasBgUrl,
        placeholder: (_, __) => placeHoder,
        errorWidget: (_, __, ___) => placeHoder,
      );
    }
    return MultiProvider(
        providers: [
          /// 撤销、恢复数据栈变动
          ChangeNotifierProvider.value(value: StackPointerChangedNotifier()),
        ],
        child: WillPopScope(
          onWillPop: () async {
            return _backSaveClick();
          },
          child: Scaffold(
              resizeToAvoidBottomInset: false,
              backgroundColor: ThemeColor.COLOR_F5F5F5,
              appBar: AppBar(
                toolbarHeight: 42,
                backgroundColor: CanvasTheme.of(context).backgroundLightColor,
                elevation: 0.2,
                flexibleSpace: _isIndustryTemplate && _attrPanelState == AttrPanelState.highest
                    ? Container(
                        decoration: BoxDecoration(
                            color: _elementFocusState != ElementFocusState.Editor
                                ? Colors.black.withOpacity(0.35)
                                : Colors.transparent // 设置遮罩颜色和透明度
                            ),
                      )
                    : null,
                systemOverlayStyle:
                    SystemUiOverlayStyle(statusBarColor: Colors.transparent, statusBarIconBrightness: Brightness.dark),
                leading: Row(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 5.0, horizontal: 0),
                      child: AttrIconButton(
                        Transform.rotate(
                          angle: Directionality.of(context) == TextDirection.rtl ? pi : 0,
                          child: SvgIcon(
                            'assets/app_bar/app_bar_close.svg',
                            side: 26,
                          ),
                        ),
                        width: 50,
                        height: 34,
                        isEnableLongPressThrottle: false,
                        onTap: () async {
                          if (_isIndustryTemplate &&
                              _attrPanelState == AttrPanelState.highest &&
                              _elementFocusState != ElementFocusState.Editor &&
                              _focusedCanvasElements.isEmpty) {
                            return;
                          }
                          _backSaveClick();
                        },
                      ),
                    ),
                  ],
                ),
                leadingWidth: canvasConfigIml?.currentConfigMode() == CanvasCurrentConfigMode.etag ? 90 : 50,
                centerTitle: true,
                title: canvasConfigIml?.currentConfigMode() == CanvasCurrentConfigMode.etag
                    ? Consumer<StackPointerChangedNotifier>(
                        builder: (BuildContext context, StackPointerChangedNotifier value, Widget? child) {
                        /// 撤销、恢复
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(
                              width: (MediaQuery.sizeOf(context).width - 90) / 2 - 105,
                            ),
                            _undoActionButton(),
                            SizedBox(
                              width: 20,
                            ),
                            _redoActionButton(),
                          ],
                        );
                      })
                    : SizedBox.shrink(),
                actions: canvasConfigIml?.currentConfigMode() == CanvasCurrentConfigMode.etag
                    ? null
                    : [
                        const SizedBox(
                          width: 65,
                        ),
                        Consumer<StackPointerChangedNotifier>(
                            builder: (BuildContext context, StackPointerChangedNotifier value, Widget? child) {
                          /// 撤销、恢复
                          return Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              _undoActionButton(),
                              SizedBox(
                                width: 20,
                              ),
                              _redoActionButton(),
                            ],
                          );
                        }),

                        const Spacer(),

                        // 清空画板
                        Offstage(
                          offstage: !(canvasConfigIml?.isShowClearButton() ?? true),
                          child: AttrIconButton(
                              SvgIcon(
                                'assets/app_bar/app_bar_clear.svg',
                                side: 24,
                              ), onTap: () {
                            if (_isIndustryTemplate &&
                                _attrPanelState == AttrPanelState.highest &&
                                _elementFocusState != ElementFocusState.Editor &&
                                _focusedCanvasElements.isEmpty) {
                              return;
                            }
                            _attrPanelState = AttrPanelState.normal;
                            TrackUtils.sendTrackingWrapTemplateId({
                              "track": "click",
                              "posCode": "108_074_103",
                              "ext": {"b_name": "清空画板"}
                            });
                            canvasKey.currentState?.clearCanvas();
                          }),
                        ),

                        const SizedBox(
                          width: 4,
                        ),

                        // 画板旋转
                        Offstage(
                          offstage: !(canvasConfigIml?.isShowOverturnButton() ?? true),
                          child: AttrIconButton(
                            Image.asset(
                              'assets/app_bar/app_bar_rotate_new.png',
                              package: 'niimbot_flutter_canvas',
                              height: 24,
                              width: 24,
                            ),
                            onTap: () {
                              if (_isIndustryTemplate &&
                                  _attrPanelState == AttrPanelState.highest &&
                                  _elementFocusState != ElementFocusState.Editor &&
                                  _focusedCanvasElements.isEmpty) {
                                return;
                              }
                              TrackUtils.sendTrackingWrapTemplateId({
                                "track": "click",
                                "posCode": "108_074_103",
                                "ext": {"b_name": "旋转画板"}
                              });
                              setState(() {
                                /// 交换宽高
                                _templateData!.width = _templateData!.width! + _templateData!.height!;
                                _templateData!.height = _templateData!.width! - _templateData!.height!;
                                _templateData!.width = _templateData!.width! - _templateData!.height!;
                                _logger.log('模板宽高切换为：(${_templateData?.width}, ${_templateData?.height})');

                                // 逻辑 from 云打印 start.
                                _templateData!.canvasRotate = _templateData!.canvasRotate + 90;
                                if (_templateData?.canvasRotate == 360) {
                                  _templateData?.canvasRotate = 0;
                                }
                                _templateData!.rotate = _templateData!.rotate + 270;
                                if (_templateData!.rotate >= 360) {
                                  _templateData!.rotate = _templateData!.rotate - 360;
                                }
                                int? cableDirection = _templateData?.cableDirection?.toInt();
                                if (cableDirection != null && cableDirection != -1) {
                                  cableDirection = (cableDirection + 1 == 4) ? 0 : cableDirection + 1;
                                  cableDirection = cableDirection % 4;
                                }
                                _templateData?.cableDirection = cableDirection;
                                // 逻辑 from 云打印 end.

                                DisplayUtil.generateDesignRatio(
                                    _templateData?.width?.toDouble(), _templateData?.height?.toDouble());
                                _templateData?.resetImageCache();
                                resetElementMirrorLocation();

                                /// 隐藏工具条，清空多选状态
                                _elementFocusState = ElementFocusState.None;
                                canvasKey.currentState?.clearMultiSelectAndFocused();
                                _attrPanelState = AttrPanelState.normal;
                                if (_isIndustryTemplate) {
                                  _attrPanelState = AttrPanelState.normal;
                                  _componentsPanelFolded = true;
                                }
                                FloatingBarHelper().dismissFloatingBar();
                              });
                            },
                          ),
                        ),

                        const SizedBox(
                          width: 4,
                        ),

                        /// 设置
                        Offstage(
                          offstage: !(canvasConfigIml?.isShowSettingButton() ?? false),
                          child: AttrIconButton(
                            SvgIcon(
                              'assets/app_bar/app_bar_setting.svg',
                              side: 24,
                            ),
                            isEnableLongPressThrottle: false,
                            onTap: () {
                              if (_isIndustryTemplate &&
                                  _attrPanelState == AttrPanelState.highest &&
                                  _elementFocusState != ElementFocusState.Editor &&
                                  _focusedCanvasElements.isEmpty) {
                                return;
                              }
                              if (_isIndustryTemplate) {
                                _attrPanelState = AttrPanelState.normal;
                                _componentsPanelFolded = true;
                              }
                              setState(() {
                                /// 隐藏工具条，清空多选状态
                                _elementFocusState = ElementFocusState.None;
                                canvasKey.currentState?.clearMultiSelectAndFocused();
                                // 清除面板选中下标
                                defaultIndex = null;
                                FloatingBarHelper().dismissFloatingBar();
                              });
                              Timer(Duration(milliseconds: 50), () {
                                widget.onConfig
                                    ?.call(_templateData?.generateCanvasJson(savePrintTemplate: true), _templateConfig,
                                        context)
                                    ?.then((value) {
                                  canvasIndustryTemplateAdapter?.getController().refreshSize(_templateData);
                                  if (value == null) {
                                    return;
                                  }

                                  setState(() {
                                    parseTemplateJsonData(value.generateCanvasJson(savePrintTemplate: true),
                                        clearStack: false);
                                  });
                                });
                              });
                            },
                          ),
                        ),

                        const SizedBox(
                          width: 4,
                        ),

                        // IM
                        Offstage(
                          offstage: !(canvasConfigIml?.isShowDrawBoardIM() ?? true),
                          child: AttrIconButton(
                              Image.asset(
                                (_isIndustryTemplate &&
                                        _attrPanelState == AttrPanelState.highest &&
                                        _elementFocusState != ElementFocusState.Editor)
                                    ? 'assets/app_bar/app_bar_im_gray.png'
                                    : 'assets/app_bar/app_bar_im.png',
                                package: 'niimbot_flutter_canvas',
                                height: 32,
                                width: 32,
                              ),
                              isEnableLongPressThrottle: false, onTap: () async {
                            if (_isIndustryTemplate &&
                                _attrPanelState == AttrPanelState.highest &&
                                _elementFocusState != ElementFocusState.Editor &&
                                _focusedCanvasElements.isEmpty) {
                              return;
                            }
                            TrackUtils.sendTrackingWrapTemplateId({
                              "track": "click",
                              "posCode": "108_074_103",
                              "ext": {"b_name": "客服"}
                            });
                            // 防止重复点击
                            if (!isEnableIM) return;
                            isEnableIM = false;
                            FloatingBarHelper().dismissFloatingBar();
                            bool networkConnected = CanvasPluginManager().fontPanelImpl?.isNetReachable() ?? false;
                            if (!networkConnected) {
                              LoadingMix.showToast(intlanguage('app100000625', '当前网络状态异常'));
                              isEnableIM = true;
                              return;
                            }
                            String imLink = await CanvasPluginManager().nativeMethodImpl?.getDrawBoardIM() ?? "";
                            if (imLink != null && imLink.isNotEmpty) {
                              if (Platform.isAndroid) {
                                if (widget.onIMClick != null) {
                                  widget.onIMClick!(imLink);
                                }
                                isEnableIM = true;
                              } else {
                                showModalBottomSheet(
                                    context: context,
                                    isScrollControlled: true,
                                    isDismissible: false,
                                    enableDrag: false,
                                    shape: const RoundedRectangleBorder(
                                        borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
                                    builder: (BuildContext context) {
                                      return DrawBoardIMPage(
                                        imLink: imLink,
                                      );
                                    });
                                isEnableIM = true;
                              }
                            }
                          }),
                        ),

                        SizedBox(
                          width: 5,
                        ),
                      ],
              ),
              body: CanvasObjectSharedWidget(
                printColor: _printColor,
                consumablesType: _consumablesType,
                canvasData: _templateData,
                fontConfig: widget.fontConfig,
                child: MultiProvider(
                  providers: [
                    // 元素数据变动
                    ChangeNotifierProvider.value(value: ElementsDataChangedNotifier()),
                    // 最近使用素材
                    ChangeNotifierProvider.value(value: MaterialRecentChangedNotifier()),
                    // 用户中心通知
                    ChangeNotifierProvider.value(value: CanvasUserCenter()),
                  ],
                  child: Stack(
                    alignment: AlignmentDirectional.bottomCenter,
                    children: [
                      canvasBgImg,
                      // 画板
                      buildMixNbCanvasData(),
                      isCable && CanvasPluginManager().nativeMethodImpl!.languageIsCN()
                          ? GestureDetector(
                              onTap: () {
                                CanvasPluginManager()
                                    .nativeMethodImpl
                                    ?.sendTrackingToNative({"track": 'click', "posCode": "125_336"});
                                String? canvasData = _templateData?.generateCanvasJson(savePrintTemplate: true);
                                Function toCable = () {
                                  CanvasPluginManager().nativeMethodImpl?.toCableApp(
                                      _templateData!.profile.extrain.barcodeCategoryMap ?? {},
                                      context,
                                      canvasData!,
                                      _templateData!.profile.extrain.labelId ?? "");
                                };
                                showCustomDialog(context, intlanguage('app00032', '提示'),
                                    intlanguage('app100001709', '即将离开通用画板，是否保存？'),
                                    rightFunStr: intlanguage('app00017', '保存'),
                                    leftFunStr: intlanguage('app00111', '不保存'), leftFunCall: () {
                                  toCable.call();
                                }, rightFunCall: () {
                                  _saveClick(isOtherSave: false);
                                  toCable.call();
                                });
                              },
                              child: Container(
                                margin: EdgeInsets.only(bottom: Platform.isAndroid ? 150 : 180, left: 16, right: 16),
                                padding: EdgeInsetsDirectional.fromSTEB(12, 0, 12, 0),
                                width: MediaQuery.of(context).size.width,
                                height: 42,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.0), // 设置圆角
                                  color: ThemeColor.background, //
                                  boxShadow: [
                                    BoxShadow(
                                      blurRadius: 5, //阴影范围
                                      spreadRadius: 0.1, //阴影浓度
                                      offset: Offset(0, 0),
                                      color: Colors.grey.withOpacity(0.2), //阴影颜色
                                    ),
                                  ], // 容器的背景颜色
                                ),
                                child: Row(
                                  children: [
                                    Image.asset(
                                      'assets/common/cable.png',
                                      package: 'niimbot_flutter_canvas',
                                      //    fit: BoxFit.cover,
                                      height: 20,
                                      width: 20,
                                    ),
                                    SizedBox(
                                      width: 6,
                                    ),
                                    Center(
                                      child: Text(intlanguage('app100001642', '使用线缆专用小程序')),
                                    ),
                                    Expanded(child: Container()),
                                    SvgIcon(
                                      'assets/common/arrow_right_grey.svg',
                                      useDefaultColor: false,
                                      width: 10,
                                      height: 16,
                                    ),
                                  ],
                                ),
                              ),
                            )
                          : Container(),
                      _isIndustryTemplate && _attrPanelState == AttrPanelState.highest && _elementFocusState == ElementFocusState.None
                          ? Container(
                              decoration: BoxDecoration(
                                color: _elementFocusState != ElementFocusState.Editor
                                    ? Colors.black.withOpacity(0.35)
                                    : Colors.transparent, // 设置遮罩颜色和透明度
                              ),
                            )
                          : Container(),
                      // 面板区域

                      ValueListenableBuilder<int>(
                        builder: (BuildContext context, int value, Widget? child) {
                          return _buildToolsPanel();
                        },
                        valueListenable: AttributePanelRefreshNotify.notifier,
                      ),
                      _isIndustryTemplate && _attrPanelState == AttrPanelState.highest ? Container() : getBottomBar(),
                      // 保存/打印
                      // if (_elementFocusState == ElementFocusState.None)
                    ],
                  ),
                ),
              )),
        ));
  }

  ///撤销
  Widget _undoActionButton() {
    return AttrIconButton(
      SvgIcon(
        'assets/app_bar/app_bar_undo.svg',
        color: canvasKey.currentState?.canUndo() ?? false
            ? CanvasTheme.of(context).iconColor
            : Colors.black.withOpacity(0.3),
        side: 24,
      ),
      width: 34,
      height: 34,
      onTap: () {
        if (_isIndustryTemplate &&
            _attrPanelState == AttrPanelState.highest &&
            _elementFocusState != ElementFocusState.Editor &&
            _focusedCanvasElements.isEmpty) {
          return;
        }
        if (!DebounceUtil.checkClick(needTime: 300)) return;
        if (FloatingBarHelper().multiSelect == false) {
          FloatingBarHelper().dismissFloatingBar();
        } else {
          canvasKey.currentState?.multiSelect = false;
          FloatingBarHelper().dismissFloatingBar();
        }
        if (_isIndustryTemplate) {
          _attrPanelState = AttrPanelState.normal;
          _componentsPanelFolded = true;
        }
        CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
          "track": "click",
          "posCode": "108_212",
          "ext": {"type": 1}
        });
        canvasKey.currentState?.canUndo() ?? false ? canvasKey.currentState?.undo.call() : null;
      },
    );
  }

  ///恢复
  Widget _redoActionButton() {
    return AttrIconButton(
      SvgIcon(
        'assets/app_bar/app_bar_redo.svg',
        color: canvasKey.currentState?.canRedo() ?? false
            ? CanvasTheme.of(context).iconColor
            : Colors.black.withOpacity(0.3),
        side: 24,
      ),
      width: 34,
      height: 34,
      onTap: () {
        if (_isIndustryTemplate &&
            _attrPanelState == AttrPanelState.highest &&
            _elementFocusState != ElementFocusState.Editor &&
            _focusedCanvasElements.isEmpty) {
          return;
        }
        if (!DebounceUtil.checkClick(needTime: 300)) return;
        if (FloatingBarHelper().multiSelect == false) {
          FloatingBarHelper().dismissFloatingBar();
        } else {
          canvasKey.currentState?.multiSelect = false;
          FloatingBarHelper().dismissFloatingBar();
        }
        CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
          "track": "click",
          "posCode": "108_212",
          "ext": {"type": 2}
        });
        _logger.log("=======canRedo: ${canvasKey.currentState?.canRedo()}");
        canvasKey.currentState?.canRedo() ?? false ? canvasKey.currentState?.redo.call() : null;
      },
    );
  }

  Align getBottomBar() {
    final canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
    return canvasConfigIml?.isShowPrintButton() ?? false ? _buildBottomBar() : _buildEtagBottomBar();
  }

  resetElementMirrorLocation() {
    String value = "";
    double? templateWidth = _templateData?.width?.toDouble();
    double? templateHeight = _templateData?.height?.toDouble();

    /// 画板中心点
    Offset? templateCenter = Offset((templateWidth ?? 0) / 2.0, (templateHeight ?? 0) / 2.0);
    List<CanvasElement> elements = [];
    for (var element in _templateData?.canvasElements ?? []) {
      if (element.data.isOpenMirror == 1) {
        JsonElement jsonElement = element.data;
        String mirrorId = element.data.generateFixedMirrorId();
        for (var mirrorElement in _templateData?.canvasElements ?? []) {
          if (mirrorId == mirrorElement.data.id) {
            JsonElement elementModel = mirrorElement.data;
            Offset distanceToCenter = templateCenter -
                Offset(jsonElement.x.toDouble() + jsonElement.width, jsonElement.y.toDouble() + jsonElement.height);

            Offset mirrorPosition = templateCenter + distanceToCenter;
            if (jsonElement.mirrorType == 1) {
              /// 1: 画板中心 y 轴镜像
              elementModel.y = mirrorPosition.dy;
            } else if (jsonElement.mirrorType == 2) {
              /// 2: 画板中心 x 轴镜像
              elementModel.x = mirrorPosition.dx;
            } else {
              /// 0: 画板中心点镜像
              elementModel.x = mirrorPosition.dx;
              elementModel.y = mirrorPosition.dy;
            }
            break;
          }
        }
      }
    }
    TemplateChangedNotifier().templateDataJsonData = _templateData?.generateCanvasJson();
  }

  Align _buildEtagBottomBar() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        padding: EdgeInsets.only(bottom: safeAreaBottomHeight),
        decoration: BoxDecoration(
          color: CanvasTheme.of(context).backgroundLightColor, // 底色
          boxShadow: [
            BoxShadow(
              blurRadius: 5, //阴影范围
              spreadRadius: 0.1, //阴影浓度
              offset: Offset(0, -2.0),
              color: Colors.grey.withOpacity(0.15), //阴影颜色
            ),
          ],
        ),
        child: Material(
          color: CanvasTheme.of(context).backgroundLightColor,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Expanded(
                child: Material(
                  color: CanvasTheme.of(context).backgroundLightColor,
                  child: InkWell(
                    highlightColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          GestureDetector(
                            onTap: () {
                              //_saveClick();
                              _etagSaveClick();
                            },
                            child: Container(
                              //padding: EdgeInsets.fromLTRB(26, 8, 26, 8),
                              height: 38,
                              decoration:
                                  BoxDecoration(color: Color(0xFFFB4B42), borderRadius: BorderRadius.circular(24)),
                              child: Row(
                                children: [
                                  SizedBox(
                                    width: 26,
                                  ),
                                  SvgIcon(
                                    'assets/bottom_bar/bottom_bar_save.svg',
                                    color: Color(0xFFFFFFFF),
                                  ),
                                  SizedBox(
                                    width: 8,
                                  ),
                                  Text(intlanguage("app100001283", '保存模版'),
                                      style: TextStyle(
                                          fontSize: 13.0, color: Color(0xFFFFFFFF), fontWeight: FontWeight.w500)),
                                  SizedBox(
                                    width: 26,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  /// 底部栏: 保存/打印
  Align _buildBottomBar() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
          height: _maxElementToolbarOffset + safeAreaBottomHeight,
          padding: EdgeInsets.only(bottom: safeAreaBottomHeight),
          decoration: BoxDecoration(
            color: CanvasTheme.of(context).backgroundLightColor, // 底色
            boxShadow: [
              BoxShadow(
                blurRadius: 5, //阴影范围
                spreadRadius: 0.1, //阴影浓度
                offset: Offset(0, -2.0),
                color: Colors.grey.withOpacity(0.15), //阴影颜色
              ),
            ],
          ),
          child: Row(mainAxisAlignment: MainAxisAlignment.spaceAround, children: [
            Expanded(
              child: Material(
                color: CanvasTheme.of(context).backgroundLightColor,
                child: InkWell(
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  onTap: () async {
                    if (!DebounceUtil.checkClick(needTime: 1000)) return;
                    onSaveResult(TemplateData value) {
                      String templateId;
                      if (Platform.isAndroid) {
                        if (value.id.isNotEmpty &&
                            value.cloudTemplateId!.isNotEmpty &&
                            value.id == value.cloudTemplateId) {
                          templateId = "";
                        } else {
                          templateId = value.id.length > 10 ? "" : value.id;
                        }
                        if (templateId.isNotEmpty) {
                          CanvasPluginManager().nativeMethodImpl!.sendTrackingToNative({
                            "track": "click",
                            "posCode": "108_071_100",
                            "ext": {
                              "b_name": "保存",
                              "temp_id": (templateId ?? "").length > 10 ? "" : (templateId ?? ""),
                              "industry_temp_id": value.cloudTemplateId ?? "",
                              "tag_id": value.profile.extrain.labelId ?? ""
                            }
                          });
                        }
                      }
                      _updateSaveTemplateData(value);
                    }

                    widget.onSave
                        ?.call(_templateData?.generateCanvasJson(savePrintTemplate: true), _templateConfig, context,
                            _templateData?.name ?? "",
                            templateId: _templateData?.id ?? "")
                        .then((value) {
                      onSaveResult(value);
                    });
                    // SharedPreferences sp = await SharedPreferences.getInstance();
                    // bool? hasShowPdfSaveTip = sp.getBool("has_show_pdf_save_tip");
                    // if (hasShowPdfSaveTip == null &&
                    //     PdfBindInfoManager.instance.getPDFEnablePrintMaxNumber(_templateData!) > 0) {
                    //   sp.setBool("has_show_pdf_save_tip", true);
                    //   String title = intlanguage('app100001861', '仅保存当前页面的PDF内容，下次打印时请重新选择PDF文件。');
                    //   showCustomDialog(context, title, "",
                    //       justSureButton: true,
                    //       dismissOutSideTouch: false,
                    //       rightFunStr: intlanguage('app00707', '我知道了'), rightFunCall: () {
                    //     widget.onSave
                    //         ?.call(_templateData?.generateCanvasJson(savePrintTemplate: true), _templateConfig, context,
                    //             _templateData?.name ?? "",
                    //             templateId: _templateData?.id ?? "")
                    //         .then((value) {
                    //       onSaveResult(value);
                    //     });
                    //   }, sureButtonColor: Colors.black);
                    // } else {
                    //   widget.onSave
                    //       ?.call(_templateData?.generateCanvasJson(savePrintTemplate: true), _templateConfig, context,
                    //           _templateData?.name ?? "",
                    //           templateId: _templateData?.id ?? "")
                    //       .then((value) {
                    //     onSaveResult(value);
                    //   });
                    // }
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 4),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgIcon('assets/bottom_bar/bottom_bar_save.svg'),
                        SizedBox(
                          width: 8,
                        ),
                        Text(intlanguage("app00017", "保存"), style: CanvasTheme.of(context).bottomBarTextStyle)
                      ],
                    ),
                  ),
                ),
              ),
            ),
            Container(
              color: CanvasTheme.of(context).backgroundColor,
              width: 1,
              height: 17,
            ),
            Expanded(
              child: Material(
                  color: CanvasTheme.of(context).backgroundLightColor,
                  child: InkWell(
                    highlightColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    onTap: () {
                      if (!DebounceUtil.checkClick(needTime: 300)) return;
                      FloatingBarHelper().dismissFloatingBar();
                      String? templateId;
                      _templateData?.hasVIPRes = _templateData?.hasVipElement()??false;
                      if ((_templateData?.id ?? "").isNotEmpty &&
                          (_templateData?.cloudTemplateId ?? "").isNotEmpty &&
                          _templateData?.id == _templateData?.cloudTemplateId &&
                          _templateData?.profile.extrain.templateType != 0) {
                        templateId = "";
                      } else {
                        templateId = (_templateData?.id ?? "").length > 10 ? "" : _templateData?.id;
                      }
                      CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                        "track": "click",
                        "posCode": "108_071_100",
                        "ext": {
                          "b_name": "打印",
                          "temp_id": templateId,
                          "industry_temp_id": _templateData?.cloudTemplateId,
                          "tag_id": _templateData?.profile.extrain.labelId
                        }
                      });
                      if (_templateData?.isContaintInstantTime() == true) {
                        CanvasPluginManager().nativeMethodImpl?.vipSourceTrial(context, () {
                          widget.onPrint.call(
                              _templateData?.generateCanvasJson(savePrintTemplate: true),
                              _templateData?.dataBindingMode,
                              _templateData?.getCurrentPage(),
                              _templateConfig,
                              _getPdfPrintInfo(),
                              context);
                        }, privilegeCode: "INSTANT_TIME_PRINT");
                      } else if ((_templateData?.needVip() ?? false) && !CanvasUserCenter().isVip) {
                        ///这里分情况，未登录时---登录弹框与购买VIP页面一起出现
                        /// 已登录时---有个中间弹框---区分会员状态是非会员还是过期
                        if (!CanvasUserCenter().isLogin) {
                          _guideBuyVip(false, () {
                            Future.delayed(Duration(seconds: 1), () {
                              widget.onPrint.call(
                                  _templateData?.generateCanvasJson(savePrintTemplate: true),
                                  _templateData?.dataBindingMode!,
                                  _templateData?.getCurrentPage(),
                                  _templateConfig,
                                  _getPdfPrintInfo(),
                                  context);
                            });
                          });
                        } else {
                          // TODO: 108_083的source来源
                          CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                            "track": "show",
                            "posCode": "108_083",
                            "ext": {"pop_type": "1", "source": ''}
                          });
                          VIPType type = CanvasUserCenter().vipType;
                          if (type == VIPType.expired) {
                            String title = intlanguage("app01519", "您的VIP会员已过期，无法继续使用VIP会员专属资源。");
                            String leftBtn = intlanguage('app00707', '我知道了');
                            String rightBtn = intlanguage("app01512", "立即续费");
                            showNimmbotDialog(context, title: title, cancelDes: leftBtn, confirmDes: rightBtn,
                                confirmAction: () {
                              CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                                "track": "click",
                                "posCode": "108_083_107",
                                "ext": {"pop_type": "1", "source": ""}
                              });
                              _guideBuyVip(false, () {
                                widget.onPrint.call(
                                    _templateData?.generateCanvasJson(savePrintTemplate: true),
                                    _templateData?.dataBindingMode!,
                                    _templateData?.getCurrentPage(),
                                    _templateConfig,
                                    _getPdfPrintInfo(),
                                    context);
                              });
                            }, cancelAction: () {
                              CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                                "track": "click",
                                "posCode": "108_083_110",
                                "ext": {"pop_type": "1", "source": ""}
                              });
                            });
                          } else {
                            String title = intlanguage("app01520", "您正在试用VIP会员专属资源，请先开通VIP会员即可享受。");
                            String leftBtn = intlanguage('app00707', '我知道了');
                            String rightBtn = intlanguage('app01521', '开通VIP');
                            showNimmbotDialog(context, title: title, cancelDes: leftBtn, confirmDes: rightBtn,
                                confirmAction: () {
                              CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                                "track": "click",
                                "posCode": "108_083_107",
                                "ext": {"pop_type": "1", "source": ""}
                              });
                              _guideBuyVip(false, () {
                                widget.onPrint.call(
                                    _templateData?.generateCanvasJson(savePrintTemplate: true),
                                    _templateData?.dataBindingMode!,
                                    _templateData?.getCurrentPage(),
                                    _templateConfig,
                                    _getPdfPrintInfo(),
                                    context);
                              });
                            }, cancelAction: () {
                              CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                                "track": "click",
                                "posCode": "108_083_110",
                                "ext": {"pop_type": "1", "source": ""}
                              });
                            });
                          }
                        }
                      } else {
                        // if (!DebounceUtil.checkClick()) return;
                        widget.onPrint.call(
                            _templateData?.generateCanvasJson(savePrintTemplate: true),
                            _templateData?.dataBindingMode!,
                            _templateData?.getCurrentPage(),
                            _templateConfig,
                            _getPdfPrintInfo(),
                            context);
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 4),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SvgIcon('assets/bottom_bar/bottom_bar_print.svg'),
                          SizedBox(
                            width: 8,
                          ),
                          Text(intlanguage("app00016", "打印"), style: CanvasTheme.of(context).bottomBarTextStyle)
                        ],
                      ),
                    ),
                  )),
            ),
          ])),
    );
  }

  _etagSaveClick() async {
    bool networkConnected = CanvasPluginManager().fontPanelImpl?.isNetReachable() ?? false;
    if (!networkConnected) {
      LoadingMix.showToast(intlanguage('app100000625', '当前网络状态异常'));
      return;
    }
    _backSaveClick(isEtagSave: true);
  }

  Future<bool> _backSaveClick({bool isEtagSave = false}) async {
    final canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
    if (_isIndustryTemplate) {
      if (canvasIndustryTemplateAdapter != null &&
          canvasIndustryTemplateAdapter?.getState().sceneSelect == CanvasIndustrySelectedState.industryScenes) {
        canvasIndustryTemplateAdapter?.getController().topDialogClickListener();
        return false;
      }
      if (canvasIndustryTemplateAdapter != null &&
          canvasIndustryTemplateAdapter?.getState().isSearchTemplate &&
          _attrPanelState == AttrPanelState.highest) {
        canvasIndustryTemplateAdapter?.getState().isSearchTemplate = false;
        canvasIndustryTemplateAdapter?.getController().update(["all"]);
        return false;
      }
    }
    if (!DebounceUtil.checkClick()) return false;
    FloatingBarHelper().dismissFloatingBar();
    Future<bool> exitCanvasResult() async {
      String? canvasData = _templateData?.generateCanvasJson(savePrintTemplate: true);
      Function resetElementFocusStateFun = () {
        _focusedCanvasElements.clear();
        _elementFocusState = ElementFocusState.None;
        setState(() {});
      };
      bool showOtherSave = false;
      // if (canvasConfigIml?.currentConfigMode() == CanvasCurrentConfigMode.normal) {
      //   showOtherSave =
      //       await CanvasPluginManager().nativeMethodImpl?.isExistTemplate(templateId: _templateData?.id) ?? false;
      // } else {
      //   showOtherSave =
      //       await CanvasPluginManager().nativeMethodImpl?.isExistEtagTemplate(templateId: _templateData?.id) ?? false;
      // }

      bool result =
          await widget.onNavigatorPop?.call(context, canvasData, showOtherSave, isEtagSave, resetElementFocusStateFun) ??
              false;
      if (result == false) {
        flutter_boost.BoostNavigator.instance.pop();
      } else {
        // flutter_boost.BoostNavigator.instance.pop();
      }

      /// 释放元素图像缓存
      _templateData?.canvasElements.forEach((element) {
        element.resetImageCache();
      });
      return !result;
    }

    /// 重置编辑状态
    resetElementEditStatus();
    // SharedPreferences sp = await SharedPreferences.getInstance();
    // bool? hasShowPdfSaveTip = sp.getBool("has_show_pdf_save_tip");
    // if (hasShowPdfSaveTip == null && PdfBindInfoManager.instance.getPDFEnablePrintMaxNumber(_templateData!) > 0) {
    //   sp.setBool("has_show_pdf_save_tip", true);
    //   String title = intlanguage('app100001861', '仅保存当前页面的PDF内容，下次打印时请重新选择PDF文件。');
    //   Completer<bool> completer = Completer<bool>();
    //   showCustomDialog(context, title, "",
    //       justSureButton: true,
    //       dismissOutSideTouch: false,
    //       rightFunStr: intlanguage('app00707', '我知道了'), rightFunCall: () {
    //     completer.complete(exitCanvasResult());
    //   }, sureButtonColor: Colors.black);
    //   return completer.future;
    // } else {
    //   return exitCanvasResult();
    // }
    return exitCanvasResult();
  }

  _getPdfPrintInfo() {
    if(PdfBindInfoManager.instance.templateIsBindPdf(_templateData!)){
      return PdfBindInfoManager.instance.getALlPdfBindInfos();
    }else{
      return null;
    }
  }

  _saveClick({bool isOtherSave = true}) async {
    if (!DebounceUtil.checkClick()) return;
    onSaveResult(TemplateData value) {
      if (Platform.isAndroid) {
        String templateId;
        if (value.id.isNotEmpty && value.cloudTemplateId!.isNotEmpty && value.id == value.cloudTemplateId) {
          templateId = "";
        } else {
          templateId = value.id.length > 10 ? "" : value.id;
        }
        CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
          "track": "click",
          "posCode": "108_071_100",
          "ext": {
            "b_name": "保存",
            "temp_id": templateId.length > 10 ? "" : templateId,
            "industry_temp_id": value.cloudTemplateId,
            "tag_id": value.profile.extrain.labelId
          }
        });
      }
      _updateSaveTemplateData(value);
    }

    widget.onSave
        ?.call(_templateData?.generateCanvasJson(savePrintTemplate: true), _templateConfig, context,
            _templateData?.name ?? "",
            templateId: _templateData?.id ?? "", isOtherSave: isOtherSave)
        .then((value) {
      onSaveResult(value);
    });
    // SharedPreferences sp = await SharedPreferences.getInstance();
    // bool? hasShowPdfSaveTip = sp.getBool("has_show_pdf_save_tip");
    // if (hasShowPdfSaveTip == null && PdfBindInfoManager.instance.getPDFEnablePrintMaxNumber(_templateData!) > 0) {
    //   sp.setBool("has_show_pdf_save_tip", true);
    //   String title = intlanguage('app100001861', '仅保存当前页面的PDF内容，下次打印时请重新选择PDF文件。');
    //   showCustomDialog(context, title, "",
    //       justSureButton: true,
    //       dismissOutSideTouch: false,
    //       rightFunStr: intlanguage('app00707', '我知道了'), rightFunCall: () {
    //     widget.onSave
    //         ?.call(_templateData?.generateCanvasJson(savePrintTemplate: true), _templateConfig, context,
    //             _templateData?.name ?? "",
    //             templateId: _templateData?.id ?? "", isOtherSave: isOtherSave)
    //         .then((value) {
    //       onSaveResult(value);
    //     });
    //   }, sureButtonColor: Colors.black);
    // } else {
    //   widget.onSave
    //       ?.call(_templateData?.generateCanvasJson(savePrintTemplate: true), _templateConfig, context,
    //           _templateData?.name ?? "",
    //           templateId: _templateData?.id ?? "", isOtherSave: isOtherSave)
    //       .then((value) {
    //     onSaveResult(value);
    //   });
    // }
  }

  _updateSaveTemplateData(TemplateData value, {bool isServicesResult = false}) {
    _logger.log('模板保存成功，更新画板======');
    if (!value.existBatchBindingData() && PdfBindInfoManager.instance.templateIsBindPdf(value)) {
      value.currentPageIndex = PdfBindInfoManager.instance.currentPageIndex;
    }
    if (value.canvasElements.isNotEmpty) {
      List<ImageElement> imageList = value.canvasElements
          .where((element) => element.data is ImageElement)
          .map((e) => e.data as ImageElement)
          .toList();
      if (imageList.isNotEmpty &&
          _templateData?.canvasElements != null &&
          (_templateData?.canvasElements ?? []).isNotEmpty) {
        List<ImageElement>? tempList = _templateData?.canvasElements
            .where((element) => element.data is ImageElement)
            .map((e) => e.data as ImageElement)
            .toList();
        imageList.forEach((image) {
          image.imageData = tempList?.firstWhereOrNull((element) => element.id == image.id)?.imageData;
        });
      }
      List<DateElement> dateList = value.canvasElements
          .where((element) => element.data is DateElement)
          .map((e) => e.data as DateElement)
          .toList();
      if (dateList.isNotEmpty &&
          _templateData?.canvasElements != null &&
          (_templateData?.canvasElements ?? []).isNotEmpty) {
        List<DateElement>? tempList = _templateData?.canvasElements
            .where((element) => element.data is DateElement)
            .map((e) => e.data as DateElement)
            .toList();
        dateList.forEach((date) {
          date.time = tempList?.firstWhere((element) => element.id == date.id).time;
        });
      }
    }
    canvasKey.currentState?.clearMultiSelectAndFocused(needFresh: false);
    FloatingBarHelper().dismissFloatingBar();

    TemplateData? canvasData = _templateData;
    List<JsonElement> elements1 = value.elements;
    List<CanvasElement> elements2 = value.canvasElements;
    canvasData?.elements.forEach((canvasElement) {
      if (canvasElement is ImageElement) {
        List<ImageElement> imageElements = List<ImageElement>.from(
            elements1.where((element) => (canvasElement.id == element.id && element is ImageElement)));
        canvasElement.imageUrl = imageElements.isNotEmpty ? imageElements.first.imageUrl : '';
        canvasElement.localUrl = imageElements.isNotEmpty ? imageElements.first.localUrl : '';
      }
    });
    canvasData?.canvasElements.forEach((canvasElement) {
      if (canvasElement.data is ImageElement) {
        List<CanvasElement> imageElements = List<CanvasElement>.from(
            elements2.where((element) => (canvasElement.data.id == element.data.id && element.data is ImageElement)));
        (canvasElement.data as ImageElement).imageUrl =
            imageElements.isNotEmpty ? (imageElements.first.data as ImageElement).imageUrl : '';
        (canvasElement.data as ImageElement).localUrl =
            imageElements.isNotEmpty ? (imageElements.first.data as ImageElement).localUrl : '';
      }
    });
    value.elements = canvasData?.elements ?? [];
    value.canvasElements = canvasData?.canvasElements ?? [];
    _templateData = value;
    ExcelTransformManager.sharedInstance().templateData = _templateData;
    _elementFocusState = ElementFocusState.None;
    setState(() {});
  }

  _refreshElementColor(List<String> paperColors) {
    String value = "";
    _templateData?.paperColor = paperColors;
    for (var element in (_templateData?.canvasElements ?? [])) {
      if (element.elementType == "table") {
        TableElement tableElement = element.data as TableElement;
        List<int> lineColors = [255];
        lineColors.addAll(paperColors[tableElement.lineColorChannel].split(".").map((e) => int.parse(e)).toList());
        tableElement.lineColor = lineColors;
        List<int> contentColors = [255];
        contentColors
            .addAll(paperColors[tableElement.contentColorChannel].split(".").map((e) => int.parse(e)).toList());
        tableElement.contentColor = contentColors;
      } else {
        JsonElement jsonElement = element.data;
        List<int> colors = [255];
        colors.addAll(paperColors[jsonElement.paperColorIndex].split(".").map((e) => int.parse(e)).toList());
        jsonElement.elementColor = colors;
      }
    }
    TemplateChangedNotifier().templateDataJsonData = _templateData?.generateCanvasJson();
  }

  CanvasDataMix buildMixNbCanvasData() {
    return CanvasDataMix(
      key: canvasKey,
      templateData: _templateData,
      defaultScale: canvasDefaultScale,
      onElementFocusChanged: (List<CanvasElement> elements, int changeType) {
        setState(() {
          //&& !elements.first.data.isMaterialBoder()
          if (elements.length > 0) {
            _templateData?.swapElementOrderByFocus(elements.first);
          }
          if ((_templateData?.dataSource?.isEmpty ?? true) &&
              !PdfBindInfoManager.instance.templateIsBindPdf(_templateData!) &&
              PdfBindInfoManager.instance.currentPageIndex != 0) {
            _templateData!.currentPageIndex = 0;
            PdfBindInfoManager.instance.currentPageIndex = 0;
            _templateData!.resetImageCache();
          }

          /// 选中锁定状态的元素时，不可进入编辑状态
          if (elements.length == 1 && elements.first.data.isLock == 1) {
            defaultIndex = null;
            attributeIndex = Random().nextInt(10000);
            if (_isIndustryTemplate) {
              _attrPanelState = AttrPanelState.normal;
              _componentsPanelFolded = true;
            }
            _elementFocusState = ElementFocusState.None;
            return;
          }

          /// 体验优化
          /// 表格中 cell 正在编辑内容时，单击 cell 可以继续保持输入焦点
          if (elements.length == 1 && elements.first.data.type == ElementItemType.table) {
            TableElement tableElement = elements.first.data as TableElement;

            /// 查找选中的单元格
            List<TableCellElement> focusCells = tableElement.getFocusedCells();
            if (this._elementFocusState == ElementFocusState.Editor &&
                this._cursorCanvasElement?.data.runtimeType.toString() == 'TableCellElement') {
              if (focusCells.length == 1) {
                this._cursorCanvasElement = focusCells.first.toCanvasElement();

                if (!focusCells.first.isBindingCommodity()) {
                  this._elementFocusState = ElementFocusState.Editor;
                } else {
                  _elementFocusState = ElementFocusState.Selection;
                  _inputCursorIndex = null;
                  defaultIndex = 0;
                  attributeIndex = Random().nextInt(10000);
                }
                return;
              }
            } else {
              if (focusCells.length == 1) {
                this._cursorCanvasElement = focusCells.first.toCanvasElement();
                if (!focusCells.first.isBindingElement() && defaultIndex == 0) {
                  this._elementFocusState = ElementFocusState.Editor;
                } else {
                  _elementFocusState = ElementFocusState.Selection;
                  _inputCursorIndex = null;
                  attributeIndex = Random().nextInt(10000);
                }
                return;
              }
            }
          }

          ///当表格选中某个单元格时,并且这个单元格没有绑定数据，则弹出键盘进行写入
          if (elements.length == 1 && elements.first.data.type == ElementItemType.table) {
            TableElement tableElement = elements.first.data as TableElement;

            /// 查找选中的单元格
            List<TableCellElement> focusCells = tableElement.getFocusedCells();
            if (focusCells.length == 1) {
              if (this._focusedCanvasElements.first.elementId == elements.first.elementId) {
                if (defaultIndex == 0 &&
                    this._elementFocusState == ElementFocusState.Selection &&
                    focusCells.first.isBindingElement() == false) {
                  defaultIndex = 1;
                }
                attributeIndex = Random().nextInt(10000);
                resetAttributePanelHeight(AttrPanelState.normal);
                this._elementFocusState = ElementFocusState.Selection;
                animatedDurationMilliseconds = 0;
              } else {
                defaultIndex = 1;
                attributeIndex = Random().nextInt(10000);
                this._cursorCanvasElement = focusCells.first.toCanvasElement();
                this._elementFocusState = ElementFocusState.Selection;
                resetAttributePanelHeight(AttrPanelState.normal);
                animatedDurationMilliseconds = 0;
              }
              _focusedCanvasElements = elements;
              return;
            } else {
              _focusedCanvasElements = elements;
              defaultIndex = null;
              attributeIndex = Random().nextInt(10000);
              this._elementFocusState = ElementFocusState.Selection;
              return;
            }
          }

          /// 切换选中 or 无焦点状态
          _focusedCanvasElements = elements;
          if ((changeType == 2 || changeType == 3) && elements.length > 0) {
            defaultIndex = null;
            _elementFocusState = ElementFocusState.Selection;
            attributeIndex = Random().nextInt(10000);
            resetAttributePanelHeight(
                _elementFocusState == ElementFocusState.Editor ? AttrPanelState.normal : _attrPanelState);
          } else if (_focusedCanvasElements.length == 1) {
            CanvasElement focusedElement = _focusedCanvasElements.first;
            if (focusedElement.elementType == ElementItemType.image &&
                (focusedElement.data as ImageElement).isMaterial()) {
              ///选中状态下素材的话始终显示，因为是在tab第一位的；其他元素不显示
              if (materialDefaultIndex == 0) {
                vipVisible.value = true;
              } else {
                vipVisible.value = false;
              }
            } else if (focusedElement.elementType == ElementItemType.date) {
              ///选中状态下素材的话始终显示，因为是在tab第一位的；其他元素不显示
              if (defaultIndex == null || defaultIndex == 0) {
                vipVisible.value = true;
              } else {
                vipVisible.value = false;
              }
            }
            if (_previousfocusedCanvasElement != null &&
                focusedElement.elementType == _previousfocusedCanvasElement?.elementType) {
              if (focusedElement.elementType == ElementItemType.image) {
                if ((focusedElement.data as ImageElement).isMaterial() !=
                    (_previousfocusedCanvasElement?.data as ImageElement).isMaterial()) {
                  defaultIndex = null;
                  attributeIndex = Random().nextInt(10000);
                }
              }

              if ((_elementFocusState == ElementFocusState.Editor) &&
                  // if (((focusedElement.elementId == _previousfocusedCanvasElement.elementId &&
                  //             _elementFocusState == ElementFocusState.Selection) ||
                  //         _elementFocusState == ElementFocusState.Editor) &&
                  (_previousfocusedCanvasElement?.elementType == ElementItemType.text ||
                      _previousfocusedCanvasElement?.elementType == ElementItemType.barcode ||
                      _previousfocusedCanvasElement?.elementType == ElementItemType.qrcode)) {
                if (changeType == 1) {
                  ///处于编辑模式时拖动
                  if (_elementFocusState == ElementFocusState.Editor) {
                    _cursorCanvasElement = elements.first;
                    _elementFocusState = ElementFocusState.Editor;
                    _inputCursorIndex = null;
                  }
                  _logger.log("移动改变大小变化");
                } else {
                  if (elements.first.data.isBindingElement() || elements.first.data.isAdvanceQRCode()) {
                    _cursorCanvasElement = elements.first;
                    _elementFocusState = ElementFocusState.Selection;
                    _inputCursorIndex = null;
                    defaultIndex = 0;
                    attributeIndex = Random().nextInt(10000);
                  } else {
                    _cursorCanvasElement = elements.first;
                    _elementFocusState = ElementFocusState.Editor;
                    _inputCursorIndex = null;
                  }
                }
              } else {
                if (changeType == 1) {
                  ///处于编辑模式时拖动
                  if (_elementFocusState == ElementFocusState.Editor) {
                    _cursorCanvasElement = elements.first;
                    _elementFocusState = ElementFocusState.Editor;
                    _inputCursorIndex = null;
                  } else if (defaultIndex == 0 &&
                      _previousfocusedCanvasElement?.data.isBindingElement() !=
                          elements.first.data.isBindingElement()) {
                    defaultIndex = null;
                    attributeIndex = Random().nextInt(10000);
                    resetAttributePanelHeight(_attrPanelState);
                    _elementFocusState = ElementFocusState.Selection;
                  } else {
                    _elementFocusState = ElementFocusState.Selection; //
                  }
                  _logger.log("移动改变大小变化");
                } else {
                  if (focusedElement.elementType == ElementItemType.text &&
                      defaultIndex == 0 &&
                      _elementFocusState != ElementFocusState.Editor &&
                      _previousfocusedCanvasElement?.data.isBindingElement() == true &&
                      focusedElement.data.isBindingElement() == false) {
                    _cursorCanvasElement = elements.first;
                    _elementFocusState = ElementFocusState.Editor;
                    _inputCursorIndex = null;
                    attributeIndex = Random().nextInt(10000);
                  } else {
                    _elementFocusState = ElementFocusState.Selection; //
                  }
                  resetAttributePanelHeight(
                      _elementFocusState == ElementFocusState.Editor ? AttrPanelState.highest : _attrPanelState);
                }
              }
            } else {
              if (focusedElement.elementType == ElementItemType.image &&
                  materialDefaultIndex == 0 &&
                  (focusedElement.data as ImageElement).isMaterial()) {
                ///选中状态下素材的话始终显示，因为是在tab第一位的；其他元素不显示
                vipVisible.value = true;
              } else if (focusedElement.elementType == ElementItemType.date &&
                  (defaultIndex == 0 || defaultIndex == null)) {
                ///选中状态下素材的话始终显示，因为是在tab第一位的；其他元素不显示
                vipVisible.value = true;
              } else {
                vipVisible.value = false;
                materialDefaultIndex = 0;
              }
              defaultIndex = null;
              attributeIndex = Random().nextInt(10000);
              resetAttributePanelHeight(
                  _elementFocusState == ElementFocusState.Editor ? AttrPanelState.normal : _attrPanelState);
              _elementFocusState = ElementFocusState.Selection;
            }
            _previousfocusedCanvasElement = elements.first;
            _logger.log(
                "赋值焦点元素111Id:${_focusedCanvasElements.first.elementId}--value:${_focusedCanvasElements.first.data.value}");
          } else {
            materialDefaultIndex = 0;
            vipVisible.value = false;
            this._focusedCanvasElements = elements;
            if (this._elementFocusState == ElementFocusState.None) {
              this._elementFocusState =
                  this._focusedCanvasElements.length == 0 ? ElementFocusState.None : ElementFocusState.Selection;
              _previousfocusedCanvasElement == null;
              defaultIndex = null;
              attributeIndex = Random().nextInt(10000);
              resetAttributePanelHeight(
                  _elementFocusState == ElementFocusState.Editor ? AttrPanelState.normal : _attrPanelState);
              if (changeType == -1) {
                _refresh(() {
                  _componentsPanelFolded = !_componentsPanelFolded;
                  if (_isIndustryTemplate) {
                    _componentsPanelFolded = true;
                  }
                  _componentsPanelBottomOffsetWhenDrag = null;
                  if (_componentsPanelFolded) {
                    _attrPanelState = AttrPanelState.shortest;
                  } else {
                    _attrPanelState = AttrPanelState.normal;
                  }
                  SmartTipsNotifier().bottomOffset = _getAttrPanelHeight();
                });
              }
            } else {
              this._elementFocusState =
                  this._focusedCanvasElements.length == 0 ? ElementFocusState.None : ElementFocusState.Selection;
              _previousfocusedCanvasElement == null;
              defaultIndex = null;
              attributeIndex = Random().nextInt(10000);
              if(this._elementFocusState == ElementFocusState.None && _isIndustryTemplate){
                _attrPanelState = AttrPanelState.normal;
                _componentsPanelFolded = true;
              }
              else {
                resetAttributePanelHeight(
                    _elementFocusState == ElementFocusState.Editor ? AttrPanelState.normal : _attrPanelState);
              }
            }
          }
        });
        // FloatingBarVisibleNotifier().forceUpdate();
      },
      onElementValueEditorDisplay: (CanvasElement cursorElement, CanvasElement focusedElement,
          {bool isDoubleClick = false}) {
        setState(() {
          _logger.log("==========================onElementValueEditorDisplay");

          /// 2023/12/28 Ice_Liu 针对未锁定能输入的元素，有焦点非输入状态下再次点击元素，此时不弹出工具条，避免工具条出现又消失
          if (_elementFocusState != ElementFocusState.Editor &&
              focusedElement.data.canDoubleClickToEditValue() &&
              !focusedElement.data.isAdvanceQRCode() &&
              !focusedElement.data.hasLock()) {
            if (!isDoubleClick && FloatingBarVisibleNotifier().floatingBarVisible)
              FloatingBarVisibleNotifier().toggle();
          } else {
            FloatingBarVisibleNotifier().toggle();
          }

          /// 切换编辑状态
          if ((focusedElement.data.isAdvanceQRCode() || cursorElement.data.isAdvanceQRCode()) &&
              (isDoubleClick ?? false)) {
            canvasKey.currentState?.multiSelect = false;
            FloatingBarHelper().dismissFloatingBar();
            CanvasElement canvasElement = focusedElement ?? cursorElement;
            canvasKey.currentState?.onAdvanceQRCodePreview(
              canvasElement,
              onElementValueDisplay: () {},
            );
          } else if ((focusedElement.data.isBindingElement() || cursorElement.data.isBindingElement()) &&
              (isDoubleClick ?? false)) {
            if (focusedElement.data.isBindingCommodity() || cursorElement.data.isBindingCommodity()) {
              defaultIndex = 0;
              this._focusedCanvasElements = [focusedElement];
              this._cursorCanvasElement = cursorElement;
              this._elementFocusState = ElementFocusState.Selection;
              this._inputCursorIndex = null;
              attributeIndex = Random().nextInt(10000);
              int index = focusedElement.data.bindingColumn;
              if (focusedElement.data.type == ElementItemType.table) {
                TableElement tableElement = focusedElement.data as TableElement;

                /// 查找选中的单元格
                List<TableCellElement> focusCells = tableElement.getFocusedCells();
                index = focusCells.first.bindingColumn;
              }
              String columnIndexGoodsField = TemplateData.goodsInfnFieldName()[index];
              ImportExcelHelper.editGoodsFromDataTab(context, _templateData, columnIndexGoodsField,
                  (ExcelDataModel excelDataModel) {
                canvasKey.currentState?.handleChangeExcel(excelDataModel, commodityChangeModify: false);
                _importMultiColumn(excelDataModel, changeExcel: false, focusElement: null);
              });
            } else {
              if (focusedElement.elementType == ElementItemType.text) {
                defaultIndex = 0;
              }
              this._focusedCanvasElements = [focusedElement];
              this._cursorCanvasElement = cursorElement;
              this._elementFocusState = ElementFocusState.Editor;
              this._inputCursorIndex = null;
            }
          } else {
            if (focusedElement.elementType == ElementItemType.text) {
              defaultIndex = 0;
              this._focusedCanvasElements = [focusedElement];
              this._cursorCanvasElement = cursorElement;
              this._elementFocusState = ElementFocusState.Editor;
              this._inputCursorIndex = null;
              //图片编辑--xjp 2025年01月15日11:34:37
            } else if (focusedElement.elementType == ElementItemType.image && isDoubleClick) {
              // final croppedFile = await FlutterNativeImage.saveCropOriginImage(originPath,elementId);
              TrackUtils.sendTrackingWrapTemplateId({
                "track": "click",
                "posCode": "108_069_098",
                "ext": {
                  "b_name": "编辑",
                  "temp_id": _templateData == null ? "" : _templateData!.id,
                  'industry_temp_id': _templateData!.cloudTemplateId ?? ""
                }
              });
              AreaModel? areaModel = _getTemplateAreaModel();

              showModalBottomSheet(
                context: context,
                barrierColor: Color(0x00000000).withOpacity(0.35),
                backgroundColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
                isDismissible: false,
                enableDrag: false,
                isScrollControlled: true,
                builder: (context) {
                  return PhotoCropWidget(
                    photoFilePath: (focusedElement.data as ImageElement).localUrl,
                    elementId: (focusedElement.data as ImageElement).id,
                    aspectRatio: areaModel != null ? areaModel.width / areaModel.height : null,
                    areaModel: areaModel,
                  );
                },
              ).then((result) {
                if (result != null && result is Map) {
                  // 默认撑满标签纸
                  // getThreshold(result['filePath'], result['imageData'], result['width'], result['height'], isNeedFill: true);
                  canvasKey.currentState?.getThreshold(result['filePath'], "", result['width'], result['height'],
                      isFreedom: result['isFreedom'],
                      isNeedFill: true,
                      areaModel: areaModel,
                      canvasElements: [focusedElement]);
                }
              });

              this._focusedCanvasElements = [focusedElement];
              this._cursorCanvasElement = cursorElement;
              this._inputCursorIndex = null;
            } else {
              this._focusedCanvasElements = [focusedElement];
              this._cursorCanvasElement = cursorElement;
              this._elementFocusState = ElementFocusState.Editor;
              this._inputCursorIndex = null;
            }
          }
        });
      },
      onElementMaterialClicked: () {
        setState(() {
          this._elementFocusState = ElementFocusState.Material;
        });
      },
      onElementMaterialBoderClicked: () {
        setState(() {
          this._elementFocusState = ElementFocusState.Material_boder;
        });
      },
      labelInfo: _templateData?.getSimpleInfo() ?? "",
      onLabelChange: () {
        // CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
        //   "track": "click",
        //   "posCode": "108_211",
        //   "ext": {"source": 1}
        // });
        setState(() {
          _elementFocusState = ElementFocusState.None;
        });
        canvasKey.currentState?.clearMultiSelectAndFocused(needFresh: true);
        // 清除面板选中下标
        defaultIndex = null;
        Timer(Duration(milliseconds: 50), () {
          widget.onLabelChange
              ?.call(_templateData?.generateCanvasJson(savePrintTemplate: true), _templateConfig, context)
              ?.then((value) {
            if (value != null) {
              setState(() {
                // _templateData = value;
                parseTemplateJsonData(value.generateCanvasJson(savePrintTemplate: true), clearStack: false);
              });
            }
          });
        });
      },
      onAdvanceQRCodeInfoChanged: () {
        setState(() {
          defaultIndex = 0;
          this._focusedCanvasElements = _focusedCanvasElements;
          this._cursorCanvasElement = _cursorCanvasElement;
          this._elementFocusState = ElementFocusState.Selection;
          this._inputCursorIndex = null;
          attributeIndex = Random().nextInt(10000);
        });
      },
      onAttrPanelChange: () {
        AttributePanelRefreshNotify.notifier.value += 1;
      },
      onImageReplace: (List<CanvasElement> canvasElements) {
        TrackUtils.sendTrackingWrapTemplateId({
          "track": "click",
          "posCode": "108_069_098",
          "ext": {
            "b_name": "替换",
            "temp_id": _templateData == null ? "" : _templateData!.id,
            'industry_temp_id': _templateData!.cloudTemplateId ?? ""
          }
        });
        canvasKey.currentState?.pickImage(canvasElements: canvasElements);
      },
      onElementFocusState: () => _elementFocusState,
      rfidBind: () {
        if (FloatingBarHelper().multiSelect == false) {
          FloatingBarHelper().dismissFloatingBar();
        }
        ImportExcelHelper.handleRfidBind(context, _templateData, (excelDataModel) {
          canvasKey.currentState
              ?.handleChangeExcel(excelDataModel, commodityChangeModify: excelDataModel.isChangeHeaderInfo);
          _importMultiColumn(excelDataModel);
        });
      },
      onToLabelDetail: () {
        setState(() {
          _elementFocusState = ElementFocusState.None;
          canvasKey.currentState?.clearMultiSelectAndFocused();
          // 清除面板选中下标
          defaultIndex = null;
          FloatingBarHelper().dismissFloatingBar();
        });
      },
    );
  }

  Widget _buildToolsPanel() {
    /// 重置元素编辑状态
    this.resetElementEditStatus();
    List<Widget> panelList = [];
    if (_elementFocusState == ElementFocusState.Selection && _focusedCanvasElements.isNotEmpty) {
      /// 元素属性面板
      panelList = [
        _isRefreshDateSelected()
            ? Consumer<ElementsDataChangedNotifier>(
                builder: (BuildContext context, ElementsDataChangedNotifier value, Widget? child) {
                if (defaultIndex == null || defaultIndex == 0) {
                  vipVisible.value = true;
                }
                return _buildAttributePanel();
              })
            : _buildAttributePanel()
      ];
    } else if (_elementFocusState == ElementFocusState.Editor && _focusedCanvasElements.isNotEmpty) {
      resetAttributePanelHeight(AttrPanelState.highest);
      panelList = [_buildAttributePanel()];
    } else if (_elementFocusState == ElementFocusState.Material ||
        _elementFocusState == ElementFocusState.Material_boder) {
      /// 素材选择面板
      panelList = [_buildAttributePanel()];
    } else {
      _elementFocusState = ElementFocusState.None;
      panelList = [_buildComponentsPanel()];
    }
    return Consumer<ElementsDataChangedNotifier>(
        builder: (BuildContext context, ElementsDataChangedNotifier value, Widget? child) {
      return Stack(
        alignment: AlignmentDirectional.bottomCenter,
        children: panelList,
      );
    });
  }

  bool _isRefreshDateSelected() {
    if (_focusedCanvasElements.length != 1) {
      return false;
    }
    return (_focusedCanvasElements[0].data is DateElement);
  }

  /// 刷新智能提示的位置
  _updateSmartTipsFrame() {
    Future.delayed(Duration(milliseconds: 100), () {
      SmartTipsNotifier().bottomOffset = _getAttrPanelHeight();
    });
  }

  /// 重置元素编辑状态
  resetElementEditStatus() {
    _templateData?.canvasElements.forEach((element) {
      if (element.data is TextElement) {
        (element.data as TextElement).isEditing = false;
      }
    });
  }

  //进入裁剪面板前面，判断裁剪区域
  AreaModel? _getTemplateAreaModel() {
    AreaModel? areaModel;
    if (_templateData != null) {
      String layoutSchema = _templateData!.layoutSchema;
      List<String> supportedEditors = _templateData!.supportedEditors;
      if (supportedEditors.contains('photo') && layoutSchema.isNotEmpty) {
        //是相框，并且存在版式
        try {
          LayoutSchemaModel layoutSchemaModel = LayoutSchemaModel.fromJson(json.decode(layoutSchema));
          List<AreaModel> areas = layoutSchemaModel.areas;
          if (areas.isNotEmpty) {
            areaModel = areas.first;
          }
        } catch (e) {
          return null;
        }
      }
    }
    return areaModel;
  }

  double _maxAttributePanelHeight = 410.0 - 40.0;
  double _midAttributePanelHeight = 370.0;
  double _minAttributePanelHeight = 132.0;

  late double _attrPanelHeightWhenDragStart;
  late double _attrPanelHeightWhenDrag;

  AttrPanelState _attrPanelState = AttrPanelState.normal;

  /// 元素Key值随机值，用于刷新
  static int attributeIndex = 0;

  /// 元素面板默认选中索引
  static int? defaultIndex = null;

  /// 素材面板索引
  static int materialDefaultIndex = 0;

  double _getAttrPanelHeight() {
    // if (MediaQuery.viewInsetsOf(context).bottom != 0.0) {
    //   // 键盘高度是会变的，键盘隐藏的时候会返回0，所以为0是不赋值
    //   double keyHeight =
    //       MediaQuery.viewInsetsOf(context).bottom < 260 ? 260 : MediaQuery.viewInsetsOf(context).bottom;
    //   // keyHeight代表键盘高度，78是输入框+excel导入高度 + 10的padding + 54的快捷操作入口
    //   _maxAttributePanelHeight = keyHeight + 78 + 10 + 54;
    // }
    double attrPanelHeight = /*_attrPanelHeightWhenDrag ??*/
        (_attrPanelState == AttrPanelState.highest
            ? _maxAttributePanelHeight
            : (_attrPanelState == AttrPanelState.shortest
                ? _minAttributePanelHeight
                : _midAttributePanelHeight + safeAreaBottomHeight));
    return attrPanelHeight;
  }

  List<CanvasElement> getFakeMaterialCanvasElements() {
    List<CanvasElement> datas =
        (_elementFocusState == ElementFocusState.Material || _elementFocusState == ElementFocusState.Material_boder)
            ? [
                ImageElement.createFakeMaterial(_elementFocusState == ElementFocusState.Material ? "1" : "2")
                    .toCanvasElement()
              ]
            : this._focusedCanvasElements;
    return datas;
  }

  Widget _buildAttributePanel() {
    if (_focusedCanvasElements.length > 1) {
      vipVisible.value = false;
    }
    // 面板高度默认为45%的屏幕高度
    double attributeHeight = 0.45 * MediaQuery.sizeOf(context).height;
    double attributePaddingBottom = 0;
    if (_elementFocusState != ElementFocusState.Editor) {
      // 底部高度 = 保存/打印 + 安全区
      attributePaddingBottom = _maxElementToolbarOffset + safeAreaBottomHeight;
    }
    Widget attributePanel = ValueListenableBuilder<int>(
      builder: (BuildContext context, int value, Widget? child) {
        return Stack(
          alignment: Alignment.topCenter,
          children: [
            ElementAttributePanel(
              // key: Key(
              //     'ElementAttributePanelKey-${_focusedCanvasElements.map((e) => e.data.type).toSet().join(',')}-$attributeIndex'),
              key: Key(
                  'ElementAttributePanelKey-${_focusedCanvasElements.length} -${_focusedCanvasElements.map((e) => e.data.type).toSet().join(',')}-$attributeIndex'),
              // canvasElements: _elementFocusState == ElementFocusState.Material
              //     ? this._fakeMaterialCanvasElements
              //     : this._focusedCanvasElements,
              canvasElements: getFakeMaterialCanvasElements(),
              attrPanelState: _attrPanelState,
              onTabClick: onTabClick,
              onChooseMaterial: (CanvasElement canvasElement, MaterialItem materialItem, isFromRecent, isFromSearch) {
                // ImageElement imageElement = canvasElement.data;
                if (_elementFocusState == ElementFocusState.Material ||
                    _elementFocusState == ElementFocusState.Material_boder) {
                  canvasKey.currentState?.addMaterial(materialItem, isFromRecent,
                      _elementFocusState == ElementFocusState.Material ? false : true, isFromSearch);
                } else {
                  canvasKey.currentState?.editMaterial(_focusedCanvasElements.first, materialItem, isFromRecent);
                }
              },
              onElementValueEditorDisplay: (CanvasElement cursorElement, CanvasElement focusedElement,
                  {int? cursorIndex, bool? isDoubleClick}) {
                setState(() {
                  /// 切换编辑状态
                  if ((focusedElement.data.isAdvanceQRCode()) && (isDoubleClick ?? false)) {
                    canvasKey.currentState?.multiSelect = false;
                    FloatingBarHelper().dismissFloatingBar();
                    canvasKey.currentState?.onAdvanceQRCodePreview(
                      focusedElement,
                      onElementValueDisplay: () {},
                    );
                  } else if (focusedElement.data.isBindingElement() && (isDoubleClick ?? false)) {
                    if (focusedElement.data.isBindingCommodity()) {
                      defaultIndex = 0;
                      this._focusedCanvasElements = [focusedElement];
                      this._cursorCanvasElement = cursorElement;
                      this._elementFocusState = ElementFocusState.Selection;
                      this._inputCursorIndex = cursorIndex;
                      int index = focusedElement.data.bindingColumn;
                      String columnIndexGoodsField = TemplateData.goodsInfnFieldName()[index];
                      ImportExcelHelper.editGoodsFromDataTab(context, _templateData, columnIndexGoodsField,
                          (ExcelDataModel excelDataModel) {
                        canvasKey.currentState?.handleChangeExcel(excelDataModel, commodityChangeModify: false);
                        _importMultiColumn(excelDataModel, changeExcel: false, focusElement: focusedElement);
                      });
                    } else {
                      if (focusedElement.elementType == ElementItemType.text) {
                        defaultIndex = 0;
                      }
                      this._focusedCanvasElements = [focusedElement];
                      this._cursorCanvasElement = cursorElement;
                      this._elementFocusState = ElementFocusState.Editor;
                      this._inputCursorIndex = cursorIndex;
                    }
                  } else {
                    if (focusedElement.elementType == ElementItemType.text) {
                      defaultIndex = 0;
                    }
                    this._focusedCanvasElements = [focusedElement];
                    this._cursorCanvasElement = cursorElement;
                    this._elementFocusState = ElementFocusState.Editor;
                    this._inputCursorIndex = cursorIndex;
                  }
                });
              },
              importExcelFromElementCall: (CanvasElement canvasElement, ExcelDataModel excelData, bool isUpdataBindData,
                  {bool? changeExcel}) {
                if (changeExcel == true) {
                  canvasKey.currentState?.handleChangeExcel(excelData);
                  _importMultiColumn(excelData);
                  return true;
                } else {
                  ElementImportResult? elementImportResult;
                  if (!isUpdataBindData) {
                    elementImportResult =
                        canvasKey.currentState?.handleElementImportExcelResult(canvasElement, excelData);
                    _importMultiColumn(excelData, changeExcel: false);
                  } else {
                    canvasKey.currentState?.handleChangeExcel(excelData, commodityChangeModify: false);
                    _importMultiColumn(excelData, focusElement: canvasElement);
                  }
                  return elementImportResult == ElementImportResult.matchImport;
                }
              },
              changeDataManualInputField: (CanvasElement canvasElement, CanvasElement? tableCell) {
                canvasKey.currentState?.changeDataManualInputField(canvasElement, tableCell);
                this._focusedCanvasElements = [canvasElement];
                if (tableCell != null) {
                  this._cursorCanvasElement = tableCell;
                } else {
                  this._cursorCanvasElement = canvasElement;
                }
                _elementFocusState = ElementFocusState.Editor;
                resetAttributePanelHeight(AttrPanelState.highest);
              },
              changeColumnIndexCall: (JsonElement jsonElement, ExcelBindPair columnBindInfo, bool importWithHeader) {
                //从元素更换列
                canvasKey.currentState?.changeColumnFromElement(jsonElement, columnBindInfo, importWithHeader);
              },
              guideBuyVip: (buySuccess, {isPopLogin = false}) => _guideBuyVip(isPopLogin, () {
                buySuccess();
              }),
              onGraphElementTypeChanged: _handleGraphElementTypeChanged,
              closeAttributePanel: closeAttributePannel,
              defaultIndex: defaultIndex,
              isNeedAttTabs:
                  !(_attrPanelState == AttrPanelState.highest && _elementFocusState == ElementFocusState.Editor),
              height: attributeHeight,
              paddingBottom: attributePaddingBottom,
            ),

            ///手势滑块
            /*IgnorePointer(
                ignoring: !_isCanDrag,
                child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onVerticalDragDown: (DragDownDetails details) {
                    _attrPanelHeightWhenDragStart = _getAttrPanelHeight();
                  },
                  onVerticalDragUpdate: (DragUpdateDetails details) {
                    _refresh(() {
                      _attrPanelHeightWhenDrag = _attrPanelHeightWhenDragStart - details.localPosition.dy;

                      if (_attrPanelHeightWhenDrag < _minAttributePanelHeight - 20) {
                        _attrPanelHeightWhenDrag = _minAttributePanelHeight - 20;
                      }
                    });
                  },
                  onVerticalDragCancel: () {
                    _refresh(() {
                      _attrPanelHeightWhenDrag = null;
                    });

                    _updateSmartTipsFrame();
                  },
                  onVerticalDragEnd: (DragEndDetails details) {
                    _refresh(() {
                      /// 滑动过半判断最终状态
                      if (_attrPanelHeightWhenDrag >
                          (_midAttributePanelHeight + (_maxAttributePanelHeight - _midAttributePanelHeight) / 2)) {
                        _attrPanelState = AttrPanelState.highest;
                        _componentsPanelFolded = false;
                      } else if (_attrPanelHeightWhenDrag <
                          (_midAttributePanelHeight - (_midAttributePanelHeight - _minAttributePanelHeight) / 2)) {
                        _attrPanelState = AttrPanelState.shortest;
                        _componentsPanelFolded = true;
                      } else {
                        _attrPanelState = AttrPanelState.normal;
                        _componentsPanelFolded = false;
                      }

                      _attrPanelHeightWhenDrag = null;
                    });

                    _updateSmartTipsFrame();
                  },
                  onTap: () {
                    // _refresh(() {
                    //   // closeAttributePannel();

                    // });
                    // setState(() {
                    //   _attrPanelHeightWhenDrag = null;
                    //   if (_elementFocusState == ElementFocusState.Editor) {
                    //     defaultIndex = null;
                    //     _attrPanelState = AttrPanelState.shortest;
                    //     attributeIndex = Random().nextInt(10000);
                    //     this._elementFocusState = ElementFocusState.Selection;
                    //     _componentsPanelFolded = true;
                    //   } else if (_attrPanelState == AttrPanelState.normal ||
                    //       _attrPanelState == AttrPanelState.highest) {
                    //     _attrPanelState = AttrPanelState.shortest;
                    //     attributeIndex = Random().nextInt(10000);
                    //     this._elementFocusState = ElementFocusState.Selection;
                    //     _componentsPanelFolded = true;
                    //   } else {
                    //     _attrPanelState = AttrPanelState.normal;
                    //     attributeIndex = Random().nextInt(10000);
                    //     this._elementFocusState = ElementFocusState.Selection;
                    //     _componentsPanelFolded = false;
                    //   }
                    //   animatedDurationMilliseconds = 200;
                    // });
                  },
                  child: Container(
                    height: 15,
                    // padding: EdgeInsets.only(top: 3),
                    child: Center(
                      child: SvgIcon(
                        _attrPanelState == AttrPanelState.normal
                            ? 'assets/common/panel_fold_handle.svg'
                            : 'assets/common/panel_fold.svg',
                        useDefaultColor: false,
                      ),
                    ),
                  ),
                )),*/
          ],
        );
      },
      valueListenable: AttributePanelRefreshNotify.notifier,
    );

    // 高面板且处于编辑状态
    if (_attrPanelState == AttrPanelState.highest && _elementFocusState == ElementFocusState.Editor) {
      Widget highPanel = Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(child: attributePanel),
          Container(
            height: 8,
            color: CanvasTheme.of(context).backgroundLightColor,
          ),
          _buildInputToolBar()
        ],
      );
      return _handleHighLowPanel(highPanel);
    }
    Widget wrapperAttributePanel = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildBuyVipGuide(),
        Flexible(child: attributePanel),
      ],
    );
    return _handleHighLowPanel(wrapperAttributePanel);
  }

  ///如果是导入多列的话  切换到导入数据tab
  _importMultiColumn(ExcelDataModel excelData, {bool changeExcel = true, CanvasElement? focusElement}) {
    List<ExcelBindPair> bindPairs = excelData.bindPairs.where((element) => element.open).toList() ?? [];
    if ((bindPairs.length > 1 || changeExcel)) {
      setState(() {
        final canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
        if (canvasConfigIml?.currentConfigMode() == CanvasCurrentConfigMode.etag) {
          _currentComponentsPanelTabIndex = 1;
        } else {
          _currentComponentsPanelTabIndex = 2;
        }
        _elementFocusState = ElementFocusState.None;
      });
    }
  }

  bool _isShowBuyVipPanel() {
    ///用户已经开通vip，并且没有过期
    if (CanvasUserCenter().vipType == VIPType.valid) {
      return false;
    }
//
    ///元素选中状态
    if (_elementFocusState == ElementFocusState.Selection || _elementFocusState == ElementFocusState.Editor) {
      if (_focusedCanvasElements.isNotEmpty) {
        for (int i = 0; i < _focusedCanvasElements.length; i++) {
          CanvasElement element = _focusedCanvasElements[i];
          if (element.data is DateElement) {
            DateElement dateElement = element.data as DateElement;
            if ((dateElement.fontCode?.isNotEmpty == true && FontManager().isFontVip(dateElement.fontCode!)) ||
                dateElement.dateIsRefresh == 1) {
              return true;
            }
          } else if (element.data is TableElement) {
            TableElement tableElement = element.data as TableElement;
            return tableElement.hasFocusedCellVipSource();
          } else if (element.data.hasVipSource()) {
            return true;
          }
        }
      }
    }
    return false;
  }

  Widget _buildBuyVipGuide() {
    return Consumer<ElementsDataChangedNotifier>(
        builder: (BuildContext context, ElementsDataChangedNotifier value, Widget? child) {
      return Selector<CanvasUserCenter, bool>(
        builder: (context, isVip, child) => ValueListenableBuilder(
            valueListenable: vipVisible,
            builder: (_, bool isVisible, __) {
              _logger.log('--------isVisible--$isVisible-------------isVip--$isVip-----');
              return Visibility(
                visible: !isVip && isVisible && _isShowBuyVipPanel(),
                child: child!,
              );
            }),
        selector: (context, canvasCenter) => canvasCenter.isVip,
        child: Container(
          height: 36,
          decoration: new BoxDecoration(
              borderRadius: BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10)),
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  ThemeColor.COLOR_FF766F,
                  ThemeColor.COLOR_FA3A31,
                ],
              )),
          child: Container(
            margin: EdgeInsets.only(left: 16, right: 16),
            child: Row(
              children: [
                SvgIcon(
                  'assets/common/vip_corner.svg',
                  width: 23,
                  height: 15,
                  useDefaultColor: false,
                ),
                const SizedBox(
                  width: 8,
                ),
                Expanded(
                    child: Text(
                  intlanguage('app01513', '成为VIP，立享专属资源'),
                  style: TextStyle(color: Colors.white, fontSize: 13, fontWeight: FontWeight.w600),
                )),
                const SizedBox(
                  width: 8,
                ),
                GestureDetector(
                  onTap: () {
                    CanvasPluginManager()
                        .nativeMethodImpl
                        ?.sendTrackingToNative({"track": "click", "posCode": "108_077", "ext": {}});
                    _guideBuyVip(false, () {
                      setState(() {});
                    });
                  },
                  behavior: HitTestBehavior.opaque,
                  child: Container(
                    height: 28,
                    //constraints: BoxConstraints(minWidth: 72),
                    padding: EdgeInsetsDirectional.fromSTEB(14, 0, 14, 0),
                    decoration: new BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(14)),
                        gradient: LinearGradient(colors: [Color(0xFFFDDAA7), Color(0xFFFFFFFF).withOpacity(0.7)])),
                    child: Center(
                      child: Text(
                        CanvasUserCenter().vipType == VIPType.expired
                            ? intlanguage('app01515', '续费')
                            : intlanguage('app01514', '成为VIP'),
                        style: TextStyle(color: Color(0xFFD74608), fontSize: 13, fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      );
    });
  }

  _guideBuyVip(bool confirm, Function buySuccess) {
    CanvasUserCenter().interface.openVipPurchasePage(isPopLogin: confirm).then((value) {
      if (value) {
        buySuccess();
      }
    });
  }

  _handleGraphElementTypeChanged() {
    FloatingBarHelper().graphElementTypeRefreshFloatingBarIfNecessary();
  }

  ///处理高低面板
  Widget _handleHighLowPanel(Widget childWidget) {
    return StatefulBuilder(
      builder: (BuildContext context, void Function(void Function()) setState) {
        return NotificationListener<AttributePanelStateNotification>(
            onNotification: (notification) {
              setState(() {
                _attrPanelState = notification.panelState;
                // 序列号中键盘弹起事件发送的高面板
                _isCanDrag = _attrPanelState == AttrPanelState.highest ? false : true;
                _updateSmartTipsFrame();
              });
              return true;
            },
            child: childWidget);
      },
    );
  }

  /// 设置元素编辑状态
  void setElementEditingStatus(CanvasElement? element) {
    if (element != null && (element.data is TextElement) && (element.data as TextElement).isSupportBoxStyle()) {
      (element.data as TextElement).isEditing = true;
    }
  }

  /// 键盘输入框及工具栏
  Widget _buildInputToolBar() {
    setElementEditingStatus(_cursorCanvasElement);
    return Align(
      alignment: Alignment.bottomCenter,
      child: KeyboardAttachable(
        backgroundColor: CanvasTheme.of(context).backgroundLightColor!,
        child: KeyboardSensitiveEditorWidget(
          key: Key('ElementEditor_${_cursorCanvasElement?.elementId ?? ""}'),
          templateData: _templateData,
          canvasElement: _cursorCanvasElement!,
          parentContext: canvasKey.currentState?.context,
          inputCursorIndex: _inputCursorIndex,
          onComplete: (_) {
            // setState(() {
            //   _elementFocusState = ElementFocusState.Selection;
            //   _attrPanelState = AttrPanelState.normal;
            // });
          },
          onFocusPreviousClicked: () {
            final previousFocusElement = _getPreviousCursorFocusElement(_cursorCanvasElement);
            if (previousFocusElement != null) {
              canvasKey.currentState?.switchCursorFocus(previousFocusElement);
            }
          },
          onFocusNextClicked: () {
            final nextFocusElement = _getNextCursorFocusElement(_cursorCanvasElement);
            if (nextFocusElement != null) {
              canvasKey.currentState?.switchCursorFocus(nextFocusElement);
            }
          },
          // onExcelFileChange: (ExcelDataModel excelData, bool isTableCell) {
          //   canvasKey.currentState.handleImportExcelResult(excelData, isFromTableCell: isTableCell);
          //   _importMultiColumn(excelData);
          // },
          importExcelFromElementCall: (CanvasElement canvasElement, ExcelDataModel excelData, bool isUpdataBindData,
              {bool? changeExcel}) {
            ElementImportResult elementImportResult;
            if (!isUpdataBindData) {
              elementImportResult = canvasKey.currentState!.handleElementImportExcelResult(canvasElement, excelData);
              _importMultiColumn(excelData, changeExcel: false);
            } else {
              elementImportResult = canvasKey.currentState!.handlUpdateBindDataResult(canvasElement, excelData);
            }
            return elementImportResult == ElementImportResult.matchImport;
          },
        ),
        keyboardChangedCallback: (keyboardOpen) {
          _logger.log("键盘收起");
          if (!keyboardOpen) {
            if (_focusedCanvasElements.isEmpty) return;
            setState(() {
              if (_cursorCanvasElement?.elementType == ElementItemType.text) {
                defaultIndex = 1;
                if (_focusedCanvasElements.first.elementType == ElementItemType.table &&
                    (_cursorCanvasElement?.data.isBindingElement() ?? false)) {
                  defaultIndex = 0;
                }
              } else if (_cursorCanvasElement?.elementType == ElementItemType.table) {
                defaultIndex = 2;
              } else {
                defaultIndex = 0;
              }
              attributeIndex = Random().nextInt(10000);
              resetAttributePanelHeight(AttrPanelState.normal);
              _elementFocusState = ElementFocusState.Selection;
            });
          } else {
            FloatingBarHelper().dismissFloatingBar();
            setState(() {
              defaultIndex = 0;
              vipVisible.value = false;
              attributeIndex = Random().nextInt(10000);
              resetAttributePanelHeight(AttrPanelState.highest);
              _updateSmartTipsFrame();
            });
          }
        },
      ),
    );
  }

  /// 底部组件面板页码
  int _currentComponentsPanelTabIndex = 0;

  /// 组件 GridView 页码
  int _currentElementCreatePageIndex = 0;

  get _isIndustryTemplate => toolbarTitles[_currentComponentsPanelTabIndex] == intlanguage("app100000755", "模板");

  //每一行放置的item的个数
  int _itemCount = 4;
  bool isCable = false;

  /// 组件面板是否收起
  bool _componentsPanelFolded = false;

  _refresh(VoidCallback voidCallback) {
    setState(voidCallback);
  }

  /// 数据、小程序面板展开 关闭时底部偏移
  final double _minComponentsPanelBottomOffset = -122;
  final double _maxComponentsPanelBottomOffset = 0;

  double _industryTemplateHeight = 660;
  final double elementToolPanelheight = 220;

  /// 创建元素面板展开 关闭时底部偏移
  final double _minElementToolbarBottomOffset = -268;
  final double _maxElementToolbarOffset = 48;
  static double safeAreaBottomHeight = 0;

  /// 面板的约束
  BoxConstraints panelConstraints = BoxConstraints.expand();

  double _getComponentsPanelBottomOffset(BuildContext context) {
    if (MediaQuery.paddingOf(context).bottom > 0 && safeAreaBottomHeight == 0) {
      safeAreaBottomHeight = MediaQuery.paddingOf(context).bottom;
    }
    double maxOffset = (_componentsPanelFolded
        ? (_currentComponentsPanelTabIndex == 0
            ? _minElementToolbarBottomOffset + _maxElementToolbarOffset + safeAreaBottomHeight + 15
            : _minElementToolbarBottomOffset + _maxElementToolbarOffset + safeAreaBottomHeight + 15)
        : (_currentComponentsPanelTabIndex == 0
                ? safeAreaBottomHeight
                : _maxComponentsPanelBottomOffset + safeAreaBottomHeight) +
            15);
    if (_componentsPanelBottomOffsetWhenDrag != null) {
      return _componentsPanelBottomOffsetWhenDrag! > maxOffset ? maxOffset : _componentsPanelBottomOffsetWhenDrag!;
    } else {
      return maxOffset;
    }
  }

  double _getIndustryTemplateBottomOffset() {
    if (MediaQuery.paddingOf(context).bottom > 0 && safeAreaBottomHeight == 0) {
      safeAreaBottomHeight = MediaQuery.paddingOf(context).bottom;
    }

    var offset = 0.0;
    // 计算当前约束下的最大高度
    _industryTemplateHeight = min(
        MediaQuery.sizeOf(context).height -
            64 -
            MediaQuery.paddingOf(context).bottom -
            MediaQuery.paddingOf(context).top,
        _industryTemplateHeight);
    if (_componentsPanelFolded) {
      if (_attrPanelState == AttrPanelState.highest || _attrPanelState == AttrPanelState.normal) {
        offset = -(_industryTemplateHeight - elementToolPanelheight - _maxElementToolbarOffset * 2) + 15;
      } else {
        offset = -_industryTemplateHeight + _maxElementToolbarOffset * 2 + 15;
      }
    } else {
      if (_attrPanelState == AttrPanelState.normal) {
        offset = -_industryTemplateHeight;
      } else {
        offset = 0;
      }
    }
    return offset;
  }

  double? _componentsPanelBottomOffsetWhenDragStart;
  double? _componentsPanelBottomOffsetWhenDrag;
  double? _componentsPanelBottomYOffsetWhenDragStart;
  double _componentsPanelBottomYOffsetDetal = 0;
  IStateManagementAdapter? canvasIndustryTemplateAdapter;

  /// 组件面板
  Widget _buildComponentsPanel() {
    StateSetter stateSetter;
    return AnimatedPositioned(
      curve: Curves.easeOut,
      bottom: _isIndustryTemplate ? _getIndustryTemplateBottomOffset() : _getComponentsPanelBottomOffset(context),
      duration: Duration(milliseconds: _componentsPanelBottomOffsetWhenDrag != null ? 0 : 300),
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onVerticalDragDown: (DragDownDetails details) {
          _componentsPanelBottomOffsetWhenDragStart = _getComponentsPanelBottomOffset(context);

          /// 记录手势开始y值
          _componentsPanelBottomYOffsetWhenDragStart = details.localPosition.dy;
        },
        onVerticalDragUpdate: (DragUpdateDetails details) {
          /// 更新移动距离
          _componentsPanelBottomYOffsetDetal = details.localPosition.dy - _componentsPanelBottomYOffsetWhenDragStart!;
        },
        onVerticalDragCancel: () {
          _componentsPanelBottomYOffsetWhenDragStart = null;
        },
        onVerticalDragEnd: (DragEndDetails details) {
          var mode = _attrPanelState;
          var folded = false;
          if (_componentsPanelBottomYOffsetDetal > _maxComponentsPanelBottomOffset) {
            _isTransparent = false;

            /// 下滑
            if (_isIndustryTemplate && mode == AttrPanelState.highest) {
              mode = AttrPanelState.normal;
              folded = true;

              if (_isIndustryTemplate) {
                if (canvasIndustryTemplateAdapter != null &&
                    canvasIndustryTemplateAdapter?.getState().sceneSelect ==
                        CanvasIndustrySelectedState.industryScenes) {
                  canvasIndustryTemplateAdapter?.getController().topDialogClickListener();
                }
              }
              canvasIndustryTemplateAdapter?.getController().unFocus();
            } else {
              mode = AttrPanelState.shortest;
              folded = true;
            }
          } else if (_componentsPanelBottomYOffsetDetal < -_maxComponentsPanelBottomOffset) {
            /// 上滑
            mode = AttrPanelState.normal;
            if (_isIndustryTemplate) {
              mode = AttrPanelState.highest;
              Future.delayed(Duration(milliseconds: 300), () {
                _isTransparent = true;
                canvasIndustryTemplateAdapter?.getController().refreshSize(_templateData);
                _refresh(() {});
              });
            }
            folded = false;
          }
          _refresh(() {
            _componentsPanelFolded = folded;
            _componentsPanelBottomOffsetWhenDrag = null;
            SmartTipsNotifier().bottomOffset = _getAttrPanelHeight();
            _attrPanelState = mode;
          });
        },
        child: SizedBox(
          width: MediaQuery.sizeOf(context).width,
          child: StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
            stateSetter = setState;
            return Column(
              children: [
                /// 手势滑块
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    _refresh(() {
                      _componentsPanelFolded = !_componentsPanelFolded;
                      _componentsPanelBottomOffsetWhenDrag = null;
                      if (_isIndustryTemplate && _attrPanelState == AttrPanelState.normal) {
                        _componentsPanelFolded = true;
                      }
                      if (_componentsPanelFolded) {
                        _attrPanelState = AttrPanelState.shortest;
                      } else {
                        _attrPanelState = AttrPanelState.normal;
                        if (_isIndustryTemplate) {
                          _attrPanelState = AttrPanelState.highest;
                          Future.delayed(Duration(milliseconds: 300), () {
                            _isTransparent = true;
                            canvasIndustryTemplateAdapter?.getController().refreshSize(_templateData);
                            // _refresh(() {});
                          });
                        }
                      }
                      SmartTipsNotifier().bottomOffset = _getAttrPanelHeight();
                    });
                  },
                  child: _isIndustryTemplate && _attrPanelState == AttrPanelState.highest
                      ? Container()
                      : Container(
                          padding: EdgeInsets.fromLTRB(50, 20, 50, 10),
                          child: Center(
                            child: SvgIcon(
                              'assets/common/panel_fold.svg',
                              useDefaultColor: false,
                            ),
                          ),
                        ),
                ),

                /// 组件面板 tabs
                _buildComponentsPanelHeader(context, stateSetter),
                AnimatedContainer(
                  curve: Curves.easeOut,
                  duration: Duration(milliseconds: 300), // 设置动画持续时间
                  height: _isIndustryTemplate
                      // 最大高度不可溢出父级
                      ? (_industryTemplateHeight + safeAreaBottomHeight - _maxElementToolbarOffset)
                      : elementToolPanelheight + _maxElementToolbarOffset,
                  child: _switchComponentsPanelTabIndex(stateSetter),
                ),
              ],
            );
          }),
        ),
      ),
    );
  }

  _switchComponentsPanelTabIndex(StateSetter stateSetter) {
    var industryTemplateHeight = (_industryTemplateHeight + safeAreaBottomHeight - _maxElementToolbarOffset);
    if (_currentComponentsPanelTabIndex == 0) {
      return createElementToolbar();
    }
    if (_isIndustryTemplate) {
      return CanvasIndustryTemplatePage(
        canvasIndustryTemplateAdapter!,
        panelState: _attrPanelState,
        industryTemplateHeight: industryTemplateHeight,
        onTap: (Map<String, dynamic>? selectInfo) {
          _replaceIndustryTemplate(selectInfo);
        },
        onRefreshTap: () {
          _attrPanelState = AttrPanelState.highest;
          _refresh(() {
            _componentsPanelBottomOffsetWhenDrag = null;
          });
          _componentsPanelFolded = false;
          stateSetter.call(() {});
          SmartTipsNotifier().bottomOffset = _getAttrPanelHeight();
        },
        onPullDownTap: () {
          _attrPanelState = AttrPanelState.normal;
          canvasIndustryTemplateAdapter?.getController().unFocus();

          _refresh(() {
            _componentsPanelBottomOffsetWhenDrag = null;
          });
          _componentsPanelFolded = true;
          stateSetter.call(() {});
          SmartTipsNotifier().bottomOffset = _getAttrPanelHeight();
        },
      );
    } else {
      return createDataBindingToolbar(context);
    }
  }

  List<String> toolbarTitles = [
    intlanguage("app100000756", "添加元素"),
    intlanguage("app100000755", "模板"),
    intlanguage("app100000757", "数据")
  ];

  /// 组件面板头
  Container _buildComponentsPanelHeader(BuildContext context, StateSetter stateSetter) {
    double toolbarHeaderHeight = 49.0;
    double borderRadius = 16.0;

    return Container(
      // color: CanvasTheme.of(context).backgroundColor, // 底色
      width: MediaQuery.sizeOf(context).width,
      height: toolbarHeaderHeight,
      child: Stack(
        children: [
          Positioned(
            top: 10,
            width: MediaQuery.sizeOf(context).width,
            // height: 40,
            child: Container(
                decoration: BoxDecoration(
                  color: CanvasTheme.of(context).backgroundColor, // 底色
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10)),
                  boxShadow: [
                    BoxShadow(
                      blurRadius: 5, //阴影范围
                      spreadRadius: 0.1, //阴影浓度
                      offset: Offset(0, -2.0),
                      color: Colors.grey.withOpacity(0.15), //阴影颜色
                    ),
                  ],
                ),
                // height: 40,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: toolbarTitles
                      .map((e) => Expanded(
                            child: InkWell(
                              onTap: () {
                                _componentsPanelFolded = false;
                                _attrPanelState = AttrPanelState.normal;
                                _isTransparent = false;
                                _currentComponentsPanelTabIndex = toolbarTitles.indexOf(e);
                                if (_isIndustryTemplate) {
                                  _attrPanelState = AttrPanelState.normal;
                                  _componentsPanelFolded = true;
                                  Future.delayed(Duration(milliseconds: 250), () {
                                    _isTransparent = true;
                                    canvasIndustryTemplateAdapter?.getController().refreshSize(_templateData);
                                    // _refresh(() {});
                                  });
                                }
                                stateSetter.call(() {});
                                _refresh(() {
                                  _componentsPanelBottomOffsetWhenDrag = null;
                                });
                                SmartTipsNotifier().bottomOffset = _getAttrPanelHeight();
                                String posCode = "";
                                if (toolbarTitles.length == 2) {
                                  if (_currentComponentsPanelTabIndex == 0) {
                                    posCode = "108_208";
                                  } else if (_currentComponentsPanelTabIndex == 1) {
                                    posCode = "108_209";
                                  }
                                } else {
                                  if (_currentComponentsPanelTabIndex == 0) {
                                    posCode = "108_208";
                                  } else if (_currentComponentsPanelTabIndex == 2) {
                                    posCode = "108_209";
                                  } else if (_currentComponentsPanelTabIndex == 1) {
                                    posCode = "108_309";
                                  }
                                }
                                if (posCode.isNotEmpty) {
                                  CanvasPluginManager()
                                      .nativeMethodImpl
                                      ?.sendTrackingToNative({"track": "click", "posCode": posCode, "ext": {}});
                                }
                              },
                              child: Container(
                                height: 40,
                                alignment: Alignment.center,
                                padding: const EdgeInsets.symmetric(horizontal: 0.0, vertical: 0.0),
                                child: Container(
                                  width: MediaQuery.sizeOf(context).width / toolbarTitles.length - 20,
                                  child: Text(e,
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      textAlign: TextAlign.center,
                                      style: CanvasTheme.of(context).toolbarTitleTextStyle),
                                ),
                              ),
                            ),
                          ))
                      .toList(),
                )),
          ),
          Positioned(
              top: 0,
              left: Directionality.of(context) == TextDirection.rtl
                  ? (MediaQuery.sizeOf(context).width -
                      (MediaQuery.sizeOf(context).width / toolbarTitles.length * (_currentComponentsPanelTabIndex + 1)))
                  : (MediaQuery.sizeOf(context).width / toolbarTitles.length * _currentComponentsPanelTabIndex),
              child: GestureDetector(
                onTap: () {
                  if (_isIndustryTemplate && _attrPanelState == AttrPanelState.highest) {
                    return;
                  }
                  if (_isIndustryTemplate) {
                    Future.delayed(Duration(milliseconds: 250), () {
                      _isTransparent = true;
                      canvasIndustryTemplateAdapter?.getController().refreshSize(_templateData);
                      _refresh(() {});
                    });
                  }
                  if (_attrPanelState == AttrPanelState.normal) {
                    _componentsPanelFolded = true;
                    _attrPanelState = AttrPanelState.shortest;
                  } else {
                    _componentsPanelFolded = false;
                    _attrPanelState = _isIndustryTemplate ? AttrPanelState.highest : AttrPanelState.normal;
                  }
                  stateSetter.call(() {});
                  _refresh(() {
                    _componentsPanelBottomOffsetWhenDrag = null;
                  });
                  SmartTipsNotifier().bottomOffset = _getAttrPanelHeight();
                },
                child: Container(
                    width: MediaQuery.sizeOf(context).width / toolbarTitles.length,
                    decoration: BoxDecoration(
                      color: CanvasTheme.of(context).backgroundLightColor, // 底色
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(borderRadius), topRight: Radius.circular(borderRadius)),
                      boxShadow: [
                        BoxShadow(
                          blurRadius: 4, //阴影范围
                          spreadRadius: 0.0, //阴影浓度
                          offset: Offset(0, -1.0),
                          color: Colors.black.withOpacity(0.05), //阴影颜色
                        ),
                      ],
                    ),
                    height: toolbarHeaderHeight,
                    child: Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(toolbarTitles[_currentComponentsPanelTabIndex],
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.center,
                              style: CanvasTheme.of(context).toolbarTitleHighlightTextStyle),
                          SizedBox(
                            height: 4,
                          ),
                          Container(
                            width: 16,
                            height: 4,
                            decoration: BoxDecoration(
                                color: CanvasTheme.of(context).highlightColor, // 底色
                                borderRadius: BorderRadius.all(Radius.circular(2))),
                          )
                        ],
                      ),
                    )),
              )),
          Positioned(
            top: toolbarHeaderHeight - borderRadius,
            left: Directionality.of(context) == TextDirection.rtl
                ? (MediaQuery.sizeOf(context).width -
                    (MediaQuery.sizeOf(context).width / toolbarTitles.length * (_currentComponentsPanelTabIndex + 1) +
                        borderRadius))
                : (MediaQuery.sizeOf(context).width / toolbarTitles.length * _currentComponentsPanelTabIndex -
                    borderRadius),
            child: Container(
              width: borderRadius,
              decoration: BoxDecoration(
                color: CanvasTheme.of(context).backgroundLightColor,
              ),
              height: borderRadius,
              child: Container(
                width: borderRadius,
                decoration: BoxDecoration(
                  color: CanvasTheme.of(context).backgroundColor,
                  borderRadius: BorderRadius.only(
                    bottomRight: Radius.circular(borderRadius),
                  ),
                  boxShadow: [],
                ),
                height: borderRadius,
              ),
            ),
          ),
          Positioned(
            top: toolbarHeaderHeight - borderRadius,
            left: Directionality.of(context) == TextDirection.rtl
                ? (MediaQuery.sizeOf(context).width -
                    (MediaQuery.sizeOf(context).width / toolbarTitles.length * _currentComponentsPanelTabIndex))
                : (MediaQuery.sizeOf(context).width / toolbarTitles.length * (_currentComponentsPanelTabIndex + 1)),
            child: Container(
              width: borderRadius,
              decoration: BoxDecoration(
                color: CanvasTheme.of(context).backgroundLightColor,
              ),
              height: borderRadius,
              child: Container(
                width: borderRadius,
                decoration: BoxDecoration(
                  color: CanvasTheme.of(context).backgroundColor,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(borderRadius),
                  ),
                  boxShadow: [],
                ),
                height: borderRadius,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取前一个可以获取输入光标的组件
  CanvasElement? _getPreviousCursorFocusElement(CanvasElement? currentCursorFocusElement) {
    if (currentCursorFocusElement == null) {
      return null;
    }

    List<CanvasElement>? cursorFocusableElements = _getCursorFocusableElements();
    if (cursorFocusableElements == null || cursorFocusableElements.length < 2) {
      return null;
    }

    int index = cursorFocusableElements.indexOf(currentCursorFocusElement);
    if (index < 0) {
      return null;
    }
    if (index == 0) {
      return cursorFocusableElements.last;
    } else {
      return cursorFocusableElements[index - 1];
    }
  }

  /// 获取后一个可以获取输入光标的组件
  CanvasElement? _getNextCursorFocusElement(CanvasElement? currentCursorFocusElement) {
    if (currentCursorFocusElement == null) {
      return null;
    }

    List<CanvasElement>? cursorFocusableElements = _getCursorFocusableElements();
    if (cursorFocusableElements == null || cursorFocusableElements.length < 2) {
      return null;
    }

    int index = cursorFocusableElements.indexOf(currentCursorFocusElement);
    if (index < 0) {
      return null;
    }
    if (index == cursorFocusableElements.length - 1) {
      return cursorFocusableElements.first;
    } else {
      return cursorFocusableElements[index + 1];
    }
  }

  /// 可获取输入焦点的元素列表
  List<CanvasElement>? _getCursorFocusableElements() {
    final cursorFocusableElements =
        _templateData?.canvasElements.where((element) => element.data.canDoubleClickToEditValue()).toList();
    cursorFocusableElements?.sort((CanvasElement a, CanvasElement b) {
      /// y 轴优先, 中心点 从左上横向排序到右下
      return ((a.data.y + a.data.height / 2) * 100000 + (a.data.x + a.data.width / 2))
          .compareTo((b.data.y + b.data.height / 2) * 100000 + (b.data.x + b.data.width / 2));
    });

    cursorFocusableElements?.forEach((element) {
      _logger.log('(x, y) ${element.data.x} ${element.data.y}, ${element.data.value}');
    });
    return cursorFocusableElements;
  }

  Widget createDataBindingToolbar(BuildContext context) {
    return TemplateDataBindingToolbar(
      templateData: _templateData,
      onDataBindingModeSwitch: (String dataBindingMode) {
        return canvasKey.currentState!.onDataBindingModeSwitch(dataBindingMode);
      },
      onExcelDataModelChanged: (ExcelDataModel? excelDataModel) {
        // canvasKey.currentState.handleDataTabImportExcelResult(excelDataModel);
        canvasKey.currentState
            ?.handleChangeExcel(excelDataModel, commodityChangeModify: excelDataModel!.isChangeHeaderInfo);
        _importMultiColumn(excelDataModel!);
      },
      onGoodsModelChanged: (List<GoodsModel> goodsList) {
        // canvasKey.currentState.goodsImport(goodsList);
      },
      onGoodsFieldsChanged: (List<String> goodsFields) {
        canvasKey.currentState?.onGoodsFieldsChanged(goodsFields);
      },
      onSelectRowChanged: (List<Range> ranges) {
        canvasKey.currentState?.handleSelectRow(ranges);
      },
      clearImportDataSource: () {
        canvasKey.currentState?.clearImportDataSource();
      },
    );
  }

  createElementToolbar() {
    List<Widget> items = getToolkitButtons();
    int pageSize = items.length ~/ 8;
    if (items.length % 8 != 0) {
      pageSize += 1;
    }
    return Container(
        width: MediaQuery.sizeOf(context).width,
        decoration: BoxDecoration(gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0.0, 0.2], //[渐变起始点, 渐变结束点]
            //渐变颜色[始点颜色, 结束颜色]
            colors: [CanvasTheme.of(context).backgroundLightColor!, CanvasTheme.of(context).backgroundColor!])),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              height: 20,
            ),
            SizedBox(
              width: MediaQuery.sizeOf(context).width,
              height: 188,
              child: PageView.builder(
                controller: PageController(initialPage: _currentElementCreatePageIndex),
                itemCount: pageSize,
                itemBuilder: (BuildContext context, int index) {
                  return GridView.count(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    crossAxisSpacing: 0.0,
                    mainAxisSpacing: 9.0,
                    crossAxisCount: _itemCount,
                    childAspectRatio: MediaQuery.sizeOf(context).width / 4 / 88,
                    children: items.getRange(index * 8, min((index + 1) * 8, items.length)).toList(),
                  );
                },
                onPageChanged: (index) {
                  setState(() {
                    _currentElementCreatePageIndex = index;
                  });
                },
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(pageSize, (i) {
                return Container(
                  margin: EdgeInsets.symmetric(horizontal: 5),
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _currentElementCreatePageIndex == i ? Color(0xFF404040) : Colors.black.withOpacity(0.13)),
                );
              }).toList(),
            ),
            SizedBox(
              height: 10,
            )
          ],
        ));
  }

  /// "形状","表格","流水号","Excel导入","扫描","时间","素材","线条","智能识别" 等
  List<Widget> getToolkitButtons() {
    /// 内置按钮
    List<ToolkitButton> builtInToolkitButtons = ToolkitButtons.createBuiltInToolkitButtons(_templateData);

    /// 传入的扩展按钮
    List<ToolkitButton> assistToolkitButtons = ToolkitButtons.createAssistToolkitButtons(_templateData);
    assistToolkitButtons.addAll(widget.toolkitButtons ?? []);

    /// index 数值倒序
    assistToolkitButtons.sort((ToolkitButton a, ToolkitButton b) {
      return (b.index ?? 0).compareTo(a.index ?? 0);
    });

    /// 拼接
    List<ToolkitButton> toolkitButtons = []
      ..addAll(builtInToolkitButtons)
      ..addAll(assistToolkitButtons);
    double bgWidth = 58;

    /// 添加用户自定义按钮
    List<Widget> widgets = [];
    toolkitButtons.forEach((toolkitButton) {
      Column column = Column(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Container(width: MediaQuery.sizeOf(context).width / _itemCount),
              SizedBox(
                width: bgWidth,
                height: bgWidth,
                child: SvgPicture.asset('assets/element/icon/element_icon_bg.svg',
                    package: 'niimbot_flutter_canvas', color: null, fit: BoxFit.none),
              ),
              Positioned.directional(
                textDirection: Directionality.of(context),
                top: 0,
                end: MediaQuery.sizeOf(context).width / _itemCount / 2 - bgWidth / 2 - 2, // 右侧需要超出2像素
                child: Offstage(
                  offstage: !(toolkitButton.markIcon?.startsWith('http') == true),
                  child: Container(
                    height: 14,
                    child: CachedNetworkImage(
                      fadeInDuration: Duration(milliseconds: 0),
                      fadeOutDuration: Duration(milliseconds: 0),
                      imageUrl: toolkitButton.markIcon ?? '',
                      height: 14,
                      fit: BoxFit.fitHeight,
                      errorWidget: (_, __, ___) => Container(),
                    ),
                  ),
                ),
              ),
              SvgPicture.asset(toolkitButton.icon, fit: BoxFit.none),
              Positioned.fill(
                child: Container(
                  child: Material(
                      color: Colors.transparent,
                      child: GestureDetector(
                        child: Container(
                          child: Stack(
                            children: [
                              Positioned.fill(
                                  child: GestureDetector(
                                onTap: () {
                                  if (toolkitButton.type == ToolkitButtonType.builtIn) {
                                    if (toolkitButton.identifier == 'date') {
                                      DateElementHelper.recordTimeFormat("HH:mm:ss");
                                    } else if (toolkitButton.identifier == 'material') {
                                      MaterialBadgeHelper.onMaterialTap(toolkitButton.markIcon);
                                    }else if (toolkitButton.identifier == 'border') {
                                      BadgeHelper.onBadgeTap(toolkitButton.markIcon,BadgeHelper.BORDER_BADGE_KEY);
                                    }
                                    canvasKey.currentState?.addElementBox(toolkitButton.identifier!);
                                  } else {
                                    if (toolkitButton.type == ToolkitButtonType.elementCreate) {
                                      _onElementCreateEvent(toolkitButton);
                                    } else if (toolkitButton.type == ToolkitButtonType.action) {
                                      if (toolkitButton.remark == 'pdf') {
                                        MaterialBadgeHelper.setBadgeNewOpen(false);
                                        _refresh(() {});
                                        canvasKey.currentState?.onImportPdfImageElement(() {});
                                      } else {
                                        _onElementActionEvent(toolkitButton);
                                      }
                                    }
                                  }
                                  CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                                    "track": "click",
                                    "posCode": "108_070_099",
                                    "ext": {
                                      "b_name": toolkitButton.tagName,
                                      "temp_id": _templateData?.id ?? "",
                                      "industry_temp_id": _templateData?.cloudTemplateId ?? ""
                                    }
                                  });
                                },
                                child: Container(
                                  color: Colors.transparent,
                                ),
                              )),
                            ],
                          ),
                        ),
                      )),
                ),
              )
            ],
          ),
          SizedBox(
            height: 4,
          ),
          Expanded(
            child: Text(
              toolkitButton.title,
              style: CanvasTheme.of(context).toolbarTextStyle,
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      );
      widgets.add(column);
    });
    return widgets;
  }

  void _onElementCreateEvent(ToolkitButton element) {
    double? templateWidth = _templateData?.width?.toDouble();
    double? templateHeight = _templateData?.height?.toDouble();
    if (element.identifier == ElementItemType.qrcode && element.codeType != null) {
      if(element.codeType == AdvanceQRCodeType.liveCode){
        canvasKey.currentState?.onCreateAdvanceQRCodeElement(element.codeType!, () {
          TemplateChangedNotifier().templateDataJsonData = _templateData?.generateCanvasJson();
        });
      }else{
        canvasKey.currentState?.onCreateAdvanceQRCodeFormElement(element.codeType!, () {
          TemplateChangedNotifier().templateDataJsonData = _templateData?.generateCanvasJson();
        });
      }
    } else {
      element.elementCreateEvent
          ?.call(context, element, Size(templateWidth ?? 0, templateHeight ?? 0), DisplayUtil.pxRatio)
          ?.then((value) {
        if (value != null && value is Map && value.isNotEmpty) {
          List<ElementCreateRequiredModel> elementCreateList = value["data"];
          bool ocrClick = value.containsKey("ocrClick") ? value["ocrClick"] : false;
          if ((elementCreateList ?? []).length > 0) {
            // 是否为OCR，OCR默认选中第一个元素
            bool isSelectedFirst =
                (element.type == ToolkitButtonType.elementCreate && element.index == 2) ? true : false;
            canvasKey.currentState
                ?.addElementFromBusiness(elementCreateList, ocrClick: ocrClick, isSelectedFirst: isSelectedFirst);
          }
        }
      });
    }
  }

  void _onElementActionEvent(ToolkitButton element) async {
    double templateWidth = _templateData?.width?.toDouble() ?? 0;
    double templateHeight = _templateData?.height?.toDouble() ?? 0;
    Map<dynamic, dynamic>? value =
        await element.actionEvent?.call(context, element, Size(templateWidth, templateHeight));

    if (value != null && value is Map && value.isNotEmpty) {
      if (element.identifier == ElementItemType.photoPrint) {
        TemplateData templateData = value[OcrUtils.KEY_OCR_COMPOSE_RESULT] as TemplateData;
        CanvasHelper.printChannelCode = PrintChannelCode.aiLayout;
        CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({"track": "show", "posCode": "124_337"});

        ///替换画布内容
        canvasKey.currentState?.clearCanvas();

        //处理画布旋转逻辑
        _templateData?.width = templateData.width;
        _templateData?.height = templateData.height;
        if (templateData.rotate != 0) {
          _templateData?.canvasRotate = (_templateData?.canvasRotate ?? 0) + 90;
          if (_templateData?.canvasRotate == 360) {
            _templateData?.canvasRotate = 0;
          }
          _templateData?.rotate = (_templateData?.rotate ?? 0) + 270;
          if (_templateData!.rotate >= 360) {
            _templateData?.rotate = (_templateData?.rotate ?? 0) - 360;
          }
        }
        _templateData?.elements = templateData.elements;
        Iterable<CanvasElement>? elementsList = [];
        if (_templateData?.elements != null) {
          elementsList = _templateData?.elements.map((e) => e.toCanvasElement()) ?? [];
        }

        ExcelTransformManager.sharedInstance().templateData = _templateData;
        _resetPanelState();
        _componentsPanelFolded = true;
        _attrPanelState = AttrPanelState.shortest;
        canvasKey.currentState?.addCanvasElements(elementsList.toList(), focused: false);
      }
    }
  }

  void onTabClick(int position, String text) {
    defaultIndex = position;
    _logger.log("_visibilitySubscription position: $position");
    if (_focusedCanvasElements.first.elementType == ElementItemType.image &&
        (_focusedCanvasElements.first.data as ImageElement).isMaterial()) {
      materialDefaultIndex = position;
    }
    if (text == intlanguage('app01005', '字体') ||
        text == intlanguage("app00835", "素材") ||
        text == intlanguage("app00010", "时间")) {
      vipVisible.value = true;
    } else {
      vipVisible.value = false;
    }
    if (position == 0) {
      setState(() {
        /// 切换编辑状态\
        CanvasElement cursorCanvasElement = _focusedCanvasElements.first;
        bool tableWriteTabVisible = false;
        if (cursorCanvasElement.elementType == ElementItemType.table) {
          tableWriteTabVisible = _focusedCanvasElements.length == 1 &&
              (cursorCanvasElement.data as TableElement).getFocusedCells().length == 1;
        }

        if (tableWriteTabVisible ||
            (cursorCanvasElement.elementType == ElementItemType.text && _focusedCanvasElements.length == 1)) {
          if (tableWriteTabVisible) {
            _cursorCanvasElement = (cursorCanvasElement.data as TableElement).getFocusedCells().first.toCanvasElement();
          } else {
            _cursorCanvasElement = _focusedCanvasElements.first;
          }
          if (cursorCanvasElement.data.isBindingElement() == false &&
              _cursorCanvasElement?.data.isBindingElement() == false) {
            _elementFocusState = ElementFocusState.Editor;
            resetAttributePanelHeight(AttrPanelState.highest);
          } else {
            resetAttributePanelHeight(AttrPanelState.normal);
          }
          animatedDurationMilliseconds = 0;
          FloatingBarVisibleNotifier().forceDismiss();
        } else {
          resetAttributePanelHeight(AttrPanelState.normal);
          animatedDurationMilliseconds = 0;
        }
      });
    } else {
      setState(() {
        /// 切换编辑状态
        resetAttributePanelHeight(AttrPanelState.normal);
        _elementFocusState = ElementFocusState.Selection;
        // FloatingBarVisibleNotifier().floatingBarVisible = true;
      });
    }
  }

  ///检查一键排版功能开关
  void _onCheckSmartTypesettingOpen(List<CanvasElement>? elements, int changeType) {
    print("=====================onCheckSmart: $_showSmartTypesetting");
    if (_showSmartTypesetting) {
      return;
    }
    widget
        .onCheckSmartTypesettingOpen(context, _templateData?.generateCanvasJson(savePrintTemplate: true))
        .then((value) {
      if (value.length == 2) {
        setState(() {
          _showSmartTypesetting = value[0];
          _showSmartTypesettingGuide = value[1];
          if (_showSmartTypesettingGuide) {
            Future.delayed(Duration(milliseconds: 500), () {
              _smartTypesettingGuide(_magicKey.currentContext!);
            });
          } else {
            //如果引导弹框还在的话，则弹出,popover是用根栈push的，所以用根栈pop
            if (CanvasHelper.hasPopSmartGuide) {
              CanvasHelper.hasPopSmartGuide = false;
              // Navigator.of(context, rootNavigator: true).popUntil((route) {
              //   if (route.settings.name == '/') {
              //     return true;
              //   }
              //   return false;
              // });
              Navigator.of(context).pop();
            }
          }
        });
      }
    });
  }

  void _smartTypesettingGuide(BuildContext context) {
    CanvasHelper.hasPopSmartGuide = true;
    showPopover(
        context: context,
        rootNavigator: false,
        bodyBuilder: (context) {
          return typesettingOverLayer(context);
        },
        shadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 55,
            spreadRadius: 0.1,
          ),
        ],
        radius: 12,
        direction: PopoverDirection.bottom,
        width: 273,
        arrowHeight: 10,
        arrowWidth: 18,
        contentDyOffset: 10,
        barrierColor: Colors.transparent,
        routeSettings: RouteSettings(name: "smartPopover"),
        onPop: () {
          CanvasHelper.hasPopSmartGuide = false;
        });
  }

  void _onClickSmartTypesetting(BuildContext context) {
    bool networkConnected = CanvasPluginManager().fontPanelImpl?.isNetReachable() ?? false;
    if (!networkConnected) {
      LoadingMix.showToast(intlanguage('app100000625', '当前网络状态异常'));
      return;
    }
    widget.onSmartTypesetting
        .call(_templateData?.generateCanvasJson(savePrintTemplate: true), _templateData?.dataBindingMode!,
            _templateData?.getCurrentPage(), _templateConfig, context)
        .then((value) {
      switch (value) {
        // case 1:
        // ///通过画板上的内容一键排版
        // ///1.检查画板文本个数是否小于3个
        // ///2.检查画板是否有表格元素
        //   widget.onSmartTypesettingFresh?.call(
        //       _templateData.generateCanvasJson(savePrintTemplate: true),
        //       _templateData.dataBindingMode,
        //       _templateData.getCurrentPage(),
        //       _templateConfig,
        //       context)?.then((value){
        //     if (value != null) {
        //       _templateData = value;
        //       _templateData.resetCanvasElement();
        //       TemplateChangedNotifier().templateDataJsonData = value.generateCanvasJson();
        //       _resetPanelState();
        //     }
        //   });
        //   break;
        case 1:

          ///通过OCR识别的内容一键排版
          ToolkitButtons.handleOcrClicked(context, fromSmartTypesetting: true).then((value) {
            if (value == null || !(value is Map) || !value.containsKey("data")) {
              return;
            }
            List<ElementCreateRequiredModel> elementCreateList = value["data"];
            if ((elementCreateList ?? []).length <= 0) {
              ///展示OCR识别错误
              return;
            }

            TemplateData? smartData = TemplateData.generateSmartJson(
                _templateData?.generateCanvasJson(savePrintTemplate: true) ?? "", elementCreateList);

            ///通过画板上的内容一键排版
            widget.onSmartTypesettingFresh
                .call(smartData.generateCanvasJson(savePrintTemplate: true), smartData.dataBindingMode!,
                    smartData.getCurrentPage(), _templateConfig, context)
                .then((value) {
              if (value != null) {
                if (value.width != 0 && value.height != 0) {
                  canvasKey.currentState?.clearCanvas();
                  _templateData = value;
                  ExcelTransformManager.sharedInstance().templateData = _templateData;
                  _templateData?.resetCanvasElement();
                  _resetPanelState();
                  _componentsPanelFolded = true;
                  _attrPanelState = AttrPanelState.shortest;
                  canvasKey.currentState?.addCanvasElements(_templateData?.canvasElements ?? [], focused: false);
                }
              } else {
                _resetPanelState();
                _componentsPanelFolded = true;
                _attrPanelState = AttrPanelState.shortest;
                List<CanvasElement> createElements = canvasKey.currentState!.generateCanvasElements(elementCreateList);
                Future.delayed(Duration(milliseconds: 200), () {
                  canvasKey.currentState?.addCanvasElements(createElements, focused: false);
                });

                String title = intlanguage("app100001213", "智能排版遇到问题");
                String message = intlanguage("app100001214", "已将识别结果添加到了画布，你可以稍后继续尝试智能排版");
                String rightFunStr = intlanguage("app01584", "关闭");
                CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({"track": "show", "posCode": "108_234"});
                showCustomDialog(context, title, message, justSureButton: true, rightFunStr: rightFunStr);
              }
            });
          });
          break;
      }
    });
  }
}

// 魔法棒事件拓展
extension MagicWand on CanvasBoxFrameState {
  Widget typesettingOverLayer(context) {
    return Container(
      width: 273,
      padding: EdgeInsetsDirectional.fromSTEB(18, 13, 10, 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            intlanguage('app100001211', '新功能：一键排版'),
            style: TextStyle(color: ThemeColor.mainTitle, fontSize: 15, fontWeight: FontWeight.w600),
          ),
          const SizedBox(
            height: 1,
          ),
          Text(
            intlanguage('app100001212', '可识别并提取图片上的文字来智能一键排版。'),
            style: TextStyle(color: ThemeColor.subtitle, fontSize: 14, fontWeight: FontWeight.w400),
          ),
        ],
      ),
    );
  }
}
