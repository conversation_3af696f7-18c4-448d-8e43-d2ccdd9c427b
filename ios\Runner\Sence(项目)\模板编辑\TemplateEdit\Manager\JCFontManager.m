//
//  JCFontManager.m
//  Runner
//
//  Created by xingling xu on 2020/2/19.
//  Copyright © 2020 Jingchen Technology Co.Ltd . All rights reserved.
//
#import "JCElementModel+Transfer.h"
#import "JCFontManager.h"

static NSString *const cn_type = @"zh";
static NSString *const en_type = @"en";
@interface JCFontManager()
@property (nonatomic, strong) NSMutableArray *downloadedFonts;
@property (nonatomic, copy) XYNormalBlock complateBlock;
@property (nonatomic, copy) NSArray *allFonts;
@end

@implementation JCFontManager

+ (id)sharedManager {
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [[JCFontManager alloc] init];
    });
    return instance;
}

- (instancetype)init{
    self = [super init];
    if(self){
        [self resetAllFontArry];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loginChanged:) name:LOGIN_CHANGED object:nil];
    }
    return self;
}

- (void)loginChanged:(NSNotification *)notification{
    if(xy_isLogin){
        NSString *graphqlStr = @"query findUserFonts { findUserFonts { id name path publishTime status isVip fileName code type thumbnailUrl type agreementUrl agreementName  advertisementImageUrl copyright classifyId classifyName priority usageDatetime} }";
        [@{@"needDesRequest":@"1"} jc_graphQLRequestWith:graphqlStr hud:nil graphQLType:@"query" Success:^(__kindof YTKBaseRequest *request, NSDictionary *requestDic) {
            NSArray *requestDiclArr = requestDic[@"findUserFonts"];
            NSMutableArray *models = [NSMutableArray array];
            NSMutableArray *fontCodes = [NSMutableArray array];
            for (NSDictionary *fontInfoDic in requestDiclArr) {
                JCFontModel *fontModel = [[JCFontModel alloc] initWithDictionary:fontInfoDic error:nil];
                fontModel.dbId = [NSString jk_UUID];
                fontModel.userId = m_userModel.userid;
                if(fontModel != nil){
                    [models addObject:fontModel];
                }
                [fontCodes addObject:fontModel.fontCode];
            }
            NSMutableArray *temp = [NSMutableArray arrayWithArray:models];
            NSArray *hasDownLoadFontArr = [self getHasDownloadFont];
            for (NSInteger index = 0 ; index < hasDownLoadFontArr.count ; index++) {
                JCFontModel *fontModel = hasDownLoadFontArr[index];
                fontModel.dbId = [NSString jk_UUID];
                fontModel.userId = m_userModel.userid;
                NSString *whereStr = [NSString stringWithFormat:@"where fontCode = '%@'",fontModel.fontCode];
                NSArray *fontArr = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_USER_FONTINFO dicOrModel:[JCFontModel class] whereFormat:whereStr];
                if(![fontCodes containsObject:fontModel.fontCode] && fontArr.count > 0){
                    long long currentTimesT = [XYTool getNowTimeTimestamp].longLongValue;
                    currentTimesT = currentTimesT + index * 1000;
                    NSString *currentTime = [XYTool getCurrentTimesWithTimeStamp:currentTimesT];
                    fontModel.usageDatetime = currentTime;
                    [temp addObject:fontModel];
                    [fontCodes addObject:fontModel.fontCode];
                }
            }
            [[JCFMDB shareDatabase:DB_DEFAULT] jc_deleteTable:TABLE_USER_FONTINFO whereFormat:@""];
            [[JCFMDB shareDatabase:DB_DEFAULT] jc_insertTable:TABLE_USER_FONTINFO dicOrModelArray:temp];
            [self uploadUserFontWithCacheInfo:temp updateInfo:^(id x) {
                [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_FONT_MEGER_SUCCESS object:nil];
            }];
        } failure:^(NSString *msg, NSString *responsCode) {
            if(responsCode.integerValue == 160000){
                [[JCFMDB shareDatabase:DB_DEFAULT] jc_deleteTable:TABLE_USER_FONTINFO whereFormat:@""];
            }
        }];
        
    }
}

- (void)downLoadFontRequestWithModel:(JCFontModel*)model finishBlock:(void(^)(BOOL isscuess))finishBlock {
    if (NETWORK_STATE_ERROR) {
        if (finishBlock) {
            finishBlock(false);
        }
        return;
    }
    NSString *saveUrlString = [NSString stringWithFormat:@"%@/font/%@.%@",DocumentsFontPath,model.fontCode,model.url.pathExtension];
    if ([model.url rangeOfString:ServerURL].location != NSNotFound) {
        model.url = [model.url stringByReplacingOccurrencesOfString:ServerURL withString:@""];
    }
    [@{} xy_downLoadFileWithUrlString:model.url savePath:saveUrlString tag:model.fontCode Success:^(__kindof YTKBaseRequest *request, id requestModel) {
        if (finishBlock) {
            NSString *whereString = [NSString stringWithFormat:@"where fontCode = '%@'",model.fontCode];
            NSArray *fonts = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_FONTINFO dicOrModel:[JCFontModel class] whereFormat:whereString];
            for (JCFontModel *obj in fonts) {
                obj.isDownload = @"1";
                [[JCFMDB shareDatabase:DB_DEFAULT] jc_updateTable:TABLE_FONTINFO dicOrModel:obj whereFormat:whereString];
            }
            finishBlock(true);
        }
    } failure:^(NSString *msg, id requestModel) {
        if (finishBlock) {
            finishBlock(false);
        }
    } downloadBlock:^(__kindof YTKBaseRequest *request) {
        
    }];
    
}

- (void)downLoadFontRequestWithModel:(JCFontModel*)model finishBlock:(void(^)(BOOL isscuess))finishBlock pressgress:(XYBlock)pregressBlock downloadBlock:(XYBlock)requestBlock{
    if (NETWORK_STATE_ERROR) {
        if (finishBlock) {
            finishBlock(false);
        }
        return;
    }
    NSString *saveUrlString = [NSString stringWithFormat:@"%@/font/%@.%@",DocumentsFontPath,model.fontCode,model.url.pathExtension];
    if ([model.url rangeOfString:ServerURL].location != NSNotFound) {
        model.url = [model.url stringByReplacingOccurrencesOfString:ServerURL withString:@""];
    }
    [@{} xy_downLoadFileWithUrlString:model.url savePath:saveUrlString tag:model.fontCode Success:^(__kindof YTKBaseRequest *request, id requestModel) {
        if (finishBlock) {
            finishBlock(true);
        }
    } failure:^(NSString *msg, id model) {
        if (finishBlock) {
            finishBlock(false);
        }
    } downloadBlock:^(__kindof YTKBaseRequest *request) {
        requestBlock(request);
    } pressgress:^(id x) {
        pregressBlock(x);
    }];
}

- (void)requestFontWithParams:(NSDictionary *)params cacheInfo:(void(^)(NSArray *enFonts,NSArray *cnFonts))cacheInfo updateInfo:(void(^)(NSArray *enFonts,NSArray *cnFonts))updateInfo {
    NSString *language = XY_JC_LANGUAGE_REAL;
    NSArray *fonts = self.allFonts;
    NSMutableArray *temp = [NSMutableArray arrayWithArray:fonts];
    /** 回调缓存数据提高显示速度 */
    if (cacheInfo) {
        cacheInfo(nil,temp);
    }
    if(fonts.count > 0) return;
    [params java_postWithModelType:[JCFontModel class] Path:J_font_resource_list hud:nil Success:^(__kindof YTKBaseRequest *request, NSArray *models) {
        //迁移原来数据中的最后使用信息到新的数据库中
        for (JCFontModel *oldModel in fonts) {
            for (JCFontModel *newModel in models) {
                if ([oldModel.xyid isEqualToString:newModel.xyid]) {
                    newModel.lastUseTimeStamp = oldModel.lastUseTimeStamp;
                    continue;
                }
            }
        }
        /** 不区分中文英文 */
        NSMutableArray *temp = [NSMutableArray arrayWithArray:fonts];
        for (JCFontModel *fontModel in models) {
            BOOL isNeedAdd = YES;
            for (JCFontModel *tempFontModel in temp) {
                if([fontModel.fontCode isEqualToString:tempFontModel.fontCode]){
                    isNeedAdd = NO;
                }
            }
            if(isNeedAdd){
                [temp addObject:fontModel];
            }
        }
        if (updateInfo) {
            updateInfo(nil,temp);
        }
        JCFontModel *model1 = [JCFontModel defaultCnFontWithLang:language];
        NSMutableArray *fontArr= [@[model1] arrayByAddingObjectsFromArray:models].mutableCopy;
        [[JCFMDB shareDatabase:DB_DEFAULT] jc_deleteTable:TABLE_FONTINFO whereFormat:@""];
        [[JCFMDB shareDatabase:DB_DEFAULT] jc_insertTable:TABLE_FONTINFO dicOrModelArray:fontArr];
        [self resetAllFontArry];
    }failure:^(NSString *msg, NSString *responsCode) {
        if(responsCode.integerValue == 160000){
            [[JCFMDB shareDatabase:DB_DEFAULT] jc_deleteTable:TABLE_FONTINFO whereFormat:@""];
            if (updateInfo) {
                updateInfo(nil,@[].mutableCopy);
            }
        }
    }];
}

- (void)resetAllFontArry{
    self.allFonts = [[JCFMDB shareDatabase:DB_DEFAULT]  jc_lookupTable:TABLE_FONTINFO dicOrModel:[JCFontModel class] whereFormat:nil];
}
- (void)requestFontWithCacheInfo:(void(^)(NSArray *languageFonts))cacheInfo params:(NSDictionary *)params updateInfo:(void(^)(NSArray *languageFonts))updateInfo{
    NSString *classifyId = [params objectForKey:@"classifyId"];
    NSString *whereStr = [NSString stringWithFormat:@"where classifyId = '%@'",classifyId];
    NSArray *fonts = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_FONTINFO dicOrModel:[JCFontModel class] whereFormat:whereStr];
    NSMutableArray *temp = [NSMutableArray arrayWithArray:fonts];
    /** 回调缓存数据提高显示速度 */
    if (cacheInfo) {
        cacheInfo(temp);
    }
    [params java_postWithModelType:[JCFontModel class] Path:J_font_resource_list hud:nil Success:^(__kindof YTKBaseRequest *request, NSArray *models) {
        //迁移原来数据中的最后使用信息到新的数据库中
        NSMutableArray *needInsertArr = [NSMutableArray array];
        
        NSArray *allFonts = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_FONTINFO dicOrModel:[JCFontModel class] whereFormat:nil];
        
        for (JCFontModel *newModel in models) {
            // Match showSystemName from all fonts
            for (JCFontModel *allFont in allFonts) {
                if ([newModel.fontCode isEqualToString:allFont.fontCode]) {
                    newModel.showSystemName = allFont.showSystemName;
                    break;
                }
            }
            [needInsertArr addObject:newModel];
        }
        [[JCFMDB shareDatabase:DB_DEFAULT] jc_deleteTable:TABLE_FONTINFO whereFormat:whereStr];
        [[JCFMDB shareDatabase:DB_DEFAULT] jc_insertTable:TABLE_FONTINFO dicOrModelArray:needInsertArr];
        [self resetAllFontArry];
        if (updateInfo) {
            updateInfo(needInsertArr);
        }
    } failure:^(NSString *msg, NSString *responsCode) {
        if(responsCode.integerValue == 160000){
            [[JCFMDB shareDatabase:DB_DEFAULT] jc_deleteTable:TABLE_FONTINFO whereFormat:@""];
            if (updateInfo) {
                updateInfo(@[].mutableCopy);
            }
        }
    }];
}

- (void)requestUserFontWithCacheInfo:(void(^)(NSArray *languageFonts))cacheInfo params:(NSDictionary *)params isNeedRequest:(BOOL)needRequest updateInfo:(void(^)(NSArray *languageFonts))updateInfo{
    NSString *userId = [params objectForKey:@"userId"];
    NSString *whereStr = [NSString stringWithFormat:@"where userId = '%@' and deleted != '1'",userId];
    NSArray *fonts = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_USER_FONTINFO dicOrModel:[JCFontModel class] whereFormat:whereStr];
    NSMutableArray *fontCodes = [NSMutableArray array];
    for (JCFontModel *fontModel in fonts) {
        [fontCodes addObject:fontModel.fontCode];
    }
    NSMutableArray *temp = [NSMutableArray arrayWithArray:fonts];
    NSArray *hasDownLoadFontArr = [self getHasDownloadFont];
    for (NSInteger index = 0 ; index < hasDownLoadFontArr.count ; index++) {
        JCFontModel *fontModel = hasDownLoadFontArr[index];
        fontModel.dbId = [NSString jk_UUID];
        fontModel.userId = userId;
        NSString *whereStr = [NSString stringWithFormat:@"where fontCode = '%@'",fontModel.fontCode];
        NSArray *fontArr = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_USER_FONTINFO dicOrModel:[JCFontModel class] whereFormat:whereStr];
        if(![fontCodes containsObject:fontModel.fontCode] && fontArr.count > 0){
            long long currentTimesT = [XYTool getNowTimeTimestamp].longLongValue;
            currentTimesT = currentTimesT + index * 1000;
            NSString *currentTime = [XYTool getCurrentTimesWithTimeStamp:currentTimesT];
            fontModel.usageDatetime = currentTime;
            [temp addObject:fontModel];
            [fontCodes addObject:fontModel.fontCode];
            [[JCFMDB shareDatabase:DB_DEFAULT] jc_updateTable:TABLE_USER_FONTINFO dicOrModel:fontModel whereFormat:whereStr];
        }
    }
    
    // 匹配赋值 showSystemName
    NSArray *allFonts = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_FONTINFO dicOrModel:[JCFontModel class] whereFormat:nil];
    for (JCFontModel *userFont in temp) {
        for (JCFontModel *allFont in allFonts) {
            if ([userFont.fontCode isEqualToString:allFont.fontCode]) {
                userFont.showSystemName = allFont.showSystemName;
                break;
            }
        }
    }
    
    /** 回调缓存数据提高显示速度 */
    if (cacheInfo) {
        cacheInfo(temp);
    }
    if(!needRequest) return;
    NSString *graphqlStr = @"query findUserFonts { findUserFonts { id name path publishTime status isVip fileName code type thumbnailUrl type agreementUrl agreementName  advertisementImageUrl copyright classifyId classifyName priority usageDatetime} }";
    [@{@"needDesRequest":@"1"} jc_graphQLRequestWith:graphqlStr hud:nil graphQLType:@"query" Success:^(__kindof YTKBaseRequest *request, NSDictionary *requestDic) {
        NSArray *requestDiclArr = requestDic[@"findUserFonts"];
        for (NSDictionary *fontInfoDic in requestDiclArr) {
            JCFontModel *fontModel = [[JCFontModel alloc] initWithDictionary:fontInfoDic error:nil];
            fontModel.dbId = [NSString jk_UUID];
            fontModel.userId = m_userModel.userid;
            if(![fontCodes containsObject:fontModel.fontCode]){
                // 匹配赋值 showSystemName
                for (JCFontModel *allFont in allFonts) {
                    if ([fontModel.fontCode isEqualToString:allFont.fontCode]) {
                        fontModel.showSystemName = allFont.showSystemName;
                        break;
                    }
                }
                
                [temp addObject:fontModel];
                [fontCodes addObject:fontModel.fontCode];
                NSString *whereStr = [NSString stringWithFormat:@"where fontCode = '%@'",fontModel.fontCode];
                NSArray *fonts = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_USER_FONTINFO dicOrModel:[JCFontModel class] whereFormat:whereStr];
                if(fonts.count != 0){
                    [[JCFMDB shareDatabase:DB_DEFAULT] jc_updateTable:TABLE_USER_FONTINFO dicOrModel:fontModel whereFormat:whereStr];
                }else{
                    [[JCFMDB shareDatabase:DB_DEFAULT] jc_insertTable:TABLE_USER_FONTINFO dicOrModel:fontModel];
                }
            }
        }
        if (updateInfo) {
            updateInfo(temp);
        }
    }failure:^(NSString *msg, NSString *responsCode) {
        if(responsCode.integerValue == 160000){
            [[JCFMDB shareDatabase:DB_DEFAULT] jc_deleteTable:TABLE_FONTINFO whereFormat:@""];
            if (updateInfo) {
                updateInfo(@{}.mutableCopy);
            }
        }
    }];
}

- (NSArray *)getHasDownloadFont{
    NSMutableArray *myFonts = @[].mutableCopy;
    NSMutableArray *fontTagArr = @[].mutableCopy;
    NSArray *hadDownLoadFontCode = [JCFontModel getDownLoadFontCodeArr];
    for (NSString *fontCode in hadDownLoadFontCode) {
        NSLog(@"222");
        NSString *whereStr = @"";
        if(jc_is_support_vip){
            whereStr = [NSString stringWithFormat:@"where fontCode = '%@'",fontCode];
        }else{
            whereStr = [NSString stringWithFormat:@"where fontCode = '%@' and isVip != '1'",fontCode];
        }
        if([fontCode isEqualToString:@"ZT001"]){
            whereStr = [NSString stringWithFormat:@"%@ and language = '%@'",whereStr,XY_JC_LANGUAGE_REAL];
        }
        NSArray *fonts = [[JCFMDB shareDatabase:DB_DEFAULT]  jc_lookupTable:TABLE_FONTINFO dicOrModel:[JCFontModel class] whereFormat:whereStr];
        if(fonts.count > 0){
            for (JCFontModel *obj in fonts) {
                NSString *fontTag = [NSString stringWithFormat:@"%@_%@",obj.name,obj.fontCode];
                if(![fontTagArr containsObject:fontTag]){
                    [myFonts addObject:obj];
                    [fontTagArr addObject:fontTag];
                }
            }
        }
    }
    return myFonts;
}

- (void)uploadUserFontWithCacheInfo:(NSArray *)localFonts updateInfo:(XYBlock)complate{
    NSMutableArray *fontInfoArr = [NSMutableArray array];
    for (JCFontModel *fontModel in localFonts) {
        if(![fontModel.fontCode isEqualToString:@""] || [fontModel.userId isEqualToString:m_userModel.userId]){
            NSMutableDictionary *fontInfoDic = [NSMutableDictionary dictionary];
            [fontInfoDic setValue:UN_NIL(fontModel.xyid) forKey:@"id"];
            [fontInfoDic setValue:UN_NIL(fontModel.fontCode) forKey:@"code"];
            [fontInfoDic setValue:UN_NIL(fontModel.usageDatetime) forKey:@"usageDatetime"];
            [fontInfoArr addObject:fontInfoDic];
        }
    }
    // 获得NSURLSession对象//
    NSURLSession *session = [NSURLSession sharedSession];
    NSString *graphqlStr = @"mutation uploadUserFonts ($input: InitUserFontsInput!) { uploadUserFonts (input: $input) { userId } }";
    NSDictionary *requestContents = @{@"query":graphqlStr,@"variables":@{@"input":@{@"fonts":fontInfoArr}}};
    NSData *requestData = [NSJSONSerialization dataWithJSONObject:requestContents options:0 error:nil];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:GraphqlAPIUrl]];
    request.HTTPMethod = @"POST"; // 请求方法
    request.HTTPBody = requestData; // 请求体
    [request setValue:@"application/json" forHTTPHeaderField:@"Content-Type"];
    NSString *userAgent = [[NSUserDefaults standardUserDefaults] objectForKey:@"niimbot-user-agent"];
    [request setValue:UN_NIL(userAgent) forHTTPHeaderField:@"niimbot-user-agent"];
    NSString *token = m_userModel.token;
    if (token.length > 0 ) {
        token = [NSString stringWithFormat:@"bearer %@",token];
        NSLog(@"请求token：%@",token);
    }
    [request setValue:token forHTTPHeaderField:@"Authorization"];
    [request setValue:XY_JC_LANGUAGE_REAL forHTTPHeaderField:@"languageCode"];
    // 创建任务
    NSURLSessionDataTask *task = [session dataTaskWithRequest:request completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        if(error == nil){
            complate(@"");
            NSLog(@"字体同步成功");
        }
    }];

    // 启动任务
    [task resume];
    
//    NSString *graphqlStr = @"mutation uploadUserFonts ($input: InitUserFontsInput!) { uploadUserFonts (input: $input) { userId } }";
//    [@{@"input":@{@"fonts":@[@{@"id":@"72",@"code":@"ZT063",@"usageDatetime":@"2022-07-26 09:12:02"},@{@"id":@"118",@"code":@"ZT116",@"usageDatetime":@"2022-07-26 09:12:02"}]}} jc_graphQLRequestWith:graphqlStr hud:nil graphQLType:@"query" Success:^(__kindof YTKBaseRequest *request, NSArray *requestModel) {
//        complate(@1);
//    } failure:^(NSString *msg, id model) {
//        complate(@0);
//    }];
}

- (void)addUserFontWithCacheInfo:(JCFontModel *)fontModel updateInfo:(XYBlock)complate{
    NSString *graphqlStr = @"mutation createUserFont ($input: CreateUserFontInput!) { createUserFont (input: $input) { userId } }";
    [@{@"input":@{@"id":UN_NIL(fontModel.xyid),@"code":UN_NIL(fontModel.fontCode),@"usageDatetime":UN_NIL(fontModel.usageDatetime)}} jc_graphQLRequestWith:graphqlStr hud:nil graphQLType:@"query" Success:^(__kindof YTKBaseRequest *request, NSArray *requestModel) {
        complate(@1);
    } failure:^(NSString *msg, id model) {
        complate(@0);
    }];
}

#pragma mark - getter

- (void)downloadAllFonts:(JCTemplateData *)templateData downloadComplateBlock:(XYNormalBlock)complateBlock{
    __block YTKBaseRequest *request = nil;
    __block NSNumber *downloadedNumber = @0;
    XYBlock requestBlock = ^(YTKBaseRequest *currentRequest){
        request = currentRequest;
    };
    self.complateBlock = complateBlock;
    self.downloadedFonts = [NSMutableArray array];
    NSOperationQueue *tempOperationQueue = [[NSOperationQueue alloc] init];
    JCAlertView *downloadAlert = [JCAlertView progressAlertWithTitle:XY_LANGUAGE_TITLE_NAMED(@"app01219", @"字体下载中")
                                                        confirmTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030", @"取消")
                                                        confirmClick:^{
        [tempOperationQueue cancelAllOperations];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            NSString *downloadTip = @"";
            if(self.downloadedFonts.count > 0){
                NSString *downloadedFonts = [self.downloadedFonts componentsJoinedByString:@","];
                downloadTip = [NSString stringWithFormat:@"%@ %@",downloadedFonts,XY_LANGUAGE_TITLE_NAMED(@"app01216", @"下载完毕")];
                if(self.complateBlock){
                    self.complateBlock();
                }else{
                    [MBProgressHUD showToastWithMessageDarkColor:downloadTip];
                }
            }else{
                if(self.complateBlock){
                    self.complateBlock();
                }
            }
        });
        [request stop];
    }];
    [downloadAlert show];
    [self downLoadAllFonts:tempOperationQueue template:templateData download:downloadAlert callback:^{
    } downloadBlock:^(YTKBaseRequest *request) {
        requestBlock(request);
    } downloadedNumber:^(NSNumber *downloadedNum) {
        downloadedNumber = downloadedNum;
    }];
}

- (void)downloadAllFonts:(JCTemplateData *)templateData downloadComplateBlock:(XYNormalBlock)complateBlock cancelBlock:(XYNormalBlock)cancelBlock{
    __block YTKBaseRequest *request = nil;
    __block NSNumber *downloadedNumber = @0;
    XYBlock requestBlock = ^(YTKBaseRequest *currentRequest){
        request = currentRequest;
    };
    self.complateBlock = complateBlock;
    self.downloadedFonts = [NSMutableArray array];
    NSOperationQueue *tempOperationQueue = [[NSOperationQueue alloc] init];
    JCAlertView *downloadAlert = [JCAlertView progressAlertWithTitle:XY_LANGUAGE_TITLE_NAMED(@"app01219", @"字体下载中")
                                                        confirmTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030", @"取消")
                                                        confirmClick:^{
        [tempOperationQueue cancelAllOperations];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            NSString *downloadTip = @"";
            if(self.downloadedFonts.count > 0){
                NSString *downloadedFonts = [self.downloadedFonts componentsJoinedByString:@","];
                downloadTip = [NSString stringWithFormat:@"%@ %@",downloadedFonts,XY_LANGUAGE_TITLE_NAMED(@"app01216", @"下载完毕")];
                if(self.complateBlock){
                    self.complateBlock();
                }else{
                    [MBProgressHUD showToastWithMessageDarkColor:downloadTip];
                }
            }else{
                if(self.complateBlock){
                    self.complateBlock();
                }
            }
        });
        [request stop];
        cancelBlock();
    }];
    [downloadAlert show];
    [self downLoadAllFonts:tempOperationQueue template:templateData download:downloadAlert callback:^{
    } downloadBlock:^(YTKBaseRequest *request) {
        requestBlock(request);
    } downloadedNumber:^(NSNumber *downloadedNum) {
        downloadedNumber = downloadedNum;
    }];
}

- (BOOL)isNeedDownloadFonts:(JCTemplateData *)templateData{
    NSArray *notExistFontModels = [self needDownloadFont:templateData];
    NSArray *needChangLangDownloadFont = [self needChangLangDownloadFont:templateData];
    return (notExistFontModels.count == 0 && needChangLangDownloadFont.count == 0)?NO:YES;
}

- (NSArray *)needDownloadFont:(JCTemplateData *)templateData{
    NSArray *fonts = self.allFonts;
    NSArray *existFontCodes = [JCFontModel getDownLoadFontCodeArr];
    NSMutableArray *notExistFontCodes = [NSMutableArray array];
    [templateData.elements enumerateObjectsUsingBlock:^(JCElementModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        NSArray *modelFonts = [obj getAllFontCodes];
        [modelFonts enumerateObjectsUsingBlock:^(NSString *fontCode, NSUInteger idx, BOOL * _Nonnull stop) {
            if (!STR_IS_NIL(fontCode) && ![existFontCodes containsObject:fontCode]) {
                [notExistFontCodes addObject:fontCode];
            }
        }];
    }];
    NSMutableArray *notExistFontModels = [NSMutableArray arrayWithCapacity:notExistFontCodes.count];
    [fonts enumerateObjectsUsingBlock:^(JCFontModel *fontModel, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([notExistFontCodes containsObject:fontModel.fontCode]) {
            [notExistFontModels addObject:fontModel];
        }
    }];
    return notExistFontModels;
}

- (BOOL)hasDownloadFont:(NSString *)fontCode{
    NSArray *existFontCodes = [JCFontModel getDownLoadFontCodeArr];
    return [existFontCodes containsObject:fontCode];
}

- (NSArray *)needChangLangDownloadFont:(JCTemplateData *)templateData{
    __block BOOL isNeedChangLang = YES;
    NSArray *fonts = self.allFonts;
    NSArray *existFontCodes = [JCFontModel getDownLoadFontCodeArr];
    NSMutableArray *notExistFontCodes = [NSMutableArray array];
    NSMutableArray *needDownloadFontCodes = [NSMutableArray array];
    [templateData.elements enumerateObjectsUsingBlock:^(JCElementModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        NSArray *modelFonts = [obj getAllFontCodes];
        [modelFonts enumerateObjectsUsingBlock:^(NSString *fontCode, NSUInteger idx, BOOL * _Nonnull stop) {
            if (!STR_IS_NIL(fontCode) && ![existFontCodes containsObject:fontCode]) {
                [notExistFontCodes addObject:fontCode];
            }
        }];
    }];
    if(notExistFontCodes.count == 0){
        isNeedChangLang = NO;
    }else{
        [notExistFontCodes enumerateObjectsUsingBlock:^(NSString *fontCode, NSUInteger idx, BOOL * _Nonnull stop) {
            isNeedChangLang = YES;
            [fonts enumerateObjectsUsingBlock:^(JCFontModel *fontModel, NSUInteger idx, BOOL * _Nonnull stop) {
                if([fontCode isEqualToString:fontModel.fontCode]){
                    isNeedChangLang = NO;
                }
            }];
            if(isNeedChangLang){
                [needDownloadFontCodes addObject:fontCode];
            }
        }];
    }
    return needDownloadFontCodes;
}

- (void)downLoadAllFonts:(NSOperationQueue *)tempOperationQueue template:(JCTemplateData *)templateData download:(JCAlertView *)downloadAlert
                callback:(void(^)(void))block downloadBlock:(XYBlock)downloadBlock
                downloadedNumber:(XYBlock)downloadedBlock{
    XYWeakSelf
    NSArray *notExistFontModels = [self needDownloadFont:templateData];
    if (notExistFontModels.count == 0 ) {
        if (block) block();
        return;
    }
    __block NSInteger downloadIndex = 1;
    NSMutableArray *downloadedFont = [NSMutableArray array];
    JCFontModel *model = [notExistFontModels safeObjectAtIndex:downloadIndex-1];
    [self setAlertViewProgressLabel:downloadIndex totleNumber:notExistFontModels.count alertView:downloadAlert];
    downloadAlert.dowmloadContentLabel.text = [NSString stringWithFormat:@"%@",model.name];
    NSMutableArray *requestOperationArr = [NSMutableArray array];
    for (NSInteger i = 0; i < notExistFontModels.count; i ++) {
        JCFontModel *model = [notExistFontModels safeObjectAtIndex:i];
        NSBlockOperation *operationDelete = [NSBlockOperation blockOperationWithBlock:^{
            dispatch_semaphore_t semaphore = dispatch_semaphore_create(0); //默认创建的信号为0
            [[JCFontManager sharedManager] downLoadFontRequestWithModel:model finishBlock:^(BOOL isscuess) {
                if(isscuess){
                    [self.downloadedFonts addObject:model.name];
                    [downloadedFont addObject:model];
                    float progress = (float)downloadIndex/notExistFontModels.count;
                    downloadAlert.progress = progress;
                    NSLog(@"已下载字体数：%ld",downloadIndex);
                    if (downloadedBlock) downloadedBlock(StringFromInt(downloadIndex));
                    [JCFontModel saveTime:[XYTool getNowTimeTimestamp] fontCode:model.fontCode];
                    downloadIndex = downloadIndex + 1;
                    [weakSelf setAlertViewProgressLabel:downloadIndex totleNumber:notExistFontModels.count alertView:downloadAlert];
                    if(i+1 < notExistFontModels.count){
                        JCFontModel *repairmodel = [notExistFontModels safeObjectAtIndex:i+1];
                        downloadAlert.dowmloadContentLabel.text = [NSString stringWithFormat:@"%@",repairmodel.name];
                    }else{
                        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                            if(self.complateBlock){
                                self.complateBlock();
                                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01216", @"下载完毕")];
                                for (JCFontModel *model in downloadedFont) {
                                    if(xy_isLogin){
                                        model.usageDatetime = [XYTool getCurrentTimesWithoutTSZ];
                                        [self addUserFontWithCacheInfo:model updateInfo:^(id x) {
                                            
                                        }];
                                    }else{
                                        NSArray *lacalData = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_USER_FONTINFO dicOrModel:[JCFontModel class] whereFormat:[NSString stringWithFormat:@"where fontCode = '%@'",model.fontCode]];
                                        if(lacalData.count == 0){
                                            model.dbId = [NSString jk_UUID];
                                            model.usageDatetime = [XYTool getCurrentTimesWithoutTSZ];
                                            [[JCFMDB shareDatabase:DB_DEFAULT] jc_insertTable:TABLE_USER_FONTINFO dicOrModel:model];
                                        }
                                    }
                                }
                            }
                        });
                    }
                }else{
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        __block NSString *downloadTip = XY_LANGUAGE_TITLE_NAMED(@"", @"下载失败");;
                        if(NETWORK_STATE_ERROR){
                            downloadTip = XY_LANGUAGE_TITLE_NAMED(@"app00268", @"网络异常");
                            [MBProgressHUD showToastWithMessageDarkColor:downloadTip];
                            if(self.downloadedFonts.count > 0){
                                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                                    NSString *downloadedFonts = [self.downloadedFonts componentsJoinedByString:@","];
                                    downloadTip = [NSString stringWithFormat:@"%@ %@",downloadedFonts,XY_LANGUAGE_TITLE_NAMED(@"app01216", @"下载完毕")];
                                    if(self.complateBlock){
                                        self.complateBlock();
                                    }else{
                                        [MBProgressHUD showToastWithMessageDarkColor:downloadTip];
                                    }
                                });
                            }
                        }else{
                            downloadTip = XY_LANGUAGE_TITLE_NAMED(@"", @"下载失败");
                            if(self.downloadedFonts.count > 0){
                                NSString *downloadedFonts = [self.downloadedFonts componentsJoinedByString:@","];
                                downloadTip = [NSString stringWithFormat:@"%@ %@",downloadedFonts,XY_LANGUAGE_TITLE_NAMED(@"app01216", @"下载完毕")];
                                if(self.complateBlock){
                                    self.complateBlock();
                                }else{
                                    [MBProgressHUD showToastWithMessageDarkColor:downloadTip];
                                }
                            }
                            [MBProgressHUD showToastWithMessageDarkColor:downloadTip];
                        }
                        
                    });
                    [tempOperationQueue cancelAllOperations];
                    [downloadAlert setHidden:YES];
                }
                dispatch_semaphore_signal(semaphore);
            } pressgress:^(NSString *pregress) {
                NSLog(@"%@",pregress);
                float allProgress = (float)(downloadIndex-1)/notExistFontModels.count;
                allProgress = allProgress + pregress.floatValue/notExistFontModels.count;
                downloadAlert.progress = allProgress;
            } downloadBlock:^(id x) {
                downloadBlock(x);
            }];
            dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        }];
        [requestOperationArr addObject:operationDelete];
    }
    if(requestOperationArr.count > 1){
        for (NSInteger i = 1 ; i < requestOperationArr.count ; i++) {
            NSBlockOperation *operationPre = [requestOperationArr safeObjectAtIndex:i-1];
            NSBlockOperation *operationNext = [requestOperationArr safeObjectAtIndex:i];
            [operationNext addDependency:operationPre];
        }
    }
    [tempOperationQueue addOperations:requestOperationArr waitUntilFinished:NO];
    NSArray *operations = tempOperationQueue.operations;
    NSUInteger operationCount = tempOperationQueue.operationCount;
    NSLog(@"下载队列内 字体个数：%ld  队列：%@",operationCount,operations);
}

//静默下载 不参合UI逻辑
-(void)downLoadFontRequestWithTemplateData:(JCTemplateData *)templateData finishBlock:(void (^)(BOOL))finishBlock{
    NSOperationQueue *tempOperationQueue = [[NSOperationQueue alloc] init];
    XYWeakSelf
    NSArray *notExistFontModels = [self needDownloadFont:templateData];
    if (notExistFontModels.count == 0 ) {
        if (finishBlock) finishBlock(true);
        return;
    }
    __block NSInteger downloadIndex = 1;
    NSMutableArray *downloadedFont = [NSMutableArray array];
   // JCFontModel *model = [notExistFontModels safeObjectAtIndex:downloadIndex-1];
    NSMutableArray *requestOperationArr = [NSMutableArray array];
    for (NSInteger i = 0; i < notExistFontModels.count; i ++) {
        JCFontModel *model = [notExistFontModels safeObjectAtIndex:i];
        NSBlockOperation *operationDelete = [NSBlockOperation blockOperationWithBlock:^{
            dispatch_semaphore_t semaphore = dispatch_semaphore_create(0); //默认创建的信号为0
            [[JCFontManager sharedManager] downLoadFontRequestWithModel:model finishBlock:^(BOOL isscuess) {
                if(isscuess){
                    [self.downloadedFonts addObject:model.name];
                    [downloadedFont addObject:model];
                    [JCFontModel saveTime:[XYTool getNowTimeTimestamp] fontCode:model.fontCode];
                    downloadIndex = downloadIndex + 1;
                    if(i+1 >= notExistFontModels.count){
                        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.05 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                            if(finishBlock != nil){
                                finishBlock(true);
                                for (JCFontModel *model in downloadedFont) {
                                    if(xy_isLogin){
                                        model.usageDatetime = [XYTool getCurrentTimesWithoutTSZ];
                                        [self addUserFontWithCacheInfo:model updateInfo:^(id x) {
                                            
                                        }];
                                    }else{
                                        NSArray *lacalData = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_USER_FONTINFO dicOrModel:[JCFontModel class] whereFormat:[NSString stringWithFormat:@"where fontCode = '%@'",model.fontCode]];
                                        if(lacalData.count == 0){
                                            model.dbId = [NSString jk_UUID];
                                            model.usageDatetime = [XYTool getCurrentTimesWithoutTSZ];
                                            [[JCFMDB shareDatabase:DB_DEFAULT] jc_insertTable:TABLE_USER_FONTINFO dicOrModel:model];
                                        }
                                    }
                                }
                            }
                        });
                    }
                }else{
                    finishBlock(true);
                    [tempOperationQueue cancelAllOperations];
                }
                dispatch_semaphore_signal(semaphore);
            } pressgress:^(NSString *pregress) {
                NSLog(@"%@-%@字体下载进度:%@",templateData.idStr,model.fontCode,pregress);
            } downloadBlock:^(id x) {
                //downloadBlock(x);
            }];
            dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        }];
        [requestOperationArr addObject:operationDelete];
    }
    if(requestOperationArr.count > 1){
        for (NSInteger i = 1 ; i < requestOperationArr.count ; i++) {
            NSBlockOperation *operationPre = [requestOperationArr safeObjectAtIndex:i-1];
            NSBlockOperation *operationNext = [requestOperationArr safeObjectAtIndex:i];
            [operationNext addDependency:operationPre];
        }
    }
    [tempOperationQueue addOperations:requestOperationArr waitUntilFinished:NO];
}

- (void)setAlertViewProgressLabel:(NSInteger)currentIndex totleNumber:(NSInteger)totaleNumber alertView:(JCAlertView *)alertView{
    NSString *pregressText = [NSString stringWithFormat:@"%@/%@",StringFromInt(currentIndex),StringFromInt(totaleNumber)];
    NSString *str1 = @"/";
    NSRange rang1 = [pregressText rangeOfString:str1];
    NSRange rang2 = NSMakeRange(0, rang1.location);
    NSMutableAttributedString *attr = [[NSMutableAttributedString alloc] initWithString:pregressText];
    [attr addAttributes:@{NSFontAttributeName:MY_FONT_Medium(14),NSForegroundColorAttributeName:XY_HEX_RGB(0x537FB7)} range:rang2];
    alertView.progressLabel.attributedText = attr;
}

- (void)updateFontDBWithLastUseDate:(NSString *)lastUseTimeStamp where:(NSString *)format {
   NSArray *arr = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_FONTINFO dicOrModel:[JCFontModel class] whereFormat:format];
    if (arr.count > 0) {
       JCFontModel *model = arr.firstObject;
        model.lastUseTimeStamp = lastUseTimeStamp;
        [[JCFMDB shareDatabase:DB_DEFAULT] jc_updateTable:TABLE_FONTINFO dicOrModel:model whereFormat:format];
    }else{
        
    }
}

- (BOOL)updateFontDBWithParam:(JCFontModel *)model {
   return [[JCFMDB shareDatabase:DB_DEFAULT] jc_updateTable:TABLE_FONTINFO dicOrModel:model whereFormat:[NSString stringWithFormat:@"where xyid = %@",model.xyid]];
}

@end
