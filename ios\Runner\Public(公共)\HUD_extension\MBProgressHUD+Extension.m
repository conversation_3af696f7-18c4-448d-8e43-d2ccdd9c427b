//
//  MBProgressHUD+Extension.m
//  Runner
//
//  Created by EG on 2018/10/18.
//  Copyright © 2018年 xiaoyao. All rights reserved.
//

//#import "MBProgressHUD+Extension.h"

@implementation MBProgressHUD (Extension)

+ (instancetype)showHUDAddedTo:(UIView *)view animated:(BOOL)animated contentColor:(UIColor *)contentColor backColor:backColor{
    MBProgressHUD *progressHUD = [MBProgressHUD showHUDAddedTo:view animated:animated];
    [progressHUD setContentColor:contentColor];
    progressHUD.bezelView.style = MBProgressHUDBackgroundStyleSolidColor;
    progressHUD.bezelView.color = backColor;
    progressHUD.bezelView.layer.cornerRadius = 6;
    return progressHUD;
}

+ (void)showSuccess:(NSString *)success {
    [self showSuccess:success toView: [UIApplication sharedApplication].keyWindow];
}

+ (void)showError:(NSString *)error {
    [self showError:error toView: [UIApplication sharedApplication].keyWindow];
}

+ (void)showError:(NSString *)error toView:(UIView *)view {
    [self show:error icon:@"failed" view:view];
}

+ (void)showSuccess:(NSString *)success toView:(UIView *)view {
    [self show:success icon:@"success" view:view];
}

+ (void)show:(NSString *)text icon:(NSString *)icon view:(UIView *)view {
    if (view == nil) view = [[UIApplication sharedApplication].windows lastObject];
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:view animated:YES];
    hud.label.text = text;
    hud.label.numberOfLines = 0;
    hud.customView = [[UIImageView alloc] initWithImage:XY_IMAGE_NAMED(icon)];
    hud.bezelView.layer.cornerRadius = 6;
    hud.mode = MBProgressHUDModeCustomView;
    hud.bezelView.style = MBProgressHUDBackgroundStyleSolidColor;
    hud.bezelView.color = hudBackColor;
    [hud setContentColor:hudContentColor];
    hud.removeFromSuperViewOnHide = YES;
    [hud hideAnimated:YES afterDelay:0.7];
}

+ (MBProgressHUD*)showToastWithMessage:(NSString *)message {
    [MBProgressHUD hideHUDForView:[UIApplication sharedApplication].keyWindow];
    MBProgressHUD *tip = [MBProgressHUD showHUDAddedTo:[UIApplication sharedApplication].keyWindow animated:YES];
    tip.mode = MBProgressHUDModeText;
    tip.opaque = 0.4;
    tip.label.text = message;
    tip.label.font = [UIFont systemFontOfSize:15];
    tip.label.textColor = [UIColor whiteColor];
    tip.bezelView.layer.cornerRadius = 6;
  
    [tip showAnimated:YES];
    [tip hideAnimated:YES afterDelay:1.5];
    return tip;
}

+ (void)showToastWithMessageDarkColor:(NSString *)message {
    if(STR_IS_NIL(message)) return;
    [MBProgressHUD hideHUDForView:[UIApplication sharedApplication].keyWindow];
    MBProgressHUD *tip = [MBProgressHUD showHUDAddedTo:[UIApplication sharedApplication].keyWindow animated:YES];
    tip.mode = MBProgressHUDModeText;
    tip.opaque = 0.35;
    tip.label.text = message;
    tip.label.numberOfLines = 0;
    tip.label.font = [UIFont systemFontOfSize:15];
    tip.bezelView.layer.cornerRadius = 6;
    tip.bezelView.style = MBProgressHUDBackgroundStyleSolidColor;
    tip.bezelView.color = hudBackColor;
    [tip setContentColor:hudContentColor];
    [tip showAnimated:YES];
    [tip hideAnimated:YES afterDelay:1.5];
}

+ (void)showToastWithMessageDarkColor:(NSString *)message hidenAfterDelay:(NSTimeInterval)delay{
    if(STR_IS_NIL(message)) return;
    [MBProgressHUD hideHUDForView:[UIApplication sharedApplication].keyWindow];
    MBProgressHUD *tip = [MBProgressHUD showHUDAddedTo:[UIApplication sharedApplication].keyWindow animated:YES];
    tip.mode = MBProgressHUDModeText;
    tip.opaque = 0.35;
    tip.label.text = message;
    tip.label.numberOfLines = 0;
    tip.label.font = [UIFont systemFontOfSize:15];
    tip.bezelView.layer.cornerRadius = 6;
    tip.bezelView.style = MBProgressHUDBackgroundStyleSolidColor;
    tip.bezelView.color = hudBackColor;
    [tip setContentColor:hudContentColor];
    [tip showAnimated:YES];
    [tip hideAnimated:YES afterDelay:delay];
}

+ (void)showToastWithMuliLinesMessage:(NSString *)message {
    [MBProgressHUD hideHUDForView:[UIApplication sharedApplication].keyWindow];
    MBProgressHUD *tip = [MBProgressHUD showHUDAddedTo:[UIApplication sharedApplication].keyWindow animated:YES];

    tip.opaque = 0.4;
    tip.bezelView.layer.cornerRadius = 6;
    tip.bezelView.color =[UIColor blackColor];
    tip.mode = MBProgressHUDModeText;
    tip.detailsLabel.textColor = [UIColor whiteColor];
    tip.detailsLabel.font = [UIFont systemFontOfSize:15];
    tip.detailsLabel.text = message;

    [tip showAnimated:YES];
    [tip hideAnimated:YES afterDelay:1.5];
}

+ (MBProgressHUD *)showMessage:(NSString *)message toView:(UIView *)view {
    if (view == nil) view = [[UIApplication sharedApplication].windows lastObject];
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:view animated:YES];
    hud.label.text = message;
    hud.removeFromSuperViewOnHide = YES;
    return hud;
}

+ (MBProgressHUD *)showMessage:(NSString *)message {
    return [self showMessage:message toView:[UIApplication sharedApplication].keyWindow];
}

+ (void)hideHUD {
    [self hideHUDForView:[[UIApplication sharedApplication].windows lastObject]];
}

+ (void)hideHUDForView:(UIView *)view {
    [self hideHUDForView:view animated:YES];
}

//横向图片 文本Toast
+ (UIView *)getSuccessToastView:(NSString *)msg icon:(NSString *)iconName{
    UIView *contentView = [[UIView alloc] init];
    contentView.backgroundColor = COLOR_CLEAR;
    contentView.clipsToBounds = YES;
    contentView.layer.cornerRadius = 12;
    float height = [msg jk_sizeWithFont:MY_FONT_Regular(16) constrainedToWidth:(SCREEN_WIDTH - 60 - 60)].height + 22;
    float width = [msg jk_sizeWithFont:MY_FONT_Regular(16) constrainedToHeight:10000].width + 36 + 24 + 10;
    if(width > SCREEN_WIDTH - 60 - 40){
        width = SCREEN_WIDTH - 60 - 40;
    }
    contentView.size = CGSizeMake(width, height);
    contentView.center = CGPointMake(SCREEN_WIDTH/2, SCREEN_HEIGHT/2);
    UIView *jcMaskView = [[UIView alloc] init];
    jcMaskView.backgroundColor = HEX_RGBA(0x000000, 0.6);
    [contentView addSubview:jcMaskView];
    [jcMaskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(contentView);
    }];
    UILabel *contentLable = [[UILabel alloc] init];
    contentLable.font = MY_FONT_Regular(16);
    contentLable.textColor = COLOR_WHITE;
    contentLable.numberOfLines = 0;
    contentLable.text = msg;
    [contentView addSubview:contentLable];
    UIImageView *iconImageView = [[UIImageView alloc] init];
    iconImageView.image = XY_IMAGE_NAMED(iconName);
    iconImageView.hidden = NO;
    [contentView addSubview:iconImageView];
    [contentLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(iconImageView.mas_trailing).offset(10);
        make.trailing.equalTo(contentView).offset(-18);
        make.centerY.equalTo(contentView);
    }];
    [iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(contentView).offset(18);
        make.centerY.equalTo(contentView);
        make.width.height.mas_equalTo(@24);
    }];
    contentView.hidden = YES;
    return contentView;
}

+ (void)showSuccessToast:(NSString *)message icon:(NSString *)iconName{
    UIView *containerView = [UIApplication sharedApplication].keyWindow;
    UIView *contentView = [self getSuccessToastView:message icon:iconName];
    [containerView addSubview:contentView];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        contentView.hidden = NO;
    });
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [contentView removeFromSuperview];
        contentView.hidden = YES;
    });
}
@end
