import 'package:text/application.dart';
import 'package:text/pages/my_template/abstract/my_template_manager_abstract.dart';
import 'package:text/pages/my_template/controller/folders_database.dart';
import 'package:text/pages/my_template/controller/folders_database_v2.dart';
import 'package:text/pages/my_template/controller/template_database.dart';
import 'package:text/pages/my_template/model/folder_model.dart';
import 'package:text/pages/my_template/page/public_template/controller/public_template_state.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';

import '../../../log_utils.dart';
import '../my_template_state.dart';

class FolderManager extends MyTemplateManagerAbstract {
  // FoldersDataBase _db = FoldersDataBase();
  FoldersDatabaseV2 _db = FoldersDatabaseV2();

  FolderManager() {}

  ///新建文件夹
  create(String name, Function function, Function fair) {
    interface.createFolderName(name, (value) {
      function.call();
    }, (code) {
      fair.call(code);
    });
  }

  /// 获取文件夹列表
  ///
  /// 该方法采用本地缓存 + 网络请求的策略：
  /// 1. 优先从本地数据库获取数据，立即返回给UI，保证快速响应
  /// 2. 同时发起网络请求获取最新数据，更新本地缓存后再次通知UI
  ///
  /// [page] 页码
  /// [funcation] 数据回调函数，接收文件夹列表
  /// [limit] 限制数量，默认-1表示无限制
  /// [folderId] 文件夹ID，可选参数
  /// [refresh] 是否刷新，默认true
  /// [isFunctionCallOnce] 是否只回调一次，默认false，为true时网络更新后不再回调
  /// [state] 模板操作状态，默认为normal状态
  @override
  getAll(int page, Function(List<FolderModel> funcation) funcation,
      {int limit = -1,
      int? folderId,
      refresh = true,
      bool isFunctionCallOnce = false,
      TemplateOperateState state = TemplateOperateState.normal}) {
    // 未登录用户只能访问本地数据库
    if (Application.user == null) {
      _db.query().then((value) {
        funcation.call(value);
      });
      return;
    }

    // 第一步：先从本地数据库获取缓存数据，立即返回给UI，保证快速响应
    _db.query().then((value) {
      funcation.call(value);
    });

    // 第二步：发起网络请求获取最新数据
    interface.getFolderList(page, (value) {
      // 清空本地数据库中的旧数据
      _db.delete().then((_) {
        // 将网络获取的新数据写入本地数据库
        return _db.addToType(value);
      }).then((_) {
        // 延迟200毫秒后检查数据变化并通知UI
        // 延迟是为了确保数据库写入操作完成
        Future.delayed(Duration(milliseconds: 200), () {
          return _db.query().then((newValue) {
            // 根据isFunctionCallOnce参数决定是否再次回调
            // 如果为false，则网络数据更新后会再次通知UI刷新
            // 如果为true，则只在首次本地查询时回调一次，避免重复刷新
            if (!isFunctionCallOnce) {
              funcation.call(newValue);
            }
          }).catchError((error) => Log.d(error.toString()));
        });
      });
    });
  }

  /// 判断是否需要更新UI
  /// 比较网络数据和本地数据，如果有差异则需要更新
  bool _shouldUpdateUI(List<FolderModel> networkData, List<FolderModel> localData) {
    if (networkData.length != localData.length) {
      return true;
    }

    // 简单比较文件夹ID和名称
    for (int i = 0; i < networkData.length; i++) {
      if (networkData[i].id != localData[i].id || networkData[i].name != localData[i].name) {
        return true;
      }
    }

    return false;
  }

  getDB() {
    return _db;
  }

  getShareAll(PublicTemplateState state, Function(List<FolderModel> funcation) funcation,
      {int page = 1, isSendEvent = false}) {
    var type = state.tabIndex == 0 ? 2 : 1;
    if (isSendEvent) {
      type = 1;
    }
    if (type == 1) {
      _db.query(type: type).then((value) {
        if (value.isNotEmpty) {
          funcation.call(value);
        }
      });
    } else {
      _db
          .shareMeQuery(
        type: type,
        pageIndex: page,
      )
          .then((value) {
        if (value.isNotEmpty) {
          funcation.call(value);
        }
      });
    }

    if (type == 1) {
      interface.getFolderList(page, (value) {
        solveFolderDB(type, value, funcation);
      });
    } else {
      interface.getShareMeFolderList(page, 100, (value) {
        solveShareMeFolderDB(type, value, funcation, page);
      }, () {
        funcation.call([]);
      });
    }
  }

  solveFolderDB(int type, List<FolderModel> value, Function funcation) {
    _db.delete().then((_) {
      return _db.addToType(value);
    }).then((_) {
      return _db.query(type: type);
    }).then((value) {
      funcation.call(value);
    }).catchError((error) => Log.d(error.toString()));
  }

  solveShareMeFolderDB(
    int type,
    List<FolderModel> value,
    Function funcation,
    int page,
  ) {
    if (page == 1) {
      _db.delete(type: type).then((_) {
        return _db.addToShareIds(value, type: type);
      }).then((_) {
        return _db.shareMeQuery(type: type);
      }).then((value) {
        funcation.call(value);
      }).catchError((error) => Log.d(error.toString()));
    } else {
      _db.addToShareIds(value, type: type).then((_) {
        return _db.shareMeQuery(type: type, pageIndex: page);
      }).then((value) {
        funcation.call(value);
      }).catchError((error) => Log.d(error.toString()));
    }
  }

  ///重命名
  @override
  void reName(dynamic model, String name, Function function) {
    var folderModel = model as FolderModel;
    interface.reFreshFolderName(folderModel.id!.toInt(), name, () {
      function.call();
    }, () {});
  }

  ///共享
  void share(dynamic model, Function(Map) function) {
    var folderModel = model as FolderModel;
    interface.setFolderShare(folderModel.id.toString(), function);
  }

  @override
  void delete(model, Function function) {
    var folderModel = model as FolderModel;
    interface.deleteFolders(folderModel.id.toString(), (value) {
      _db.delete(id: value);
      TemplateDataBase().deleteTemplatesinFolder(folderModel.id.toString()).then((value) {
        ToNativeMethodChannel.nativeTemplateNeedRefresh();
      });

      function.call();
    }, () {});
  }

  cancelShare(String id, TemplateOperateState state, Function function) {
    var type = state == TemplateOperateState.shareOut ? 1 : 2;
    interface.cancelFolderShare(id, () {
      _db.delete(id: id, type: type);
      function.call();
    }, () {});
  }

  exitShare(String id, TemplateOperateState state, Function function) {
    var type = state == TemplateOperateState.shareOut ? 1 : 2;
    interface.exitFolderShare(id, () {
      _db.delete(type: type);
      function.call();
    }, () {});
  }
}
