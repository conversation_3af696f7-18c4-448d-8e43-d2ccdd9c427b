import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:netal_plugin/netal_plugin.dart';
import 'package:netal_plugin/niimbot_netal.dart';
import 'package:niimbot_log_plugin/model/niimbot_log_result.dart';
import 'package:niimbot_log_plugin/niimbot_log_manager.dart';
import 'package:niimbot_mobile_ui/widgets/niimbot_toast/niimbot_loading_controller.dart';
import 'package:niimbot_mobile_ui/widgets/niimbot_toast/niimbot_toast_controller.dart';
import 'package:niimbot_template/models/copy_wrapper.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/template/tube_file_setting.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/models/values/composite_value_type.dart';
import 'package:niimbot_template/models/values/template_value_type.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:text/application.dart';
import 'package:text/database/C1/niimbot_template_db_utils.dart';
import 'package:text/network/dio_utils.dart';
import 'package:text/network/http_api.dart';
import 'package:text/pages/C1/model/c1_file_list_model.dart';
import 'package:text/pages/C1/model/template_data_transform.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/event_bus.dart';

typedef SyncServiceTemplateFun = Function(String, TemplateData, bool);

class C1FileBusiness {
  C1FileBusiness._();

  static final C1FileBusiness _instance = C1FileBusiness._();

  factory C1FileBusiness() => _instance;

  static C1FileBusiness get instance => _instance;

  Map<String, TemplateData> templateDetailMap = {};

  List<SyncServiceTemplateFun> syncServiceTemplateFunList = [];

  bool isEditStatus = false;

  addSyncServiceTemplateFun(SyncServiceTemplateFun fun) {
    if (!syncServiceTemplateFunList.contains(fun)) {
      syncServiceTemplateFunList.add(fun);
    }
  }

  removeSyncServiceTemplateFun(SyncServiceTemplateFun fun) {
    if (syncServiceTemplateFunList.contains(fun)) {
      syncServiceTemplateFunList.remove(fun);
    }
  }

  /// 从本地数据库分页获取模板列表
  requestTemplateListFromLocal(int page, {int pageSize = 10, Function(List<TemplateData>)? requestResult}) async {
    List<TemplateData> templateList = await NiimbotTemplateDbUtils.queryTemplateList(page, pageSize: pageSize);
    requestResult?.call(templateList);
  }

  /// 请求模板列表
  /// 先从服务端请求模板列表同步到数据库
  /// 再从本地数据分页获取模板列表
  requestTemplateList(int page, {int pageSize = 10, Function(List<TemplateData>)? requestResult}) {
    requestTemplateListFromService(page, pageSize: pageSize, success: (result) {
      NiimbotTemplateDbUtils.syncServiceFileListModel(result).then((value) async {
        requestTemplateListFromLocal(page, pageSize: pageSize, requestResult: requestResult);
      });
    }, fail: (code, msg) async {
      requestTemplateListFromLocal(page, pageSize: pageSize, requestResult: requestResult);
    });
  }

  /// 查询本地数据库是否包含指定模板id的模板
  Future<bool> checkContainFile(TemplateData templateData) async {
    String? templateId = templateData.id;
    if (templateId == null || templateId.isEmpty) {
      return false;
    }
    return NiimbotTemplateDbUtils.checkTemplateExist(templateId);
  }

  /// 文件是否包含多序号
  bool checkContainMultipleSerials(TemplateData templateData) {
    for(int i = 0; i < templateData.values!.length; i++) {
      CompositeValueType paragraph = templateData.values![i] as CompositeValueType;
      List<ValueTypeProtocol> lines = paragraph.valueObjects!;
      List<ValueTypeProtocol> splitValue1 = (lines[0] as CompositeValueType).valueObjects!;
      int count1 = 0;
      for(int j = 0; j < splitValue1.length; j++) {
        if(splitValue1[j].type == TemplateValueType.serial) {
          count1++;
        }
        if(count1 > 1) {
          return true;
        }
      }
      if(lines.length == 2) {
        List<ValueTypeProtocol> splitValue2 = (lines[1] as CompositeValueType).valueObjects!;
        int count2 = 0;
        for(int j = 0; j < splitValue2.length; j++) {
          if(splitValue2[j].type == TemplateValueType.serial) {
            count2++;
          }
          if(count2 > 1) {
            return true;
          }
        }
      }
    }
    return false;
  }

  /// 新建模板文件
  createC1File(TemplateData templateData, {Function(TemplateData)? success, Function(String)? fail}) {
    _generateThumbnail(templateData).then((localThumbnail) {
      NiimbotTemplateDbUtils.markCreateTemplate(templateData.copyWith(
              localThumbnail: localThumbnail,
              thumbnail: CopyWrapper<String?>.value(''),
              elements: templateData.elements,
              values: templateData.values))
          .then((createTemplate) {
        templateDetailMap[createTemplate.id!] = createTemplate;
        success?.call(createTemplate);
        _checkNetworkConnected().then((value) {
          if (!value) {
            return;
          }
          _uploadThumbnailIfNecessary(createTemplate, success: (uploadTemplate) {
            createTemplateToService(uploadTemplate);
          });
        });
      });
    });
  }

  /// 编辑模板文件
  modifyC1File(TemplateData templateData, {Function(TemplateData)? success, Function(String)? fail}) {
    num localType = templateData.local_type;
    _generateThumbnail(templateData).then((localThumbnail) {
      NiimbotTemplateDbUtils.markUpdateTemplate(templateData.copyWith(
              localThumbnail: localThumbnail,
              thumbnail: CopyWrapper<String?>.value(''),
              elements: templateData.elements,
              values: templateData.values))
          .then((modifyTemplate) {
        templateDetailMap[modifyTemplate.id!] = modifyTemplate;
        success?.call(modifyTemplate);
        _checkNetworkConnected().then((value) {
          if (!value) {
            return;
          }
          _uploadThumbnailIfNecessary(modifyTemplate, success: (uploadTemplate) {
            if (localType != NiimbotTemplateDbUtils.CREATE) {
              modifyTemplateToService(uploadTemplate);
            } else {
              if (toServiceTemplateIdList.contains(uploadTemplate.id!)) {
                createTemplateToService(uploadTemplate);
              }
            }
          });
        });
      });
    });
  }

  /// 重命名模板文件
  renameC1File(TemplateData templateData, String fileName, {Function(TemplateData)? success, Function(String)? fail}) {
    num localType = templateData.local_type;
    String updateTime = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());
    NiimbotTemplateDbUtils.markModifyTemplateName(templateData, fileName, updateTime).then((modifyTemplate) {
      success?.call(modifyTemplate);
      _checkNetworkConnected().then((value) {
        if (!value) {
          return;
        }
        _uploadThumbnailIfNecessary(modifyTemplate, success: (uploadTemplate) {
          if (localType == NiimbotTemplateDbUtils.UPDATE) {
            modifyTemplateToService(uploadTemplate);
          } else if (localType == NiimbotTemplateDbUtils.SYNC) {
            modifyTemplateNameToService(uploadTemplate.id!, fileName, updateTime);
          }
        });
      });
    });
  }

  /// 复制模板文件
  copyC1File(TemplateData templateData, {Function(TemplateData)? success, Function(String)? fail}) {
    //数据库本地复制
    NiimbotTemplateDbUtils.markCreateTemplate(templateData.copyWith(
            name: "${templateData.name}-1", elements: templateData.elements, values: templateData.values))
        .then((createTemplate) {
      success?.call(createTemplate);
      _checkNetworkConnected().then((value) {
        if (!value) {
          return;
        }
        _uploadThumbnailIfNecessary(createTemplate, success: (uploadTemplate) {
          createTemplateToService(uploadTemplate);
        });
      });
    });
  }

  /// 删除模板文件
  deleteC1File(TemplateData templateData, {Function? success, Function(String)? fail}) {
    templateDetailMap.remove(templateData.id!);
    if (templateData.local_type == NiimbotTemplateDbUtils.CREATE) {
      NiimbotTemplateDbUtils.deleteTemplate(templateData.id!).then((value) {
        success?.call();
      });
      return;
    }
    //数据库模板标记为已删除
    NiimbotTemplateDbUtils.markDeleteTemplate(templateData).then((value) {
      success?.call();
      _checkNetworkConnected().then((value) {
        if (!value) {
          return;
        }
        deleteTemplateToService(templateData);
      });
    });
  }

  /// 上传缩略图到oss服务器
  _uploadThumbnailIfNecessary(TemplateData templateData, {Function(TemplateData)? success, Function? fail}) {
    if (templateData.localThumbnail.isEmpty || templateData.thumbnail.isNotEmpty) {
      success?.call(templateData);
      return;
    }
    NiimbotLogManager.instance.uploadFile(filePath: templateData.localThumbnail,
        (NiimbotLogResultType resultCode, String value) {
      if (resultCode == NiimbotLogResultType.success) {
        success?.call(templateData.copyWith(
            thumbnail: CopyWrapper<String?>.value(value),
            elements: templateData.elements,
            values: templateData.values));
      } else {
        fail?.call();
      }
    });
  }

  /// 服务端新增模板文件
  createTemplateToService(TemplateData localTemplate,
      {Function(String?)? success, Function(int, String)? fail, bool fromBackground = false}) {
    debugPrint("createTemplateToService toJson: ${DateFormat('yyyy-MM-dd HH:mm:ss.SSS').format(DateTime.now())}");
    Map<String, dynamic> map = localTemplate.toJson();
    if(map.containsKey("currentPage")) {
      map.remove("currentPage");
    }
    debugPrint(
        "createTemplateToService requestNetwork: ${DateFormat('yyyy-MM-dd HH:mm:ss.SSS').format(DateTime.now())}");
    Options options = new Options(
      receiveTimeout: Duration(seconds: 20),
      sendTimeout: Duration(seconds: 20),
    );
    DioUtils.instance.requestNetwork<Map<String, dynamic>>(Method.post, MyTemplateApi.createTemplate,
        params: map, options: options, isList: false, onSuccess: (data) {
      debugPrint(
          "createTemplateToService requestNetwork success: ${DateFormat('yyyy-MM-dd HH:mm:ss.SSS').format(DateTime.now())}");
      String serviceId = data!['id'].toString();
      NiimbotTemplateDbUtils.createSyncServiceTemplateId(localTemplate, serviceId).then((syncTemplate) {
        success?.call(serviceId);
        templateDetailMap.remove(localTemplate.id!);
        // Map<String, dynamic> map = {};
        // map["action"] = "uploadTemplate";
        // map["fromBackground"] = fromBackground;
        // map["templateId"] = localTemplate.id;
        // map["localTemplateId"] = localTemplate.id;
        // map["template"] = jsonEncode(syncTemplate.toJson());
        // NiimbotEventBus.getDefault().post(map);
        if (syncServiceTemplateFunList.isNotEmpty) {
          for (int i = syncServiceTemplateFunList.length - 1; i >= 0; i--) {
            syncServiceTemplateFunList[i].call(localTemplate.id!, syncTemplate, fromBackground);
          }
        }
        // if (serviceTemplate.local_type == NiimbotTemplateDbUtils.UPDATE) {
        //   createTemplateToService(localTemplate, fromBackground: fromBackground);
        // }
      });
    }, onError: (int code, String msg) {
      debugPrint(
          "createTemplateToService requestNetwork failed: ${DateFormat('yyyy-MM-dd HH:mm:ss.SSS').format(DateTime.now())}");
      fail?.call(code, msg);
    });
  }

  /// 服务端删除模板文件
  deleteTemplateToService(TemplateData template,
      {Function? success, Function(int, String)? fail, bool fromBackground = false}) {
    Map<String, dynamic> map = {"ids": template.id!};
    DioUtils.instance.requestNetwork<dynamic>(Method.post, MyTemplateApi.deleteTemplate, params: map, isList: false,
        onSuccess: (data) {
      //数据库本地删除
      NiimbotTemplateDbUtils.deleteTemplate(template.id!).then((value) {
        success?.call();
        if (!fromBackground) {
          NiimbotEventBus.getDefault().post("refreshFileList");
        }
      });
    }, onError: (int code, String msg) {
      fail?.call(code, msg);
    });
  }

  /// 服务端修改模板文件
  modifyTemplateToService(TemplateData template,
      {Function? success, Function(int, String)? fail, bool fromBackground = false}) {
    Map<String, dynamic> map = template.toJson();
    Options options = new Options(
      receiveTimeout: Duration(seconds: 20),
      sendTimeout: Duration(seconds: 20),
    );
    if(map.containsKey("currentPage")) {
      map.remove("currentPage");
    }
    DioUtils.instance.requestNetwork<String>(Method.post, MyTemplateApi.updateTemplate,
        params: map, options: options, isList: false, onSuccess: (data) {
      NiimbotTemplateDbUtils.updateTemplate(template).then((syncTemplate) {
        success?.call();
        // Map<String, dynamic> map = {};
        // map["action"] = "uploadTemplate";
        // map["fromBackground"] = fromBackground;
        // map["templateId"] = template.id;
        // map["template"] = jsonEncode(syncTemplate.toJson());
        // NiimbotEventBus.getDefault().post(map);
        if (syncServiceTemplateFunList.isNotEmpty) {
          for (int i = syncServiceTemplateFunList.length - 1; i >= 0; i--) {
            syncServiceTemplateFunList[i].call(syncTemplate.id!, syncTemplate, fromBackground);
          }
        }
      });
    }, onError: (int code, String msg) {
      fail?.call(code, msg);
    });
  }

  /// 服务端修改模板文件名称
  modifyTemplateNameToService(String templateId, String name, String updateTime,
      {Function? success, Function(int, String)? fail}) {
    Map<String, dynamic> map = {};
    map["id"] = templateId;
    map["name"] = name;
    map["updateTime"] = updateTime;
    DioUtils.instance.requestNetwork(Method.post, MyTemplateApi.templateRename, params: map, isList: false,
        onSuccess: (data) {
      NiimbotEventBus.getDefault().post("refreshFileList");
    }, onError: (int code, String msg) {
      fail?.call(code, msg);
    });
  }

  /// 从服务端请求模板文件列表
  requestTemplateListFromService(int page,
      {int pageSize = 10, Function(C1FileListModel)? success, Function(int, String)? fail}) {
    _checkNetworkConnected().then((value) {
      if (!value) {
        fail?.call(-1, "网络异常");
        return;
      }
      int? userId = Application.user?.userId;
      if (userId == null || userId <= 0) {
        fail?.call(-1, "userId为空");
        return;
      }
      getLastRequestFileListServerTime(userId).then((serverTime) {
        Map<String, dynamic> map = {};
        map["page"] = page;
        map["limit"] = pageSize;
        map["tubeFile"] = 1;
        map["excludeElementValuesIfTubeTemplate"] = true;
        if (serverTime == null) {
          DateTime toTime = DateTime.now();
          DateTime fromTime = toTime.subtract(Duration(days: 180));
          map["fromTimestamp"] = fromTime.millisecondsSinceEpoch.toString();
          map["toTimestamp"] = toTime.millisecondsSinceEpoch.toString();
        } else {
          DateTime fromTime = DateTime.fromMillisecondsSinceEpoch(serverTime).subtract(Duration(hours: 1));
          map["fromTimestamp"] = fromTime.millisecondsSinceEpoch.toString();
        }
        Options options = new Options(
          receiveTimeout: Duration(seconds: 20),
          sendTimeout: Duration(seconds: 20),
        );
        DioUtils.instance.requestNetwork<Map>(Method.post, MyTemplateApi.myTemplateList,
            params: map, options: options, isList: false, useServerTime: true, onSuccess: (data) {
          C1FileListModel.fromJson(data).then((model) {
            success?.call(model);
          });
        }, onError: (int code, String msg) {
          fail?.call(code, msg);
        });
      });
    });
  }

  removeTemplateDetailCache(String templateId) {
    templateDetailMap.remove(templateId);
  }

  /// 获取模板文件详情
  Future<TemplateData?> getTemplateDetailByTemplateId(String templateId, String updateTime) async {
    TemplateData? templateData = await NiimbotTemplateDbUtils.queryTemplateDetailById(templateId);
    if (templateData != null) {
      String updateTime1 = templateData.profile.extra.updateTime ?? "";
      if (updateTime1.isNotEmpty && updateTime.isNotEmpty && updateTime1.compareTo(updateTime) >= 0) {
        templateDetailMap[templateId] = templateData;
        return templateData;
      }
    }
    if (!Application.networkConnected) {
      if (templateData != null) {
        return templateData;
      } else {
        NiimbotToastController().showErrorToast(intlanguage('app01139', '网络异常'));
        return null;
      }
    }
    templateData = await getTemplateDetailFromService(templateId);
    if (templateData != null) {
      templateDetailMap[templateId] = templateData;
      NiimbotTemplateDbUtils.insertTemplate(templateData);
    }
    if (templateData == null) {
      NiimbotToastController().showErrorToast(intlanguage('app100001936', '获取文件详情失败'));
    }
    return templateData;
  }

  /// 从服务端获取模板文件详情
  Future<TemplateData?> getTemplateDetailFromService(String templateId) {
    NiimbotLoadingController().easyLoadingShow();
    Completer<TemplateData?> completer = Completer();
    Options options = new Options(
      receiveTimeout: Duration(seconds: 20),
      sendTimeout: Duration(seconds: 20),
    );
    DioUtils.instance.requestNetwork<Map<String, dynamic>>(
        Method.get, MyTemplateApi.fetchTemplateDetailById(templateId), options: options, needEncrypt: false,
        onSuccess: (data) async {
      NiimbotLoadingController().easyLoadingDismiss();
      await Future.delayed(Duration(milliseconds: 50));
      if (data != null && data.isNotEmpty) {
        List<Map<String, dynamic>> elementList = List<Map<String, dynamic>>.from(data["elements"] as List<dynamic>);
        List<BaseElement> elements = elementList.map((e) => TextElement.fromJson(e)).toList();
        data["elements"] = null;
        TemplateData templateData = await TemplateData.fromJson(data);
        templateData.elements.addAll(elements);
        templateData = templateData.copyWith(local_type: NiimbotTemplateDbUtils.SYNC, elements: templateData.elements, values: templateData.values);
        TubeFileSetting? tubeFileSetting = templateData.tubeFileSetting;
        if(tubeFileSetting != null) {
          if(tubeFileSetting.autoWidth ?? false) {
            tubeFileSetting.width = 30;
          }
          else {
            tubeFileSetting.width = templateData.width;
          }
        }
        completer.complete(templateData);
      } else {
        completer.complete(null);
      }
    }, onError: (code, message) {
      NiimbotLoadingController().easyLoadingDismiss();
      completer.complete(null);
    });
    return completer.future;
  }

  Future<TemplateData?> getTemplateDetailByShareCode(String shareCode) {
    NiimbotLoadingController().easyLoadingShow();
    Map<String, dynamic> params = {"shareCode": shareCode};
    Completer<TemplateData?> completer = Completer();
    Options options = new Options(
      receiveTimeout: Duration(seconds: 20),
      sendTimeout: Duration(seconds: 20),
    );
    DioUtils.instance.requestNetwork<Map<String, dynamic>>(
        Method.post, MyTemplateApi.shareDecode, params: params, options: options, needEncrypt: true,
        onSuccess: (data) async {
          NiimbotLoadingController().easyLoadingDismiss();
          await Future.delayed(Duration(milliseconds: 50));
          if (data != null && data.isNotEmpty) {
            List<Map<String, dynamic>> elementList = List<Map<String, dynamic>>.from(data["elements"] as List<dynamic>);
            List<BaseElement> elements = elementList.map((e) => TextElement.fromJson(e)).toList();
            data["elements"] = null;
            TemplateData templateData = await TemplateData.fromJson(data);
            templateData.elements.addAll(elements);
            templateData = templateData.copyWith(local_type: NiimbotTemplateDbUtils.DEFAULT, elements: templateData.elements, values: templateData.values);
            TubeFileSetting? tubeFileSetting = templateData.tubeFileSetting;
            if(tubeFileSetting != null) {
              if(tubeFileSetting.autoWidth ?? false) {
                tubeFileSetting.width = 30;
              }
              else {
                tubeFileSetting.width = templateData.width;
              }
            }
            completer.complete(templateData);
          } else {
            completer.complete(null);
          }
        }, onError: (code, message) {
      NiimbotLoadingController().easyLoadingDismiss();
      completer.complete(null);
    });
    return completer.future;
  }

  /// 生成模板文件第一个段落的缩略图
  Future<String> _generateThumbnail(TemplateData templateData) async {
    List<TextElement> textElements = [];
    // 获取value段落以及element样式
    CompositeValueType value = templateData.values![0] as CompositeValueType;
    CompositeValueType columnValue1 = value.valueObjects![0] as CompositeValueType;
    TextElement? element1 =
        (templateData.elements.where((element) => element.id == columnValue1.elementId).toList().first) as TextElement;
    // 生成当前元素的值
    String? resultValue1 = columnValue1.generateElementValue();
    textElements.add(element1.copyWith(value: resultValue1));
    if (templateData.tubeFileSetting?.line == 2) {
      CompositeValueType columnValue2 = value.valueObjects![1] as CompositeValueType;
      TextElement? element2 = (templateData.elements
          .where((element) => element.id == columnValue2.elementId)
          .toList()
          .first) as TextElement;
      // 生成当前元素的值
      String? resultValue2 = columnValue2.generateElementValue();
      textElements.add(element2.copyWith(value: resultValue2));
    }
    // 遍历元素，设置文本元素以及竖排文本的height和Y值
    List<Map<String, dynamic>> elementJsons = textElements.map((element) {
      // 竖排时设置宽度为管长｜｜100防止换行的情况，调整后更新Y轴的坐标，图像库默认会根据具体文本进行实际的长度裁剪，横排则不变
      num height = element.height < templateData.width ? templateData.width : 100;
      num y = element.typesettingMode == NetalTypesettingMode.vertical
          ? -(height - element.height) / 2 + element.y
          : element.y;
      num elementHeight = element.typesettingMode == NetalTypesettingMode.vertical ? height : element.height;
      // 竖排下预览默认是居中的
      NetalTextAlign textAlignVertical =
          element.typesettingMode == NetalTypesettingMode.vertical ? NetalTextAlign.center : element.textAlignVertical;
      TextElement newElement = element.copyWith(y: y, height: elementHeight, textAlignVertical: textAlignVertical);
      Map<String, dynamic> json = newElement.toNetal().copyWith(value: newElement.value).toJson();
      json['lineMode'] = 7;
      return json;
    }).toList();
    Color? elementColor = TemplateDataTransform.ribbonColor;
    String localBackgroundImageUrl = '';
    try {
      FileResponse response = await DefaultCacheManager().getImageFile(templateData.backgroundImage).first;
      if (response is FileInfo) {
        localBackgroundImageUrl = response.file.path;
      }
    } catch (e) {
      localBackgroundImageUrl = '';
    }
    NetalImageResult imageResult = NiimbotNetal.generateImageFromPrintJson(
      jsonString: jsonEncode({
        'width': templateData.width.toDouble(),
        'height': templateData.height.toDouble(),
        'usedFonts': NetalPlugin().getUsedFonts(),
        'elements': elementJsons,
        'backgroundImage': '',
        'localBackgroundImageUrl': localBackgroundImageUrl
      }),
      ratio: TemplateDataTransform.pxRatio,
      printRatio: TemplateDataTransform.pxRatio,
      printerMargin: [0, 0, 0, 0],
      printerOffset: [0, 0],
      orientation: 0,
      color: elementColor,
    );
    var imageData = imageResult.pixels;
    if (imageData.length > 0) {
      String imagePath = (await getApplicationDocumentsDirectory()).path + "/c1_thumbnail";
      if (!Directory(imagePath).existsSync()) {
        Directory(imagePath).createSync(recursive: true);
      }
      File thumbnailFile = File("$imagePath/${templateData.id}.png");
      await thumbnailFile.writeAsBytes(imageData, flush: true);
      return thumbnailFile.path;
    }
    return "";
  }

  Future<bool> _checkNetworkConnected() async {
    ConnectivityResult connectivityResult = await Connectivity().customCheckConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  List<String> toServiceTemplateIdList = [];

  /// 模板文件未同步的本地操作同步到服务端
  localFileToService() async {
    List<TemplateData> templateList = await NiimbotTemplateDbUtils.queryAllLocalTemplateList();
    templateList.forEach((template) {
      String templateId = template.id!;
      int localType = template.local_type.toInt();
      if (toServiceTemplateIdList.contains(template.id!)) {
        return;
      }
      if (localType == NiimbotTemplateDbUtils.CREATE) {
        toServiceTemplateIdList.add(templateId);
        _uploadThumbnailIfNecessary(template, success: (uploadTemplate) {
          createTemplateToService(uploadTemplate, success: (serviceId) {
            if (toServiceTemplateIdList.contains(templateId)) {
              toServiceTemplateIdList.remove(templateId);
            }
          }, fail: (code, msg) {
            if (toServiceTemplateIdList.contains(templateId)) {
              toServiceTemplateIdList.remove(templateId);
            }
          }, fromBackground: true);
        }, fail: () {
          if (toServiceTemplateIdList.contains(templateId)) {
            toServiceTemplateIdList.remove(templateId);
          }
        });
      } else if (localType == NiimbotTemplateDbUtils.DELETE) {
        toServiceTemplateIdList.add(templateId);
        deleteTemplateToService(template, success: () {
          if (toServiceTemplateIdList.contains(templateId)) {
            toServiceTemplateIdList.remove(templateId);
          }
        }, fail: (code, msg) {
          if (toServiceTemplateIdList.contains(templateId)) {
            toServiceTemplateIdList.remove(templateId);
          }
        }, fromBackground: true);
      } else if (localType == NiimbotTemplateDbUtils.UPDATE) {
        toServiceTemplateIdList.add(templateId);
        _uploadThumbnailIfNecessary(template, success: (uploadTemplate) {
          modifyTemplateToService(uploadTemplate, success: () {
            if (toServiceTemplateIdList.contains(templateId)) {
              toServiceTemplateIdList.remove(templateId);
            }
          }, fail: (code, msg) {
            if (toServiceTemplateIdList.contains(templateId)) {
              toServiceTemplateIdList.remove(templateId);
            }
          }, fromBackground: true);
        }, fail: () {
          if (toServiceTemplateIdList.contains(templateId)) {
            toServiceTemplateIdList.remove(templateId);
          }
        });
      }
    });
  }

  Future<int?> getLastRequestFileListServerTime(int userId) async {
    SharedPreferences sp = await SharedPreferences.getInstance();
    return sp.getInt(_saveServerTimeKey(userId));
  }

  saveRequestFileListServerTime(int userId, int serverTime) async {
    SharedPreferences sp = await SharedPreferences.getInstance();
    sp.setInt(_saveServerTimeKey(userId), serverTime);
  }

  String _saveServerTimeKey(int userId) {
    return "c1_file_list_server_time_$userId";
  }
}
