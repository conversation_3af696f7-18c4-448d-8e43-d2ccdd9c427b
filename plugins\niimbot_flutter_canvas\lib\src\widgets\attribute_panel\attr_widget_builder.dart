import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:niimbot_flutter_canvas/src/model/canvas_element.dart';
import 'package:niimbot_flutter_canvas/src/model/material/material_item.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/barcode_attr_widget_builder.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/common_import_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/layout_panel_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/text_attr_panel_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/text_font_panel_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm_attr_widget_builder.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/date_attr_widget_builder.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/element_attribute_panel.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/graph_attr_widget_builder.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/image_attr_widget_builder.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/line_attr_widget_builder.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/qrcode_attr_widget_builder.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/serial_attr_widget_builder.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/table_attr_widget_builder.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/text_attr_widget_builder.dart';
import 'package:niimbot_flutter_canvas/src/widgets/box_frame/canvas_box_frame.dart';

import '/src/localization/localization_public.dart';
import 'comm/text_font_panel_mini_widget.dart';

/// 属性面板的状态
enum AttrPanelState { normal, highest, shortest }

class RadioModel {
  bool isSelected;
  final String buttonText;
  final String text;

  RadioModel(this.isSelected, this.buttonText, this.text);
}

class MenuTag {
  MenuTag({
    required this.isSelected,
    required this.index,
    required this.text,
    required this.trackName,
  });

  bool isSelected = false;
  int index;
  String text;
  Color? bgColor;
  Color? textColor;
  String trackName;
}

typedef GuideBuyVipCallback = void Function(Function, {bool isPopLogin});

abstract class AttrWidgetBuilder {
  /// 面板状态
  AttrPanelState attrPanelState;
  double height;

  /// 处理替换素材的逻辑
  final Function(CanvasElement, MaterialItem, bool, bool)? onChooseMaterial;
  final ElementValueEditorDisplay? onElementValueEditorDisplay;
  final ImportExcelFromElementCall? importExcelFromElementCall;
  final ChangeDataManualInputField? changeDataManualInputField;
  final ChangeColumnIndexCall? changeColumnIndexCall;

  final GuideBuyVipCallback? guideBuyVip;

  /// 线条变为非线条，或非线条变为线条
  final Function? onGraphElementTypeChanged;

  AttrWidgetBuilder(
    this.attrPanelState,
    this.height, {
    this.onChooseMaterial,
    this.onElementValueEditorDisplay,
    this.importExcelFromElementCall,
    this.changeDataManualInputField,
    this.changeColumnIndexCall,
    this.guideBuyVip,
    this.onGraphElementTypeChanged,
  });

  /// 属性栏
  List<MenuTag> menus = [];

  List<MenuTag> getMenus(List<CanvasElement> canvasElements) {
    return [];
  }

  bool canScroll({required int position, required CanvasElement element}) {
    if (position < menus.length &&
        menus[position].text == intlanguage('app01005', '字体') ||
        menus[position].text == intlanguage('app00010', '时间')) {
      return false;
    }
    return true;
  }

  init(BuildContext context, List<CanvasElement> canvasElements) {}

  dispose() {}

  bool isSwitchTabDisable({required CanvasElement element}) {
    return false;
  }

  /// 构建属性页
  Widget buildAttrWidget(MenuTag menuTag, List<CanvasElement> canvasElements,
      BuildContext context, VoidCallback refresh);

  static AttrWidgetBuilder? createBuilder(
      List<CanvasElement> canvasElements,
      AttrPanelState attrPanelState,
      double height,
      Function(CanvasElement, MaterialItem, bool, bool) onChooseMaterial,
      ElementValueEditorDisplay onElementValueEditorDisplay,
      ImportExcelFromElementCall importExcelFromElementCall,
      ChangeDataManualInputField changeDataManualInputField,
      ChangeColumnIndexCall changeColumnIndexCall,
      Function(Function, {bool isPopLogin}) guideBuyVip,
      Function onGraphElementTypeChanged) {
    Set<String> set = canvasElements.map((e) => e.data.type).toSet();
    if (set.length == 1) {
      String type = set.first;
      if (type == ElementItemType.text) {
        return TextAttrWidgetBuilder(
            attrPanelState,
            height,
            onElementValueEditorDisplay,
            importExcelFromElementCall,
            changeDataManualInputField,
            changeColumnIndexCall,
            guideBuyVip);
      } else if (type == ElementItemType.serial) {
        return SerialAttrWidgetBuilder(attrPanelState, height, guideBuyVip);
      } else if (type == ElementItemType.date) {
        return DateAttrWidgetBuilder(attrPanelState, height, guideBuyVip);
      } else if (canvasElements.length > 1) {
        return CommAttrWidgetBuilder(attrPanelState, height);
      } else if (type == ElementItemType.barcode) {
        return BarcodeAttrWidgetBuilder(
            attrPanelState,
            height,
            onElementValueEditorDisplay,
            importExcelFromElementCall,
            changeDataManualInputField,
            changeColumnIndexCall);
      } else if (type == ElementItemType.qrcode) {
        return QrcodeAttrWidgetBuilder(
            attrPanelState,
            height,
            onElementValueEditorDisplay,
            importExcelFromElementCall,
            changeDataManualInputField,
            changeColumnIndexCall);
      } else if (type == ElementItemType.graph ||
          type == ElementItemType.line) {
        return GraphAttrWidgetBuilder(
            attrPanelState, height, onGraphElementTypeChanged);
      } else if (type == ElementItemType.line) {
        return LineAttrWidgetBuilder(attrPanelState, height);
      } else if (type == ElementItemType.image) {
        //ImageElement element = canvasElements.first.data;
        return ImageAttrWidgetBuilder(attrPanelState, height, onChooseMaterial);
      } else if (type == ElementItemType.table) {
        return TableAttrWidgetBuilder(
            attrPanelState,
            height,
            onElementValueEditorDisplay,
            importExcelFromElementCall,
            changeDataManualInputField,
            changeColumnIndexCall,
            guideBuyVip);
      }
    } else if (set.length > 1) {
      /// 多选
      if ((set.contains(ElementItemType.text) ||
              set.contains(ElementItemType.date) ||
              set.contains(ElementItemType.serial)) &&
          !set.contains(ElementItemType.barcode) &&
          !set.contains(ElementItemType.qrcode) &&
          !set.contains(ElementItemType.graph) &&
          !set.contains(ElementItemType.line) &&
          !set.contains(ElementItemType.image) &&
          !set.contains(ElementItemType.table)) {
        return TextAttrWidgetBuilder(
            attrPanelState,
            height,
            onElementValueEditorDisplay,
            importExcelFromElementCall,
            changeDataManualInputField,
            changeColumnIndexCall,
            guideBuyVip);
      } else {
        return CommAttrWidgetBuilder(attrPanelState, height);
      }
    }
    return null;
  }

  static bool isAttrWidgetMatchElement(AttrWidgetBuilder builder, CanvasElement canvasElement) {
    String type = canvasElement.data.type;
    if(type == ElementItemType.text) {
      return builder is TextAttrWidgetBuilder;
    } else if (type == ElementItemType.serial) {
      return builder is SerialAttrWidgetBuilder;
    } else if (type == ElementItemType.date) {
      return builder is DateAttrWidgetBuilder;
    } else if (type == ElementItemType.line) {
      return builder is LineAttrWidgetBuilder;
    } else if (type == ElementItemType.table) {
      return builder is TableAttrWidgetBuilder;
    } else if (type == ElementItemType.image) {
      return builder is ImageAttrWidgetBuilder;
    } else if (type == ElementItemType.barcode) {
      return builder is BarcodeAttrWidgetBuilder;
    } else if (type == ElementItemType.qrcode) {
      return builder is QrcodeAttrWidgetBuilder;
    } else if (type == ElementItemType.graph) {
      return builder is GraphAttrWidgetBuilder;
    }
    return true;
  }

  /// 对齐
  Widget layoutPanel(List<CanvasElement> canvasElements, BuildContext context,
      VoidCallback? refresh) {
    return AnimatedSwitcher(
        duration: Duration(milliseconds: animatedDurationMilliseconds),
        child: LayoutPanelWidget(
          canvasElements: canvasElements,
          refresh: refresh,
        ));
  }

  /// 文本样式
  Widget textStylePanel(List<CanvasElement> canvasElements,
      BuildContext context, VoidCallback? refresh) {
    return AnimatedSwitcher(
        duration: Duration(milliseconds: animatedDurationMilliseconds),
        child: TextAttrPanelWidget(
          canvasElements: canvasElements,
          refresh: refresh,
        ));
  }

  /// 字体样式
  Widget textFontPanel(List<CanvasElement> canvasElements, BuildContext context,
      VoidCallback? refresh, Function(Function)? guideBuyVip) {
    return AnimatedSwitcher(
        duration: Duration(milliseconds: animatedDurationMilliseconds),
        child: /*attrPanelState == AttrPanelState.shortest*/ false
            ? TextFontPanelMiniWidget(
                canvasElements: canvasElements,
                refresh: refresh,
              )
            : TextFontPanelWidget(
                canvasElements: canvasElements,
                refresh: refresh,
                guideBuyVip: guideBuyVip,
                height: height,
              ));
  }
}
