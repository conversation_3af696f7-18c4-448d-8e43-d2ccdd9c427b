import 'dart:convert';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/rfid_bind/rfid_info.dart';
import 'package:niimbot_template/rfid_bind/rfid_repository.dart';
// import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:popover/popover.dart';
import 'package:text/application.dart';
import 'package:text/macro/constant.dart';
import 'package:text/pages/industry_template/home/<USER>/label_usage_record.dart';
import 'package:text/pages/my_template/abstract/getx_controller_abstract.dart';
import 'package:text/pages/my_template/model/folder_model.dart';
import 'package:text/pages/my_template/model/template_data_extension.dart';
import 'package:text/pages/my_template/my_template_state.dart';
import 'package:text/pages/my_template/page/personal_template/controller/personal_template_state.dart';
import 'package:text/template/util/template_misc_utils.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/hardware_manager.dart';
import 'package:text/utils/text_utils.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/utils/toast_util.dart';
import 'package:text/vipTrial/trial_activity.dart';
import 'package:text/vipTrial/vip_trial_manager.dart';

import '../../../../../business/user/user_login_helper.dart';
import '../../../../../network/entity/user.dart';
import '../../../../../routers/custom_navigation.dart';
import '../../../../create/model/printer_connect_state.dart';

class PersonalTemplateLogic extends GetxControllerAbstract {
  final PersonalTemplateState personalTemplateState = PersonalTemplateState();
  static String maxSupportVersion = Application.maxSupportTemplateVersion;

  /**
   * 初始化函数，准备就绪后调用
   */
  @override
  void onReady() {
    super.onReady();
    _getLabelData();
    _hardWareWithLabelListener();
    Future.delayed(Duration(milliseconds: 500), () {
      if (Application.user != null) {
        var batchName = ConstantKey.firstBatchPrintToast + (Application.user?.id ?? -1).toInt().toString();
        var isShow = Application.sp.getBool(batchName) ?? false;
        if (!isShow) {
          Application.sp.setBool(batchName, true);
          _smartTypesettingGuide(personalTemplateState.batchPrintKey.currentContext!);
        }
      }
    });
    NiimbotEventBus.getDefault().register(this, (data) async {
      if (data is Map && data.containsKey('networkChanged')) {
        if (personalTemplateState.connectState() != data['networkChanged']) {
          personalTemplateState.connectState.value = data['networkChanged'];
        }
      }
    });
    // 设置初始网络状态
    (Connectivity().customCheckConnectivity()).then((value) {
      if (value == ConnectivityResult.none || value == ConnectivityResult.bluetooth) {
        personalTemplateState.connectState.value = false;
      }
    });
  }

  @override
  void onClose() {
    super.onClose();
    NiimbotEventBus.getDefault().unregister(this);
  }

  //批量打印
  batchPrint(BuildContext context) {
    (Connectivity().customCheckConnectivity()).then((value) {
      if (Application.user == null) {
        if (value == ConnectivityResult.none || value == ConnectivityResult.bluetooth) {
          showToast(msg: intlanguage('app100000625', '当前网络状态异常'));
          return;
        }
        String title = intlanguage('app00210', '当前未登录，请先登录！');
        String cancelDes = intlanguage('app00030', '取消');
        String confirmDes = intlanguage('app01191', '立即登录');
        UserLoginHelper().confirmLogin(context, title, cancelDes, confirmDes, loginSucceed: () {
          Future.delayed(const Duration(milliseconds: 1000), () {
            if (Application.user != null) {
              // 先更新文件夹列表，等待完成后再加载模板
              _updateFolderListAndLoadTemplates();
              changeBatchPrintState();
              checkVipToast();
            }
          });
        });
      } else {
        changeBatchPrintState();
      }
    });
  }

  /// 更新文件夹列表并加载模板
  /// 确保文件夹列表加载完成后再加载模板列表，避免时序问题
  void _updateFolderListAndLoadTemplates() {
    folderManager.getAll(state.folderPage, (list) {
      state.folderList.clear();
      state.folderCoverList.clear();
      state.folderList.add(FolderModel(name: intlanguage('app00659', '新建文件夾'), id: -1, isAdd: true));
      state.folderList.add(FolderModel(name: intlanguage('app100001185', '未整理文件夹'), id: 0, isDefault: true));
      state.folderCoverList.add(FolderModel(name: intlanguage('app00659', '新建文件夾'), id: -1, isAdd: true));
      if (list.isNotEmpty) {
        state.folderList.addAll(list);
      }
      refreshFolderList();

      // 文件夹列表更新完成后，再加载模板列表
      if (state.folderList.isNotEmpty && state.folderIndex < state.folderList.length) {
        getTemplateList(folderId: state.folderList[state.folderIndex].id!.toInt());
      }
    });
  }

  /**
   * 改变批量打印状态
   */
  changeBatchPrintState() {
    // 改变操作状态
    state.operateType = state.operateType == TemplateOperateState.batchPrint
        ? TemplateOperateState.normal
        : TemplateOperateState.batchPrint;

    // 取消下载模板详情
    if (state.operateType != TemplateOperateState.batchPrint) {
      ToNativeMethodChannel().cancelDownloadTemplateDetails();
    }
    state.myTemplateSelectList.forEach((element) {
      element.printPaper = 0;
    });
    // 清空模板选择列表
    state.myTemplateSelectList.clear();

    // 取消全选状态
    state.selectAllState = false;

    // 清空个人模板状态提示
    personalTemplateState.showToast = "";

    // 更新状态
    update();
  }

  /// 增加模板份数
  addTemplateCopies(TemplateData data) {
    // 根据id筛选出模板数据列表
    List<TemplateData> templates = state.myTemplateSelectList.where((element) => element.id == data.id).toList();

    // 如果模板数据列表为空
    if (templates.isEmpty) {
      if (state.myTemplateSelectList.length > 999) {
        showToast(msg: intlanguage('app100001278', '请选择少于1000的模板数量'));
        return;
      }

      data.printPaper = 1;

      // 如果打印纸张数量超过999，设置为999
      if (data.printPaper > 999) {
        data.printPaper = 999;
      }
      // 添加模板数据到列表中
      state.myTemplateSelectList.add(data);

      // 下载模板详情
      ToNativeMethodChannel().downloadTemplateDetails([data.id!]);

      ///快捷打印 模板顺序调整为选择时顺序 即 无需对模板排序
      /*
      // 根据更新时间对模板数据进行排序
      state.myTemplateSelectList.sort((a, b) {
        DateTime dateA = DateTime.parse(a.profile.extrain.updateTime ?? "");
        DateTime dateB = DateTime.parse(b.profile.extrain.updateTime ?? "");
        return dateB.compareTo(dateA);
      });*/
    } else {
      // 增加打印纸张数量
      data.printPaper = templates.first.printPaper;
      data.printPaper += 1;

      // 如果打印纸张数量超过999，设置为999
      if (data.printPaper > 999) {
        data.printPaper = 999;
      }
      // 更新模板数据的打印纸张数量
      templates.first.printPaper = data.printPaper;
    }

    // 刷新模板列表
    refreshTemplateList();

    // 检查模板大小
    checkTemplateSize();

    // 刷新全选状态
    refreshAllSelectState();
  }

  /// 编辑模板份数
  editTemplateCopies(String id, int size) {
    // 根据id筛选出模板数据列表
    List<TemplateData> templates = state.myTemplateSelectList.where((element) => element.id == id).toList();

    // 如果模板数据列表不为空
    if (templates.isNotEmpty) {
      // 更新模板数据的打印纸张数量
      templates.first.printPaper = size;

      // 如果打印纸张数量小于1，移除模板数据并删除模板详情
      if (templates.first.printPaper < 1) {
        state.myTemplateSelectList.remove(templates.first);
        ToNativeMethodChannel().removeTemplateDetails([templates.first.id!]);
      }

      // 更新模板数据
      update();

      // 检查模板大小
      checkTemplateSize();

      // 刷新模板列表
      refreshTemplateList();

      // 刷新全选状态
      refreshAllSelectState();
    }
  }

  /// 删除模板份数
  deleteTemplateCopies(String id, FocusNode printFocusNode) {
    // 根据id筛选出模板数据列表
    List<TemplateData> templates = state.myTemplateSelectList.where((element) => element.id == id).toList();

    // 如果模板数据列表不为空
    if (templates.isNotEmpty) {
      // 获取模板数据的打印纸张数量
      var printSize = templates.first.printPaper;

      // 更新模板数据的打印纸张数量，减1
      templates.first.printPaper = printSize - 1;

      // 如果打印纸张数量小于1，移除模板数据并删除模板详情，同时失去焦点
      if (templates.first.printPaper < 1) {
        state.myTemplateSelectList.remove(templates.first);
        ToNativeMethodChannel().removeTemplateDetails([templates.first.id!]);
        printFocusNode.unfocus();
      }

      // 更新模板数据
      update();

      // 检查模板大小
      checkTemplateSize();

      // 刷新模板列表
      refreshTemplateList();

      // 刷新全选状态
      refreshAllSelectState();
    }
  }

  ///删除批量打印模板

  deleteBatchTemplate(String id) {
    // 根据id筛选出模板数据列表
    List<TemplateData> templates = state.myTemplateSelectList.where((element) => element.id == id).toList();

    // 如果模板数据列表不为空
    if (templates.isNotEmpty) {
      // 将模板数据的打印纸张数量设置为0
      templates.first.printPaper = 0;

      // 移除模板数据
      state.myTemplateSelectList.remove(templates.first);

      // 删除模板详情
      ToNativeMethodChannel().removeTemplateDetails([templates.first.id!]);

      // 检查模板大小
      checkTemplateSize();
    }

    // 更新模板数据
    update();

    // 刷新模板列表
    refreshTemplateList();

    // 刷新全选状态
    refreshAllSelectState();
  }

  //数据源模板校验
  checkTemplateDataSource(TemplateData model) {
    // if (model.externalData != null &&
    //     model.externalData!.externalDataList != null &&
    //     (model.externalData!.externalDataList ?? []).isNotEmpty) {
    //   return true;
    // }

    if (state.operateType == TemplateOperateState.batchPrint) {
      if (model.dataSources != null && (model.dataSources ?? []).length > 0) {
        return true;
      }

      if (model.commodityTemplate || model.profile.extra.templateType == 2) {
        return true;
      }

      if (model.totalPage > 1) {
        return true;
      }
      if(model.elementsJsonStr?.contains('"type":"serial"') ?? false){
        return true;
      }
      bool containsString = model.elements.any((element) => element.type == "serial");
      if (containsString) {
        return true;
      }

      return false;
    } else {
      return false;
    }
  }

  ///通过当前模板 版本号判断是否需要升级
  bool isTemplateNeedUpgrade(TemplateData templateData) {
    String templateVersion = (templateData.templateVersion == null || (templateData.templateVersion ?? "").isEmpty)
        ? "0.0.0.0"
        : templateData.templateVersion!;
    return isUpdateVersion(templateVersion, maxSupportVersion);
  }

  bool isUpdateVersion(String newVersion, String old) {
    int newVersionInt, oldVersion;
    var newList = newVersion.split('.');
    var oldList = old.split('.');
    if (newList.length == 0 || oldList.length == 0 || newList.length != oldList.length) {
      return false;
    }
    try {
      for (int i = 0; i < newList.length; i++) {
        newVersionInt = int.parse(newList[i]);
        oldVersion = int.parse(oldList[i]);
        if (newVersionInt > oldVersion) {
          return true;
        } else if (newVersionInt < oldVersion) {
          return false;
        }
      }
    } catch (e, s) {
      debugPrint('异常信息:\n $e');
    }
    return false;
  }

  ///模板大小检查
  checkTemplateSize() {
    personalTemplateState.showToast = ""; // 初始化提示信息为空

    // 如果已选择的模板列表不为空
    if (state.myTemplateSelectList.length > 0) {
      // 如果连接模型不为空
      if (personalTemplateState.ConnectingModel != null) {
        state.myTemplateSelectList.forEach((templateData) {
          var connectWidth = personalTemplateState.ConnectingModel!.width; // 获取连接模型的宽度
          var connectHeight = personalTemplateState.ConnectingModel!.height; // 获取连接模型的高度
          // 如果模板的宽度或高度与连接模型的宽度或高度不一致
          if (templateData.width != connectWidth || templateData.height != connectHeight) {
            personalTemplateState.showToast = intlanguage('app100001244', '与已安装标签纸尺寸不一致，请选择\$x\$尺寸的模版', param: [
              JCTextUtils.getRealitySize(connectWidth.toString().toString()), // 获取现实尺寸的宽度
              JCTextUtils.getRealitySize(connectHeight.toString().toString()) // 获取现实尺寸的高度
            ]);
          }
        });
      } else {
        var list = state.myTemplateSelectList; // 获取已选择的模板列表
        list.forEach((templateData) {
          // 如果模板的宽度或高度与列表中的第一个模板的宽度或高度不一致
          if (templateData.width != list.first.width || templateData.height != list.first.height) {
            personalTemplateState.showToast = intlanguage('app100001240', '已选模版包含多个不同尺寸，请选择同一尺寸的模版');
          }
        });
      }
    }

    // 更新提示信息
    update([RefreshManager.sizeErrorToast]);
  }

  ///模板大小检查
  checkVipToast() {
    personalTemplateState.showTopToast = ""; // 初始化提示信息为空
    if (!Application.user!.isVip) {
      ToNativeMethodChannel.getUnVipBatchPrintCount().then((value) {
        value = 10 - value;
        if (value < 0) {
          value = 0;
        }
        personalTemplateState.showTopToast = intlanguage('app100001290', '非会员可试用【\$次】快捷打印，当前剩余【\$次】', param: [
          10.toString(),
          JCTextUtils.getRealitySize(value.toString()),
        ]);
        update([RefreshManager.notVipToast]);
      });
    } else {
      update([RefreshManager.notVipToast]);
    }
  }

  ///批量打印
  batchPrintTemplates(BuildContext context) {
    // 防重复点击检查
    if (personalTemplateState.isBatchPrinting) {
      return;
    }

    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "007_256_237",
    });

    // 设置批量打印状态，防止重复点击
    personalTemplateState.isBatchPrinting = true;
    Future.delayed(Duration(seconds: 3), () {
      // 重置批量打印状态
      personalTemplateState.isBatchPrinting = false;
    });
    // 默认此时弹窗展示
    personalTemplateState.isShowVIPTrailing = true;
    // 权益试用
    VipTrialManager().trialActivity(
        context: context,
        trialActivityType: TrialActivityType.batchPrint,
        trailActivity: () {
          personalTemplateState.isShowVIPTrailing = false;
          // 当前状态只有是批量打印才继续，否则不处理，防止网络回调慢导致的意外弹出甚至崩溃
          if (state.operateType == TemplateOperateState.batchPrint) {
            _toBatchPrintSettingPage(context);
          }
        },
        dismissCallback: () {
          personalTemplateState.isShowVIPTrailing = false;
        });
  }

  onPageHideAction(BuildContext context) {
    // 正在展示弹窗且当前非最顶层Route，弹出Page
    if (personalTemplateState.isShowVIPTrailing && !(ModalRoute.of(context)?.isCurrent ?? true)) {
      // 尝试弹出
      Navigator.of(context).maybePop();
      personalTemplateState.isShowVIPTrailing = false;
    }
  }

  /**
   * 跳转到批量打印设置页面
   */
  _toBatchPrintSettingPage(BuildContext context) async {
    // 获取模板列表
    List<TemplateData> templates = state.myTemplateSelectList;
    // 试用慢网络回调问题
    if (templates.isEmpty) return;

    ///快捷打印 模板顺序调整为选择时顺序 即 无需对模板排序
    /*
    // 根据更新时间对模板进行排序
    state.myTemplateSelectList.sort((a, b) {
      DateTime dateA = DateTime.parse(a.profile.extrain.updateTime ?? "");
      DateTime dateB = DateTime.parse(b.profile.extrain.updateTime ?? "");
      return dateB.compareTo(dateA);
    });*/
    List<Map> names2 = templates.map((e) {
      return {"name": e.name, "uodateTime": e.profile.extra.updateTime};
    }).toList();
    String rfidInfoJson = "";
    RfidInfo? rfidInfo = RfidRepository().getRfidInfo();
    if (rfidInfo != null) {
      rfidInfoJson = jsonEncode(rfidInfo.toJson());
    }
    var printer = await HardWareManager.instance().getPrioritizationHardware();
    bool isShowRFID = printer?.isSupportRecordRfid() ?? false;
    // 将模板列表转换为用于打印的设置信息列表
    List<Map> printInfos = templates.map((e) {
      return {
        "id": e.id,
        "printPage": e.printPaper,
      };
    }).toList();
    // 检查网络连接状态
    // if (!Application.networkConnected) {
    //   // 检查模板资源是否完整
    //   ToNativeMethodChannel.checkTemplateResourceComplate(templates.map((e) => e.id!).toList()).then((value) {
    //     if (value) {
    //       // 连接网络后跳转到批量打印设置页面
    //       CustomNavigation.gotoNextPage(
    //           'toBatchPrintSettingPage', {'printInfos': printInfos, 'showRfid': isShowRFID, 'rfidInfo': rfidInfoJson});
    //     } else {
    //       // 显示提示消息，存在未下载的云端资源
    //       showCenterToast(context, intlanguage('app100001256', '存在未下载的云端资源，请连接网络'));
    //       return;
    //     }
    //   });
    // } else {
    //   // 直接跳转到批量打印设置页面
    //   CustomNavigation.gotoNextPage(
    //       'toBatchPrintSettingPage', {'printInfos': printInfos, 'showRfid': isShowRFID, 'rfidInfo': rfidInfoJson});
    // }

    if (!Application.networkConnected) {
      // 检查模板资源是否完整
      bool isResourceComplete =
          await TemplateMiscUtils.checkTemplateResourceComplate(templates.map((e) => e.id!).toList());
      if (isResourceComplete) {
        // 连接网络后跳转到批量打印设置页面
        CustomNavigation.gotoNextPage(
            'toBatchPrintSettingPage', {'printInfos': printInfos, 'showRfid': isShowRFID, 'rfidInfo': rfidInfoJson});
      } else {
        // 显示提示消息，存在未下载的云端资源
        showCenterToast(context, intlanguage('app100001256', '存在未下载的云端资源，请连接网络'));
        return;
      }
    } else {
      // 直接跳转到批量打印设置页面
      CustomNavigation.gotoNextPage(
          'toBatchPrintSettingPage', {'printInfos': printInfos, 'showRfid': isShowRFID, 'rfidInfo': rfidInfoJson});
    }
  }

  void _handleOpenVip(BuildContext context, VoidCallback openVipCallback) {
    CustomNavigation.gotoNextPage('ToVipPage', {}).then((value) {
      if (value is Map && value['result'] is int && value['result'] > 0) {
        /// 已经开通 vip
        openVipCallback.call();
      }
    });
  }

  /// 获取标签纸信息
  _getLabelData() {
    ToNativeMethodChannel().getPrinterLabelData().then((value) {
      if (value is Map) {
        if (value.isNotEmpty) {
          // 当前已连接机器且存在标签纸
          final _dataMap = Map<String, dynamic>.from(value);
          personalTemplateState.ConnectingModel = LabelUsageRecord.fromJson(_dataMap);
        }
      }
      checkTemplateSize();
    });
  }

  /// 打印机、标签纸、网络状态监听
  _hardWareWithLabelListener() {
    NiimbotEventBus.getDefault().register(this, (data) {
      if (data is Map && data.containsKey('labelData') && data['labelData'] is Map) {
        // 标签纸状态更新
        if ((data['labelData'] as Map).isNotEmpty) {
          final _dataMap = Map<String, dynamic>.from(data['labelData']);
          var labelModel = LabelUsageRecord.fromJson(_dataMap);
          // 更新当前连接的标签纸
          personalTemplateState.ConnectingModel = labelModel;
        } else {
          // 未识别到纸，取出纸
          personalTemplateState.ConnectingModel = null;
        }
        checkTemplateSize();
      } else if (data is Map && data.containsKey('printerConnectState')) {
        var printerState = PrinterConnectState.fromJson(Map<String, dynamic>.from(data['printerConnectState']));
        // 确认当前处于连接状态
        if ((printerState.connected ?? 0) != 1) {
          personalTemplateState.ConnectingModel = null;
        }
        checkTemplateSize();
      }
    });
  }

  void _smartTypesettingGuide(BuildContext context) {
    personalTemplateState.smartIsPop = true;
    showPopover(
        context: context,
        transitionDuration: Duration(milliseconds: 1),
        bodyBuilder: (context) {
          return Container(
            width: 250,
            padding: EdgeInsetsDirectional.fromSTEB(18, 13, 10, 12),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  intlanguage('app100001279', '新功能：快捷打印'),
                  style: TextStyle(color: ThemeColor.mainTitle, fontSize: 15, fontWeight: FontWeight.w600),
                ),
                const SizedBox(
                  height: 1,
                ),
                Text(
                  intlanguage('app100001250', '支持多个模板一起打印'),
                  style: TextStyle(color: ThemeColor.subtitle, fontSize: 14, fontWeight: FontWeight.w400),
                ),
              ],
            ),
          );
        },
        shadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 55,
            spreadRadius: 0.1,
          ),
        ],
        radius: 12,
        direction: PopoverDirection.bottom,
        width: 250,
        arrowHeight: 10,
        arrowWidth: 18,
        contentDyOffset: 10,
        barrierColor: Colors.transparent,
        routeSettings: RouteSettings(name: "smartPopover"),
        onPop: () {
          personalTemplateState.smartIsPop = false;
        });
  }

  ///vip开通续费弹窗
  showBatchPrintVipAlert(BuildContext context, VoidCallback cancelAction, VoidCallback confirmAction,
      {int maxUseCount = 10}) {
    String headImagePath = "";
    String unVipRightDesc = "";
    String vipRightDesc = "";
    String title = "";
    String subTile = "";
    switch (Application.user!.vipType) {
      ///未开通过VIP
      case VIPType.neverPurchase:
        headImagePath = 'assets/images/my_template/batch_print_vip_upgrade_bg.svg';
        unVipRightDesc = intlanguage("app100001286", '10次', param: [maxUseCount.toString()]);
        vipRightDesc = intlanguage("app100001285", '无限次');
        title = intlanguage("app100001287", '开通会员解锁无限次快捷打印');
        subTile = intlanguage("app100001288", '非会员仅可试用10次', param: [maxUseCount.toString()]);
        showCustomOpenVipGuideAlert(context,
            headImagePath: headImagePath,
            unVipRightDesc: unVipRightDesc,
            vipRightDesc: vipRightDesc,
            title: title,
            subTile: subTile,
            cancelCallBack: cancelAction,
            confirmCallBack: confirmAction);
        break;

      ///VIP过期
      case VIPType.expired:
        title = intlanguage("app100000199", '您的会员已到期');
        subTile = intlanguage("app100001288", '非会员仅可试用10次', param: [maxUseCount.toString()]) +
            "," +
            intlanguage("app100001289", '续费会员解锁无限次快捷打印');
        showCustomRenewVipGuideAlert(context, title, subTile: subTile, cancelAction, confirmAction);
        break;

      ///VIP有效
      case VIPType.valid:
        break;
    }
  }

  toPrintSettingPage(BuildContext context, TemplateData model,TemplateOperateState operateType, {bool isFolderShare = false}) async {
    super.toPrintSettingPage(context, model,operateType,isFolderShare: isFolderShare);
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "007_447_441",
      'ext': {"temp_id": model.id, "folder_name": state.folderList[state.folderIndex].name}
    });
  }
}
