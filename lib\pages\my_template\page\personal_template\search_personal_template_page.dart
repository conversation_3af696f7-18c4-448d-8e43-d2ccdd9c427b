import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:niim_login/login_plugin/macro/color.dart';
import 'package:niim_login/login_plugin/utils/image_utils.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:text/application.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_state.dart';
import 'package:text/pages/my_template/controller/search_template_manager.dart';
import 'package:text/pages/my_template/my_template_state.dart';
import 'package:text/pages/my_template/page/personal_template/controller/personal_template_logic.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/log_utils.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';

import '../../widget/my_template_item_widget.dart';

class SearchPersonalTemplatePage extends StatefulWidget {
  final TemplateOperateState operateType;
  SearchPersonalTemplatePage({Key? key, this.operateType = TemplateOperateState.normal}) : super(key: key);

  @override
  State<SearchPersonalTemplatePage> createState() => _SearchPersonalTemplatePageState();
}

class _SearchPersonalTemplatePageState extends State<SearchPersonalTemplatePage> {
  @override
  void initState() {
    SearchTemplateManager _controller = SearchTemplateManager();
    _controller.operateType.value = widget.operateType;
    Get.put(_controller);
    super.initState();
    _controller.updateHistroyList(this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: false,
      body: Container(
        margin: const EdgeInsets.symmetric(horizontal: 0),
        padding: EdgeInsets.only(top: MediaQuery.paddingOf(context).top),
        child: Column(
          children: [
            GetBuilder<SearchTemplateManager>(
              builder: (controller) {
                return _AppBarContent();
              },
            ),
            Expanded(
              child: GetX<SearchTemplateManager>(
                builder: (controller) {
                  return Container(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 250),
                      child: controller.fade.value == CrossFadeState.showFirst &&
                              controller.searchState != IndustryHomeState.showContainer
                          ? Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                controller.searchList.value!.length > 0 ? _HistoryBanner() : Container(),
                                _HistoryItem(),
                              ],
                            )
                          : _ResultList(),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _AppBarContent extends StatefulWidget {
  const _AppBarContent({Key? key}) : super(key: key);

  @override
  State<_AppBarContent> createState() => _AppBarContentState();
}

class _AppBarContentState extends State<_AppBarContent> {
  final TextEditingController _textController = TextEditingController();

  final SearchTemplateManager _controller = Get.find<SearchTemplateManager>();

  final FocusNode node = FocusNode();

  @override
  void initState() {
    _controller.addEventListen((event) {
      _textController.text = event;
      Get.focusScope?.unfocus();
      _submittedAction(event);
    });
    super.initState();
    _textController.addListener(() {
      _controller.update([1]);
    });
    node.addListener(() {
      if (node.hasFocus) {
        _controller.fade.value = CrossFadeState.showFirst;
        _controller.searchState = IndustryHomeState.loading;
        _controller.update();
      }
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    Get.delete<SearchTemplateManager>();
    super.dispose();
  }

  /// 提交Action
  _submittedAction(String text) {
    if (text.isEmpty) {
      return;
    }
    text = text.trim();
    if (text.isNotEmpty) {
      // 存储一次记录
      _controller.saveSearchText(this, text);
    }
    // 获取搜索结果
    _controller.getSearchResult(
        searchKey: text,
        dbresultClosure: (p0) {
          _controller.fade.value = CrossFadeState.showSecond;
          _controller.update();
        },
        resultClosure: (model) {
          _controller.fade.value = CrossFadeState.showSecond;
          _controller.update();
        });
  }

  /// 取消事件
  _backAction() {
    _textController.clear();
    CustomNavigation.pop();
  }

  /// 清除事件
  _clearAction() {
    _textController.clear();
    _controller.fade.value = CrossFadeState.showFirst;
    node.requestFocus();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsetsDirectional.symmetric(vertical: 5.0),
      height: 44,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 返回
          GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: _backAction,
              child: Container(
                padding: const EdgeInsetsDirectional.fromSTEB(14, 5, 8, 5),
                child: const SvgIcon(
                  'assets/images/industry_template/search_template/search_bar_back.svg',
                  matchTextDirection: true,
                ),
              )),
          // 搜索栏
          Expanded(
            flex: 2,
            child: Stack(
              children: [
                CupertinoTextField(
                  enabled: !_controller.isSearching.value,
                  focusNode: node,
                  autofocus: true,
                  controller: _textController,
                  cursorColor: KColor.RED,
                  cursorHeight: 18,
                  onSubmitted: _submittedAction,
                  placeholder: intlanguage('app00290', '搜索模板名称'),
                  placeholderStyle: const TextStyle(
                    color: Color(0xFF999999),
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'PingFangSC-Regular',
                  ),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'PingFangSC-Regular',
                  ),
                  prefix: const Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(14, 8, 8, 8),
                    child: Image(
                      image: AssetImage('assets/images/industry_template/home/<USER>'),
                      matchTextDirection: true,
                    ),
                  ),
                  suffix: Padding(
                      padding: const EdgeInsetsDirectional.only(end: 8),
                      child: GetBuilder<SearchTemplateManager>(
                          id: 1,
                          builder: (controller) {
                            return Visibility(
                              visible: _textController.text.isNotEmpty,
                              child: GestureDetector(
                                  onTap: _clearAction,
                                  child: const SvgIcon(
                                      'assets/images/industry_template/search_template/search_clear.svg')),
                            );
                          })),
                  textInputAction: TextInputAction.search,
                  decoration: BoxDecoration(color: const Color(0xFFF7F7F7), borderRadius: BorderRadius.circular(20)),
                ),
              ],
            ),
          ),

          GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () => _controller.sendEvent(_textController.text),
              child: Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(10, 5, 16, 5),
                child: Text(
                  intlanguage('app00226', '搜索'),
                  style: const TextStyle(fontSize: 15.0, fontWeight: FontWeight.w400, color: ThemeColor.brand),
                ),
              ))
        ],
      ),
    );
  }
}

class _HistoryBanner extends StatelessWidget {
  final SearchTemplateManager _controller = Get.find<SearchTemplateManager>();

  _HistoryBanner({Key? key}) : super(key: key);

  _tapAction() {
    _controller.deleteAllHistory();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 16, bottom: 10),
      child: Row(
        children: [
          Text(
            intlanguage('app100000502', '历史记录'),
            style: const TextStyle(fontSize: 14.0, fontWeight: FontWeight.w600, color: KColor.title),
          ),
          const Spacer(),
          GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: _tapAction,
              child: const Padding(
                padding: EdgeInsets.all(8.0),
                child: SizedBox(
                    width: 15,
                    height: 17,
                    child: SvgIcon('assets/images/industry_template/search_template/delete_gray.svg')),
              ))
        ],
      ),
    );
  }
}

class _HistoryItem extends StatelessWidget {
  final _controller = Get.find<SearchTemplateManager>();

  _HistoryItem({Key? key}) : super(key: key);

  /// 点击事件
  _tapAction(int index) {
    var text = _controller.searchList.value?[index] ?? '';
    _controller.sendEvent(text);
    _controller.insertSearchTextToFront(index);
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: GetX<SearchTemplateManager>(builder: (controller) {
        return ConstrainedBox(
          constraints: BoxConstraints(minWidth: context.width - 32),
          child: SingleChildScrollView(
            child: Wrap(
              spacing: 10,
              runSpacing: 10,
              children: List.generate(controller.searchList.value?.length ?? 0, (index) {
                return GestureDetector(
                  onTap: () => _tapAction(index),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(61.0),
                          border: Border.all(color: KColor.color_divider, width: 0.5)),
                      child: Text(
                        controller.searchList.value?[index] ?? '',
                        style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: KColor.title),
                      )),
                );
              }),
            ),
          ),
        );
      }),
    );
  }
}

class _ResultList extends StatelessWidget {
  final _controller = Get.find<SearchTemplateManager>();
  final PersonalTemplateLogic personalLogic = Get.find<PersonalTemplateLogic>();
  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  /// 模版滑动管理器
  final ScrollController _scrollController = ScrollController();

  int page = 1;

  _ResultList({Key? key}) : super(key: key);

  _onRefresh() {
    Log.d('-----_onRefresh-----');
    // 拉取新一页的page
    page = 1;
    _refreshController.resetNoData();
    String searchText = _controller.searchList.value?.first ?? '';
    searchText = searchText.trim();
    _controller.getSearchResult(
        page: page,
        searchKey: _controller.searchList.value?.first ?? '',
        isMoreData: false,
        dbresultClosure: (p0) {
          if (!Application.networkConnected && p0!.length < page * 20) {
            if (p0.isEmpty && _controller.templatesResult.isEmpty) {
              _refreshController.refreshFailed();
            } else {
              _refreshController.refreshCompleted();
            }
          } else if (p0!.length == page * 20) {
            _refreshController.refreshCompleted();
          }
          _controller.update();
        },
        resultClosure: (model) {
          if (model == null) {
            _refreshController.refreshFailed();
          } else {
            _refreshController.refreshCompleted();
          }
          _controller.update();
        });
  }

  _onLoading() {
    Log.d('-----_onLoading----');
    // 拉取新一页的page
    String searchText = _controller.searchList.value?.first ?? '';
    searchText = searchText.trim();
    _controller.getSearchResult(
        page: page += 1,
        searchKey: _controller.searchList.value?.first ?? '',
        isMoreData: true,
        dbresultClosure: (p0) {
          if (!Application.networkConnected && p0!.length < page * 20) {
            if (p0.isEmpty && _controller.templatesResult.isEmpty) {
              _refreshController.loadFailed();
            } else {
              _refreshController.loadNoData();
            }
          } else if (p0!.length == page * 20) {
            _refreshController.loadComplete();
          } else if (p0.length == 0) {
            _refreshController.loadNoData();
          }
          _controller.update();
        },
        resultClosure: (model) {
          if (model == null) {
            _refreshController.loadFailed();
          } else if (model.list!.isEmpty) {
            _refreshController.loadNoData();
          } else {
            _refreshController.loadComplete();
          }
          _controller.update();
        });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SearchTemplateManager>(
      builder: (controller) {
        return _searchListState(controller, context);
      },
    );
  }

  _searchListState(SearchTemplateManager controller, BuildContext context) {
    if (controller.searchState != IndustryHomeState.error) {
      return controller.templatesResult.isEmpty
          ? Container(
              padding: const EdgeInsets.only(bottom: 180),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: (controller.templatesResult.isEmpty && controller.isSearching.value == true)
                    ? []
                    : [
                        Image.asset(
                          ImageUtils.getImgPath('no_data'),
                          fit: BoxFit.contain,
                        ),
                        Text(
                          intlanguage('app100000515', '暂无相关结果'),
                          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
                        ),
                      ],
              ))
          : SmartRefresher(
              enablePullDown: false,
              enablePullUp: true,
              controller: _refreshController,
              onLoading: _onLoading,
              child: MasonryGridView.count(
                crossAxisCount: 2,
                mainAxisSpacing: 5,
                controller: controller.logic.state.searchTemplateScrollController,
                keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
                crossAxisSpacing: 15,
                itemCount: controller.templatesResult.length,
                itemBuilder: (context, index) {
                  GetBuilder<SearchTemplateManager>(
                    builder: (controller) {
                      return _searchListState(controller, context);
                    },
                  );
                  return MyTemplateItemWidget(
                    personalLogic,
                    controller.templatesResult[index],
                    (TemplateEventType eventType) {
                      _controller.refereshSelectTemplate(index);
                    },
                    // isCanEdit: false,
                    operateType: controller.operateType.value,
                    seachKey: controller.searchingKey.value,
                    index: index,
                    isSearch: true,
                    morefunction: (value) {
                      controller.templatesResult.remove(controller.templatesResult[index]);
                      bool isNeedRequestRefresh = controller.templatesResult.length <= 5;
                      if (isNeedRequestRefresh) {
                        _controller.getSearchResult(
                            searchKey: _controller.searchingKey.value,
                            dbresultClosure: (p0) {
                              _controller.fade.value = CrossFadeState.showSecond;
                              _controller.update();
                            },
                            resultClosure: (model) {
                              _controller.fade.value = CrossFadeState.showSecond;
                              _controller.update();
                            });
                      } else {
                        _controller.fade.value = CrossFadeState.showSecond;
                        _controller.update();
                      }
                    },
                  );
                },
              ));
    } else {
      if (controller.templatesResult.length > 1 && page != 1) {
        page--;
        return SmartRefresher(
            enablePullUp: true,
            enablePullDown: false,
            controller: _refreshController,
            onLoading: _onLoading,
            child: MasonryGridView.count(
              crossAxisCount: 2,
              mainAxisSpacing: 5,
              crossAxisSpacing: 15,
              itemCount: controller.templatesResult.length,
              itemBuilder: (context, index) {
                return MyTemplateItemWidget(
                  personalLogic,
                  controller.templatesResult[index],
                  (TemplateEventType eventType) {
                    _controller.refereshSelectTemplate(index);
                  },
                  seachKey: controller.searchingKey.value,
                  index: index,
                  isSearch: true,
                  morefunction: (value) {
                    // Future.delayed(const Duration(seconds: 1), () {
                     controller.templatesResult.remove(controller.templatesResult[index]);
                    bool isNeedRequestRefresh = controller.templatesResult.length <= 5;
                    if (isNeedRequestRefresh) {
                      _controller.getSearchResult(
                          searchKey: _controller.searchingKey.value,
                          dbresultClosure: (p0) {
                            _controller.fade.value = CrossFadeState.showSecond;
                            _controller.update();
                          },
                          resultClosure: (model) {
                            _controller.fade.value = CrossFadeState.showSecond;
                            _controller.update();
                          });
                    } else {
                      _controller.fade.value = CrossFadeState.showSecond;
                      _controller.update();
                    }
                    // });
                  },
                );
              },
            ));
      } else {
        return Container(
            padding: const EdgeInsets.only(bottom: 180),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(ImageUtils.getImgPath('no_net'), fit: BoxFit.contain),
                Text(
                  intlanguage('app100000625', '当前网络状态异常'),
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
                ),
              ],
            ));
      }
    }
  }

  showIconToast() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0),
        color: Colors.black.withOpacity(0.5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            'assets/images/industry_template/home/<USER>',
            height: 20,
            width: 20,
          ),
          SizedBox(
            width: 12.0,
          ),
          Text(
            intlanguage('app100000629', '反馈成功!'),
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.background),
          ),
        ],
      ),
    );
  }
}
