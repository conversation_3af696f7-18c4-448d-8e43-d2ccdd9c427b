import 'package:niimbot_cache_manager/niimbot_cache_manager.dart';
import 'package:text/application.dart';
import 'package:text/pages/C1/model/symbol/symbol_category.dart';
import 'package:text/pages/C1/model/symbol/symbol_item.dart';
import 'package:text/pages/C1/view/symbol/symbol_list_item.dart';

class SymbolCacheData {
  static const String Category_List_Key = "symbol_category_list";
  static const String Category_Item_List_Key = "symbol_item_list";

  /// 符号分类
  List<SymbolCategory> categories = [];

  /// 分类下的符号数据
  Map<String, List<SymbolItem>?> itemsList = {};

  clearCacheData() {
    categories.clear();
    itemsList.clear();
  }
}

class SymbolManager {
  /// 单例公开访问点
  factory SymbolManager() => sharedInstance();

  /// 静态私有成员，没有初始化
  static SymbolManager? _instance;

  /// 静态、同步、私有访问点
  static SymbolManager sharedInstance() {
    if (_instance == null) {
      // 单例初始化
      _instance = new SymbolManager._internal();
    }
    return _instance!;
  }

  SymbolManager._internal() {
    symbolCacheData = SymbolCacheData();
  }

  /// 符号缓存
  SymbolCacheData? symbolCacheData;

  /// 语言下的缓存
  String currentLanguage = "";
}

extension clear on SymbolManager {
  languageChangedClearCache() {
    String language = Application.getAppLanguage().toLowerCase();
    if (language != currentLanguage) {
      symbolCacheData?.clearCacheData();
      currentLanguage = language;
    }
  }
}

/// 本地缓存获取
extension Local on SymbolManager {
  /// 从本地文件加载素材信息
  Future<List<SymbolCategory>?> loadLocalSymbolCategory() async {
    languageChangedClearCache();
    if (symbolCacheData?.categories.isNotEmpty ?? false) {
      return symbolCacheData?.categories;
    }
    // 获取语言码
    String language = Application.getAppLanguage().toLowerCase();
    // 从缓存管理器获取缓存数据
    List<SymbolCategory>? categories = await NiimbotCacheManager().loadLocalSymbolCategory<List<SymbolCategory>>(
        languageCode: language,
        transformer: (dynamic rawValue) {
          List<SymbolCategory> list = [];
          if (rawValue is List<dynamic>) {
            for (var e in rawValue) {
              list.add(SymbolCategory.fromJson(e));
            }
          }
          return list;
        });
    if (categories != null) {
      symbolCacheData?.categories = categories;
    } else {
      symbolCacheData?.clearCacheData();
    }
    return symbolCacheData?.categories;
  }

  /// 从本地文件加载某符号分类下信息
  Future<List<SymbolItem>?> loadLocalSymbolList({required String categoryId}) async {
    languageChangedClearCache();
    if (symbolCacheData?.itemsList[categoryId] != null) {
      return symbolCacheData?.itemsList[categoryId];
    }
    // 从缓存管理器获取缓存数据
    List<SymbolItem>? items = await NiimbotCacheManager().loadLocalSymbolListFromCategory<List<SymbolItem>>(
        categoryId: categoryId ?? '',
        transformer: (dynamic rawValue) {
          List<SymbolItem> list = [];
          if (rawValue is List<dynamic>) {
            for (var e in rawValue) {
              list.add(SymbolItem.fromJson(e));
            }
          }
          return list;
        });
    if (items != null) {
      symbolCacheData?.itemsList[categoryId] = items;
    } else {
      symbolCacheData?.itemsList.remove(categoryId);
    }
    return items;
  }
}

extension NetWork on SymbolManager {
  /// 从服务端获取素材分类
  Future<List<SymbolCategory>?> requestSymbolCategoryFromNet() async {
    // 获取语言码
    List<SymbolCategory>? categories =
        await NiimbotCacheManager().requestSymbolCategoryFromNet<List<SymbolCategory>>(transformer: (dynamic rawValue) {
      List<SymbolCategory> list = [];
      if (rawValue is List<dynamic>) {
        for (var e in rawValue) {
          list.add(SymbolCategory.fromJson(e));
        }
      }
      return list;
    });
    if (categories != null) {
      symbolCacheData?.categories = categories;
    } else {
      symbolCacheData?.clearCacheData();
    }
    return symbolCacheData?.categories;
  }

  /// 从服务端获取符号列表数据
  Future<List<SymbolItem>?> requestSymbolListFromNet(
      {required int page, required int limit, required String categoryId}) async {
    List<SymbolItem>? items = await NiimbotCacheManager().requestSymbolListFromNet<List<SymbolItem>>(
        params: {"page": page, "limit": limit, "categoryId": categoryId},
        transformer: (dynamic rawValue) {
          List<SymbolItem> list = [];
          if (rawValue is List<dynamic>) {
            for (var e in rawValue) {
              list.add(SymbolItem.fromJson(e));
            }
          }
          return list;
        });
    if (items != null) {
      // 为空或者重新加载
      if (symbolCacheData?.itemsList[categoryId] == null || page == 1) {
        symbolCacheData?.itemsList[categoryId] = items;
      } else {
        symbolCacheData?.itemsList[categoryId]?.addAll(items);
      }
    }
    return items;
  }
}
