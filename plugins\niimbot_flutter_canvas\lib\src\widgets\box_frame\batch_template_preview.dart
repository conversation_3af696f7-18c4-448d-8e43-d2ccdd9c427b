import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:flutter_canvas_plugins_interface/utils/display_util.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/localization/localization_public.dart';
import 'package:niimbot_flutter_canvas/src/utils/loading_mix.dart';
import 'package:niimbot_flutter_canvas/src/utils/theme_color.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/attr_icon_button.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/svg_icon.dart';
import 'package:niimbot_flutter_canvas/src/widgets/controller/canvas/canvas_controller.dart';
import 'package:niimbot_flutter_canvas/src/widgets/rfid_bind/rfid_info_manager.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '/src/utils/canvas_image_util.dart';
import '/src/utils/debounce_util.dart';

class BatchTemplatePreview extends StatefulWidget {
  final TemplateData templateData;
  final ValueChanged<int>? onPageIndexSelect;
  final BuildContext parentContext;
  final Color? printColor;
  final int crossAxisCount;

  const BatchTemplatePreview(
    this.templateData,
    this.parentContext,
    this.printColor,
    this.crossAxisCount,
    this.onPageIndexSelect, {
    super.key,
  });

  @override
  State<StatefulWidget> createState() {
    return BatchTemplatePreviewState();
  }
}

class BatchTemplatePreviewState extends State<BatchTemplatePreview> {
  static Logger _logger = Logger("BatchTemplatePreviewState", on: kDebugMode);
  bool _loadList = false;
  bool _loadItemImage = false;
  int _crossAxisCount = 2;
  RefreshController refreshController = RefreshController();
  ScrollController scrollController = ScrollController();
  TemplateData? template;
  @override
  void initState() {
    super.initState();
    _crossAxisCount = widget.crossAxisCount;
    var templateJson = widget.templateData.toJson();
    template = TemplateData.fromJson(templateJson);
    int pages = widget.templateData.pagesOfBatchData() ?? 0;

    for (int i = 0; i < pages; i++) {
      dataList.add(i);
    }
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      setState(() {
        _loadList = true;
      });
    });
    CanvasEventBus.getDefault().register(this, (data) {
      // _logger.log("打印画板EventBus消息：$data");
      if (data is Map && data.containsKey("action")) {
        final action = data['action'];
        switch (action) {
          /// RFID模板替换
          case "replaceTemplate":
            if (Navigator.of(context).canPop()) Navigator.of(context).pop();
            break;
        }
      }
    });
  }

  int pageCount = 0;
  int startIndex = 0;
  int endIndex = 0;
  double itemHeight = 0;
  double itemWidth = 0;
  double imageWidth = 0;
  double imageHeight = 0;
  double childAspectRatio = 0;
  List<int> dataList = [];
  List<int> currentList = [];

  _calcItem() {
    if (dataList.isEmpty) return;
    if (pageCount == 0 && template!.designWidth != null && template!.designHeight != null) {
      /// 子元素宽高比
      childAspectRatio = template!.designWidth! / template!.designHeight!;
      itemWidth =
          ((MediaQuery.sizeOf(context).width - 20 * 2) /** GridView 宽度 */ - (_crossAxisCount - 1) * 16) /** 列间隙宽度 */ /
              _crossAxisCount /** 列数 */;

      itemHeight = itemWidth / childAspectRatio + 23 /** 字体高度 */ + 12 /** padding*/;

      if (itemHeight > 220) {
        itemHeight = 220.toDouble();
        imageHeight = (220 - 23 - 12).toDouble();
        imageWidth = imageHeight * childAspectRatio;
      } else {
        imageHeight = (itemWidth - 24) / childAspectRatio;
        imageWidth = itemWidth - 24;
      }
      int currentPageIndex = widget.templateData.currentPageIndex;
      int rowCount =  (MediaQuery.sizeOf(context).height - 88) ~/ itemHeight;
      pageCount = _crossAxisCount * ((MediaQuery.sizeOf(context).height - 88) ~/ itemHeight) + _crossAxisCount
          + _crossAxisCount;
      // 增加单行时列表一次加载数量
      if (_crossAxisCount == 1) pageCount = 10;

      startIndex = currentPageIndex ~/ pageCount * pageCount;
      endIndex = (currentPageIndex ~/ pageCount + 1) * pageCount > widget.templateData.pagesOfBatchData()
          ? widget.templateData.pagesOfBatchData()
          : (currentPageIndex ~/ pageCount + 1) * pageCount;
    }
    _logger.log("============pageCount: $pageCount, startIndex: $startIndex, endIndex: $endIndex");
    currentList = dataList.sublist(startIndex, endIndex);
  }

  _onRefresh() {
    _logger.log("============_onRefresh: call");
    Future.delayed(Duration(milliseconds: 1000), () {
      setState(() {
        startIndex -= pageCount;
        if (startIndex < 0) startIndex = 0;
        refreshController.refreshCompleted();
      });
    });
  }

  _onLoadMore() {
    _logger.log("============_onLoadMore: call");
    Future.delayed(Duration(milliseconds: 1000), () {
      setState(() {
        endIndex += pageCount;
        if (endIndex >= template!.pagesOfBatchData()) {
          endIndex = template!.pagesOfBatchData();
          refreshController.loadNoData();
        } else {
          refreshController.loadComplete();
        }
      });
    });
  }

  _switchCrossCount() {
    if (!DebounceUtil.checkClick()) return;
    template!.clearBatchPreviewCache();
    setState(() {
      if (_crossAxisCount == 2) {
        _crossAxisCount = 1;
      } else if (_crossAxisCount == 1) {
        _crossAxisCount = 2;
      }
      SharedPreferences.getInstance().then((sp) {
        sp.setInt("batch_cross_axis_count", _crossAxisCount);
      });
      pageCount = 0;
      currentList.clear();
      refreshController.loadComplete();
    });
  }

  @override
  Widget build(BuildContext context) {
    _calcItem();
    return Container(
      decoration: BoxDecoration(
          color: CanvasTheme.of(widget.parentContext).backgroundColor,
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12))),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Container(
            decoration: BoxDecoration(
              color: CanvasTheme.of(widget.parentContext).backgroundLightColor,
              borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12)),
              boxShadow: [
                BoxShadow(
                  blurRadius: 5, //阴影范围
                  spreadRadius: 0.1, //阴影浓度
                  offset: Offset(0, 0.0),
                  color: Colors.grey.withOpacity(0.15), //阴影颜色
                )
              ],
            ),
            height: 48,
            child: Row(
              children: [
                AttrIconButton(
                  SvgPicture.asset('assets/common/sheet_close.svg',
                      package: 'niimbot_flutter_canvas', width: 24, height: 24, fit: BoxFit.none),
                  width: 48,
                  height: 48,
                  onTap: () => Navigator.of(context).pop(),
                ),
                Spacer(),
                AttrIconButton(
                  SizedBox(
                    height: 24,
                    width: 24,
                    child: SvgPicture.asset(
                      _crossAxisCount == 2 ? 'assets/common/sheet_grid.svg' : 'assets/common/sheet_column.svg',
                      package: 'niimbot_flutter_canvas',
                    ),
                  ),
                  width: 48,
                  height: 48,
                  onTap: _switchCrossCount,
                ),
              ],
            ),
          ),
          Expanded(
              child: _loadList
                  ? SmartRefresher(
                      controller: refreshController,
                      enablePullDown: true,
                      enablePullUp: true,
                      onRefresh: _onRefresh,
                      onLoading: _onLoadMore,
                      child: MasonryGridView.builder(
                          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                          itemCount: currentList.length,
                          controller: scrollController,

                          // 纵轴间距
                          mainAxisSpacing: 9.0,

                          // 横轴间距
                          crossAxisSpacing: 16.0,

                          // SliverGridDelegateWithFixedCrossAxisCount 构建一个横轴固定数量Widget
                          gridDelegate: SliverSimpleGridDelegateWithFixedCrossAxisCount(
                            // 横轴元素个数
                            crossAxisCount: _crossAxisCount,
                          ),
                          // gridDelegate: SliverGridDelegateWithFixedSize(itemWidth, 100, mainAxisSpacing: 10),
                          itemBuilder: (BuildContext context, int index) {
                            return buildItem(_loadItemImage, currentList[index]);
                          }))
                  : Container())
        ],
      ),
    );
  }

  buildItem(bool loadImage, int index) {
    return InkWell(
        onTap: () {
          Navigator.of(context).pop();
          widget.onPageIndexSelect?.call(index);
        },
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                  color: Colors.transparent,
                  border: Border.all(
                      color: template!.currentPageIndex == index ? ThemeColor.brand : Colors.transparent, width: 1),
                  borderRadius: const BorderRadius.all(Radius.circular(10))),
              child: ClipRRect(
                  // borderRadius: BorderRadius.circular(10),
                  child: Stack(
                alignment: Alignment.center,
                children: [
                  _buildSelectedBackgroundWidget(),
                  buildItemImage(loadImage, index),
                ],
              )),
            ),
            const SizedBox(
              height: 4,
            ),
            _buildRFIDWidget(context, index),
            Text(
              '${index + 1}',
              style: CanvasTheme.of(widget.parentContext).attributeBodyTextStyle?.copyWith(
                  color: template!.currentPageIndex == index ? ThemeColor.brand : ThemeColor.title,
                  fontWeight: FontWeight.w600),
            )
          ],
        ));
  }

  buildItemImage(bool loadImage, int index) {
    return ItemDelegate(imageWidth, imageHeight, template!, widget.printColor, _crossAxisCount, index, loadImage);
  }

  Widget _buildSelectedBackgroundWidget() {
    List<String> backgroudList = template!.backgroundImage.split(",");
    String selectedLocalPath =
        template!.localBackground.length > template!.multipleBackIndex && template!.multipleBackIndex >= 0
            ? template!.localBackground[template!.multipleBackIndex]
            : "";
    _logger.log(
        "多图预览背景：$selectedLocalPath, background: ${template!.backgroundImage}, canvasRotate: ${template!.canvasRotate}");
    String imageUrl = "";
    if (backgroudList.isNotEmpty) {
      int index = template!.multipleBackIndex;
      if (index < 0 || index >= backgroudList.length) {
        index = 0;
      }
      imageUrl = backgroudList[index];
    }
    File localImage = File(selectedLocalPath);
    return template!.backgroundImage.isEmpty
        ? Container()
        : Container(
            width: imageWidth,
            height: imageHeight,
            child: RotatedBox(
              quarterTurns: (template!.canvasRotate) ~/ 90,
              child: localImage.existsSync()
                  ? Image.file(
                      localImage,
                      fit: BoxFit.fill,
                      errorBuilder: (_, __, ___) => CachedNetworkImage(
                          fit: BoxFit.fill,
                          imageUrl: imageUrl,
                          errorWidget: (_, __, ___) =>
                              CanvasImageUtils.errorHolder(width: itemWidth, height: itemHeight)),
                    )
                  : CachedNetworkImage(
                      fit: BoxFit.fill,
                      imageUrl: imageUrl,
                      errorWidget: (_, __, ___) => CanvasImageUtils.errorHolder(width: itemWidth, height: itemHeight),
                    ),
            ));
  }

  Widget _buildRFIDWidget(BuildContext context, int index) {
    // rfidContent生成
    List<Widget> Function({RFIDShowType type, required String rfidValue}) rfidContent =
        ({RFIDShowType? type, required String rfidValue}) {
      List<Widget> rfidContent = [SizedBox.shrink()];
      switch (type) {
        case RFIDShowType.NotMatch:
          rfidContent = [
            const SvgIcon(
              'assets/excel/rfid_mark_small.svg',
              color: ThemeColor.COLOR_595959,
              width: 18,
              height: 12,
              fit: BoxFit.contain,
            ),
            const SizedBox(
              width: 3,
            ),
            const SvgIcon(
              'assets/excel/rfid_warn.svg',
              width: 16,
              height: 16,
              useDefaultColor: false,
            ),
            const SizedBox(
              width: 3,
            ),
            Flexible(
              child: Text(
                intlanguage('app100001218', '不符合规范'),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: ThemeColor.COLOR_F8473E),
              ),
            ),
          ];
          break;
        case RFIDShowType.Normal:
          rfidContent = [
            Padding(
              // 此处的Paddding是为了处理文本和SvgIcon的baseline对齐的问题
              padding: const EdgeInsetsDirectional.only(top: 2),
              child: const SvgIcon(
                'assets/excel/rfid_mark_small.svg',
                color: ThemeColor.COLOR_595959,
                width: 18,
                height: 12,
                fit: BoxFit.contain,
              ),
            ),
            const SizedBox(
              width: 3,
            ),
            Flexible(
              child: Text(
                rfidValue,
                style: TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ];
          break;
        default:
          break;
      }
      return rfidContent;
    };

    RfidShowItem rfidShowItem = RfidRepository().getRfidShowItem(index);
    return Obx(() {
      return Get.find<CanvasController>().state.isShowRFID() == true &&
              rfidShowItem.rfidShowType != RFIDShowType.NotBinding
          ? Padding(
              padding: const EdgeInsetsDirectional.symmetric(horizontal: 10.0),
              child: Row(
                mainAxisAlignment: _crossAxisCount == 1 ? MainAxisAlignment.center : MainAxisAlignment.start,
                crossAxisAlignment: rfidShowItem.rfidShowType == RFIDShowType.Normal
                    ? CrossAxisAlignment.start
                    : CrossAxisAlignment.center,
                children: [
                  ...rfidContent(type: rfidShowItem.rfidShowType, rfidValue: rfidShowItem.rfidContent),
                ],
              ),
            )
          : SizedBox.shrink();
    });
  }

  @override
  void dispose() {
    super.dispose();
    template!.clearBatchPreviewCache();
  }
}

class ItemDelegate extends StatefulWidget {
  final double width;
  final double height;
  final TemplateData templateData;
  final Color? printColor;
  final int crossAxisCount;
  final int index;
  final bool loadImage;

  const ItemDelegate(
    this.width,
    this.height,
    this.templateData,
    this.printColor,
    this.crossAxisCount,
    this.index,
    this.loadImage, {
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _ItemDelegateState();
}

class _ItemDelegateState extends State<ItemDelegate> {
  static Logger _logger = Logger("_ItemDelegateState", on: kDebugMode);

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Future<NetalImageResult?> imageData = widget.templateData.generateBatchTemplatePreview(
        context, widget.index, widget.printColor,
        preRatio: DisplayUtil.pxRatio / widget.crossAxisCount);
    _logger.log("================second Load image container");
    return Container(
        width: widget.width,
        height: widget.height, //itemHeight - 23 - 12,
        child: NBImageView(imageData, widget.templateData.canvasRotate));
  }
}

class NBImageView extends StatelessWidget {
  static Logger _logger = Logger("NBImageView", on: kDebugMode);
  final Future<NetalImageResult?> future;
  final int canvasRotate;

  const NBImageView(this.future, this.canvasRotate, {super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<NetalImageResult?>(
      future: future,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done && (snapshot.data?.pixels.isNotEmpty ?? false)) {
          NetalImageResult imageData = snapshot.data!;
          _logger.log('批量预览imageData----->Size: ${imageData.width}x${imageData.height}, length:${imageData.size}');
          LoadingMix.dismissLoading();
          return Image.memory(
            imageData.pixels,
            /** 避免闪动 */
            gaplessPlayback: true,
            fit: BoxFit.fill,
          );
        } else {
          _logger.log("================first Load NBImageView container");
          return Container(
            height: 100,
            decoration: BoxDecoration(color: Colors.transparent, borderRadius: BorderRadius.all(Radius.circular(10))),
          );
        }
      },
    );
  }
}
