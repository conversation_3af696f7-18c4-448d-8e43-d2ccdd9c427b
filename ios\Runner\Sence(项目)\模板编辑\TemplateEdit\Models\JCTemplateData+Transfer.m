//
//  JCTemplateData+Transfer.m
//  Runner
//
//  Created by xingling xu on 2020/3/23.
//  Copyright © 2020 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCTemplateData+Transfer.h"
#import "JCElementModel+Transfer.h"
#import "JCExcelTransUtil.h"
/** 字符串属性 */
#define dict_set_key_value_string(key,string)   if (string && string.length > 0){[dict setObject:string forKey:key];}
/** 字符串属性 */
#define dict_set_key_value_object(key,object)   if (object){[dict setObject:object forKey:key];}
/** 数字类型属性 */
#define dict_set_key_value_number(key,number)   [dict setObject:@(number) forKey:key]
/** bool属性转换 */
#define dict_set_key_value_bool(key,boolValue)  [dict setObject:boolValue?@(1):@(0) forKey:key]

NSString *const JCTemplateData2SdkPropertyKey       = @"JCTemplateData2SdkPropertyKey";
NSString *const JCTemplateData2CachePropertyKey     = @"JCTemplateData2CachePropertyKey";
NSString *const JCTemplateDataShowPlaceHolder       = @"JCTemplateDataShowPlaceHolder";
NSString *const KJCTemplateDataCurrentIndex         = @"KJCTemplateDataCurrentIndex";
NSString *const KJCTemplateDataVirtualIndex         = @"KJCTemplateDataVirtualIndex";
NSString *const KJCTemplateDataHasBackground        = @"KJCTemplateDataHasBackground";
NSString *const KJCTemplateDataPrintPageOnly       = @"KJCTemplateDataPrintPageOnly";
NSString *const KJCTemplateDataCurrentCopyNumber    = @"KJCTemplateDataCurrentCopyNumber";
NSString *const KJCTemplateDataTotalCopyNumber      = @"KJCTemplateDataTotalCopyNumber";

@implementation JCTemplateData (Transfer)

- (NSDictionary *)dataDict {
    XYWeakSelf
    if (DrawBoardInfo.mm2pxScale == 0) DrawBoardInfo.mm2pxScale = (SCREEN_WIDTH-45)/self.width;
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:22];
    dict_set_key_value_string(@"id", self.idStr);
    dict_set_key_value_string(@"name", self.name);
    dict_set_key_value_string(@"thumbnail", self.thumbnail);
   dict_set_key_value_string(@"localThumbnail", self.localThumbnail);
    dict_set_key_value_string(@"version", self.version);
    dict_set_key_value_number(@"fromOldVersion", self.fromOldVersion);
    dict_set_key_value_number(@"canvasRotate", self.canvasRotate);
    if ([self forSdk]) {
        // sdk返回的image要显示背景图
        if ([self getConfigureBackground] && !STR_IS_NIL(self.backgroundImage)) {
            // 多背景图
            NSString *currentBackImgPath = self.backgroundImage;
            UIImage *image = [[UIImage alloc] initWithContentsOfFile:currentBackImgPath];
            NSData *imageData = UIImagePNGRepresentation(image);
            NSString * emptyString = @"";
            if (self.canvasRotate == 0) {
                dict_set_key_value_string(@"backgroundImage",self.backgroundImage);
              NSString *localBackgroundIndex = [self.localBackground safeObjectAtIndex:self.multipleBackIndex];
              dict_set_key_value_string(@"localBackgroundImageUrl", localBackgroundIndex);
            }else{
                NSString * path = [self saveLocalImagePathWithData:imageData];
                dict_set_key_value_string(@"backgroundImage",emptyString);
                dict_set_key_value_string(@"localBackgroundImageUrl", path);
            }
            //dict_set_key_value_string(@"backgroundImage", encodedImageStr);
        }
    } else {
        // 上传服务端统一url
        dict_set_key_value_string(@"backgroundImage", self.backgroundImage);
    }
    dict_set_key_value_object(@"localBackground", self.localBackground);
    dict_set_key_value_string(@"previewImage", self.previewImage);
    dict_set_key_value_number(@"rotate", self.rotate);
    dict_set_key_value_number(@"cableLength", self.cableLength);
    dict_set_key_value_number(@"cableDirection", self.cableDirection);
    dict_set_key_value_number(@"paperType", self.paperType);
     dict_set_key_value_number(@"consumableType", self.consumableType);
    dict_set_key_value_bool(@"isCable", self.isCable);
    dict_set_key_value_bool(@"commodityTemplate", self.commodityTemplate);
    dict_set_key_value_string(@"isEdited", self.isEdited);
    dict_set_key_value_string(@"contentThumbnail", self.contentThumbnail);
    dict_set_key_value_bool(@"isPrintHistory", self.isPrintHistory);
    dict_set_key_value_number(@"width", self.width);
    dict_set_key_value_number(@"height", self.height);
    dict_set_key_value_object(@"margin", self.margin);
    dict_set_key_value_object(@"supportedEditors", self.supportedEditors);
    dict_set_key_value_object(@"paperColor", self.paperColor);
    dict_set_key_value_object(@"names", self.names);
    dict_set_key_value_object(@"labelNames", self.labelNames);
    dict_set_key_value_object(@"inputAreas", self.inputAreas);
    dict_set_key_value_object(@"usedFonts", self.usedFonts);
    dict_set_key_value_object(@"commodityInfo", self.commodityInfo);
    dict_set_key_value_string(@"unit", self.unit);
    dict_set_key_value_string(@"platformCode", self.platformCode);
    dict_set_key_value_number(@"currentPage",self.currentPage);
    dict_set_key_value_number(@"totalPage",self.totalPage);
    dict_set_key_value_number(@"paccuracyName",self.paccuracyName);
    dict_set_key_value_bool(@"hasVipRes", self.hasVipRes);
    dict_set_key_value_bool(@"localType", self.localType);
    dict_set_key_value_bool(@"vip", self.vip);
    dict_set_key_value_number(@"multipleBackIndex",self.multipleBackIndex);
    dict_set_key_value_string(@"originTemplateId", self.originTemplateId);
    dict_set_key_value_string(@"sourceOfIndustryTemplateId", self.sourceOfIndustryTemplateId);
    NSMutableArray *elements = [NSMutableArray arrayWithCapacity:self.elements.count];
    [self.elements enumerateObjectsUsingBlock:^(JCElementModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        // 注入Excel数据
        [obj injectExternalData:weakSelf.externalData task:weakSelf.task modify:weakSelf.modify dataSource:weakSelf.dataSource];
        [obj injectGoodsData:weakSelf.goodsList];
        // 配置sdk与否
        obj = obj.toSdk([weakSelf forSdk]);
        obj = obj.toCache([weakSelf forCache]);
        // 配置份数 针对sdk打印流水号
        obj = obj.setCurrentCopyNumber([weakSelf getCurrentCopyNumber]);
        // 配置当前excel分页
        obj = obj.setCurrentPageIndex([weakSelf getCurrentPageIndex]);
        // 设置虚拟页码，对比setCurrentPageIndex可能存在映射关系
        obj = obj.setVirtualPageIndex([weakSelf getVirtualPageIndex]);
        // 配置当前打印的总页数，适配流水号递增
        obj = obj.setTotalCopyNumber([weakSelf getTotalCopyNumber]);
        // 是否显示placeholder
        obj = obj.showPlaceHolder([weakSelf getShowPlaceHolder]);
        //流水号是否按页打印
        obj = obj.setPrintPageOnly([weakSelf getPrintPageOnly]);
        // 生成json
        if(!obj.isHidden){
            [elements addObject:[obj elementDict].mutableCopy];
        }
    }];
    for (NSMutableDictionary *obj in elements) {
        NSNumber *isOpenMirror = obj[@"isOpenMirror"];
        if(isOpenMirror.integerValue == 1){
            for (NSMutableDictionary * obj2 in elements) {
                NSString *elementId = obj[@"id"];
                NSString *mirrorId = obj2[@"mirrorId"];
                if([elementId isEqualToString:mirrorId]){
                    NSString *value = obj[@"value"];
                    [obj2 setValue:UN_NIL(value) forKey:@"value"];
                }
            }
        }
    }
    dict_set_key_value_object(@"elements", elements);
    if (![self forSdk]) {
        dict_set_key_value_string(@"des", self.des);
        dict_set_key_value_object(@"profile", [self.profile profileDict]);
        dict_set_key_value_object(@"externalData", self.externalData);
        dict_set_key_value_object(@"task", self.task);
    }
    
    if([self.dataSource isKindOfClass:[NSArray class]]){
        NSMutableArray *dataSource = [[NSMutableArray alloc]init];
        [self.dataSource enumerateObjectsUsingBlock:^(NSDictionary * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            [dataSource addObject:obj];
        }];
        dict_set_key_value_object(@"dataSource", dataSource);
    }
  
    dict_set_key_value_object(@"modify", self.modify);
    dict_set_key_value_object(@"bindInfo", self.bindInfo);
    dict_set_key_value_string(@"templateVersion", self.templateVersion);
    dict_set_key_value_string(@"consumableTypeTextId", self.consumableTypeTextId);
    dict_set_key_value_string(@"layoutSchema", self.layoutSchema);
    return dict;
}

-(NSString *)saveLocalImagePathWithData:(NSData *)imageData{
    NSArray *pathArr =  @[RESOURCE_IMAGE_THUMB_BACK_PATH,RESOURCE_IMAGE_CLOILD_THUMB_BACK_PATH,RESOURCE_IMAGE_CLOILD_THUMB_BACK_PATH,RESOURCE_ELEMENT_PATH];
       [pathArr enumerateObjectsUsingBlock:^(NSString  *rootPath, NSUInteger idx, BOOL * _Nonnull stop) {
           if(![[NSFileManager defaultManager] fileExistsAtPath:rootPath]) {
               [[NSFileManager defaultManager] createDirectoryAtPath:rootPath withIntermediateDirectories:YES attributes:nil error:nil];
           }
       }];
    NSString * labelId = STR_IS_NIL(self.profile.extrain.labelId) == true ? [@([[NSDate date] timeIntervalSince1970]) description] : self.profile.extrain.labelId;
    NSString * pathKey = [NSString stringWithFormat:@"%@%ld%ld",labelId,self.multipleBackIndex,self.rotate];
    NSString * path = self.thumbnail;
    if (![[NSFileManager defaultManager] fileExistsAtPath:path]) {
       [imageData writeToFile:path atomically:YES];
    }
    return path;
}

//单独处理json对齐新版本 element里面的value字段
+(NSDictionary *)configElementWithNewJson:(NSDictionary *)dic
{
    NSMutableDictionary * copyDic = [NSMutableDictionary dictionaryWithDictionary:dic];
    NSMutableArray *elements = [copyDic objectForKey:@"elements"];
    if(elements.count > 0){
        for (NSDictionary * modifyDic in elements) {
            //更改最外层value //文本  条码 二维码  表格
            NSString * type = [modifyDic objectForKey:@"type"];
            if([type isEqualToString:@"text"] || [type isEqualToString:@"barcode"] || [type isEqualToString:@"qrcode"]){
                [self fixDic:modifyDic];
            }else if([type isEqualToString:@"table"]){
                //表格
                //更改cells里面的value
                NSArray * cells = [modifyDic objectForKey:@"cells"];
                for (NSDictionary * cellDic in cells) {
                    [self fixDic:cellDic];
                }
                //更改combineCells里面的value
                NSArray * combineCells = [modifyDic objectForKey:@"combineCells"];
                for (NSDictionary * combineCell in combineCells) {
                    [self fixDic:combineCell];
                }
            }
        }
    }
    id dataSource = [copyDic objectForKey:@"dataSource"];
    if([dataSource isKindOfClass:[NSArray class]]){
        [copyDic setValue:dataSource forKey:@"dataSources"];
    }
    id modify = [copyDic objectForKey:@"modify"];
    if([modify isKindOfClass:[NSDictionary class]]){
        [copyDic setValue:modify forKey:@"dataSourceModifies"];
    }
    id bindInfo = [copyDic objectForKey:@"bindInfo"];
    if([bindInfo isKindOfClass:[NSDictionary class]]){
        [copyDic setValue:bindInfo forKey:@"dataSourceBindInfo"];
    }
    return copyDic;
}

+(void)fixDic:(NSDictionary *)dic
{
    NSArray * dataBind = [dic objectForKey:@"dataBind"];
    if(dataBind != nil && [dataBind isKindOfClass:[NSArray class]] && dataBind.count > 0){
        NSString * value = [dic objectForKey:@"value"];
        if([value hasPrefix:@"${"]){
            value = [JCExcelTransUtil transToNewJsonValue:value];
            [dic setValue:value forKey:@"value"];
        }
    }
}

- (BOOL)isFromWithInstruTemplate{
    BOOL isFromWithInstru = NO;
    if(!STR_IS_NIL(self.profile.extrain.sourceId) && self.profile.extrain.sourceId.integerValue != 0){
        isFromWithInstru = YES;
    }
    if(!STR_IS_NIL(self.profile.extrain.labelId) && self.profile.extrain.labelId.integerValue != 0){
        isFromWithInstru = YES;
    }
    if(!STR_IS_NIL(self.profile.barcode)){
        isFromWithInstru = YES;
    }
    if(!STR_IS_NIL(self.profile.extrain.amazonCodeWuhan)){
        isFromWithInstru = YES;
    }
    if(!STR_IS_NIL(self.profile.extrain.amazonCodeBeijing)){
        isFromWithInstru = YES;
    }
    if(!STR_IS_NIL(self.profile.extrain.sparedCode)){
        isFromWithInstru = YES;
    }
    if(!STR_IS_NIL(self.profile.extrain.virtualBarCode)){
        isFromWithInstru = YES;
    }
    if(self.profile.extrain.barcodeCategoryMap.allKeys.count > 0){
        isFromWithInstru = YES;
    }
    return isFromWithInstru;
}

#pragma mark - some configure
- (JCTemplateData *(^)(BOOL transfer2sdk))toSdk {
    return ^(BOOL transfer2sdk){
        objc_setAssociatedObject(self, &JCTemplateData2SdkPropertyKey, @(transfer2sdk), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (BOOL)forSdk {
    id object = objc_getAssociatedObject(self, &JCTemplateData2SdkPropertyKey);
    return [object integerValue] > 0 ? YES:NO;
}

- (JCTemplateData *(^)(BOOL transfer2cache))toCache {
    return ^(BOOL transfer2cache){
        objc_setAssociatedObject(self, &JCTemplateData2CachePropertyKey, @(transfer2cache), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (BOOL)forCache {
    id object = objc_getAssociatedObject(self, &JCTemplateData2CachePropertyKey);
    return [object integerValue] > 0 ? YES:NO;
}

- (JCTemplateData *(^)(BOOL showHolder))showPlaceHolder {
    return ^(BOOL showHolder){
        objc_setAssociatedObject(self, &JCTemplateDataShowPlaceHolder, @(showHolder), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (BOOL)getShowPlaceHolder {
    id object = objc_getAssociatedObject(self, &JCTemplateDataShowPlaceHolder);
    return [object integerValue] > 0 ? YES:NO;
}


- (JCTemplateData *(^)(NSInteger currentIndex))setCurrentPageIndex {
    return ^(NSInteger currentIndex){
        objc_setAssociatedObject(self, &KJCTemplateDataCurrentIndex, @(currentIndex), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (NSInteger)getCurrentPageIndex {
    id object = objc_getAssociatedObject(self, &KJCTemplateDataCurrentIndex);
    return [object integerValue];
}

- (JCTemplateData *(^)(NSInteger currentIndex))setVirtualPageIndex {
    return ^(NSInteger currentIndex){
        objc_setAssociatedObject(self, &KJCTemplateDataVirtualIndex, @(currentIndex), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (NSInteger)getVirtualPageIndex {
    id object = objc_getAssociatedObject(self, &KJCTemplateDataVirtualIndex);
    return [object integerValue];
}

- (JCTemplateData *(^)(BOOL hasBackground))configureBase64Background {
    return ^(BOOL hasBackground){
        objc_setAssociatedObject(self, &KJCTemplateDataHasBackground, @(hasBackground), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (JCTemplateData *(^)(BOOL isPrintPageOnly))setPrintPageOnly{
    return ^(BOOL isPrintPageOnly){
        objc_setAssociatedObject(self, &KJCTemplateDataPrintPageOnly, @(isPrintPageOnly), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (BOOL)getPrintPageOnly{
    id object = objc_getAssociatedObject(self, &KJCTemplateDataPrintPageOnly);
    return [object boolValue];
}

- (BOOL)getConfigureBackground {
    id object = objc_getAssociatedObject(self, &KJCTemplateDataHasBackground);
    return [object integerValue] > 0 ? YES:NO;
}

- (JCTemplateData *(^)(NSInteger copyNumber))setCurrentCopyNumber {
    return ^(NSInteger copyNumber){
        objc_setAssociatedObject(self, &KJCTemplateDataCurrentCopyNumber, @(copyNumber), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (NSInteger)getCurrentCopyNumber {
    id object = objc_getAssociatedObject(self, &KJCTemplateDataCurrentCopyNumber);
    return [object integerValue];
}

- (JCTemplateData *(^)(NSInteger copyNumber))setTotalCopyNumber {
    return ^(NSInteger copyNumber){
        objc_setAssociatedObject(self, &KJCTemplateDataTotalCopyNumber, @(copyNumber), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (NSInteger)getTotalCopyNumber {
    id object = objc_getAssociatedObject(self, &KJCTemplateDataTotalCopyNumber);
    return [object integerValue];
}

- (BOOL)containSerialNumber {
    __block BOOL value = NO;
    [self.elements enumerateObjectsUsingBlock:^(JCElementModel *model, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([model.type isEqualToString:@"serial"]) {
            value = YES;*stop = YES;
        }
    }];
    return value;
}

- (BOOL)containSerialNumberOnly {
    __block BOOL value = NO;
    if([self hasExcelElement]){
        return value;
    }
    if([self hasPDFImageElement]){
        return value;
    }
    [self.elements enumerateObjectsUsingBlock:^(JCElementModel *model, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([model.type isEqualToString:@"serial"]) {
            value = YES;*stop = YES;
        }
    }];
    return value;
}

- (BOOL)hasExcelElement {
    __block BOOL value = NO;
    [self.elements enumerateObjectsUsingBlock:^(JCElementModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj hasExcelImport]) {
            value = YES;
            *stop = YES;
        }
    }];
    return value;
}

- (BOOL)hasPDFImageElement {
    __block BOOL value = NO;
    [self.elements enumerateObjectsUsingBlock:^(JCElementModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj.type isEqualToString:@"image"] && [[JCPDFPrintSourceManager sharedInstance] getImagePathsByElementId:obj.elementId].count > 0) {
            value = YES;
            *stop = YES;
        }
    }];
    return value;
}

-(NSString *)getExcelHash{
    if([self.dataSource isKindOfClass:[NSArray class]] && self.dataSource.count > 0){
        NSDictionary * data = self.dataSource.firstObject;
        NSString * hash = [data objectForKey:@"hash"];
        return hash;
    }
    return @"";
}

- (NSInteger)currentMaxExcelNumber {
    __block NSInteger excelNumber = 1;
    [self.elements enumerateObjectsUsingBlock:^(JCElementModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj hasExcelImport]) {
            excelNumber = MAX(excelNumber, [obj currentExcelNumber]);
        }
    }];
    return excelNumber;
}

// 比较基础信息是否被修改过
- (BOOL)baseInfoIsSameWith:(JCTemplateData *)anotherData {
    BOOL same = [self.name isEqualToString:anotherData.name] && (self.cableLength == anotherData.cableLength) && (self.cableDirection == anotherData.cableDirection) /*&& (self.paperType == anotherData.paperType)*/ && (self.consumableType == anotherData.consumableType) && (self.width == anotherData.width) && (self.height == anotherData.height) && (self.currentPage == anotherData.currentPage) && (self.totalPage == anotherData.totalPage || (self.totalPage - anotherData.totalPage <= 1))/* && [self.profile isEqual:anotherData.profile]*/;
    return same;
}

// 比较模板数据
- (BOOL)isSameWith:(JCTemplateData *)anotherData {
    NSArray *orignalElements = self.elements;
    NSArray *anotherElements = anotherData.elements;
    if (orignalElements.count != anotherElements.count) return NO;
    __block BOOL same = [self baseInfoIsSameWith:anotherData];
    if (!same) return NO;
    [orignalElements enumerateObjectsUsingBlock:^(JCElementModel *orignalModel, NSUInteger idx, BOOL * _Nonnull stop) {
        NSArray *filterArr = [anotherElements filter:^BOOL(JCElementModel *anotherModel) {
            return [orignalModel.elementId isEqualToString:anotherModel.elementId];
        }];
        if (filterArr.count == 0) {
            same = NO;
            *stop = YES;
        } else if (filterArr.count == 1) {
            same = [orignalModel isSameWith:filterArr.firstObject];
             if (!same) *stop = YES;
        } else {
            __block BOOL temple = NO;
            [filterArr enumerateObjectsUsingBlock:^(JCElementModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
                if ([orignalModel isSameWith:obj]) {
                    temple = YES;
                    *stop = YES;
                }
            }];
            same = temple;
            if (!same) *stop = YES;
        }
//        JCElementModel *model = [anotherElements find:^BOOL(JCElementModel *anotherModel) {
//            return [orignalModel.elementId isEqualToString:anotherModel.elementId];
//        }];
//        if (model) {
//            same = [orignalModel isSameWith:model];
//            if (!same) *stop = YES;
//        } else {
//            same = NO;
//            *stop = YES;
//        }
    }];
    return same;
}

- (NSString *)localBackgroundImagePath {
    // 多背景图
    NSString *currentBackImgPath;
    UIImage *image;
    NSString *imageId = STR_IS_NIL(self.temporaryId) ? self.idStr : self.temporaryId;
    BOOL isCloud = [self isCloudTemplate];
    if (imageId.length > 12) isCloud = NO;
    if (self.localBackground.count > 0) {
        currentBackImgPath = [self.localBackground safeObjectAtIndex:self.multipleBackIndex];
    }
    image = [[UIImage alloc] initWithContentsOfFile:currentBackImgPath];
    if(image == nil){
        if ([self.backgroundImage componentsSeparatedByString:background_image_component].count > 1) {
            // set base64 string
            currentBackImgPath = self.localBackground[self.multipleBackIndex];
        } else {
            currentBackImgPath = self.localBackground.firstObject;
        }
        image = [[UIImage alloc] initWithContentsOfFile:currentBackImgPath];
    }
    if(image == nil){
        isCloud = YES;
        imageId = self.profile.extrain.labelId;
        if ([self.backgroundImage componentsSeparatedByString:background_image_component].count > 1) {
            // set base64 string
            currentBackImgPath = self.localBackground[self.multipleBackIndex];
        } else {
            currentBackImgPath = self.localBackground.firstObject;
        }
        image = [[UIImage alloc] initWithContentsOfFile:currentBackImgPath];
    }
    return currentBackImgPath;
}

@end

@implementation JCProfile (Transfer)

- (NSDictionary *)profileDict {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:10];
    dict_set_key_value_string(@"barcode", self.barcode);
    dict_set_key_value_string(@"keyword", self.keyword);
    dict_set_key_value_string(@"hardwareSeriesId", self.hardwareSeriesId);
    dict_set_key_value_string(@"hardwareSeriesName", self.hardwareSeriesName);
    dict_set_key_value_string(@"machineName", self.machineName);
    dict_set_key_value_string(@"machineId", self.machineId);
    dict_set_key_value_object(@"extrain", [self.extrain extrainDict]);
    return dict;
}

@end

@implementation JCProfileExtrain (Transfer)
- (NSDictionary *)extrainDict {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:10];
    dict_set_key_value_bool(@"isCustom", self.isCustom);
    dict_set_key_value_string(@"folderId", self.folderId);
    dict_set_key_value_string(@"adaptPlatformCode", self.adaptPlatformCode);
    dict_set_key_value_string(@"adaptPlatformName", self.adaptPlatformName);
    dict_set_key_value_string(@"userId", self.userId);
    dict_set_key_value_string(@"industryId", self.industryId);
    dict_set_key_value_string(@"commodityCategoryId", self.commodityCategoryId);
    dict_set_key_value_string(@"downloadCount", self.downloadCount);
    dict_set_key_value_bool(@"isDelete", self.isDelete);
    dict_set_key_value_string(@"createTime", self.createTime);
    dict_set_key_value_string(@"updateTime", self.updateTime);
    dict_set_key_value_bool(@"isHot", self.isHot);
    dict_set_key_value_string(@"sortDependency", self.sortDependency);
    dict_set_key_value_string(@"clickNum", self.clickNum);
    dict_set_key_value_string(@"templateType", self.templateType);
    dict_set_key_value_string(@"templateClass", self.templateClass);
    dict_set_key_value_bool(@"isPrivate", self.isPrivate);
    dict_set_key_value_string(@"amazonCodeBeijing", self.amazonCodeBeijing);
    dict_set_key_value_string(@"amazonCodeWuhan", self.amazonCodeWuhan);
    dict_set_key_value_string(@"sparedCode", self.sparedCode);
    dict_set_key_value_string(@"virtualBarCode", self.virtualBarCode);
    dict_set_key_value_bool(@"isNewPath", self.isNewPath);
    dict_set_key_value_bool(@"isMobileTemplete", self.isMobileTemplete);
    dict_set_key_value_string(@"resourceVersion", self.resourceVersion );
    dict_set_key_value_object(@"phone", self.phone);
    dict_set_key_value_string(@"sourceId", self.sourceId);
    dict_set_key_value_string(@"labelId", self.labelId);
    dict_set_key_value_string(@"goodsCode", self.goodsCode);
    dict_set_key_value_string(@"materialModelSn", self.materialModelSn);
    dict_set_key_value_object(@"barcodeCategoryMap", self.barcodeCategoryMap);
    return dict;
}

@end
