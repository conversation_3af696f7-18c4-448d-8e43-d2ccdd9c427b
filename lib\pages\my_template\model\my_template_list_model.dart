
import 'dart:async';

import 'package:niimbot_template/models/parse/parse_resource.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/template_parse.dart';
import 'package:text/template/util/template_transform_utils.dart';

/// total : 1
/// page : 1
/// limit : 10
/// list : [{"name":"1111","id":500003127,"gmtCreated":"2023-11-30T02:02:06.655Z","gmtModified":"2023-11-30T02:02:06.655Z","userId":54828}]

class MyTemplateListModel {
  MyTemplateListModel({
    num? total,
    num? page,
    num? limit,
    List<TemplateData>? list,
    List<num>? deletedTemplates,
  }) {
    _total = total;
    _page = page;
    _limit = limit;
    _list = list;
    _deletedTemplates = deletedTemplates;
  }

  // MyTemplateListModel.fromJson(dynamic json) {
  //   _total = json['total'];
  //   _page = json['page'];
  //   _limit = json['limit'];
  //   _deletedTemplates = [];
  //   json['deletedTemplates']?.forEach((value) {
  //     _deletedTemplates?.add(value);
  //   });
  //
  //   if (json['list'] != null) {
  //     _list = [];
  //     json['list'].forEach((v) {
  //       _list?.add(TemplateData.fromJson(v));
  //     });
  //   }
  // }
  static Future<MyTemplateListModel> fromJson(dynamic json) async{
    var _total = json['total'];
    var _page = json['page'];
    var _limit = json['limit'];
    List<num>? _deletedTemplates = [];
    List<TemplateData> _list = [];
    json['deletedTemplates']?.forEach((value) {
      _deletedTemplates?.add(value);
    });

    if (json['list'] != null) {
      // await Future.forEach(json['list'], (v)async{
      //   _list.add(await TemplateTransformUtils.parseServiceTemplateJsonToNiimbotTemplate(v as Map<String,dynamic>));
      // });
      final futures = (json['list'] as List)
          .map((v) => TemplateTransformUtils.parseServiceTemplateJsonToNiimbotTemplate(v as Map<String, dynamic> , needParseElements: false))
          .toList();

      final results = await Future.wait(futures);
      _list.addAll(results);

    }
    return MyTemplateListModel().copyWith(total: _total,page: _page,limit: _limit,deletedTemplates: _deletedTemplates,list: _list);
  }

  num? _total;
  num? _page;
  num? _limit;
  List<TemplateData>? _list;
  List<num>? _deletedTemplates;
  MyTemplateListModel copyWith({
    num? total,
    num? page,
    num? limit,
    List<TemplateData>? list,
    List<num>? deletedTemplates,
  }) =>
      MyTemplateListModel(
        total: total ?? _total,
        page: page ?? _page,
        limit: limit ?? _limit,
        list: list ?? _list,
        deletedTemplates: deletedTemplates ?? _deletedTemplates,
      );
  num? get total => _total;
  num? get page => _page;
  num? get limit => _limit;
  List<TemplateData>? get list => _list;
  List<num>? get deletedTemplates => _deletedTemplates;
  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['total'] = _total;
    map['page'] = _page;
    map['limit'] = _limit;
    if (_list != null) {
      map['list'] = _list?.map((v) => v.toJson()).toList();
    }
    map['deletedTemplates'] = _deletedTemplates;
    return map;
  }
}

/// name : "1111"
/// id : 500003127
/// gmtCreated : "2023-11-30T02:02:06.655Z"
/// gmtModified : "2023-11-30T02:02:06.655Z"
/// userId : 54828
