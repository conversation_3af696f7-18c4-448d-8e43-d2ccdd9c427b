import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:text/pages/my_template/model/template_data_extension.dart';
// import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:text/pages/my_template/page/personal_template/controller/personal_template_logic.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/plane_button.dart';
import 'package:text/utils/theme_color.dart';

import '../../../../utils/image_utils.dart';
import '../../my_template_state.dart';
import '../../widget/my_template_item_widget.dart';

///已选择的模板列表页面
class PersonalTemplateSelectedPage extends StatefulWidget {
  List<TemplateData>? templates = [];

  PersonalTemplateSelectedPage({Key? key, this.templates}) : super(key: key);

  @override
  _PersonalTemplateSelectedPageState createState() => _PersonalTemplateSelectedPageState();
}

class _PersonalTemplateSelectedPageState extends State<PersonalTemplateSelectedPage> {
  final myTemplateLogic = Get.find<PersonalTemplateLogic>();
  late MyTemplateState templateState;
  final ScrollController _listScrollController = ScrollController();
  List<TextEditingController> printEditControllers = [];
  List<FocusNode> printFocusNodes = [];
  @override
  void initState() {
    super.initState();
    templateState = myTemplateLogic.state;
    printEditControllers = myTemplateLogic.state.myTemplateSelectList.map((e) {
      TextEditingController printEditController = TextEditingController();
      printEditController.text = e.printPaper.toString();
      return printEditController;
    }).toList();
    printFocusNodes = myTemplateLogic.state.myTemplateSelectList.map((e) {
      FocusNode printFocusNode = FocusNode();
      return printFocusNode;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: ThemeColor.background,
        borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12)),
      ),
      height: MediaQuery.sizeOf(context).height - 60,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 48,
            width: context.width,
            padding: EdgeInsets.only(left: 16, right: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(
                  width: 60,
                ),
                GetBuilder<PersonalTemplateLogic>(
                  builder: (controller) {
                    return ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: MediaQuery.sizeOf(context).width - 200),
                      child: Text(
                        intlanguage('app100001239', '已选\$个模板',
                            param: [(myTemplateLogic.state.myTemplateSelectList.length ?? 0).toString()]),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                        style: const TextStyle(color: ThemeColor.mainTitle, fontSize: 17, fontWeight: FontWeight.w600),
                      ),
                    );
                  },
                ),
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: PlaneButton(
                    child: Text(
                      intlanguage('app01584', '关闭'),
                      style: TextStyle(color: ThemeColor.title, fontSize: 16, fontWeight: FontWeight.w600),
                    ),
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ),
              ],
            ),
          ),
          Expanded(
              child: Padding(
                  padding: Directionality.of(context) == TextDirection.rtl
                      ? EdgeInsets.only(left: 0, right: 15)
                      : EdgeInsets.only(left: 15, right: 0),
                  child: GetBuilder<PersonalTemplateLogic>(
                    builder: (controller) {
                      if (myTemplateLogic.state.myTemplateSelectList.isEmpty) {
                        return Container(
                            padding: const EdgeInsets.only(bottom: 180),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset(
                                  ImageUtils.getImgPath('no_data'),
                                  fit: BoxFit.contain,
                                ),
                                Text(
                                  intlanguage('app100000515', '暂无相关结果'),
                                  style: const TextStyle(
                                      fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
                                ),
                              ],
                            ));
                      } else {
                        return MasonryGridView.count(
                          crossAxisCount: 2,
                          mainAxisSpacing: 5,
                          controller: myTemplateLogic.state.previewTemplateScrollController,
                          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
                          crossAxisSpacing: 0,
                          itemCount: myTemplateLogic.state.myTemplateSelectList.length,
                          itemBuilder: (context, index) {
                            return MyTemplateItemWidget(
                              myTemplateLogic,
                              myTemplateLogic.state.myTemplateSelectList[index],
                              (TemplateEventType eventType) {
                                myTemplateLogic.refereshSelectTemplate(index);
                              },
                              isCanEdit: false,
                              isPreview: true,
                              operateType: myTemplateLogic.state.operateType,
                              index: index,
                              morefunction: (value){
                                myTemplateLogic.onRefreshTemplate(isNeedRequestRefresh: value);
                              },
                            );
                          },
                        );
                      }
                    },
                  ))),
        ],
      ),
    );
  }
}
