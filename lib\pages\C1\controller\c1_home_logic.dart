import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_cache_manager/niimbot_cache_manager.dart' hide HardWareManager;
import 'package:niimbot_template/niimbot_template.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:text/application.dart';
import 'package:text/network/dio_utils.dart';
import 'package:text/network/http_api.dart';
import 'package:text/pages/C1/model/c1_file_business.dart';
import 'package:text/pages/C1/model/define_model.dart';
import 'package:text/pages/C1/model/print_setting_config.dart';
import 'package:text/pages/C1/model/template_data_transform.dart';
import 'package:text/pages/C1/view/c1_edit_page.dart';
import 'package:text/pages/C1/view/c1_share_template_detail_page.dart';
import 'package:text/pages/industry_template/home/<USER>/hard_ware_serise_model.dart';
import 'package:text/pages/industry_template/select_label/barcode_consumable.dart';
import 'package:text/pages/industry_template/select_label/hardware_list_model.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/hardware_manager.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/utils/toast_util.dart';
import 'package:text/widget/adjust_bottom_sheet.dart' as adjust_bottom_sheet;
import 'package:tuple/tuple.dart';

/// 扩展ConsumableSpec，添加getSpecSize方法
extension ConsumableSpecHelper on ConsumableSpec {
  /// 获取规格尺寸
  num? getSpecSize() {
    // 筛选数字位的字符，防止size中带有mm或者mm²导致的序列parse失败
    RegExp numberPattern = RegExp(r'^[0-9]+(\.[0-9]+)?');
    String? matchString = numberPattern.stringMatch(size ?? "");
    if (matchString != null && matchString.isNotEmpty) {
      try {
        return num.tryParse(matchString);
      } catch (e) {}
    }
    return height;
  }
}

class ParagraphElementItem {
  Widget widget;
  bool overflow;

  ///模板自适应宽度，单位为dp
  double templateAdaptWidth;

  ParagraphElementItem({required this.widget, required this.overflow, required this.templateAdaptWidth});
}

/// C1首页的控制器实现
class C1HomeLogic extends GetxController {
  num defaultLength = 30;

  num minLength = 10;

  num maxLength = 100;

  /// 碳带展示状态
  var ribbonState = RibbonState.installed.obs;

  /// 连接展示状态
  var connectedState = C1ConnectedState.unConnected.obs;

  ///电量档位（1-10档）
  ///1-14：10%
  ///15-24: 20%
  ///...
  ///95-100：100%
  var electricityLevel = 0.obs;

  /// 文本长度
  var textLength = TextLength.fixedWidth.obs;

  /// 文本格式
  var textFormat = TextFormat.singleLine.obs;

  /// 文本对齐方式
  var textAlignment = ParagraphAlignment.center.obs;

  /// C1设备模型
  HardwareModel? hardWare;

  Rxn<HardWareSeriesModel?> hardwareSeriesC1 = Rxn<HardWareSeriesModel?>(null);

  /// 可选耗材
  List<Consumable>? get consumables => hardWare?.consumables;

  /// 选中规格（没有默认值，需要用户手动选择）
  var consumableSpecification = Rxn<ConsumableSpec?>();

  /// 缓存管理器，用于获取耗材规格信息
  final NiimbotCacheManager _cacheManager = NiimbotCacheManager();

  /// 固定长度，设置的对齐方式；自动长度，默认在段落中居左
  ParagraphAlignment get applyTextAlignment =>
      textLength() == TextLength.fixedWidth ? textAlignment() : ParagraphAlignment.Leading;

  /// 双行最小高度
  double doubleLineMinHeight = 3.5;

  /// 双行不受耗材规格高度限制（临时使用字段）
  bool allSupportDoubleLine = false;

  /// 文件列表状态：0-初始请求文件列表，1-文件列表不为空，2-文件列表为空
  Rxn<int> fileState = Rxn<int>(0);

  Rxn<List<TemplateData>> templateList = Rxn<List<TemplateData>>();

  late RefreshController _refreshController;
  late ScrollController _scrollController;
  late SyncServiceTemplateFun _syncServiceTemplateFun;
  late BuildContext Function() _getContext;

  int page = 1;

  C1HomeLogic({required RefreshController refreshController, required ScrollController scrollController, required BuildContext Function() getContext}) : super() {
    _refreshController = refreshController;
    _scrollController = scrollController;
    _syncServiceTemplateFun = (templateId, syncTemplate, fromBackground) {
      int index = templateList()?.indexWhere((e) => e.id == templateId) ?? -1;
      if (index != -1) {
        templateList()![index] = syncTemplate;
        update([index]);
      }
      if (!fromBackground) {
        refreshFileList();
      }
    };
    _getContext = getContext;
  }

  @override
  void onInit() {
    super.onInit();
    // 获取C1设备模型
    hardWare = HardWareManager.instance()
        .findHardwareModel(machineId: Application.appEnv != AppEnv.production ? "10027" : '10035');

    // 从缓存加载上次选择的耗材信息
    _loadLastSelectedConsumable();

    _initHardwareSeriesC1();
    C1FileBusiness.instance.addSyncServiceTemplateFun(_syncServiceTemplateFun);
  }

  /// 从SharedPreferences和缓存加载上次选择的耗材信息
  Future<void> _loadLastSelectedConsumable() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();

      // 获取上次选择的耗材代码、分类和规格ID
      String? lastSelectedConsumableCode = prefs.getString('last_selected_consumable_code');
      String? lastSelectedCategoryName = prefs.getString('last_selected_category');
      String? lastSelectedSpecId = prefs.getString('last_selected_spec_id');

      // 如果没有上次选择的信息，则退出
      if (lastSelectedConsumableCode == null || lastSelectedSpecId == null) {
        return;
      }

      // 从缓存获取该耗材代码的规格信息
      List<ConsumableCategory>? categories = await _cacheManager.loadLocalConsumableSpecs<List<ConsumableCategory>>(
        consumableCode: lastSelectedConsumableCode,
      );

      // 如果没有找到缓存数据，则退出
      if (categories == null || categories.isEmpty) {
        return;
      }

      // 找到匹配的分类
      ConsumableCategory? category;
      if (lastSelectedCategoryName != null) {
        category = categories.firstWhereOrNull((element) => element.name == lastSelectedCategoryName);
      }

      // 如果没有找到匹配的分类，使用第一个
      category ??= categories.first;

      // 从分类中找到匹配的规格
      if (category.labels != null && category.labels!.isNotEmpty) {
        ConsumableSpec? spec = category.labels!.firstWhereOrNull((label) => label.id == lastSelectedSpecId);

        // 如果找到了匹配的规格，设置为当前选中的规格
        if (spec != null) {
          consumableSpecification.value = spec;
        }
      }
    } catch (e) {
      debugPrint('Error loading cached consumable specs: ${e.toString()}');
    }
  }

  _initHardwareSeriesC1() async {
    List<HardWareSeriesModel>? hardwareSeriesList = await HardWareManager.instance().getPrinterSeriesInfo();
    hardwareSeriesC1.value = hardwareSeriesList?.firstWhereOrNull(
        (element) => element.hardwareNameStr!.split(",").contains('C1') || element.name!.split(",").contains('线号机'));
  }

  handleShareCode(String shareCode, BuildContext context) async {
    TemplateData? templateDetail = await C1FileBusiness.instance.getTemplateDetailByShareCode(shareCode);
    if(templateDetail == null) {
      EasyLoading.showToast(intlanguage('app100002020', '获取模板详情失败'));
      return;
    }
    templateDetail = templateDetail.copyWith(id: "");
    templateDetail.profile.extra.userId = "";
    templateDetail.profile.extra.folderId = "0";
    var value = await adjust_bottom_sheet.showModalBottomSheet(
      context: context,
      barrierColor: Colors.black.withOpacity(0.35),
      isDismissible: false,
      enableDrag: false,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12))),
      clipBehavior: Clip.antiAlias,
      builder: (_) => C1ShareTemplateDetailPage(templateDetail!),
    );
    if(value == null) {
      return;
    }
    // 2. 根据文件设置计算设计比例
    if (templateDetail.tubeFileSetting?.autoWidth == true) {
      TemplateDataTransform.generateDesignRatio(30, templateDetail.height.toDouble());
    } else {
      TemplateDataTransform.generateDesignRatio(templateDetail.width.toDouble(), templateDetail.height.toDouble());
    }

    // 3. 获取与模板关联的耗材信息
    Consumable consumable =
    hardWare!.consumables!.firstWhere((element) => element.parentProperty?.code == templateDetail!.consumableType);

    // 4. 根据规格ID获取耗材规格详情
    String? specId = templateDetail.tubeFileSetting?.specId;
    ConsumableSpec? consumableSpec;

    if (specId != null && specId.isNotEmpty) {
      final NiimbotCacheManager cacheManager = NiimbotCacheManager();
      final result = await cacheManager.findConsumableSpecById(
        consumableCode: templateDetail.consumableType.toString(),
        specId: specId,
        preferLocalData: true,
      );

      if (result.item3) {
        consumableSpec = result.item2;
      }
    }

    // 5. 检查并设置背景图
    if (templateDetail.backgroundImage.isEmpty && templateDetail.consumableType != 0 && specId != null) {
      if (consumableSpec != null &&
          consumableSpec.previewImageUrl != null &&
          consumableSpec.previewImageUrl!.isNotEmpty) {
        // 使用找到的规格图片作为背景图
        templateDetail = templateDetail.copyWith(
          backgroundImage: CopyWrapper<String?>.value(consumableSpec.previewImageUrl),
        );
      }
    }

    // 6. 准备编辑页面所需的参数
    // 耗材和长度信息显示
    String consumableInfo =
        '${intlanguage(consumable.parentProperty?.multilingualCode ?? '', consumable.parentProperty?.name ?? '')}: ${consumableSpec?.name ?? ''}';
    String lengthInfo = templateDetail.tubeFileSetting?.autoWidth == true
        ? ''
        : ' ${intlanguage('app100001513', '长')}${templateDetail.width.toInt()}mm';

    // 耗材类型和规格
    int tubeType = templateDetail.consumableType == 53 ? 2 : 1; // 1-线管，2-热缩管
    double size = consumableSpec?.height?.toDouble() ?? templateDetail.height.toDouble();

    // 文本格式设置
    TextFormat fileTextFormat =
    templateDetail.tubeFileSetting?.line == 2 ? TextFormat.doubleLine : TextFormat.singleLine;
    templateDetail.tubeFileSetting?.align = "";

    // 7. 进入画板编辑页
    Navigator.of(context)
        .push(MaterialPageRoute(
        builder: (_) => C1EditPage(
            templateData: templateDetail!,
            consumableInfo: consumableInfo,
            lengthInfo: lengthInfo,
            tubeType: tubeType,
            tubeSpecs: size,
            textFormat: fileTextFormat
        ),
        settings: RouteSettings(name: "c1_edit_page")))
        .then((value) {
      if (value) {
        refreshFileListFromLocal();
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    PrintSettingConfig.deviceConnected = false;
    NiimbotEventBus.getDefault().register(this, (data) async {
      if (data is Map && data.containsKey("action")) {
        if (data["action"] == "c1ConnectStatus") {
          //连接状态
          int connectStatus = data["connectStatus"];
          if (connectStatus == 0) {
            connectedState.value = C1ConnectedState.unConnected;
            ribbonState.value = RibbonState.installed;
            // if (data.containsKey("reason")) {
            //   int reason = data["reason"];
            //   //无法拿到当前页面的context，暂时不用自定义toast
            //   if (reason == 1) {
            //     showToast(msg: intlanguage("app00728", "连接被断开"));
            //   } else if (reason == 2) {
            //     showToast(msg: intlanguage("app00730", "连接失败"));
            //   }
            // }
            PrintSettingConfig.deviceConnected = false;
          } else if (connectStatus == 1) {
            connectedState.value = C1ConnectedState.connecting;
            ribbonState.value = RibbonState.connecting;
          } else if (connectStatus == 2) {
            connectedState.value = C1ConnectedState.connected;
            ribbonState.value = RibbonState.reading;
            PrintSettingConfig.deviceConnected = true;
          }
        } else if (data["action"] == "c1RibbonStatus") {
          //碳带状态
          int readRibbonStatus = data["ribbonStatus"];
          if (readRibbonStatus == 1) {
            ribbonState.value = RibbonState.adequate;
          } else if (readRibbonStatus == 2) {
            ribbonState.value = RibbonState.insufficient;
          } else if (readRibbonStatus == 3) {
            ribbonState.value = RibbonState.runOut;
          } else if (readRibbonStatus == 4) {
            ribbonState.value = RibbonState.unInstalled;
          } else if (readRibbonStatus == 5) {
            ribbonState.value = RibbonState.readFailed;
          } else if (readRibbonStatus == 6) {
            ribbonState.value = RibbonState.unMatched;
          } else {
            ribbonState.value = RibbonState.installed;
          }
        } else if (data["action"] == "c1ElectricityStatus") {
          int electricity = data["electricity"];
          if (electricity <= 4) {
            electricityLevel.value = 1;
          } else {
            electricityLevel.value = min((electricity.toDouble() / 10).round(), 10);
          }
        }
      } else if (data == "refreshFileList") {
        refreshFileList();
      } else if(data is Map && data.containsKey("C1TemplateShareCode")) {
        if(!Application.networkConnected) {
          EasyLoading.showToast(intlanguage('app01139', '网络异常'));
          return;
        }
        String shareCode = data["C1TemplateShareCode"];
        BuildContext context = _getContext();
        handleShareCode(shareCode, context);
      }
    });
    ToNativeMethodChannel.notifyEnterC1Page();
    refreshFileList();
  }

  @override
  void onClose() {
    super.onClose();
    C1FileBusiness.instance.removeSyncServiceTemplateFun(_syncServiceTemplateFun);
    NiimbotEventBus.getDefault().unregister(this);
    ToNativeMethodChannel.notifyBackFromC1Page();
    PrintSettingConfig.deviceConnected = false;
  }
}

extension connectStatus on C1HomeLogic {
  toConnectPage() {
    CustomNavigation.gotoNextPage('DeviceConnectC1HomePage', {});
    if (connectedState() == C1ConnectedState.unConnected) {
      ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "129_367_335", "ext": {}});
    }
  }
}

extension C1File on C1HomeLogic {
  /// 查找耗材
  void searchByConsumableBarcode({required BuildContext context, String keywords = '', String includeVip = ''}) async {
    // Map<String, dynamic> parameters = {
    //   "barcode": '69221100032288',
    // };
    Map<String, dynamic> parameters = {
      "barcode": keywords,
    };

    DioUtils.instance.requestNetwork<BarCodeConsumable>(Method.post, C1Api.searchByConsumableBarcode,
        params: parameters, isList: false, onSuccess: (model) async {
      final Function(bool) buriedAction = (result) {
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "click",
          "posCode": "129_368_337",
          "ext": {"type": result ? 1 : 0}
        });
      };
      if (model == null) {
        EasyLoading.showToast(intlanguage('app100001781', '请使用官方套管耗材'), duration: Duration(seconds: 1));
        buriedAction(false);
        return;
      }
      int? consumableCode = model.parentProperty?.code;
      String? specId = model.consumableSpecification?.id;

      if (hardWare?.consumables?.isNotEmpty == true && consumableCode != null && specId != null) {
        try {
          // Find the consumable in hardware consumables list
          Consumable? selectedConsumable;
          try {
            selectedConsumable = hardWare!.consumables!.firstWhere(
              (element) => element.parentProperty?.code == consumableCode,
              orElse: () => throw Exception('Consumable not found'),
            );
          } catch (_) {
            // Consumable not found
            selectedConsumable = null;
          }

          if (selectedConsumable != null) {
            // Use the NiimbotCacheManager to find the spec by ID
            final NiimbotCacheManager cacheManager = NiimbotCacheManager();
            var result = await cacheManager.findConsumableSpecById(
              consumableCode: consumableCode.toString(),
              specId: specId,
            );

            // If found, set the specification and save to SharedPreferences
            if (result.item3) {
              // item3 is the success flag
              ConsumableCategory? category = result.item1;
              ConsumableSpec? spec = result.item2;

              if (spec != null) {
                // Set the selected specification
                consumableSpecification.value = spec;

                // Save to SharedPreferences
                SharedPreferences prefs = await SharedPreferences.getInstance();
                prefs.setString('last_selected_consumable_code', consumableCode.toString());

                if (category != null && category.name != null) {
                  prefs.setString('last_selected_category', category.name!);
                }

                prefs.setString('last_selected_spec_id', spec.id!);
              }
            }
          }
        } catch (_, __) {
          EasyLoading.showToast(intlanguage('app100001781', '请使用官方套管耗材'), duration: Duration(seconds: 1));
          buriedAction(false);
          return;
        }
      }
      if (context.mounted) {
        showSuccessToast(context, intlanguage('app100001783', '识别成功'));
        buriedAction(true);
      }
    }, onError: (code, message) {});
  }

  selectConsumable(int selectedIndex) async {
    Consumable? select = consumables?[selectedIndex];
    // The code referencing consumable will cause errors now
    // if (select == null || select.parentProperty?.code == consumable()?.parentProperty?.code) {
    //   return;
    // }
    // consumable.value = select;
    consumableSpecification.value = null;
    // int? code = consumable()?.parentProperty?.code;
    // if (code != null) {
    //   SharedPreferences sp = await SharedPreferences.getInstance();
    //   sp.setInt("select_consumable", code);
    //   sp.remove("select_consumable_specification");
    // }
  }

  selectConsumableSpecification(int selectedIndex) async {
    // The code referencing consumableSpecifications will cause errors now
    // ConsumableSpec? specSelect = consumableSpecifications?[selectedIndex];
    // int? code = consumable()?.parentProperty?.code;
    // if (code != null && specSelect != null) {
    //   SharedPreferences sp = await SharedPreferences.getInstance();
    //   sp.setInt("select_consumable", code);
    //   consumableSpecification.value = specSelect;
    //   String? id = consumableSpecification()?.id;
    //   if (id != null) {
    //     SelectConsumableSpecification selectItem = SelectConsumableSpecification(code, id);
    //     sp.setString("select_consumable_specification", jsonEncode(selectItem.toJson()));
    //   }
    // }
  }

  /// 新建文件
  createDefaultFile({required BuildContext context, String? name, String? length = ''}) async {
    Consumable? consumable;
    // 获取选择的耗材代码、分类和规格ID
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? lastSelectedConsumableCode = prefs.getString('last_selected_consumable_code');
    if (lastSelectedConsumableCode != null && consumables != null) {
      consumable = consumables!
          .firstWhereOrNull((element) => element.parentProperty?.code.toString() == lastSelectedConsumableCode);
    }
    // 设置默认值
    name = name?.isEmpty ?? true ? intlanguage('app100001450', '未命名文件') : name;
    double settingWidth = double.tryParse(length ?? '30') ?? defaultLength.toDouble();
    double templateWidth = textLength() == TextLength.autoWidth ? defaultLength.toDouble() : settingWidth;
    // 获取打印盲区
    List<num> margin = consumableSpecification()?.margin ?? List.generate(4, (_) => 0);
    // 获取可打印高度
    double safeHeight = consumableSpecification()?.height?.toDouble() ?? 6;
    // 模版高度等于高度 + 上下盲区高度
    double templateHeight = safeHeight /*+ (margin[0] + margin[2]).toDouble()*/;
    // 从文件加载默认文件结构
    String? defaultJson = await rootBundle.loadString("assets/config/c1_default_template_file.json");
    try {
      int userId = Application.user?.userId ?? 0;
      // 从默认JSON创建文件
      TemplateData templateData = await TemplateParse.parse(defaultJson);
      // 计算当前模板在当前屏幕设备上的DP
      TemplateDataTransform.generateDesignRatio(templateWidth, templateHeight);
      // 生成默认元素
      Tuple2<List<TextElement>, ValueTypeProtocol> elementValuePair =
          EditTemplateDataTransform.createDefaultElementValue(
              templateWidth, templateHeight, margin, textFormat(), textLength() == TextLength.fixedWidth ? NetalTextAlign.center : NetalTextAlign.start);
      List<TextElement> elements = elementValuePair.item1;
      ValueTypeProtocol value = elementValuePair.item2;
      TubeFileSetting tubeFileSetting = TubeFileSetting();
      tubeFileSetting.autoWidth = textLength() == TextLength.autoWidth;
      tubeFileSetting.line = textFormat() == TextFormat.doubleLine ? 2 : 1;
      tubeFileSetting.align = "";
      tubeFileSetting.specId = consumableSpecification()!.id;
      tubeFileSetting.specName = consumableSpecification()!.name;
      tubeFileSetting.width = settingWidth;
      templateData.profile.extra.userId = userId.toString();
      if (textLength == TextLength.autoWidth) {
        templateWidth = elements.first.width + 0.3;
      }
      // 设置参数
      TemplateData defaultTemplateData = templateData.copyWith(
          // 设置名称
          name: name,
          // 设置耗材类型
          consumableType: consumable?.parentProperty?.code,
          // 设置耗材对应下的margin，上下左右盲区
          margin: consumableSpecification()?.margin,
          // 设置宽度
          width: templateWidth,
          // 设置规格对应的高度
          height: templateHeight,
          // 设置文本格式以及默认值
          values: [value],
          // 设置文本对齐方式
          elements: elements,
          // 设置背景图URL
          backgroundImage: CopyWrapper<String?>.value(consumableSpecification()?.previewImageUrl),
          tubeFileSetting: CopyWrapper<TubeFileSetting?>.value(tubeFileSetting),
          templateAttributes: [8]);
      String consumableInfo =
          intlanguage(consumable?.parentProperty?.multilingualCode ?? '', consumable?.parentProperty?.name ?? '') +
              ': ' +
              (consumableSpecification()?.name ?? '');
      String lengthInfo = '';
      if (textLength() == TextLength.fixedWidth) {
        lengthInfo = ' ${intlanguage('app100001513', '长')}${settingWidth}mm';
      }
      // 1-线管，2-热缩管
      // This will cause an error as consumable has been removed
      int tubeType = consumable?.parentProperty?.code == 53 ? 2 : 1;
      // 线管/热缩管规格尺寸
      double specSize = consumableSpecification()?.getSpecSize()?.toDouble() ?? 6;
      // 进入画板编辑页
      Navigator.of(context)
          .push(MaterialPageRoute(
              builder: (_) => C1EditPage(
                    templateData: defaultTemplateData,
                    consumableInfo: consumableInfo,
                    lengthInfo: lengthInfo,
                    tubeType: tubeType,
                    tubeSpecs: specSize,
                    textFormat: textFormat()
                  ),
              settings: RouteSettings(name: "c1_edit_page")))
          .then((value) {
        if (value) {
          refreshFileListFromLocal();
        }
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  refreshFileListFromLocal() async {
    _refreshController.footerMode?.value = LoadStatus.idle;
    C1FileBusiness.instance.requestTemplateListFromLocal(1, requestResult: (result) {
      _refreshController.refreshCompleted();
      if (result.length == 10) {
        _refreshController.footerMode?.value = LoadStatus.canLoading;
      }
      templateList.value = result;
      fileState.value = templateList()?.isNotEmpty == true ? 1 : 2;
      _scrollController.animateTo(0, duration: Duration(milliseconds: 50), curve: Curves.easeOut);
    });
  }

  refreshFileList() {
    _refreshController.footerMode?.value = LoadStatus.idle;
    C1FileBusiness.instance.requestTemplateList(1, requestResult: (result) {
      _refreshController.refreshCompleted();
      if (result.length == 10) {
        _refreshController.footerMode?.value = LoadStatus.canLoading;
      }
      templateList.value = result;
      fileState.value = templateList()?.isNotEmpty == true ? 1 : 2;
      page = 1;
    });
  }

  loadMoreFileList() {
    C1FileBusiness.instance.requestTemplateList(page + 1, requestResult: (result) {
      if (result.length == 10) {
        _refreshController.loadComplete();
      } else {
        _refreshController.loadNoData();
      }
      List<TemplateData> list = [];
      list.addAll(templateList.value!);
      list.addAll(result);
      templateList.value = list;
      page++;
    });
  }

  /// 编辑已存在的文件
  ///
  /// 加载文件详情并进入编辑页面
  /// @param context - 当前上下文
  /// @param index - 文件在列表中的索引
  editFile(BuildContext context, int index) async {
    // 1. 获取文件详情
    TemplateData templateData = templateList()![index];
    TemplateData? templateDetailTemp = await C1FileBusiness.instance
        .getTemplateDetailByTemplateId(templateData.id!, templateData.profile.extra.updateTime ?? "");

    if (templateDetailTemp == null) {
      return;
    }

    // 使用非空变量处理模板详情
    TemplateData templateDetail = templateDetailTemp;

    // 检查并补充规格ID信息（如果缺失）
    checkTemplateSpecId(templateDetail);

    // 2. 根据文件设置计算设计比例
    if (templateDetail.tubeFileSetting?.autoWidth == true) {
      TemplateDataTransform.generateDesignRatio(30, templateDetail.height.toDouble());
    } else {
      TemplateDataTransform.generateDesignRatio(templateDetail.width.toDouble(), templateDetail.height.toDouble());
    }

    // 3. 获取与模板关联的耗材信息
    Consumable consumable =
        hardWare!.consumables!.firstWhere((element) => element.parentProperty?.code == templateDetail.consumableType);

    // 4. 根据规格ID获取耗材规格详情
    String? specId = templateDetail.tubeFileSetting?.specId;
    ConsumableSpec? consumableSpec;

    if (specId != null && specId.isNotEmpty) {
      final NiimbotCacheManager cacheManager = NiimbotCacheManager();
      final result = await cacheManager.findConsumableSpecById(
        consumableCode: templateDetail.consumableType.toString(),
        specId: specId,
        preferLocalData: true,
      );

      if (result.item3) {
        consumableSpec = result.item2;
      }
    }

    // 5. 检查并设置背景图
    if (templateDetail.backgroundImage.isEmpty && templateDetail.consumableType != 0 && specId != null) {
      if (consumableSpec != null &&
          consumableSpec.previewImageUrl != null &&
          consumableSpec.previewImageUrl!.isNotEmpty) {
        // 使用找到的规格图片作为背景图
        templateDetail = templateDetail.copyWith(
          backgroundImage: CopyWrapper<String?>.value(consumableSpec.previewImageUrl),
        );
      }
    }

    // 6. 准备编辑页面所需的参数
    // 耗材和长度信息显示
    String consumableInfo =
        '${intlanguage(consumable.parentProperty?.multilingualCode ?? '', consumable.parentProperty?.name ?? '')}: ${consumableSpec?.name ?? ''}';
    String lengthInfo = templateDetail.tubeFileSetting?.autoWidth == true
        ? ''
        : ' ${intlanguage('app100001513', '长')}${templateDetail.width.toInt()}mm';

    // 耗材类型和规格
    int tubeType = templateDetail.consumableType == 53 ? 2 : 1; // 1-线管，2-热缩管
    double size = consumableSpec?.height?.toDouble() ?? templateDetail.height.toDouble();

    // 文本格式设置
    TextFormat fileTextFormat =
        templateDetail.tubeFileSetting?.line == 2 ? TextFormat.doubleLine : TextFormat.singleLine;
    templateDetail.tubeFileSetting?.align = "";

    // 7. 进入画板编辑页
    Navigator.of(context)
        .push(MaterialPageRoute(
            builder: (_) => C1EditPage(
                  templateData: templateDetail,
                  consumableInfo: consumableInfo,
                  lengthInfo: lengthInfo,
                  tubeType: tubeType,
                  tubeSpecs: size,
                  textFormat: fileTextFormat
                ),
            settings: RouteSettings(name: "c1_edit_page")))
        .then((value) {
      if (value) {
        refreshFileListFromLocal();
      }
    });
  }

  checkTemplateSpecId(TemplateData templateData) {
    String? specId = templateData.tubeFileSetting?.specId;
    if (specId != null && specId.isNotEmpty) {
      return;
    }
    Consumable consumable =
        hardWare!.consumables!.firstWhere((element) => element.parentProperty?.code == templateData.consumableType);
    String? specName = templateData.tubeFileSetting?.specName;
    if (specName != null && specName.isNotEmpty) {
      // 从消费者规格中找到名称匹配的spec
      for (var spec in consumable.consumableSpecifications!) {
        if (spec.name == specName) {
          templateData.tubeFileSetting?.specId = spec.id;
          return;
        }
      }
    }

    // 根据高度匹配规格
    for (var spec in consumable.consumableSpecifications!) {
      if (spec.height == templateData.height) {
        templateData.tubeFileSetting?.specId = spec.id;
        return;
      }
    }
  }

  renameFile(BuildContext context, int index) {
    TemplateData templateData = templateList()![index];
    String fileName = templateList()![index].name;
    showEditTextDialog(context, intlanguage('app01321', '重命名'), fileName, 50, (value) async {
      TemplateData? templateDataDetail = await C1FileBusiness.instance
          .getTemplateDetailByTemplateId(templateData.id!, templateData.profile.extra.updateTime ?? "");
      if (templateDataDetail == null) {
        return;
      }
      C1FileBusiness.instance.renameC1File(templateDataDetail, value, success: (result) {
        refreshFileListFromLocal();
      });
    }, selectAll: fileName.isNotEmpty, editTextEmptyToast: intlanguage('app100001666', '请输入文件名称'));
  }

  copyFile(int index) async {
    TemplateData templateData = templateList()![index];
    TemplateData? templateDataDetail = await C1FileBusiness.instance
        .getTemplateDetailByTemplateId(templateData.id!, templateData.profile.extra.updateTime ?? "");
    if (templateDataDetail == null) {
      return;
    }
    checkTemplateSpecId(templateDataDetail);
    C1FileBusiness.instance.copyC1File(templateDataDetail, success: (result) {
      refreshFileListFromLocal();
    });
  }

  deleteFile(BuildContext context, int index) {
    TemplateData templateData = templateList()![index];
    showCustomDialog(context, intlanguage('app100001665', '确定要删除当前文件吗？'), '',
        leftFunStr: intlanguage('app00030', '取消'),
        rightFunStr: intlanguage('app00063', '删除'),
        rightTextColor: ThemeColor.brand, rightFunCall: () {
      C1FileBusiness.instance.deleteC1File(templateData, success: () {
        refreshFileListFromLocal();
      });
    });
  }
}
