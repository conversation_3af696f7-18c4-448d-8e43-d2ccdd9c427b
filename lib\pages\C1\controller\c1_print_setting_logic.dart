import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:niimbot_print_setting_plugin/connect/printer_setting_manager.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:text/pages/C1/common/toast_util.dart';
import 'package:text/pages/C1/model/c1_print_manager.dart';
import 'package:text/pages/C1/model/define_model.dart';
import 'package:text/pages/C1/model/print_setting_config.dart';
import 'package:text/pages/C1/model/template_data_transform.dart';
import 'package:text/pages/C1/view/c1_print_offset_calibration.dart';
import 'package:text/pages/C1/view/c1_tube_error_alert.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/isolate_cancel_token.dart';
import 'package:text/utils/toast_util.dart';
import 'package:text/widget/adjust_bottom_sheet.dart' as adjust_bottom_sheet;

class C1PrintSettingLogic extends GetxController {
  //是否展开更多
  var expandMore = false.obs;

  //段落个数
  var paragraphCount = 1;

  //打印起始段落
  var paragraphStart = 1.obs;

  //打印结束段落
  late var paragraphEnd = 1.obs;

  //打印份数
  var printCount = 1.obs;

  //打印浓度
  var printDensity = 3.obs;

  var printDensityMin = 1;

  var printDensityMax = 5;

  //段落补偿（mm）
  var paragraphCompensation = 0.obs;

  //打印次序：重复优先，序号优先
  var repeatType = RepeatType.repeatPriority.obs;

  //切割方式：不切，半切
  var cutType = CutType.halfCut.obs;

  //切割深度：-3~3,默认为0
  var cutDepth = 0.obs;

  //是否打印分割线（只有不切的情况下才显示）
  var printDivider = false.obs;

  //是否手动调整过打印浓度
  var setDensityFlag = false;

  var deviceConnected = false.obs;

  late TemplateData templateData;

  late int tubeType;
  late double tubeSpecs;
  late TextFormat textFormat;
  late BuildContext Function() getBuildContext;

  IsolateCancelToken? cancellationToken;

  C1PrintSettingLogic(
      {required TemplateData templateData,
      required int tubeType,
      required double tubeSpecs,
      required TextFormat textFormat,
      required BuildContext Function() getBuildContext}) {
    this.templateData = templateData;
    this.tubeType = tubeType;
    this.tubeSpecs = tubeSpecs;
    this.textFormat = textFormat;
    this.getBuildContext = getBuildContext;
    this.paragraphCount = PrintTemplateDataTransform.getParagraphCount(templateData);
    this.paragraphEnd = paragraphCount.obs;
  }

  @override
  void onInit() {
    super.onInit();
    printDensity = PrintSettingConfig.printDensity.obs;
    printDensityMin = PrintSettingConfig.printDensityMin;
    printDensityMax = PrintSettingConfig.printDensityMax;
    paragraphCompensation = PrintSettingConfig.paragraphCompensation.obs;
    repeatType = RepeatType.values[PrintSettingConfig.repeatType].obs;
    cutType = CutType.values[PrintSettingConfig.cutType].obs;
    cutDepth = PrintSettingConfig.cutDepth.obs;
    printDivider = PrintSettingConfig.printDivider.obs;
    deviceConnected = PrintSettingConfig.deviceConnected.obs;
    NiimbotEventBus.getDefault().register(this, (data) async {
      if (data is Map) {
        if (data.containsKey("action")) {
          if (data["action"] == "c1ConnectStatus") {
            //连接状态
            int connectStatus = data["connectStatus"];
            if (connectStatus == 2) {
              deviceConnected.value = true;
            } else {
              deviceConnected.value = false;
            }
          }
        }
      } else if (data is String) {
        if (data == "printFinish") {
          CustomNavigation.pop();
        }
        else if(data == "printTubeException") {
          BuildContext context = getBuildContext.call();
          bool? action = await adjust_bottom_sheet.showModalBottomSheet(
            context: context,
            barrierColor: Colors.black.withOpacity(0.35),
            isDismissible: false,
            enableDrag: false,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12))),
            clipBehavior: Clip.antiAlias,
            builder: (_) => C1TubeErrorAlert(),
          );
          if(action ?? false) {
            int result = await PrinterSettingManager().reversePrinterFeed();
            if(result == 0) {
              ToastUtil.showSuccessToast(context, intlanguage('app100002015', '退管成功'));
            }
            else {
              showToast(msg: intlanguage('app100002016', '退管失败'));
            }
          }
        }
      }
    });
    // refreshParagraphDirtyImageCache();
  }

  void refreshParagraphDirtyImageCache() {
    final stopwatch = Stopwatch()..start();
    cancellationToken = IsolateCancelToken();
    EditTemplateDataTransform.refreshParagraphDirtyImageCacheInBackground(templateData, token: cancellationToken).then((value) {
      stopwatch.stop();
      debugPrint('onInit refreshParagraphDirtyImageCacheInBackground, execution time ${stopwatch.elapsedMilliseconds} ms');
    });
  }

  @override
  void onClose() {
    super.onClose();
    updatePrintConfig();
    NiimbotEventBus.getDefault().unregister(this);
    TemplateDataTransform.printTemplateList.clear();
    cancellationToken?.cancel();
    cancellationToken = null;
    C1PrintManager.instance.dispose();
  }

  updatePrintConfig() async {
    Map<String, dynamic> map = {};
    map["c1_print_density"] = printDensity.value;
    map["c1_paragraph_compensation"] = paragraphCompensation.value;
    map["c1_repeat_type"] = repeatType.value.index;
    map["c1_cut_type"] = cutType.value.index;
    map["c1_cut_depth"] = cutDepth.value;
    map["c1_print_divider"] = printDivider.value;
    map["set_density_flag"] = setDensityFlag;
    await PrintSettingConfig.updateCommonPrintConfig(map);
  }
}

extension DeviceType on C1PrintSettingLogic {
  //偏移校准
  toOffsetCalibrationPage(BuildContext context) async {
    if(!checkConnectStatus()) {
      return;
    }
    await PrintSettingConfig.loadOffsetConfig();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: false,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      builder: (BuildContext context) {
        // 返回 PrintSlowPage 作为底部导航的内容
        return PrintOffsetWidget();
      },
    );
  }

  //设备连接
  toDeviceConnectPage() {
    CustomNavigation.gotoNextPage('DeviceConnectC1HomePage', {});
  }
}

extension PrintActionSetting on C1PrintSettingLogic {
  //减小打印浓度
  subtractDensity() {
    printDensity.value -= 1;
    setDensityFlag = true;
  }

  //增加打印浓度
  addDensity() {
    printDensity.value += 1;
    setDensityFlag = true;
  }

  //减小段落补偿
  subtractParagraphCompensation() {
    paragraphCompensation.value -= 1;
  }

  //增加段落补偿
  addParagraphCompensation() {
    paragraphCompensation.value += 1;
  }
}

extension CutSetting on C1PrintSettingLogic {}

extension C1Print on C1PrintSettingLogic {
  handlePrint() async {
    //更新设置的打印参数
    await updatePrintConfig();
    C1PrintManager.instance.handlePrint(templateData, paragraphStart.value, paragraphEnd.value);
    int totalParagraphCount = C1PrintManager.instance.getTotalParagraphCount(printCount.value);
    TemplateData printTemplate = C1PrintManager.instance.getPrintTemplateData(0)!;
    Map<String, dynamic> map = {
      "paragraphCount": totalParagraphCount,
      "printCount": printCount.value,
      "niimbotTemplate": json.encode(printTemplate.toJson()),
      "resetDisplayMultiple": true,
      "printScene": "print_C1_ui",
      'uniAppInfo': {'uniAppId': 'FLUTTER_C1'},
      "customData": {
        "printCount": printCount.value,
        "printDensity": printDensity.value,
        "hOffset": PrintSettingConfig.hOffset,
        "vOffset": PrintSettingConfig.vOffset,
        "isHalfCut": cutType.value == CutType.halfCut,
        "cutType": cutType.value == CutType.halfCut ? 2 : (printDivider.value ? 1 : 0),
        "cutDepth": cutDepth.value,
        "printDivider": printDivider.value,
        "tubeType": tubeType,
        "tubeSpecs": tubeSpecs,
      }
    };
    CustomNavigation.gotoNextPage('C1PrintPage', map, withContainer: false);
  }

  bool checkConnectStatus() {
    if (deviceConnected.value) {
      return true;
    }
    CustomNavigation.gotoNextPage('DeviceConnectC1HomePage', {});
    return false;
  }
}
