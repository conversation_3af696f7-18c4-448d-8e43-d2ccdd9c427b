//
//  JCBluetoothConnectAlert.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2022/5/5.
//

#import "UILabel+YBAttributeTextTapAction.h"
#import "JCBluetoothWifiSwitchView.h"
#import "JCWifiManager.h"
#import "JCBluetoothModel.h"
#import "JCWifiConfigAlert.h"
#import "JCBluetoothCell.h"
#import "JCDeviceSeriesHelp.h"
#import "JCBluetoothSwitchGuideAlert.h"
#import <YYWebImage/YYWebImage.h>
#import "JCBluetoothCell.h"
#import "UIImage+GIF.h"
#import "Runner-swift.h"
#import "YLButton.h"
#import "JCPrinterConnectListAdaptor.h"
#import "JCBluetoothManager+Connect.h"
#import "JCBluetoothConnectAlert+Widget.h"
#import "NBCAPMiniAppEngine.h"
#import "JCBluetoothConnectAlert+DeviceInfoLogic.h"
#import "JCUpdateFirmAlert.h"
#import "MBProgressHUD.h"
#import "JCTemplateSaveAsView.h"
#import "NotificationMacro.h"

@interface JCBluetoothConnectAlert()<UITextViewDelegate>{

    JCPrinterConnectListAdaptor *deviceListAdaptor;
}

// 刷新打印机列表方法
- (void)refreshPrinterList;

@property(nonatomic,strong) UIView *headerPart1View;
@property(nonatomic,strong) UIView *headerPart2View;

@property(nonatomic,strong) UILabel *listTitleLabel;
@property(nonatomic,strong) UIView *printListView;
@property(nonatomic,strong) UIButton *searchBtn;
@property(nonatomic,strong) GroupShadowTableView *bluetoothTable;

@property(nonatomic,assign) JCPrinterDeviceConnectStatus currentState; // 0 搜索 1 已连接 2 正在连接  3蓝牙/WIFI未开启 4停止搜索状态  5未搜索到打印机
@property(nonatomic,copy) ScanStateBlock ScanStateBlock;
@property(nonatomic,copy) ConnectBlock connectBlock;
@property(nonatomic,copy) void(^discoveredDevices)(NSArray *devices);
@property(nonatomic,assign) BOOL canShowSwitchGuide;
@property(nonatomic,strong) NSMutableArray *bannerViewArr;
@property(nonatomic,assign) BOOL isFirstLoadSearch; // 不区分首次与否
@property(nonatomic,copy) NSString *connectTitle;
@property (nonatomic, assign) BOOL isSwitchMode;
@property(nonatomic,strong) JCBluetoothModel *switchBluetoothModel;
@end


@implementation JCBluetoothConnectAlert

static NSInteger bluetoothStateCount = 0;
static BOOL toSwithConnectType = NO;

- (instancetype)init{
    self = [super initWithAlertSize:CGSizeMake(SCREEN_WIDTH, SCREEN_HEIGHT - (iPhoneX?52:20)) navHeight:58];
    if(self){//
        XYWeakSelf
        self.connectTitle = XY_LANGUAGE_TITLE_NAMED(@"app100001650", @"连接设备中");
        UIViewController *currentController = [XYTool getCurrentVC];
        if([NSStringFromClass([currentController class]) containsString:@"NBCAPBridgeViewController"]){
            NSString *appName = [[NBCAPMiniAppManager sharedInstance] getUniAppNameWithAppId:JCBlUETOOTH_MANAGER.limitAppId];
            if(!STR_IS_NIL(appName)){
              self.connectTitle = [NSString stringWithFormat:@"%@-%@",appName,XY_LANGUAGE_TITLE_NAMED(@"app100001650", @"连接设备")];
            }
        }
        if([currentController isKindOfClass:[FBFlutterViewContainer class]]){
            if([((FBFlutterViewContainer*)currentController).name isEqualToString:@"eTag"]){
                NSString *appName = [[NBCAPMiniAppManager sharedInstance] getUniAppNameWithAppId:JC_ETAG];
                if(!STR_IS_NIL(appName)){
                  self.connectTitle = [NSString stringWithFormat:@"%@-%@",appName,XY_LANGUAGE_TITLE_NAMED(@"app100001650", @"连接设备")];
                }
            }else if([((FBFlutterViewContainer*)currentController).name isEqualToString:@"cableCanvas"]){
                NSString *appName = [[NBCAPMiniAppManager sharedInstance] getUniAppNameWithAppId:JC_Cable_Canvas];
                if(!STR_IS_NIL(appName)){
                  self.connectTitle = [NSString stringWithFormat:@"%@-%@",appName,XY_LANGUAGE_TITLE_NAMED(@"app100001650", @"连接设备")];
                }
            } else if([((FBFlutterViewContainer*)currentController).name isEqualToString:@"C1"]){
                NSString *appName = [[NBCAPMiniAppManager sharedInstance] getUniAppNameWithAppId:JC_C1];
                if(!STR_IS_NIL(appName)){
                  self.connectTitle = [NSString stringWithFormat:@"%@-%@",appName,XY_LANGUAGE_TITLE_NAMED(@"app100001650", @"连接设备")];
                }
            } else if([((FBFlutterViewContainer*)currentController).name isEqualToString:@"printSettingDialog"] && [currentController.presentingViewController isKindOfClass:[NBCAPBridgeViewController class]]){
                NSString *appName = [[NBCAPMiniAppManager sharedInstance] getUniAppNameWithAppId:JCBlUETOOTH_MANAGER.limitAppId];
                if(!STR_IS_NIL(appName)){
                  self.connectTitle = [NSString stringWithFormat:@"%@-%@",appName,XY_LANGUAGE_TITLE_NAMED(@"app100001650", @"连接设备")];
                }
            }
        }
        self.canShowSwitchGuide = YES;
        self.isFirstLoadSearch = YES;
        self.isSwitchMode = NO;
        deviceListAdaptor = [[JCPrinterConnectListAdaptor alloc] init];
        [self setupBluetoothUI];
        [self showAlertTitleWith: self.connectTitle];
        [self showRightButtonWithImage:XY_IMAGE_NAMED(@"bluetoothClose")];
        [self setRightBtnClickBlock:^{
            if(weakSelf.currentState == JCPRINTER_DEVICE_STOP_SEARCH && [[weakSelf listAdaptor] datas].count == 0 && !JC_IS_CONNECTED_PRINTER){
                JC_TrackWithparms(@"click",@"010_110_125",@{});
            }
            [weakSelf hiddenContentAlert];
        }];
        [self initBluetoothBlock];
        [JCBluetoothManager sharedInstance].needShowState = NO;
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(bluetoothStateChanged:) name:JCBlueToothOpenStateNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(batteryPowerChangeNotifation:) name:PrinterBatteryPowerNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(deviceAliasesUpdated:) name:@"DeviceAliasesUpdated" object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(printerStatusNotification:) name:PrinterStatusNotification object:nil];
        // 添加设备信息监听
        [self addDeviceInfoNotification];
        [self setCCurentState:JC_IS_CONNECTED_PRINTER?1:0];
        self.isShouldHideAlert = YES;
        NSLog(@"蓝牙- Wi-Fi：开启蓝牙弹窗%@",self);
    }
    return self;
}

- (void)dealloc{
    NSLog(@"蓝牙- Wi-Fi：蓝牙弹窗释放%@",self);
}


- (void)initBluetoothBlock{
    XYWeakSelf
    JCBluetoothManager *bluetoothManager = [JCBluetoothManager sharedInstance];
    self.ScanStateBlock = ^(JCScanState state, NSString *message) {
        if (state == JCScanStateSearching) {
            if(!JC_IS_CONNECTED_PRINTER || weakSelf.isSwitchMode){
                JCBluetoothModel *model = nil;
                if(weakSelf.isSwitchMode){
                    model = weakSelf.switchBluetoothModel;
                    [weakSelf setCurrentState:JCPRINTER_DEVICE_CONNECTTING model:model];
                }else{
                    [weakSelf setCurrentState:JCPRINTER_DEVICE_SEARCHING model:model];
                }

            }else{
                JCBluetoothModel *connectedModel = bluetoothManager.connectedModel;
                [weakSelf setCurrentState:JCPRINTER_DEVICE_CONNECTED model:connectedModel];
            }
        }else{
            if (state ==  JCScanStateAllClose) {
                [weakSelf cleanPrinterList];
            }
            else if (state == JCScanStateEnd) {
                if(JC_IS_CONNECTED_PRINTER){
                    JCBluetoothModel *connectedModel = bluetoothManager.connectedModel;
                    [weakSelf setCurrentState:JCPRINTER_DEVICE_CONNECTED model:connectedModel];
                }else{
                    [weakSelf setCurrentState:JCPRINTER_DEVICE_STOP_SEARCH model:nil];
                    UILabel *stateLabel = [weakSelf.stopSearchView viewWithTag:30001];
                    stateLabel.text = @"";
                }
            }
        }
        [bluetoothManager updateCurrentConnectPrinter];
    };
    self.connectBlock = ^(JCConnectState state, JCBluetoothModel * _Nullable model){
        dispatch_async(dispatch_get_main_queue(), ^{
           switch (state) {
               case JCConnectStateInvaild:
               {
                   if(!bluetoothManager.isAutoConnect){
                       [weakSelf showPrinterOperateErrorTipView:2];
                   }
                   break;
               }
               case JCConnectStateRepeat:
               {
                   if(!bluetoothManager.isAutoConnect){
                       [weakSelf showPrinterOperateErrorTipView:2];
                   }
                   break;
               }
               case JCConnectStateFailed:
               {
                   if(!bluetoothManager.isAutoConnect){
                       [weakSelf showPrinterOperateErrorTipView:2];
                   }
                   break;
               }
               case JCConnectStateOuttime:
               {
                   if(!bluetoothManager.isAutoConnect){
                       [weakSelf showPrinterOperateErrorTipView:2];
                   }
                   break;
               }
               case JCConnectStateDisconnected:
               {
                   if(!bluetoothManager.closeByHand){
//                       [weakSelf showPrinterOperateErrorTipView:2];
                       [weakSelf setCurrentState:JCPRINTER_DEVICE_SEARCHING model:model];
                       // 重新开始新的扫描
                       [[JCBluetoothManager sharedInstance] startScan:@"" searchType:SEARCH_ALL];
                   }else{
                       if(weakSelf.currentState != JCPRINTER_DEVICE_STOP_SEARCH && weakSelf.currentState != JCPRINTER_DEVICE_CONNECTTING){
                           [weakSelf setCurrentState:JCPRINTER_DEVICE_SEARCHING model:nil];
                           if([[JCBluetoothManager sharedInstance] getBluetoothState] == 4){
                               [weakSelf showDeviceConnectStatus];
                           }
                       }
                   }
                   break;
               }
               case JCConnectStateSuccess:
               {
                   toSwithConnectType = NO;
                   [weakSelf setCurrentState:JCPRINTER_DEVICE_CONNECTED model:model];
                   for(NSInteger index = 0; index < [[weakSelf listAdaptor] datas].count; index++){
                       JCBluetoothModel *blueModel = [[weakSelf listAdaptor] datas][index];
                       if([blueModel.name isEqualToString:model.name]){
                           [[[weakSelf listAdaptor] datas] removeObject:blueModel];
                           break;
                       }
                   }

                   [weakSelf refreshPrinterList];
                   dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                       UIView *topView = [UIApplication sharedApplication].keyWindow.subviews.lastObject;
                       if (JC_IS_CONNECTED_PRINTER && weakSelf.isShouldHideAlert) {
                           if (![topView isMemberOfClass:[JCUpdateFirmAlert class]]) {
                               [weakSelf hiddenContentAlert];
                           }
                       }
                       weakSelf.isShouldHideAlert = YES;
                   });
                   break;
               }
               case JCConnectStateConnecting:
               {
                   [weakSelf setCurrentState:JCPRINTER_DEVICE_CONNECTTING model:model];
                   break;
               }
               default:
                   break;
           }
            [weakSelf refreshPrinterList];
        });
    };
    self.discoveredDevices = ^(NSArray *devices) {
        //对设备列表只能增加，不能减少

        __block NSMutableArray *diffDevices = @[].mutableArray;

        __block NSMutableArray *originalDeviceNameList = @[].mutableArray;
        [[[weakSelf listAdaptor] datas] enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            JCBluetoothModel *aModel = (JCBluetoothModel*)obj;
            [originalDeviceNameList addObject:aModel.name];
        }];
        if(devices && [devices count]){
            [devices enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                JCBluetoothModel *aModel = (JCBluetoothModel*)obj;
                if(![originalDeviceNameList containsObject:aModel.name]){
                    [diffDevices addObject:aModel];
                }else if(!STR_IS_NIL(aModel.host)){
                    JCBluetoothModel *printerBluetoothModel = [weakSelf getBluetoothModelWithName:aModel.name];
                    printerBluetoothModel.host = aModel.host;
                }
            }];
        }

        if([diffDevices count] > 0){
            int originalCount =  [[[weakSelf listAdaptor] datas] count];
            [[weakSelf listAdaptor]  addDatas:diffDevices];
            NSMutableArray *indexes = @[].mutableArray;
            for(int i = 0; i < [diffDevices count]; i++ ){
                [indexes addObject:[NSIndexPath indexPathForRow: originalCount + i inSection:0]];
            }
            [weakSelf refreshPrinterList];
        }
    };
    deviceListAdaptor.connectResultBlock = self.connectBlock;
    [deviceListAdaptor setCloseConnectAlertBlock:^{
        [weakSelf hiddenContentAlert];
    }];
}

- (JCBluetoothModel *)getBluetoothModelWithName:(NSString *)printerName{
    __block JCBluetoothModel *printerBluetoothModel = nil;
    [[[self listAdaptor] datas] enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        JCBluetoothModel *aModel = (JCBluetoothModel*)obj;
        if([printerName isEqualToString:printerName]){
            printerBluetoothModel = aModel;
        }
    }];
    return printerBluetoothModel;
}

static XYNormalBlock guideAlertBlock = nil;
- (void)showDeviceConnectStatus{
    JCBluetoothAndWifiOpenStatus status;
    JCBluetoothManager *connectManager = [JCBluetoothManager sharedInstance];
    if(![connectManager isWiFiEnabled]){
        UILabel *stateTipLabel = [self.stopSearchView viewWithTag:30002];
        stateTipLabel.text = iPhoneX?XY_LANGUAGE_TITLE_NAMED(@"app100000899", @"请从屏幕顶端下滑打开蓝牙和Wi-Fi"):XY_LANGUAGE_TITLE_NAMED(@"app100000900", @"请从屏幕顶端上滑打开蓝牙和Wi-Fi");
        UILabel *stateDescLabel = [self.stopSearchView viewWithTag:30006];
        stateDescLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app100000898", @"（部分机型可通过WiFi连接）");
        [self setCurrentState:JCPRINTER_DEVICE_ALL_CLOSE model:nil];

        if(!([connectManager getDeviceBluetoothState]) && ![connectManager isWiFiEnabled]){
            status = JCBluetoothAndWifiClosed;
        }else if(![connectManager getDeviceBluetoothState]){
            status = JCWiFiOpen;
        }else if(![connectManager isWiFiEnabled]){
            status = JCBluetoothOpen;
        }
    }
    JCBluetoothSwitchGuideAlert *alert = [[JCBluetoothSwitchGuideAlert alloc] initWithType:JCBluetoothAndWifiClosed];
    [alert showContentAlert];
    guideAlertBlock = ^(){
        [alert hiddenContentAlert];
    };
}

/// 蓝牙连接状态变动回调
- (void)bluetoothStateChanged:(NSNotification*)noti {
    NSNumber *stateNumber = noti.object;
    if(stateNumber.boolValue){
        //
    }
}

-(void)batteryPowerChangeNotifation:(NSNotification *)noti
{
    if(JC_IS_CONNECTED_PRINTER){
        XYWeakSelf
        NSString *batteryPower = [JCPrintDevice shareDevice].batteryPower;
        UIImageView *batteryLevelView = [self.connectView viewWithTag:20004];
        NSString *imageName = STR_IS_NIL(batteryPower)?@"":[NSString stringWithFormat:@"电量%@%%",batteryPower];
        UIImage *batteryImage = [XY_IMAGE_NAMED(imageName) imageFlippedForRightToLeftLayoutDirection];
        batteryLevelView.image = batteryImage;
        batteryLevelView.hidden = STR_IS_NIL(batteryPower);
        // 更新电量图标宽度约束
        [batteryLevelView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(@(STR_IS_NIL(batteryPower) ? 0 : 24));
        }];
        UILabel *connectLabel = [self.connectView viewWithTag:20005];
        NSInteger offset = STR_IS_NIL(batteryPower) ? 0 :self.isRTL ? 15 : -15;
        [connectLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(weakSelf.connectView).offset(offset);
        }];
    }
}

// 处理设备别名更新通知
- (void)deviceAliasesUpdated:(NSNotification *)noti {
    [self refreshPrinterList];
}

- (void)setupBluetoothUI{
    XYWeakSelf
    self.backView.backgroundColor = HEX_RGB(0xF7F7FA);
    self.contentView.backgroundColor = HEX_RGB(0xF7F7FA);
    [self.contentView addSubview:self.headerView];
    [self.contentView addSubview:self.printListView];
    [self.contentView addSubview:self.noPrinterView];
    [self.contentView addSubview:self.deviceInfoView];
    [self initAdaptor];
    [self.headerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(weakSelf.contentView);
        make.leading.trailing.equalTo(weakSelf.contentView);
        make.height.mas_equalTo(@(XY_AutoWidth(310)));
    }];
    [self.printListView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(weakSelf.headerView.mas_bottom).offset(15);
        make.leading.trailing.equalTo(weakSelf.contentView);
        make.bottom.equalTo(weakSelf.contentView);
    }];
    [self.noPrinterView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(weakSelf.contentView);
    }];
    [self.deviceInfoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(weakSelf.headerView.mas_bottom).offset(10);
        make.leading.trailing.equalTo(weakSelf.contentView);
        make.bottom.equalTo(weakSelf.contentView);
    }];
}

#pragma mark - UITextViewDelegate ----核心代码
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-implementations"
- (BOOL)textView:(UITextView *)textView shouldInteractWithURL:(NSURL *)URL inRange:(NSRange)characterRange {
    if ([[URL scheme] isEqualToString:@"helpCenterProtocol"]) {
        if(self.operateBlock){
            JC_TrackWithparms(@"click",@"010_110_123",@{});
            self.operateBlock(@2);
        }
        return NO;
    }
    return YES;
}


- (UIView *)printListView{
    if(!_printListView){
        _printListView = [[UIView alloc] init];
        UIView *lineView = [[UIView alloc] init];
        lineView.tag = 40002;
        lineView.backgroundColor = HEX_RGB(0xEBEBEB);
        [_printListView addSubview:lineView];
        UILabel *listTitleLabel = [[UILabel alloc] init];
        listTitleLabel.font = MY_FONT_Regular(13);
        listTitleLabel.textColor = HEX_RGB(0x262626);
        listTitleLabel.numberOfLines = 0;
        listTitleLabel.tag = 40001;
        listTitleLabel.textAlignment = NSTextAlignmentLeft;
        listTitleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app100001516", @"搜索到以下设备");
        [_printListView addSubview:listTitleLabel];
        [_printListView addSubview:self.bluetoothTable];
        [lineView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(_printListView);
            make.leading.equalTo(_printListView).offset(18);
            make.trailing.equalTo(_printListView).offset(-18);
            make.height.mas_equalTo(@0.5);
        }];
        [listTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(_printListView).offset(20);
            make.leading.equalTo(_printListView).offset(18);
            make.trailing.equalTo(_printListView).offset(-18);
        }];
        [self.bluetoothTable mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(listTitleLabel.mas_bottom);
            make.leading.trailing.bottom.equalTo(_printListView);
        }];
    }
    return _printListView;
}

-(GroupShadowTableView *)bluetoothTable
{
    if (!_bluetoothTable) {
        _bluetoothTable = [[GroupShadowTableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
        _bluetoothTable.groupShadowDelegate = self;
        _bluetoothTable.groupShadowDataSource = self;
        _bluetoothTable.separatorStyle = UITableViewCellSeparatorStyleNone;
        _bluetoothTable.showsVerticalScrollIndicator = YES;
        _bluetoothTable.showSeparator = NO;
        _bluetoothTable.bounces = NO;
        _bluetoothTable.addShadow = NO;
        _bluetoothTable.backgroundColor = XY_HEX_RGB(0xF7F7FA);
        _bluetoothTable.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 25)];
        [_bluetoothTable registerClass:[JCBluetoothCell class] forCellReuseIdentifier:JCPrinterCellIdentifier];
    }
    return _bluetoothTable;
}

- (void)setCurrentState:(JCPrinterDeviceConnectStatus)currentState model:(JCBluetoothModel *)model{
    XYWeakSelf
//    if(_currentState == JCPRINTER_DEVICE_CONNECTED && currentState == JCPRINTER_DEVICE_SEARCHING) return;
    [self setCCurentState: currentState];
    self.isShowTitle = YES;
    [self showAlertTitleWith: self.connectTitle];
    switch (currentState) {
        case JCPRINTER_DEVICE_SEARCHING:
        {
            NSLog(@"蓝牙- Wi-Fi：蓝牙新交互：进入搜索状态");
            self.connectView.hidden = YES;
            self.searchingView.hidden = NO;
            self.stopSearchView.hidden = YES;
            self.noPrinterView.hidden = YES;
            self.deviceInfoView.hidden = YES;
            self.printListView.hidden = NO;
            [self.searchingView layoutIfNeeded];
            UIView *bottomView = [self.searchingView viewWithTag:10002];
            bottomView.hidden = NO;
            UIView *searchingTipContentView = [self.searchingView viewWithTag:10003];
            searchingTipContentView.hidden = YES;
            float height = bottomView.bottom;
            [UIView animateWithDuration:self.isFirstLoadSearch?0:0.3 animations:^{
                [weakSelf.headerView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.mas_equalTo(@(height + 5));
                }];
                [weakSelf.headerView.superview layoutIfNeeded];
            } completion:^(BOOL finished) {
            }];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                NSLog(@"蓝牙新交互：进入搜索提示状态%ld",_currentState);
                if(_currentState == JCPRINTER_DEVICE_SEARCHING){
                    NSLog(@"蓝牙新交互：刷新提示状态");
                    bottomView.hidden = YES;
                    searchingTipContentView.hidden = NO;
                    float height = searchingTipContentView.bottom;
                    [UIView animateWithDuration:0.3 animations:^{
                        [self.headerView mas_updateConstraints:^(MASConstraintMaker *make) {
                            make.height.mas_equalTo(@(height + 5));
                        }];
                        [self.headerView.superview layoutIfNeeded];
                    } completion:^(BOOL finished) {
                    }];
                }
            });
            break;
        }
        case JCPRINTER_DEVICE_CONNECTED:
        {
            NSLog(@"蓝牙- Wi-Fi：蓝牙新交互：进入已连接状态");
            self.connectView.hidden = NO;
            self.searchingView.hidden = YES;
            self.stopSearchView.hidden = YES;
            self.noPrinterView.hidden = YES;
            self.printListView.hidden = YES;
            self.deviceInfoView.hidden = NO;
            // 刷新头部展示信息
            JCBluetoothModel *connectedModel = [JCBluetoothManager sharedInstance].connectedModel;
            UIImageView *printerImageView = [self.connectView viewWithTag:200001];
            [self setConnectPrinterImage:connectedModel imageView:printerImageView];
            UIImageView *connectingImageView = [self.connectView viewWithTag:20003];
            UIImageView *connectedImageView = [self.connectView viewWithTag:20008];
            UILabel *stateLabel = [self.connectView viewWithTag:20002];
            [stateLabel setHidden:YES];

            // 蓝牙WIFI切换
            NSString *connectType = @"";
            NSString *swithStr = @"";
            UIImage *connectTypeImage = XY_IMAGE_NAMED(@"bluetooth_connect");
            if(JC_CURRENT_CONNECTTYPE == JCConnectStateTypeBlueTooth){
                connectType = XY_LANGUAGE_TITLE_NAMED(@"app100000896", @"蓝牙");
                swithStr = XY_LANGUAGE_TITLE_NAMED(@"app100000551", @"切换至Wi-Fi连接");
            }else if(JC_CURRENT_CONNECTTYPE == JCConnectStateTypeWifi){
                connectType = XY_LANGUAGE_TITLE_NAMED(@"", @"Wi-Fi");
                swithStr = XY_LANGUAGE_TITLE_NAMED(@"app100000550", @"切换至蓝牙连接");
                connectTypeImage = XY_IMAGE_NAMED(@"wifi_connect");
            }
            UIButton *switchBtn = [self.connectView viewWithTag:20009];
            [switchBtn setTitle:swithStr forState:UIControlStateNormal];
            BOOL isHiddenSwitch = !JC_IS_CONNECTED_PRINTER || connectedModel.printer.isSupportWifi.integerValue == 0 || toSwithConnectType;
            switchBtn.hidden = isHiddenSwitch;

            // 电量
            NSString *batteryPower = [JCPrintDevice shareDevice].batteryPower;
            NSString *imageName = STR_IS_NIL(batteryPower)?@"":[NSString stringWithFormat:@"电量%@%%",batteryPower];
            UIImage *batteryImage = [XY_IMAGE_NAMED(imageName) imageFlippedForRightToLeftLayoutDirection];
            UIImageView *batteryLevelView = [self.connectView viewWithTag:20004];
            batteryLevelView.image = batteryImage;
            batteryLevelView.hidden = STR_IS_NIL(batteryPower);
            // 更新电量图标宽度约束
            [batteryLevelView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(@(STR_IS_NIL(batteryPower) ? 0 : 24));
            }];
            
            // 修改别名按钮
            UIButton *editNameBtn = [self.connectView viewWithTag:20010];
             // 根据是否支持别名功能决定是否显示编辑按钮
            editNameBtn.hidden = ![JCBluetoothManager sharedInstance].connectedNetyModel.supportAlias;

            // 机器名称
            UILabel *connectLabel = [self.connectView viewWithTag:20005];
            connectLabel.text = STR_IS_NIL(connectedModel.alias) ? connectedModel.name : connectedModel.alias;

            // 连接类型
            UIButton *connectTypeBtn = [self.connectView viewWithTag:20006];
            // 设置连接类型
            [connectTypeBtn setImage:connectTypeImage forState:UIControlStateNormal];
            UIButton *disConnectBtn = [self.connectView viewWithTag:20007];
            connectTypeBtn.hidden = NO;

            disConnectBtn.hidden = NO;
            connectingImageView.hidden = YES;
            connectedImageView.hidden = NO;

            // 约束更新, 判断电量以及是否支持别名功能
            NSInteger offset = STR_IS_NIL(batteryPower) ? 0 :self.isRTL ? 15 : -15;
            offset = [JCBluetoothManager sharedInstance].connectedNetyModel.supportAlias ? self.isRTL ? 30 : -30 : 0;
            [connectLabel mas_updateConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(weakSelf.connectView).offset(offset);
            }];

            UIView *layoutGuide = [self.connectView viewWithTag:20240828];

            if (isHiddenSwitch) {
                // 不展示切换，代表只能单连
                [connectTypeBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.leading.equalTo(layoutGuide);
                    make.trailing.equalTo(disConnectBtn.mas_leading).inset(10);
                    make.centerY.equalTo(disConnectBtn);
                }];

                float titleWidth = [XY_LANGUAGE_TITLE_NAMED(@"app00789", @"断开") jk_sizeWithFont:MY_FONT_Medium(14) constrainedToHeight:1000].width;
                [disConnectBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(connectLabel.mas_bottom).offset(4);
                    make.height.mas_equalTo(@(32));
                    make.width.mas_equalTo(@(titleWidth + 44));
                    make.trailing.equalTo(layoutGuide);
                }];

                [switchBtn mas_remakeConstraints:^(MASConstraintMaker *make) {

                }];
            } else {
                // 展示切换，多连
                [connectTypeBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.centerX.equalTo(self.connectViewC);
                    make.top.equalTo(connectLabel.mas_bottom).offset(4);
                }];

                float titleWidth = [XY_LANGUAGE_TITLE_NAMED(@"app00789", @"断开") jk_sizeWithFont:MY_FONT_Medium(14) constrainedToHeight:1000].width;
                [disConnectBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(connectTypeBtn.mas_bottom).offset(5);
                    make.height.mas_equalTo(@(32));
                    make.width.mas_equalTo(@(titleWidth + 44));
                    make.trailing.equalTo(layoutGuide);
                }];

                float width = [XY_LANGUAGE_TITLE_NAMED(@"app100000551", @"切换到Wi-Fi连接") jk_sizeWithFont:MY_FONT_Regular(14) constrainedToWidth:1000].width + 18 + 28;
                [switchBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.trailing.equalTo(disConnectBtn.mas_leading).inset(10);
                    make.centerY.equalTo(disConnectBtn);
                    make.height.mas_equalTo(@((isHiddenSwitch? 0: 32)));
                    make.width.mas_equalTo(@(width));
                    make.leading.equalTo(layoutGuide);
                }];
            }

            [disConnectBtn.superview layoutIfNeeded];
            float bottom = disConnectBtn.bottom;
            [self.headerView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.mas_equalTo(@(bottom + 10));
            }];

            [self.headerView.superview layoutIfNeeded];

            // 检查RFID识别信息
            [self checkDeviceRFIDInfoState];
            // 检查设备详细信息
            [self checkDeviceDetailInfoState];
            break;
        }
        case JCPRINTER_DEVICE_CONNECTTING:
        {
            NSLog(@"蓝牙- Wi-Fi：蓝牙新交互：进入正在连接状态");
            self.connectView.hidden = NO;
            self.searchingView.hidden = YES;
            self.stopSearchView.hidden = YES;
            self.noPrinterView.hidden = YES;
            self.deviceInfoView.hidden = YES;
            self.printListView.hidden = NO;
            UILabel *connectLabel = [self.connectView viewWithTag:20005];
            UIImageView *printerImageView = [self.connectView viewWithTag:20001];
            [self setConnectPrinterImage:model imageView:printerImageView];
            UIImageView *connectingImageView = [self.connectView viewWithTag:20003];
            UIImageView *connectedImageView = [self.connectView viewWithTag:20008];
            UILabel *stateLabel = [self.connectView viewWithTag:20002];
            stateLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app01415", @"正在连接");
            stateLabel.font = MY_FONT_Medium(16);
            stateLabel.textColor = HEX_RGB(0x999999);
            [stateLabel setHidden:NO];
            UIButton *connectTypeBtn = [self.connectView viewWithTag:20006];
            UIButton *disConnectBtn = [self.connectView viewWithTag:20007];
            UIButton *switchBtn = [self.connectView viewWithTag:20009];
            UIImageView *batteryLevelView = [self.connectView viewWithTag:20004];
            UIButton *editNameBtn = [self.connectView viewWithTag:20010];
            editNameBtn.hidden = YES;
            connectLabel.text = STR_IS_NIL(model.alias) ? model.name : model.alias;
            batteryLevelView.hidden = YES;
            // 更新电量图标宽度约束
            [batteryLevelView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(@0);
            }];
            connectTypeBtn.hidden = YES;
            switchBtn.hidden = YES;
            disConnectBtn.hidden = YES;
            connectingImageView.hidden = NO;
            connectedImageView.hidden = YES;
            [switchBtn mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.mas_equalTo(@(0));
                make.top.equalTo(connectLabel.mas_bottom).offset(20);
            }];
            [self.connectView layoutIfNeeded];
            float bottom = connectLabel.bottom;
            [UIView animateWithDuration:0.3 animations:^{
                [self.headerView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.mas_equalTo(@(bottom + 5));
                }];
                [self.headerView.superview layoutIfNeeded];
            } completion:^(BOOL finished) {
            }];
            [connectLabel mas_updateConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(weakSelf.connectView);
            }];
            [connectLabel.superview layoutIfNeeded];
            bottom = connectLabel.bottom;
            [self.headerView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.mas_equalTo(@(bottom + 40));
            }];
            break;
        }
        case JCPRINTER_DEVICE_ALL_CLOSE:
        case JCPRINTER_DEVICE_NOTING_SEARCH:
        {
            NSLog(@"蓝牙- Wi-Fi：蓝牙新交互：进入蓝牙未打开状态");
            self.connectView.hidden = YES;
            self.searchingView.hidden = YES;
            self.stopSearchView.hidden = NO;
            self.noPrinterView.hidden = YES;
            self.deviceInfoView.hidden = YES;
            UILabel *stateLabel = [self.stopSearchView viewWithTag:30001];
            stateLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app100000890", @"蓝牙和Wi-Fi未打开");
            UILabel *stateTipLabel = [self.stopSearchView viewWithTag:30002];
            stateTipLabel.hidden = NO;
            UILabel *stateWBDescLabel = [self.stopSearchView viewWithTag:30006];
            stateWBDescLabel.hidden = NO;
            UIButton *searchButton1 = [self.stopSearchView viewWithTag:30003];
            searchButton1.hidden = NO;
            UIButton *searchButton2 = [self.stopSearchView viewWithTag:30004];
            searchButton2.hidden = YES;
            UIButton *searchButton3 = [self.stopSearchView viewWithTag:30005];
            [self.stopSearchView layoutIfNeeded];
            float bottom = 0;
            if(currentState == JCPRINTER_DEVICE_ALL_CLOSE){
                bottom = stateTipLabel.bottom;
                searchButton3.hidden = YES;
            }else{
                bottom = searchButton3.bottom;
                searchButton3.hidden = NO;
            }
            [self.headerView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.mas_equalTo(@(bottom));
                [weakSelf.headerView.superview layoutIfNeeded];
            }];
            break;
        }
        case JCPRINTER_DEVICE_STOP_SEARCH:
        {
            NSLog(@"蓝牙- Wi-Fi：蓝牙新交互：进入停止搜索状态");
            self.connectView.hidden = YES;
            self.searchingView.hidden = YES;
            self.stopSearchView.hidden = NO;
            self.deviceInfoView.hidden = YES;
            BOOL hiddenNoDevice = YES;
            if(!STR_IS_NIL([JCBluetoothManager sharedInstance].limitAppId)){
                BOOL hasSupportDevice = NO;
                for (JCBluetoothModel *model in [[self listAdaptor] datas]) {
                    JCSelectDeviceModel *deviceModel = model.printerModel;
                    if(deviceModel.isNotSupport.integerValue != 1){
                        hasSupportDevice = YES;
                    }
                }
                hiddenNoDevice = ([[self listAdaptor] datas].count >0 && hasSupportDevice) || JC_IS_CONNECTED_PRINTER;
                if(!hiddenNoDevice){
                    JC_TrackWithparms(@"show",@"050_122",@{});
                    [self showPrinterOperateErrorTipView:1];
                }else{
                    [self hiddenPrinterOperateErrorTipView];
                }
            }else{
                hiddenNoDevice = ([[self listAdaptor] datas].count >0 || JC_IS_CONNECTED_PRINTER);
                if(!hiddenNoDevice){
                    JC_TrackWithparms(@"show",@"010_110",@{});
                    [self showPrinterOperateErrorTipView:1];
                }else{
                    [self hiddenPrinterOperateErrorTipView];
                }
            }//
            self.isShowTitle = ([[self listAdaptor] datas].count > 0 || JC_IS_CONNECTED_PRINTER);
            UILabel *stateTipLabel = [self.stopSearchView viewWithTag:30002];
            stateTipLabel.hidden = YES;
            UILabel *stateLabel = [self.stopSearchView viewWithTag:30001];
            stateLabel.text = XY_LANGUAGE_TITLE_NAMED(@"", @"");
            UIButton *searchButton1 = [self.stopSearchView viewWithTag:30003];
            searchButton1.hidden = NO;
            UIButton *searchButton2 = [self.stopSearchView viewWithTag:30004];
            searchButton2.hidden = NO;
            UIButton *searchButton3 = [self.stopSearchView viewWithTag:30005];
            searchButton3.hidden = YES;
            UILabel *stateDescLabel = [self.stopSearchView viewWithTag:30006];
            stateDescLabel.hidden = YES;
            float bottom = searchButton2.bottom;
            [UIView animateWithDuration:0.3 animations:^{
                [self.headerView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.mas_equalTo(@(bottom + 22));
                }];
                [self.headerView.superview layoutIfNeeded];
            } completion:^(BOOL finished) {
            }];
            break;
        }
        default:
            break;
    }
    if(currentState != JCPRINTER_DEVICE_SEARCHING){
        self.isFirstLoadSearch = YES;
    }
}

- (void)hiddenContentAlert{
    [super hiddenContentAlert:^{
    }];
    if(guideAlertBlock){
        guideAlertBlock();
    }
    self.canShowSwitchGuide = NO;
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    UIViewController *currentVC = [XYTool getCurrentVC];
    if(self.dismissConnectBlock){
        self.dismissConnectBlock();
    }
    [[JCBluetoothManager sharedInstance] cancelScan:nil];
    // 是否正在弹出，弹出需关闭
    if ([[XYTool getCurrentVC] isMemberOfClass:[ContextMenuController class]]) {
        [[XYTool getCurrentVC] dismissViewControllerAnimated:YES completion:nil];
    }
    [JCBluetoothManager sharedInstance].isFirstBluetoothAlertSearching = NO;
}

- (void)hiddenContentAlert:(void (^)(void))completion{
    [super hiddenContentAlert:^{
        completion();
        self.canShowSwitchGuide = NO;
        [[NSNotificationCenter defaultCenter] removeObserver:self];
    }];
    guideAlertBlock();
    [[JCBluetoothManager sharedInstance] cancelScan:nil];
    // 是否正在弹出，弹出需关闭
    if ([[XYTool getCurrentVC] isMemberOfClass:[ContextMenuController class]]) {
        [[XYTool getCurrentVC] dismissViewControllerAnimated:YES completion:nil];
    }
    [JCBluetoothManager sharedInstance].isFirstBluetoothAlertSearching = NO;
}

- (void)showContentAlert{
    NSLog(@"蓝牙- Wi-Fi：展示蓝牙搜索%@",self);
    [super showContentAlertWithCompletion:^{
        NSLog(@"蓝牙- Wi-Fi：重置搜索状态%@",self);
        [self redayToStartScan];
    }];
    toSwithConnectType = NO;
    bluetoothStateCount = 0;
    [self showBluetoothGuideTip];
}


//展示系统蓝牙异常提示 当蓝牙状态未知时 递归获取
- (void)showBluetoothGuideTip{
    if(bluetoothStateCount >= 20) return;
    if([[JCBluetoothManager sharedInstance] getBluetoothState] == 0 && !JC_IS_CONNECTED_PRINTER){
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self showBluetoothGuideTip];
            bluetoothStateCount ++;
        });
    }else{
        if([[JCBluetoothManager sharedInstance] getBluetoothState] == 4 && !JC_IS_CONNECTED_PRINTER){
            [self showDeviceConnectStatus];
        }else if([[JCBluetoothManager sharedInstance] getBluetoothState] == 3 && !JC_IS_CONNECTED_PRINTER){
            [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app01300",@"请在“设置-隐私-蓝牙” ， 允许精臣云打印访问你的蓝牙") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00951",@"去设置") cancelBlock:nil sureBlock:^{
                NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
                if ([[UIApplication sharedApplication]canOpenURL:url]) {
                    [[UIApplication sharedApplication]openURL:url options:@{} completionHandler:^(BOOL success) {}];
                }
            } alertType:3];
        }
    }
}

- (void)redayToStartScan{
    [[JCBluetoothManager sharedInstance] didReadBluetoothScanStateHandler:self.ScanStateBlock];
    [[JCBluetoothManager sharedInstance] didReadBluetoothConnectStateHandler:self.connectBlock];
    [[JCBluetoothManager sharedInstance] setDiscoveredDevices:self.discoveredDevices];
    if(JC_IS_CONNECTED_PRINTER){
        JCBluetoothModel *connectedModel = [JCBluetoothManager sharedInstance].connectedModel;
        [self setCurrentState:JCPRINTER_DEVICE_CONNECTED model:connectedModel];
    }else{
      // 搜索之前查看当前的蓝牙状态，当处于未授权的时候不进行搜索，等待授权监听的返回后自动搜索，防止重复搜索导致的空白问题
        CBManagerState state = [[JCBluetoothManager sharedInstance] getBluetoothState];
        if ([JCBluetoothManager sharedInstance].isFirstBluetoothAlertSearching == NO) {
            [[JCBluetoothManager sharedInstance] startScan:@"" searchType:SEARCH_ALL];
        }
        NSLog(@"蓝牙- Wi-Fi：准备设置搜索页状态%@",self);
        if([[JCBluetoothManager sharedInstance] getDeviceBluetoothState]){
            NSLog(@"蓝牙- Wi-Fi：开始设置搜索页状态%@",self);
            [self setCurrentState:JCPRINTER_DEVICE_SEARCHING model:nil];
        }
    }
}

- (void)operateEvent:(UIButton *)sender{
    JCBluetoothManager *connectManager = [JCBluetoothManager sharedInstance];
    if(sender.tag == 20007){
        JC_TrackWithparms(@"click",@"127_344",@{});
        [[JCBluetoothManager sharedInstance] closeConnectedByHand:YES deviceType:connectManager.deviceType];
    }else if(sender.tag == 50001){
        [self setCurrentState:JCPRINTER_DEVICE_SEARCHING model:nil];
        [[self listAdaptor] cleanDatas];
        [self refreshPrinterList];
        [connectManager startScan:@"" searchType:SEARCH_ALL];
        JC_TrackWithparms(@"click",@"010_110_124",@{});
    }else if(sender.tag == 30003 || sender.tag == 30004){
        if(![connectManager isWiFiEnabled] && [connectManager getBluetoothState] == 4){
            [self showDeviceConnectStatus];
            [[self listAdaptor] cleanDatas];
            [self refreshPrinterList];
            return;
        }else{
            [[self listAdaptor] cleanDatas];
            [self refreshPrinterList];
        }
        [[JCBluetoothManager sharedInstance] startScan:@"" searchType:SEARCH_ALL];
    }else if(sender.tag == 20009){
        // 如果未返回所有的打印机信息不允许切换
      if (IsNotEmptyDictionary(connectManager.deviceDict)) {
        NSLog(@"蓝牙- Wi-Fi：切换连接方式");
        JC_TrackWithparms(@"click",@"127_343",@{});
        if(JC_CURRENT_CONNECTTYPE == JCConnectStateTypeBlueTooth){
          [self switchBluetoothToWifi];
        }else if(JC_CURRENT_CONNECTTYPE == JCConnectStateTypeWifi){
          [self switchWifiToBluetooth];
        }
      }
    }else if(sender.tag == 30005){
        NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
        if ([[UIApplication sharedApplication] canOpenURL:url]) {
            [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
        }
    }else if(sender.tag == 20010){
        if (NETWORK_STATE_ERROR) {
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        // 修改别名按钮点击
        JCBluetoothModel *connectedModel = [JCBluetoothManager sharedInstance].connectedModel;
        JCNiimbotPrinterModel *connectedNetyModel = [JCBluetoothManager sharedInstance].connectedNetyModel;
        if(connectedModel){
            JCTemplateSaveAsView *saveAsView = [JCTemplateSaveAsView xy_xib];
            // 设置标题为修改设备别名
            saveAsView.titleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app100002002", @"设备名称");
            // 设置当前别名作为默认文本
            NSString *currentAlias = STR_IS_NIL(connectedModel.alias) ? connectedModel.name : connectedModel.alias;
            saveAsView.saveTextField.text = currentAlias;
            // 设置文本长度限制
            saveAsView.length = 50; // 设备别名长度限制为50
            
            // 选中所有文本
            UITextPosition *start = saveAsView.saveTextField.beginningOfDocument;
            UITextPosition *end = saveAsView.saveTextField.endOfDocument;
            saveAsView.saveTextField.selectedTextRange = [saveAsView.saveTextField textRangeFromPosition:start toPosition:end];
            
            saveAsView.clickViewBtnBlock1 = ^(UIButton *sender, NSString *text) {
                if (sender.tag == 1) { // 确定按钮
                    if (NETWORK_STATE_ERROR) {
                      dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.35 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
                      });
                        return;
                    }
                    if(STR_IS_NIL(text) || text.length == 0){
                        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100002005", @"请填写设备名称")];
                        return;
                    }
                    
                    // 构建参数
                    NSDictionary *params = @{
                        @"machineNo": connectedModel.name ?: @"",
                        @"macNo": connectedNetyModel.macAddress ?: @"",
                        @"alias": text
                    };
                    
                    // 直接调用Flutter方法修改别名
                    [[JCAppMethodChannel shareInstance].methodChannel invokeMethod:@"modifyMachineAlias"
                                                                        arguments:params
                                                                           result:^(id _Nullable result) {
                        
                        // 修改完成，更新设备模型的alias属性
                        if ([result isKindOfClass:[NSString class]] && !STR_IS_NIL(result)) {
                          // 显示失败提示
                          dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.35 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(result, @"修改失败，请稍后再试")];
                          });
                        } else if (result == nil) {
                          // 更新成功
                          connectedModel.alias = text;
                          connectedNetyModel.alias = text;
                          
                          // 更新UI显示
                          UILabel *connectLabel = [self.connectView viewWithTag:20005];
                          connectLabel.text = text;
                          
                          // 发送机器别名修改成功通知
                          NSDictionary *userInfo = @{
                              @"machineAlias": text
                          };
                          [[NSNotificationCenter defaultCenter] postNotificationName:JCMachineAliasChangeNotification 
                                                                              object:nil 
                                                                            userInfo:userInfo];
                        }
                    }];
                }
            };
        }
    }
}

- (void)switchWifiToBluetooth{
    self.isSwitchMode = YES;
    JCBluetoothManager *connectManager = [JCBluetoothManager sharedInstance];
    JCBluetoothModel *model = connectManager.connectedModel;
    JCBluetoothModel *cueerentmodel = [[JCBluetoothModel alloc] init];
    cueerentmodel.state = JCConnectStateTypeBlueTooth;
    cueerentmodel.name = model.name;
    cueerentmodel.peripheral = model.peripheral;
    cueerentmodel.advertisementData = model.advertisementData;
    cueerentmodel.RSSI = model.RSSI;
    JCSelectDeviceModel *selectModel = [JCDeviceSeriesHelp selectModelWithDeviceName:model.name appId:[JCBluetoothManager sharedInstance].limitAppId];
    JCPrinterModel *printerModel = [JCDeviceSeriesHelp printerModelWithModel:model];
    if(printerModel == nil || selectModel == nil){ //没有对应的系列与打印模型不添加
        return;
     }
    cueerentmodel.printer_type = [printerModel.interface_type mutableCopy];
    cueerentmodel.printerModel = selectModel;
    cueerentmodel.printer = printerModel;
    self.switchBluetoothModel = cueerentmodel;
    [connectManager switchCloseConnectedWith:^{
      if(![connectManager getDeviceBluetoothState]){
          self.isSwitchMode = NO;
          [self setCurrentState:JCPRINTER_DEVICE_STOP_SEARCH model:cueerentmodel];
          [self showPrinterOperateErrorTipView:2];
          JCBluetoothSwitchGuideAlert *alert = [[JCBluetoothSwitchGuideAlert alloc] initWithType:JCWiFiOpen];
          [alert showContentAlert];
          guideAlertBlock = ^(){
              [alert hiddenContentAlert];
          };
          return;
      }
      [self setCurrentState:JCPRINTER_DEVICE_CONNECTTING model:cueerentmodel];
      self.printListView.hidden = YES;
      [connectManager connectWith:cueerentmodel connectType:CLOUDPRINTER_BLUETOOTH complate:^(NSNumber *success) {
          self.printListView.hidden = NO;
          if(success.boolValue){
              toSwithConnectType = YES;
              [self setCurrentState:JCPRINTER_DEVICE_CONNECTED model:cueerentmodel];
          }else{
              [connectManager cancelScan:nil];
              [self showPrinterOperateErrorTipView:2];
              [self setCurrentState:JCPRINTER_DEVICE_STOP_SEARCH model:cueerentmodel];
          }
          self.isSwitchMode = NO;
      }];
    }];
}

- (void)switchBluetoothToWifi{

    self.isSwitchMode = YES;
    NSString *printerName = JC_CURRENT_CONNECTED_PRINTER;

    JCBluetoothManager *connectManager = [JCBluetoothManager sharedInstance];
    //该loadding为切换连接的过程
    __block JCBluetoothModel  *connectedModel = [connectManager connectedModel];
    JCBluetoothModel *cueerentmodel = [[JCBluetoothModel alloc] init];
    cueerentmodel.state = JCConnectStateTypeBlueTooth;
    cueerentmodel.name = connectedModel.name;
    cueerentmodel.peripheral = connectedModel.peripheral;
    cueerentmodel.advertisementData = connectedModel.advertisementData;
    cueerentmodel.RSSI = connectedModel.RSSI;
    JCSelectDeviceModel *selectModel = [JCDeviceSeriesHelp selectModelWithDeviceName:connectedModel.name appId:[JCBluetoothManager sharedInstance].limitAppId];
    JCPrinterModel *printerModel = [JCDeviceSeriesHelp printerModelWithModel:connectedModel];
    if(printerModel == nil || selectModel == nil){ //没有对应的系列与打印模型不添加
        return;
     }
    cueerentmodel.printer_type = [printerModel.interface_type mutableCopy];
    cueerentmodel.printerModel = selectModel;
    cueerentmodel.printer = printerModel;
    self.switchBluetoothModel = cueerentmodel;
    [connectManager switchCloseConnectedWith:^{
      XYNormalBlock wifiSwithBlock = ^(){
        [connectManager cancelScan:nil];
          [self setCurrentState:JCPRINTER_DEVICE_STOP_SEARCH model:connectedModel];
          [self showPrinterOperateErrorTipView:2];
          JCBluetoothSwitchGuideAlert *alert = [[JCBluetoothSwitchGuideAlert alloc] initWithType:JCBluetoothOpen];
          [alert showContentAlert];
          guideAlertBlock = ^(){
              [alert hiddenContentAlert];
          };
      };
      if(![connectManager isWiFiEnabled] || !jc_is_connected_wifi){
          self.isSwitchMode = NO;
          wifiSwithBlock();
          return;
      }
      [self setCurrentState:JCPRINTER_DEVICE_CONNECTTING model:connectedModel];
      if(!STR_IS_NIL(connectedModel.host)){
          connectedModel.state = JCConnectStateTypeWifi;
          self.printListView.hidden = YES;
          [connectManager connectWith:connectedModel connectType:CLOUDPRINTER_WIFI complate:^(NSNumber *success) {
              self.printListView.hidden = NO;
              if(success.boolValue){
                  toSwithConnectType = YES;
                  [self setCurrentState:JCPRINTER_DEVICE_CONNECTED model:connectedModel];
              }else{
                [connectManager cancelScan:nil];
                  connectedModel.state = JCConnectStateTypeWifi;
                  [self showPrinterOperateErrorTipView:2];
                  [self setCurrentState:JCPRINTER_DEVICE_STOP_SEARCH model:connectedModel];
              }
              self.isSwitchMode = NO;
          }];
      }else{
          JCBluetoothModel *searchedWifiPrinter = [connectManager getWifiSeachedPrinterModelWith:printerName];
          if(searchedWifiPrinter != nil){
              connectedModel.state = JCConnectStateTypeWifi;
              connectedModel.host = searchedWifiPrinter.host;
              connectedModel.availableClient = searchedWifiPrinter.availableClient;
              self.printListView.hidden = YES;
              [connectManager connectWith:connectedModel connectType:CLOUDPRINTER_WIFI complate:^(NSNumber *success) {
                  self.printListView.hidden = NO;
                  if(success.boolValue){
                      toSwithConnectType = YES;
                      [self setCurrentState:JCPRINTER_DEVICE_CONNECTED model:connectedModel];
                  }else{
                    [connectManager cancelScan:nil];
                      connectedModel.state = JCConnectStateTypeWifi;
                      [self showPrinterOperateErrorTipView:2];
                      [self setCurrentState:JCPRINTER_DEVICE_STOP_SEARCH model:connectedModel];
                  }
                  self.isSwitchMode = NO;
              }];
              return;
          }
          [connectManager startScan:@"" searchType:SEARCH_ALL];
          [self setCurrentState:JCPRINTER_DEVICE_CONNECTTING model:connectedModel];
          __block BOOL switchComplete = NO;
          self.printListView.hidden = YES;
          dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
              JCBluetoothModel *model = [connectManager getWifiSeachedPrinterModelWith:printerName];
              if(model == nil){
                  self.printListView.hidden = NO;
                  [connectManager startScan:@"" searchType:SEARCH_ALL];
                  JCWifiConfigAlert *wifiConfigAlert = [[JCWifiConfigAlert alloc] initWithPrinterName:printerName];
                  XYWeakSelf
                  wifiConfigAlert.closeBlock = ^{
                      // 关闭重置状态
                      weakSelf.isSwitchMode = NO;
                      [connectManager startScan:@"" searchType:SEARCH_ALL];
                  };
                  [wifiConfigAlert showContentAlert];
                  return;
              }else{
                  if([JCBluetoothManager isMaxDeviceWith:model]){
                      connectedModel.state = JCConnectStateTypeBlueTooth;
                      [self setCurrentState:JCPRINTER_DEVICE_CONNECTED model:connectedModel];
                      [JCBluetoothManager showPrinterLinkOverflowHint];
                      self.isSwitchMode = NO;
                      self.printListView.hidden = NO;
                      return;
                  }
                  model.state = JCConnectStateTypeWifi;
                  [connectManager connectWith:model connectType:CLOUDPRINTER_WIFI complate:^(NSNumber *success) {
                      self.printListView.hidden = NO;
                      if(!switchComplete){
                          if(success.boolValue){
                              toSwithConnectType = YES;
                              [self setCurrentState:JCPRINTER_DEVICE_CONNECTED model:connectedModel];
                          }else{
                              [connectManager cancelScan:nil];
                              model.state = JCConnectStateTypeWifi;
                              [self showPrinterOperateErrorTipView:2];
                              [self setCurrentState:JCPRINTER_DEVICE_STOP_SEARCH model:connectedModel];
                          }
                      }
                      self.isSwitchMode = NO;
                      switchComplete = YES;
                  }];
              }
          });
      }
    }];
}

- (void)setIsSwitchMode:(BOOL)isSwitchMode{
    _isSwitchMode = isSwitchMode;
    if(!_isSwitchMode){
      self.switchBluetoothModel = nil;
    }
    [[JCBluetoothManager sharedInstance] setIsSwitchMode:isSwitchMode];
}


- (void)connectWifiWith:(NSString *)printerName printIpAdd:(NSString *)ipAdd{
    [JCAPI openPrinterHost:ipAdd completion:^(BOOL isSuccess) {
        if(isSuccess)[JCBluetoothManager setWifiPrinterConnectRraceCache];
    }];
}

- (void)printerStatusNotification:(NSNotification *)noti {
    NSString *state = noti.object;
    BOOL isBlueOpen = ([state isEqualToString:@"1"] ? YES:NO);
    self.isSwitchMode = NO;
}


// 1 搜索不到  2 连接失败
- (void)showPrinterOperateErrorTipView:(NSInteger)type{

    if(self.isSwitchMode)return;

    self.noPrinterView.hidden = NO;
    [self showAlertTitleWith: @""];
    UILabel *titleLabel = [self.noPrinterView viewWithTag:50002];
    UIButton *reScanBtn = [self.noPrinterView viewWithTag:50001];
    titleLabel.text = type == 1?XY_LANGUAGE_TITLE_NAMED(@"app100000087", @"搜不到您的打印机"):XY_LANGUAGE_TITLE_NAMED(@"app100000540", @"打印机连接失败");
    UIScrollView *contentScrollView = [self.noPrinterView viewWithTag:50003];
    UITextView *contentTextView = [contentScrollView viewWithTag:500031];
    NSString *content = type == 1?XY_LANGUAGE_TITLE_NAMED(@"app100000571", @"以上都无效，请尝试重新启动APP或蓝牙，如需更多帮助请查看"):XY_LANGUAGE_TITLE_NAMED(@"app100000571", @"以上都无效，请尝试重新启动打印机或APP与蓝牙，如需更多帮助请查看");
    content = [NSString stringWithFormat:@"%@ %@",content,XY_LANGUAGE_TITLE_NAMED(@"app100000089", @"帮助指南>")];
    content = [content stringByReplacingOccurrencesOfString:@"\\n" withString:@" \r\n"];
    contentTextView.attributedText = [self getContentLabelAttributedText:content font:MY_FONT_Medium(16) textColor:HEX_RGB(0x262626) title2:XY_LANGUAGE_TITLE_NAMED(@"app100000089", @"帮助指南>") textColor2:XY_HEX_RGB(0x5C88C1)];
    contentTextView.linkTextAttributes = @{NSForegroundColorAttributeName : XY_HEX_RGB(0x5C88C1)};
    [contentTextView sizeToFit];
    [contentTextView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(contentTextView.height);
    }];
    [contentScrollView layoutIfNeeded];
    contentScrollView.contentSize = CGSizeMake(SCREEN_WIDTH, reScanBtn.bottom + 20);
}


#pragma mark - adaptor method
- (void)initAdaptor{
    XYWeakSelf
    if(self.bluetoothTable){
        self.bluetoothTable.groupShadowDelegate = deviceListAdaptor;
        self.bluetoothTable.groupShadowDataSource =deviceListAdaptor;
    }
    deviceListAdaptor.connectEventBlock = ^{
      weakSelf.switchBluetoothModel = nil;
    };
    [self listAdaptor].printListView = self.printListView;
}

- (JCPrinterConnectListAdaptor *)listAdaptor{
    return deviceListAdaptor;
}

//刷新设备列表
- (void)refreshPrinterList{
    [self.bluetoothTable reloadData];
}

//清空设备列表
- (void)cleanPrinterList{
    [[self listAdaptor] cleanDatas];
    [self refreshPrinterList];
}


//设置当前状态
- (void)setCCurentState:(int)state{
    _currentState = state;
    [self listAdaptor].currentState = state;
}

#pragma mark - category property interface

- (UIView *)headerViewC{
    return  _headerView;
}
- (void)setHeaderViewC:(UIView*)view{
    _headerView = view;
}
- (UIView *)connectViewC{
    return _connectView;
}
- (void)setConnectViewC:(UIView*)view{
    _connectView = view;
}
- (UIView *)searchingViewC{
    return _searchingView;
}

- (void)setSearchingViewC:(UIView *)searchingView{
    _searchingView = searchingView;
}

- (UIView *)stopSearchViewC{
    return _stopSearchView;
}

- (void)setStopSearchViewC:(UIView*)view{
    _stopSearchView = view;
}

- (UIView *)noPrinterViewC{
    return _noPrinterView;
}

- (void)setNoPrinterViewC:(UIView*)view{
    _noPrinterView = view;
}

- (UIView *)deviceInfoViewC {
    return _deviceInfoView;
}

- (void)setDeviceInfoViewC:(UIView *)view {
    _deviceInfoView = view;
}

@end
