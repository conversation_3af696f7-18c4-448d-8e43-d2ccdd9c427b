//
//  JCPrintDevice.m
//  Runner
//
//  Created by EG on 2018/10/22.
//  Copyright © 2018年 JingchenSoft. All rights reserved.
//

#import "JCPrintDevice.h"
#import "JCAPI.h"
#import "JCPrinterProperty.h"
#import "JCPrinterSettingInfo.h"

NSString *const PRINT_DEVICE_NAME = @"printDeviceName";
NSString *const PRINT_DEVICE_MODEL = @"printDeviceModel";
NSString *const PRINT_DEVICE_HARDWARE_TYPE = @"printDeviceHardwareType";
NSString *const PRINT_DEVICE_FIRMWARE_TYPE = @"printDeviceFirmwareType";
NSString *const PRINT_DEVICE_DESITY = @"printDeviceDensity";
NSString *const PRINT_DEVICE_SPEED = @"printDeviceSpeed";
NSString *const PRINT_DEVICE_AUTO_SHUTDOWN_DURATION = @"printDeviceAutoShutdownDuration";
NSString *const PRINT_DEVICE_PAPER_TYPE = @"printDevicePaperType";
NSString *const PRINT_DEVICE_HARDWARE_FLAGS = @"hardwareFlags";
NSString *const PRINT_DEVICE_RFID_COUNT = @"RFID_Count";
NSString *const PRINT_DEVICE_RFID_SUCCESS_COUNT = @"RFID_Success_Count";
NSString *const PRINT_MODE = @"Print_Mode";
NSString *const PRINT_DEVICE_MAC_ADDRESS = @"device_mac_address";
NSString *const PRINT_DEVICE_REGION = @"device_region";
NSString *const PRINT_DEVICE_WIFI_CONFIG = @"device_last_wifi_config";
NSString *const PRINT_DEVICE_POWER_VOICE = @"device_power_voice";
NSString *const PRINT_DEVICE_BLUETOOTH_VOICE = @"device_bluetooth_voice";
NSString *const PRINT_DEVICE_SECRET = @"printDeviceSecret";

NSString *const PRINT_DEVICE_PROPERTY = @"PRINT_DEVICE_PROPERTY";
NSString *const PRINT_DEVICE_KEY_FUNCTION = @"PRINT_DEVICE_KEY_FUNCTION";
NSString *const PRINT_QUALITY_SUPPORT = @"device_quality_support";
NSString *const PRINT_QUALITY_RESULT = @"device_quality_result";

@interface JCPrintDevice()

@property(nonatomic,copy) NSString *printDeviceName;

@property(nonatomic,copy) NSString *printDeviceModel;

@property(nonatomic,copy) NSString *printDeviceHardwareType;

@property(nonatomic,copy) NSString *lastWifiConfigName;

@property(nonatomic,copy) NSString *printDeviceFirmwareType;

@property(nonatomic,strong) NSString *printDeviceDensity;

@property(nonatomic,strong) NSString *printDeviceSpeed;

@property(nonatomic,strong) NSString *printDeviceAutoShutdownDuration;

@property(nonatomic,strong) NSString *printDevicePaperType;

@property(nonatomic,strong) NSString *rfid_Success_Count;

@property(nonatomic,strong) NSString *rfid_Count;

@property(nonatomic,strong) NSString *hardwareFlags;

@property(nonatomic,strong) NSString *macAddress;

@property(nonatomic,copy) NSString *printRegion;

@property(nonatomic,copy) NSString *printPowerVoiceState;

@property(nonatomic,copy) NSString *printBluetoothVoiceState;

/// 硬件防伪码
@property(nonatomic,copy) NSString *hardwareSecret;

/// 打印机属性，目前只包含开关机键位属性
@property (nonatomic, strong) NSArray *printerKeyProperties;

/// 开关机键键位对应的function
@property (nonatomic, strong) NSArray *keyFunction;

/// 是否正在从SDK获取信息
@property (nonatomic, assign) BOOL isGetingInfoFromSDK;

/// 存储调用当前正在调用getDeviceInfoDict的Block回调组
@property (nonatomic, strong, nonnull) NSMutableArray<XYBlock> *blockArray;


/// 支持的打印模式
@property(nonatomic,strong) NSDictionary *qualitySupport;

/// 当前的打印模式 0 - 高质量，1 - 高速度
@property(nonatomic,copy) NSString *qualityResult;

@end

@implementation JCPrintDevice

implementationSingleton(Device)

- (instancetype)init {
    if (self = [super init]) {
        self.printDeviceName = @"";
        self.printDeviceModel = @"";
        self.printDeviceHardwareType = @"";
        self.lastWifiConfigName = @"";
        self.printDeviceFirmwareType = @"";
        self.printDeviceDensity = @"0";
        self.printDeviceSpeed = @"0";
        self.printDeviceAutoShutdownDuration = @"0";
        self.printDevicePaperType = @"0";
        self.printPowerVoiceState= @"";
        self.printBluetoothVoiceState = @"";
        self.qualitySupport = @{};
        self.qualityResult = @"";
        if (!self.blockArray) {
            self.blockArray = [NSMutableArray array];
        }
    }
    return self;
}

- (void)getInfoFromSDK:(void(^)(void))calculateBlock {
    XYWeakSelf
    self.printDeviceName = UN_NIL([JCBluetoothManager sharedInstance].connectedModel.name);
    self.printDeviceModel = @"";
    JCBluetoothManager *manager = [JCBluetoothManager sharedInstance];
    NSString *printType = [[JCBluetoothManager sharedInstance] printerTypeFromPrinterName:JC_CURRENT_CONNECTED_PRINTER isNeedShowChildType:YES];
    JCPrinterModel *printModel = [[XYCenter sharedInstance] getPrinterModelWithMochineId:nil orMachineName:printType];
    XYBlock1 deviceInfoBlock = ^(JCPrinterSettingInfo *printerSettingInfo, NSDictionary *rawResponse){
      weakSelf.printDeviceName = UN_NIL([JCBluetoothManager sharedInstance].connectedModel.name);
      NSString *printType = @"";
      NSArray *arr1 = [weakSelf.printDeviceName componentsSeparatedByString:@"-"];
      if(arr1.count > 1){
          printType = [arr1 safeObjectAtIndex:0];
      }else{
          NSArray *arr2 = [weakSelf.printDeviceName componentsSeparatedByString:@"_"];
          printType = [arr2 safeObjectAtIndex:0];
      }
      weakSelf.printDeviceModel = printType;
      // 获取名称默认的SN号
      NSString *bluetoothNumber = @"";
      NSString *name = UN_NIL([JCBluetoothManager sharedInstance].connectedModel.name);
      if(!STR_IS_NIL(name)){
          NSArray *arr1 = [name componentsSeparatedByString:@"-"];
          if(arr1.count > 1){
              bluetoothNumber = arr1[arr1.count -1];
          }
          NSArray *arr2 = [name componentsSeparatedByString:@"_"];
          if(arr2.count > 1){
              bluetoothNumber = arr2[arr2.count -1];
          }
      }
      // SN号
      NSString *sn = STR_IS_NIL([JCBluetoothManager sharedInstance].connectedNetyModel.sn) ? bluetoothNumber : [JCBluetoothManager sharedInstance].connectedNetyModel.sn;
      manager.connectedModel.sn = UN_NIL(sn);
      // 硬件版本
      NSString *hardware = UN_NIL([JCBluetoothManager sharedInstance].connectedNetyModel.hardwareVersion);
      weakSelf.printDeviceHardwareType = UN_NIL(hardware);
      manager.connectedModel.printDeviceHardwareType = UN_NIL(hardware);
      // 软件版本
      NSString *firmware = UN_NIL([JCBluetoothManager sharedInstance].connectedNetyModel.firmwareVersion);
      weakSelf.printDeviceFirmwareType = UN_NIL(firmware);
      manager.connectedModel.printDeviceFirmwareType = UN_NIL(firmware);
      // 打印浓度
      weakSelf.printDeviceDensity = UN_NIL([JCBluetoothManager sharedInstance].connectedNetyModel.printDensity);
      manager.connectedModel.printDeviceDensity = UN_NIL([JCBluetoothManager sharedInstance].connectedNetyModel.printDensity);;
      // 打印速度
      weakSelf.printDeviceSpeed = UN_NIL([JCBluetoothManager sharedInstance].connectedNetyModel.printSpeed);
      manager.connectedModel.printDeviceSpeed = UN_NIL([JCBluetoothManager sharedInstance].connectedNetyModel.printSpeed);
      // 自动关机时间
      weakSelf.printDeviceAutoShutdownDuration = UN_NIL(printerSettingInfo.autoShutDownTimeLevel);
      manager.connectedModel.printDeviceAutoShutdownDuration = UN_NIL(printerSettingInfo.autoShutDownTimeLevel);
      // 纸张类型
      weakSelf.printDevicePaperType = UN_NIL([JCBluetoothManager sharedInstance].connectedNetyModel.paperType);
      manager.connectedModel.printDevicePaperType = UN_NIL([JCBluetoothManager sharedInstance].connectedNetyModel.paperType);
      // 区域码
      weakSelf.printRegion = UN_NIL([JCBluetoothManager sharedInstance].connectedNetyModel.areaCode);
      // 开关机声音
      weakSelf.printPowerVoiceState = UN_NIL(printerSettingInfo.deviceVoice);
      // 蓝牙连接声音
      weakSelf.printBluetoothVoiceState = UN_NIL(printerSettingInfo.bluetoothVoice);
      // 防伪密钥
      weakSelf.hardwareSecret = UN_NIL([JCBluetoothManager sharedInstance].connectedNetyModel.antiCounterfeitKey);
      // mac地址
      weakSelf.macAddress = UN_NIL([JCBluetoothManager sharedInstance].connectedNetyModel.macAddress);
      // 开关机键位支持列表
      weakSelf.printerKeyProperties = rawResponse[@"printerKeyProperties"];
      // 开关机键键位对应的function
      weakSelf.keyFunction = rawResponse[@"printerKeyFunctions"];
      // 获取打印机读取成功次数信息
      if (printerSettingInfo.rfidSuccessTimesResult == nil) {
          weakSelf.rfid_Count = @"";
          weakSelf.rfid_Success_Count = @"";
      } else {
          weakSelf.rfid_Count = UN_NIL(printerSettingInfo.rfidSuccessTimesResult.totalNumber);
          weakSelf.rfid_Success_Count = UN_NIL(printerSettingInfo.rfidSuccessTimesResult.successNumber);
      }
      // WIFI名称
      weakSelf.lastWifiConfigName = printerSettingInfo.wifiName;
      // 返回存储dic
      calculateBlock();
      
      // 设置电量
      if (STR_IS_NIL(printerSettingInfo.electricity)) {
        weakSelf.batteryPower = @"";
        JCNCPost2p(PrinterBatteryPowerNotification, @"0");
      } else {
        NSString *printInfo = printerSettingInfo.electricity;
        NSInteger intPower = [printInfo integerValue];
        intPower = intPower >= 100?100:intPower;
        NSInteger powerLever = intPower/10;
        if(intPower%10 >= 5 || powerLever == 0){
            powerLever++;
        }
        weakSelf.batteryPower = StringFromInt(powerLever*10);
        JCNCPost2p(PrinterBatteryPowerNotification, weakSelf.batteryPower);
        JCNCPost2p(PrinterBatteryPowerNotification, @"0");
      }
    };
      if(printModel != nil){
        // 调用打印机信息
        CFAbsoluteTime startTime = CFAbsoluteTimeGetCurrent();
        [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"getPackedPrinterInfo"
                                                               arguments:nil result:^(NSString * _Nullable response) {
          if (response) {
            CFAbsoluteTime endTime = CFAbsoluteTimeGetCurrent();
            NSLog(@"getPackedPrinterInfo 耗时: %.6f 秒", endTime - startTime);
            NSLog(@"获取打印机信息成功----%@", response);
            NSError *error;
            NSDictionary *rawResponse = [NSJSONSerialization JSONObjectWithData:[response dataUsingEncoding:NSUTF8StringEncoding]
                                                                        options:kNilOptions
                                                                          error:nil];
            JCPrinterSettingInfo *printerSettingInfo = [[JCPrinterSettingInfo alloc] initWithString:response error:&error];
            // 设置当前时间
            void(^setPrinterTime)(void(^backBlock)(void)) = ^void(void(^backBlock)(void)) {
              // 设置当前时间
              NSCalendar *calendar = [NSCalendar currentCalendar];
              
              // 将日期拆分为年、月、日、时、分和秒
              NSDateComponents *components = [calendar components:NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay | NSCalendarUnitHour | NSCalendarUnitMinute | NSCalendarUnitSecond fromDate:[NSDate date]];
              
              NSDictionary *time = @{
                @"year": [NSNumber numberWithInteger:components.year],
                @"month": [NSNumber numberWithInteger:components.month],
                @"day": [NSNumber numberWithInteger:components.day],
                @"hour": [NSNumber numberWithInteger:components.hour],
                @"minute": [NSNumber numberWithInteger:components.minute],
                @"second": [NSNumber numberWithInteger:components.second],
              };
              
              // 调用打印机信息
              CFAbsoluteTime startTime1 = CFAbsoluteTimeGetCurrent();
              // 设置当前时间
              [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"setPrinterTime"
                                                                     arguments:time result:^(NSString * _Nullable response) {
                CFAbsoluteTime endTime1 = CFAbsoluteTimeGetCurrent();
                NSLog(@"setPrinterTime 耗时: %.6f 秒", endTime1 - startTime1);
                backBlock();
              }];
            };
            // 存在开关机键位设置才设置时间
            if (IsNotEmptyArray(printerSettingInfo.printerKeyFunctions)) {
              setPrinterTime(^void() {
                deviceInfoBlock(printerSettingInfo, rawResponse);
              });
            } else {
              deviceInfoBlock(printerSettingInfo, rawResponse);
            }
          }}];
      } else {
        calculateBlock();
     }
}

- (void)updateFirm:(XYBlock)block{
    
}

-(void)getBatteryPower
{
  XYWeakSelf
//    [JCAPI getPrintInfo:10 sucess:^(NSDictionary *printDicInfo) {
//        if([@"0" isEqualToString:printDicInfo[@"statusCode"]]){
//            NSString *printInfo = printDicInfo[@"result"];
//            NSInteger intPower = [printInfo integerValue];
//            intPower = intPower >= 100?100:intPower;
//            NSInteger powerLever = intPower/10;
//            if(intPower%10 >= 5 || powerLever == 0){
//                powerLever++;
//            }
//            weakSelf.batteryPower = StringFromInt(powerLever*10);
//            JCNCPost2p(PrinterBatteryPowerNotification, weakSelf.batteryPower);
//        }else{
//            weakSelf.batteryPower = @"";
//            JCNCPost2p(PrinterBatteryPowerNotification, @"0");
//        }
//    }];//新SDK联调注释
}

- (void)getDeviceInfoDict:(XYBlock)block{
    XYWeakSelf
    if (self.isGetingInfoFromSDK) {
        // 当前已经有获取信息Block，进行队列排队
        [self.blockArray addObject:[block copy]];
        return;
    }
    self.isGetingInfoFromSDK = YES;
    NSLog(@"开始获取机器硬件信息：%@",[XYTool getCurrentTimes]);
    [self getInfoFromSDK:^{
        NSMutableDictionary *dict = [NSMutableDictionary dictionary];
        if (weakSelf.printDeviceName) {
            [dict setObject:weakSelf.printDeviceName forKey:PRINT_DEVICE_NAME];
        }
        if (weakSelf.printDeviceModel) {
            [dict setObject:weakSelf.printDeviceModel forKey:PRINT_DEVICE_MODEL];
        }
        if (weakSelf.printDeviceHardwareType) {
            [dict setObject:weakSelf.printDeviceHardwareType forKey:PRINT_DEVICE_HARDWARE_TYPE];
        }
        if (weakSelf.printDeviceFirmwareType) {
            [dict setObject:weakSelf.printDeviceFirmwareType forKey:PRINT_DEVICE_FIRMWARE_TYPE];
        }
        if (weakSelf.printDeviceDensity) {
            [dict setObject:weakSelf.printDeviceDensity forKey:PRINT_DEVICE_DESITY];
        }
        if (weakSelf.printDeviceSpeed) {
            [dict setObject:weakSelf.printDeviceSpeed forKey:PRINT_DEVICE_SPEED];
        }
        if (weakSelf.printDeviceAutoShutdownDuration) {
            [dict setObject:weakSelf.printDeviceAutoShutdownDuration forKey:PRINT_DEVICE_AUTO_SHUTDOWN_DURATION];
        }
        if (weakSelf.printDevicePaperType) {
            [dict setObject:weakSelf.printDevicePaperType forKey:PRINT_DEVICE_PAPER_TYPE];
        }
        if (weakSelf.hardwareFlags) {
            [dict setObject:weakSelf.hardwareFlags forKey:PRINT_DEVICE_HARDWARE_FLAGS];
        }
        if (weakSelf.rfid_Count) {
            [dict setObject:weakSelf.rfid_Count forKey:PRINT_DEVICE_RFID_COUNT];
        }
        if (weakSelf.rfid_Success_Count) {
            [dict setObject:weakSelf.rfid_Success_Count forKey:PRINT_DEVICE_RFID_SUCCESS_COUNT];
        }
        if (weakSelf.macAddress) {
            [dict setObject:weakSelf.macAddress forKey:PRINT_DEVICE_MAC_ADDRESS];
        }
        if (weakSelf.printRegion) {
            [dict setObject:weakSelf.printRegion forKey:PRINT_DEVICE_REGION];
        }
        if (weakSelf.lastWifiConfigName) {
            [dict setObject:weakSelf.lastWifiConfigName forKey:PRINT_DEVICE_WIFI_CONFIG];
        }
        if (weakSelf.printPowerVoiceState) {
            [dict setObject:weakSelf.printPowerVoiceState forKey:PRINT_DEVICE_POWER_VOICE];
        }
        if (weakSelf.printBluetoothVoiceState) {
            [dict setObject:weakSelf.printBluetoothVoiceState forKey:PRINT_DEVICE_BLUETOOTH_VOICE];
        }
        if (weakSelf.hardwareSecret) {
            [dict setObject:weakSelf.hardwareSecret forKey:PRINT_DEVICE_SECRET];
        }
        if (weakSelf.printerKeyProperties) {
            [dict setObject:weakSelf.printerKeyProperties forKey:PRINT_DEVICE_PROPERTY];
        }
        if (weakSelf.keyFunction) {
            [dict setObject:weakSelf.keyFunction forKey:PRINT_DEVICE_KEY_FUNCTION];
        }
        if (weakSelf.qualitySupport) {
            [dict setObject:weakSelf.qualitySupport forKey:PRINT_QUALITY_SUPPORT];
        }
        if (weakSelf.qualityResult) {
            [dict setObject:weakSelf.qualityResult forKey:PRINT_QUALITY_RESULT];
        }
        NSLog(@"device description %@", [weakSelf description]);
        [[NSUserDefaults standardUserDefaults] setObject:dict forKey:@"printerInfo"];
        NSLog(@"获取机器硬件信息成功：信息：%@。时间%@",dict,[XYTool getCurrentTimes]);
        block([dict copy]);
        // 恢复状态，开启下一轮获取
        weakSelf.isGetingInfoFromSDK = NO;
        if (weakSelf.blockArray.count != 0) {
            XYBlock _block = weakSelf.blockArray.firstObject;
            // 开启新一轮获取
            [weakSelf.blockArray removeObjectAtIndex:0];
            [weakSelf getDeviceInfoDict:_block];
        }
    }];
}

- (void)updateFirmwareWithResource:(NSString *)path crcString:(NSString *)md5String firmVersion:(NSString *)firmVersion hardVersion:(NSString *)hardVersion result:(nonnull void (^)(NSString * _Nonnull))result {
//    [JCAPI updatePrinter:firmVersion crcValue:md5String path:path hversion:hardVersion printerType:[JCAPI printerTypeInfo] sucess:^(NSString *printInfo) {
//        result(printInfo);
//    }];//新SDK联调注释
}

- (NSString *)description {
    return [NSString stringWithFormat:@"\n设备名称：%@\n设备型号：%@\n硬件版本：%@\n固件名称：%@\n打印浓度：%@\n打印速度：%@\n自动关机时间：%@\n纸张类型：%@\n",
            self.printDeviceName,
            self.printDeviceModel,
            self.printDeviceHardwareType,
            self.printDeviceFirmwareType,
            self.printDeviceDensity,
            self.printDeviceSpeed,
            self.printDeviceAutoShutdownDuration,
            self.printDevicePaperType
            ];
}

/// 清除连接获取信息的队列
- (void)cleanGetInfoArray {
    self.blockArray = [NSMutableArray array];
    self.isGetingInfoFromSDK = NO;
}

@end
 
