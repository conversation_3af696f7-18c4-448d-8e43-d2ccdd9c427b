import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:dio/src/response.dart' as DioRep;
import 'package:flutter/material.dart';
import 'package:niimbot_local_storage/niimbot_sp.dart';
import 'package:text/utils/common_fun.dart';

import '../../application.dart';
import '../../tools/to_Native_Method_Channel.dart';
import 'app_config_manager.dart';

enum OutLineDayType {
  normal,
  alertCanClose,
  alertNotClose,
}

enum NiimbotNetworkType{
  outOfService,
  networkError,
  networkAvailable,
  connectTimeout
}

extension NetworkTypeParser on NiimbotNetworkType {
  static NiimbotNetworkType fromIndex(int index) {
    if (index < 0 || index >= NiimbotNetworkType.values.length) {
      return NiimbotNetworkType.networkAvailable;
    }
    return NiimbotNetworkType.values[index];
  }
}

typedef NetworkChangedCallback = void Function(bool isConnected);

class NiimbotNetworkManager {
  static String TAG_OFFLINE_SAVE_TIME = "offline_save_time";
  static String TAG_OFFLINE_TIPS_TIMES = "offline_tips_times";
  static final NiimbotNetworkManager _instance = NiimbotNetworkManager._internal();
  static NiimbotNetworkType _status = NiimbotNetworkType.networkAvailable;
  static bool _networkConnected = true;
  /// 网络波动回调
  static NetworkChangedCallback? callback = null;

  static bool isOfflineDialogShowing = false;

  factory NiimbotNetworkManager() {
    return _instance;
  }

  NiimbotNetworkManager._internal();

  void addNetworkConnectivityListener() {
    Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
      bool networkConnectedNew = result != ConnectivityResult.none && result != ConnectivityResult.bluetooth;
      notifyNetworkChange(networkConnectedNew);
    });
  }

  void sendNetworkStatus() {
    (Connectivity().customCheckConnectivity()).then((value) {
      bool networkConnectedNew = value != ConnectivityResult.none && value != ConnectivityResult.bluetooth;
      notifyNetworkChange(networkConnectedNew);
    });
    detectConnected();
  }

  void notifyNetworkChange(bool networkConnectedNew) {
    if (networkConnectedNew != _networkConnected) {
      _networkConnected = networkConnectedNew;
      callback?.call(_networkConnected);
      handleNetworkChanged(_networkConnected);
    }
  }

  void handleNetworkChanged(bool isConnected) {
    if (isConnected && _status != NiimbotNetworkType.outOfService) {
      clearOfflineTime();
      _status = NiimbotNetworkType.networkAvailable;
    } else {
      recordOfflineTime();
      if (_status != NiimbotNetworkType.outOfService) {
        _status = NiimbotNetworkType.networkError;
      }
    }
  }

  bool isConnected(){
    return _status == NiimbotNetworkType.networkAvailable && _networkConnected;
  }

  bool isOutOfService(){
    return _status == NiimbotNetworkType.outOfService;
    // return true;
  }

  Future<NiimbotNetworkType> getStatus() async{
    if (Platform.isAndroid) {
      bool hasNet = await ToNativeMethodChannel.getNativeNetworkState();
      if (hasNet && !NiimbotNetworkManager().isOutOfService()) {
        return NiimbotNetworkType.networkAvailable;
      } else {
        return NiimbotNetworkType.networkError;
      }
    } else {
      ConnectivityResult result = await Connectivity().checkConnectivity();
      if (result != ConnectivityResult.none
          && result != ConnectivityResult.bluetooth
          && !NiimbotNetworkManager().isOutOfService()) {
        return NiimbotNetworkType.networkAvailable;
      } else {
        return NiimbotNetworkType.networkError;
      }
    }
  }


  //原生通知flutter设置状态方法
  setStatus(NiimbotNetworkType niimbotNetworkType) {
    _status = niimbotNetworkType;
  }

  void notifyStatus(NiimbotNetworkType status) {
    if (status != _status) {
      _status = status;
      notifyNetworkChange(status == NiimbotNetworkType.networkAvailable);
      ToNativeMethodChannel.toSyncNetworkState(status == NiimbotNetworkType.outOfService);
    }
  }

  Dio detectDio = new Dio();
  void detectConnected() async {
    if (_status == NiimbotNetworkType.outOfService) {
      try {
        Options options = Options(
          method: 'HEAD',
          headers: {
            'Content-Type': 'application/json', // Add any necessary headers
          },
        );
        DioRep.Response serverResp = await detectDio.head(
          'https://${_getHost()}/api/system/app/configs',
          options: options,
        );
        debugPrint('HEAD request successful: ${serverResp.statusCode}');
        notifyStatus(NiimbotNetworkType.networkAvailable);
      } catch (e, s) {
        debugPrint('HEAD request error: ${e}');
      }
    }
  }

  String _getHost() {
    return Application.isProductEnv() ? 'print.niimbot.com' : 'print.jc-test.cn';
  }
}

extension OfflineAlertHelper on NiimbotNetworkManager {

  /// 记录离线时间
  void recordOfflineTime() {
    debugPrint("================记录离线时间，recordOfflineTime");
    final saveTime = NiimbotSp().getInt(NiimbotNetworkManager.TAG_OFFLINE_SAVE_TIME);
    if (saveTime == 0) {
      NiimbotSp().setInt(NiimbotNetworkManager.TAG_OFFLINE_SAVE_TIME, DateTime.now().millisecondsSinceEpoch);
    }
  }

  /// 清理上次离线时间
  void clearOfflineTime() {
    debugPrint("================清理上次离线时间，clearOfflineTime");
    NiimbotSp().remove(NiimbotNetworkManager.TAG_OFFLINE_SAVE_TIME);
    NiimbotSp().remove(NiimbotNetworkManager.TAG_OFFLINE_TIPS_TIMES);
  }

  /// 检查离线周期
  Future<Map> checkOfflinePeriod() async {
    final hasTipsTimes = NiimbotSp().getInt(NiimbotNetworkManager.TAG_OFFLINE_TIPS_TIMES);
    final nowTime = DateTime.now().millisecondsSinceEpoch;
    final saveTime = NiimbotSp().getInt(NiimbotNetworkManager.TAG_OFFLINE_SAVE_TIME);
    final type = await _getOutlineType(nowTime, saveTime, hasTipsTimes);
    final result = {};
    result['alertType'] = type.index;
    result['alertTitle'] = 'app01139';
    result['alertMessage'] = 'app100001845';
    debugPrint("================原生调用，offlineConfigMap，alertType： ${type.index}");
    return result;
  }

  /// 获取当前离线弹窗类型
  Future<OutLineDayType> _getOutlineType(final int nowTime, final int saveTime, final int hasTipsTimes) async {
    OutLineDayType type = OutLineDayType.normal;
    try {
      //离线弹窗弹出必要条件手机品牌为以下几个 小米 oppo vivo honor几个品牌,所以ios肯定不会弹出
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await DeviceInfoPlugin().androidInfo;
        String brand = androidInfo.brand.toLowerCase();
        debugPrint("get phone brand=$brand");
        if (brand != 'xiaomi' && brand != 'oppo' && brand != 'vivo' && brand != 'honor') {
          return OutLineDayType.normal;
        }
      }else{
        return OutLineDayType.normal;
      }
      final offlineConfig = await AppConfigManager().getAppConfigInfo();

      if (offlineConfig == null || offlineConfig.isEmpty || offlineConfig['offlinePeriodV2'] == null || saveTime == 0 || isConnected()) {
        debugPrint("配置加载失败，offlineConfigMap 为空，使用默认配置");
        return OutLineDayType.normal; // 返回默认值
      }

      final offlinePeriod = offlineConfig['offlinePeriodV2'] as int;
      final maxOfflineTimes = offlineConfig['maxOfflineTimesV2'] as int;
      final totalPeriodInMinutes = offlinePeriod*maxOfflineTimes;

      // 日期差值计算
      final nowDateTime = DateTime.fromMillisecondsSinceEpoch(nowTime);
      final saveDateTime = DateTime.fromMillisecondsSinceEpoch(saveTime);
      final diff = nowDateTime.difference(saveDateTime);

      if (diff.inMinutes >= totalPeriodInMinutes) {
        /// 超过最大间隔时间
        type = OutLineDayType.alertNotClose;
      } else if (diff.inMinutes~/offlinePeriod > hasTipsTimes) {
        type = OutLineDayType.alertCanClose;
        /// 更新显示次数
        NiimbotSp().setInt(NiimbotNetworkManager.TAG_OFFLINE_TIPS_TIMES, hasTipsTimes + 1);
      }
    }  catch (e, s) {
      debugPrint('异常信息: $e\n调用栈信息: $s');
    }
    return type;
  }


  ///show离线弹窗
  Future<void> showOfflineTipDialog(BuildContext context) async{
    String title = intlanguage("app01139", "网络异常");
    String content = intlanguage("app100001845", "当前网络未连接，请检查网络设置");
    String okStr = intlanguage('app00707', '我知道了');
    if (NiimbotNetworkManager.isOfflineDialogShowing) return;

    NiimbotNetworkManager.isOfflineDialogShowing = true;
    await showDialog(
        useRootNavigator: false,
        context: context,
        barrierColor: Color.fromRGBO(0, 0, 0, 0.35),
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Center(
              child: Container(
                  constraints: BoxConstraints(minHeight: 0, minWidth: 180),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.white,
                  ),
                  width: MediaQuery.of(context).size.width - 104,
                  child:
                  Column(mainAxisSize: MainAxisSize.min, crossAxisAlignment: CrossAxisAlignment.stretch, children: [
                    Container(
                      padding:  EdgeInsetsDirectional.fromSTEB(16, 20, 16, 6),
                      child:Text(
                        "$title",
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                            fontSize: 16.0,
                            color: Color(0xFF000000),
                            fontWeight: FontWeight.bold,
                            decoration: TextDecoration.none),
                      ),
                    ),
                    Container(
                      padding:  EdgeInsetsDirectional.fromSTEB(16,  0, 16, 20),
                      child:  Text(
                        content,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                            fontSize: 14.0,
                            color: Color(0xFF000000),
                            fontWeight: FontWeight.w400,
                            decoration: TextDecoration.none),
                      ),
                      decoration: BoxDecoration(
                          border: Border(
                            bottom: Divider.createBorderSide(context, width: 0.6),
                          )),
                    ),
                    Container(
                      child: Row(
                        children: <Widget>[

                          Expanded(
                            flex: 1,
                            child: Material(
                              borderRadius: BorderRadiusDirectional.only(
                                  bottomEnd: const Radius.circular(12),
                                  bottomStart: Radius.circular(12)),
                              color: Colors.white,
                              child: InkWell(
                                onTap: () {
                                  Navigator.of(context).pop();
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                      border: BorderDirectional(start: Divider.createBorderSide(context, width: 0.5))),
                                  alignment: Alignment.center,
                                  height: 50,
                                  child: Text(
                                    okStr,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(color:  Color(0xFF000000), fontSize: 17, fontWeight: FontWeight.w400),
                                  ),
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    )
                  ])));
        });
    NiimbotNetworkManager.isOfflineDialogShowing = false;
  }

  void dismissOfflineTipDialog(BuildContext context) {
    if (NiimbotNetworkManager.isOfflineDialogShowing) {
      Navigator.of(context).pop();
    }
  }

}
