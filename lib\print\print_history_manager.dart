import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:niimbot_log_plugin/niimbot_log_manager.dart';
import 'package:niimbot_print_setting_plugin/utils/print_preview_image_util.dart';
import 'package:niimbot_template/niimbot_template.dart';
import 'package:text/application.dart';
import 'package:text/business/print/print_history_business.dart';
import 'package:text/database/printHistory/print_history_db_utils.dart';
import 'package:text/database/printHistory/print_history_model.dart';
import 'package:text/print/template_image_utils.dart';
import 'package:text/template/util/template_misc_utils.dart';
import 'package:text/template/util/template_transform_utils.dart';
import 'package:text/tools/rfid_manager.dart';
import 'package:text/utils/event_bus.dart';
import 'package:uuid/uuid.dart';

class PrintHistoryManager {
  static const String OSS_MODULE_USER_TEMPLATE = "USER_TEMPLATE";

  saveHistory(String deviceType, String copies, String uniqueId, bool isBatchPrint, TemplateData templateData,
      {Map? templateMap, int page = 1, bool isSupportGray16 = false, bool isPrintHistory = false}) async {
    // 未登录不进行保存以及上传操作
    if (!Application.isLogin) {
      return;
    }
    if (templateMap != null) {
      // 复制 templateMap
      templateMap = Map.from(templateMap);
    }
    debugPrint("printHistory: Start开始保存打印历史");
    PrintHistoryModel model = PrintHistoryModel();
    model.uniqueId = uniqueId.isNotEmpty ? uniqueId : Uuid().v4();
    model.userId = (Application.user?.userId ?? 0).toString();
    model.deviceId = Application.deviceId;
    model.systemType = Platform.operatingSystem;
    model.timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    model.printerName = deviceType;
    model.ribbonDoubleColorInfo = RfidManager.instance.getRibbonDoubleColorInfo();
    model.copies = copies;
    model.checkVersion = Uuid().v4();
    model.templateId = templateData.id;
    model.templateName = templateData.name;

    Map<String, String> linkedDataMap = {};
    if (templateMap != null &&
        templateMap["externalData"] != null &&
        (templateMap["externalData"]["id"] != null && templateMap["externalData"]["id"] != "0")) {
      linkedDataMap['excelId'] = templateMap["externalData"]["id"];
    }
    if (templateData.dataSources != null && templateData.dataSources!.isNotEmpty) {
      linkedDataMap['type'] = templateData.dataSources![0].type.name;
    }
    model.linkedData = jsonEncode(linkedDataMap);

    debugPrint("printHistory: 设置打印历史模版结束 准备上传历史");
    if (isBatchPrint) {
      model.thumbnail = templateMap?["thumbnail"];
      // model.content = jsonEncode(templateMap);
      Map<String,dynamic> contentMap = templateData.toJson();
      //兼容旧版本打印记录
      contentMap['dataSource'] = templateData?.dataSources?.map((e) => e.toJson()).toList();
      contentMap['modify'] = templateData?.dataSourceModifies?.map((key, value) =>
          MapEntry(key, value.map((k, v) => MapEntry(k, v.toJson()))));
      contentMap['bindInfo'] = templateData?.dataSourceBindInfo?.toJson();
      model.content = jsonEncode(contentMap);
      bool result = await PrintHistoryBusiness().savePrintHistory(model.toMap());
      debugPrint("printHistory: 保存打印历史数据至本地$result");
      if (Application.networkConnected) {
        await uploadHistory(model);
      }
    } else {
      // if (!isPrintHistory) {
      var localThumbnail = templateData.localThumbnail;
      // var fileExist = localThumbnail.isNotEmpty && File(localThumbnail).existsSync();
      // 2025/6/17 Ice_Liu 从首页和我的模板直接打印，缩略图未按连接机器的颜色重新生成
      var fileExist = false;
      if (!fileExist) {
        Color? printColor = _getPrintColor();
        Uint8List onValue = await PrintPreviewImageUtil.generatePreviewImage(templateData,
            page: page, firstRfidColor: printColor, isSupportGray16: isSupportGray16, isShowBindSlash: false);
        localThumbnail = await TemplateImageUtils.writeUint8ListToFile(onValue, templateData.id ?? "");
      }
      // 本地图回写，缩略图清空, 存储两个只是为了数据兼容防止本地字段丢失
      templateMap?['localThumbnail'] = localThumbnail;
      templateMap?['local_thumb'] = localThumbnail;
      templateMap?['thumbnail'] = '';
      model.thumbnail = '';
      // model.content = jsonEncode(templateMap);
      Map<String,dynamic> contentMap = templateData.toJson();
      //兼容旧版本打印记录
      // 本地图回写，缩略图清空, 存储两个只是为了数据兼容防止本地字段丢失
      contentMap['localThumbnail'] = localThumbnail;
      contentMap['local_thumb'] = localThumbnail;
      contentMap['thumbnail'] = '';
      contentMap['dataSource'] = templateData?.dataSources?.map((e) => e.toJson()).toList();
      contentMap['modify'] = templateData?.dataSourceModifies?.map((key, value) =>
          MapEntry(key, value.map((k, v) => MapEntry(k, v.toJson()))));
      contentMap['bindInfo'] = templateData?.dataSourceBindInfo?.toJson();
      model.content = jsonEncode(contentMap);
      await PrintHistoryBusiness().savePrintHistory(model.toMap());
      if (!Application.networkConnected) {
        return;
      }
      TemplateData? template =
          await TemplateImageUtils.uploadTemplateImages(templateData.copyWith(localThumbnail: localThumbnail));
      if (template == null) {
        return;
      }
      template = await TemplateMiscUtils.processDataSourceUrl(template);
      // templateMap?["dataSources"] = template?.dataSources?.map((e) => e.toJson()).toList();
      // templateMap?["dataSource"] = templateMap?["dataSources"];
      // templateMap?["thumbnail"] = template.thumbnail;
      // List<Map<String, dynamic>> elements = [];
      // for (var element in template.elements) {
      //   Map<String, dynamic> elementInfo = element.toJson();
      //   elements.add(elementInfo);
      // }
      // templateMap?["elements"] = elements;
      model.thumbnail = template.thumbnail;
      // model.content = jsonEncode(templateMap);
      Map<String,dynamic> contentMap1 = template.toJson();
      //兼容旧版本打印记录
      contentMap1['dataSource'] = template?.dataSources?.map((e) => e.toJson()).toList();
      contentMap1['modify'] = template?.dataSourceModifies?.map((key, value) =>
          MapEntry(key, value.map((k, v) => MapEntry(k, v.toJson()))));
      contentMap1['bindInfo'] = template?.dataSourceBindInfo?.toJson();
      contentMap1['id'] = DateTime.now().millisecondsSinceEpoch.toString();
      model.templateId = contentMap1['id'];
      model.content = jsonEncode(contentMap1);
      bool result = await PrintHistoryBusiness().savePrintHistory(model.toMap());
      debugPrint("printHistory: 保存打印历史数据至本地$result");
      if (!Application.networkConnected) {
        return;
      }
      await uploadHistory(model);
      // } else {
      //   model.thumbnail = templateData.thumbnail;
      //   model.content = jsonEncode(templateMap);
      //   await PrintHistoryBusiness().savePrintHistory(model.toMap());
      //   if (Application.networkConnected) {
      //     await uploadHistory(model);
      //   }
      // }
    }
  }

  // 上传打印历史
  uploadHistory(PrintHistoryModel model) async {
    if (NiimbotLogManager.instance.slsModel == null) {
      await NiimbotLogManager.instance.getPrintTemplateContentSlsModel("sts/template/content");
    }
    var signBodyMd5 = md5.convert(utf8.encode((NiimbotLogManager.instance.slsModel?.signBody ?? "") + model.content!));
    var contentSign =
        "${NiimbotLogManager.instance.slsModel?.signHead ?? ""}.${Base64Encoder().convert(signBodyMd5.bytes)}";
    var modelMap = model.toMap();
    modelMap["contentSign"] = contentSign;
    bool isSuccess = await NiimbotLogManager.instance
        .addAppLogInstandTime(modelMap, path: "sts/template/content", topic: "printHistory");
    debugPrint("printHistory: 发送打印历史数据至服务端$isSuccess");
    if (isSuccess) {
      NiimbotEventBus.getDefault().post({'refreshPrintRecordList': {}});
      PrintHistoryDbUtils.deletePrintHistoryByUniqueIds([model.uniqueId!]);
    }
  }

  /// 本地保存的同步打印历史到云端
  syncHistoryToServer() async {
    if (!Application.isLogin) {
      return;
    }
    // 查询所有的未上传的打印历史
    List<PrintHistoryModel> printHistoryModels = await PrintHistoryDbUtils.getAllPrintHistory();
    if (printHistoryModels.isEmpty) {
      return;
    }
    for (int i = 0; i < printHistoryModels.length; i++) {
      PrintHistoryModel model = printHistoryModels[i];
      if (model.content == null) {
        continue;
      }
      Map<String, dynamic> templateMap = jsonDecode(model.content ?? '');
      TemplateData? template = await TemplateTransformUtils.parseServiceTemplateJsonToNiimbotTemplate(templateMap);
      // 查看是否拥有缩略图地址，没有的话进行上传
      if (template.thumbnail.isEmpty) {
        template = await TemplateImageUtils.uploadTemplateImages(template);
      }
      if (template == null) {
        continue;
      }
      // 回写OSS预览图地址
      model.thumbnail = template.thumbnail;
      templateMap["thumbnail"] = template.thumbnail;
      List<Map<String, dynamic>> elements = [];
      for (var element in template.elements) {
        Map<String, dynamic> elementInfo = element.toJson();
        elements.add(elementInfo);
      }
      templateMap["elements"] = elements;
      model.content = jsonEncode(templateMap);
      await uploadHistory(model);
    }
  }

  /// 获取打印颜色
  Color? _getPrintColor() {
    String gray16ColorValue = "159.160.160";
    String paperColor = RfidManager.serviceRfidInfo.rfidPaperColor ?? "";
    String ribbonColor = RfidManager.serviceRfidInfo.rfidRibbonColor ?? "";
    List<int> rgb = [];
    if (ribbonColor.isNotEmpty && ribbonColor.split(".").length == 3) {
      rgb = ribbonColor.split(".").map((element) {
        return int.parse(element);
      }).toList();
    } else if (paperColor.isNotEmpty && paperColor.split(".").length == 3) {
      rgb = paperColor.split(".").map((element) {
        return int.parse(element);
      }).toList();
    }
    if (rgb.isNotEmpty) {
      if (paperColor == gray16ColorValue || ribbonColor == gray16ColorValue) {
        return null;
      }
      return Color.fromARGB(255, rgb[0], rgb[1], rgb[2]);
    }
    return null;
  }
}

extension PrintHistoryModelToMap on PrintHistoryModel {
  Map<String, dynamic> toMap() {
    return {
      "uniqueId": uniqueId,
      "userId": userId,
      "deviceId": deviceId,
      "systemType": systemType,
      "timestamp": timestamp,
      "printerName": printerName,
      "ribbonDoubleColorInfo": ribbonDoubleColorInfo,
      "copies": copies,
      "checkVersion": checkVersion,
      "templateId": templateId,
      "templateName": templateName,
      "linkedData": linkedData,
      "content": content,
      "thumbnail": thumbnail,
    };
  }
}
