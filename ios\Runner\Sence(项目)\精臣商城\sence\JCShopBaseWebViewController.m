//
//  JCShopBaseWebViewController.m
//  Runner
//
//  Created by xy on 2018/6/13.
//  Copyright © 2018年 xiaoyao. All rights reserved.
//

#import "JCShopBaseWebViewController.h"
#import "JCShopMallsViewController.h"
#import "JCLabelCustomizeViewController.h"
#import <WebKit/WebKit.h>
#import <AlipaySDK/AlipaySDK.h>
#import "JCShopAliPay.h"
#import "sys/utsname.h"

@interface JCShopBaseWebViewController ()<WKUIDelegate,WKNavigationDelegate>

@property(nonatomic,strong) UIView *networkErrorView;
@property(nonatomic,strong) NSString *oldUrl;
@property(nonatomic,assign) NSInteger loadType;
@end

@implementation JCShopBaseWebViewController

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.loadType = 0;
        self.isNeedBackApp = YES;
        self.logStepArr = [NSMutableArray array];
        [self addStepLogDescr:@"商城首页基类初始化init"];
        self.statusBackColor = 0xFFFFFF;
        self.configuration = [[WKWebViewConfiguration alloc] init];
        self.configuration.userContentController = [WKUserContentController new];
        self.configuration.allowsInlineMediaPlayback = YES;
        WKPreferences *preferences = [WKPreferences new];
        preferences.javaScriptCanOpenWindowsAutomatically = YES;
        self.configuration.preferences = preferences;
        self.webView = [[WKWebView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight  -  (iPhoneX?88:64)) configuration:self.configuration];
        self.webView.scrollView.scrollEnabled = NO;
        self.webView.navigationDelegate = self;
        self.webView.UIDelegate = self;
        [self.webView addObserver:self forKeyPath:@"estimatedProgress" options:NSKeyValueObservingOptionOld | NSKeyValueObservingOptionNew context:nil];
        self.progressView = [[UIProgressView alloc]initWithFrame:CGRectMake(0, 0,kScreenWidth, 2)];
        CGAffineTransform transform = CGAffineTransformMakeScale(1.0f, 2.0f);
        self.progressView.transform = transform;//设定宽高
        self.progressView.progressTintColor = HEX_RGB(0x537FB7);
        [self resetWKWebViewUA];
    }
    return self;
}

- (void)addStepLogDescr:(NSString *)stepLogDescr{
    if([self isKindOfClass:[JCShopMallsViewController class]]){
        [self.logStepArr addObject:stepLogDescr];
    }
}
- (void)webView:(WKWebView *)webView runJavaScriptTextInputPanelWithPrompt:(NSString *)prompt defaultText:(nullable NSString *)defaultText initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(NSString * __nullable result))completionHandler {
    NSError *err = nil;
    NSData *dataFromString = [prompt dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *payload = [NSJSONSerialization JSONObjectWithData:dataFromString options:NSJSONReadingMutableContainers error:&err];
    if (!err){
        NSString *type = [payload objectForKey:@"type"];
        if (type && [type isEqualToString:@"JSbridge"]){
            completionHandler([self getReturnValueWithPayload:payload]);
        }
    }
}
// 自定义方法
- (NSString *)getReturnValueWithPayload:(NSDictionary *)payload{
    NSString *returnValue = @"";
    NSString *functionName = [payload objectForKey:@"functionName"];
    if ([functionName isEqualToString:@"getPrinterType"]) {
        NSString *printerName = [[NSUserDefaults standardUserDefaults] valueForKey:CENTERLASTCONNECTPRINTERNAME];
        NSString *printerType = [[JCBluetoothManager sharedInstance] printerTypeFromPrinterName:printerName isNeedShowChildType:NO];
        returnValue = printerType;
    }//
    return returnValue;
}

// 字典转化为JSON字符串
- (NSString *)toJsonWithDictionary:(NSDictionary *)dict{
    if (dict == nil || dict.allKeys.count == 0) {
        return @"";
    }else{
        NSError *parseError = nil;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dict options:NSJSONWritingPrettyPrinted error:&parseError];
        return [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    }
}

- (void)refreshWebViewWith:(NSString *)myUrlStr{};
// 使用浅色模式
- (UIUserInterfaceStyle)overrideUserInterfaceStyle
{
    // 使用浅色模式
    return UIUserInterfaceStyleLight;
}

- (void)resetWKWebViewUA{
    XYWeakSelf
    [self.webView evaluateJavaScript:@"navigator.userAgent" completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSString *oldUA = result;
        NSString *appId = [NSString stringWithFormat:@"AppId/%@",[[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleIdentifier"]];
        NSString *os = [NSString stringWithFormat:@"OS/%@",@"ios"];
        NSString *appVersionName = [NSString stringWithFormat:@"AppVersionName/%@",[[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"]];
        NSString *model = [NSString stringWithFormat:@"Model/%@",[XYTool deviceName]];
        NSString *systemVersion = [NSString stringWithFormat:@"SystemVersion/%@",[UIDevice currentDevice].systemVersion];
        NSString *deviceId = [NSString stringWithFormat:@"DeviceId/%@",[JCKeychainTool getDeviceIDInKeychain]];
        NSString *boundleId = [NSString stringWithFormat:@"boundleId/%@",[[NSBundle mainBundle] bundleIdentifier]];
        NSString *referer = @"referer/CP001Mobile";
        NSString *newUA =[NSString stringWithFormat:@"%@ %@ %@ %@ %@ %@ %@ %@ %@", oldUA,appId,os,appVersionName,model,systemVersion,deviceId,boundleId,referer];
        weakSelf.webView.customUserAgent = newUA;
//        [[NSUserDefaults standardUserDefaults] setObject:newUA forKey:@"niimbot-user-agent"];
//        [[NSUserDefaults standardUserDefaults] synchronize];
        NSLog(@"UA:%@",newUA);
    }];
}
- (void)loadUrl:(NSString *)url
{
    
    NSString *versionComplateString = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    if(!self.isNeedBackApp){
        if([url rangeOfString:@"?"].location != NSNotFound){
            url = [NSString stringWithFormat:@"%@&entrance_type_id=%@&token=%@&version=%@&platform_system_id=CP001&from=yundayin",url,self.entrance_type_id,userShopToken,versionComplateString];
        }else{
            url = [NSString stringWithFormat:@"%@?entrance_type_id=%@&token=%@&version=%@&platform_system_id=CP001&from=yundayin" ,url,self.entrance_type_id,userShopToken,versionComplateString];
        }
    }else{
        if([url rangeOfString:@"?"].location != NSNotFound){
            url = [NSString stringWithFormat:@"%@&back_app=1&entrance_type_id=%@&token=%@&version=%@&platform_system_id=CP001&from=yundayin",url,self.entrance_type_id,userShopToken,versionComplateString];
        }else{
            url = [NSString stringWithFormat:@"%@?back_app=1&entrance_type_id=%@&token=%@&version=%@&platform_system_id=CP001&from=yundayin" ,url,self.entrance_type_id,userShopToken,versionComplateString];
        }
    }
    
    [self.progressView setAlpha:1.0f];
    if(self.jumpSource != nil && self.jumpSource.length > 0){
        url = [NSString stringWithFormat:@"%@&jumpSource=%@",url,self.jumpSource];
    }
    
    // 非中文环境且存在站点信息
    if (!([XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] && STR_IS_NIL([[XYCenter sharedInstance] siteCode]))) {
        url = [url stringByAppendingFormat:@"&siteCode=%@&languageCode=%@", [[XYCenter sharedInstance] siteCode], [[XYCenter sharedInstance] abroadSiteLanguageCode]];
    }

    [self addStepLogDescr:[NSString  stringWithFormat:@"商城首页加载通过URL：%@",url]];
    [self.webView loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:url] cachePolicy:NSURLRequestReloadIgnoringCacheData timeoutInterval:15.0]];
    self.oldUrl = url;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(20 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if(self.loadType == 0){
            self.networkErrorView.hidden = NO;
            NSLog(@"定时检查 商城H5加载白屏失败");
        }else{
        }
    });
}
- (void)viewDidLoad {
    XYWeakSelf
    [super viewDidLoad];
    [self setVCBackImageView];
    [self.view addSubview:self.webView];
    [self.view addSubview:self.progressView];
    [self.view addSubview:self.networkErrorView];
    self.lsl_prefersNavigationBarHidden = YES;
    [self.networkErrorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(weakSelf.view);
    }];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillShow:) name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWasHidden:) name:UIKeyboardDidHideNotification object:nil];
    // 添加应用前后台通知监听
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(applicationWillEnterForeground:) name:UIApplicationWillEnterForegroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(applicationDidEnterBackground:) name:UIApplicationDidEnterBackgroundNotification object:nil];
    [self addStepLogDescr:@"商城首页基类viewDidLoad"];
}

- (void)colseCurrentPage{
    if(self.colseCurrentPageBlock){
        self.colseCurrentPageBlock();
    }else{
        [self.navigationController popViewControllerAnimated:YES];
    }
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [self addStepLogDescr:@"商城首页基类viewWillAppear"];
    if(self.webView.title == nil) {
        [self.webView reload];
    }
}

- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
}

- (void)getTokenFromNative{
    [[JCLoginManager sharedInstance] checkLogin:^{
        
    } viewController:self loginSuccessBlock:^{
        // 加载完毕，传递Token
        NSString *jsStr = xy_isLogin ? [NSString stringWithFormat:@"AppSetToken('%@', '%@', '%@', '%@', '%@')",m_userModel.shopToken, [JCKeychainTool getDeviceIDInKeychain], m_userModel.userId, @"iOS_YDY", @"1"] : [NSString stringWithFormat:@"AppSetToken('', '%@', '', '%@', '%@')",[JCKeychainTool getDeviceIDInKeychain], @"iOS_YDY", @"0"];
        [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
            NSLog(@"%@----%@",result, error);
        }];
    }];
}

- (UIView *)networkErrorView{
    if(!_networkErrorView){
        _networkErrorView = [[UIView alloc] init];
        _networkErrorView.backgroundColor = HEX_RGB(0xE5E5E5);
        _networkErrorView.hidden = YES;
        UIImageView *networkErrImage = [[UIImageView alloc] init];
        networkErrImage.image = XY_IMAGE_NAMED(@"shopNetworkErr");
        [_networkErrorView addSubview:networkErrImage];
        
        UILabel *networkErrTipLabel = [[UILabel alloc] init];
        networkErrTipLabel.font = MY_FONT_Regular(14);
        networkErrTipLabel.numberOfLines = 0;
        networkErrTipLabel.textColor = HEX_RGB(0x666666);
        networkErrTipLabel.textAlignment = NSTextAlignmentCenter;
        networkErrTipLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app100000121", @"当前网络状态不佳，请稍后再试");
        [_networkErrorView addSubview:networkErrTipLabel];
        
        UIButton *refreshBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        refreshBtn.backgroundColor = HEX_RGB(0xFFFFFF);
        refreshBtn.layer.cornerRadius = 20;
        refreshBtn.titleLabel.font = MY_FONT_Regular(16);
        [refreshBtn setTitleColor:COLOR_NEW_THEME forState:UIControlStateNormal];
        [refreshBtn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000048", @"重新加载") forState:UIControlStateNormal];
        [refreshBtn  addTarget:self action:@selector(refreshCurrentWeb) forControlEvents:UIControlEventTouchUpInside];
        [_networkErrorView addSubview:refreshBtn];
        
        UIButton *backBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        backBtn.backgroundColor = HEX_RGB(0xFFFFFF);
        backBtn.layer.cornerRadius = 20;
        backBtn.titleLabel.font = MY_FONT_Regular(16);
        [backBtn setTitleColor:COLOR_NEW_THEME forState:UIControlStateNormal];
        [backBtn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app01179", @"返回") forState:UIControlStateNormal];
        [backBtn  addTarget:self action:@selector(colseCurrentPage) forControlEvents:UIControlEventTouchUpInside];
        [_networkErrorView addSubview:backBtn];
        if(self.isNeedBackApp){
            [networkErrImage mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(_networkErrorView);
                make.width.mas_equalTo(@(SCREEN_WIDTH - XY_AutoWidth(100)));
                make.height.mas_equalTo(networkErrImage.mas_width).multipliedBy(0.5);
                make.centerY.equalTo(_networkErrorView).offset(-130);
            }];
            [networkErrTipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(_networkErrorView);
                make.width.mas_equalTo(@(SCREEN_WIDTH - XY_AutoWidth(100)));
                make.top.equalTo(networkErrImage.mas_bottom).offset(35);
            }];
            
            float btnTitleWidth1 = [XY_LANGUAGE_TITLE_NAMED(@"app100000048", @"重新加载") jk_sizeWithFont:MY_FONT_Regular(16) constrainedToWidth:1000].width;
            [refreshBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(btnTitleWidth1 + 48);
                make.height.mas_equalTo(@40);
                make.centerX.equalTo(_networkErrorView);
                make.top.equalTo(networkErrTipLabel.mas_bottom).offset(28);
            }];
            
            [backBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(btnTitleWidth1 + 48);
                make.height.mas_equalTo(@40);
                make.centerX.equalTo(_networkErrorView);
                make.top.equalTo(refreshBtn.mas_bottom).offset(20);
            }];
        }else{
            [networkErrImage mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(_networkErrorView);
                make.width.mas_equalTo(@(SCREEN_WIDTH - XY_AutoWidth(100)));
                make.height.mas_equalTo(networkErrImage.mas_width).multipliedBy(0.5);
                make.centerY.equalTo(_networkErrorView).offset(-100);
            }];
            [networkErrTipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(_networkErrorView);
                make.width.mas_equalTo(@(SCREEN_WIDTH - XY_AutoWidth(100)));
                make.top.equalTo(networkErrImage.mas_bottom).offset(35);
            }];
            
            float btnTitleWidth = [XY_LANGUAGE_TITLE_NAMED(@"app100000048", @"重新加载") jk_sizeWithFont:MY_FONT_Regular(16) constrainedToWidth:1000].width;
            [refreshBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(btnTitleWidth + 48);
                make.height.mas_equalTo(@40);
                make.centerX.equalTo(_networkErrorView);
                make.top.equalTo(networkErrTipLabel.mas_bottom).offset(28);
            }];
        }
        
    }
    return _networkErrorView;
}

- (void)refreshCurrentWeb{
    [self.webView loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:self.oldUrl] cachePolicy:NSURLRequestReloadIgnoringCacheData timeoutInterval:15.0]];
}

- (void)shopDetailStateView{
    float stateViewheight = iPhoneX?44:20;
    UIView *navBKView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, stateViewheight)];
    navBKView.backgroundColor = HEX_RGB(0xFFFFFF);
    [self.view addSubview:navBKView];
    self.detailStateView = navBKView;
}

- (void)statusBarBackgroundColor:(NSString *)colorValueString{
    NSDictionary *colorValueDic = [colorValueString xy_toDictionary];
    NSString *typeValue = [colorValueDic objectForKey:@"type"];
    NSString *colorValue = [colorValueDic objectForKey:@"color"];
    NSString *textColor = [NSString stringWithFormat:@"0x%@",[colorValue stringByReplacingOccurrencesOfString:@"#" withString:@""]];
    unsigned long red = strtoul([textColor UTF8String],0,16);
    if([typeValue isEqualToString:@"1"]){
        AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
        XYTabBarController *currentTabbarVC = appDelegate.mainVC;
        XYNavigationController *navVC = currentTabbarVC.selectedViewController;
        UIStatusBarStyle statusBarStyle = UIStatusBarStyleDefault;
        if (@available(iOS 13.0, *)) {
            statusBarStyle = UIStatusBarStyleDarkContent;
        }
        self.statusBarStyle = statusBarStyle;
        if([navVC.visibleViewController isKindOfClass:[self class]]){
            [UIApplication sharedApplication].statusBarStyle = self.statusBarStyle;
        }
    }else{
        self.statusBarStyle = UIStatusBarStyleLightContent;
        [UIApplication sharedApplication].statusBarStyle = self.statusBarStyle;
    }
    self.detailStateView.backgroundColor = HEX_RGB(red);
    self.statusBackColor = red;
}

-(void)addObserverNotification{
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(windowDidBecomeHidden:) name:UIWindowDidBecomeHiddenNotification object:nil];
}

-(void)windowDidBecomeHidden:(NSNotification *)noti{
    UIWindow * win = (UIWindow *)noti.object;
    if(win){
        UIViewController *rootVC = win.rootViewController;
        NSArray<__kindof UIViewController *> *vcs = rootVC.childViewControllers;
        if([vcs.firstObject isKindOfClass:NSClassFromString(@"AVPlayerViewController")]){
        #pragma clang diagnostic push
        #pragma clang diagnostic ignored "-Wdeprecated"
            [[UIApplication sharedApplication] setStatusBarHidden:NO withAnimation:UIStatusBarAnimationNone];
        #pragma clang diagnostic pop
        }
    }
}

- (void)setVCBackImageView{
    UIView *backView = [[UIView alloc] initWithFrame:self.view.bounds];
    backView.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:backView];
}

// 页面开始加载时调用
-(void)webView:(WKWebView *)webView didStartProvisionalNavigation:(WKNavigation *)navigation{
    NSLog(@"didStartProvisionalNavigation");
    NSLog(@"当前加载URL：%@",webView.URL);
    [self addStepLogDescr:[NSString  stringWithFormat:@"商城首页webview回调开始加载通过URL：%@",webView.URL.absoluteString]];
}

// 当内容开始返回时调用
- (void)webView:(WKWebView *)webView didCommitNavigation:(WKNavigation *)navigation{
    NSLog(@"内容开始返回:%@ 当前加载URL：%@",[XYTool getCurrentTimes],webView.URL);
    [self addStepLogDescr:[NSString  stringWithFormat:@"商城首页webview回调内容开始返回通过URL：%@",webView.URL.absoluteString]];
}

// 页面加载完成之后调用
- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation{//这里修改导航栏的标题，动态改变
    NSLog(@"开始H5完毕:%@  当前加载URL：%@",[XYTool getCurrentTimes],webView.URL);
    NSLog(@"%@",self.view);
    self.loadSuccess = YES;
    self.loadType = 1;
    self.networkErrorView.hidden = YES;
    [self.webView evaluateJavaScript:@"navigator.userAgent" completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSLog(@"UA:%@",result);
    }];
    [self addStepLogDescr:[NSString  stringWithFormat:@"商城首页webview回调加载H5完毕通过URL：%@",webView.URL.absoluteString]];
}

- (void)webViewWebContentProcessDidTerminate:(WKWebView *)webView NS_AVAILABLE(10_11, 9_0){
    [self addStepLogDescr:[NSString  stringWithFormat:@"商城首页webview回调即将白屏通过URL：%@",webView.URL.absoluteString]];
    [self.webView reload];
}

// 页面加载失败时调用
-(void)webView:(WKWebView *)webView didFailProvisionalNavigation:(WKNavigation *)navigation withError:(NSError *)error{
    NSLog(@"请求失败，不显示webView");
    self.loadSuccess = NO;
    self.loadType = 2;
    NSDictionary *userInfo = error.userInfo;
    NSURL *errorFailingUrl = [userInfo objectForKey:@"NSErrorFailingURLKey"];
    NSString *errorFailingString = [errorFailingUrl absoluteString];
    if(error.code == 102 || [errorFailingString rangeOfString:@"iwantbacktoapp"].location != NSNotFound || [errorFailingString rangeOfString:@"alipay"].location != NSNotFound){
        [self.progressView setProgress:0.0f animated:YES];
        [self.progressView setAlpha:0.0f];
    }else{
        self.networkErrorView.hidden = NO;
        [self addStepLogDescr:[NSString  stringWithFormat:@"商城首页webview回调didFailProvisional加载失败通过URL：%@",webView.URL.absoluteString]];
    }
}

- (void)webView:(WKWebView *)webView didFailNavigation:(null_unspecified WKNavigation *)navigation withError:(NSError *)error{
    [self addStepLogDescr:[NSString  stringWithFormat:@"商城首页webview回调didFailNavigation加载失败通过URL：%@",webView.URL.absoluteString]];
}

// 接收到服务器跳转请求之后再执行
- (void)webView:(WKWebView *)webView didReceiveServerRedirectForProvisionalNavigation:(WKNavigation *)navigation{
    NSLog(@"didReceiveServerRedirectForProvisionalNavigation");
}

#ifdef DEBUG
- (void)webView:(WKWebView *)webView didReceiveAuthenticationChallenge:(NSURLAuthenticationChallenge *)challenge completionHandler:(void (^)(NSURLSessionAuthChallengeDisposition, NSURLCredential * _Nullable))completionHandler {
    NSURLCredential * credential = [[NSURLCredential alloc] initWithTrust:[challenge protectionSpace].serverTrust];
    completionHandler(NSURLSessionAuthChallengeUseCredential, credential);
}
#endif


- (void)webView:(WKWebView *)webView decidePolicyForNavigationResponse:(WKNavigationResponse *)navigationResponse decisionHandler:(void (^)(WKNavigationResponsePolicy))decisionHandler {
    if (((NSHTTPURLResponse *)navigationResponse.response).statusCode == 200) {
        decisionHandler (WKNavigationResponsePolicyAllow);
    }else {
        decisionHandler(WKNavigationResponsePolicyCancel);
    }
}

//支付宝支付完成或取消时候回到App回调
- (void)webView:(WKWebView*)webView decidePolicyForNavigationAction:(WKNavigationAction*)navigationAction decisionHandler:(void(^)(WKNavigationActionPolicy))decisionHandler{
    WKNavigationActionPolicy actionPolicy = WKNavigationActionPolicyAllow;
    NSString *str = [navigationAction.request.URL absoluteString];
    if ([str containsString:@"alipayurl"]) {
        [self performSelector:@selector(gotoAliPay1) withObject:nil afterDelay:1];
    }
    BOOL isIntercepted = NO;
    isIntercepted = [[AlipaySDK defaultService] payInterceptorWithUrl:[navigationAction.request.URL absoluteString] fromScheme:@"JCYDY" callback:^(NSDictionary *result) {
        // 处理支付结果
        // isProcessUrlPay 代表 支付宝已经处理该URL
        if ([result[@"isProcessUrlPay"]boolValue]) {
            
        }
    }];
    
    if (isIntercepted) {
        actionPolicy = WKNavigationActionPolicyCancel;
    }
    
    NSString *urlString = [[navigationAction.request.URL absoluteString] stringByRemovingPercentEncoding];
    if ([urlString containsString:@"weixin://wap/pay?"]) {
        actionPolicy =WKNavigationActionPolicyCancel;
        //解决wkwebview weixin://无法打开微信客户端的处理
        NSURL*url = [NSURL URLWithString:urlString];
        BOOL bSucc = [[UIApplication sharedApplication] canOpenURL:url];
        if(!bSucc) {
            [MBProgressHUD showToastWithMessageDarkColor:@"未检测到微信APP，请您先安装"];
        } else {
            [[UIApplication sharedApplication] openURL:url options:@{UIApplicationOpenURLOptionUniversalLinksOnly: @NO} completionHandler:^(BOOL success) {
            }];
        }
    }
    
    decisionHandler(actionPolicy);
    
}
- (void)gotoAliPay1
{
    [self.webView goBack];
    [self performSelector:@selector(gotoAliPay2) withObject:nil afterDelay:3];
}
- (void)gotoAliPay2
{
    JCShopAliPay *c1 = [[JCShopAliPay alloc] init];
    [self.navigationController pushViewController:c1 animated:YES];
}

// 监听事件处理
- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context{
    XYWeakSelf
    if ([keyPath isEqual:@"estimatedProgress"] && object == self.webView) {
        [self.progressView setAlpha:1.0f];
        [self.progressView setProgress:self.webView.estimatedProgress animated:YES];
        if (self.webView.estimatedProgress  >= 1.0f) {
            [UIView animateWithDuration:0.3 delay:0.3 options:UIViewAnimationOptionCurveEaseOut animations:^{
                [weakSelf.progressView setAlpha:0.0f];
            } completion:^(BOOL finished) {
                [weakSelf.progressView setProgress:0.0f animated:YES];
            }];
        }
//        [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
    }else{
//        [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
    }
}

- (void)dealloc
{
    [self.webView removeObserver:self forKeyPath:@"estimatedProgress"];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    NSLog(@"控制器：%@释放",NSStringFromClass([self class]));
}

- (void)keyboardWillShow:(NSNotification *)aNotification
{
     //获取键盘的高度
    NSDictionary *userInfo = [aNotification userInfo];
    NSValue *aValue = [userInfo      objectForKey:UIKeyboardFrameEndUserInfoKey];
    CGRect keyboardRect = [aValue CGRectValue];
    int height = keyboardRect.size.height;
    NSString *jsStr = [NSString stringWithFormat:@"setKeyboardHeight('%@')",StringFromInt((NSInteger)height)];
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSLog(@"%@----%@",result, error);
    }];
}

//当键盘出现或改变时调用
- (void)keyboardWasHidden:(NSNotification *)aNotification
{
    int height = 0;
    NSString *jsStr = [NSString stringWithFormat:@"setKeyboardHeight('%@')",StringFromInt((NSInteger)height)];
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSLog(@"%@----%@",result, error);
    }];
}

// 应用即将进入前台
- (void)applicationWillEnterForeground:(NSNotification *)aNotification
{
    NSString *jsStr = @"applicationWillEnterForeground()";
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        if (error) {
            NSLog(@"applicationWillEnterForeground JS执行错误: %@", error);
        } else {
            NSLog(@"applicationWillEnterForeground 事件已发送到前端");
        }
    }];
}

// 应用已进入后台
- (void)applicationDidEnterBackground:(NSNotification *)aNotification
{
    NSString *jsStr = @"applicationDidEnterBackground()";
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        if (error) {
            NSLog(@"applicationDidEnterBackground JS执行错误: %@", error);
        } else {
            NSLog(@"applicationDidEnterBackground 事件已发送到前端");
        }
    }];
}


- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

/*
 #pragma mark - Navigation
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

@end

