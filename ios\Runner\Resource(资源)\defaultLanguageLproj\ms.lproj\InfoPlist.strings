/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "Aplikasi ini akan mengakses kebenaran kamera anda untuk fungsi imbas kod bar, pengecaman teks dan mengambil gambar. Adakah anda membenarkan kamera dibuka?";
NSBluetoothPeripheralUsageDescription = "Aplikasi ini akan mengakses kebenaran Bluetooth anda untuk perkhidmatan sambungan pencetak. Adakah anda membenarkan Bluetooth dibuka?";
NSBluetoothAlwaysUsageDescription = "Aplikasi ini akan mengakses kebenaran Bluetooth anda untuk perkhidmatan sambungan pencetak. Adakah anda membenarkan Bluetooth dibuka?";
NSContactsUsageDescription = "Aplikasi ini akan mengakses kebenaran buku alamat anda untuk perkhidmatan membaca kenalan. Adakah anda membenarkan buku alamat dibuka?";
NSMicrophoneUsageDescription = "Aplikasi ini akan mengakses kebenaran mikrofon anda untuk perkhidmatan pengecaman suara. Adakah anda membenarkan mikrofon dibuka?";
NSPhotoLibraryUsageDescription = "Kebenaran ini akan digunakan untuk mencetak bahan gambar, pengecaman kod bar, pengecaman kod QR, pengecaman teks, menetapkan avatar tersuai dan sebagainya. Sila 'Benarkan Akses Semua Foto' untuk memastikan aplikasi NIIMBOT Cloud Printing dapat mengakses album foto dengan lancar. Jika anda menggunakan 'Pilih Foto...', semua foto yang tidak dipilih serta foto-foto baru yang akan datang tidak akan dapat diakses dalam NIIMBOT.";
NSLocationWhenInUseUsageDescription = "Untuk memudahkan anda menggunakan rangkaian Wi-Fi berdekatan, NIIMBOT memohon kebenaran lokasi anda.";
NSLocationAlwaysUsageDescription = "Untuk memudahkan anda menggunakan rangkaian Wi-Fi berdekatan, NIIMBOT memohon kebenaran lokasi anda.";
NSLocationAlwaysAndWhenInUseUsageDescription = "Untuk memudahkan anda menggunakan rangkaian Wi-Fi berdekatan, NIIMBOT memohon kebenaran lokasi anda.";
NSSpeechRecognitionUsageDescription = "Aplikasi ini memerlukan persetujuan anda untuk mengakses pengecaman suara. Adakah anda membenarkan pengecaman suara dibuka?";
"UILaunchStoryboardName" = "LaunchScreen";
