import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:niim_login/login_plugin/macro/color.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:text/pages/industry_template/home/<USER>/hard_ware_serise_model.dart';
import 'package:text/pages/industry_template/select_label/device_series_switch_page.dart';
import 'package:text/pages/industry_template/select_label/label_details_page.dart';
import 'package:text/pages/industry_template/select_label/label_search_page.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/hardware_manager.dart';
import 'package:text/utils/input_field.dart';
import 'package:text/utils/item_divider.dart';
import 'package:text/utils/log_utils.dart';
import 'package:text/utils/plane_button.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/utils/toast_util.dart';
import 'package:text/widget/custom_popup_menu.dart';

import '../../../application.dart';
import '../../../macro/constant.dart';
import '../../../utils/cachedImageUtil.dart';
import '../../../utils/event_bus.dart';
import '../../../utils/image_utils.dart';
import '../home/<USER>/label_usage_record.dart';
import 'hardware_list_model.dart';
import 'label_category_list_model.dart';
import 'label_category_model.dart';
import 'label_selector_controller.dart';

enum LabelSelecSence { normalCanvas, cableCanvas, unkonwSence }

class LabelSelectorPage extends StatefulWidget {
  /// 是否是选中标签后直接pop返回数据
  final bool isNeedPop;
  final bool needCheckConnected;

  /// 是否展示型号, 不展示型号的时候根据缓存的机型所在系列筛选标签纸
  final bool isShowSeries;

  /// 是否从画板界面进入
  final bool isFromCanvas;
  final bool isCable;
  final ValueChanged<Map<String, dynamic>>? onChanged;
  //切换标签场景
  final LabelSelecSence sence;

  LabelSelectorPage(
      {Key? key,
      this.isNeedPop = true,
      this.onChanged,
      this.needCheckConnected = false,
      this.isShowSeries = true,
      this.isCable = false,
      this.sence = LabelSelecSence.unkonwSence,
      this.isFromCanvas = false})
      : super(key: key);

  @override
  State<LabelSelectorPage> createState() => _LabelSelectorPageState();
}

class _LabelSelectorPageState extends State<LabelSelectorPage> with TickerProviderStateMixin {
  final TextEditingController _textEditingController = TextEditingController();

  /// 连接的硬件系列模型
  HardWareSeriesModel? connectSeriesModel;

  @override
  void initState() {
    super.initState();
    LabelSelectorController _controller = LabelSelectorController()
      ..isNeedPop = widget.isNeedPop
      ..onChanged = widget.onChanged;

    // 连接硬件系列模型初始化
    HardWareManager.instance().connectHardWareSeriesModel().then((value) => connectSeriesModel = value);
    _controller.isCable = widget.isCable;
    if (widget.isCable) {
      _controller.categories.value = [];
      _controller.listOfCategory.value ??= [];
    }

    /// 注入模版管理
    Get.put(_controller);
    _controller.fetchRecentLabels = () async {
      List<LabelUsageRecord>? labelUsageRecord = await ToNativeMethodChannel().getUseRecentLabel(limit: 10);
      if (labelUsageRecord == null) {
        return null;
      }
      List<Item> labels = labelUsageRecord.map((e) => Item.fromJson(e.rawData)).toList();
      return labels;
    };

    handlePrinterConnectState(value) {
      if (value is Map) {
        _controller.printerConnectState = value['connected'] > 0
            ? (value['rfidStatus'] > 0 ? PrinterConnectState.connected : PrinterConnectState.cannotIdentifyRfid)
            : PrinterConnectState.disconnected;
        if (_controller.printerConnectState == PrinterConnectState.connected) {
          /// 获取打印机 RFID 状态
          ToNativeMethodChannel().getPrinterLabelData().then((value) {
            if (value is Map) {
              if (value.isNotEmpty) {
                _controller.printerConnectState = PrinterConnectState.rfidIdentified;
                final _dataMap = Map<String, dynamic>.from(value);
                _controller.labelIdentifiedItem = Item.fromJson(_dataMap);
              }
            }
          });
        }
      }
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      /// 数据埋点
      ToNativeMethodChannel().sendTrackingToNative({"track": "view", "posCode": "069", "ext": {}});

      /// TODO: get from native
      _controller.includeVip = '1';

      /// 获取打印机连接状态
      ToNativeMethodChannel().getPrinterConnectState().then((value) {
        if (value is Map) {
          String? seriesId = value['seriesId'];

          if (HardWareManager.instance().seriesList?.isNotEmpty ?? false) {
            /// 当前连接的设备
            HardWareSeriesModel? series;
            if ((seriesId ?? '').isNotEmpty) {
              series = HardWareManager.instance().seriesList?.firstWhereOrNull((element) {
                return seriesId == element.id;
              });
            }

            /// 上次存储选择的设备, 用 B21 兜底
            if (series == null) {
              String? latestHardwareSeriesId = Application.sp.getString(ConstantKey.latestHardwareSeriesId) ?? '';
              if (latestHardwareSeriesId.isNotEmpty) {
                series = HardWareManager.instance()
                    .seriesList
                    ?.firstWhereOrNull((element) => element.id == latestHardwareSeriesId);
              } else {
                series = HardWareManager.instance()
                    .seriesList
                    ?.firstWhereOrNull((element) => element.hardwareIdStr?.contains('27') == true);
              }
            }

            if (series != null) {
              _controller.selectedPrinterInfo.value = series;
            }
          }

          // 最后兜底
          if (_controller.selectedPrinterInfo() == null) {
            _controller.selectedPrinterInfo.value = HardWareSeriesModel(
              id: '4',
              name: Application.currentAppLanguageType.toLowerCase().contains('zh') ? '拾光机' : 'B21 Series',
              image: "https://oss-print-fat.jc-test.cn/public_resources/images/01d8558fad54fb78ec1e7552eb9ecc5b.png",
              hardwareNameStr: "B21-L2W,B21,B21-L2B,B21-C2B,B21S-C2B,B21S",
              hardwareIdStr: "9,27,31,32,53,54",
            );
          }
          _controller.machineIdList = _controller.selectedPrinterInfo()?.hardwareIdStr ?? '';
          _getTemplate();

          handlePrinterConnectState(value);
        }
      });
    });

    // 注册标签纸状态更新
    NiimbotEventBus.getDefault().register(this, (data) {
      if (data is Map && data.containsKey('printerConnectState')) {
        /// 当前连接的设备
        String? seriesId = data['printerConnectState']['seriesId'];
        if ((seriesId ?? '').isNotEmpty) {
          HardWareSeriesModel? series = HardWareManager.instance().findHardwareSeriesModel(seriesId: seriesId ?? '');
          if (series != null) {
            _controller.selectedPrinterInfo.value = series;
            _controller.machineIdList = _controller.selectedPrinterInfo()?.hardwareIdStr ?? '';
            _controller.generateDeviceAdapterCategories(_controller.machineIdList);
          }
        }
        handlePrinterConnectState(data['printerConnectState']);
        Map printerConnectInfo = data['printerConnectState'];
        int connected = printerConnectInfo["connected"];
        Log.d("监听到打印机变化$data");
        // 确认当前处于连接状态
        if (connected == 1) {
          // 用机器 id 查找
          String? machineId = printerConnectInfo["machineId"];
          // 通过机型ID查找系列模型
          connectSeriesModel = HardWareManager.instance().findHardwareSeriesModelBy(machineId: machineId);
        } else {
          connectSeriesModel = null;
        }
      } else if (data is Map && data.containsKey('labelData') && data['labelData'] is Map) {
        if ((data['labelData'] as Map).isNotEmpty) {
          final _dataMap = Map<String, dynamic>.from(data['labelData']);
          var record = LabelUsageRecord.fromJson(_dataMap);

          _controller.labelIdentifiedItem = Item.fromJson(record.rawData);
          _controller.printerConnectState = PrinterConnectState.rfidIdentified;
        } else {
          if (_controller.printerConnectState == PrinterConnectState.rfidIdentified) {
            _controller.printerConnectState = PrinterConnectState.connected;
          }
        }
      } else if (data is Map && data.containsKey('networkChanged')) {
        if (_controller.connectState.value != data['networkChanged']) {
          _controller.connectState.value = data['networkChanged'];
          if (_controller.connectState.value == true) {
            _getTemplate();
          }
        }
      }
    });

    _controller.connectState.value = Application.networkConnected;

    // 模版状态更新
    _controller.addListenerEvent((event) {
      if (event is ResetTemplateState && event == ResetTemplateState.ResetAllListState) {
        _controller.resetState(ResetTemplateState.ResetCategoryListState);
        _controller.resetState(ResetTemplateState.ResetTemplateListState);
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    _controller.closeListener();
    Get.delete<LabelSelectorController>();
    NiimbotEventBus.getDefault().unregister(this);
  }

  LabelSelectorController get _controller => Get.find<LabelSelectorController>();

  _getTemplate() {
    // 获取模版分类以及推荐列表
    _controller.getTemplateCategories(
        includeVip: _controller.includeVip, hardwareIdStr: _controller.machineIdList, isCable: widget.isCable);
    if (!_controller.isCable) {
      _controller.getRecommendList();
    }
    // 重置状态
    _controller.resetState(ResetTemplateState.ResetAllListState);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: MediaQuery.viewInsetsOf(context).bottom),
      decoration: const BoxDecoration(
        color: ThemeColor.background,
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
      height: MediaQuery.sizeOf(context).height - 60,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 7),
            child: Row(
              children: [
                widget.isShowSeries
                    ? GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          if (widget.needCheckConnected && _controller.printerConnected.value == true) {
                            return;
                          }

                          /// 数据埋点
                          if (widget.sence != LabelSelecSence.unkonwSence) {
                            ToNativeMethodChannel().sendTrackingToNative({
                              "track": "click",
                              "posCode": "069_172_174",
                              "ext": {"source": widget.sence == LabelSelecSence.cableCanvas ? 2 : 1}
                            });
                          } else {
                            ToNativeMethodChannel()
                                .sendTrackingToNative({"track": "click", "posCode": "069_172_174", "ext": {}});
                          }

                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            shape: const RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
                            builder: (BuildContext context) {
                              return DeviceSeriesSwitchPage(
                                  selectedSeries: _controller.selectedPrinterInfo()?.name ?? '',
                                  currentConnectSeriesModel: connectSeriesModel,
                                  isNeedDisConnectHardWareAlert: true);
                            },
                          ).then((seriesModel) {
                            if (seriesModel is HardWareSeriesModel) {
                              // 重复选择一个机型排重
                              if (Application.sp.getString(ConstantKey.latestHardwareSeriesId) != seriesModel.id) {
                                Application.sp.setString(ConstantKey.latestHardwareSeriesId, seriesModel.id ?? '');
                                // 存储系列下首个设备name名称为选中型号
                                HardwareModel? hardWareModel = HardWareManager.instance()
                                    .findHardwareModel(machineId: seriesModel.hardwareIdList?.first ?? '');
                                String machineName = hardWareModel?.name ?? '';
                                if (machineName.contains('-')) {
                                  machineName = machineName.split('-').first;
                                }
                                // 存储当前连接设备的机型名称
                                Application.sp.setString(ConstantKey.latestHardwareName, machineName);
                                // 发送通知，告知RFID的显示变化
                                NiimbotEventBus.getDefault().post({'action': 'RFIDShowStatus'});
                                _controller.selectedPrinterInfo.value = seriesModel;
                                _controller.machineIdList = _controller.selectedPrinterInfo()?.hardwareIdStr ?? '';
                                _controller.generateDeviceAdapterCategories(_controller.machineIdList);
                              }
                            }
                          });
                        },
                        child: Container(
                          decoration: const BoxDecoration(
                              color: ThemeColor.listBackground, borderRadius: BorderRadius.all(Radius.circular(17))),
                          padding: const EdgeInsets.fromLTRB(14, 9, 10, 9),
                          child: Obx(() {
                            return Opacity(
                              opacity:
                                  (widget.needCheckConnected && _controller.printerConnected.value == true) ? 0.5 : 1.0,
                              // opacity: 1.0,
                              child: Row(
                                children: [
                                  Text(
                                    _controller.selectedPrinterInfo()?.name ?? '',
                                    style: const TextStyle(
                                        color: ThemeColor.title, fontSize: 13, fontWeight: FontWeight.w600),
                                  ),
                                  const SizedBox(
                                    width: 4,
                                  ),
                                  const SvgIcon('assets/images/industry_template/replace_label/switch_device.svg')
                                ],
                              ),
                            );
                          }),
                        ),
                      )
                    : const SizedBox(
                        width: 30,
                      ),
                const Spacer(),
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: MediaQuery.sizeOf(context).width - 200),
                  child: Text(
                    intlanguage('app100000511', '请选择标签纸'),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: ThemeColor.mainTitle, fontSize: 17, fontWeight: FontWeight.w600),
                  ),
                ),
                const Spacer(),
                const SizedBox(
                  width: 30,
                ),
                PlaneButton(
                  width: 30,
                  height: 30,
                  child: const SvgIcon('assets/images/industry_template/replace_label/close_line.svg'),
                  onTap: () => Navigator.of(context).pop(),
                ),
              ],
            ),
          ),
          const ItemDivider(),

          /// 搜索框
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                /// 数据埋点
                ///
                if (widget.sence != LabelSelecSence.unkonwSence) {
                  ToNativeMethodChannel().sendTrackingToNative({
                    "track": "click",
                    "posCode": "069_172_173",
                    "ext": {"source": widget.sence == LabelSelecSence.cableCanvas ? 2 : 1}
                  });
                } else {
                  ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "069_172_173", "ext": {}});
                }

                showModalBottomSheet(
                  transitionAnimationController: AnimationController(
                    duration: const Duration(milliseconds: 10),
                    reverseDuration: const Duration(milliseconds: 10),
                    debugLabel: 'BottomSheet-LabelSearchPage',
                    vsync: this,
                  ),
                  enableDrag: false,
                  isDismissible: false,
                  barrierColor: Colors.transparent,
                  context: context,
                  isScrollControlled: true,
                  shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
                  builder: (BuildContext context) {
                    return LabelSearchPage();
                  },
                ).then((value) {
                  if (value is Map<String, dynamic>) {
                    if (_controller.isNeedPop == false && _controller.onChanged != null) {
                      /// 回调onChanged且不pop
                      _controller.onChanged?.call(value);
                    } else {
                      Navigator.of(context).pop(value);
                    }
                  }
                });
              },
              child: Container(
                decoration: BoxDecoration(color: ThemeColor.listBackground, borderRadius: BorderRadius.circular(20)),
                child: Row(
                  children: [
                    const SizedBox(
                      width: 12,
                    ),
                    const SvgIcon('assets/images/industry_template/replace_label/search_icon.svg'),
                    Expanded(
                        child: InputField(
                      enabled: false,
                      textEditingController: _textEditingController,
                      textInputAction: TextInputAction.search,
                      height: 34,
                      contentPadding: const EdgeInsets.fromLTRB(6, 12, 6, 0),
                      hintText: intlanguage('app100000512', '请输入标签条码、名称'),
                      onSubmitted: (String text) {},
                    )),
                    const SizedBox(
                      width: 8,
                    ),
                    GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () async {
                          /// 检查网络连接状态
                          // final connectivityResult = await (Connectivity().checkConnectivity());

                          /// 没有网络显示异常并退出
                          if (!Application.networkConnected) {
                            showToast(msg: intlanguage('app01139', '网络异常'));
                            return;
                          }

                          /// 数据埋点

                          if (widget.sence != LabelSelecSence.unkonwSence) {
                            ToNativeMethodChannel().sendTrackingToNative({
                              "track": "click",
                              "posCode": "069_172_175",
                              "ext": {"source": widget.sence == LabelSelecSence.cableCanvas ? 2 : 1}
                            });
                          } else {
                            ToNativeMethodChannel()
                                .sendTrackingToNative({"track": "click", "posCode": "069_172_175", "ext": {}});
                          }

                          CustomNavigation.gotoNextPage('ScanBarcode', {}).then((result) {
                            /// 获取扫码结果
                            if (result is Map && result['value'] is String && result['value'].isNotEmpty) {
                              /// EasyLoading.show(status: '${intlanguage('app01202', '正在识别')}...');

                              /// 识别标签纸编码
                              ///

                              _controller.searchByLabelBarcode(
                                  keywords: result['value'],
                                  includeVip: _controller.includeVip,
                                  resultClosure: (model) {
                                    EasyLoading.dismiss();
                                    String scanResult = "";
                                    if (model == null || model.list is! List) {
                                      showToast(msg: intlanguage('app01160', '数据请求失败'));
                                      scanResult = "0";
                                    } else if (model.list is List) {
                                      if (model.list!.isEmpty) {
                                        if (_controller.isCable) {
                                          showToast(msg: intlanguage('app100001673', '请使用正品线缆标签纸'));
                                        } else {
                                          showToast(msg: intlanguage('app00321', '请使用正品耗材'));
                                        }
                                        scanResult = "0";
                                      } else {
                                        if (_controller.isNeedPop == false && _controller.onChanged != null) {
                                          /// 回调onChanged且不pop
                                          _controller.onChanged?.call((model.list!.first).rawJson);
                                        } else {
                                          Navigator.pop(context, (model.list!.first).rawJson);
                                        }
                                        scanResult = "1";
                                      }
                                    }
                                    result["result"] = scanResult;
                                    result.remove("value");
                                    ToNativeMethodChannel().sendTrackingToNative(
                                        {"track": "show", "posCode": "002_003_178", "ext": result});
                                  });
                            }
                          });
                        },
                        child: Row(
                          children: [
                            // const SizedBox(
                            //   width: 50,
                            // ),
                            Container(
                              padding: const EdgeInsets.symmetric(vertical: 51),
                              height: 20,
                              width: 0.5,
                              color: const Color(0xFFD9D9D9),
                            ),
                            const SizedBox(
                              width: 15,
                              height: 32,
                            ),
                            const SvgIcon(
                              'assets/images/industry_template/home/<USER>',
                              width: 18,
                              height: 18,
                            ),
                            const SizedBox(
                              width: 16,
                            ),
                          ],
                        )),
                  ],
                ),
              ),
            ),
          ),
          Obx(() {
            return !_controller.connectState.value
                ? Padding(
                    padding: const EdgeInsets.only(bottom: 10),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                      color: ThemeColor.imageBackground,
                      child: Row(
                        children: [
                          const Padding(
                              padding: EdgeInsets.only(right: 3),
                              child: RotatedBox(
                                quarterTurns: 2,
                                child: Icon(
                                  Icons.info_outline,
                                  size: 20,
                                  color: ThemeColor.brand,
                                ),
                              )),
                          Expanded(
                              child: Text(
                            intlanguage('app01272', '网络连接异常，离线使用中'),
                            style: const TextStyle(color: ThemeColor.brand, fontWeight: FontWeight.w400, fontSize: 14),
                          )),
                        ],
                      ),
                    ),
                  )
                : Container();
          }),
          Expanded(
              child: Container(
                  color: ThemeColor.background,
                  child: LabelTemplateCategory(
                    isFromCanvas: widget.isFromCanvas,
                    isCable: widget.isCable,
                  )))
        ],
      ),
    );
  }
}

/// 模版分类列表
class LabelTemplateCategory extends StatefulWidget {
  final bool isFromCanvas;
  final bool isCable;

  LabelTemplateCategory({Key? key, this.isFromCanvas = false, this.isCable = false}) : super(key: key);

  @override
  State<LabelTemplateCategory> createState() => _LabelTemplateCategoryState();
}

class _LabelTemplateCategoryState extends State<LabelTemplateCategory> {
  LabelSelectorController get _controller => Get.find<LabelSelectorController>();

  /// 复制过来的列表结构状态管理凌乱无法改动, 使用终极杀器强刷页面
  Key _key = Key('${DateTime.now().millisecondsSinceEpoch}');

  @override
  void initState() {
    super.initState();
    _controller.addListenerEvent((event) {
      if (event is ResetTemplateState &&
          (event == ResetTemplateState.ResetCategoryListState || event == ResetTemplateState.ResetAllListState)) {
        setState(() {
          _key = Key('${DateTime.now().millisecondsSinceEpoch}');
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      key: _key,
      children: [
        widget.isCable ? Container() : const LabelTemplateExpansiontile(),
        TemplateList(
          isFromCanvas: widget.isFromCanvas,
        )
      ],
    );
  }
}

class LabelTemplateExpansiontile extends StatefulWidget {
  const LabelTemplateExpansiontile({Key? key}) : super(key: key);

  @override
  State<LabelTemplateExpansiontile> createState() => _LabelTemplateExpansiontileState();
}

class _LabelTemplateExpansiontileState extends State<LabelTemplateExpansiontile> {
  /// 选中Index，默认第一个
  int _selectedIndex = 0;

  final _controller = Get.find<LabelSelectorController>();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFF5F5F5),
      width: 110,
      child: Obx(() {
        return ListView.builder(
            itemCount: _controller.categories.length,
            itemBuilder: (context, index) {
              return LabelTemplateCell(
                _controller.categories[index],
                isSelected: _selectedIndex == index,
                onChanged: (item) {
                  if (_selectedIndex == index) {
                    return;
                  }

                  /// 数据埋点
                  try {
                    ToNativeMethodChannel().sendTrackingToNative({
                      "track": "click",
                      "posCode": "069_173",
                      "ext": {'tab_id': '${item.cat.first.id ?? ''}'}
                    });
                  } catch (_) {}

                  setState(() {
                    _selectedIndex = index;
                  });
                },
              );
            });
      }),
    );
  }
}

class LabelTemplateCell extends StatefulWidget {
  final LabelCategoryModel item;

  /// 是否选中
  bool isSelected;

  /// 选中回调
  final ValueChanged<LabelCategoryModel>? onChanged;

  LabelTemplateCell(this.item, {Key? key, this.isSelected = false, this.onChanged}) : super(key: key);

  @override
  State<LabelTemplateCell> createState() => _LabelTemplateCellState();
}

class _LabelTemplateCellState extends State<LabelTemplateCell> with SingleTickerProviderStateMixin {
  /// 是否展开
  bool _isExpanded = false;

  /// 选中Index，默认第一个
  int _selectedIndex = 0;

  late LabelCategoryModel _item;

  /// 小于2个子级不需要展开
  late final bool _isHaveSubItem = _item.cat.length > 1;

  late AnimationController _animationController;

  final _controller = Get.find<LabelSelectorController>();

  @override
  void initState() {
    _item = widget.item;
    _animationController =
        AnimationController(duration: const Duration(milliseconds: 100), lowerBound: 0, upperBound: 0.5, vsync: this);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant LabelTemplateCell oldWidget) {
    if (!widget.isSelected) {
      setState(() {
        _isExpanded = false;
        _selectedIndex = 0;
      });
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// 模版列表项点击，用于选中单个项目或者展开收起子类
  void _tapListAction() {
    // 传递给父级当前的选中Item
    widget.onChanged?.call(_item);
    // 是否拥有子项
    if (_isHaveSubItem) {
      setState(() {
        // 是否展开
        if (_isExpanded) {
          _animationController.reverse(from: 0.5);
        } else {
          _animationController.forward(from: 0);
        }
        _isExpanded = !_isExpanded;
      });
    }

    // 如果是为'我的'
    if (_item.id == _controller.categories.first.id) {
      _controller.getRecommendList();
      _controller.item = null;
    } else {
      // 获取当前分类下的模版数据
      _controller.getListUnderCategory(
        industryId: '${_item.id}',
        categoryId: _item.cat.first.id.toString(),
        machineIdList: _controller.machineIdList,
        includeVip: _controller.includeVip,
      );
      // 当前的选中Item
      _controller.item = _item.cat.first;
      // 重置状态
      _controller.resetState(ResetTemplateState.ResetTemplateListState);
    }
  }

  /// 子类展开项
  Visibility expandedList() {
    return Visibility(
      visible: _isExpanded && _isHaveSubItem,
      child: ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: _item.cat.length,
          itemBuilder: (context, index) {
            return _ExpandListCell(
              item: _item.cat[index],
              isSelected: _selectedIndex == index,
              selectedClosure: (item) {
                setState(() {
                  _selectedIndex = index;
                  widget.onChanged?.call(_item);
                });
              },
            );
          }),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 标题
        DefaultTextStyle(
          style: _isExpanded
              ? const TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: Color(0xFFFB4B42))
              : widget.isSelected
                  ? const TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: Color(0xFFFB4B42))
                  : const TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: Color(0xFF595959)),
          child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: _tapListAction,
            child: Container(
              color: widget.isSelected && !_isHaveSubItem ? Colors.white : KColor.content_background,
              child: Row(
                children: [
                  Visibility(
                    child: Image.asset(
                      'assets/images/industry_template/home/<USER>',
                      width: 4,
                      height: 20,
                      matchTextDirection: true,
                    ),
                    visible: widget.isSelected && !_isHaveSubItem,
                    maintainSize: true,
                    maintainAnimation: true,
                    maintainState: true,
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(12, 13, 0, 13),
                      child: Text(widget.item.name ?? ''),
                    ),
                  ),
                  if (_isHaveSubItem)
                    Padding(
                      padding: const EdgeInsetsDirectional.only(end: 21),
                      child: RotationTransition(
                          turns: _animationController,
                          child: Image.asset(
                            'assets/images/industry_template/home/<USER>',
                            color: _isExpanded || widget.isSelected ? const Color(0xFFFB4B42) : null,
                            width: 8,
                            height: 8,
                          )),
                    ),
                ],
              ),
            ),
          ),
        ),
        expandedList()
      ],
    );
  }
}

class _ExpandListCell extends StatefulWidget {
  final Cat item;
  bool isSelected;
  Function(Cat item)? selectedClosure;

  _ExpandListCell({Key? key, required this.item, this.isSelected = false, this.selectedClosure}) : super(key: key);

  @override
  State<_ExpandListCell> createState() => _ExpandListCellState();
}

class _ExpandListCellState extends State<_ExpandListCell> {
  late Cat _item;

  final _controller = Get.find<LabelSelectorController>();

  @override
  void initState() {
    _item = widget.item;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (widget.isSelected) {
          return;
        }

        /// 数据埋点
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "click",
          "posCode": "069_173",
          "ext": {'tab_id': '${_item.id ?? ''}'}
        });

        // 获取当前分类下的模版数据
        _controller.getListUnderCategory(
          industryId: '${_item.parentId}',
          categoryId: _item.id.toString(),
          machineIdList: _controller.machineIdList,
          includeVip: _controller.includeVip,
        );

        _controller.item = _item;
        // 重置状态
        _controller.resetState(ResetTemplateState.ResetTemplateListState);
        setState(() {
          widget.isSelected = true;
          widget.selectedClosure?.call(_item);
        });
      },
      child: Container(
        color: widget.isSelected ? Colors.white : const Color(0xFFF5F5F5),
        child: Row(
          children: [
            Visibility(
              child: Image.asset(
                'assets/images/industry_template/home/<USER>',
                width: 4,
                height: 20,
                matchTextDirection: true,
              ),
              visible: widget.isSelected,
              maintainSize: true,
              maintainAnimation: true,
              maintainState: true,
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 13.0),
                child: Align(
                    child: Text(
                  _item.name ?? '',
                  style: TextStyle(fontWeight: widget.isSelected ? FontWeight.w600 : FontWeight.w400),
                )),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class TemplateList extends StatefulWidget {
  final bool isFromCanvas;

  TemplateList({Key? key, this.isFromCanvas = false}) : super(key: key);

  @override
  State<TemplateList> createState() => _TemplateListState();
}

class _TemplateListState extends State<TemplateList> {
  final _controller = Get.find<LabelSelectorController>();

  /// 模版滑动管理器
  final ScrollController _scrollController = ScrollController();

  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  int page = 1;

  @override
  initState() {
    // 添加通知，重置状态
    _controller.addListenerEvent((event) {
      if (event is ResetTemplateState && event == ResetTemplateState.ResetTemplateListState) {
        try {
          _scrollController.jumpTo(0);
        } catch (_) {}
        _refreshController.refreshCompleted(resetFooterState: true);
        page = 1;
      }
    });
    super.initState();
  }

  _onRefresh() {
    if (_controller.item == null && !_controller.isCable) {
      // 推荐列表刷新
      _controller.getRecommendList(resultClosure: (model) {
        if (model == null) {
          _refreshController.refreshFailed();
        } else {
          _refreshController.refreshCompleted();
        }
      });
    } else if (_controller.item == null && _controller.isCable) {
      _refreshController.refreshCompleted();
    } else {
      // 刷新当前分类下的模版数据
      _controller.getListUnderCategory(
          industryId: _controller.item!.parentId.toString(),
          categoryId: _controller.item!.id.toString(),
          machineIdList: _controller.machineIdList,
          includeVip: _controller.includeVip,
          resultClosure: (model) {
            if (model == null) {
              _refreshController.refreshFailed();
            } else {
              _refreshController.refreshCompleted(resetFooterState: true);
            }
          });
    }
    // 重置状态
    _controller.resetState(ResetTemplateState.ResetTemplateListState);
  }

  _onLoading() {
    if (_controller.item == null) {
      _refreshController.loadNoData();
      return;
    }
    // 拉取新一页的page
    _controller.getListUnderCategory(
        page: page += 1,
        industryId: _controller.item!.parentId.toString(),
        categoryId: _controller.item!.id.toString(),
        machineIdList: _controller.machineIdList,
        includeVip: _controller.includeVip,
        isMoreData: true,
        resultClosure: (model) {
          if (model == null) {
            _refreshController.loadFailed();
          } else if ((model.list ?? []).isEmpty) {
            _refreshController.loadNoData();
          } else {
            _refreshController.loadComplete();
          }
        });
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Expanded(
          child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: SmartRefresher(
          enablePullUp: true,
          controller: _refreshController,
          onRefresh: _onRefresh,
          onLoading: _onLoading,
          header: const ClassicHeader(
            idleText: '',
            refreshingText: '',
            releaseText: '',
            completeText: '',
          ),
          footer: const ClassicFooter(
            idleText: '',
            canLoadingText: '',
            loadingText: '',
            noDataText: '',
            failedText: '',
          ),
          child: _controller.listOfCategory.value == null || _controller.listOfCategory.value!.isEmpty
              ? Container(
                  padding: const EdgeInsets.only(bottom: 180),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        ImageUtils.getImgPath('no_data'),
                        fit: BoxFit.contain,
                      ),
                      Text(
                        intlanguage('app100000515', '暂无相关结果'),
                        style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
                      ),
                    ],
                  ))
              : ListView.separated(
                  controller: _scrollController,
                  itemCount: _controller.listOfCategory.value!.length,
                  itemBuilder: (context, index) {
                    Item mode = _controller.listOfCategory.value![index];
                    if (mode.id == _controller.id4PrinterState) {
                      return PrinterStateCell(
                        key: GlobalKey(),
                        isFromCanvas: widget.isFromCanvas,
                      );
                    }
                    return GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          _controller.clickNoRepeat(() {
                            Item item = _controller.listOfCategory.value![index] as Item;

                            /// 数据埋点
                            ToNativeMethodChannel().sendTrackingToNative({
                              "track": "click",
                              "posCode": "069_174_176",
                              "ext": {
                                'barcode': item.barcode ?? '',
                                'pos': index + 1,
                                'tab_id': '${_controller.item?.id ?? ''}',
                                'is_vip': (item.vip == true) ? 1 : 0,
                              }
                            });

                            if (_controller.isNeedPop == false && _controller.onChanged != null) {
                              /// 回调onChanged且不pop
                              _controller.onChanged?.call(item.rawJson);
                            } else {
                              Navigator.of(context).pop(item.rawJson);
                            }
                          });
                        },
                        child: TemplateListCell(
                            model: _controller.listOfCategory.value![index],
                            isUsedLabel: _controller.item == null ? true : false,
                            isFromCanvas: widget.isFromCanvas));
                  },
                  separatorBuilder: (context, index) {
                    return const SizedBox(
                      height: 12,
                    );
                  },
                ),
        ),
      ));
    });
  }
}

class TemplateListCell extends StatelessWidget {
  final Item model;

  /// 是否是用户历史记录中使用过的标签纸
  final bool isUsedLabel;

  final bool isFromCanvas;

  const TemplateListCell({Key? key, required this.model, required this.isUsedLabel, required this.isFromCanvas})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 190,
      decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFEBEBEB), width: 0.5), borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Stack(
              fit: StackFit.expand,
              children: [
                Container(
                  decoration: const BoxDecoration(
                      color: Color(0xFFF7F7F7),
                      borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12))),
                ),
                Positioned(
                    top: 16,
                    left: 37,
                    right: 37,
                    bottom: 16,
                    child: CacheImageUtil().netCacheImage(
                        imageUrl: model.thumbnail ?? '',
                        errorWidget: ThemeWidget.placeholder(
                            backgroundColor: Colors.transparent, padding: const EdgeInsets.all(20)))),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(10, 10, 10, 0),
                child: Text(model.getDisplayName(),
                    style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w600, color: Color(0xFF262626))),
              ),
              const SizedBox(height: 2),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(10, 0, 0, 0),
                    child: Text(
                      '${model.width}x${model.height}mm',
                      style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFF999999)),
                    ),
                  ),
                  CustomPopupMenuButton(
                    itemBuilder: (BuildContext context) {
                      return [
                        CustomPopupMenuItem(
                          height: 5,
                          child: Row(
                            children: [
                              const SvgIcon(
                                'assets/images/label_create/info.svg',
                                width: 21,
                                height: 21,
                              ),
                              SizedBox(
                                width: 6,
                              ),
                              Text(
                                intlanguage('app100000720', '标签纸详情'),
                                style: const TextStyle(
                                  color: ThemeColor.title,
                                  fontSize: 17,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                          value: "label",
                        )
                      ];
                    },
                    child: Container(
                      padding: const EdgeInsets.fromLTRB(20, 10, 10, 5),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.white,
                      ),
                      child: Icon(
                        Icons.more_horiz,
                        size: 19,
                      ),
                    ),
                    offset: Offset(Directionality.of(context) == TextDirection.ltr ? -10 : 10, 30),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    onSelected: (value) {
                      ToNativeMethodChannel().sendTrackingToNative({
                        "track": "click",
                        "posCode": "069_174_198",
                      });
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        barrierColor: Colors.transparent,
                        shape: const RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
                        builder: (BuildContext context) {
                          return LabelDetailsPage(
                            model,
                            shopSource: isFromCanvas ? 'print_021' : '',
                          );
                        },
                      ).then((value) {
                        if (value != null && value is Item) {
                          final _controller = Get.find<LabelSelectorController>();
                          if (_controller.isNeedPop == false && _controller.onChanged != null) {
                            /// 回调onChanged且不pop
                            _controller.onChanged?.call(value.rawJson);
                          } else {
                            Navigator.of(context).pop(value.rawJson);
                          }
                        }
                      });
                    },
                    onOpened: () {
                      ToNativeMethodChannel().sendTrackingToNative({
                        "track": "click",
                        "posCode": "069_174_196",
                      });
                    },
                  )
                ],
              )
            ],
          ),
        ],
      ),
    );
  }
}

class PrinterStateCell extends StatelessWidget {
  final bool isFromCanvas;

  PrinterStateCell({Key? key, this.isFromCanvas = false}) : super(key: key);

  LabelSelectorController get _controller => Get.find<LabelSelectorController>();

  @override
  Widget build(BuildContext context) {
    // Log.d('_controller.printerConnectState ${_controller.printerConnectState}');
    if (_controller.printerConnectState == PrinterConnectState.disconnected) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        padding: const EdgeInsets.symmetric(vertical: 20),
        width: 264,
        decoration: BoxDecoration(
            color: ThemeColor.greyBackground,
            border: Border.all(color: ThemeColor.border, width: 0.5),
            borderRadius: const BorderRadius.all(Radius.circular(12))),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SvgIcon('assets/images/industry_template/replace_label/printer.svg'),
              const SizedBox(
                height: 10,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Text(
                  intlanguage('app100000490', '识别打印机内标签纸'),
                  style: const TextStyle(color: ThemeColor.title, fontSize: 13, fontWeight: FontWeight.w600),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(
                height: 26,
              ),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  /// 数据埋点
                  ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "069_173_177", "ext": {}});

                  CustomNavigation.gotoNextPage('DeviceConnectPage', {"fromCanvasSelectLabel": isFromCanvas});
                },
                child: Container(
                  decoration: const BoxDecoration(
                      color: ThemeColor.background, borderRadius: BorderRadius.all(Radius.circular(15))),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6.0),
                    child: Text(
                      intlanguage('app01096', '连接打印机'),
                      style: const TextStyle(color: ThemeColor.brand, fontSize: 13, fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      );
    } else if (_controller.printerConnectState == PrinterConnectState.connected ||
        _controller.printerConnectState == PrinterConnectState.cannotIdentifyRfid) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        padding: const EdgeInsets.symmetric(vertical: 20),
        width: 264,
        decoration: BoxDecoration(
            color: ThemeColor.greyBackground,
            border: Border.all(color: ThemeColor.border, width: 0.5),
            borderRadius: const BorderRadius.all(Radius.circular(12))),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SvgIcon('assets/images/industry_template/replace_label/label_unidentified.svg'),
              const SizedBox(
                height: 16,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Text(
                  intlanguage('app100000506', '未识别到标签纸'),
                  style: const TextStyle(color: ThemeColor.title, fontSize: 13, fontWeight: FontWeight.w600),
                  textAlign: TextAlign.center,
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(18, 8, 18, 0),
                child: Text(
                  _controller.printerConnectState == PrinterConnectState.connected
                      ? intlanguage('app100000532', '请检查标签纸是否安装正确，且为精臣可识别标签纸。')
                      : intlanguage('app100000583', '当前打印机不具有自动识别功能，请选择一张标签纸。'),
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: ThemeColor.subtitle, fontSize: 13, fontWeight: FontWeight.w400),
                ),
              )
            ],
          ),
        ),
      );
    } else if (_controller.printerConnectState == PrinterConnectState.rfidIdentified) {
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          if (_controller.isNeedPop == false && _controller.onChanged != null) {
            /// 回调onChanged且不pop
            _controller.onChanged?.call(_controller.labelIdentifiedItem!.rawJson);
          } else {
            Navigator.pop(context, _controller.labelIdentifiedItem!.rawJson);
          }
        },
        child: TemplateListCell(
          model: _controller.labelIdentifiedItem!,
          isUsedLabel: true,
          isFromCanvas: isFromCanvas,
        ),
      );
    }
    return Container();
  }
}
