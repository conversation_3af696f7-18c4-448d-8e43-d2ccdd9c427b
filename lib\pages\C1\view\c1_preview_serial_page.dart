import 'dart:convert';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:netal_plugin/netal_plugin.dart';
import 'package:netal_plugin/niimbot_netal.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:text/pages/C1/model/template_data_transform.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/theme_color.dart';

class C1PreviewSerialPage extends StatefulWidget {
  final List<TemplateData> previewTemplateList;

  const C1PreviewSerialPage({required this.previewTemplateList});

  @override
  State<C1PreviewSerialPage> createState() => _C1PreviewSerialPageState();
}

class _C1PreviewSerialPageState extends State<C1PreviewSerialPage> {
  Map<int, NetalImageResult> imageCacheMap = {};
  List<NetalImageResult> imageList = [];
  int pageSize = 20;
  int currentPage = 0;
  RefreshController refreshController = RefreshController();
  late bool isInit;

  @override
  void initState() {
    super.initState();
    isInit = true;
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _onRefresh(setFlag: () {
        isInit = false;
      });
    });
  }

  @override
  void dispose() {
    super.dispose();
    imageList.clear();
    imageCacheMap.clear();
    widget.previewTemplateList.clear();
  }

  @override
  Widget build(BuildContext context) {
    double height = MediaQuery.of(context).size.height - MediaQuery.of(context).viewPadding.top - 52;
    double listHeight = height - MediaQuery.of(context).viewPadding.bottom - 62;
    return ClipRRect(
        borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0)),
        child: Container(
            height: height,
            padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewPadding.bottom),
            color: ThemeColor.COLOR_F5F5F5,
            child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              _barWidget(),
              // SizedBox(
              //   height: 16,
              // ),
              // Container(
              //     height: listHeight,
              //     child: Padding(
              //       padding: EdgeInsets.all(16),
              //       child: ListView.separated(
              //         itemBuilder: (context, index) {
              //           return _previewWidget(index, widget.previewTemplateList[index]);
              //         },
              //         separatorBuilder: (context, index) {
              //           return SizedBox(height: 16);
              //         },
              //         itemCount: widget.previewTemplateList.length,
              //         shrinkWrap: true,
              //       ),
              //     )),
              if (!isInit)
                Expanded(
                    child: SmartRefresher(
                  controller: refreshController,
                  enablePullDown: true,
                  enablePullUp: true,
                  onRefresh: () => _onRefresh(),
                  onLoading: _onLoadMore,
                  child: ListView.separated(
                    itemBuilder: (context, index) {
                      return Container(
                        padding: EdgeInsetsDirectional.only(
                            start: 16,
                            top: index == 0 ? 16 : 0,
                            end: 16,
                            bottom: index == imageList.length - 1 ? 16 : 0),
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Image.memory(
                            imageList[index].pixels,
                            scale: TemplateDataTransform.devicePixelRatio,
                            fit: BoxFit.none,
                            gaplessPlayback: true,
                          ),
                        ),
                      );
                    },
                    separatorBuilder: (context, index) {
                      return SizedBox(height: 16);
                    },
                    itemCount: imageList.length,
                    shrinkWrap: true,
                  ),
                ))
            ])));
  }

  Widget _barWidget() {
    return Container(
        color: Colors.white,
        height: 46,
        child: Row(
          children: [
            Container(
              padding: Directionality.of(context) == TextDirection.rtl
                  ? EdgeInsets.only(right: 16)
                  : EdgeInsets.only(left: 16),
              width: 80,
              child: Align(
                alignment:
                    Directionality.of(context) == TextDirection.rtl ? Alignment.centerRight : Alignment.centerLeft,
                child: GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Text(intlanguage('app01584', '关闭'),
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: ThemeColor.COLOR_262626)),
                ),
              ),
            ),
            Expanded(
                child: Center(
              child: Text(
                  intlanguage('app100001702', '预览（共\$段）', param: [widget.previewTemplateList.length.toString()]),
                  style: TextStyle(fontSize: 17, fontWeight: FontWeight.w600, color: Colors.black),
                  textAlign: TextAlign.center),
            )),
            const SizedBox(
              width: 80,
            ),
          ],
        ));
  }

  Widget _previewWidget(int index, TemplateData templateData) {
    NetalImageResult imageResult;
    if (imageCacheMap.containsKey(index)) {
      imageResult = imageCacheMap[index]!;
    } else {
      List<TextElement> textElements = templateData.elements.map((e) => e as TextElement).toList();
      imageResult = NetalPlugin().generatePreview(
          size: Size(templateData.width.toDouble(), templateData.height.toDouble()),
          elements: textElements.map((e) => e.toNetal().copyWith(value: e.value)).toList(),
          ratio: TemplateDataTransform.pxRatio);
      imageCacheMap[index] = imageResult;
    }
    return Container(
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Image.memory(
          imageResult.pixels,
          scale: TemplateDataTransform.devicePixelRatio,
          fit: BoxFit.none,
          gaplessPlayback: true,
        ),
      ),
    );
  }

  _onRefresh({Function? setFlag}) async {
    refreshController.footerMode?.value = LoadStatus.idle;
    List<NetalImageResult> pageList = [];
    for (int index = 0; index < min(pageSize, widget.previewTemplateList.length); index++) {
      if (imageCacheMap.containsKey(index)) {
        pageList.add(imageCacheMap[index]!);
      } else {
        TemplateData templateData = widget.previewTemplateList[index];
        List<TextElement> textElements = templateData.elements.map((e) => e as TextElement).toList();
        // 遍历元素，设置文本元素以及竖排文本的height和Y值
        List<Map<String, dynamic>> elementJsons = textElements.map((element) {
          // 竖排时设置宽度为管长｜｜100防止换行的情况，调整后更新Y轴的坐标，图像库默认会根据具体文本进行实际的长度裁剪，横排则不变
          num height = element.height < templateData.width ? templateData.width : 100;
          num y = element.typesettingMode == NetalTypesettingMode.vertical
              ? -(height - element.height) / 2 + element.y
              : element.y;
          num elementHeight = element.typesettingMode == NetalTypesettingMode.vertical ? height : element.height;
          // 竖排下预览默认是居中的
          NetalTextAlign textAlignVertical = element.typesettingMode == NetalTypesettingMode.vertical
              ? NetalTextAlign.center
              : element.textAlignVertical;
          TextElement newElement = element.copyWith(y: y, height: elementHeight, textAlignVertical: textAlignVertical);
          Map<String, dynamic> json = newElement.toNetal().copyWith(value: newElement.value).toJson();
          json['lineMode'] = 7;
          return json;
        }).toList();
        Color? elementColor = TemplateDataTransform.ribbonColor;
        String localBackgroundImageUrl = '';
        try {
          FileResponse response = await DefaultCacheManager().getImageFile(templateData.backgroundImage).first;
          if (response is FileInfo) {
            localBackgroundImageUrl = response.file.path;
          }
        } catch (e) {
          localBackgroundImageUrl = '';
        }
        NetalImageResult imageResult = NiimbotNetal.generateImageFromPrintJson(
          jsonString: jsonEncode({
            'width': templateData.width.toDouble(),
            'height': templateData.height.toDouble(),
            'usedFonts': NetalPlugin().getUsedFonts(),
            'elements': elementJsons,
            'backgroundImage': '',
            'localBackgroundImageUrl': localBackgroundImageUrl
          }),
          ratio: TemplateDataTransform.pxRatio,
          printRatio: TemplateDataTransform.pxRatio,
          printerMargin: [0, 0, 0, 0],
          printerOffset: [0, 0],
          orientation: 0,
          color: elementColor,
        );
        imageCacheMap[index] = imageResult;
        pageList.add(imageResult);
      }
    }
    currentPage = 1;
    imageList.clear();
    imageList.addAll(pageList);
    refreshController.refreshCompleted();
    setFlag?.call();
    setState(() {});
  }

  _onLoadMore() async {
    List<NetalImageResult> pageList = [];
    for (int index = currentPage * pageSize;
        index < min((currentPage + 1) * pageSize, widget.previewTemplateList.length);
        index++) {
      if (imageCacheMap.containsKey(index)) {
        pageList.add(imageCacheMap[index]!);
      } else {
        TemplateData templateData = widget.previewTemplateList[index];
        List<TextElement> textElements = templateData.elements.map((e) => e as TextElement).toList();
        // 遍历元素，设置文本元素以及竖排文本的height和Y值
        List<Map<String, dynamic>> elementJsons = textElements.map((element) {
          // 竖排时设置宽度为管长｜｜100防止换行的情况，调整后更新Y轴的坐标，图像库默认会根据具体文本进行实际的长度裁剪，横排则不变
          num height = element.height < templateData.width ? templateData.width : 100;
          num y = element.typesettingMode == NetalTypesettingMode.vertical
              ? -(height - element.height) / 2 + element.y
              : element.y;
          num elementHeight = element.typesettingMode == NetalTypesettingMode.vertical ? height : element.height;
          // 竖排下预览默认是居中的
          NetalTextAlign textAlignVertical = element.typesettingMode == NetalTypesettingMode.vertical
              ? NetalTextAlign.center
              : element.textAlignVertical;
          TextElement newElement = element.copyWith(y: y, height: elementHeight, textAlignVertical: textAlignVertical);
          Map<String, dynamic> json = newElement.toNetal().copyWith(value: newElement.value).toJson();
          json['lineMode'] = 7;
          return json;
        }).toList();
        Color? elementColor = TemplateDataTransform.ribbonColor;
        String localBackgroundImageUrl = '';
        try {
          FileResponse response = await DefaultCacheManager().getImageFile(templateData.backgroundImage).first;
          if (response is FileInfo) {
            localBackgroundImageUrl = response.file.path;
          }
        } catch (e) {
          localBackgroundImageUrl = '';
        }
        NetalImageResult imageResult = NiimbotNetal.generateImageFromPrintJson(
          jsonString: jsonEncode({
            'width': templateData.width.toDouble(),
            'height': templateData.height.toDouble(),
            'usedFonts': NetalPlugin().getUsedFonts(),
            'elements': elementJsons,
            'backgroundImage': '',
            'localBackgroundImageUrl': localBackgroundImageUrl
          }),
          ratio: TemplateDataTransform.pxRatio,
          printRatio: TemplateDataTransform.pxRatio,
          printerMargin: [0, 0, 0, 0],
          printerOffset: [0, 0],
          orientation: 0,
          color: elementColor,
        );
        imageCacheMap[index] = imageResult;
        pageList.add(imageResult);
      }
    }
    currentPage++;
    imageList.addAll(pageList);
    bool hasMore = false;
    if (imageList.length < widget.previewTemplateList.length) {
      hasMore = true;
    }
    if (hasMore) {
      refreshController.loadComplete();
    } else {
      refreshController.loadNoData();
    }
    setState(() {});
  }
}
