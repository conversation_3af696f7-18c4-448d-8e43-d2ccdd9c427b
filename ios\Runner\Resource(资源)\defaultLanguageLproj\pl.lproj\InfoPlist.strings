/* 
  InfoPlist.strings
  XYFrameWork

  Created by zhaohu on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "Ta aplikacja wymaga dostępu do Twojego aparatu w celu skanowania kodów kreskowych, rozpoznawania tekstu i robienia zdjęć. <PERSON>zy zezwala<PERSON> na dostęp do aparatu?";
NSBluetoothPeripheralUsageDescription = "Ta aplikacja wymaga dostępu do Bluetooth w celu połączenia z drukarką. Czy zezwalasz na dostęp do Bluetooth?";
NSBluetoothAlwaysUsageDescription = "Ta aplikacja wymaga dostępu do Bluetooth w celu połączenia z drukarką. Czy zezwalasz na dostęp do Bluetooth?";
NSContactsUsageDescription = "Ta aplikacja wymaga dostępu do Twoich kontaktów w celu odczytu. Czy zezwalasz na dostęp do książki adresowej?";
NSMicrophoneUsageDescription = "Ta aplikacja wymaga dostępu do Twojego mikrofonu w celu rozpoznawania mowy. Czy zezwalasz na dostęp do mikrofonu?";
NSPhotoLibraryUsageDescription = "To uprawnienie jest używane do drukowania grafik, rozpoznawania kodów kreskowych, kodów QR, rozpoznawania tekstu, ustawiania niestandardowych awatarów itp. Prosimy o „zezwolenie na dostęp do wszystkich zdjęć”, aby zapewnić prawidłowy dostęp do galerii w NIIMBOT. Jeśli wybierzesz „Wybierz zdjęcia...”, wszystkie niezaznaczone oraz przyszłe nowe zdjęcia nie będą dostępne w NIIMBOT.";
NSLocationWhenInUseUsageDescription = "Aby ułatwić korzystanie z pobliskich sieci Wi-Fi, NIIMBOT prosi o uprawnienia lokalizacji.";
NSLocationAlwaysUsageDescription = "Aby ułatwić korzystanie z pobliskich sieci Wi-Fi, NIIMBOT prosi o uprawnienia lokalizacji.";
NSLocationAlwaysAndWhenInUseUsageDescription = "Aby ułatwić korzystanie z pobliskich sieci Wi-Fi, NIIMBOT prosi o uprawnienia lokalizacji.";
NSSpeechRecognitionUsageDescription = "Ta aplikacja wymaga Twojej zgody na dostęp do rozpoznawania mowy. Czy zezwalasz na dostęp do rozpoznawania mowy?";
"UILaunchStoryboardName" = "LaunchScreen";
