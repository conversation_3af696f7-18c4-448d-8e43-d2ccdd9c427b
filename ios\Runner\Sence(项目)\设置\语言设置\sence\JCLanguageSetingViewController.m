//
//  JCLanguageSetingViewController.m
//  Runner
//
//  Created by huzi_0118 on 2018/3/29.
//  Copyright © 2018年 xiaoyao. All rights reserved.
//

#import "JCLanguageSetingViewController.h"
#import "JCLanguageWaittingController.h"
#import "XYTitleCell2.h"
#import "JCLanguageModel.h"
#import "JCLanguageDetailModel.h"
#import "JCAdTool.h"
#import <HoundAnalyticsSDK/SensorsAnalyticsSDK.h>
#import "JCHorizontalButton.h"
# import "JPUSHService.h"
// iOS10 注册 APNs 所需头文件
# ifdef NSFoundationVersionNumber_iOS_9_x_Max
# import <UserNotifications/UserNotifications.h>
# endif
#import <PushKit/PushKit.h>
@interface JCLanguageSetingViewController ()<GroupShadowTableViewDelegate,GroupShadowTableViewDataSource,UIGestureRecognizerDelegate>
@property (nonatomic, copy) NSString *currentLanguage;
@property (nonatomic, strong) NSMutableArray <JCLanguageModel *> *languageModelArr;
@end

@implementation JCLanguageSetingViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.title = XY_LANGUAGE_TITLE_NAMED(@"app00337",@"语言切换");
    self.navigationController.interactivePopGestureRecognizer.delegate = self;
    self.groupShadowTableView.panGestureRecognizer.delaysTouchesBegan = YES;
}

- (void)initData{
    _currentLanguage = XY_CURRENT_APP_LANGUAGE;
    if([_currentLanguage isEqualToString:@""] || _currentLanguage == nil){
        _currentLanguage = @"zh-cn";
    }else{
        _currentLanguage = [XYTool getNationalLanguageNameFromSystemLanguage:_currentLanguage];
    }
    [self loadData];
    [self initRequest];
}

- (void)initRequest{
    XYWeakSelf;
    NSArray *langArr = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_LANGINFO dicOrModel:[JCLanguageModel class] whereFormat:nil];
    weakSelf.languageModelArr = langArr.mutableCopy;
    if(weakSelf.languageModelArr.count == 0){
        NSString *langBundlePath = [[NSBundle mainBundle] pathForResource:@"LanguageBundle" ofType:@"bundle"];
        NSString *jsonString = [NSString stringWithContentsOfFile:[NSString stringWithFormat:@"%@/languageList.text",langBundlePath] encoding:NSUTF8StringEncoding error:nil];
        NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
        if(jsonData){
            NSArray *arr = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingAllowFragments error:nil];
            NSMutableArray *array = [NSMutableArray array];
            for(NSDictionary *dic in arr){
                JCLanguageModel *m = [[JCLanguageModel alloc] initWithDictionary:dic error:nil];
                [array addObject:m];
            }
            [[JCFMDB shareDatabase] jc_inTransaction:^(BOOL *rollback) {
                if(*rollback){
                    NSLog(@"更新失败");
                }else{
                    NSArray *aaa = [[JCFMDB shareDatabase:DB_DEFAULT] jc_insertTable:TABLE_LANGINFO dicOrModelArray:array];
                    NSLog(@"=====------=======%@",aaa);
                }
            }];
            weakSelf.languageModelArr = array;
        }
        [weakSelf reloadLanguageTable];
    }
    if(weakSelf.languageModelArr.count > 0){
        [weakSelf reloadLanguageTable];
    }
    [weakSelf.groupShadowTableView reloadData];
    NSString *versionComplateString = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];

#pragma JAVA 字体库
    [self java_postWithValues:@{@"version":versionComplateString,@"platformId":@"1",@"cachePolicy":@"2"} ModelType:[JCLanguageModel class] Path:J_template_lang_list hud:@"" Success:^(__kindof YTKBaseRequest *request, NSArray *languageModelArr) {
        weakSelf.languageModelArr = [[NSMutableArray alloc] initWithArray:languageModelArr];
        [[JCFMDB shareDatabase:DB_DEFAULT] jc_deleteAllDataFromTable:TABLE_LANGINFO];
        [[JCFMDB shareDatabase:DB_DEFAULT] jc_insertTable:TABLE_LANGINFO dicOrModelArray:languageModelArr];
        [weakSelf reloadLanguageTable];
    } failure:^(NSString *msg, id model) {
        [MBProgressHUD showToastWithMessageDarkColor:msg];
    }];

}

- (void)reloadLanguageTable{
    [self.groupShadowTableView reloadData];
}

- (void)loadData{
}


- (void) initTableView{
    XYWeakSelf
    [super initTableView];
    CGFloat navHeight = iPhoneX?88:64;
    self.groupShadowTableView.groupShadowDelegate = self;
    self.groupShadowTableView.groupShadowDataSource = self;
    self.groupShadowTableView.showSeparator = YES;
    self.groupShadowTableView.separatorInset = UIEdgeInsetsZero;
    [self.groupShadowTableView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(weakSelf.view).offset(0);
        make.top.equalTo(weakSelf.view).offset(0);
        make.trailing.equalTo(weakSelf.view).offset(0);
        make.height.mas_equalTo(SCREEN_HEIGHT - navHeight);
    }];
    [self.groupShadowTableView registerNib:@"XYTitleCell2"];
}

- (void)changeLanguage{
    [self loadData];
    self.title = XY_LANGUAGE_TITLE_NAMED(@"app00337",@"语言切换");;
    _currentLanguage = XY_CURRENT_APP_LANGUAGE;
    if([_currentLanguage isEqualToString:@""] || _currentLanguage == nil){
        _currentLanguage = @"zh-cn";
    }else{
        _currentLanguage = [XYTool getNationalLanguageNameFromSystemLanguage:_currentLanguage];
    }
    [self.groupShadowTableView reloadData];
}

- (void)languageChange:(id)sender {
    if (self.isViewLoaded && !self.view.window) {
        self.view = nil;
    }
}

- (void)changeLanguage:(NSString *)languageString {
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
    hud.label.text = XY_LANGUAGE_TITLE_NAMED(@"app00405", @"正在切换语言");
    [NSBundle setCusLanguage:languageString];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [hud hideAnimated:YES];
        [JCAdTool getAdvertisement];
    });

    [self registrationJpushId];
    [self refreshTabbarBadge];
}

- (void)registrationJpushId {
    XYWeakSelf
    if(xy_isLogin) {
        [JPUSHService registrationIDCompletionHandler:^(int resCode, NSString *registrationID) {
            [weakSelf java_postWithValues:@{@"deviceId":registrationID} ModelType:nil Path:J_mobile_v30_band_device hud:nil Success:^(__kindof YTKBaseRequest *request, id model) {
                [[NSUserDefaults standardUserDefaults] setObject:[NSString stringWithFormat:@"%@-YES", registrationID] forKey:@"jpushRegistrationID"];
                [[NSUserDefaults standardUserDefaults] synchronize];
            } failure:^(NSString *msg, id model) {
                [[NSUserDefaults standardUserDefaults] setObject:[NSString stringWithFormat:@"%@-NO", registrationID] forKey:@"jpushRegistrationID"];
                [[NSUserDefaults standardUserDefaults] synchronize];
            }];

        }];
    }
}

- (void)refreshTabbarBadge {
    if(xy_isLogin){
      [self java_getWithValues:@{} ModelType:nil Path:J_mobile_v30_message_unreadCount hud:nil Success:^(__kindof YTKBaseRequest *request, NSNumber *unreadCount) {
          dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
              [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_MIND_TABBAR_BADGE object:unreadCount];
          });

      } failure:^(NSString *msg, id model) {
      }];
    }
    
}

- (void)currentLanguageString{
    NSString *currLanguage = [NSBundle getCusLanguage];
    NSLog(@"当前语言是=%@", currLanguage);
}
- (void)restoreLanguageAction{

    [self showHUDCompletion:^{
        [NSBundle restoreSysLanguage];
    }];

}
- (void)strTestAction{

    NSString *tes = NSLocalizedString(@"cus_item", nil);
    NSLog(@"测试字符的结果是=%@", tes);
}

- (void)showHUDCompletion:(void (^ __nullable)(void))completion {
    JCLanguageWaittingController *vc=[[JCLanguageWaittingController alloc]init];
    vc.modalPresentationStyle = UIModalPresentationOverCurrentContext;
    vc.view.backgroundColor = [UIColor clearColor];
    vc.view.alpha = 0.5;
    [self presentViewController:vc animated:NO completion:completion];
}

#pragma mark -tableView

- (NSInteger)numberOfSectionsInGroupShadowTableView:(GroupShadowTableView *)tableView
{
    return 1;
}

- (NSInteger)groupShadowTableView:(GroupShadowTableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.languageModelArr.count;
}

- (CGFloat)groupShadowTableView:(GroupShadowTableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 50;
}

- (CGFloat)groupShadowTableView:(GroupShadowTableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return 15.f;
}

- (UIView *)groupShadowTableView:(GroupShadowTableView *)tableView viewForHeaderInSection:(NSInteger)section{
    UIView *headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 15)];
    return headerView;
}

- (UITableViewCell *)groupShadowTableView:(GroupShadowTableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    JCLanguageModel *model = [self.languageModelArr safeObjectAtIndex:indexPath.row];
    XYTitleCell2 *cell = [tableView dequeueReusableCellWithIdentifier:@"XYTitleCell2"];
    [cell showRight:NO topline:NO bottomline:YES jiantou:NO];
    cell.jiantouWidthConstraint.constant = 21;
    cell.jiantouTailingConstrtaint.constant = 15;
    cell.leftTitleWidthConstraint.constant = SCREEN_WIDTH*0.7;
    cell.leftTitle.textColor = XY_HEX_RGB(0x333333);
    cell.leftTitle.font = [UIFont fontWithName:@"PingFang-SC-Medium" size:16];
    cell.jiantouImage.image = XY_IMAGE_NAMED(@"ic_wancheng");
    cell.jiantouImage.hidden = YES;
    if([model.langCode isEqualToString:XY_JC_LANGUAGE_REAL]){
        cell.jiantouImage.hidden = NO;
    }
    cell.leftTitle.text = model.name;
    return cell;
}



- (void)groupShadowTableView:(GroupShadowTableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    JCLanguageModel *model = [self.languageModelArr safeObjectAtIndex:indexPath.row];
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    [[NSUserDefaults standardUserDefaults] setObject:model.langCode forKey:@"appLanguage"];
    [[NSUserDefaults standardUserDefaults] synchronize];
    [self swichLanguage:model.langCode];
    [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_LANGUAGECHANGECLICK object:@{@"code":model.langCode ?: @""}];
}

- (void)swichLanguage:(NSString *)languageCode{
    XYWeakSelf;
    [[SensorsAnalyticsSDK sharedInstance] setLanguage:languageCode];
    NSString *languageFilePath = [NSString stringWithFormat:@"%@/%@.text",RESOURCE_LANGUAGE_PATH,languageCode];
    if(NETWORK_STATE_ERROR){
        [self refreshLanguageData:languageCode languageFilePath:languageFilePath];
        return;
    }
    NSString *languageCDNUrl = [NSString stringWithFormat:@"%@/%@.json",LanguageInfoCDNURL,languageCode];
    [@{} xy_downLoadFileWithUrlString:languageCDNUrl savePath:languageFilePath tag:@"0" Success:^(__kindof YTKBaseRequest *request, id object) {
        [weakSelf refreshLanguageData:languageCode languageFilePath:languageFilePath];
//        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//            [weakSelf switchLoginPluginLanguage:languageCode];
//        });
    } failure:^(NSString *msg, id object) {
        [weakSelf refreshLanguageData:languageCode languageFilePath:languageFilePath];
    } downloadBlock:^(__kindof YTKBaseRequest *request) {

    }];

}

-(void)refreshLanguageData:(NSString *)languageCode languageFilePath:(NSString *)languageFilePath{
    NSFileManager *fileManager=[NSFileManager defaultManager];

    NSString *langBundlePath = [[NSBundle mainBundle] pathForResource:@"LanguageBundle" ofType:@"bundle"];
    NSString *langFileBundlePath = [NSString stringWithFormat:@"%@/%@.text",langBundlePath,languageCode];
    if([fileManager fileExistsAtPath:languageFilePath]){
        [self loadLocalLanguageSourceToSwith:languageFilePath languageCode:languageCode];
    }else{
        if([fileManager fileExistsAtPath:langFileBundlePath]){
            [fileManager copyItemAtPath:langFileBundlePath toPath:languageFilePath error:nil];
            [self loadLocalLanguageSourceToSwith:languageFilePath languageCode:languageCode];
        }
    }
    [self refreshGlobalUIWithLanguageCode:languageCode];
}

-(void)refreshGlobalUIWithLanguageCode:(NSString *)languageCode{
    UISemanticContentAttribute semanticContentAttribute = UIView.appearance.semanticContentAttribute;
    JCHorizontalButton * btn = self.navigationItem.leftBarButtonItem.customView;
    if ([languageCode isEqualToString:@"ar"]) {
        UIView.appearance.semanticContentAttribute = UISemanticContentAttributeForceRightToLeft;
        btn.imageView.left = btn.width - btn.imageView.width;
    } else {
        UIView.appearance.semanticContentAttribute = UISemanticContentAttributeForceLeftToRight;
        btn.imageView.left = 0;
    }
    bool isNeedFresh = semanticContentAttribute != UIView.appearance.semanticContentAttribute;
    if (isNeedFresh) {
        [[NSNotificationCenter defaultCenter] postNotificationName:@"isNeedFreshGLobalAttribute" object:nil];
    }

}

- (void)loadLocalLanguageSourceToSwith:(NSString *)langFileLocalPath languageCode:(NSString *)languageCode{
    NSFileManager *fileManager=[NSFileManager defaultManager];
    NSString *langDataString = [NSString stringWithContentsOfFile:langFileLocalPath encoding:NSUTF8StringEncoding error:nil];
    self.currentLanguage = languageCode;
    JCLanguageDetailModel *languageDModel = [[JCLanguageDetailModel alloc] initWithString:langDataString error:nil];
    NSDictionary *dic = languageDModel.lang;
    [[XYCenter sharedInstance] setLanguageCache:dic];
//    NSString *loginLangFilePath = RESOURCE_LoginPlugin_LANGUAGE_PATH;
//    NSString *localLoginLangResource = [NSString stringWithFormat:@"%@/login_%@.json",loginLangFilePath,languageCode];
//    if([fileManager fileExistsAtPath:localLoginLangResource]){
//        NSString *langDataString = [NSString stringWithContentsOfFile:localLoginLangResource encoding:NSUTF8StringEncoding error:nil];
//        NSDictionary *dic = langDataString.xy_toDictionary;
//        [[XYCenter sharedInstance] setLoginLanguageCache:dic languageType:languageCode];
//    }
    [self changeLanguage:languageCode];
    [self.groupShadowTableView reloadData];
    if(NETWORK_STATE_ERROR) return;
    [[XYCenter sharedInstance] getMyDevicesListRequestWithHub:NO success:^(NSArray *x) {
    } faild:^(NSArray *x) {
    }];
}

- (void)switchLoginPluginLanguage:(NSString *)languageCode{
    NSString *graphqlStr = @"query getLoginLanguagePack { getLoginLanguagePack }";
    [@{} jc_graphQLRequestWith:graphqlStr hud:nil graphQLType:@"query" Success:^(__kindof YTKBaseRequest *request, NSDictionary *loginLangDic) {
        if([loginLangDic isKindOfClass:[NSDictionary class]]){
            NSDictionary *languageDataDic = loginLangDic[@"getLoginLanguagePack"];
            if([languageDataDic isKindOfClass:[NSDictionary class]] && languageDataDic.allKeys.count > 0){
                NSDictionary *detailDic = @{@"languageCode":UN_NIL(languageCode),@"data":languageDataDic};
                NSString *langString = [detailDic xy_toJsonString];
                NSString *langFilePath = RESOURCE_LoginPlugin_LANGUAGE_PATH;
                [XYCenter writeToFile:langFilePath fileName:[NSString stringWithFormat:@"login_%@.json",languageCode] data:langString];
                [[XYCenter sharedInstance] setLoginLanguageCache:detailDic languageType:languageCode];
            }
        }
    } failure:^(NSString *msg, id model) {

    }];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

/*
 #pragma mark - Navigation

 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */


// MARK: - UIGestureRecognizerDelegate
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer
       shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {

    // 允许系统的侧滑返回手势与 UITableView 的滑动手势并存
    if (gestureRecognizer == self.navigationController.interactivePopGestureRecognizer) {
        return YES;
    }
    return NO;
}
@end
