import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:niimbot_template/models/template_data.dart';
// import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:text/application.dart';
import 'package:text/pages/my_template/abstract/my_template_manager_abstract.dart';
import 'package:text/pages/my_template/controller/template_database.dart';
import 'package:text/pages/my_template/controller/template_database_v2.dart';
import 'package:text/pages/my_template/model/folder_model.dart';
import 'package:text/pages/my_template/my_template_state.dart';
import 'package:text/template/constant/template_local_type.dart';
import 'package:text/template/template_manager.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/niimbot_template_db_manager.dart';
import 'package:text/utils/toast_util.dart';

import '../../../template/model/template_operation_status.dart';

class TemplateManagerV2 extends MyTemplateManagerAbstract {
  TemplateDataBaseV2 _db = TemplateDataBaseV2();

  @override
  void getAll(int page, Function(List<TemplateData?> funcation) funcation,
      {int limit = 10,
      int? folderId,
      refresh = true,
      onlyNative = false,
      TemplateOperateState state = TemplateOperateState.normal,
      FolderModel? folderModel,
      Function? errorFuncation}) {
    if (onlyNative || Application.user == null) {
      _db.query(id: folderId.toString(), pageIndex: page, state: state).then((s) {
        funcation.call(s);
      });
      return;
    }
    if (folderModel?.type == 2) {
      if (!Application.networkConnected) {
        _db.query(id: folderId.toString(), pageIndex: page, state: state).then((s) {
          s as List<TemplateData?>;
          funcation.call(s);
        });
      }
    } else {
      _db.query(id: folderId.toString(), pageIndex: page, state: state).then((s) {
        s as List<TemplateData?>;
        if (s.isNotEmpty) {
          funcation.call(s);
        }
      });
    }

    interface.getMyTemplateList(page, limit, folderId!, folderModel: folderModel, (value, deleteData) async {
      if (refresh) {
        // 接口如果第一页无数据 清空缓存 -wy
        if (value.isEmpty && page == 1) {
          _db.deleteTemplatesByFolderId(folderId);
        }
        _db.deleteTemplatesInUpdate(value, folderId, deleteData).then((s) {
          _db.addTemplate(s).then((_) {
            _db.query(id: folderId.toString(), pageIndex: page, state: state).then((dbValue) {
              if (value.length > 0 && value.length != dbValue.length) {
                funcation.call(value);
              } else {
                funcation.call(dbValue);
              }
            });
          });
        });
      } else {
        _db.addTemplate(value).then((_) {
          return _db.query(id: folderId.toString(), pageIndex: page, state: state);
        }).then((t) {
          funcation.call(t);
        });
      }
    }, (status, msg) {
      if (status == 10403 || status == 11404) {
        errorFuncation?.call(status);
      } else {
        _db.query(id: folderId.toString(), pageIndex: page, state: state).then((s) {
          funcation.call(s);
        });
      }
    });
  }

  ///获取离线未登录数据
  getOffUnLoginTemplate() async {
    List<TemplateData> templates = [];
    try {
      templates = await _db.dbQueryOffUnLoginTemplates(batchSize: 500*10);
    } catch (e) {
      templates = [];
    }
    if (templates.isNotEmpty)
      ToNativeMethodChannel().sendTrackingToNative({
        "track": "show",
        "posCode": "007_329",
        'ext': {"offline_temp": templates.length}
      });
  }

  ///批量删除模板
  void deleteTemplateList(List<TemplateData> modelList, Function(bool result) function) {
    if (Application.user == null) {
      _db.batchDelete(modelList).then((value) {
        function.call(true);
      });
      return;
    }
    interface.batchDeleteMyTemplate(modelList, () {
      _db.batchDelete(modelList).then((value) {
        function.call(true);
      });
    }, (status, msg) {
      List<TemplateData> needDelete = [];
      List<TemplateData> needUpdateDelete = [];
      modelList.forEach((element) {
        if (element.local_type == 1) {
          needDelete.add(element);
        } else {
          TemplateData templateData = element.copyWith(local_type: 3);
          needUpdateDelete.add(templateData);
        }
      });
      _db.batchDelete(needDelete).then((_) {
        _db.batchUpdate(needUpdateDelete).then((value) {
          function.call(true);
        });
      });
    });
  }

  ///批量移动模板
  void moveTemplateList(List<TemplateData> modelList, String targetFolderId, Function(bool result) function) {
    interface.batchMoveMyTemplate(modelList, targetFolderId, () {
      _db.batchDelete(modelList).then((value) {
        function.call(true);
      });
    }, (status, msg) {
      function.call(false);
      showToast(msg: msg);
    });
  }

  @override
  void reName(dynamic model, String name, Function function) {
    var templateModel = model as TemplateData;
    int timestamp = DateTime.now().millisecondsSinceEpoch;
    DateTime time = DateTime.fromMillisecondsSinceEpoch(timestamp);
    var updateTime = DateFormat('yyyy-MM-dd HH:mm:ss').format(time);
    int localType;
    if(templateModel.local_type == TemplateLocalType.CREATE){
      localType = TemplateLocalType.CREATE;
    }else{
      localType = TemplateLocalType.UPDATE;
    }
    if(Application.networkConnected && Application.isLogin){
      interface.reFreshTemplateName(templateModel.id!, updateTime, name, () {
        TemplateData templateData = templateModel.copyWith(name: name,local_type: TemplateLocalType.SYNC,profile: templateModel.profile.copyWith(extra: templateModel.profile.extra.copyWith(updateTime: updateTime)));
        _db.updateTemplate([templateData]).then((value) {
          function.call();
        });
      }, () {});
    }else{
      TemplateData templateData = templateModel.copyWith(name: name,local_type: localType,profile: templateModel.profile.copyWith(extra: templateModel.profile.extra.copyWith(updateTime: updateTime)));
      _db.updateTemplate([templateData]).then((value) {
        function.call();
      });
    }

  }

  @override
  void delete(model, Function function) async{
    var templateModel = model as TemplateData;
    //create标记的模版直接删除
    if(templateModel.local_type == TemplateLocalType.CREATE){
      await _db.delete(id: templateModel.id);
      function.call();
      return;
    }

    int timestamp = DateTime.now().millisecondsSinceEpoch;
    DateTime time = DateTime.fromMillisecondsSinceEpoch(timestamp);
    String updateTime = DateFormat('yyyy-MM-dd HH:mm:ss').format(time);
    TemplateData templateData = templateModel.copyWith(local_type: 3,profile: templateModel.profile.copyWith(extra: templateModel.profile.extra.copyWith(updateTime: updateTime)));

    _db.updateTemplate([templateData]).then((value) {
      interface.deleteTemplates(templateData.id!, () {
        _db.delete(id: templateData.id).then((_){
          function.call();
        });
      }, () {
        function.call();
      });
    });
  }

  move(TemplateData model, String folderId, Function function) {
    interface.moveTemplates(folderId, model.id!, () {
      _db.delete(id: model.id).then((value) {
        function.call();
      });
    }, () {});
  }


  Future<bool> copyTemplate(model,Function(TemplateOperationStatus status, TemplateData? template) function) async{
    TemplateData? templateData = await TemplateManager().getTemplateDetail(model.id!,needUpdateDb: true);
    if(templateData == null){
      return false;
    }
    await TemplateManager().copyTemplate(templateData,function);
    return true;
  }
}
