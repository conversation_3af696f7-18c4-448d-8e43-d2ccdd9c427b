import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:nety/exceptions/niimbot_nety_exception.dart';
import 'package:nety/models/index.dart';
import 'package:nety/models/niimbot_print_c1_params.dart';
import 'package:nety/models/niimbot_print_params.dart';
import 'package:nety/models/niimbot_printer.dart';
import 'package:nety/models/niimbot_printer_print_progress_info.dart';
import 'package:nety/models/niimbot_rfid_info.dart';
import 'package:nety/nety.dart';
import 'package:nety/store.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/extensions/change_notifer.dart';
import 'package:niimbot_print_setting_plugin/extensions/niimbot_printer.dart';
import 'package:niimbot_print_setting_plugin/print/c1_print_config.dart';
import 'package:niimbot_print_setting_plugin/print/controller/print_config_controller.dart';
import 'package:niimbot_print_setting_plugin/print/controller/print_content_controller.dart';
import 'package:niimbot_print_setting_plugin/print/print_config.dart';
import 'package:niimbot_print_setting_plugin/print/printer_strategy_manager.dart';
import 'package:niimbot_print_setting_plugin/print/widget/niimbot_print_progress_widget.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart';
import 'package:niimbot_print_strategy/niimbot_print_strategy.dart';
import 'package:niimbot_template/niimbot_template.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';

import '../model/color.dart';
import '../utils/dialog_util.dart';
import 'model/print_state_model.dart';

class PrintManager {
  late PrintSettingLogic printSettingLogic;
  static PrintManager? _instance;

  PrintManager._();

  factory PrintManager() {
    _instance ??= PrintManager._();
    return _instance!;
  }

  ///最后一次打印张数统计
  int lastPrintCount = 0;
  int lastPrintPage = 0;
  int lastPrintTotal = 0;
  bool isPaused = false;

  ///最后一次打印米数统计
  double lastRibbonLength = 0;

  // Avoid self instance

  ///打印颜色
  String printColors = "";

  ///打印日志用
  String uniqueValue = "";

  ///当前识别到的RFID标签信息
  TemplateData? rfidTemplate;

  var _currentConnectedPrintListener;

  //打印checkversion
  printRecordUnique() {
    Uuid uuid = Uuid();
    uniqueValue = uuid.v4(); // 生成随机的 UUID v4
  }

  ///RFID数据源错误页
  int? errorRFIDSourcePage;

  ///获取多语言信息
  String getI18nString(String stringCode, String defaultStr, {List<String>? param}) {
    return printSettingLogic.pageManager.getI18nString?.call(stringCode, defaultStr, param: param) ?? '';
  }

  //获取当前打印场景主题色
  Color getCurrentPrintSettingThemeColor() {
    return printSettingLogic.style.printSettingThemeColor;
  }

  String get uniAppId {
    final id = printSettingLogic.parameter?.customData?["uniAppInfo"]?["uniAppId"];
    return id is String ? id : "";
  }

  ///打印前检查完毕 准备打印
  readyToPrint(PrintSettingLogic printLogic, String uuid) async {
    uniqueValue = uuid;
    errorRFIDSourcePage = null;
    PrinterStrategyManager().lastPrintStrategy = null;
    //生成打印任务checkVersion
    printRecordUnique();
    // 触发打印任务并处理完成后的状态更新
    printSettingLogic = printLogic;
    //添加打印过程状态监控∏
    _addPrintStatusListener();
    //开始提交打印任务
    lastPrintCount = 0;
    lastRibbonLength = 0.0;
    lastPrintPage = 0;
    isPaused = false;
    NiimbotPrinter? connectedPrinter = NiimbotPrintSDK().store.connectedPrinter;
    //提交打印时 打印机断开场景 重新连接
    if (connectedPrinter == null) {
      printLogic.printingStatusReset();
      DialogUtil().showCustomDialog(printSettingLogic.pageManager.context, "", getI18nString("app00324", "请先连接打印机"),
          contentTextStyle: const TextStyle(color: Color((0xFF000000)), fontSize: 16, fontWeight: FontWeight.w600),
          leftFunStr: getI18nString("app00030", "取消"),
          rightFunStr: getI18nString("app00034", "连接"), rightFunCall: () {
        printSettingLogic.printingStatusReset();
        printSettingLogic.openConnectPage();
      },
          leftFunCall: () {},
          withButtonOutline: true,
          rightBackgroundColor: printSettingLogic.style.printSettingThemeColor,
          channel: printSettingLogic.channel);
    } else {
      //获取打印参数
      PrintConfig? printConfig = await PrintConfigController().getPrintConfig(printSettingLogic, connectedPrinter);
      if (printConfig != null) {
        Map<String, dynamic> colorInfo = printSettingLogic.channel.getPaperRibbonColor();
        bool isSupportGray16 =
            PrintConfigController().isSupportCurrentColorModel(NiimbotPrintColorMode.gray16, colorInfo);
        _onPrintCommit(printConfig, colorInfo, isSupportGray16);
      }
    }
  }

  /// 发送打印指令
  _onPrintCommit(PrintConfig printConfig, Map<String, dynamic> colorInfo, bool isSupportGray16) async {
    ///小程序打印埋点
    if (printSettingLogic.parameter!.customData!['uniAppInfo'] != null &&
        (printSettingLogic.parameter!.customData!['uniAppInfo'] as Map).isNotEmpty) {
      Map uniAppInfo = printSettingLogic.parameter!.customData!['uniAppInfo'] as Map;
      printSettingLogic.channel.trackEvent('click', '055_133',
          eventData: {'mpname': uniAppInfo['uniAppName'], 'check_version': uniqueValue});
    }
    //未连接时先连接打印机
    final connectedPrinter = NiimbotPrintSDK().store.connectedPrinter;
    if (connectedPrinter != null) {
      if (printSettingLogic.taskType == PrintTaskType.printNullUi) {
        //无打印进度场景首先弹出透明页面 后开启打印任务 防止页面阻塞 无法点击返回
        Future.delayed(Duration(milliseconds: 300), () {
          Navigator.of(printSettingLogic.pageManager.context).pop();
          debugPrint("无UI打印进度场景 pop1");
        });
        //发起打印任务开始打印
        _toPrint(printConfig, printSettingLogic, isSupportGray16);
      } else {
        if (printSettingLogic.taskType == PrintTaskType.printC1) {
          // 注入logic的context消失弹出打印进度条
          Navigator.of(printSettingLogic.pageManager.context).pop();
        }
        //展示打印进度弹窗
        _showPrintAlert(printConfig, printSettingLogic);
        //发起打印任务开始打印
        _toPrint(printConfig, printSettingLogic, isSupportGray16);
        printSettingLogic.printingStatusReset();
      }
    } else {
      DialogUtil().showCustomDialog(printSettingLogic.pageManager.context, "", getI18nString("app00324", "请先连接打印机"),
          contentTextStyle: const TextStyle(color: Color((0xFF000000)), fontSize: 16, fontWeight: FontWeight.w600),
          leftFunStr: getI18nString("app00030", "取消"),
          rightFunStr: getI18nString("app00034", "连接"), rightFunCall: () {
        printSettingLogic.printingStatusReset();
        printSettingLogic.openConnectPage();
      },
          leftFunCall: () {},
          withButtonOutline: true,
          rightBackgroundColor: printSettingLogic.style.printSettingThemeColor,
          channel: printSettingLogic.channel);
    }
  }

  ///通过SDK设置打印参数 提供二值化数据 开始打印
  _toPrint(PrintConfig printConfig, PrintSettingLogic logic, bool isDeviceLabelSupportGray16) async {
    ///获取打印图片二值化数据
    errorRFIDSourcePage = null;
    buildPrintImage(int index, double multiple) async {
      NetalImageResult image = await PrintContentController()
          .currentPrintTemplateData(printConfig, logic, index, multiple, isDeviceLabelSupportGray16);
      int page = index + (printConfig.printRange?.first ?? 1) - 1;
      RfidShowItem rfidShowItem = RfidRepository().getRfidShowItem(page);
      // if (rfidShowItem.rfidShowType == RFIDShowType.NotMatch) {
      //   if (index == 0) {
      //     NiimbotPrintSDK().store.updatePrintError(10000, NiimbotPrinterPrintStatus.terminated);
      //     Future.delayed(const Duration(milliseconds: 200), () {
      //       NiimbotPrintSDK().printerStopPrint();
      //     });
      //     return null;
      //   } else {
      //     errorRFIDSourcePage ??= index;
      //     return null;
      //   }
      // } else {
      //   return image;
      // }
      return image;
    }

    ///调用SDK打印
    try {
      //取模版处置类型作为打印机设置，批量模板打印取第一个模板
      TemplateData templateData;
      if (logic.taskType == PrintTaskType.batch) {
        if (PrintManager().rfidTemplate == null) {
          templateData = await logic.getBatchPrintTemplateWith(0);
        } else {
          templateData = PrintManager().rfidTemplate!;
        }
      } else {
        templateData = logic.parameter!.templateMoudleNew!;
      }
      List<int>? batchPrintCopiesList;
      //处理批量模板打印 获取模板打印份数数组
      if (logic.parameter!.batchtIds != null && logic.parameter!.batchtIds!.isNotEmpty) {
        if (logic.printData.taskType == PrintTaskType.printJustOne.name) {
          batchPrintCopiesList = [1];
        } else if (logic.parameter!.taskType == PrintTaskType.printC1) {
          batchPrintCopiesList = logic.parameter!.batchtIds!.map((element) {
            return (element["printPage"] as int);
          }).toList();
        } else {
          batchPrintCopiesList = logic.parameter!.batchtIds!.map((element) {
            return (element["printPage"] as int) * printConfig.copies;
          }).toList();
        }
      } else if (logic.isExcelTemplate() && logic.printCopiesMode == PrintCopiesMode.linkCopies) {
        if (logic.printData.taskType == PrintTaskType.printJustOne.name) {
          batchPrintCopiesList = [1];
        } else {
          int start = printConfig.printRange?.first ?? 1;
          int end = printConfig.printRange?.last ?? 1;
          batchPrintCopiesList = [];
          try {
            List<List<String>> rowAndHeaderData = logic.parameter?.templateMoudleNew?.dataSources?[0].rowData ?? [];
            List<TemplateDataSourceRange>? ranges = logic.parameter?.templateMoudleNew?.dataSources?[0].range ?? [];
            if(ranges.isEmpty && logic.parameter?.templateMoudleNew?.dataSourceBindInfo != null){
              ranges = [TemplateDataSourceRange(s: 1, e: logic.parameter!.templateMoudleNew!.dataSourceBindInfo!.total)];
            }
            List<int> rangeIndex = rangeToArr(ranges);
            List<List<String>> result = [];
            for (int index in rangeIndex) {
              if (index >= 0 && index < rowAndHeaderData.length) {
                result.add(rowAndHeaderData[index]);
              }
            }
            for (int i = start; i <= end; i++) {
              int currentCopiesLinkFieldIndex = logic.currentCopiesLinkFieldIndex;
              if (currentCopiesLinkFieldIndex < 0) {
                //如果没有选择关联份数的时候，则默认1份
                batchPrintCopiesList.add(1);
              } else {
                //四舍五入
                String fieldValue = result[i - 1][currentCopiesLinkFieldIndex];
                int count = parseStringToInt(fieldValue);
                batchPrintCopiesList.add(count);
              }
            }
          } catch (e) {
            debugPrint(e.toString());
            batchPrintCopiesList = [];
          }
        }
      }
      //打印页数
      final count = printConfig.printCount;
      //打印份数
      final copies = printConfig.copies;
      debugPrint(
          "currentPrint density:${printConfig.density} printMode:${printConfig.printMode?.value} colorMode:${printConfig.colorMode?.value} qualityMode:${printConfig.qualityMode?.value} paperType:${templateData.paperType}");
      // 根据机型打印
      // 纸张类型采用识别到的标签纸类型，如果未识别则使用模版中的标签纸类型
      NiimbotPrintParams niimbotPrintParams = NiimbotPrintParams(
        printMode: printConfig.printMode,
        colorMode: printConfig.colorMode,
        qualityMode: printConfig.qualityMode,
        paperType:
            PrintManager().rfidTemplate == null ? templateData.paperType : PrintManager().rfidTemplate?.paperType,
        page: count,
        copies: copies,
        batchTemplatePrintCopiesList: batchPrintCopiesList,
        buildPrintImage: buildPrintImage,
        buildRFIDSourceValue: (index) {
          String rfidEpc = logic.parameter?.customData?['rfidEpc'] ?? '';
          if (rfidEpc.isNotEmpty) {
            return rfidEpc;
          }
          int page = index + (printConfig.printRange?.first ?? 1) - 1;
          RfidShowItem rfidShowItem = RfidRepository().getRfidShowItem(page);
          if (rfidShowItem.rfidShowType == RFIDShowType.NotBinding) {
            return '';
          } else {
            return rfidShowItem.rfidContent;
          }
        },
        density: printConfig.density,
      );
      if (printConfig is C1PrintConfig) {
        niimbotPrintParams = NiimbotPrintC1Params(
          printMode: printConfig.printMode,
          colorMode: printConfig.colorMode,
          qualityMode: printConfig.qualityMode,
          paperType: templateData.paperType,
          page: count,
          copies: copies,
          batchTemplatePrintCopiesList: batchPrintCopiesList,
          cutType: printConfig.cutType,
          tubeType: printConfig.tubeType,
          tubeSpecs: printConfig.tubeSpecs,
          isHalfCut: printConfig.isHalfCut,
          cutDepth: printConfig.cutDepth,
          buildPrintImage: buildPrintImage,
          buildCutTypeValue: (index) => printConfig.cutType,
          buildRFIDSourceValue: (index) {
            RfidShowItem rfidShowItem = RfidRepository().getRfidShowItem(index);
            if (rfidShowItem.rfidShowType == RFIDShowType.NotBinding) {
              return '';
            } else {
              return rfidShowItem.rfidContent;
            }
          },
          density: printConfig.density,
        );
      }
      await NiimbotPrintSDK().printerPrint(niimbotPrintParams);
    } on NiimbotNetyException catch (e, s) {
      debugPrintStack(stackTrace: s, label: '打印异常=>');
      printConfig.updatePrintError(e.code);
    } catch (e, s) {
      debugPrintStack(stackTrace: s, label: '打印异常=>');
    }
  }

  int parseStringToInt(String input) {
    final number = double.tryParse(input) ?? 1.0;
    int copies = number.round();
    if(copies < 1){
      copies = 1;
    }
    return min(copies, 999);
  }

  /// 打印进度弹窗
  _showPrintAlert(PrintConfig printConfig, PrintSettingLogic logic) async {
    BuildContext context = logic.pageManager.context;
    logic.channel.setFlutterVCCanSideslip(false);
    showDialog(
        context: context,
        barrierDismissible: false,
        useRootNavigator: false,
        useSafeArea: false,
        barrierColor: KColor.mainTitle.withOpacity(0.35),
        builder: (BuildContext context) {
          bool isComplate = false;
          return PopScope(
            canPop: false,
            child: ChangeNotifierProvider.value(
              value: NiimbotPrintSDK().store,
              child: Selector<NiimbotPrintSDKStore, NiimbotPrinter?>(
                  selector: (_, v) => v.connectedPrinter,
                  shouldRebuild: (prev, next) {
                    //C1打印过程中，堵管异常不用打印插件提示异常，回调到外面处理
                    int errorCode = next?.progressInfo?.errorCode ?? 0;
                    if (logic.taskType == PrintTaskType.printC1 && errorCode == 55) {
                      Navigator.of(context).pop();
                      logic.channel.tubeExceptionCallback();
                      return false;
                    }
                    return true;
                  },
                  builder: (context, printer, child) {
                    return NiimbotPrintProgressWidget(
                      config: printConfig,
                      onClose: () {
                        //取消打印回调
                        Navigator.of(context).pop();
                        if (logic.isShowingRfidReplaceDialog) {
                          Navigator.of(context).pop();
                        }
                        if (logic.taskType == PrintTaskType.printNullUiShowProgress) {
                          Navigator.of(logic.pageManager.context).pop();
                        }
                      },
                      onPrinterContinued: () {
                        //继续打印回调
                        printRecordUnique();
                        if (((logic.parameter!.customData!['uniAppInfo'] ?? {}) as Map).isNotEmpty) {
                          List<NiimbotRFIDInfo>? rfidInfos =
                              NiimbotPrintSDK().store.printPrinter?.consumablesRFID ?? [];
                          String ribbonSerial = '';
                          String paperSerial = '';
                          if (rfidInfos.isNotEmpty) {
                            for (var element in rfidInfos) {
                              if (element.type != 6) {
                                paperSerial = element.uuid;
                              } else {
                                ribbonSerial = element.uuid;
                              }
                            }
                          }
                          logic.channel.trackEvent(
                              'click', logic.taskType == PrintTaskType.miniApp ? '055_133' : '024_067_094', eventData: {
                            'check_version': uniqueValue,
                            'paperSerial': paperSerial,
                            'ribbonSerial': ribbonSerial
                          });
                        }
                      },
                      onPrinterCompleted: () async {
                        //打印完成回调
                        if (isComplate) return;
                        isComplate = true;
                        Navigator.of(context).pop();
                        if (logic.isShowingRfidReplaceDialog) {
                          Navigator.of(context).pop();
                        }
                        if (logic.taskType == PrintTaskType.printNullUiShowProgress) {
                          Navigator.of(logic.pageManager.context).pop();
                        }
                        await NiimbotPrintSDK().printerCompletedPrint();

                        if (logic.taskType == PrintTaskType.miniApp || logic.taskType == PrintTaskType.printC1) {
                          // 判断是否要弹出nps弹窗
                          bool isTriggerNpsPop = await logic.channel.isTriggerMiniProgramNpsPop(uniAppId: uniAppId);
                          if (isTriggerNpsPop && uniAppId.isNotEmpty) {
                            logic.channel.showMiniProgramNps(uniAppId: uniAppId);
                            printSettingLogic.channel.trackEvent("show", "112_401", eventData: {"source": uniAppId});
                          }
                        } else if (logic.taskType != PrintTaskType.miniApp &&
                            logic.taskType != PrintTaskType.printNullUi &&
                            logic.taskType != PrintTaskType.printNullUiShowProgress) {
                          //判断是否要弹出nps弹窗
                          bool isTriggerNpsPop = await logic.channel.isTriggerNpsPop();
                          if (isTriggerNpsPop) {
                            logic.channel.showNps();
                            printSettingLogic.channel.trackEvent("show", "112_401", eventData: {"source": 3});
                          } else {
                            if (Platform.isAndroid) {
                              logic.channel.toMarketRating();
                            }
                          }
                        }
                        // logic.channel.savePrintDeviceLog();
                      },
                    );
                  }),
            ),
          );
        }).then((value) {
      logic.channel.setFlutterVCCanSideslip(true);
    });
  }

  ///打印过程中 打印状态监控
  _addPrintStatusListener() {
    if (_currentConnectedPrintListener != null) {
      _currentConnectedPrintListener.call();
    }
    String deviceName = "";
    _currentConnectedPrintListener = NiimbotPrintSDK()
        .store
        .watch<NiimbotPrintSDKStore, NiimbotPrinter?>((v) => v.connectedPrinter, (oldVal, newVal) async {
      NiimbotPrinterPrintProgressInfo? newProgressInfo = newVal?.progressInfo;
      NiimbotPrinterPrintProgressInfo? oldProgressInfo = oldVal?.progressInfo;
      if (newVal == null) {
        //处理打印机断开场景
        printSettingLogic.printingStatusReset();
        newProgressInfo = oldProgressInfo?.copyWith(status: NiimbotPrinterPrintStatus.terminated);
        debugPrint("打印机已断开");
      }
      if (newVal != null) {
        NiimbotPrinter? currentConnectedPrinte = newVal;
        String hardCode = currentConnectedPrinte.code?.toString() ?? "";
        Map<String, dynamic> deviceInfo =
            await printSettingLogic.channel.getCurrentPrinterInfo(hardCode, currentConnectedPrinte.name ?? "");
        deviceName = deviceInfo["name"] ?? "";
      }
      if (newProgressInfo == null || oldProgressInfo == null) {
        return;
      }
      if (newProgressInfo.status != oldProgressInfo.status) {
        if (newProgressInfo.status == NiimbotPrinterPrintStatus.pausing ||
            newProgressInfo.status == NiimbotPrinterPrintStatus.completed ||
            newProgressInfo.status == NiimbotPrinterPrintStatus.terminated) {
          debugPrint("记录打印记录");
          printSettingLogic.printingStatusReset();
          final errorCode = newProgressInfo.errorCode ?? 0;
          final oldErrorCode = oldProgressInfo.errorCode ?? 0;
          if (errorCode > 0 && errorCode != oldErrorCode) {
            // printSettingLogic.channel.savePrintDeviceLog();
            ///打印暂停及取消埋点
            PrintStateModel? buildStatus = PrintStateModel.buildStatus(
                errorCode: errorCode, isC1Data: printSettingLogic.parameter!.taskType == PrintTaskType.printC1);
            if (newProgressInfo.status == NiimbotPrinterPrintStatus.pausing &&
                (buildStatus!.content ?? "").isNotEmpty) {
              // 使用封装的函数构建埋点参数
              Map<String, dynamic> eventData = PrinterStrategyManager().buildPrintTrackEventData(
                  uniqueValue, NiimbotPrintSDK().store.printPrinter?.consumablesRFID, null, buildStatus.content ?? "");
              eventData["state"] = 0; // 失败状态
              eventData["errorCode"] = errorCode;
              printSettingLogic.channel.trackEvent("print", "001", eventData: eventData);
            }
            if (newProgressInfo.status == NiimbotPrinterPrintStatus.terminated) {
              // 使用封装的函数构建埋点参数
              Map<String, dynamic> eventData = PrinterStrategyManager().buildPrintTrackEventData(
                  uniqueValue, NiimbotPrintSDK().store.printPrinter?.consumablesRFID, null, buildStatus!.title);
              eventData["state"] = 0; // 失败状态
              eventData["errorCode"] = errorCode;
              printSettingLogic.channel.trackEvent("print", "001", eventData: eventData);
            }
          }

          // 增加打印次数
          if (printSettingLogic.taskType == PrintTaskType.miniApp ||
              printSettingLogic.taskType == PrintTaskType.printC1) {
            if (newProgressInfo.status == NiimbotPrinterPrintStatus.completed) {
              /// 打印次数
              printSettingLogic.channel.updateUserMiniProgramPrintCount(1, uniAppId: uniAppId);
            }
          }

          ///无需打印进度场景 暂停或异常 原生交互处理
          if (printSettingLogic.taskType == PrintTaskType.printNullUi ||
              printSettingLogic.taskType == PrintTaskType.miniApp) {
            _unUIPrintStatusChangeScene(errorCode, oldErrorCode, newProgressInfo.copyWith());
            if (newProgressInfo.status == NiimbotPrinterPrintStatus.completed &&
                printSettingLogic.taskType == PrintTaskType.printNullUi) {
              await NiimbotPrintSDK().printerCompletedPrint();
            }
          } else if (printSettingLogic.taskType == PrintTaskType.printNullUiShowProgress) {
            if (newProgressInfo.status == NiimbotPrinterPrintStatus.completed) {
              ///无打印设置页面打印进度场景 打印完成判断
              printSettingLogic.channel.setUnNeedUIPrintComplete(
                  '1', uniAppId, printSettingLogic.parameter?.customData?["taskId"] ?? '', '');
            } else if (newProgressInfo.status == NiimbotPrinterPrintStatus.terminated) {
              if (newProgressInfo.printedPage > 0) {
                printSettingLogic.channel.setUnNeedUIPrintComplete(
                    '0', uniAppId, printSettingLogic.parameter?.customData?["taskId"] ?? '', '');
              }
            }
          }
          debugPrint("记录打印记录" + newProgressInfo.status.name);

          ///上传打印日志
          // if (newProgressInfo.status != NiimbotPrinterPrintStatus.completed &&
          //     newProgressInfo.status != NiimbotPrinterPrintStatus.pausing) {
          //   uploadPrintRecord();
          // }
          if (newProgressInfo.status == NiimbotPrinterPrintStatus.completed ||
              newProgressInfo.status == NiimbotPrinterPrintStatus.pausing ||
              newProgressInfo.status == NiimbotPrinterPrintStatus.terminated) {
            saveRecord();
            uploadPrintRecord();
          }
        }
      } else if ((newProgressInfo.errorCode ?? 0) > 0 && newProgressInfo.errorCode != oldProgressInfo.errorCode) {
        final errorCode = newProgressInfo.errorCode ?? 0;
        final oldErrorCode = oldProgressInfo.errorCode ?? 0;
        if (printSettingLogic.taskType == PrintTaskType.printNullUi ||
            printSettingLogic.taskType == PrintTaskType.miniApp) {
          _unUIPrintStatusChangeScene(errorCode, oldErrorCode, newProgressInfo);
        } else if (printSettingLogic.taskType == PrintTaskType.printNullUiShowProgress) {
          if (newProgressInfo.status == NiimbotPrinterPrintStatus.completed) {
            ///无打印设置页面打印进度场景 打印完成判断
            printSettingLogic.channel
                .setUnNeedUIPrintComplete('1', uniAppId, printSettingLogic.parameter?.customData?["taskId"] ?? '', '');
          } else if (newProgressInfo.status == NiimbotPrinterPrintStatus.terminated) {
            if (newProgressInfo.printedPage > 0) {
              printSettingLogic.channel.setUnNeedUIPrintComplete(
                  '0', uniAppId, printSettingLogic.parameter?.customData?["taskId"] ?? '', '');
            }
          }
        }
      }

      ///记录打印历史
      if (newProgressInfo.printedPage != lastPrintPage) {
        lastPrintPage = newProgressInfo.printedPage;
        var uniqueId = printSettingLogic.parameter!.printHistoryId ?? const Uuid().v4();
        var copies = 0;
        String id = "";
        TemplateData? templateData;
        if (printSettingLogic.taskType == PrintTaskType.batch) {
          copies = printSettingLogic.parameter!
              .batchtIds![newProgressInfo.printedPage + printSettingLogic.printData.pageBegin - 2]["printPage"];
          templateData = printSettingLogic.parameter?.templateMoudleNew;
          if (printSettingLogic.printData.isJustOne) {
            id = printSettingLogic.parameter!.batchtIds![printSettingLogic.pageManager.style.pageIndex]["id"];
          } else {
            id = printSettingLogic
                .parameter!.batchtIds![newProgressInfo.printedPage + printSettingLogic.printData.pageBegin - 2]["id"];
          }
          Map<String, dynamic> detail = await printSettingLogic.channel.getBatchPrintTemplate(id);
          templateData = await TemplateParse.parseFromMap(detail);
        } else {
          int currentPage = newProgressInfo.printedPage + printSettingLogic.printData.pageBegin - 1;
          copies = newProgressInfo.copies;
          templateData = printSettingLogic.parameter!.templateMoudleNew!.copyWith();
          var elements = TemplateGenerate.generateElements(templateData, page: currentPage);
          List<BaseElement> baseElements = [];
          for (var element in elements) {
            if (element is ImageElement) {
              ImageElement imageElement = element.copyWith();
              if (printSettingLogic.parameter?.pdfBindInfo != null &&
                  printSettingLogic.parameter!.pdfBindInfo!.elementIsBindPdf(imageElement.id)) {
                List<String> imagePaths =
                    printSettingLogic.parameter!.pdfBindInfo!.getPDFImagePathsWithElementId(element.id);
                String localImagePath = currentPage - 1 >= imagePaths.length ? "" : imagePaths[currentPage - 1];
                imageElement = imageElement.copyWith(localImageUrl: localImagePath);
                baseElements.add(imageElement);
                debugPrint("PDF打印：当前图片地址：$localImagePath");
              } else {
                baseElements.add(element);
              }
            } else {
              baseElements.add(element);
            }
          }
          templateData.elements.clear();
          templateData.elements.addAll(baseElements);
        }

        if (printSettingLogic.taskType != PrintTaskType.miniApp &&
            printSettingLogic.taskType != PrintTaskType.printC1 &&
            printSettingLogic.taskType != PrintTaskType.printNullUi &&
            printSettingLogic.taskType != PrintTaskType.printNullUiShowProgress) {
          if (printSettingLogic.taskType == PrintTaskType.batch) {
            var count = printSettingLogic.printData.isJustOne ? 1 : (copies * newProgressInfo.copies);
            Map<String, dynamic> batchTemplateData = await printSettingLogic.channel.getBatchPrintTemplate(id);
            TemplateData templateData = await TemplateParse.parseFromMap(
              batchTemplateData,
              parseLocalImageResources: (url) {
                if (printSettingLogic.parameter!.templateMoudle!['localBackground'] != null) {
                  int index = printSettingLogic.parameter!.templateMoudle!['multipleBackIndex'];
                  List localBackground = printSettingLogic.parameter!.templateMoudle!['localBackground'];
                  if (localBackground.isNotEmpty) {
                    return Future.value(localBackground[index]);
                  } else {
                    return Future.value('');
                  }
                } else {
                  return Future.value('');
                }
              },
            );
            debugPrint("--------sls保存打印历史:${printSettingLogic.style.pageIndex + 1}");
            printSettingLogic.channel.saveHistory(deviceName, count.toString(), uniqueId.toString(), true, templateData,
                templateMap: batchTemplateData,
                page: printSettingLogic.style.pageIndex + 1,
                isSupportGray16: printSettingLogic.checkCurrentIsSupportGray16(templateData),
                isPrintHistory: printSettingLogic.taskType == PrintTaskType.printSceneHistory);
          } else {
            debugPrint("--------sls保存打印历史:$lastPrintPage");
            if (lastPrintPage == 1) {
              //小程序商品库批量打印不保存打印历史
              if (printSettingLogic.parameter?.customData?["batchGoodsList"] != null &&
                  printSettingLogic.parameter?.customData?["batchGoodsList"]!.isNotEmpty) {
              } else {
                // log("================saveHistory Template: ${printSettingLogic.parameter!.templateMoudleToJson()}");
                int currentPage = printSettingLogic.parameter!.templateMoudle?["currentPage"] ?? 1;
                printSettingLogic.channel.saveHistory(deviceName, copies.toString(), uniqueId.toString(), false,
                    printSettingLogic.parameter!.templateMoudleNew!,
                    templateMap: printSettingLogic.parameter!.templateMoudle,
                    page: currentPage,
                    isSupportGray16:
                        printSettingLogic.checkCurrentIsSupportGray16(printSettingLogic.parameter!.templateMoudleNew!),
                    isPrintHistory: printSettingLogic.taskType == PrintTaskType.printSceneHistory);
              }
            }
          }
        }
        if (printSettingLogic.taskType != PrintTaskType.printC1) {
          debugPrint("--------sls保存打印内容:$lastPrintPage");
          printSettingLogic.channel.saveContent(templateData, uniqueValue);
        }
        debugPrint("已打印页数:${newProgressInfo.printedPage} RFID数据源错误页码$errorRFIDSourcePage");
        if (newProgressInfo.printedPage == errorRFIDSourcePage) {
          NiimbotPrintSDK().store.updatePrintError(10000, NiimbotPrinterPrintStatus.terminated);
          NiimbotPrintSDK().printerStopPrint();
        }
        if (newProgressInfo.status == NiimbotPrinterPrintStatus.pausing ||
            newProgressInfo.status == NiimbotPrinterPrintStatus.completed ||
            newProgressInfo.status == NiimbotPrinterPrintStatus.terminated) {
          return;
        }
      }
      NiimbotPrinterPrintProgressInfo? progressInfo = NiimbotPrintSDK().store.printPrinter?.progressInfo;
      debugPrint('-------当前已打印张数1:${progressInfo?.printedTotal} 打印状态:${progressInfo?.status}');
      if (progressInfo != null && progressInfo.printedTotal != lastPrintTotal) {
        int total = progressInfo.printedTotal;
        lastPrintTotal = total;
        if (total > 0) {
          // saveRecord();
        }
        debugPrint('已打印张数:${progressInfo.printedTotal} 打印状态:${progressInfo.status}');
      }
    });
  }

  ///无打印进度UI场景，异常中断处理
  _unUIPrintStatusChangeScene(int errorCode, int oldErrorCode, NiimbotPrinterPrintProgressInfo progressInfo) {
    if (errorCode > 0 && errorCode != oldErrorCode && printSettingLogic.taskType == PrintTaskType.printNullUi) {
      PrintStateModel? buildStatus = PrintStateModel.buildStatus(
          errorCode: errorCode, isC1Data: printSettingLogic.taskType == PrintTaskType.printC1);
      Map<String, dynamic> errorInfo = {
        "isPaused": progressInfo.status == NiimbotPrinterPrintStatus.pausing,
        "errorReason": buildStatus!.content ?? buildStatus.title,
        "errorCode": errorCode,
        "uniAppId": printSettingLogic.parameter!.customData!["uniAppInfo"]["uniAppId"] ?? '',
        "taskId": printSettingLogic.parameter?.customData?["taskId"] ?? ''
      };
      // if (printSettingLogic.pageManager.context.mounted) {
      //   Future.delayed(const Duration(milliseconds: 300), () {
      //     Navigator.of(printSettingLogic.pageManager.context).pop();
      //     debugPrint("无UI打印进度场景 pop4");
      //   });
      // }
      printSettingLogic.channel.unUIPrintErrorEventCallback(errorInfo).then((operateType) async {
        if (operateType == 1) {
          //原生点击了 继续打印
          uniqueValue = Uuid().v4();
          if (printSettingLogic.taskType == PrintTaskType.printNullUi) EasyLoading.show();
          final densityType = await PrinterStrategyManager().printCheckRFIDPrintStrategy(printSettingLogic, uniqueValue,
              (languageCode, descrp) {
            return getI18nString(languageCode, descrp);
          });
          int density = printSettingLogic.parameter!.customData?['density'] ?? 3;
          if (printSettingLogic.taskType == PrintTaskType.printNullUi) EasyLoading.dismiss();
          if (densityType != StrategyType.forbid) {
            if (densityType == StrategyType.lowDensity0) {
              density = 0;
            } else if (densityType == StrategyType.lowDensity1) {
              density = -1;
            } else if (densityType == StrategyType.lowDensity2) {
              density = -2;
            }
            final res = await NiimbotPrintSDK().printerContinuePrint(density);
            debugPrint('printerContinuePrint-----$res');
            printSettingLogic.channel.trackEvent("click", "118_264_242",
                eventData: {"error_title": buildStatus.title, "qr_id": printSettingLogic.meetingId});
          }
        } else {
          //原生点击了 继续取消
          printSettingLogic.channel.trackEvent("click", "118_264_241",
              eventData: {"error_title": buildStatus.title, "qr_id": printSettingLogic.meetingId});
          NiimbotPrintSDK().printerCancelPrint();

          ///无需打印进度场景 打印完成判断
          printSettingLogic.channel.setUnNeedUIPrintComplete(
              '0',
              printSettingLogic.parameter!.customData!["uniAppInfo"]["uniAppId"],
              printSettingLogic.parameter?.customData?["taskId"] ?? '',
              buildStatus!.content ?? buildStatus.title);
        }
      });
      printSettingLogic.channel.trackEvent("show", "118_263",
          eventData: {"error_title": buildStatus!.title, "qr_id": printSettingLogic.meetingId});
    } else if (progressInfo.status == NiimbotPrinterPrintStatus.completed) {
      ///无需打印进度场景 打印完成判断
      printSettingLogic.channel.setUnNeedUIPrintComplete(
          '1',
          printSettingLogic.parameter!.customData!["uniAppInfo"]["uniAppId"],
          printSettingLogic.parameter?.customData?["taskId"] ?? '',
          '');
    }
  }

  ///保存打印记录
  void saveRecord() {
    var uniAppInfo = (printSettingLogic.parameter?.customData?["uniAppInfo"] ?? {}) as Map;
    double? printCardPaperLength =
        NiimbotPrintSDK().store.printPrinter?.progressInfo!.ribbonUsed.fold(0.0, (sum, element) => sum! + element) ??
            0.0;
    int printedTotal = NiimbotPrintSDK().store.printPrinter?.progressInfo?.printedTotal ?? 0;
    int printNum = printedTotal;
    debugPrint(
        '-------当前已打印张数2:${NiimbotPrintSDK().store.printPrinter?.progressInfo?.printedTotal} 打印状态:${NiimbotPrintSDK().store.printPrinter?.progressInfo?.status}');
    if (NiimbotPrintSDK().store.printPrinter?.progressInfo?.status == NiimbotPrinterPrintStatus.pausing || isPaused) {
      printNum = printedTotal - lastPrintCount;
      debugPrint("-------当前分数；$printNum 最后一次分数 $lastPrintCount");
    }
    var paperLength = printCardPaperLength - lastRibbonLength;
    debugPrint(
        "-------当前打印==>>paperLength: $paperLength, printCardPaperLength: $printCardPaperLength, lastRibbonLength:$lastRibbonLength");
    final lastPrintStrategyInfo = PrinterStrategyManager().getLastPrintStrategyInfo();
    printSettingLogic.channel.savePrintRecord(
        uniqueValue,
        printSettingLogic.parameter!.templateMoudleNew!,
        printNum,
        NiimbotPrintSDK().store.printPrinter?.progressInfo?.printedTotal ?? 1,
        printSettingLogic.taskType,
        paperLength ?? 0.0,
        NiimbotPrintSDK().store.printPrinter?.progressInfo!.ribbonUsed.fold(0.0, (sum, element) => sum! + element) ??
            0.0,
        uniAppInfo["uniAppId"] ?? "",
        printSettingLogic.isConnected(),
        lastPrintStrategyInfo['name'] ?? '',
        lastPrintStrategyInfo['errorCode'] ?? '',
        () {});

    // 获取当前RFID信息用于埋点
    List<NiimbotRFIDInfo>? currentRfidInfos = NiimbotPrintSDK().store.connectedPrinter?.consumablesRFID;
    // 使用封装的函数构建埋点参数
    Map<String, dynamic> eventData = PrinterStrategyManager().buildPrintTrackEventData(
        uniqueValue,
        currentRfidInfos,
        null, // 这里没有策略结果，传null
        "" // 这里没有禁止原因，传空字符串
        );
    eventData["state"] = 1; // 成功状态
    printSettingLogic.channel.trackEvent("print", "001", eventData: eventData);

    // if (NiimbotPrintSDK().store.printPrinter?.progressInfo?.status == NiimbotPrinterPrintStatus.completed ||
    //     NiimbotPrintSDK().store.printPrinter?.progressInfo?.status == NiimbotPrinterPrintStatus.pausing) {
    //   uploadPrintRecord();
    // }
  }

  ///上传打印记录
  uploadPrintRecord() {
    int printedTotal = NiimbotPrintSDK().store.printPrinter?.progressInfo?.printedTotal ?? 0;
    int printNum = printedTotal;
    double? printCardPaperLength =
        NiimbotPrintSDK().store.printPrinter?.progressInfo!.ribbonUsed.fold(0.0, (sum, element) => sum! + element) ??
            0.0;
    var paperLength = printCardPaperLength;
    if (NiimbotPrintSDK().store.printPrinter?.progressInfo?.status == NiimbotPrinterPrintStatus.pausing || isPaused) {
      printNum = printedTotal - lastPrintCount;
      paperLength = printCardPaperLength - lastRibbonLength;
    }
    //通知打印张数进行累加
    printSettingLogic.channel.updateUserPrintCount(printNum);
    lastPrintCount = NiimbotPrintSDK().store.printPrinter?.progressInfo?.printedTotal ?? 1 - lastPrintCount;
    // lastRibbonLength = printCardPaperLength - lastRibbonLength;
    lastRibbonLength = printCardPaperLength;
    Future.delayed(Duration(milliseconds: 400), () {
      printSettingLogic.channel.uploadPrintDataLog(uniqueValue, () {
        isPaused = true;
        //打印机仍处于连接状态时 更新打印策略
        if (NiimbotPrintSDK().store.connectedPrinter != null) {
          PrinterStrategyManager()
              .getRFIDStrategy(GetSecurityScene.afterPrint, pageuUsed: printNum, ribbonUsed: paperLength.toInt());
        }
      });
    });
  }
}
