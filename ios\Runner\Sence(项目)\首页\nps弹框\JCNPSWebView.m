//
//  JCNPSWebView.m
//  Runner
//
//  Created by ch<PERSON><PERSON> on 2024/3/7.
//

#import "JCNPSWebView.h"
#import "JCNewHomeViewController.h"
#import "JCBluetoothManager+Connect.h"
#import "NBCAPMiniAppManager.h"
@interface JCNPSWebView()<WKUIDelegate,WKNavigationDelegate,WKScriptMessageHandler>

@property (nonatomic, strong)  WKWebViewConfiguration *configuration;

@property (nonatomic,strong)WKWebView * contentWebView;

@end
@implementation JCNPSWebView

-(instancetype)initWithFrame:(CGRect)frame{
    if(self = [super initWithFrame:frame]){
        [self prepareUIWithFrame:frame];
    }
    return self;
}

-(void)prepareUIWithFrame:(CGRect)rect{
    self.configuration = [[WKWebViewConfiguration alloc] init];
    //实例化对象
    self.configuration.userContentController = [WKUserContentController new];
    //接下来我们来进行配置WKWebView的偏好设置WKPreferences:
    WKPreferences *preferences = [WKPreferences new];
    preferences.javaScriptCanOpenWindowsAutomatically = YES;
    self.configuration.preferences = preferences;
    
    self.contentWebView = [[WKWebView alloc]initWithFrame:rect configuration:self.configuration];
    [self.contentWebView setOpaque:NO];
    self.contentWebView.backgroundColor = [UIColor clearColor];
    self.contentWebView.scrollView.backgroundColor = [UIColor clearColor];
    self.contentWebView.navigationDelegate = self;
    self.contentWebView.scrollView.scrollEnabled = YES;
    self.contentWebView.UIDelegate = self;
    
    self.contentWebView.scrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    self.contentWebView.scrollView.bounces = NO;
    
    self.contentWebView.scrollView.showsVerticalScrollIndicator = NO;
    self.contentWebView.scrollView.showsHorizontalScrollIndicator = NO;
    
    [self addSubview:self.contentWebView];
    [self.contentWebView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.bottom.right.equalTo(self);
    }];
    
    [self.configuration.userContentController addScriptMessageHandler:self name:@"webEventTrack"];
    [self.configuration.userContentController addScriptMessageHandler:self name:@"closePopup"];
    
    [self.configuration.userContentController addScriptMessageHandler:self name:@"closeWebview"];
    [self.configuration.userContentController addScriptMessageHandler:self name:@"hiddenNps"];
    
    [self.configuration.userContentController addScriptMessageHandler:self name:@"getClientData"];
    [self.configuration.userContentController addScriptMessageHandler:self name:@"notifyPageLoaded"];
    
}

-(void)loadUrl:(NSString *)url{
    NSURLRequest *req = [NSURLRequest requestWithURL:[NSURL URLWithString:url] cachePolicy:NSURLRequestUseProtocolCachePolicy timeoutInterval:20];
    self.loadUrl = url;
    [self.contentWebView loadRequest:req];
}

-(void)reload{
    [self.contentWebView reload];
}

//-(void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation
//{
//    self.loadSuccess = YES;
//}


- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message
{
    //    message.body  --  Allowed types are NSNumber, NSString, NSDate, NSArray,NSDictionary, and NSNull.
    NSLog(@"body:%@",message.body);
    if ([message.name isEqualToString:@"webEventTrack"]) {//
        NSString *messageInfo = message.body;
        NSDictionary *messageDic = [messageInfo xy_toDictionary];
        if(messageDic.allKeys.count > 0){
            NSString *eventCode = messageDic[@"eventCode"];
            NSString *posCode = messageDic[@"posCode"];
            NSDictionary *eventTitle = messageDic[@"params"];
            if(eventTitle == nil || eventTitle.count == 0){
                eventTitle = @{};
            }
            JC_TrackWithparms(UN_NIL(eventCode),UN_NIL(posCode),eventTitle);
        }
    }else if([message.name isEqualToString:@"closeWebview"]){
        NSString *messageInfo = message.body;
      if([messageInfo isKindOfClass:[NSString class]] && !STR_IS_NIL(messageInfo)){
          NSDictionary *closeNpsInfo = [NSDictionary dictionaryWithNSString:messageInfo];
          if(closeNpsInfo[@"closeType"] != nil ){
            BOOL isCommit = [(NSString *)(closeNpsInfo[@"closeType"]) isEqualToString:@"submit"];
//            NSString *uniAppId = [NBCAPMiniAppManager sharedInstance].currentAppId;
            [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"closeNps" arguments:@{@"closeNps":isCommit?@1:@0,@"uniAppId":self.uniAppId} result:nil];
          }
          [self loadUrl:self.loadUrl];
        }
        if(self.closeClick != nil){
            self.closeClick(@(0));
        }
    }else if([message.name isEqualToString:@"hiddenNps"]){
//        if(self.closeClick != nil){
//            self.closeClick(@(1));
//        }
    }else if([message.name isEqualToString:@"getClientData"]){
        [self sendWebInfo];
    }else if([message.name isEqualToString:@"notifyPageLoaded"]){
        self.loadSuccess = true;
    }else{
        
    }
}

-(void)sendWebInfo{
    NSMutableDictionary * data = [NSMutableDictionary dictionary];
    
    NSString *token = STR_IS_NIL(m_userModel.token)?@"":[NSString stringWithFormat:@"%@ %@",@"bearer",m_userModel.token];
    if(token != nil && token.length > 0){
        [data addEntriesFromDictionary:@{@"token":token}];
    }

    NSString *lastConnectName = [[NSUserDefaults standardUserDefaults] valueForKey:CENTERLASTCONNECTPRINTERNAME];
    if(lastConnectName != nil && [lastConnectName isKindOfClass:[NSString class]]){
        [data addEntriesFromDictionary:@{@"hardwareName":lastConnectName}];
    }
    
    // 小程序id传给H5
//    NSString *uniAppId = [NBCAPMiniAppManager sharedInstance].currentAppId;
    NSString *uniAppId = self.uniAppId;
    if(uniAppId != nil && uniAppId.length > 0){
        [data addEntriesFromDictionary:@{@"uniAppId":uniAppId}];
    }
    
    UIViewController * topVc = [XYTool getCurrentVC];
    if([self.sku isNotEmpty]) {
        [data addEntriesFromDictionary:@{@"pageName":@"vipToast"}];
        [data addEntriesFromDictionary:@{@"productId":self.sku}];
    }else {
        if([topVc isKindOfClass:[JCNewHomeViewController class]]){
            [data addEntriesFromDictionary:@{@"pageName":@"home"}];
        }else{
          [data addEntriesFromDictionary:@{@"pageName":UN_NIL(self.source)}];
        }
    }
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:data options:0 error:nil];
    NSString *dataStr = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    NSString *jsStr = [NSString stringWithFormat:@"clientDataReceived('%@')",dataStr];
//    NSLog(@"上传nps H5交互信息：%@",jsStr);
    [self.contentWebView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSLog(@"%@----%@",result, error);
    }];
}

- (void)setSource:(NSString *)source{
  _source = source;
}
@end
