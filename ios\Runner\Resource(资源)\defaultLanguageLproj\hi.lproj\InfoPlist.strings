/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "यह ऐप बारकोड स्कैनिंग, टेक्स्ट पहचान और फोटो लेने के कार्यों में आपके कैमरे की अनुमतियों तक पहुँच प्राप्त करेगा। क्या आप कैमरा खोलने की अनुमति देते हैं?";
NSBluetoothPeripheralUsageDescription = "यह ऐप प्रिंटर से जुड़ी सेवा में आपके ब्लूटूथ अनुमतियों तक पहुँच प्राप्त करेगा। क्या आप ब्लूटूथ चालू करने की अनुमति देते हैं?";
NSBluetoothAlwaysUsageDescription = "यह ऐप प्रिंटर से जुड़ी सेवा में आपके ब्लूटूथ अनुमतियों तक पहुँच प्राप्त करेगा। क्या आप ब्लूटूथ चालू करने की अनुमति देते हैं?";
NSContactsUsageDescription = "यह ऐप संपर्क पढ़ने की सेवा में आपके पता पुस्तिका अनुमतियों तक पहुँच प्राप्त करेगा। क्या आप पता पुस्तिका खोलने की अनुमति देते हैं?";
NSMicrophoneUsageDescription = "यह ऐप आवाज़ पहचान की सेवा में आपके माइक्रोफ़ोन अनुमतियों तक पहुँच प्राप्त करेगा। क्या आप माइक्रोफ़ोन खोलने की अनुमति देते हैं?";
NSPhotoLibraryUsageDescription = "इस अनुमति का उपयोग चित्र सामग्री, बारकोड पहचान, क्यूआर कोड पहचान, पाठ पहचान, कस्टम अवतार सेट करने और अन्य परिदृश्यों को प्रिंट करने के लिए किया जाएगा। कृपया \"सभी फ़ोटो तक पहुँच की अनुमति दें\" यह सुनिश्चित करने के लिए कि NIIMBOT में एल्बम को सामान्य रूप से एक्सेस किया जा सकता है। यदि आप \"फ़ोटो चुनें...\" का उपयोग करते हैं, तो सभी अचयनित और भविष्य में जोड़े गए फ़ोटो NIIMBOT में एक्सेस नहीं किए जा सकेंगे।";
NSLocationWhenInUseUsageDescription = "आस-पास के वाई-फाई नेटवर्क के आपके उपयोग को सुविधाजनक बनाने के लिए, NIIMBOT आपसे स्थान अनुमतियों के लिए आवेदन करता है";
NSLocationAlwaysUsageDescription = "आस-पास के वाई-फाई नेटवर्क के आपके उपयोग को सुविधाजनक बनाने के लिए, NIIMBOT आपसे स्थान अनुमतियों के लिए आवेदन करता है";
NSLocationAlwaysAndWhenInUseUsageDescription = "आस-पास के वाई-फाई नेटवर्क के आपके उपयोग को सुविधाजनक बनाने के लिए, NIIMBOT आपसे स्थान अनुमतियों के लिए आवेदन करता है";
NSSpeechRecognitionUsageDescription = "इस ऐप को वॉयस रिकग्निशन एक्सेस करने के लिए आपकी सहमति की आवश्यकता है। क्या आप वॉयस रिकग्निशन को चालू करने की अनुमति देते हैं?";
"UILaunchStoryboardName" = "LaunchScreen-Chinese";
