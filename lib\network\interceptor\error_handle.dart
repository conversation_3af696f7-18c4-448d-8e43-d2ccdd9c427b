import 'dart:io';

import 'package:dio/dio.dart';
import 'package:text/utils/common_fun.dart';

class ExceptionHandle {
  static const int success = 200;
  static const int success_not_content = 204;
  static const int unauthorized = 401;
  static const int forbidden = 403;
  static const int not_found = 404;

  static const int net_error = 1000;
  static const int parse_error = 1001;
  static const int socket_error = 1002;
  static const int http_error = 1003;
  static const int timeout_error = 1004;
  static const int cancel_error = 1005;
  static const int unknown_error = 9999;

  static NetError handleException(dynamic error) {
    // Log.d(error);
    if (error is DioError) {
      if (error.type == DioErrorType.unknown || error.type == DioErrorType.badResponse) {
        dynamic e = error.error;
        if (e is SocketException) {
          return NetError(socket_error, /*loginIntl("cxy000494")*/ intlanguage("app100000354", "网络异常"));
        }
        if (e is HttpException) {
          return NetError(http_error, /*loginIntl("cxy000633")*/ intlanguage("app100000844", "服务异常"));
        }
        if (e is FormatException) {
          return NetError(parse_error, /*loginIntl("cxy000512")*/ intlanguage("app00629", "数据错误"));
        }
        return NetError(unknown_error, /*loginIntl("cxy000494")*/ error.toString());
      } else if (error.type == DioErrorType.connectionTimeout ||
          error.type == DioErrorType.sendTimeout ||
          error.type == DioErrorType.receiveTimeout) {
        return NetError(timeout_error, /*loginIntl("cxy000320")*/ intlanguage("app100000354", "网络异常"));
      } else if (error.type == DioErrorType.cancel) {
        return NetError(cancel_error, /*loginIntl("cxy000634")*/ "");
      } else {
        return NetError(unknown_error, /*loginIntl("cxy000631")*/ error.toString());
      }
    } else {
      return NetError(unknown_error, /*loginIntl("cxy000631")*/ error.toString());
    }
  }
}

class NetError {
  int code;
  String msg;

  NetError(this.code, this.msg);
}
