//
//  JCWifiManager.h
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2023/2/28.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import <Foundation/Foundation.h>
#import "JCBluetoothModel.h"
NS_ASSUME_NONNULL_BEGIN

typedef void(^searchRelustBlock) (NSArray<JCBluetoothModel*> *modelArray);

typedef void(^wifiConfigBlock) (BOOL isConfig);

@interface JCWifiManager : NSObject

@property(nonatomic,assign,readonly) BOOL canWifiOperate;

+ (instancetype)shareInstance;

// 插入WIFI搜索到的设备
/// - Parameters:
///   - bleName: 蓝牙名称
///   - ip: IP地址
///   - port: 端口地址
///   - available: 剩余可连接数
///   - alias: 设备别名
- (void)insertData:(NSString *)bleName ip:(NSString *)ip port:(NSNumber *)port available:(NSNumber *)available alias:(NSString *)alias;

/// 结束WIFI搜索
- (void)discoverWifiPrinterFinished;

- (void)startWifiScanWithBleName:(NSString*)bleName completion:(void(^)(NSArray *scanedPrinters))completion;
/**
 配网
 @param password wifi密码
 @param wifiName wifi名称
 @param completion 配网回调
 
 */
- (void)configWithWifiName:(NSString*)wifiName password:(NSString*)password completion:(void(^)(NSNumber *))callbackValue;

/**是否有配网*/
- (NSDictionary*)isConfigedWifi:(JCBluetoothModel *)model;


/**
 使用wifi连接打印机
 @param model 当前连接打印机模型
 */
- (void)connectWith:(JCBluetoothModel *)model complate:(XYBlock)complate;
/**
 配网成功与否根据是否能搜索到此wifi设备
 
 @param model 当前配网模型
 
 @param finishedBlock 回调
 */
- (void)CheckconfigWithModel:(JCBluetoothModel*)model;

- (void)didWifiSerachEndWithResultBlock:(searchRelustBlock)result;

- (void)didConfigWithResultBlock:(wifiConfigBlock)result;

/**
 *  WiFi开关是否打开
 *
 *  @return 状态
 */
- (BOOL)isWiFiEnabled;
@end

NS_ASSUME_NONNULL_END
