import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_canvas_plugins_interface/config/template_config.dart';
import 'package:flutter_canvas_plugins_interface/config/toolkit_button.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/shared/canvas_font_data.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:flutter_canvas_plugins_interface/utils/display_util.dart';
import 'package:netal_plugin/netal_plugin.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/components/add_subtract_text_group.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/components/keep_keyboard_popup/keep_keyboard_popup_menu.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/components/stick/keyboard_attachable.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/components/svg_icon.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/keyboard_sensitive_editor_widget.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/model/canvas_element.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/model/element/text_element.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/model/element/text_element_bo.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/model/template_data.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/notification/attribute_panel_refresh_notify.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/provider/canvas_attr_state_notifier.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/provider/elements_data_changed_notifier.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/provider/elements_floatingbar_visible_notifier.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/tinykit_app_config.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/tinykit_canvas_data_mix.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/tinykit_canvas_theme_widget.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/tinykit_lego_utils.dart';
import 'package:provider/provider.dart';

import 'components/throttled_button.dart';
import 'model/tinykit_box_frame_info.dart';
import 'tinykit_theme_color.dart';

Logger _logger = Logger("CanvasItemBoxScreen", on: kDebugMode);

int animatedDurationMilliseconds = 200;

final GlobalKey<CanvasDataMixState> canvasKey = GlobalKey();

/// 定义当前画板焦点状态
enum ElementFocusState {
  /// 未选中
  None,

  /// 选中状态
  Selection,

  /// 编辑内容
  Editor,

  /// 素材选择面板
  Material,

  //素材边框面板
  Material_boder,
}

enum AttrPanelState { normal, highest, shortest }

enum TextMode {
  Horizontal,
  Horizontal_90,
  Vertical,
  Arc,
}

/// 共享数据
class TinyKitCanvasObjectSharedWidget extends InheritedWidget {
  final TemplateData? canvasData;
  final String printColor;
  final CanvasFontConfig fontConfig;
  final int consumablesType;

  TinyKitCanvasObjectSharedWidget({
    super.key,
    required this.canvasData,
    required this.fontConfig,
    required this.printColor,
    required this.consumablesType,
    required Widget child,
  }) : super(child: child);

  static TemplateData? canvasDataOf(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<TinyKitCanvasObjectSharedWidget>()!.canvasData;
  }

  static String printColorOf(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<TinyKitCanvasObjectSharedWidget>()!.printColor;
  }

  static int consumablesTypeOf(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<TinyKitCanvasObjectSharedWidget>()!.consumablesType;
  }

  static CanvasFontConfig fontConfigOf(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<TinyKitCanvasObjectSharedWidget>()!.fontConfig;
  }

  @override
  bool updateShouldNotify(TinyKitCanvasObjectSharedWidget oldWidget) {
    return oldWidget.canvasData != canvasData ||
        oldWidget.fontConfig != fontConfig ||
        oldWidget.printColor != printColor;
  }
}

class CanvasBoxFrame extends StatefulWidget {
  final Map? singleColorPrintInfo;
  final String jsonData;
  final int? consumablesType;
  final CanvasFontConfig fontConfig;
  final List<ToolkitButton> toolkitButtons;
  final bool needDownloadFonts;

  ///默认选中元素类型
  final String defaultSelectType;

  final bool Function(BuildContext buildContext, String? canvasJson, bool showOtherSave, bool isEtagSave,
      Function resetElementFocusStateFun)? onNavigatorPop;
  final Future<TemplateData> Function(String? canvasJson, TemplateConfig? config, BuildContext context,
      {String templateId})? onSave;
  final Future<TemplateData> Function(CanvasBoxFrameState buildContext)? onLabelInit;

  /// 去通用画板编辑
  final void Function(String? canvasJson)? canvasInitData;
  final void Function(
      String? canvasJson, int? currentPage, TemplateConfig? config, BuildContext context, Widget? extraWidget) onPrint;
  final Future<TemplateData?> Function(String? canvasJson, TemplateConfig? config, BuildContext context)? onConfig;
  final Future<TemplateData?> Function(String? canvasJson, TemplateConfig? config, BuildContext context)? onLabelChange;
  final void Function(String canvasJson, BuildContext context)? onBack;
  final String Function(String stringCode, String defaultStr)? getI18nString;

  /// 去打印设置页面
  final void Function()? onPrintSettings;

  /// cable画板内埋点事件
  final void Function(Map<String, dynamic> trackParms)? onSendTrackEvent;

  /// vip 入口栏
  final Widget? vipTipWidget;

  ///标签纸异常提示,高度最高34px
  final Widget? paperErrorTipWidget;
  final TinyKitBoxFrameInfo? frameInfo;

  const CanvasBoxFrame(
      {super.key,
      required this.jsonData,
      required this.fontConfig,
      required this.toolkitButtons,
      this.onNavigatorPop,
      this.onSave,
      this.onLabelInit,
      required this.onPrint,
      this.onLabelChange,
      this.onBack,
      this.onConfig,
      this.getI18nString,
      this.singleColorPrintInfo,
      this.consumablesType,
      this.onSendTrackEvent,
      required this.needDownloadFonts,
      required this.defaultSelectType,
      this.canvasInitData,
      this.onPrintSettings,
      this.vipTipWidget,
      this.paperErrorTipWidget,
      this.frameInfo});

  @override
  CanvasBoxFrameState createState() => CanvasBoxFrameState();
}

class CanvasBoxFrameState extends State<CanvasBoxFrame> {
  /// 当前画板焦点状态
  ElementFocusState _elementFocusState = ElementFocusState.None;

  /// 当前选中组件对象
  List<CanvasElement> _focusedCanvasElements = [];

  CanvasElement? _previousfocusedCanvasElement;

  /// 编辑框组件对象
  /// 表格组件为 table cell
  /// 文本类组件与 focusedCanvasElements 相同
  CanvasElement? _cursorCanvasElement;

  /// 模板配置
  TemplateConfig? _templateConfig;

  /// 写入键盘输入框焦点index
  int? _inputCursorIndex;

  /// 是否可以通过indicator滑动面板，在序列号中输入的时候防止键盘下拉
  bool _isCanDrag = true;

  /// VIP横条显示控制，此变量和用户是否为VIP一起控制
  ValueNotifier<bool> vipVisible = ValueNotifier<bool>(true);

  void reset() {
    _logger.log("reset()");
    canvasKey.currentState?.reset();
    resetAttributePanelHeight(AttrPanelState.normal);
  }

  resetAttributePanelHeight(AttrPanelState state) {
    _attrPanelState = state;
    if (state == AttrPanelState.normal || state == AttrPanelState.highest) {
      _componentsPanelFolded = false;
    } else {
      _componentsPanelFolded = true;
    }
  }

  Future<String> asyncPrintPreView() async {
    reset();
    await Future.delayed(Duration(milliseconds: 200));
    return "1";
  }

  void setTemplateConfig(TemplateConfig templateConfig) {
    _templateConfig = templateConfig;

    /// 宽高重置
    _templateData?.width = templateConfig.templateWidth.toDouble();
    _templateData?.height = templateConfig.templateHeight.toDouble();
    _logger.log('模板宽高重置为：(${_templateData?.width}, ${_templateData?.height})');

    /// 倍率重置
    DisplayUtil.generateDesignRatio(_templateData!.width!.toDouble(), _templateData!.height!.toDouble());

    /// 清空图像库缓存
    _templateData?.resetImageCache();
  }

  TemplateData? _templateData;
  String _printColor = "";
  int _consumablesType = 0;

  @override
  void initState() {
    _logger.log("=========canvas_box_frame的initState方法被调用");
    super.initState();
    if (widget.singleColorPrintInfo?.isNotEmpty ?? false) {
      _printColor = widget.singleColorPrintInfo!["currentPrintColor"];
      _consumablesType = widget.singleColorPrintInfo!["consumablesType"];
    }
    NetalPlugin().init;
    widget.onLabelInit?.call(this);
    initLegoJsonData(widget.jsonData);
  }

  @override
  void setState(VoidCallback fn) {
    super.setState(fn);
  }

  /// 版式json 转换为画板(图像库)需要的json ，jsonData而非模板
  void initLegoJsonData(String jsonData, {bool clearStack = true}) {
    try {
      /// 清除图像缓存
      if (_templateData != null) {
        _templateData?.resetImageCache();
      }
    } catch (e, stack) {
      _logger.log("parseTemplateJsonData error==$stack");
    }

    try {
      /// 解析版式数据
      Map<String, dynamic> jsonMap = jsonDecode(jsonData);

      /// 1.获取版式的宽高
      /// 2.调用lego排版库拿到转换后可直接渲染，操作熟悉的json
      /// 3.走画板流程

      double canvasWidth = widget.frameInfo!.canvasWidth;
      double canvasHeight = widget.frameInfo!.canvasHeight;

      debugPrint("parseLayoutToRenderJson canvasWidth ==========> ${canvasWidth} , ${canvasHeight}");

      debugPrint("parseLayoutToRenderJson jsonData ==========> ${jsonData}");

      String layoutJsonData = TinyKitLegoUtils.instance.parseLayoutToRenderJson(jsonData, canvasWidth, canvasHeight);

      jsonMap = jsonDecode(layoutJsonData);
      _templateData = TemplateData.fromJson(jsonMap);

      // 重置状态
      canvasKey.currentState?.clearMultiSelectAndFocused(needFresh: true);
      _elementFocusState = ElementFocusState.None;

      ///切换版式 要设置预置的初始值
      if (_templateData != null) {
        if (widget.frameInfo!.reInitTextValues.isNotEmpty) {
          String? defValue = widget.frameInfo?.reInitTextValues[0];
          _templateData?.elements[0].value = defValue;
          CanvasElement? element = _templateData?.canvasElements[0];
          TextElement textElement = element?.data as TextElement;
          textElement.value = defValue;
          textElement.isEditing = false;
          textElement.isEdited = false;

          // widget.frameInfo?.reInitTextValues.forEachIndexed((index, value) {
          //   if (index < _templateData!.elements.length) {
          //     _templateData?.elements[0].value = value;
          //     CanvasElement? element = _templateData?.canvasElements[0];
          //     TextElement textElement = element?.data as TextElement;
          //     textElement.value = value;
          //     textElement.isEditing = false;
          //     textElement.isEdited = false;
          //   }
          // });

          /// 绑定关系，二次刷新
          String? trackerJson = _templateData?.generateCanvasJson(savePrintTemplate: true);
          String layoutJsonData = TinyKitLegoUtils.instance.parseCanvasJson(trackerJson!, canvasWidth, canvasHeight);
          jsonMap = jsonDecode(layoutJsonData);
          _templateData = TemplateData.fromJson(jsonMap);
        }
      }
      canvasKey.currentState?.resetTemplate(_templateData!);

      /// 扫描指定路径下字体文件
      // _readFontFiles();
    } catch (e, stack) {
      _logger.log("parseTemplateJsonData error==$stack");
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  _resetPanelState() {
    if (_focusedCanvasElements.isEmpty) {
      _elementFocusState = ElementFocusState.None;
    }
  }

  @override
  Widget build(BuildContext context) {
    _logger.log("=========canvas_box_frame的build方法被调用  ${CanvasTheme.of(context)}");

    /// 计算倍率
    DisplayUtil.init(context);
    DisplayUtil.generateDesignRatio(_templateData!.width!.toDouble(), _templateData!.height!.toDouble());

    return WillPopScope(
      onWillPop: () async {
        return _backSaveClick();
      },
      child: Scaffold(
          resizeToAvoidBottomInset: false,
          backgroundColor: TinyKitThemeColor.COLOR_F5F5F5,
          appBar: AppBar(
            toolbarHeight: 48,
            backgroundColor: CanvasTheme.of(context).backgroundLightColor,
            elevation: 0.2,
            systemOverlayStyle:
                SystemUiOverlayStyle(statusBarColor: Colors.transparent, statusBarIconBrightness: Brightness.dark),
            centerTitle: true,
            leading: IconButton(
                icon: Image.asset(
                  "packages/niimbot_tiny_canvaskit/assets/images/tiny_canvas_back.png",
                  matchTextDirection: true,
                  height: 17,
                  fit: BoxFit.contain,
                ),
                focusColor: Colors.transparent,
                splashColor: Colors.transparent,
                onPressed: () {
                  if (FocusScope.of(context).hasFocus) {
                    FocusScope.of(context).unfocus();
                  } else {
                    Navigator.of(context).pop();
                  }
                }),
            title: Text(
              '线缆小程序',
              style: TextStyle(color: TinyKitThemeColor.mainTitle, fontSize: 17, fontWeight: FontWeight.w500),
            ),
          ),
          body: TinyKitCanvasObjectSharedWidget(
            printColor: _printColor,
            consumablesType: _consumablesType,
            canvasData: _templateData,
            fontConfig: widget.fontConfig,
            child: MultiProvider(
              providers: [
                ChangeNotifierProvider.value(value: ElementsDataChangedNotifier()),
                ChangeNotifierProvider.value(value: CanvasAttrStateNotifier()),
                ChangeNotifierProvider.value(value: ElementsFloatingBarVisibleNotifier())
              ],
              child: Container(
                child: Stack(
                  alignment: AlignmentDirectional.bottomCenter,
                  fit: StackFit.loose,
                  children: [
                    /// 画板
                    buildMixNbCanvasData(),

                    // _buildDrawBoardTips(),

                    /// 面板区域
                    _buildToolsPanel(),

                    ///标签纸异常提示
                    _buildPaperErrorTips()
                  ],
                ),
              ),
            ),
          )),
    );
  }

  Widget _buildPaperErrorTips() {
    return Align(
      alignment: Alignment.topLeft,
      child: Container(child: widget.paperErrorTipWidget),
    );
  }

  Align getBottomBar() {
    final canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
    return canvasConfigIml?.isShowPrintButton() ?? false ? _buildBottomBar() : _buildBottomBar();
  }

  String? getLabelData() {
    CanvasDataMixState currentState = canvasKey.currentState as CanvasDataMixState;
    String? canvasJson = currentState.getCanvasJsonDATA();
    return canvasJson;
  }

  /// 底部栏: 保存/打印
  Align _buildBottomBar() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
          height: _maxElementToolbarOffset + safeAreaBottomHeight,
          padding: EdgeInsets.only(bottom: safeAreaBottomHeight),
          decoration: BoxDecoration(
            color: CanvasTheme.of(context).backgroundLightColor, // 底色
            boxShadow: [
              BoxShadow(
                blurRadius: 5, //阴影范围
                spreadRadius: 0.1, //阴影浓度
                offset: Offset(0, -2.0),
                color: Colors.grey.withOpacity(0.15), //阴影颜色
              ),
            ],
          ),
          child: Row(mainAxisAlignment: MainAxisAlignment.spaceAround, children: [
            Container(
              color: CanvasTheme.of(context).backgroundColor,
              width: 1,
              height: 17,
            ),
          ])),
    );
  }

  Future<bool> _backSaveClick({bool isEtagSave = false}) async {
    return true;
  }

  CanvasDataMix buildMixNbCanvasData() {
    return CanvasDataMix(
      key: canvasKey,
      templateData: _templateData,
      onElementFocusChanged: (List<CanvasElement> elements, int changeType) {
        setState(() {
          /// 选中锁定状态的元素时，不可进入编辑状态
          if (elements.length == 1 && elements.first.data.isLock == 1) {
            defaultIndex = null;
            attributeIndex = Random().nextInt(10000);
            _elementFocusState = ElementFocusState.None;
            return;
          }

          /// 切换选中 or 无焦点状态
          _focusedCanvasElements = elements;
          if ((changeType == 2 || changeType == 3) && elements.length > 0) {
            defaultIndex = null;
            _elementFocusState = ElementFocusState.Selection;
            attributeIndex = Random().nextInt(10000);
            resetAttributePanelHeight(
                _elementFocusState == ElementFocusState.Editor ? AttrPanelState.normal : _attrPanelState);
          } else if (_focusedCanvasElements.length == 1) {
            CanvasElement focusedElement = _focusedCanvasElements.first;
            if (_previousfocusedCanvasElement != null &&
                focusedElement.elementType == _previousfocusedCanvasElement?.elementType) {
              if ((_elementFocusState == ElementFocusState.Editor) &&
                  (_previousfocusedCanvasElement?.elementType == ElementItemType.text ||
                      _previousfocusedCanvasElement?.elementType == ElementItemType.barcode ||
                      _previousfocusedCanvasElement?.elementType == ElementItemType.qrcode)) {
                if (changeType == 1) {
                  ///处于编辑模式时拖动
                  if (_elementFocusState == ElementFocusState.Editor) {
                    _cursorCanvasElement = elements.first;
                    _elementFocusState = ElementFocusState.Editor;
                    _inputCursorIndex = null;
                  }
                  _logger.log("移动改变大小变化");
                } else {
                  if (elements.first.data.isBindingElement() || elements.first.data.isAdvanceQRCode()) {
                    _cursorCanvasElement = elements.first;
                    _elementFocusState = ElementFocusState.Selection;
                    _inputCursorIndex = null;
                    defaultIndex = 0;
                    attributeIndex = Random().nextInt(10000);
                  } else {
                    _cursorCanvasElement = elements.first;
                    _elementFocusState = ElementFocusState.Editor;
                    _inputCursorIndex = null;
                  }
                }
              } else {
                if (changeType == 1) {
                  ///处于编辑模式时拖动
                  if (_elementFocusState == ElementFocusState.Editor) {
                    _cursorCanvasElement = elements.first;
                    _elementFocusState = ElementFocusState.Editor;
                    _inputCursorIndex = null;
                  } else if (defaultIndex == 0 &&
                      _previousfocusedCanvasElement?.data.isBindingElement() !=
                          elements.first.data.isBindingElement()) {
                    defaultIndex = null;
                    attributeIndex = Random().nextInt(10000);
                    resetAttributePanelHeight(_attrPanelState);
                    _elementFocusState = ElementFocusState.Selection;
                  } else {
                    _elementFocusState = ElementFocusState.Selection; //
                  }
                  _logger.log("移动改变大小变化");
                } else {
                  if (focusedElement.elementType == ElementItemType.text &&
                      defaultIndex == 0 &&
                      _elementFocusState != ElementFocusState.Editor &&
                      _previousfocusedCanvasElement?.data.isBindingElement() == true &&
                      focusedElement.data.isBindingElement() == false) {
                    _cursorCanvasElement = elements.first;
                    _elementFocusState = ElementFocusState.Editor;
                    _inputCursorIndex = null;
                    attributeIndex = Random().nextInt(10000);
                  } else {
                    _elementFocusState = ElementFocusState.Selection; //
                  }
                  resetAttributePanelHeight(
                      _elementFocusState == ElementFocusState.Editor ? AttrPanelState.highest : _attrPanelState);
                }
              }
            } else {
              defaultIndex = null;
              attributeIndex = Random().nextInt(10000);
              resetAttributePanelHeight(
                  _elementFocusState == ElementFocusState.Editor ? AttrPanelState.normal : _attrPanelState);
              _elementFocusState = ElementFocusState.Selection;
            }
            _previousfocusedCanvasElement = elements.first;
            _logger.log(
                "赋值焦点元素111Id:${_focusedCanvasElements.first.elementId}--value:${_focusedCanvasElements.first.data.value}");
          } else {
            materialDefaultIndex = 0;
            vipVisible.value = false;
            this._focusedCanvasElements = elements;
            if (this._elementFocusState == ElementFocusState.None) {
              this._elementFocusState =
                  this._focusedCanvasElements.length == 0 ? ElementFocusState.None : ElementFocusState.Selection;
              _previousfocusedCanvasElement == null;
              defaultIndex = null;
              attributeIndex = Random().nextInt(10000);
              resetAttributePanelHeight(
                  _elementFocusState == ElementFocusState.Editor ? AttrPanelState.normal : _attrPanelState);
              if (!_componentsPanelFolded && changeType == -1) {
                _refresh(() {
                  _componentsPanelFolded = !_componentsPanelFolded;
                  if (_componentsPanelFolded) {
                    _attrPanelState = AttrPanelState.shortest;
                  } else {
                    _attrPanelState = AttrPanelState.normal;
                  }
                });
              }
            } else {
              this._elementFocusState =
                  this._focusedCanvasElements.length == 0 ? ElementFocusState.None : ElementFocusState.Selection;
              _previousfocusedCanvasElement == null;
              defaultIndex = null;
              attributeIndex = Random().nextInt(10000);
              resetAttributePanelHeight(
                  _elementFocusState == ElementFocusState.Editor ? AttrPanelState.normal : _attrPanelState);
            }
          }
        });
      },
      onElementValueEditorDisplay: (CanvasElement cursorElement, CanvasElement focusedElement,
          {bool isDoubleClick = false}) {
        setState(() {
          _logger.log("==========================onElementValueEditorDisplay=================================");

          /// 切换编辑状态
          if ((focusedElement.data.isBindingElement() || cursorElement.data.isBindingElement()) &&
              (isDoubleClick ?? false)) {
            if (focusedElement.data.isBindingCommodity() || cursorElement.data.isBindingCommodity()) {
              defaultIndex = 0;
              this._focusedCanvasElements = [focusedElement];
              this._cursorCanvasElement = cursorElement;
              this._elementFocusState = ElementFocusState.Selection;
              this._inputCursorIndex = null;
              attributeIndex = Random().nextInt(10000);
              int index = focusedElement.data.bindingColumn;
            } else {
              if (focusedElement.elementType == ElementItemType.text) {
                defaultIndex = 0;
              }
              this._focusedCanvasElements = [focusedElement];
              this._cursorCanvasElement = cursorElement;
              this._elementFocusState = ElementFocusState.Editor;
              this._inputCursorIndex = null;
            }
          } else {
            if (focusedElement.elementType == ElementItemType.text) {
              defaultIndex = 0;
            }
            this._focusedCanvasElements = [focusedElement];
            this._cursorCanvasElement = cursorElement;
            this._elementFocusState = ElementFocusState.Editor;
            this._inputCursorIndex = null;
          }
        });
      },
      onElementMaterialClicked: () {},
      onElementMaterialBoderClicked: () {},
      onLabelChange: () {},
      onAdvanceQRCodeInfoChanged: () {},
      onAttrPanelChange: () {
        AttributePanelRefreshNotify.notifier.value += 1;
      },
      onElementFocusState: () => _elementFocusState,
      rfidBind: () {},
      onToLabelDetail: () {},
      labelInfo: '',
      frameInfo: widget.frameInfo!,
    );
  }

  Widget _ThrottledButtonToDrawBoard() {
    return ThrottledButton(
      onPressed: () {
        CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({"track": 'click', "posCode": "125_335"});
        CanvasDataMixState currentState = canvasKey.currentState as CanvasDataMixState;
        String? canvasJson = currentState.getCanvasJsonDATA();
        widget.canvasInitData?.call(canvasJson);
      },
      child: Container(
        decoration: BoxDecoration(
            color: TinyKitThemeColor.COLOR_747480.withOpacity(0.08), borderRadius: BorderRadius.circular(18)),
        padding: EdgeInsetsDirectional.symmetric(horizontal: 12, vertical: 4),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '使用“通用画板”编辑',
              style: TextStyle(color: TinyKitThemeColor.mainTitle, fontSize: 13, fontWeight: FontWeight.normal),
            ),
            const SizedBox(
              width: 6,
            ),
            SvgIcon(
              'assets/tiny_go_gcanvas_vip.svg',
              color: TinyKitThemeColor.title,
            )
          ],
        ),
      ),
    );
  }

  /// 非VIP时展示画板的编辑
  Widget _buildDrawBoardTips() {
    return Consumer<ElementsDataChangedNotifier>(
        builder: (BuildContext context, ElementsDataChangedNotifier value, Widget? child) {
      // 计算标签纸的高度进行偏移
      final startOffsetHeight = DisplayUtil.designMarginBounds * 3;
      final paperRenderBoxHeight = (_templateData?.height ?? 1) * DisplayUtil.dpRatio;
      return Positioned(top: startOffsetHeight + paperRenderBoxHeight + 60, child: _ThrottledButtonToDrawBoard());
    });
  }

  Widget _buildToolsPanel() {
    /// 重置元素编辑状态
    this.resetElementEditStatus();
    if (_elementFocusState == ElementFocusState.Selection && _focusedCanvasElements.isNotEmpty) {
      resetAttributePanelHeight(AttrPanelState.normal);
      _elementFocusState = ElementFocusState.None;
      _focusedCanvasElements.clear();
      _showPrintState = false;
    } else if (_elementFocusState == ElementFocusState.Editor && _focusedCanvasElements.isNotEmpty) {
      resetAttributePanelHeight(AttrPanelState.highest);
      _showPrintState = false;
    } else {
      _elementFocusState = ElementFocusState.None;
      _showPrintState = true;
    }
    // if(!_showAnimatedContainer && _showPrintState){
    //   _printNumFocus.unfocus();
    // }
    return Consumer<ElementsDataChangedNotifier>(
        builder: (BuildContext context, ElementsDataChangedNotifier value, Widget? child) {
      return _buildAttributePanel(context);
    });
  }

  Widget tipsContainer() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      // crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        SizedBox(
          height: 10,
        ),
        ThrottledButton(
          onPressed: () {
            CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({"track": 'click', "posCode": "125_335"});
            CanvasDataMixState currentState = canvasKey.currentState as CanvasDataMixState;
            String? canvasJson = currentState.getCanvasJsonDATA();
            widget.canvasInitData?.call(canvasJson);
          },
          child: Container(
            color: Colors.transparent,
            padding: EdgeInsetsDirectional.symmetric(horizontal: 10, vertical: 6),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "使用通用画板",
                  style: TextStyle(color: TinyKitThemeColor.mainTitle, fontSize: 13, fontWeight: FontWeight.normal),
                ),
                const SizedBox(
                  width: 6,
                ),
                SvgIcon(
                  'assets/tiny_go_gcanvas_vip.svg',
                  color: TinyKitThemeColor.subtitle,
                )
              ],
            ),
          ),
        ),
        SizedBox(
          height: 12,
        ),
        Container(
          child: widget.vipTipWidget,
        ),
      ],
    );
  }

  /// 重置元素编辑状态
  void resetElementEditStatus() {
    _logger.log("====resetElementEditStatus====> ");
    _templateData?.canvasElements.forEach((element) {
      if (element.data is TextElement) {
        // (element.data as TextElement).isEditing = false;
      }
    });
  }

  AttrPanelState _attrPanelState = AttrPanelState.normal;

  /// 元素Key值随机值，用于刷新
  static int attributeIndex = 0;

  /// 元素面板默认选中索引
  static int? defaultIndex = null;

  /// 素材面板索引
  static int materialDefaultIndex = 0;

  List<CanvasElement> getFakeMaterialCanvasElements() {
    List<CanvasElement> datas = this._focusedCanvasElements;
    return datas;
  }

  ///打印按钮
  Widget _printContainer(BuildContext context, CanvasAttrStateNotifier _canvasAttrStateNotifier) {
    return GestureDetector(
        onTap: () {
          // _toggleContainer(context, _canvasAttrStateNotifier.showAnimatedContainer);
          widget.onSendTrackEvent?.call({
            "track": "click",
            "posCode": "125_327_303",
            "ext": {"is_vip": widget.frameInfo!.isVip ? 1 : 0}
          });
          // if (!_canvasAttrStateNotifier.showAnimatedContainer) {
            int currentPage = 1;
            // if (_incrementValueController.value.text.isNotEmpty) {
            //   currentPage = int.parse(_incrementValueController.value.text);
            // }
            // String ? canvasJson = _templateData?.generateCanvasJson(savePrintTemplate: true);
            CanvasDataMixState currentState = canvasKey.currentState as CanvasDataMixState;
            String? canvasJson = currentState.getCanvasJsonDATA();
            widget.onPrint.call(canvasJson, currentPage, _templateConfig, context, _ThrottledButtonToDrawBoard());
            // _printNumFocus.unfocus();

            ///收起键盘回复初始状态 ，避免属性窗口
            canvasKey.currentState?.resetOperablePanel();
          // }
        },
        child: Container(
          alignment: Alignment.centerRight,
          margin: EdgeInsets.fromLTRB(2, 4, 20, 4),
          width: _elementFocusState != ElementFocusState.None ? 52 : 62,
          height: _elementFocusState != ElementFocusState.None ? 35 : 45,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(30),
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [Color(0xFF6D97FF), Color(0xFF2F69EC)],
            ),
            boxShadow: [
              BoxShadow(
                color: Color(0xFFFB4B42).withOpacity(0.1), // 阴影颜色
                spreadRadius: 0, // 阴影扩散半径
                blurRadius: 3, // 阴影模糊半径
                offset: Offset(0, 3), // 阴影的偏移量
              ),
            ],
          ),
          child: widget.frameInfo!.isPrint
              ? Container(
                  alignment: Alignment.center,
                  child: const CupertinoActivityIndicator(
                      radius: 10, animating: true, color: TinyKitThemeColor.background),
                )
              : Center(
                  child: _canvasAttrStateNotifier.showAnimatedContainer
                      ? Text(
                          '打印',
                          style: TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.bold),
                        )
                      : SvgIcon(
                          'assets/element/attribute/tiny_attr_print.svg',
                          width: 30,
                          height: 30,
                          color: Colors.white,
                        ),
                ),
        ));
  }

  ///构建对齐widget，键盘不随着选中收起
  List<KeepKeyboardPopupMenuItem> _buildKeepKeyboardPopupMenuItem(closePopup, int _index, BuildContext context) {
    _logger.log("_buildKeepKeyboardPopupMenuItem ====> _index = ${_index} , needHeight = ${needHeight}");
    List<AlignAttr> aligns = TinyAppConfig.alignList;
    return aligns
        .mapIndexed((index, e) => KeepKeyboardPopupMenuItem(
              child: Container(
                height: 50,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: EdgeInsetsDirectional.symmetric(horizontal: 2),
                      child: _index == index
                          ? SvgIcon(
                              'assets/element/tick.svg',
                            )
                          : SizedBox(
                              width: 22,
                              height: 22,
                            ),
                    ),
                    Text(
                      e.title,
                      style: TextStyle(color: const Color(0xFF262626), fontSize: 16, fontWeight: FontWeight.w400),
                    ),
                    Container(
                        width: 20,
                        child: Image.asset(
                          e.iconUrl,
                          matchTextDirection: true,
                          width: 24,
                          height: 24,
                        ))
                  ],
                ),
              ),
              onTap: () {
                closePopup.call();
                // _alignAttr = e;
                _logger.log("_buildKeepKeyboardPopupMenuItem click ====> index = ${index}");
                _focusedCanvasElements.forEach((canvasElement) {
                  TextElement textElement = canvasElement.data as TextElement;
                  textElement.textAlignHorizontal = index;
                  // textElement.isEditing = true;
                  // textElement.isEdited = true;

                  textElement.isEdited = true && textElement.isEdited;
                  textElement.isEditing = true && textElement.isEditing;
                });
                Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                    .setDataChangedElements(_focusedCanvasElements);
              },
            ))
        .toList();
  }

  double needHeight = 0;

  Widget _panelContainer(BuildContext context, CanvasAttrStateNotifier value) {
    double screenHeight = MediaQuery.sizeOf(context).height;
    var keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    needHeight = _elementFocusState == ElementFocusState.Editor ? (screenHeight - keyboardHeight - 280) : 80;
    int _index = 1;
    if (_focusedCanvasElements.isNotEmpty) {
      TextElement textElement = _focusedCanvasElements.first.data as TextElement;
      _index = textElement.textAlignHorizontal ?? 0;
    }

    return Container(
      color: Colors.transparent,
      child: Row(
        children: [
          WithKeepKeyboardPopupMenu(
              menuItemBuilder: (context, closePopup) => _buildKeepKeyboardPopupMenuItem(closePopup, _index, context),
              childBuilder: (context, openPopup) => Padding(
                    padding: const EdgeInsets.fromLTRB(8, 0, 0, 0),
                    child: GestureDetector(
                      onTap: openPopup,
                      child: Container(
                        color: Colors.transparent,
                        child: Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: Image.asset(
                              TinyAppConfig.alignList[_index].iconUrl,
                              matchTextDirection: true,
                              width: 24,
                              height: 24,
                            )),
                      ),
                    ),
                  ),
              calculatePopupPosition: (Size menuSize, Rect overlayRect, Rect buttonRect) {
                return Offset(8, needHeight);
              },
              backgroundBuilder: (context, child) => Material(
                    elevation: 10,
                    borderRadius: BorderRadius.circular(16),
                    shadowColor: Colors.black.withOpacity(0.3),
                    color: Colors.white,
                    child: child,
                  )),
          GestureDetector(
            onTap: () {
              _focusedCanvasElements.forEach((canvasElement) {
                TextElement textElement = canvasElement.data as TextElement;
                List<String> fontStyle = textElement.fontStyle;
                if (fontStyle.contains("bold")) {
                  fontStyle = [];
                } else {
                  fontStyle = ["bold"];
                }
                textElement.fontStyle = fontStyle;
                // textElement.isEditing = false;
                // textElement.isEdited = false;

                textElement.isEdited = true && textElement.isEdited;
                textElement.isEditing = true && textElement.isEditing;
              });
              Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                  .setDataChangedElements(_focusedCanvasElements);
            },
            child: Container(
              color: Colors.transparent,
              child: Padding(
                  padding: const EdgeInsets.fromLTRB(10, 10, 10, 10),
                  child: Image.asset(
                    "packages/niimbot_tiny_canvaskit/assets/images/tiny_attr_bold.png",
                    width: 24,
                    matchTextDirection: true,
                  )),
            ),
          ),
        ],
      ),
    );
  }

  ///点击打印按钮状态管理 0: 初始状态，1:默认条弹出，2:设置打印分数条弹出
// bool _showAnimatedContainer = false;
  bool _showPrintState = false;

  ///发送provider 通知更新在状态
  void _toggleContainer(BuildContext context, bool state) {
    debugPrint("_toggleContainer state = ${state}");
    Provider.of<CanvasAttrStateNotifier>(context, listen: false).setAnimatedContainer(state);
  }

  final TextEditingController _incrementValueController = TextEditingController(text: "1");
  double safeBottom = 0;

  ///构建操作属性和softKeyboard 面板
  Widget _buildAttributePanel(BuildContext context) {
    if (_elementFocusState == ElementFocusState.None && safeBottom == 0) {
      safeBottom = MediaQuery.paddingOf(context).bottom;
    }
    Size screenSize = MediaQuery.sizeOf(context);
    _logger.log(
        "_buildAttributePanel========> _elementFocusState: ${_elementFocusState},_showPrintState: ${_showPrintState}, ${screenSize.width / 6.6} ");

    /// 键盘不能放在不确定高度的widget 中，比如:Column,Row,List等，会引起整个窗口重绘，
    /// 另外MediaQuery.of会引起重绘不能使用, 更换成MediaQuery.sizeOf ，MediaQuery.paddingOf ，viewInsetsOf 等
    bool softKeyBoardAppear =
        _attrPanelState == AttrPanelState.highest && (_elementFocusState == ElementFocusState.Editor);

    ///记录键盘和面板状态
    Widget attributePanel = Consumer<CanvasAttrStateNotifier>(
      builder: (BuildContext context, value, Widget? child) {
        debugPrint("CanvasAttrStateNotifier value = ${value.showAnimatedContainer}");
        return GestureDetector(
          onTap: () {
            // 拦截软键盘弹出后工具栏空白处点击事件
          },
          child: Stack(alignment: Alignment.topCenter, children: [
            _buildInnerAttrPanel(context, value),
            _buildInnerPrintPanel(context, value),
          ]),
        );
      },
    );

    if (softKeyBoardAppear) {
      Widget highPanel = Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 2,
            color: CanvasTheme.of(context).backgroundLightColor,
          ),
          Flexible(child: attributePanel),
          Container(
            height: 2,
            color: CanvasTheme.of(context).backgroundLightColor,
          ),
          Container(child: _buildInputToolBar()),
          Container(
            height: 8,
            color: CanvasTheme.of(context).backgroundLightColor,
          ),
        ],
      );
      return highPanel;
    }

    Widget wrapperAttributePanel = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // tipsContainer(),
        _ThrottledButtonToDrawBoard(),
        SizedBox(
          height: 24,
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: KeyboardAttachable(child: attributePanel),
        ),
      ],
    );
    return wrapperAttributePanel;
  }

  ///底部元素属性操作栏
  Widget _buildInnerAttrPanel(BuildContext context, CanvasAttrStateNotifier _canvasAttrStateNotifier) {
    if (_elementFocusState == ElementFocusState.None && safeBottom == 0) {
      safeBottom = MediaQuery.paddingOf(context).bottom;
    }
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(bottom: _elementFocusState == ElementFocusState.None ? safeBottom : 0),
      height: 44 + (_elementFocusState == ElementFocusState.None ? safeBottom : 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Visibility(
              visible: _elementFocusState != ElementFocusState.None && !_canvasAttrStateNotifier.showAnimatedContainer,
              child: _panelContainer(context, _canvasAttrStateNotifier)),
          _printContainer(context, _canvasAttrStateNotifier)
        ],
      ),
      color: Colors.white,
    );
  }

  ///底部打印设置栏面板
  FocusNode _printNumFocus = FocusNode();

  Widget _buildInnerPrintPanel(BuildContext context, CanvasAttrStateNotifier _canvasAttrStateNotifier) {
    Size screenSize = MediaQuery.sizeOf(context);
    return Visibility(
        visible: _elementFocusState != ElementFocusState.None || _showPrintState,
        child: Positioned.fill(
          top: 0,
          child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
            AnimatedOpacity(
              opacity: _canvasAttrStateNotifier.showAnimatedContainer ? 1.0 : 0.0,
              duration: Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: IgnorePointer(
                ignoring: !_canvasAttrStateNotifier.showAnimatedContainer,
                child: GestureDetector(
                  onTap: () {
                    _toggleContainer(context, _canvasAttrStateNotifier.showAnimatedContainer);
                  },
                  child: Container(
                    height: 44,
                    alignment: Alignment.centerLeft,
                    margin: EdgeInsets.fromLTRB(14, 0, 0, 0),
                    child: SvgIcon(
                      'assets/element/attribute/tiny_attr_right_arrow.svg',
                      width: 32,
                      height: 32,
                      color: Colors.black,
                    ),
                  ),
                ),
              ),
            ),
            AnimatedPadding(
              duration: Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              padding: EdgeInsetsDirectional.only(start: _canvasAttrStateNotifier.showAnimatedContainer ? 0 : 30),
              child: AnimatedOpacity(
                opacity: _canvasAttrStateNotifier.showAnimatedContainer ? 1.0 : 0.0,
                duration: Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                child: IgnorePointer(
                  ignoring: !_canvasAttrStateNotifier.showAnimatedContainer,
                  child: Container(
                    //  width: screenSize.width / 6,
                    height: 44,
                    color: Colors.white,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 40,
                        ),
                        GestureDetector(
                            onTap: widget.onPrintSettings,
                            child: Container(
                                padding: EdgeInsets.fromLTRB(10, 6, 10, 6),
                                alignment: Alignment.centerRight,
                                child: SvgIcon(
                                  'assets/tiny_print_settings.svg',
                                ))),
                        Container(
                            child: AddSubTractTextGroup(
                          changeEvent: (type) {
                            if (type == 1 || type == 3) {
                              widget.onSendTrackEvent?.call({
                                "track": "click",
                                "posCode": type == 1 ? "125_327_301" : "125_327_302",
                                "ext": {"source": 2}
                              });
                            }
                          },
                          onChanged: (num value) {},
                          hintText: '',
                          textEditingController: _incrementValueController,
                          maxLength: 3,
                          focusNode: _printNumFocus,
                        ))
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ]),
        ));
  }

  /// 设置元素编辑状态
  void setElementEditingStatus(CanvasElement? element) {
    if (element != null && (element.data is TextElement) && (element.data as TextElement).isSupportBoxStyle()) {
      if (!(element.data as TextElement).isEditing) {
        (element.data as TextElement).isEditing = false;
      } else {
        (element.data as TextElement).isEditing = true;
      }
      //(element.data as TextElement).isEditing = true;
    }
  }

  /// 键盘输入框及工具栏
  Widget _buildInputToolBar() {
    setElementEditingStatus(_cursorCanvasElement);
    return Align(
      alignment: Alignment.bottomCenter,
      child: KeyboardAttachable(
        backgroundColor: CanvasTheme.of(context).backgroundLightColor!,
        child: KeyboardSensitiveEditorWidget(
          key: Key('ElementEditor_${_cursorCanvasElement?.elementId ?? ""}'),
          templateData: _templateData,
          canvasElement: _cursorCanvasElement!,
          parentContext: canvasKey.currentState?.context,
          inputCursorIndex: _inputCursorIndex,
          onComplete: (_) {
            _logger.log("键盘收起: onComplete");
            canvasKey.currentState?.resetOperablePanel();

            ///下面的方式会重刷整个窗口不可取
            // setState(() {
            //   _elementFocusState = ElementFocusState.None;
            //   _attrPanelState = AttrPanelState.normal;
            // });
          },
          onFocusPreviousClicked: () {
            _logger.log("键盘收起: onFocusPreviousClicked");
            final previousFocusElement = _getPreviousCursorFocusElement(_cursorCanvasElement);
            if (previousFocusElement != null) {
              canvasKey.currentState?.switchCursorFocus(previousFocusElement);
            }
          },
          onFocusNextClicked: () {
            _logger.log("键盘收起: onFocusNextClicked");
            final nextFocusElement = _getNextCursorFocusElement(_cursorCanvasElement);
            if (nextFocusElement != null) {
              canvasKey.currentState?.switchCursorFocus(nextFocusElement);
            }
          },
        ),
        keyboardChangedCallback: (keyboardOpen) {
          _logger.log("键盘收起: ${keyboardOpen}");
          // setElementEditingStatus(_cursorCanvasElement,keyboardOpen);
          double screenHeight = MediaQuery.sizeOf(context).height;
          if (!keyboardOpen) {
            needHeight = screenHeight - 80 - 50 * 4;
            canvasKey.currentState?.resetOperablePanel();
          } else {
            needHeight = (screenHeight - screenHeight / 2 - 50 * 4);
          }
        },
      ),
    );
  }

  /// 组件 GridView 页码
  int _currentElementCreatePageIndex = 0;

//每一行放置的item的个数
  int _itemCount = 4;

  /// 组件面板是否收起
  bool _componentsPanelFolded = false;

  _refresh(VoidCallback voidCallback) {
    setState(voidCallback);
  }

  /// 创建元素面板展开 关闭时底部偏移
  final double _maxElementToolbarOffset = 48;
  static double safeAreaBottomHeight = 0;

  /// 获取前一个可以获取输入光标的组件
  CanvasElement? _getPreviousCursorFocusElement(CanvasElement? currentCursorFocusElement) {
    if (currentCursorFocusElement == null) {
      return null;
    }

    List<CanvasElement>? cursorFocusableElements = _getCursorFocusableElements();
    if (cursorFocusableElements == null || cursorFocusableElements.length < 2) {
      return null;
    }

    int index = cursorFocusableElements.indexOf(currentCursorFocusElement);
    if (index < 0) {
      return null;
    }
    if (index == 0) {
      return cursorFocusableElements.last;
    } else {
      return cursorFocusableElements[index - 1];
    }
  }

  /// 获取后一个可以获取输入光标的组件
  CanvasElement? _getNextCursorFocusElement(CanvasElement? currentCursorFocusElement) {
    if (currentCursorFocusElement == null) {
      return null;
    }

    List<CanvasElement>? cursorFocusableElements = _getCursorFocusableElements();
    if (cursorFocusableElements == null || cursorFocusableElements.length < 2) {
      return null;
    }

    int index = cursorFocusableElements.indexOf(currentCursorFocusElement);
    if (index < 0) {
      return null;
    }
    if (index == cursorFocusableElements.length - 1) {
      return cursorFocusableElements.first;
    } else {
      return cursorFocusableElements[index + 1];
    }
  }

  /// 可获取输入焦点的元素列表
  List<CanvasElement>? _getCursorFocusableElements() {
    final cursorFocusableElements =
        _templateData?.canvasElements.where((element) => element.data.canDoubleClickToEditValue()).toList();
    cursorFocusableElements?.sort((CanvasElement a, CanvasElement b) {
      /// y 轴优先, 中心点 从左上横向排序到右下
      return ((a.data.y + a.data.height / 2) * 100000 + (a.data.x + a.data.width / 2))
          .compareTo((b.data.y + b.data.height / 2) * 100000 + (b.data.x + b.data.width / 2));
    });

    cursorFocusableElements?.forEach((element) {
      _logger.log('(x, y) ${element.data.x} ${element.data.y}, ${element.data.value}');
    });
    return cursorFocusableElements;
  }
}
