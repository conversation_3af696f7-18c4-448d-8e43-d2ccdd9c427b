import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:keyboard_actions/keyboard_actions.dart';
import 'package:text/application.dart';
import 'package:text/macro/constant.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_logic.dart';
import 'package:text/pages/industry_template/home/<USER>/hard_ware_serise_model.dart';
import 'package:text/pages/industry_template/home/<USER>/label_usage_record.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_state.dart';
import 'package:text/pages/industry_template/home/<USER>/custom_popup_menu.dart';
import 'package:text/pages/industry_template/select_label/device_series_switch_page.dart';
import 'package:text/pages/industry_template/select_label/hardware_list_model.dart';
import 'package:text/pages/industry_template/select_label/label_selector_page.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/hardware_manager.dart';
import 'package:text/utils/item_divider.dart';
import 'package:text/utils/precision_limit_formatter.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';

import '../../../../utils/cachedImageUtil.dart';
import '../../select_label/create_label_page.dart';

class IndustryModelSizePage extends StatefulWidget {
  const IndustryModelSizePage({Key? key}) : super(key: key);

  @override
  State<IndustryModelSizePage> createState() => _IndustryModelSizePageState();
}

class _IndustryModelSizePageState extends State<IndustryModelSizePage> {
  @override
  Widget build(BuildContext context) {
    return DefaultTextStyle(
      style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: ThemeColor.title),
      child: Column(
        children: [
          Expanded(
              child: Container(
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(bottomLeft: Radius.circular(12), bottomRight: Radius.circular(12)),
                    color: ThemeColor.background,
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 7),
                  child: Scrollbar(
                      child: SingleChildScrollView(
                    child: Column(
                        //动态创建一个List<Widget>
                        children: const [_PrinterModel(), _LabelSize(), _CommonSize()]),
                  )))),
          const _ConFirmButton()
        ],
      ),
    );
  }
}

class _PrinterModel extends StatefulWidget {
  const _PrinterModel({Key? key}) : super(key: key);

  @override
  State<_PrinterModel> createState() => _PrinterModelState();
}

class _PrinterModelState extends State<_PrinterModel> {
  final IndustryTemplateLogic logic = Get.find<IndustryTemplateLogic>();
  final IndustryTemplateState state = Get.find<IndustryTemplateLogic>().state;

  /// 机型选择
  _tapAction(
    BuildContext context,
    HardWareSeriesModel? model,
  ) {
    // 重置状态
    _resetState() {
      // 匹配当前的标签纸,防止之前因选纸界面的不匹配
      state.isLabelMatch.value = true;
      // 存在自定义尺寸需要置空，且选中全部尺寸
      if ((state.customWidth != 0 || state.customHeight != 0 || !_supportCheckedSize())) {
        state.sizeSelectedState.value = "all";
        state.size.value = Size.zero;
        // 置空自定义尺寸
        state.isNeedClearCustomSize.value = !(state.isNeedClearCustomSize());
      }
      // 刷新标签纸尺寸页以及机型选择页
      // logic.update(['labelSize']);

      if (state.isLabelSizeSelected) {
        state.size.value = Size(state.labelModel?.width.toDouble() ?? 0, state.labelModel?.height.toDouble() ?? 0);
        // 选中标签纸尺寸
        state.sizeSelectedState.value = "labelSize";
        // 检查标签纸是否匹配当前系列
        logic.checkLabelMatchHardWare(
            labelSupportDeviceSet: {...?(state.labelUseModel?.profile.machineId.split(',').toSet())});
      }
      setState(() {});
      logic.update(['labelSize']);
    }

    // 更新硬件模型
    state.hardWareSeriesModel.value = model;
    // 存储当前连接设备的系列ID（用于更换标签纸中默认选中的系列）
    Application.sp.setString(ConstantKey.latestHardwareSeriesId, model?.id ?? '');
    // 当前机型系列ID和选择的机型系列ID匹配且当前已识别到标签纸
    if (state.hardWareSeriesModel()?.id == state.hardwareSeriesModelUsing()?.id && state.labelConnectingModel != null) {
      // 更新标签纸模型, 连接的标签纸模型比历史记录的优先级更高
      state.labelUseModel = state.labelConnectingModel;
      if (state.isLabelSizeSelected) {
        // 当前标签纸尺寸被选择，则更新尺寸
        state.size.value = Size(state.labelModel?.width.toDouble() ?? 0, state.labelModel?.height.toDouble() ?? 0);
      }
      // 收起机型列表页
      if (MultipleTapGestureDetector.of(context) != null) {
        MultipleTapGestureDetector.of(context)!.onTap?.call();
      }
      // 重置状态
      _resetState();
      return;
    }
    // 筛选符合机型的历史记录标签纸
    ToNativeMethodChannel.sharedInstance().getUseRecentLabel(machineIds: model?.hardwareIdList).then((labeList) {
      try {
        if (labeList?.isNotEmpty ?? false) {
          // 存在符合机型的标签纸
          state.labelUseModel = labeList?.first;
          if (state.isLabelSizeSelected) {
            // 当前标签纸尺寸被选择，则更新尺寸
            state.size.value = Size(state.labelModel?.width.toDouble() ?? 0, state.labelModel?.height.toDouble() ?? 0);
          }
        } else {
          // 不存在符合机型的标签纸，默认选中全部, 尺寸与标签纸模型置空
          state.isLabelSizeSelected = false;
          state.isUsedLabelBackGround = false;
          state.labelUseModel = null;
          state.sizeSelectedState.value = "all";
          state.size.value = Size.zero;
        }
        // 重置状态
        _resetState();
      } catch (error) {}
    });
  }

  bool _supportCheckedSize() {
    bool result = false;
    for (var element in state.templateSizes) {
      if (state.seriesFirstDevice?.seriesId == element.id?.toString()) {
        for (var element in element.sizes!) {
          if (element.width == state.size().width && element.height == state.size().height) {
            result = true;
            break;
          }
        }
      }
    }
    return result;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsetsDirectional.only(start: 20),
      child: Column(
        children: [
          Row(
            children: [
              const SvgIcon(
                'assets/images/industry_template/home/<USER>',
                width: 19,
                height: 17,
              ),
              const SizedBox(
                width: 6,
              ),
              Text(intlanguage('app100000607', '打印机型号')),
              const Spacer(),
              buildSeriesSelectedPage()
            ],
          ),
          const ItemDivider(
            margin: EdgeInsets.only(right: 20),
          ),
        ],
      ),
    );
  }

  _showSeriesSelectedPage() {
    showDeviceSeriesSwitchPage() {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
        builder: (BuildContext context) {
          return DeviceSeriesSwitchPage(
            selectedSeries: state.hardWareSeriesModel()?.name ?? '拾光机',
            isNeedDisConnectHardWareAlert: state.hardwareSeriesModelUsing() != null,
            currentConnectSeriesModel: state.hardwareSeriesModelUsing.value,
          );
        },
      ).then((seriesModel) async {
        if (seriesModel is HardWareSeriesModel) {
          // 重复选择一个机型排重
          if (Application.sp.getString(ConstantKey.latestHardwareSeriesId) != seriesModel.id) {
            Application.sp.setString(ConstantKey.latestHardwareSeriesId, seriesModel.id ?? '');
            // 存储系列下首个设备name名称为选中型号
            HardwareModel? hardWareModel =
                HardWareManager.instance().findHardwareModel(machineId: seriesModel.hardwareIdList?.first ?? '');
            String machineName = hardWareModel?.name ?? '';
            if (machineName.contains('-')) {
              machineName = machineName.split('-').first;
            }
            // 存储当前连接设备的机型名称
            Application.sp.setString(ConstantKey.latestHardwareName, machineName);
            _tapAction(context, seriesModel);
          }
        }
      });
    }

    // 防止自定义尺寸焦点问题
    FocusManager.instance.primaryFocus?.unfocus();
    ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "011_192_188", "ext": {}});
    // 展示系列切换页
    showDeviceSeriesSwitchPage();
  }

  /// 系列挑选页
  Widget buildSeriesSelectedPage() {
    return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: _showSeriesSelectedPage,
        child: Container(
          padding: const EdgeInsets.fromLTRB(25, 8, 20, 15),
          child: Row(
            children: [
              Obx(() {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // 是否当前设备模型等于正在连接的硬件设备模型， 当前设备模型可能是历史选择、当前手动选择、默认B21
                    state.hardwareSeriesModelUsing()?.id != null
                        ? Text(intlanguage('app00198', '已连接'),
                            textAlign: TextAlign.right,
                            style:
                                const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
                            overflow: TextOverflow.ellipsis)
                        : const SizedBox(
                            width: 5,
                          ),
                    Text(
                        state.hardwareSeriesModelUsing()?.id != null
                            ? state.hardwareSeriesModelUsing()?.name ?? ""
                            : state.hardWareSeriesModel()?.name ?? '',
                        textAlign: TextAlign.right,
                        style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
                        overflow: TextOverflow.ellipsis),
                  ],
                );
              }),
              const SizedBox(
                width: 4,
              ),
              const SvgIcon(
                'assets/images/industry_template/home/<USER>',
                matchTextDirection: true,
              )
            ],
          ),
        ));
  }
}

class _LabelSize extends StatefulWidget {
  const _LabelSize({Key? key}) : super(key: key);

  @override
  State<_LabelSize> createState() => _LabelSizeState();
}

class _LabelSizeState extends State<_LabelSize> {
  final IndustryTemplateLogic logic = Get.find<IndustryTemplateLogic>();
  final IndustryTemplateState state = Get.find<IndustryTemplateLogic>().state;

  /// 打开选择标签纸
  _changeLabel() {
    // 防止自定义尺寸焦点问题
    FocusManager.instance.primaryFocus?.unfocus();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      builder: (BuildContext context) {
        return LabelSelectorPage();
      },
    ).then((value) {
      if (value != null) {
        if (value is Map && value.isEmpty) return;
        // 选中当前的标签纸
        state.labelUseModel = LabelUsageRecord.fromJson(value);
        state.isLabelSizeSelected = true;
        state.size.value = Size(state.labelModel?.width.toDouble() ?? 0, state.labelModel?.height.toDouble() ?? 0);
        state.sizeSelectedState.value = "labelSize";
        // 刷新标签纸尺寸页
        logic.update(['labelSize']);
        // 检查标签纸是否匹配当前系列
        state.hasSelectLabelByUser = true;
      }
      // 判断当前系列ID和标签纸选择页的系列ID是否一致，不一致需获取标签纸选择页选择的系列下的第一个机型作为选中机型
      String? latestHardwareSeriesId = Application.sp.getString(ConstantKey.latestHardwareSeriesId) ?? '';
      if (state.hardWareSeriesModel()?.id != latestHardwareSeriesId) {
        state.hardWareSeriesModel.value =
            HardWareManager.instance().findHardwareSeriesModel(seriesId: latestHardwareSeriesId);
      }

      logic.checkLabelMatchHardWare(
          labelSupportDeviceSet: {...?(state.labelUseModel?.profile.machineId.split(',').toSet())});
    });
  }

  /// 标签纸点击事件
  _tapLabelSize() {
    // 标签纸选中后不可取消，必须存在筛选尺寸
    if (state.isLabelSizeSelected) return;
    state.isLabelSizeSelected = !state.isLabelSizeSelected;
    if (state.isLabelSizeSelected) {
      state.size.value = Size(state.labelModel?.width.toDouble() ?? 0, state.labelModel?.height.toDouble() ?? 0);
      // 选中标签纸尺寸
      state.sizeSelectedState.value = "labelSize";
      // 检查标签纸是否匹配当前系列
      logic.checkLabelMatchHardWare(
          labelSupportDeviceSet: {...?(state.labelUseModel?.profile.machineId.split(',').toSet())});
    }
    setState(() {});
    logic.update(['labelSize']);
  }

  /// 使用当前标签纸背景
  _tapLabelBackGround() {
    // 更新状态
    state.isUsedLabelBackGround = !state.isUsedLabelBackGround;
    if (state.isUsedLabelBackGround) {
      // 选中标签纸并刷新
      // 更新当前标签纸为尺寸进行筛选
      // 默认选中此标签纸尺寸,更新尺寸信息
      state.isLabelSizeSelected = true;
      state.size.value = Size(state.labelModel?.width.toDouble() ?? 0, state.labelModel?.height.toDouble() ?? 0);
      state.sizeSelectedState.value = "labelSize";
      // 检查标签纸是否匹配当前系列
      logic.checkLabelMatchHardWare(
          labelSupportDeviceSet: {...?(state.labelUseModel?.profile.machineId.split(',').toSet())});
      // 更新标签纸
      logic.update(['labelSize']);
    }
    setState(() {});
    // 埋点数据
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "011_192_190",
      "ext": {'state': state.isUsedLabelBackGround ? 1 : 0}
    });
  }

  Widget _content() {
    // 存在标签纸的情况下
    if (state.isHaveLabel) {
      return Container(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(
              height: 16,
            ),
            Row(
              children: [
                SizedBox(
                  width: 20,
                ),
                Expanded(child: Text(intlanguage('app100000609', '使用当前标签纸的尺寸') + '(mm)')),
                // const Spacer(),
                Expanded(
                  child: GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        ToNativeMethodChannel()
                            .sendTrackingToNative({"track": "click", "posCode": "011_192_191", "ext": {}});
                        _changeLabel();
                      },
                      child: Container(
                        height: 30,
                        padding: const EdgeInsetsDirectional.only(end: 20),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            const SizedBox(
                              width: 30,
                            ),
                            Text(
                              intlanguage('app01589', '更换'),
                              style: const TextStyle(
                                  fontSize: 13, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
                            ),
                            const SizedBox(
                              width: 4,
                            ),
                            const SvgIcon(
                              'assets/images/industry_template/home/<USER>',
                              matchTextDirection: true,
                            )
                          ],
                        ),
                      )),
                )
              ],
            ),
            Container(
              padding: const EdgeInsetsDirectional.only(start: 20, end: 20),
              child: Column(
                children: [
                  const SizedBox(
                    height: 9,
                  ),
                  // 标签纸信息显示
                  _labelSize(),
                  const SizedBox(
                    height: 8,
                  ),
                ],
              ),
            ),
            // 标签纸背景
            // _labelBackground(),
            Container(
              padding: const EdgeInsetsDirectional.only(start: 20, end: 20),
              child: Column(
                children: [
                  const SizedBox(
                    height: 6,
                  ),
                  // 不匹配的时候展示
                  _labelUnmatch(state.isLabelMatch()),
                ],
              ),
            ),
          ],
        ),
      );
    } else {
      // 不存在标签纸的情况下
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(
              height: 16,
            ),
            Row(
              children: [
                Expanded(child: Text(intlanguage('app100000609', '使用当前标签纸的尺寸') + '(mm)')),
              ],
            ),
            const SizedBox(
              height: 9,
            ),
            selectLabel()
          ],
        ),
      );
    }
  }

  /// 挑选标签纸确定尺寸
  Widget selectLabel() {
    return GestureDetector(
      onTap: () {
        // 选择标签纸
        _changeLabel();
      },
      child: Container(
        width: MediaQuery.sizeOf(context).width - 40,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), color: ThemeColor.listBackground),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Padding(
              padding: EdgeInsets.only(top: 2.0),
              child: SvgIcon(
                'assets/images/industry_template/home/<USER>',
                // width: 17,
                // height: 19,
              ),
            ),
            const SizedBox(
              width: 6,
            ),
            SizedBox(
              width: MediaQuery.sizeOf(context).width - 106,
              child: Text(
                intlanguage('app100000610', '通过选择标签纸确定尺寸'),
                style: const TextStyle(fontSize: 13, color: ThemeColor.COLOR_595959),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const Spacer(),
            const Padding(
              padding: EdgeInsetsDirectional.only(top: 2.0),
              child: SvgIcon(
                'assets/images/industry_template/home/<USER>',
                // width: 12,
                // height: 8,
                matchTextDirection: true,
              ),
            )
          ],
        ),
      ),
    );
  }

  /// 标签纸尺寸信息显示
  Widget _labelSize() {
    return GestureDetector(
      onTap: () {
        // 埋点
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "click",
          "posCode": "011_192_189",
          "ext": {'state': logic.getPaperStateForTrack()}
        });
        _tapLabelSize();
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          border:
              state.isLabelSizeSelected ? Border.all(color: ThemeColor.brand) : Border.all(color: Colors.transparent),
          color: state.isLabelSizeSelected ? ThemeColor.COLOR_FFEDEC : ThemeColor.COLOR_F5F5F5,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 9),
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), color: ThemeColor.COLOR_FAFAFA),
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 12),
              child: CacheImageUtil().netCacheImage(
                imageUrl: state.labelModel?.backgroundImage ?? '',
                width: 39,
                height: 24,
                errorWidget: const SvgIcon('assets/images/label_create/label_placeholder.svg'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    state.labelModel?.labelName ?? '',
                    style: const TextStyle(color: ThemeColor.title, fontSize: 14, fontWeight: FontWeight.w600),
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(
                    height: 4,
                  ),
                  Row(
                    children: [
                      Text(
                        '${state.labelModel?.intWidth}x${state.labelModel?.intHeight}mm',
                        style: TextStyle(
                            color: state.isLabelSizeSelected ? ThemeColor.brand : ThemeColor.title,
                            fontSize: 14,
                            fontWeight: FontWeight.w600),
                      ),
                      SizedBox(
                        width: 2,
                      ),
                      _labelStateWidget()
                    ],
                  )
                ],
              ),
            ),
            // const Spacer(),
          ],
        ),
      ),
    );
  }

  _labelStateWidget() {
    if (state.hasSelectLabelByUser && state.labelConnectingModel?.id != state.labelModel?.id) {
      return _sizeStatusTips(intlanguage('app100000496', '选择标签纸'), ThemeColor.subtitle, Color.fromARGB(20, 0, 0, 0));
    } else if (state.labelConnectingModel != null && state.labelConnectingModel?.id == state.labelModel?.id) {
      return _sizeStatusTips(
          intlanguage('app100000683', '当前识别'), ThemeColor.COLOR_17CB7B, Color.fromARGB(20, 23, 203, 123));
    } else {
      return _sizeStatusTips(intlanguage('app100000567', '上次使用'), ThemeColor.subtitle, Color.fromARGB(20, 0, 0, 0));
    }
  }

  Widget _sizeStatusTips(String txt, Color txtColor, Color bgColor) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 5),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(4), color: bgColor),
      child: Text(
        txt,
        style: TextStyle(color: txtColor, fontSize: 10, fontWeight: FontWeight.w600),
        strutStyle: const StrutStyle(
            fontSize: 10, leading: 0, fontWeight: FontWeight.w600, height: 1.1, forceStrutHeight: true),
      ),
    );
  }

  /// 是否应用当前标签纸背景
  Widget _labelBackground() {
    return Row(
      children: [
        GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: _tapLabelBackGround,
            child: Container(
              padding: const EdgeInsetsDirectional.only(start: 20, end: 8),
              width: MediaQuery.sizeOf(context).width - 28,
              // height: 30,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 2.0),
                    child: SvgIcon(state.isUsedLabelBackGround
                        ? 'assets/images/industry_template/home/<USER>'
                        : 'assets/images/industry_template/home/<USER>'),
                  ),
                  const SizedBox(
                    width: 4,
                  ),
                  Flexible(
                      child: Text(
                    intlanguage('app100000633', '使用当前标签纸为背景'),
                    style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  )),
                ],
              ),
            )),
      ],
    );
  }

  Widget _labelUnmatch(bool isLabelMatch) {
    return !isLabelMatch
        ? Row(
            children: [
              const Padding(
                  padding: EdgeInsetsDirectional.only(end: 3),
                  child: SvgIcon(
                    'assets/images/industry_template/home/<USER>',
                    width: 16,
                    height: 16,
                  )),
              Expanded(
                  child: Text(
                intlanguage('app100000632', '当前标签纸与所选机型不匹配'),
                style: const TextStyle(color: ThemeColor.brand, fontWeight: FontWeight.w400, fontSize: 14),
              )),
            ],
          )
        : const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<IndustryTemplateLogic>(
        id: 'labelSize',
        builder: (logic) {
          return _content();
        });
  }
}

class _CommonSize extends StatefulWidget {
  const _CommonSize({Key? key}) : super(key: key);

  @override
  State<_CommonSize> createState() => _CommonSizeState();
}

class _CommonSizeState extends State<_CommonSize> {
  final IndustryTemplateLogic logic = Get.find<IndustryTemplateLogic>();
  final IndustryTemplateState state = Get.find<IndustryTemplateLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(
            height: 24,
          ),
          Text(intlanguage('app100000611', '常用尺寸') + '(mm)'),
          const SizedBox(
            height: 8,
          ),
          Obx(() {
            return Wrap(
              spacing: 8,
              runSpacing: 10,
              children: [
                _SizeItem(
                  text: intlanguage('app01101', '全部'),
                  isSelected: !state.isLabelSizeSelected && state.sizeSelectedState == "all",
                  selected: "all",
                  canInput: false,
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 自定宽度
                    _SizeItem(
                      text: intlanguage('app100000612', '自定宽度'),
                      selected: "customWidth",
                      isSelected: state.sizeSelectedState == "customSize",
                      canInput: true,
                    ),
                    const SizedBox(
                      width: 8,
                    ),
                    // 乘
                    const SvgIcon(
                      'assets/images/industry_template/home/<USER>',
                      width: 13,
                      height: 16,
                    ),
                    const SizedBox(
                      width: 8,
                    ),
                    // 自定高度
                    _SizeItem(
                      text: intlanguage('app100000635', '自定高度'),
                      selected: "customHeight",
                      isSelected: state.sizeSelectedState == "customSize",
                      canInput: true,
                    ),
                  ],
                ),
                Wrap(
                  spacing: 8,
                  runSpacing: 10,
                  children: _SizeListWidget(),
                )
              ],
            );
          })
        ],
      ),
    );
  }

  _SizeListWidget() {
    var widgets = <Widget>[];
    for (var element in state.templateSizes) {
      if (state.seriesFirstDevice?.seriesId == element.id?.toString()) {
        for (var element in element.sizes!) {
          widgets.add(
            _SizeItem(
              text: '${element.width!.toInt()}x${element.height!.toInt()}',
              isSelected: state.sizeSelectedState == '${element.width!.toInt()}x${element.height!.toInt()}',
              selected: '${element.width!.toInt()}x${element.height!.toInt()}',
              canInput: false,
              width: element.width!.toInt(),
              height: element.height!.toInt(),
            ),
          );
        }
      }
    }
    return widgets;
  }
}

class _SizeItem extends StatefulWidget {
  /// 显示文本，如果是空则显示输入
  final String? text;

  final bool canInput;
  int width, height;

  String selected;

  /// 默认不选中
  bool isSelected;

  _SizeItem(
      {Key? key,
      this.text,
      required this.selected,
      this.isSelected = false,
      this.canInput = false,
      this.width = 0,
      this.height = 0})
      : super(key: key);

  @override
  State<_SizeItem> createState() => _SizeItemState();
}

class _SizeItemState extends State<_SizeItem> {
  final IndustryTemplateLogic logic = Get.find<IndustryTemplateLogic>();
  final IndustryTemplateState state = Get.find<IndustryTemplateLogic>().state;

  final FocusNode _nodeText = FocusNode();

  final TextEditingController _textEditingController = TextEditingController();

  Worker? worker;

  KeyboardActionsConfig _buildConfig() {
    return KeyboardActionsConfig(
      keyboardBarColor: Colors.grey[200],
      nextFocus: false,
      actions: [
        KeyboardActionsItem(
          focusNode: _nodeText,
          onTapAction: () {
            // 两者不为空，更新尺寸选中状态
            if (state.customWidth != 0 && state.customHeight != 0) {
              state.isLabelSizeSelected = false;
              state.isUsedLabelBackGround = false;
              state.sizeSelectedState.value = "customSize";
              logic.update(['labelSize']);
            }
          },
        )
      ],
      defaultDoneWidget: Text(
        intlanguage('app01031', '完成'),
        style: const TextStyle(
          color: ThemeColor.COLOR_4676EE,
          fontSize: 15.0,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _inputTextField() {
    return KeyboardActions(
      // tapOutsideBehavior: TapOutsideBehavior.translucentDismiss,
      config: _buildConfig(),
      child: CupertinoTextField(
          focusNode: _nodeText,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.title),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: widget.isSelected ? ThemeColor.COLOR_FFEDEC : ThemeColor.listBackground),
          placeholder: widget.text,
          placeholderStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
          controller: _textEditingController,
          textAlign: TextAlign.center,
          textInputAction: TextInputAction.done,
          keyboardType: const TextInputType.numberWithOptions(signed: false, decimal: true),
          cursorColor: ThemeColor.brand,
          inputFormatters: [
            PrecisionLimitFormatter(2, max: CreateLabelPage.MAX_TEMPLATE_SIZE),
          ],
          onTap: () {
            setState(() {
              state.sizeSelectedState.value = "customSize";
            });
          },
          onChanged: (value) {
            if (widget.selected == "customWidth") {
              state.customWidth = value.isEmpty ? 0 : double.parse(value);
            } else {
              state.customHeight = value.isEmpty ? 0 : double.parse(value);
            }
            state.size.value = Size(state.customWidth, state.customHeight);
          }),
    );
  }

  _tapAction() {
    switch (widget.selected) {
      case "all":
        state.size.value = Size.zero;
        state.sizeSelectedState.value = widget.selected;
        break;
      case "customWidth":
      case "customHeight":
        state.size.value = Size.zero;
        state.sizeSelectedState.value = "customSize";
        break;
      default:
        state.size.value = Size(widget.width.toDouble() ?? 0, widget.height.toDouble() ?? 0);
        state.sizeSelectedState.value = widget.selected;
        break;
    }
    if (state.isLabelSizeSelected) {
      // 标签纸尺寸正在选中
      state.isLabelSizeSelected = false;
      // 刷新标签纸尺寸页
      logic.update(['labelSize']);
    }
    // 更新标签纸匹配, 并非当前的标签纸和机型匹配，只是作为筛选条件时，只有选中不匹配的标签纸才展示
    state.isLabelMatch.value = true;
    // 埋点
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "011_192_192",
      "ext": {'b_name': widget.text}
    });
  }

  @override
  void initState() {
    switch (widget.selected) {
      case "customWidth":
        _textEditingController.text = state.customWidth == 0 ? '' : state.customWidth.toString();
        break;
      case "customHeight":
        _textEditingController.text = state.customHeight == 0 ? '' : state.customHeight.toString();
        break;
      default:
        break;
    }

    if (widget.selected == "customWidth" || widget.selected == "customHeight") {
      // 监听是否需要重置数据
      worker = everAll([state.isNeedClearCustomSize, state.sizeSelectedState], (callBack) {
        // 非自定义尺寸或者需要置空
        if ((callBack is String && callBack != "customSize") || callBack is bool) {
          setState(() {
            state.customWidth = 0;
            state.customHeight = 0;
            _textEditingController.text = '';
            FocusManager.instance.primaryFocus?.unfocus();
          });
        }
      });
    }
    super.initState();
  }

  @override
  void dispose() {
    worker?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _tapAction,
      child: Container(
        width: (context.width - 80) / 3,
        height: 36,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            color: widget.isSelected ? ThemeColor.COLOR_FFEDEC : ThemeColor.COLOR_F5F5F5,
            border: Border.all(color: widget.isSelected ? ThemeColor.brand : Colors.transparent, width: 1.0)),
        child: Center(
          child: widget.canInput
              ? _inputTextField()
              : Text(
                  widget.text ?? '',
                  style: TextStyle(
                      fontWeight: FontWeight.w400, color: widget.isSelected ? ThemeColor.brand : ThemeColor.title),
                ),
        ),
      ),
    );
  }
}

class _ConFirmButton extends StatefulWidget {
  const _ConFirmButton({Key? key}) : super(key: key);

  @override
  State<_ConFirmButton> createState() => _ConFirmButtonState();
}

class _ConFirmButtonState extends State<_ConFirmButton> {
  final IndustryTemplateLogic logic = Get.find<IndustryTemplateLogic>();
  final IndustryTemplateState state = Get.find<IndustryTemplateLogic>().state;

  /// 确认机型与尺寸
  _tapAction() {
    logic.confirmAction();
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "011_192_193",
      "ext": {'type1': state.hardWareWithSizeName.value}
    });
    if (state.sizeSelectedState.value == "customSize") {
      ToNativeMethodChannel().sendTrackingToNative({
        "track": "click",
        "posCode": "011_192_192",
        "ext": {'b_name': "${state.customWidth}x${state.customHeight}"}
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 68,
      decoration: BoxDecoration(
          borderRadius: const BorderRadius.only(bottomLeft: Radius.circular(12), bottomRight: Radius.circular(12)),
          color: ThemeColor.background,
          boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.08), blurRadius: 19, offset: const Offset(0, -4))]),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: GestureDetector(
        onTap: _tapAction,
        child: Container(
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(12), color: ThemeColor.brand),
          child: Center(
              child: Text(
            intlanguage('app00048', '确定'),
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: ThemeColor.background),
          )),
        ),
      ),
    );
  }
}
