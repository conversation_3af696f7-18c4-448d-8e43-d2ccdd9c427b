import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:nety/models/niimbot_bluetooth_printer.dart';
import 'package:nety/models/niimbot_printer.dart';
import 'package:nety/models/niimbot_rfid_info.dart';
import 'package:nety/models/niimbot_wifi_printer.dart';
import 'package:nety/models/sdk/index.dart';
import 'package:nety/models/sdk/sdk_result.dart';
import 'package:nety/nety.dart';
import 'package:nety/nety_mobile_binding.dart';
import 'package:nety/store.dart';
import 'package:niimbot_print_setting_plugin/extensions/change_notifer.dart';
import 'package:niimbot_print_setting_plugin/extensions/niimbot_printer.dart';
import 'package:niimbot_print_setting_plugin/print/printer_strategy_manager.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart';
import 'package:niimbot_print_strategy/niimbot_print_strategy.dart';
import 'package:niimbot_template/models/elements/date_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/template_parse.dart';
import 'package:niimbot_flutter_canvas/src/utils/canvas_helper.dart';
import 'package:niimbot_flutter_canvas/src/utils/print_channel.dart';
import 'package:niimbot_flutter_canvas/src/utils/rfid_util.dart';
import 'package:text/application.dart';
import 'package:text/business/app/app_config_manager.dart';
import 'package:text/business/print/print_log_business.dart';
import 'package:text/database/printLog/print_data_log_db_utils.dart';
import 'package:text/database/printLog/print_data_log_model.dart';
import 'package:text/model/print_device_info.dart';
import 'package:text/model/print_record_require_info.dart';
import 'package:text/model/print_strategy.dart';
import 'package:text/model/service_rfid_info.dart';
import 'package:text/pages/industry_template/select_label/hardware_list_model.dart';
import 'package:text/riskShield/risk_shield_helper.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/hardware_manager.dart';
import 'package:text/utils/print_setting_channel.dart';

import '../log_utils.dart';

enum RiskRequestCode {
  APPSTART(0),
  APPLOGIN(1),
  PRINT(5),
  BEFOREPRINT(4),
  COVER(2),
  Connected(3);

  final int value;

  const RiskRequestCode(this.value);

  static RiskRequestCode? byValue(int value) {
    return RiskRequestCode.values.firstWhereOrNull((e) => e.value == value);
  }
}

class RfidManager {
  static RfidManager? _instance;

  RfidManager._internal();

  static RfidManager get instance {
    _instance ??= RfidManager._internal();
    return _instance!;
  }

  /** 支持 RFID 标签 */
  static const int PRINTER_RFID_PAPER = 1;

  /** 支持碳带 */
  static const int PRINTER_RFID_CARD = 2;

  /** 支持 RFID + 碳带 */
  static const int PRINTER_RFID_PAC = 3;

  /******* 打印策略 *********/
// 清晰打印
  static const int STATUS_CLEAR = 0;

// 模糊打印(按打印策略打印)
  static const int STATUS_FUZZY = 1;

  static PDeviceInfo? connectedDevice;
  static PDeviceInfo? caCheDevice; // 用于打印日志，即使中间发生断联等情况时，使用caCheDevice依然能获取到之前的打印机信息
  static ServiceRfidInfo serviceRfidInfo = ServiceRfidInfo();
  static TemplateData? rfidTemplateData; // 当前 RFID 标签纸信息
  PrintStrategy? printStrategy = null;
  bool sdkHasRFIDLabel = false;

  var printRecordRequireInfo = PrintRecordRequireInfo();

  // 用于存储非法 RFID 值的 Map
  final Map<String, NiimbotRFIDInfo> illegalRfidMaps = HashMap();

  /**
   * RFID耗材信息
   */
  static List<NiimbotRFIDInfo> sdkRFIDLabelInfos = [];

  /// 用于打印日志，即使中间发生断联等情况时，使用cachedSdkRFIDLabelInfos依然能获取到之前的打印机信息
  static List<NiimbotRFIDInfo> cachedSdkRFIDLabelInfos = [];

  Function? updateDeviceSettingListener;
  Function(Color?)? updateC1RibbonColorListener;

  void refreshConnectDevice(NiimbotPrinter device) {
    this.printStrategy = null;
    // 为连接设备定义一个初始值
    connectedDevice = _initializeConnectedDevice(device);
    caCheDevice = connectedDevice;
    // ToNativeMethodChannel().setConnectDevice(connectedDevice?.toJson() ?? {});
  }

  /// 检查设备是否支持别名功能
  ///
  /// 支持别名的条件：
  /// 1. 设备已连接且信息完整
  /// 2. 设备不在不支持别名的黑名单中
  /// 3. iOS平台需要有效的MAC地址
  /// 4. 设备名称与机器ID匹配
  ///
  /// Returns: true if device supports alias, false otherwise
  bool checkSupportAlias() {
    // 前置条件检查：确保设备已连接
    if (connectedDevice == null) {
      return false;
    }

    // 获取当前连接的打印机信息
    NiimbotPrinter? printer = NiimbotPrintSDK().store.connectedPrinter;
    if (printer == null) {
      return false;
    }

    // 提取设备关键信息
    String sn = printer.sn ?? ""; // 设备序列号
    String name = printer.name ?? ""; // 搜索到的蓝牙/WiFi设备名称
    String machineId = connectedDevice!.deviceName; // 系列+序号拼接的设备名称

    // 检查设备信息完整性
    if (sn.isEmpty || name.isEmpty || machineId.isEmpty) {
      return false;
    }

    // 检查设备是否在不支持别名的黑名单中
    List<String> notSupportAliasList = AppConfigManager().getNotSupportAliasList();
    if (notSupportAliasList.isNotEmpty && notSupportAliasList.contains(name)) {
      return false;
    }

    // iOS平台特殊检查：需要有效的MAC地址
    if (Platform.isIOS) {
      String? macAddress = printer.macAddress;
      if (macAddress == null || macAddress.isEmpty) {
        return false;
      }
    }

    // 最终验证：设备名称必须与机器ID匹配
    // 这确保了设备的真实性和一致性
    return name == machineId;
  }

  refreshRfidTemplateData(Map<String, dynamic> templateData) {
    TemplateParse.parseFromMap(templateData).then((data) {
      rfidTemplateData = data;
    });
  }

  PDeviceInfo? _initializeConnectedDevice(NiimbotPrinter device) {
    HardwareModel? printerModel;
    String deviceName = device.name ?? "";

    if (device is NiimbotBluetoothPrinter) {
      printerModel = getCurrentPrinterInfo(device.code.toString(), deviceName);
      return _mapDeviceInfo(device, printerModel, device.mac);
    } else if (device is NiimbotWIFIPrinter) {
      printerModel = getCurrentPrinterInfo(device.code.toString(), deviceName);
      return _mapDeviceInfo(device, printerModel, device.ip);
    }

    return null;
  }

  String RFIDLabetoJson() {
    List<Map<String, dynamic>> jsonList = sdkRFIDLabelInfos
        .map((info) => {
              'uuid': info.uuid,
              'barcode': info.barcode,
              'serialNumber': info.uuid,
              'allPaperMeters': info.allPaperMeters,
              'usedPaperMeters': info.usedPaperMeters,
              'type': info.type,
              'state': info.state,
            })
        .toList();
    return jsonEncode(jsonList);
  }

  PDeviceInfo _mapDeviceInfo(NiimbotPrinter device, HardwareModel? printerModel, String address) {
    final connectedDevice = PDeviceInfo().mapFields(device, printerModel);
    connectedDevice.address = address;
    String realyDeviceName = PrintSettingChannel()
        .getCurrentPrinterMachineId((device.code ?? 0).toString(), device.name ?? "", device.sn ?? "");
    if (device is NiimbotBluetoothPrinter) {
      connectedDevice.deviceName = realyDeviceName;
      connectedDevice.isWifi = 0;
    } else if (device is NiimbotWIFIPrinter) {
      connectedDevice.deviceName = realyDeviceName;
      connectedDevice.isWifi = 1;
    }
    return connectedDevice;
  }

  refreshSdkRfidInfos(int coverStates, {bool isRibbonStates = false}) async {
    //开盒盖时 RFID请求
    if (coverStates == 0) {
      if (isRibbonStates) {
        sdkRFIDLabelInfos.removeWhere((element) {
          return (element.type == 6 || element.type == 8);
        });
      } else {
        sdkRFIDLabelInfos = [];
      }
    } else {
      sdkRFIDLabelInfos = NiimbotPrintSDK().store.connectedPrinter?.consumablesRFID ?? [];
      cachedSdkRFIDLabelInfos.clear();
      cachedSdkRFIDLabelInfos.addAll(sdkRFIDLabelInfos);
      _refreshRFIDColorsInfo(true);
    }
    ToNativeMethodChannel().setSDKRfidData(RFIDLabetoJson());
  }

  watchSdkRfid() {
    NiimbotPrintSDK().store.watch<NiimbotPrintSDKStore, NiimbotPrinter?>((v) => v.connectedPrinter,
        (oldVal, newVal) async {
      if (newVal != null && oldVal != null) {
        //连接成功时RFID请求完成
        if (oldVal.consumablesRFID == null && newVal.consumablesRFID != null) {
          sdkRFIDLabelInfos = newVal.consumablesRFID ?? [];
          ToNativeMethodChannel().setSDKRfidData(RFIDLabetoJson());
          _refreshRFIDColorsInfo(true);
        }
      } else {
        if (oldVal != newVal) {
          sdkRFIDLabelInfos = newVal?.consumablesRFID ?? [];
          if (newVal == null) {
            _refreshRFIDColorsInfo(false);
            ToNativeMethodChannel().setSDKRfidData(RFIDLabetoJson());
          } else if (oldVal == null) {
            PrinterStrategyManager().setPrintStrategyDeviceInfo(PrintSettingChannel());
          }
        }
      }

      ///打印已准备时，缓存rfid信息
      if (oldVal?.progressInfo?.status.index == 1 && oldVal?.status.index == 3) {
        cachedSdkRFIDLabelInfos.addAll(sdkRFIDLabelInfos);
      }
    });
  }

  bool checkUuidMatch(List<NiimbotRFIDInfo> oldRfid, List<NiimbotRFIDInfo> newRfid) {
    // 提取 oldRfid 和 newRfid 中的所有 uuid
    var oldUuids = oldRfid.map((item) => item.uuid).toList();
    var newUuids = newRfid.map((item) => item.uuid).toList();

    // 检查两个 uuid 列表是否具有相同的长度和相同的元素
    if (oldUuids.length != newUuids.length) {
      return false; // 长度不同，直接返回 false
    }

    // 使用 Dart 的内置集合操作来检查两个列表是否相等
    var oldUuidSet = oldUuids.toSet();
    var newUuidSet = newUuids.toSet();

    return oldUuidSet.containsAll(newUuidSet);
  }

  _refreshRFIDColorsInfo(bool isConnected) {
    if (!isConnected) {
      refreshRfidColorWith('', '');
    } else {
      PrinterStrategyManager().getRFIDStrategy(GetSecurityScene.connectDevice, rfidInfos: sdkRFIDLabelInfos,
          serverInfoCallback: (serverInfo) {
        Map<String, String> colorsInfo = {};
        colorsInfo["paperColor"] = serverInfo["paperColor"] ?? "";
        colorsInfo["ribbonColor"] = serverInfo["ribbonColor"] ?? "";
        refreshRfidColorWith(colorsInfo['paperColor']!, colorsInfo['ribbonColor']!);
      });
    }
  }

  getCurrentPrinterInfo(String hardCode, String printerName) {
    HardwareModel? printerModel;
    List<HardwareModel> printerModels =
        HardWareManager.instance().findHardwareDetailModelWithHardCode(hardCode: hardCode);
    String printerType;
    if (printerName.contains('-')) {
      printerType = printerName.split('-').first;
    } else if (printerName.contains('_')) {
      printerType = printerName.split('_').first;
    } else {
      printerType = printerName;
    }
    if (printerModels.isEmpty) {
      printerModel = HardWareManager.instance().findHardwareModelContainChildrenPrintBy(machineName: printerType);
    } else {
      if (printerModels.length == 1) {
        //根据硬件编号搜索到一个设备则直接返回
        printerModel = printerModels.first;
      } else {
        //根据硬件编号搜索到多个设备 则匹配设备名称跟蓝牙编号分割机型是否一致，匹配不到则取第一个设备
        var printers = printerModels.where((printerModel) => printerModel.name == printerType);
        printerModel = printers.isNotEmpty ? printers.first : printerModels.first;
      }
    }
    return printerModel;
  }

  Future<void> uploadPrintInfo(String uniqueValue,
      {required TemplateData template,
      required bool isCloudTemplate,
      required int printCount,
      required int lastPrintCount,
      required double printCardPaperLength,
      required double lastRibbonLength,
      required String code,
      required int printType,
      required int commodityCount,
      required PrintTaskType taskType,
      String uniappId = '',
      bool isConnected = false,
      String printStrategy = '',
      String illegalCode = '',
      Function? function}) async {
    if (printCount == 0) {
      debugPrint('RFIDConnectionProxy: upLoadPrintInfo, printCount = 0');
      function?.call();
      return;
    }
    final printRecordBean = PrintDataLogModel();
    printRecordBean.printFinishTime = DateTime.now().millisecondsSinceEpoch;
    printRecordBean.printType = printType;
    printRecordBean.commodityCount = commodityCount;
    printRecordBean.status = 1;
    printRecordBean.systemVersion = Application.systemVersion;
    printRecordBean.phoneBrand = Application.phone;
    var featureCode = CanvasHelper.printChannelCode?.featureCode;
    if (uniappId.isNotEmpty) {
      printRecordBean.printChannel = 'uniapp_${uniappId}';
    } else if (taskType == PrintTaskType.batch) {
      printRecordBean.printChannel = 'mytemplate_quick';
    } else if (featureCode != null && featureCode.isNotEmpty) {
      printRecordBean.printChannel = 'uniapp_${featureCode}';
    } else if (taskType == PrintTaskType.printSceneHistory) {
      printRecordBean.printChannel = 'uniapp_printRecord';
    }

    debugPrint("RFIDConnectionProxy: printChannel：${printRecordBean.printChannel}");
    printRecordBean.userId = Application.user?.userId;

    requestDeviceVersionInfo();

    if (template != null) {
      if (template.id == null || template.id!.length > 10) {
        printRecordBean.templeteId = "0";
      } else {
        printRecordBean.templeteId = template.id;
      }
      printRecordBean.sourceId = template.profile.extra.sourceId ?? "";
      printRecordBean.cloudTemplateId = template.cloudTemplateId;
      printRecordBean.width = "${template.width}";
      printRecordBean.height = "${template.height}";
    }

    printRecordBean.number = '${printCount}|${double2StringByPoint(printCardPaperLength, 2)}';
    debugPrint("RFIDConnectionProxy: 当前打印张数：${printRecordBean.number}");
    printRecordBean.systemType = Platform.isAndroid ? "2" : "1";
    printRecordBean.addTime = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());
    printRecordBean.printStyle = connectedDevice?.isWifi == 1 ? "2" : "1"; // '打印方式:1-蓝牙,2-局域网,3-云端,4-数据线',
    printRecordBean.hardwareVersion = caCheDevice?.hardwareVersion;
    printRecordBean.firmwareVersion = caCheDevice?.softwareVersion;
    printRecordBean.deviceRegionCode = caCheDevice?.printArea;
    printRecordBean.applicationVersion = Application.appVersion;
    printRecordBean.uniqueValue = uniqueValue;

    printRecordBean.ribbonUsed = printCardPaperLength * 10;
    printRecordBean.recordSource = 1;
    printRecordBean.isCloudTemplate = isCloudTemplate;
    printRecordBean.oneCode = code;
    printRecordBean.machineId = caCheDevice?.deviceName;
    printRecordBean.userId = Application.user?.userId;
    printRecordBean.city = "";
    printRecordBean.country = "";
    printRecordBean.district = "";
    printRecordBean.latitude = "";
    printRecordBean.longitude = "";
    printRecordBean.province = "";
    printRecordBean.street = "";
    printRecordBean.allTimes = "0|0";
    printRecordBean.successTimes = "0|0";
    printRecordBean.paperLengthUsedQuantity = "0";
    printRecordBean.paperLengthUsedQuantitySum = "0";
    printRecordBean.deviceId = Application.deviceId;
    if (Platform.isAndroid) {
      printRecordBean.device_id_dot = await ToNativeMethodChannel().getAnonymousId();
    }

    printRecordBean.macNo = caCheDevice?.address;
    printRecordBean.machineStatus = checkDeviceSupportRFID();
    printRecordBean.recordType = checkDeviceSupportRFID() ? 1 : 0;

    ///传过来的是毫米需要转换成厘米
    printCardPaperLength = printCardPaperLength / 10;
    if (checkDeviceSupportRFID()) {
      var rfidPaper = sdkRFIDLabelInfos.firstWhereOrNull((it) => it.isValid() && !it.isRibbon());
      var rfidRibbon = sdkRFIDLabelInfos.firstWhereOrNull((it) => it.isValid() && it.isRibbon());
      // var rfidReadTimesList = await getRfidReadNumber();
      if (isSupportPAC()) {
        printRecordBean.number = '${printCount}|${double2StringByPoint(printCardPaperLength, 2)}';
        printRecordBean.rfidPrintNumber =
            '${rfidPaper != null ? (lastPrintCount + rfidPaper.usedPaperMeters).toInt() : 0}|' +
                '${rfidRibbon != null ? double2StringByPoint(lastRibbonLength / 10 + rfidRibbon.usedPaperMeters, 2) : 0}';
        printRecordBean.rfidSerialNumber = "${getCachedPaperSerial(rfidPaper)}|${getCachedRibbonSerial(rfidRibbon)}";

        // printRecordBean.successTimes = '${rfidReadTimesList[1]}|${rfidReadTimesList[1]}';
        // printRecordBean.allTimes = '${rfidReadTimesList[0]}|${rfidReadTimesList[0]}';
      } else if (isSupportCard()) {
        printRecordBean.number = '${printCount}|${double2StringByPoint(printCardPaperLength, 2)}';
        // if (sdkHasRFIDLabel) {
        //   printRecordBean.rfidPrintNumber =
        //       "0|${rfidRibbon != null ? double2StringByPoint(lastRibbonLength / 10 + rfidRibbon.usedPaperMeters, 2) : 0}";
        //   printRecordBean.rfidSerialNumber = "-1|${rfidRibbon?.uuid ?? -1}";
        // } else {
        //   printRecordBean.rfidSerialNumber = "-1|-1";
        //   printRecordBean.rfidPrintNumber = "0|0";
        // }
        printRecordBean.rfidPrintNumber =
            "0|${rfidRibbon != null ? double2StringByPoint(lastRibbonLength / 10 + rfidRibbon.usedPaperMeters, 2) : 0}";
        printRecordBean.rfidSerialNumber = "-1|${getCachedRibbonSerial(rfidRibbon)}";
        // printRecordBean.successTimes = "0|${rfidReadTimesList[1]}";
        // printRecordBean.allTimes = "0|${rfidReadTimesList[0]}";
      } else {
        printRecordBean.number = '${printCount}|0';
        printRecordBean.rfidPrintNumber =
            '${rfidPaper != null ? (lastPrintCount + rfidPaper.usedPaperMeters).toInt() : 0}|0';
        printRecordBean.rfidSerialNumber = "${getCachedPaperSerial(rfidPaper)}|-1";
        // printRecordBean.successTimes = "${rfidReadTimesList[1]}|0";
        // printRecordBean.allTimes = "${rfidReadTimesList[0]}|0";
      }

      if (rfidPaper?.isHotShrinkTubing() == true) {
        printRecordBean.paperLengthUsedQuantity = "${double2StringByPoint(printCardPaperLength, 2)}";
        printRecordBean.paperLengthUsedQuantitySum =
            "${rfidPaper != null ? double2StringByPoint(printCardPaperLength + rfidPaper.usedPaperMeters, 2) : 0}";
      } else if (rfidPaper?.isContinuousPaper() == true) {
        var printLength = printCount * (template.rotate % 180 == 0 ? template.height : template.width).toDouble();
        var sumPrintLength =
            lastPrintCount * (template.rotate % 180 == 0 ? template.height : template.width).toDouble();
        printRecordBean.paperLengthUsedQuantity = "${double2StringByPoint(printLength / 10, 2)}";
        printRecordBean.paperLengthUsedQuantitySum =
            "${rfidPaper != null ? double2StringByPoint((sumPrintLength / 10) + rfidPaper.usedPaperMeters, 2) : 0}";
      }
    } else {
      // if ( checkDeviceSupportRFID()) {
      //   printRecordBean.rfidSerialNumber = printRecordRequireInfo.rfidSerialNumber;
      // } else {
      //   printRecordBean.rfidSerialNumber = '-1|-1';
      // }

      printRecordBean.rfidSerialNumber = '-1|-1';
      printRecordBean.number = '${printCount}|0';
      printRecordBean.rfidPrintNumber = "${printCount}|-1";
      //    printRecordBean.successTimes = '-1|-1';
      // printRecordBean.allTimes = '-1|-1';
      // if (printRecordRequireInfo.isHotShrinkTubing) {
      //   printRecordBean.paperLengthUsedQuantity = "${double2StringByPoint(printCardPaperLength, 2)}";
      //   printRecordBean.paperLengthUsedQuantitySum =
      //       "${double2StringByPoint(printRecordRequireInfo.rfidPaperPrintNumber + printCardPaperLength, 2)}";
      // } else if (printRecordRequireInfo.isContinuousPaper) {
      //   var printLength = printCount * (template.rotate % 180 == 0 ? template.height : template.width).toDouble();
      //   printRecordBean.paperLengthUsedQuantity = "${double2StringByPoint(printLength / 10, 2)}";
      //   printRecordBean.paperLengthUsedQuantitySum =
      //       "${double2StringByPoint(printRecordRequireInfo.rfidPaperPrintNumber + printLength / 10, 2)}";
      // }
    }

    if (!Application.user!.isVip) {
      var isRealTime = false;
      template.elements.forEach((element) {
        if (element is DateElement) {
          if (element.dateIsRefresh) {
            isRealTime = true;
          }
        }
      });
      // 试用会员 && 非VIP
      if (taskType == PrintTaskType.batch) {
        printRecordBean.probationPrivilege = "BATCH_PRINTING";
      } else if (isRealTime) {
        printRecordBean.probationPrivilege = "INSTANT_TIME_PRINT";
      }
    }
    printRecordBean.status = 1;
    printRecordBean.printStrategy = printStrategy;
    printRecordBean.illegalCode = illegalCode;

    await PrintDataLogDbUtils.refreshPrintDataLogs(printRecordBean);
  }

  uploadPrintDataLog(String uniqueValue, Function function) async {
    final printRecordBean = await PrintDataLogDbUtils.getPrintDataLogForUniqueValue(uniqueValue);
    PrintLogBusiness().uploadPrintDataLog().then((onValue) {
      function.call();
    });
    // ToNativeMethodChannel().printCommodity();
    List<String> printNumList = (printRecordBean.number?.split("|") ?? []).toList();
    if (Application.isLogin && Platform.isAndroid) {
      int lastCount =
          await ToNativeMethodChannel().getIntSPData("user_print_count_${Application.user?.id.toInt().toString()}") ??
              0;
      // final printCount = int.tryParse(printRecordBean.number!.split("|")[0]) ?? 0;
      final printCount = (printNumList.isEmpty) ? 0 : (int.tryParse(printNumList[0]) ?? 0);
      ToNativeMethodChannel()
          .setSPData({"user_print_count_${Application.user?.id.toInt().toString()}": lastCount + printCount});
    }

    int paperUsed = (printNumList.isEmpty) ? -1 : (int.tryParse(printNumList[0]) ?? -1);

    List<String> printTotalList = (printRecordBean.rfidPrintNumber?.split("|") ?? []).toList();
    int paperUsedQuantitySum = (printTotalList.isEmpty) ? -1 : (int.tryParse(printTotalList[0]) ?? -1);
    double ribbonUsed =
        (printNumList.isEmpty || printNumList.length < 2) ? -1.0 : (double.tryParse(printNumList[1]) ?? -1.0);
    double ribbonUsedQuantitySum =
        (printTotalList.isEmpty || printTotalList.length < 2) ? -1.0 : (double.tryParse(printTotalList[1]) ?? -1.0);

    riskCheck(
        action: RiskRequestCode.PRINT.value,
        paperLengthUsed: double.parse(printRecordBean.paperLengthUsedQuantity ?? "0"),
        paperLengthUsedQuantitySum: double.parse(printRecordBean.paperLengthUsedQuantitySum ?? "0"),
        paperUsed: paperUsed,
        paperUsedQuantitySum: paperUsedQuantitySum,
        ribbonUsed: ribbonUsed,
        ribbonUsedQuantitySum: ribbonUsedQuantitySum);
  }

  buildRiskCheck(int action, {List<NiimbotRFIDInfo>? rfidInfos}) async {
    List<NiimbotRFIDInfo> rfidInfos = NiimbotPrintSDK().store.connectedPrinter?.consumablesRFID ?? [];
    Map result = await getRfidInfo(rfidInfos: rfidInfos);
    riskCheck(
        action: action,
        paperLengthUsed: double.parse(result["paperLengthUsed"]),
        paperLengthUsedQuantitySum: double.parse(result["paperLengthUsedQuantitySum"]),
        paperUsed: -1,
        paperUsedQuantitySum: int.parse(result["paperUsedQuantitySum"]),
        ribbonUsed: -1,
        ribbonUsedQuantitySum: double.parse(result["ribbonUsedQuantitySum"]));
  }

  riskCheck({
    int action = 0,
    double? paperLengthUsed,
    double? paperLengthUsedQuantitySum,
    int? paperUsed,
    int? paperUsedQuantitySum,
    double? ribbonUsed,
    double? ribbonUsedQuantitySum,
  }) {
    Map<String, dynamic> params = {};
    params['action'] = action;
    if (action != 1) {
      if (action == 5) {
        params['deviceConnectType'] = caCheDevice!.isWifi == 1 ? 2 : 1;
        params['firmwareVersion'] = caCheDevice!.softwareVersion;
        params['hardwareVersion'] = caCheDevice!.hardwareVersion;
        params['machineId'] = caCheDevice!.deviceName;
        params['machineSecret'] = caCheDevice!.antiCounterfeiKey ?? "";
        params['paperLengthUsed'] = paperLengthUsed;
        params['paperLengthUsedQuantitySum'] = paperLengthUsedQuantitySum;
        params['ribbonUsed'] = ribbonUsed;
        params['paperUsed'] = paperUsed;
      } else if (connectedDevice != null) {
        params['paperLengthUsedQuantitySum'] = paperLengthUsedQuantitySum;
        params['deviceConnectType'] = connectedDevice!.isWifi == 1 ? 2 : 1;
        params['firmwareVersion'] = connectedDevice!.softwareVersion;
        params['hardwareVersion'] = connectedDevice!.hardwareVersion;
        params['machineId'] = connectedDevice!.deviceName;
        params['machineSecret'] = connectedDevice!.antiCounterfeiKey ?? "";
      }
      final paperRfidInfo = sdkRFIDLabelInfos.firstWhereOrNull(
        (info) => info.isValid() && !info.isRibbon(),
      );
      params['paperSerial'] = paperRfidInfo?.uuid ?? "";
      params['paperUsedQuantitySum'] = paperUsedQuantitySum;

      final ribbonRfidInfo = sdkRFIDLabelInfos.firstWhereOrNull(
        (info) => info.isValid() && info.isRibbon(),
      );
      params['ribbonSerial'] = ribbonRfidInfo?.uuid ?? "";
      params['ribbonUsedQuantitySum'] = ribbonUsedQuantitySum;
    }
    Log.d("============Flutter, 风控接口调用入参：${params.toString()}");
    RiskShieldHelper().checkRisk(params, (p0) {
      Log.d("============Flutter, 风控接口调用结果：${p0}");
      ToNativeMethodChannel.toRiskCheckDialog(p0);
    });
  }

  void requestDeviceVersionInfo() {
    if (caCheDevice != null) {
      if (caCheDevice!.hardwareVersion.isNotEmpty) {
        ToNativeMethodChannel().setSPData({"save_last_hardversion": caCheDevice!.hardwareVersion});
      }
      if (caCheDevice!.softwareVersion.isNotEmpty) {
        ToNativeMethodChannel().setSPData({"save_last_softversion": caCheDevice!.softwareVersion});
      }
      ToNativeMethodChannel().setSPData({"last_device_info": jsonEncode(caCheDevice)});
      caCheDevice!.requestDeviceVersionFlag = true;
    }
  }

  Future<List<int>> getRfidReadNumber() async {
    List<int> result = [0, 0];
    try {
      SDKRFIDSuccessTimesResult timesMap = await NiimbotMobileBinding.getRfidSuccessTimes();
      if (timesMap.code == SDKResultCode.OK) {
        result[0] = timesMap.totalNumber;
        result[1] = timesMap.successNumber;
      }
    } catch (e) {}
    return result;
  }

  String double2StringByPoint(double source, int bit) {
    // 使用 toStringAsFixed 来设置小数位数
    return source.toStringAsFixed(bit);
  }

  String getCachedPaperSerial(NiimbotRFIDInfo? rfidPaper) {
    return rfidPaper?.uuid ??
        cachedSdkRFIDLabelInfos.firstWhereOrNull((it) => it.isValid() && !it.isRibbon())?.uuid ??
        '-1';
  }

  String getCachedRibbonSerial(NiimbotRFIDInfo? rfidribbon) {
    return rfidribbon?.uuid ??
        cachedSdkRFIDLabelInfos.firstWhereOrNull((it) => it.isValid() && it.isRibbon())?.uuid ??
        '-1';
  }

  /**
   * 判断设备是否支持 RFID
   */
  bool checkDeviceSupportRFID() {
    bool isSupportRfid = false;
    try {
      isSupportRfid = isSupportRFID() || isSupportCard() || isSupportPAC();
    } catch (e) {
      // 处理异常
    }
    return isSupportRfid;
  }

  /**
   * 判断设备是否支持 RFID 标签
   */
  bool isSupportRFID() {
    // 检查 connectedDevice 是否不为 null
    if (caCheDevice != null) {
      // 如果 connectedDevice.fake 等于 PRINTER_RFID_PAPER，则返回 true
      if (caCheDevice!.fake == PRINTER_RFID_PAPER) {
        return true;
      }
    }
    return false;
  }

  /**
   * 判断设备是否支持 RFID 碳带
   */
  bool isSupportCard() {
    // 检查 connectedDevice 是否不为 null
    if (caCheDevice != null) {
      // 如果 connectedDevice.fake 等于 PRINTER_RFID_CARD，则返回 true
      if (caCheDevice!.fake == PRINTER_RFID_CARD) {
        return true;
      }
    }
    return false;
  }

  /**
   * 判断设备是否支持 RFID 标签 + 碳带
   */
  bool isSupportPAC() {
    // 检查 connectedDevice 是否不为 null
    if (caCheDevice != null) {
      // 如果 connectedDevice.fake 等于 PRINTER_RFID_PAC，则返回 true
      if (caCheDevice!.fake == PRINTER_RFID_PAC) {
        return true;
      }
    }
    return false;
  }

  NiimbotRFIDInfo? getRFIDRibbon() {
    if (isSupportCard() || isSupportPAC()) {
      return sdkRFIDLabelInfos.firstWhereOrNull(
        (label) => label.isValid() && label.isRibbon(),
      );
    }
    return null;
  }

  NiimbotRFIDInfo? getRFIDPaper() {
    if (isSupportRFID() || isSupportPAC()) {
      return sdkRFIDLabelInfos.firstWhereOrNull(
        (rfidLabelInfo) => rfidLabelInfo.isValid() && !rfidLabelInfo.isRibbon(),
      );
    }
    return null;
  }

  NiimbotRFIDInfo? getRFIDLabelInfo() {
    if (isSupportRFID()) {
      return sdkRFIDLabelInfos.firstWhereOrNull(
        (labelInfo) => labelInfo.isValid() && !labelInfo.isRibbon(),
      );
    } else if (isSupportCard()) {
      return sdkRFIDLabelInfos.firstWhereOrNull(
        (labelInfo) => labelInfo.isValid() && labelInfo.isRibbon(),
      );
    } else {
      return sdkRFIDLabelInfos.firstWhereOrNull(
        (labelInfo) => labelInfo.isValid() && !labelInfo.isRibbon(),
      );
    }
  }

  NiimbotRFIDInfo? getSDKRFIDLabelInfo(String? serialNo) {
    return sdkRFIDLabelInfos.firstWhereOrNull(
      (labelInfo) => labelInfo.serialNumber == serialNo,
    );
  }

  bool isShowTemplate() {
    // 检查 connectedDevice 是否为 null，并进行后续条件判断
    if (caCheDevice == null) {
      return false;
    }

    // 获取 connectedDevice 的属性
    final device = caCheDevice!;
    bool isShowTemplate = !device.hardwareVersion.isNullOrBlank &&
        ((device.securityAction == 2 && device.fake != 2) ||
            (device.securityAction == 1 &&
                device.rfidNotSupportVersions.isNotEmpty &&
                device.rfidNotSupportVersions.contains(device.hardwareVersion!)));
    return isShowTemplate;
  }

  int rfidStatus() {
    var status = 0;

    // 检查 flutterRFIDStatus 的状态
    if (flutterRFIDStatus()) {
      // 根据 isShowTemplate 来决定 status 的值
      status = isShowTemplate() ? 0 : 1;
    } else {
      status = 0;
    }

    return status;
  }

  bool flutterRFIDStatus() {
    bool isRfid = false;
    try {
      isRfid = isSupportRFID() || isSupportPAC();
    } catch (e) {}
    return isRfid;
  }

  /// 获取打印颜色,考虑一半红一般黑的情况
  List<Color>? getPrintDoubleColorForThumb() {
    final String? rfidColor = getRibbonDoubleColorInfo();
    if (rfidColor == null || rfidColor.isEmpty || !rfidColor.contains(',')) {
      return null;
    }

    final splitColor = rfidColor.split(',');
    if (splitColor.length != 2) return null;

    List<int> parseColorPart(String part) {
      return part.split('.').map((e) {
        try {
          return int.parse(e);
        } catch (_) {
          return -1;
        }
      }).toList();
    }

    final firstSplitColor = parseColorPart(splitColor[0]);
    final secondSplitColor = parseColorPart(splitColor[1]);

    const defaultColor = Color(0xFF000000); // 默认黑色，可根据需要调整
    final defaultColorList = [defaultColor, defaultColor];

    if (firstSplitColor.length != 3 || secondSplitColor.length != 3) {
      return defaultColorList;
    }

    if (firstSplitColor.any((c) => c < 0 || c > 255) ||
        secondSplitColor.any((c) => c < 0 || c > 255)) {
      return defaultColorList;
    }

    if (firstSplitColor.every((c) => c == 0) &&
        secondSplitColor.every((c) => c == 0)) {
      return defaultColorList;
    }

    final firstColor = Color.fromARGB(255, firstSplitColor[0], firstSplitColor[1], firstSplitColor[2]);
    final secondColor = Color.fromARGB(255, secondSplitColor[0], secondSplitColor[1], secondSplitColor[2]);

    return [firstColor, secondColor];
  }

  List<int> getPrintColorForPrintSdk() {
    if (isDoubleColorRibbon()) {
      return [255, 0, 0, 0]; // 双色带颜色
    } else {
      return getPrintColor(); // 其他情况使用 getPrintColor 方法的颜色
    }
  }

  List<int> getPrintColor() {
    List<int> result = [];

    if (isSupportRFID()) {
      var paperColor = serviceRfidInfo?.rfidPaperColor;
      if (paperColor != null && paperColor.isNotEmpty && !paperColor.contains(",")) {
        var temp = paperColor.split('.').toList();
        temp.insert(0, paperColor == "255.255.255" ? "0" : "255");

        result = temp.map((colorValue) {
          try {
            return int.parse(colorValue);
          } catch (e) {
            print(e);
            return 0;
          }
        }).toList();
      }
    } else {
      var carbonColor = serviceRfidInfo?.rfidRibbonColor;

      if (carbonColor != null && carbonColor.isNotEmpty) {
        result = [255, 0, 0, 0];
      }
    }

    return result;
  }

  bool isDoubleColorRibbon() {
    // 如果 lastRfidColorInfo 不为 null，并且其 isDoubleColorRibbon 属性为 true，则返回 true
    return serviceRfidInfo?.isDoubleColorRibbon();
  }

  String? getRibbonDoubleColorInfo() {
    if (isDoubleColorRibbon()) {
      return serviceRfidInfo?.rfidRibbonColor;
    }
    return null;
    // return "0.0.0,230.0.18";
  }

  Color? getCarbonARGBForPrint() {
    final List<int>? list = _getCarbonARGBForPrint();
    if (list?.isEmpty ?? true) {
      return null;
    }
    Color color = Color.fromARGB(
      list![0], // Alpha
      list[1], // Red
      list[2], // Green
      list[3], // Blue
    );
    if (RfidUtil.is16GrayColor(color)) {
      return null;
    }
    return color;
  }

  List<int>? _getCarbonARGBForPrint() {
    if (serviceRfidInfo?.isDoubleColorRibbon()) {
      return null;
    }

    var colors = serviceRfidInfo?.getPreviewColor(isSupportRFID());

    if (colors == null || colors.isEmpty || colors.length != 3) {
      return null;
    } else {
      colors.insert(0, 255);
      return colors;
    }
  }

  Color getCarbonColorForDisplay() {
    List<int>? carbonColors = _getCarbonARGBForPrint();

    // 检查碳带颜色是否为空或长度不为4
    if (carbonColors == null || carbonColors.isEmpty || carbonColors.length != 4) {
      return Color.fromARGB(255, 0, 0, 0); // 默认颜色为黑色，不透明
    }

    // 使用 carbonColors 构建颜色
    return Color.fromARGB(
      carbonColors[0], // Alpha
      carbonColors[1], // Red
      carbonColors[2], // Green
      carbonColors[3], // Blue
    );
  }

  String? getRealCarbonColor() {
    if (serviceRfidInfo?.isDoubleColorRibbon()) {
      return null;
    }
    return serviceRfidInfo?.rfidColor;
  }

  Future<int> getPrintColorType(TemplateData template) async {
    if (await isDoubleColorRibbon()) {
      if (template.elements.whereType<ImageElement>().any((item) => item.imageProcessingType == 3)) {
        // 非双色灰阶
        return 2;
      }
      // 非双色非灰阶
      return 0;
    }

    if ((await getPrintColor()).isEmpty &&
        await ifLabelSupportDoubleColorPrint() &&
        await ifDeviceSupportDoubleColorPrint()) {
      // 双色
      return 1;
    }

    if (template.elements.whereType<ImageElement>().any((item) => item.imageProcessingType == 3)) {
      // 非双色灰阶
      return 2;
    }

    // 非双色非灰阶
    return 0;
  }

  Future<bool> ifDeviceSupportDoubleColorPrint() async {
    SDKColorSupportResult result = await NiimbotMobileBinding.getPrinterColorSupport();
    var color = result.color.firstWhereOrNull((e) => e.value == 1);
    return caCheDevice != null && color != null;
  }

  bool ifLabelSupportDoubleColorPrint() {
    if (isSupportRFID()) {
      var paperColor = serviceRfidInfo?.rfidPaperColor;
      return paperColor != null && paperColor.isNotEmpty;
    } else {
      return false;
    }
  }

  void clearRFIDInfo() {
    sdkRFIDLabelInfos.clear();
    serviceRfidInfo = ServiceRfidInfo();
    sdkHasRFIDLabel = false;
    rfidTemplateData = null;
  }

  void clearConnectDevice() {
    clearRFIDInfo(); // 呼叫清除 RFID 信息的方法
    connectedDevice = null; // 断开连接的设备
  }

  /**
   * 获取当前连接设备的打印模式
   */
  int getPrintMode(String consumableType) {
    return connectedDevice?.getPrintMode(consumableType) ?? 1;
  }

  bool isNoCallbackPrinter() {
    return connectedDevice?.isB3() == true;
  }

  bool ifPaperMatch() {
    // 检查 connectedDevice 和 rfidTemplateModuleLocal 是否都不为 null
    if (connectedDevice != null && rfidTemplateData != null) {
      // 类似于 safeLet，只有当 connectedDevice 和 rfidTemplateModuleLocal 都不为 null 时才会执行
      var device = connectedDevice;
      var rfidTemplate = rfidTemplateData;

      // 检查 rfidTemplate 的 profile 是否不为 null，并执行相应的逻辑
      if (rfidTemplate?.profile != null && rfidTemplate?.profile.machineName != null) {
        return rfidTemplate!.profile.machineName!.split(",").contains(device?.name);
      }
    }

    // 如果条件不满足，则返回 true（类似于 Kotlin 的 ?: true）
    return true;
  }

  String getRfidSerials() {
    // 获取第一个有效且不是 Ribbon 的 RFID 信息
    final paperRfidInfo = sdkRFIDLabelInfos.firstWhereOrNull(
      (info) => info.isValid() && !info.isRibbon(),
    );

    // 获取第一个有效且是 Ribbon 的 RFID 信息
    final ribbonRfidInfo = sdkRFIDLabelInfos.firstWhereOrNull(
      (info) => info.isValid() && info.isRibbon(),
    );

    // 获取序列号，若没有则返回空字符串
    final paperSerial = paperRfidInfo?.serialNumber ?? "";
    final ribbonSerial = ribbonRfidInfo?.serialNumber ?? "";

    // 返回格式化的字符串
    return "$paperSerial|$ribbonSerial";
  }

  /**
   * 获取rfid相关参数
   */
  Future<Map<String, String>> getRfidInfo({List<NiimbotRFIDInfo>? rfidInfos}) async {
    final result = HashMap<String, String>();
    if (rfidInfos != null) {
      sdkRFIDLabelInfos = rfidInfos;
    } else {
      try {
        sdkRFIDLabelInfos = await NiimbotPrintSDK().getConsumablesRFIDData(accurate: true);
      } catch (e) {
        sdkRFIDLabelInfos = [];
      }
    }

    // 获取第一个有效且不是 Ribbon 的 RFID 信息
    final paperRfidInfo = sdkRFIDLabelInfos.firstWhereOrNull(
      (info) => info.isValid() && !info.isRibbon(),
    );

    // 获取第一个有效且是 Ribbon 的 RFID 信息
    final ribbonRfidInfo = sdkRFIDLabelInfos.firstWhereOrNull(
      (info) => info.isValid() && info.isRibbon(),
    );

    // 获取序列号，若没有则返回空字符串
    final paperSerial = paperRfidInfo?.uuid ?? "";
    final ribbonSerial = ribbonRfidInfo?.uuid ?? "";

    // 初始化其他参数
    final paperLengthUsed = "0";
    var paperLengthUsedQuantitySum = "0";
    if (paperRfidInfo?.isHotShrinkTubing() == true || paperRfidInfo?.isContinuousPaper() == true) {
      if (paperRfidInfo != null) {
        NiimbotRFIDInfo rfidInfo = paperRfidInfo;
        paperLengthUsedQuantitySum = double2StringByPoint(rfidInfo.usedPaperMeters.toDouble(), 2);
      }
    }
    final paperUsed = "0";
    final ribbonUsed = "0";
    final paperUsedQuantitySum = paperRfidInfo?.usedPaperMeters.toString() ?? "0";
    final ribbonUsedQuantitySum = ribbonRfidInfo?.usedPaperMeters.toString() ?? "0";

    // 填充结果 Map
    result["paperSerial"] = paperSerial;
    result["paperUsed"] = paperUsed;
    result["paperUsedQuantitySum"] = paperUsedQuantitySum;
    result["paperLengthUsed"] = paperLengthUsed;
    result["paperLengthUsedQuantitySum"] = paperLengthUsedQuantitySum;
    result["ribbonSerial"] = ribbonSerial;
    result["ribbonUsed"] = ribbonUsed;
    result["ribbonUsedQuantitySum"] = ribbonUsedQuantitySum;

    return result;
  }

  String getRealitySize(String size) {
    if (size.contains('.')) {
      String after = size.split('.').last;
      if (int.parse(after) == 0) {
        return size.split('.').first;
      } else {
        String trimEnd = after.replaceAll(RegExp(r'0*$'), '');
        return '${size.split('.').first}.$trimEnd';
      }
    } else {
      return size;
    }
  }

  static refreshRfidColorWith(String paperColor, String ribbonColor) {
    serviceRfidInfo.rfidPaperColor = paperColor;
    serviceRfidInfo.rfidRibbonColor = ribbonColor;
    ToNativeMethodChannel.setRfidColor({"paperColor": paperColor, "ribbonColor": ribbonColor});
    Color? c1RibbonColor = getC1RibbonColor();
    RfidManager.instance.updateC1RibbonColorListener?.call(c1RibbonColor);
  }

  static Color? getC1RibbonColor() {
    String colorValue = _getC1RibbonColorValue();
    if (colorValue.isNotEmpty) {
      try {
        return Color.fromARGB(255, int.parse(colorValue.split(".")[0]), int.parse(colorValue.split(".")[1]),
            int.parse(colorValue.split(".")[2]));
      } on Exception catch (e) {
        debugPrint(e.toString());
        return null;
      }
    } else {
      return null;
    }
  }

  static String _getC1RibbonColorValue() {
    NiimbotPrinter? printer = NiimbotPrintSDK().store.connectedPrinter;
    if (printer?.name?.startsWith("C1") != true) {
      return "";
    }
    return serviceRfidInfo.rfidRibbonColor;
  }
}

extension StringExtensions on String? {
  bool get isNullOrBlank => this == null || this!.trim().isEmpty;
}

extension RFIDLabel on NiimbotRFIDInfo {
  bool isValid() {
    return this.type > 0 && this.serialNumber.isNotEmpty;
  }

  bool isRibbon() => this.type == 6 || this.type == 8;

  bool isContinuousPaper() => this.type == 11 || this.type == 3;

  bool isHotShrinkTubing() => this.type == 11;
}
