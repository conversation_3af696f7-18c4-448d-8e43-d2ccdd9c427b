import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_canvas_plugins_interface/config/canvas_theme_data.dart';
import 'package:flutter_canvas_plugins_interface/config/template_config.dart';
import 'package:flutter_canvas_plugins_interface/shared/canvas_font_data.dart';
import 'package:flutter_canvas_plugins_interface/shared/localization_config.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:netal_plugin/netal_plugin.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/model/template_data.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/model/tinykit_box_frame_info.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/tinykit_canvas_box_frame.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/tinykit_canvas_theme_widget.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/utils/input_utils.dart';
import 'package:path_provider/path_provider.dart';

Logger _logger = Logger("CanvasItemBoxScreen");

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle());
  runApp(MyApp());
  configLoading();
}

void configLoading() {
  EasyLoading.instance
    ..displayDuration = const Duration(milliseconds: 2000)
    ..indicatorType = EasyLoadingIndicatorType.fadingCircle
    ..loadingStyle = EasyLoadingStyle.light
    ..indicatorSize = 45.0
    ..radius = 10.0
    ..progressColor = Colors.yellow
    ..backgroundColor = Colors.green
    ..indicatorColor = Colors.yellow
    ..textColor = Colors.yellow
    ..maskColor = Colors.blue.withOpacity(0.5)
    ..userInteractions = true
    ..dismissOnTap = false;
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: '新画板',
      theme: ThemeData(
        primarySwatch: kPrimaryColor,
      ),
      home: const MyHomePage(title: 'Flutter画板'),
      builder: EasyLoading.init(),
    );
  }
}

const MaterialColor kPrimaryColor = const MaterialColor(
  0xFFFFFFFF,
  const <int, Color>{
    50: const Color(0xFFFFFFFF),
    100: const Color(0xFFFFFFFF),
    200: const Color(0xFFFFFFFF),
    300: const Color(0xFFFFFFFF),
    400: const Color(0xFFFFFFFF),
    500: const Color(0xFFFFFFFF),
    600: const Color(0xFFFFFFFF),
    700: const Color(0xFFFFFFFF),
    800: const Color(0xFFFFFFFF),
    900: const Color(0xFFFFFFFF),
  },
);

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  _MyHomePageState createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  final GlobalKey _canvasAddItemBoxKey = GlobalKey();

  @override
  void initState() {
    parserJson();
    super.initState();
    // NetalPlugin.initFontsFilePath("fontPath");
    EasyLoading.addStatusCallback((status) {
      print('EasyLoading Status $status');
      if (status == EasyLoadingStatus.dismiss) {}
    });
    // EasyLoading.showSuccess('Use in initState');
    // EasyLoading.removeCallbacks();
  }

  ///横板2区域 i形
  //String jsonData =
  //    "{\"id\":\"e4ef31e5-9ec1-4300-90ea-6bdb16bd6f8f\",\"version\":\"1.0.0\",\"image\":\"https://oss-print-fat.jc-test.cn/user_resources/03c2641eb88a11eb9302a683e7b21ab9/344/templates/100850119b2b0c72fff5c530cbb0c173feeabc7a\",\"width\":74,\"height\":12.5,\"scalable\":true,\"padding\":[0,0,0,0],\"areas\":[{\"id\":\"fef149851fb84c9b83ad5b30a475de0a\",\"x\":1.5,\"y\":1.5,\"width\":34.6,\"height\":9.8,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":\"\",\"copyProperty\":[],\"type\":\"text\",\"typeProperty\":{\"layout\":0,\"xAlign\":1,\"boxMode\":3,\"fontSize\":3.9,\"yAlign\":2,\"fontColor\":0,\"fontCode\":\"ZT001\"},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0},{\"id\":\"fef149851fb84c9b83ad5b30a475de0b\",\"x\":38.4,\"y\":1.5,\"width\":34.6,\"height\":9.8,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":\"fef149851fb84c9b83ad5b30a475de0a\",\"copyProperty\":[\"style\",\"value\"],\"type\":\"text\",\"typeProperty\":{\"layout\":0,\"xAlign\":1,\"boxMode\":3,\"fontSize\":3.9,\"yAlign\":2,\"fontColor\":0,\"fontCode\":\"ZT001\"},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0}]}";

  /// 4个标签
  //String jsonData =
  //  "{\"id\":\"14d3245e-d4d8-400d-bec8-960de8317443\",\"version\":\"1.0.0\",\"image\":\"https://oss-print-fat.jc-test.cn/public_resources/labels/50233656-06c884ff6080fa3cb2ee91f0ff0d2eed.png\",\"width\":76,\"height\":34.75,\"scalable\":true,\"padding\":[0,0,0,0],\"areas\":[{\"id\":\"fef149851fb84c9b83ad5b30a475de0a\",\"x\":1,\"y\":1,\"width\":35.7,\"height\":10.6,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":\"\",\"copyProperty\":[],\"type\":\"text\",\"typeProperty\":{\"layout\":0,\"xAlign\":1,\"boxMode\":3,\"fontSize\":3.9,\"yAlign\":2,\"fontColor\":0,\"fontCode\":\"ZT001\"},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0},{\"id\":\"fef149851fb84c9b83ad5b30a475de0b\",\"x\":1.0,\"y\":13.4,\"width\":35.7,\"height\":10.6,\"max\":1,\"min\":1,\"rotatable\":true,\"rotate\":180,\"copyFrom\":\"fef149851fb84c9b83ad5b30a475de0a\",\"copyProperty\":[\"style\",\"value\"],\"type\":\"text\",\"typeProperty\":{\"layout\":0,\"xAlign\":1,\"boxMode\":3,\"fontSize\":3.9,\"yAlign\":2,\"fontColor\":0,\"fontCode\":\"ZT001\"},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0},{\"id\":\"fef149851fb84c9b83ad5b30a475de0c\",\"x\":39.0,\"y\":10.8,\"width\":35.7,\"height\":10.6,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":\"fef149851fb84c9b83ad5b30a475de0b\",\"copyProperty\":[\"style\",\"value\"],\"type\":\"text\",\"typeProperty\":{\"layout\":0,\"xAlign\":1,\"boxMode\":3,\"fontSize\":3.9,\"yAlign\":2,\"fontColor\":0,\"fontCode\":\"ZT001\"},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0},{\"id\":\"fef149851fb84c9b83ad5b30a475de0d\",\"x\":39,\"y\":23.0,\"width\":35.7,\"height\":10.6,\"max\":1,\"min\":1,\"rotatable\":true,\"rotate\":180,\"copyFrom\":\"fef149851fb84c9b83ad5b30a475de0a\",\"copyProperty\":[\"style\",\"value\"],\"type\":\"text\",\"typeProperty\":{\"layout\":0,\"xAlign\":1,\"boxMode\":3,\"fontSize\":3.9,\"yAlign\":2,\"fontColor\":0,\"fontCode\":\"ZT001\"},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0}]}";
  ///3排标签
  // String jsonData =
  //     "{\"id\":\"0000000023232000000009e6bfac\",\"version\":\"1.0.0\",\"image\":\"https://oss-print-fat.jc-test.cn/public_resources/labels/DN202406170001-21b41b03ef5fd7148ac37f7ee64724c7.png\",\"width\":78,\"height\":25,\"scalable\":true,\"padding\":[0,0,0,0],\"areas\":[{\"id\":\"fef149851fb84c9b83ad5b30a475de0a\",\"x\":1,\"y\":1,\"width\":36.0,\"height\":10.8,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":\"\",\"copyProperty\":[],\"type\":\"text\",\"typeProperty\":{\"layout\":0,\"xAlign\":1,\"boxMode\":3,\"fontSize\":3.9,\"yAlign\":2,\"fontColor\":0,\"fontCode\":\"ZT001\"},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0},{\"id\":\"fef149851fb84c9b83ad5b30a475de0b\",\"x\":1,\"y\":13.4,\"width\":36,\"height\":10.8,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":180,\"copyFrom\":\"fef149851fb84c9b83ad5b30a475de0a\",\"copyProperty\":[\"style\",\"value\"],\"type\":\"text\",\"typeProperty\":{\"layout\":0,\"xAlign\":1,\"boxMode\":3,\"fontSize\":3.9,\"yAlign\":2,\"fontColor\":0,\"fontCode\":\"ZT001\"},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0},{\"id\":\"fef149851fb84c9b83ad5b30a475de0c\",\"x\":39.2,\"y\":11.0,\"width\":37.6,\"height\":12.8,\"max\":1,\"min\":1,\"rotatable\":false,\"rotate\":0,\"copyFrom\":\"fef149851fb84c9b83ad5b30a475de0a\",\"copyProperty\":[\"style\",\"value\"],\"type\":\"text\",\"typeProperty\":{\"layout\":0,\"xAlign\":1,\"boxMode\":3,\"fontSize\":3.9,\"yAlign\":2,\"fontColor\":0,\"fontCode\":\"ZT001\"},\"margin\":[0,0,0,0],\"padding\":[0,0,0,0],\"elementMargin\":[0,0,0,0],\"elementRowGap\":[0,0,0,0],\"lineSpacing\":0}]}";

  ///2个标签
  String jsonData =
      "{\"id\":\"665eb0d6b9cd6b5220b0b5fe1231\",\"version\":\"1.0.0\",\"image\":\"https://oss-print-fat.jc-test.cn/public_resources/labels/DN202308300005-9b052be1d14e69535a08d23a42067ee4.png\",\"width\":64.0,\"height\":32.0,\"scalable\":true,\"padding\":[0.0,0.0,0.0,0.0],\"areas\":[{\"id\":\"fef149851fb84c9b83ad5b30a475de0a\",\"x\":1.5,\"y\":1.5,\"width\":61.0,\"height\":13.5,\"max\":1.0,\"min\":1.0,\"rotatable\":false,\"rotate\":0.0,\"copyFrom\":\"\",\"copyProperty\":[],\"type\":\"text\",\"typeProperty\":{\"layout\":0.0,\"xAlign\":1.0,\"boxMode\":3.0,\"fontSize\":3.9,\"yAlign\":2.0,\"fontColor\":0.0,\"fontCode\":\"ZT001\"},\"margin\":[0.0,0.0,0.0,0.0],\"padding\":[0.0,0.0,0.0,0.0],\"elementMargin\":[0.0,0.0,0.0,0.0],\"elementRowGap\":[0.0,0.0,0.0,0.0],\"lineSpacing\":1.0},{\"id\":\"fef149851fb84c9b83ad5b30a475de0b\",\"x\":1.5,\"y\":17.1,\"width\":61.0,\"height\":13.5,\"max\":1.0,\"min\":1.0,\"rotatable\":false,\"rotate\":180,\"copyFrom\":\"fef149851fb84c9b83ad5b30a475de0a\",\"copyProperty\":[\"style\",\"value\"],\"type\":\"text\",\"typeProperty\":{\"layout\":0.0,\"xAlign\":1.0,\"boxMode\":3.0,\"fontSize\":3.9,\"yAlign\":2.0,\"fontColor\":0.0,\"fontCode\":\"ZT001\"},\"margin\":[0.0,0.0,0.0,0.0],\"padding\":[0.0,0.0,0.0,0.0],\"elementMargin\":[0.0,0.0,0.0,0.0],\"elementRowGap\":[0.0,0.0,0.0,0.0],\"lineSpacing\":1.0}],\"localBackground\":[\"/data/user/0/com.gengcon.android.jccloudprinter/files/jc/zh-cn/cloud/background/cable_background_166116193_1719395018274.png\"]}";

  late String printerJsonData;

  List<CanvasFontData> _canvasFontData = [];

  void parserJson() async {
    jsonData = await DefaultAssetBundle.of(context).loadString("assets/print_json_40_30_empty.txt");
    // jsonData = await DefaultAssetBundle.of(context)
    //     .loadString("assets/print_json_45_80.txt");
    // jsonData =
    //     await DefaultAssetBundle.of(context).loadString("assets/商品模板.json");
    printerJsonData = await DefaultAssetBundle.of(context).loadString("assets/printer_json_40_30.txt");
    String fontJsonData = await DefaultAssetBundle.of(context).loadString("assets/fontlib_zh-cn.json");
    var jsonMap = jsonDecode(fontJsonData);
    for (var fontDataJson in jsonMap) {
      _canvasFontData.add(CanvasFontData(
          code: fontDataJson['code'] as String,
          isVip: fontDataJson['isVip'] as bool,
          thumbnailUrl: fontDataJson['thumbnailUrl'] as String,
          id: fontDataJson['id'] as int,
          url: fontDataJson['path'] as String,
          name: fontDataJson['name'] as String));
    }

    appDocDir = (await getApplicationDocumentsDirectory()).path + '/font/';
    print('------appDocDir $appDocDir');
    setState(() {});
  }

  late String appDocDir;

  void initDeviceInch(BuildContext context) {}

  int _windowsPrintMode = 0;

  /*void _switchPrintMode() {
    disConnect().then((value) {
      setWindowsPrintMode(_windowsPrintMode == 0 ? 1 : 0).then((value) {
        print('[nety] setWindowsPrintMode: $value');
        setState(() {
          _windowsPrintMode = (_windowsPrintMode == 0 ? 1 : 0);
        });
      });
    });
  }*/

  final info = TinyKitBoxFrameInfo();

  @override
  Widget build(BuildContext context) {
    initDeviceInch(context);
    Widget vipTipWidget = vipTipContainer();
    Widget paperErrorTipWidget = paperErrorTipContainer();
    info.canvasWidth = 50;
    info.canvasHeight = 30;
    return CanvasTheme(
      themeData: CanvasThemeData.canvasThemeDataDefault(),
      language: 'en',
      child: GestureDetector(
        onTap: InputUtils.unFocus,
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          body: jsonData != null
              ? TinyKitCanvasThemeWidget(
                  key: _canvasAddItemBoxKey,
                  jsonData: jsonData,
                  language: 'en',
                  fontConfig: CanvasFontConfig(
                      fontCategories: [],
                      fontPath: Platform.isIOS
                          ? '/data/user/0/com.example.example/files/'
                          : '/data/user/0/com.example.example/files/',
                      fontDefaultFile: 'ZT001.ttf'),
                  onPrint: onPrintData,
                  onSave: onSavedData,
                  onLabelInit: onLabelInit,
                  onConfig: onConfig,
                  localizationConfig: LocalizationConfig(Locale('zh'),
                      customLocalizationResource: {
                        // LocalStringKey.element: '自定义标题'
                      },
                      direction: TextDirection.ltr),
                  toolkitButtons: [],
                  needDownloadFonts: true,
                  defaultSelectType: '',
                  onPrintSettings: onPrintSettings,
                  vipTipWidget: vipTipWidget,
                  paperErrorTipWidget: paperErrorTipWidget,
                  canvasInitData: onCanvasInitData,
                  frameInfo: info,
                )
              : Container(),
        ),
      ),
    );
  }

  Widget vipTipContainer() {
    return Container(
      padding: EdgeInsets.all(10),
      width: double.infinity,
      height: 44,
      color: Color(0xffFFEACB),
      child: Row(
        children: [
          Text(
            "您正在试用线缆会员专属功能",
            style: TextStyle(color: Color(0xff8D4C20)),
          )
        ],
      ),
    );
  }

  Widget paperErrorTipContainer() {
    return Container(
      padding: EdgeInsets.fromLTRB(10, 0, 0, 10),
      width: double.infinity,
      height: 34,
      color: Color(0xffFFEACB),
      child: Row(
        children: [
          Text(
            "未检查到标签纸",
            style: TextStyle(color: Color(0xff8D4C20)),
          )
        ],
      ),
    );
  }

  Future<bool> onCanvasInitData(String? canvasJson) {
    _logger.log("onCanvasInitData click =================> ${canvasJson}");
    Completer<bool> completer = Completer<bool>();

    return completer.future;
  }

  void onPrintSettings() {
    _logger.log("onPrintSettings click =================>");
  }

  /// 打印入口
  void onPrintData(
      String? canvasJson, int? currentPage, TemplateConfig? config, BuildContext context, Widget? extraWidget) {
    _logger.log("onPrintData  canvasJson======> ${canvasJson}");
    _logger.log("onPrintData  currentPage======> ${currentPage}");

    /// 实例化模板数据结构
    // TemplateData templateData = TemplateData.fromJson(json.decode(canvasJson));

    /// 生成可打印的转义数据
    // String printJson = templateData.generatePrintJson(pageIndex: currentPage);
    // print('----  canvasData ----\n${templateData.generateCanvasJson()}');
    // print(
    //     '----  printData ----\n${templateData.generatePrintJson(pageIndex: currentPage)}');

    // if (canvasJson != null) {
    //   Navigator.of(context).push(new MaterialPageRoute(builder: (context) {
    //     return PrintSettingScreen(
    //       canvasJsonData: canvasJson,
    //     );
    //   }));
    // }
  }

  Future<TemplateData> onSavedData(String? canvasJson, TemplateConfig? config, BuildContext context,
      {String? templateId}) {
    Completer<TemplateData> completer = Completer<TemplateData>();
    EasyLoading.showSuccess('点击了保持数据');
    Future.delayed(Duration(milliseconds: 1000), () {
      completer.complete(null);
    });
    return completer.future;
  }

  Future<TemplateData> onLabelInit(CanvasBoxFrameState buildContext) {
    Completer<TemplateData> completer = Completer<TemplateData>();
    EasyLoading.showSuccess('点击了保持数据');
    Future.delayed(Duration(milliseconds: 1000), () {
      completer.complete(null);
    });
    return completer.future;
  }

  Future<TemplateData?> onConfig(String? canvasJson, TemplateConfig? config, BuildContext context) {
    return Navigator.of(context).push(new MaterialPageRoute(builder: (context) {
      return Container();
    }));
  }

  void onPrintPress() async {
    /// 判断当前连接状态
    /*getConnectedDevice().then((value) {
      print('当前连接设备判断：$value');
      if (value is Map &&
          value["identifier"] != null &&
          value["identifier"] != "") {
        // EasyLoading.show(
        //   status: '生成打印数据中...',
        //   maskType: EasyLoadingMaskType.black,
        // );
        print('EasyLoading show');
        // _canvasAddItemBoxKey.currentState
        //     .asyncPrintPreView()
        //     .then((value) => onPrintResult(value));
      } else {
        _blueToothConnect(null);
      }
    });*/
  }

  void onPrintResult(String value) {
    print("onPrintResult => value = $value");
//   _canvasAddItemBoxKey.currentState.printPreView(_onHandlePrintData);
//     String canvasJsonData =
//         _canvasAddItemBoxKey.currentState.getCanvasJsonDATA();
//     if (canvasJsonData != null) {
//       Navigator.of(context).push(new MaterialPageRoute(builder: (context) {
//         return PrintSettingScreen(
//           canvasJsonData: canvasJsonData,
//         );
//       }));
//     }
  }
}
