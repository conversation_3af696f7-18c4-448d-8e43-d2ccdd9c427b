import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:text/application.dart';
import 'package:text/network/dio_utils.dart';
import 'package:text/network/http_api.dart';
import 'package:text/riskShield/risk_cache_bean.dart';
import 'package:text/utils/log_utils.dart';

import '../tools/to_Native_Method_Channel.dart';

enum ActionName {
  APP_START,

  ///App启动
  LOGIN,

  ///登录
  CLOSE_LIB,

  ///开合盖
  CONNECTING_PRINTER,

  ///连接打印机
  BEFORE_PRINTING,

  ///打印前
  AFTER_PRINTING

  ///打印后
}

enum RiskType { normal, info, warning, error }

///风控帮助类
class RiskShieldHelper {
  static RiskShieldHelper? _instance;
  static SharedPreferences? sp;

  factory RiskShieldHelper() {
    _instance ??= RiskShieldHelper._internal();
    return _instance!;
  }

  RiskShieldHelper._internal();

  ///检查当前行为的风险等级
  ///接口入参：
  /// {
  ///   "action": "AFTER_PRINTING",     -- 用户行为
  ///   "deviceConnectType": 0,         -- 连接类型： 1=蓝牙,2=局域网,3=云端,4=数据线
  ///   "firmwareVersion": "string",    -- 固件版本号
  ///   "hardwareVersion": "string",    -- 硬件版本号
  ///   "machineId": "string",          -- 机型名称
  ///   "paperSerial": "string",        -- 标签rfid编号
  ///   "paperUsed": 0,                 -- 单次标签打印张数 整形数字
  ///   "paperLengthUsed": 0,           -- 单次标签打印长度 可带两位小数（兼容热缩管和连续纸）
  ///   "paperUsedQuantitySum": 0,      -- 设备内标签总打印张数 整形数字
  ///   "paperLengthUsedQuantitySum": 0,-- 设备内标签总打印长度 可带两位小数（兼容热缩管和连续纸）
  ///   "ribbonSerial": "string",       -- 碳带rfid编号
  ///   "ribbonUsed": 0,                -- 单次碳带打印长度 可带两位小数
  ///   "ribbonUsedQuantitySum": 0      -- 设备内碳带总打印长度 可带两位小数
  /// }
  /// 接口出参：
  /// {
  ///   "code": 61000,
  ///   "status_code": 61000,
  ///   "message": "正常<61000 info 级别 61000 ~ 61999 warning 级别 62000 ~ 62999 error 级别 63000 ~ 63999"
  /// }
  /// return
  checkRisk(Map<String, dynamic> params, Function(Map<String, dynamic>) callback) {
    try {
      var defaultCode = 0;
      var defaultMessage = '';
      Map<String, dynamic> cachePair = {};
      if (params['action'] == null || _getAction(params['action'] as int) == null) {
        callback({'code': defaultCode, 'message': defaultMessage});
        return;
      }
      ActionName? action = _getAction(params['action'] as int);
      params['action'] = action?.name;
      if (!Application.networkConnected) {
        _checkCacheStrategy(params, callback);
        return;
      }
      DioUtils.instance.requestNetwork<Map<String, dynamic>>(Method.post, RiskShieldApi.riskCheck,
          params: params, needEncrypt: true, onSuccess: (data) {
        if (data != null && data.isNotEmpty) {
          defaultCode = data['errorCode'];
          defaultMessage = data['message'];
          try {
            cachePair = data['cacheData'] != null ? jsonDecode(data['cacheData']) : {};
          } catch (e, s) {
            Log.d('异常信息:\n $e');
          }
        }
        int errorLevel =  _getRiskType(defaultCode.toInt()).index;
        _trackEvent(errorLevel, params, defaultCode);
        callback({'code': errorLevel, 'message': defaultMessage});
        _updateCacheStrategy(
            params,
            {
              'errorLevel': _getRiskType(defaultCode.toInt()).index,
              'errorMessage': defaultMessage,
              'errorCode': defaultCode.toInt()
            },
            cachePair.map((key, value) => MapEntry(key, List<String>.from(value))));
      }, onError: (code, message) {
        callback({'code': _getRiskType(code).index, 'message': message ?? ''});
      });
    } catch (e, s) {
      Log.d('异常信息:\n $e');
      Log.d('调用栈信息:\n $s');
    }
  }

  ActionName? _getAction(int index) {
    try {
      return ActionName.values.elementAt(index);
    } catch (e, s) {
      Log.d('异常信息:\n $e');
      Log.d('调用栈信息:\n $s');
      return null;
    }
  }

  RiskType _getRiskType(int code) {
    if (code >= 61000 && code < 61999) {
      return RiskType.info;
    } else if (code >= 62000 && code < 62999) {
      return RiskType.warning;
    } else if (code >= 63000 && code < 63999) {
      return RiskType.error;
    } else {
      return RiskType.normal;
    }
  }

  Map<String, Map<String, dynamic>> cacheStrategy = {};
  final tag_risk_cache_key = "tag_risk_cache";

  ///无网时，检查本地缓存策略
  _checkCacheStrategy(Map<String, dynamic> params, Function(Map<String, dynamic>) callback) async {
    _initCache();
    var cacheBean = _ifHitStrategy(params);
    if (cacheBean != null) {
      _trackEvent(cacheBean.errorLevel, params, cacheBean.errorCode);
      callback({'code': cacheBean.errorLevel, 'message': cacheBean.errorMessage});
    } else {
      callback({'code': 0, 'message': ''});
    }
  }

  ///更新本地缓存策略
  _updateCacheStrategy(
      Map<String, dynamic> params, Map<String, dynamic> result, Map<String, List<String>> serverCachePair) {
    _initCache();
    serverCachePair.entries.forEach((element) {
      element.value.forEach((value) {
        if (result['errorLevel'] == RiskType.normal.index) {
          cacheStrategy[element.key]?.remove(value);
        } else {
          var cacheBean = RiskCacheBean(
              cacheKey: element.key,
              cacheValue: value,
              errorLevel: result['errorLevel'],
              errorMessage: result['errorMessage'],
              errorCode: result['errorCode']);
          Map<String, dynamic> entryMap = cacheStrategy[element.key] ?? {};
          entryMap[value] = cacheBean.toJson();
          cacheStrategy[element.key] = entryMap;
        }
      });
    });
    if (serverCachePair.isNotEmpty) {
      _freshSpCache();
    }
  }

  ///初始化缓存
  _initCache() async {
    sp ??= await SharedPreferences.getInstance();
    if (cacheStrategy.isEmpty && sp?.getString(tag_risk_cache_key)?.isNotEmpty == true) {
      Map<String, dynamic> cacheJson = jsonDecode(sp?.getString(tag_risk_cache_key) ?? '{}');
      cacheJson.entries.forEach((e) {
        cacheStrategy[e.key] = e.value;
      });
    }
  }

  ///检查是否命中缓存策略
  RiskCacheBean? _ifHitStrategy(Map<String, dynamic> params) {
    RiskCacheBean? lastCache = null;
    cacheStrategy.entries.forEach((element) {
      List<String> keys = element.key.split("^");
      String requestValues = keys.map((e) => params[e]).join("^");
      if (element.value.containsKey(requestValues)) {
        if (lastCache == null || (lastCache?.errorCode ?? 0) < element.value[requestValues]['errorCode']) {
          lastCache = RiskCacheBean(
              cacheKey: element.key,
              cacheValue: requestValues,
              errorLevel: element.value[requestValues]['errorLevel'],
              errorMessage: element.value[requestValues]['errorMessage'],
              errorCode: element.value[requestValues]['errorCode']);
        }
      }
    });
    return lastCache;
  }

  ///刷新本地缓存
  _freshSpCache() {
    sp?.setString(tag_risk_cache_key, jsonEncode(cacheStrategy));
  }

  ///上报风控埋点
  _trackEvent(int type, Map<String, dynamic> params, int errorCode) {
    if (type > 0) {
      final sn = params['machineId'] ?? '';
      final paperSerial = params['paperSerial'] ?? '';
      final ribbonSerial = params['ribbonSerial'] ?? '';
      final action = params['action'] ?? '';
      ToNativeMethodChannel().sendTrackingToNative({"track": 'show', 'posCode': '002_441',
        'ext': {'sn': sn, 'paperSerial': paperSerial, 'ribbonSerial': ribbonSerial, 'error_code': errorCode, 'action': action}});
    }
  }
}
