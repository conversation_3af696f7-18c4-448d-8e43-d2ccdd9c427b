import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:text/application.dart';
import 'package:text/network/dio_utils.dart';
import 'package:text/network/http_api.dart';

class PrintCountBusiness {
  static final Logger _logger = Logger("PrintCountBusiness", on: kDebugMode);
  static const String KEY_USER_PRINT_COUNT = "key_user_print_count";
  static const String KEY_LAST_USER_PRINT_COUNT_REQUEST = "key_last_user_print_count_request";
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');

  PrintCountBusiness._internal();

  factory PrintCountBusiness() => _instance;

  static late final PrintCountBusiness _instance = PrintCountBusiness._internal();

  Future<void> updateLocalUserPrintCount(int printNum, {String? uniAppId = ""}) async {
    if (!Application.isLogin) {
      return;
    }
    String userPrintCountKey = "${KEY_USER_PRINT_COUNT}_${Application.user?.userId}_$uniAppId";
    int userPrintCount = Application.sp.getInt(userPrintCountKey) ?? 0;
    int currentUserPrintCount = userPrintCount + printNum;
    await Application.sp.setInt(userPrintCountKey, currentUserPrintCount);
  }

  Future<void> initUserPrintCount(int printCount, {String? uniAppId = ""}) async {
    String userPrintCountKey = "${KEY_USER_PRINT_COUNT}_${Application.user?.userId}_$uniAppId";
    String lastUserPrintCountKey = "${KEY_LAST_USER_PRINT_COUNT_REQUEST}_${Application.user?.userId}_$uniAppId";
    await Application.sp.setInt(userPrintCountKey, printCount);
    await Application.sp.setString(lastUserPrintCountKey, _currentDate());
  }

  int getLocalUserPrintCount({String? uniAppId = ""}) {
    if (!Application.isLogin) {
      return 0;
    }
    String userPrintCountKey = "${KEY_USER_PRINT_COUNT}_${Application.user?.userId}_$uniAppId";
    int count = Application.sp.getInt(userPrintCountKey) ?? 0;
    return count;
  }

  //获取上次请求的时间
  String? getLastUserPrintCountRequestDate({String? uniAppId = ""}) {
    if (!Application.isLogin) {
      return null;
    }
    String lastUserPrintCountKey = "${KEY_LAST_USER_PRINT_COUNT_REQUEST}_${Application.user?.userId}_$uniAppId";
    String? date = Application.sp.getString(lastUserPrintCountKey);
    return date;
  }

  Future<int> requestUserPrintCount({String? uniAppId = ""}) async {
    if (uniAppId?.isNotEmpty ?? false) {
      int cacheCount = getLocalUserPrintCount(uniAppId: uniAppId);
      return cacheCount;
    }
    final lastRequestDate = getLastUserPrintCountRequestDate();
    if (lastRequestDate != null) {
      final lastDate = _dateFormat.parse(lastRequestDate);
      final today = DateTime.now();
      if (today.difference(lastDate).inDays == 0) {
        int cacheCount = getLocalUserPrintCount(uniAppId: uniAppId);
        return cacheCount;
      }
    }
    Completer<int> completer = Completer<int>();
    Map<String, dynamic> params = {};
    params["applicationCodes"] = "CP001";
    DioUtils.instance.requestNetwork<int>(Method.get, PrintCountApi.getUserPrintCount,
        queryParameters: params, isList: false, needLogin: true, onSuccess: (data) {
      _logger.log("用户打印张数 ${data}");
      initUserPrintCount(data ?? 0);
      completer.complete(data);
    }, onError: (code, message) {
      completer.complete(0);
    });
    return completer.future;
  }

  String _currentDate() => _dateFormat.format(DateTime.now());
}
