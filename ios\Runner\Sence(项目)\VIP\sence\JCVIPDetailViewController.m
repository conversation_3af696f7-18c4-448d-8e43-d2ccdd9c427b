//
//  JCVIPDetailViewController.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/23.
//  Copyright © 2021 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCVIPDetailViewController.h"
#import "JCVIPAutoRenewViewController.h"
#import "JCRedeemCodeViewController.h"
#import "JCVIPDetailView.h"
#import "JCVIPOperateAlert.h"
#import "JCUserVIPInfoModel.h"
#import "JCShopNormalVC.h"
#import "JCVIPRetentionManager.h"

@interface JCVIPDetailViewController ()
@property(nonatomic,strong) JCVIPDetailView *mainView;
@property(nonatomic,strong) JCUserCenterInfoModel *userVipInfoModel;
@property(nonatomic,strong) JCUserVIPInfoModel *vipInfoModel;
@property(nonatomic,strong) NSMutableArray *vipInfoModelsArr;
@property(nonatomic,strong) NSMutableArray *fontInfoModelsArr;
@property(nonatomic,strong) NSMutableArray *rightInfoModelsArr;
@property(nonatomic,strong) JCVIPOperateAlert *vipOperateAlert;
@property(nonatomic,assign) BOOL needShowNav;
@property(nonatomic,assign) MBProgressHUD *progressHud;
@property(nonatomic,assign)BOOL isDark; //默认白色
@property(nonatomic,strong) NSString *selectedProductId; //当前选中SKUID
/// VIP活动资格
@property(nonatomic,strong) JCQualificationModel *qualificationModel;
@end

@implementation JCVIPDetailViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.lsl_prefersNavigationBarHidden = YES;
    [[JCIAPHelper sharedInstance] setVipIAPSence:NormalVip];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(openVIPSuccessRefresh) name:JCNOTICATION_VIP_OPERATE_SUCCESS object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(openVIPSuccessRefresh) name:FLUTTER_LOGIN_SUCCESS object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(openVIPSuccessRefresh) name:LOGIN_CHANGED object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshStatusBarColor:) name:JCChangeVipStatusBarColor object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshSKUInfo) name:@"VIPCountDownTimeOut" object:nil];
    // Do any additional setup after loading the view.
}

-(void)refreshStatusBarColor:(NSNotification*)notify{
    BOOL isDark = [[notify object] boolValue];
    self.isDark = isDark;
    [self setNeedsStatusBarAppearanceUpdate];
}

- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    JC_TrackWithparms(@"view",@"029",(@{@"source_page":UN_NIL(self.sourcePage),@"act_name":UN_NIL(self.eventTitle)}));
}

- (UIStatusBarStyle)preferredStatusBarStyle {
    // 根据需要返回你想要的状态栏样式
    // 这里我们使用默认的状态栏颜色
    if (@available(iOS 13.0, *)) {
        return self.isDark == false ? UIStatusBarStyleLightContent : UIStatusBarStyleDarkContent;
    } else {
        // Fallback on earlier versions
        return UIStatusBarStyleDefault;
    }
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
}

- (void)initMainView{
    XYWeakSelf
    BOOL isNeedRenew = m_userModel.vipInfo != nil && !m_userModel.vipInfo.valid;
    JCVIPDetailView *mainView = [[JCVIPDetailView alloc] initWithFrame:CGRectZero isNeedRenewVIP:isNeedRenew showAllInfo:YES isShowUserInfo:YES isShowPointsBenefits:(self.vipInfoModel.gifts && [self.vipInfoModel.gifts isKindOfClass:[NSDictionary class]] && [self.vipInfoModel.gifts count] && [[XYCenter sharedInstance] isShowVipBenefits]) totalData:self.userVipInfoModel vipInfo:m_userModel.vipInfo];
    mainView.sourcePage = self.sourcePage;
    mainView.eventTitle = self.eventTitle;
    [mainView refreshUserVIPInfo:m_userModel.vipInfo];
    [mainView setBackBlock: ^{
        // 返回的block
        void (^backBlock)(void) = ^{
            if([weakSelf.navigationController.jk_rootViewController isKindOfClass:[weakSelf class]]){
                [weakSelf dismissViewControllerAnimated:YES completion:^{
                }];
            }else{
                [weakSelf.navigationController popViewControllerAnimated:YES];
            }
        };

        if (NETWORK_STATE_ERROR) {
            backBlock();
            return;
        }

        // 只有登录且非VIP才调用
        if (xy_isLogin && !m_user_vip) {
            BOOL isShow = [JCVIPRetentionManager handleVipRetentionModel:weakSelf.qualificationModel success:^{
                // 拉取新的SKU信息
                [weakSelf refreshSKUInfo:YES];
            } failure:^(NSString * _Nonnull msg, id  _Nonnull model) {

            }];
            if(!isShow){
                backBlock();
            }
        } else {
            backBlock();
        }
    }];
    [mainView setRestoreBlock: ^{
        [weakSelf restoreEvent];
    }];
    [mainView setLoginBlock: ^{
        if(!xy_isLogin){
            [[JCLoginManager sharedInstance] presentLoginViewController:^{

            } viewController:weakSelf loginSuccessBlock:^{

            }];
        }
    }];
    [mainView setRedeemCodeBlock:^{
        JCRedeemCodeViewController *vc = [[JCRedeemCodeViewController alloc] init];
        [weakSelf.navigationController pushViewController:vc animated:YES];
    }];
    [mainView setRightButtonBlock: ^{
        JCRedeemCodeViewController *vc = [[JCRedeemCodeViewController alloc] init];
        [weakSelf.navigationController pushViewController:vc animated:YES];
    }];
    [mainView setVipEventBlock:^(NSString *selectIndex) {
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        JCVIPEventModel *eventModel = [weakSelf.userVipInfoModel.showActivities safeObjectAtIndex:selectIndex.integerValue];
        JC_TrackWithparms(@"click",@"029_088_112",(@{@"b_name":UN_NIL(eventModel.title), @"a_name":UN_NIL(eventModel.activityName)}));
        NSString *link = eventModel.link;
        NSString *title = eventModel.title;
        NSString *typeCode = eventModel.typeCode;
        weakSelf.needShowNav = YES;
        if(STR_IS_NIL(link)){
            return;
        }
        [JCToNativeRouteHelp bannberJumpWith:typeCode routeString:link title:title sourceVC:weakSelf];
    }];
  [mainView setBannershowBlock:^(NSString *showIndex) {
      JCVIPEventModel *eventModel = [weakSelf.userVipInfoModel.showActivities safeObjectAtIndex:showIndex.integerValue];
      JC_TrackWithparms(@"show",@"029_088_112",(@{@"b_name":UN_NIL(eventModel.title), @"a_name":UN_NIL(eventModel.activityName)}));
  }];
    [mainView setRenewBlock: ^{
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        [weakSelf renewVip];
    }];
    [mainView setVipAgreeBlock: ^{
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        JCUserAgmentController *vc = [[JCUserAgmentController alloc] init];
        vc.type = 3;
        [weakSelf.navigationController pushViewController:vc animated:YES];
        NSLog(@"VIP协议");
    }];
    [mainView setVipRenewDescrBlock: ^{
        NSDictionary *autoPriceDic = [XYCenter sharedInstance].autoNewPriceDic;
        if((NETWORK_STATE_ERROR) || autoPriceDic.count == 0){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        JCUserAgmentController *vc = [[JCUserAgmentController alloc] initWithDismissBlock:^{} parmDic:autoPriceDic];
        vc.type = 5;
        [weakSelf.navigationController pushViewController:vc animated:YES];
        NSLog(@"VIP协议");
    }];
    [mainView setVipRenewAgreeBlock: ^{
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        JCUserAgmentController *vc = [[JCUserAgmentController alloc] init];
        vc.type = 4;
        [weakSelf.navigationController pushViewController:vc animated:YES];
        NSLog(@"VIP协议");
    }];

    [mainView setCouponQuestionBlock: ^{
        NSString *str = [XY_LANGUAGE_TITLE_NAMED(@"app01525", @"1、连续包年：包年期内赠送多张优惠 券，其中一张超值优惠券。\n2、连续包月：包月期内每月赠送一张优惠券。连续包月满11个月，赠送一张超值优惠券，有效期一个月。\n3、优惠券全场通用。") stringByReplacingOccurrencesOfString:@"\\n" withString:@" \r\n" ];
        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01524", @"优惠券说明") message:str cancelButtonTitle:nil sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707", @"我知道了") cancelBlock:nil sureBlock:^{
        } alertType:1];
    }];
    [mainView setOperateVIPBlock:^(NSString *priceId,NSString *proId, NSString *autoRenewal) {
        [weakSelf acticityVIPWith:priceId productId:proId isSubcripe:autoRenewal];
    }];
    [mainView setCancelAutoRenewBlock:^{
//        if(NETWORK_STATE_ERROR){
//            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
//            return;
//        }
//        JCVIPAutoRenewViewController *vc = [[JCVIPAutoRenewViewController alloc] init];
//        [weakSelf.navigationController pushViewController:vc animated:YES];
      if(NETWORK_STATE_ERROR){
          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
          return;
      }
      JCVIPAutoRenewViewController *vc = [[JCVIPAutoRenewViewController alloc] initWithDismissBlock:^{
      }];
      NSString *url = [NSString stringWithFormat:@"%@#/unsubscribe",ServerURL1];
      [vc loadUrl:url];
      vc.title = XY_LANGUAGE_TITLE_NAMED(@"app100000150", @"如何取消订阅服务？");
      [weakSelf.navigationController pushViewController:vc animated:YES];
    }];
  
    [mainView setAutoRenewBlock:^{
//        if(NETWORK_STATE_ERROR){
//            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
//            return;
//        }
//        JCVIPAutoRenewViewController *vc = [[JCVIPAutoRenewViewController alloc] init];
//        [weakSelf.navigationController pushViewController:vc animated:YES];
      if(NETWORK_STATE_ERROR){
          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
          return;
      }
      NSString *url = [NSString stringWithFormat:@"%@#/unsubscribe",ServerURL1];
//      [[JCLoginManager sharedInstance] checkLogin:^{
//
//      } viewController:weakSelf loginSuccessBlock:^{
          XYWKWebViewController *vipInfoVC = [[XYWKWebViewController alloc] initWithUrl:url];
      NSLog(@"-------%@",XY_LANGUAGE_TITLE_NAMED(@"app100000150", @""));
      NSLog(@"-------%@",XY_LANGUAGE_TITLE_NAMED(@"app100000150", @"如何取消订阅服务？"));
        vipInfoVC.title = XY_LANGUAGE_TITLE_NAMED(@"app100000150", @"如何取消订阅服务？");
          [weakSelf.navigationController pushViewController:vipInfoVC animated:YES];
//      }];
    }];
    
    [mainView setVipTakeEffectBlock:^{
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        NSString *url = [NSString stringWithFormat:@"%@/#/niim/user-order-search",NewH5BaseURL];
        [[JCLoginManager sharedInstance] checkLogin:^{

        } viewController:weakSelf loginSuccessBlock:^{
            XYWKWebViewController *vipInfoVC = [[XYWKWebViewController alloc] initWithUrl:url];
            vipInfoVC.title = XY_LANGUAGE_TITLE_NAMED(@"app100000443", @"订单明细");
            vipInfoVC.isSupportShare = NO;
            [weakSelf.navigationController pushViewController:vipInfoVC animated:YES];
        }];
    }];
    [mainView setVipDetailInfoBlock:^{
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        NSString *url = [NSString stringWithFormat:@"%@/#/niim/user-order-search",NewH5BaseURL];
        [[JCLoginManager sharedInstance] checkLogin:^{

        } viewController:weakSelf loginSuccessBlock:^{
            XYWKWebViewController *vipInfoVC = [[XYWKWebViewController alloc] initWithUrl:url];
            vipInfoVC.title = XY_LANGUAGE_TITLE_NAMED(@"app100000443", @"订单明细");
            vipInfoVC.isSupportShare = NO;
            [weakSelf.navigationController pushViewController:vipInfoVC animated:YES];
        }];
    }];
    [mainView setCoupinListBlock:^{
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        [JCRecordTool recordWithAction:click_my_orders];
        JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopChildPath:@"myCoupon" options:nil];
        vc.entrance_type_id = @"3";
        vc.jumpSource = @"y_page_user_vip_detail";
        [weakSelf.navigationController pushViewController:vc animated:YES];
    }];
    [mainView setSelectVipTypeBlock:^(id x) {
        JCVIPCostInfoModel *selectCostInfo;
        for (JCVIPCostInfoModel *costInfo in weakSelf.vipInfoModel.prices) {
            if(costInfo.isSelected.integerValue == 1){
                selectCostInfo = costInfo;
                break;
            }
        }
        [weakSelf.mainView refreshContentViewWith:weakSelf.vipInfoModel needLoadAll:NO];
    }];
    [self.view addSubview:mainView];
    [mainView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.leading.trailing.bottom.equalTo(weakSelf.view);
    }];
    self.mainView = mainView;
}

- (void)initNet{
    [super initNet];
    [[JCIAPHelper sharedInstance] setVipIAPSence:NormalVip];
    MBProgressHUD *progressHUD = [MBProgressHUD showHUDAddedTo:self.view animated:NO contentColor:hudContentColor backColor:hudBackColor];
    [self getUserCenterInfo:^{
        [progressHUD hideAnimated:YES];
    } failure:^(NSString *msg, id model) {
        [progressHUD hideAnimated:NO];
        [MBProgressHUD showToastWithMessageDarkColor:msg];
    } isIgnoreSelectedProductState:NO];

    // 只有登录且非VIP才调用
    if (xy_isLogin && !m_user_vip) {
        [JCVIPRetentionManager requestVipQualification:^(JCQualificationModel *model) {
            self.qualificationModel = model;
        } failure:^(NSString *msg, id model) {

        }];
    }
}

- (void)acticityVIPWith:(NSString *)priceId productId:(NSString *)proId isSubcripe:(NSString *)isSubcripe{
    XYWeakSelf
    BOOL isRenew = m_userModel.vipInfo != nil && !m_userModel.vipInfo.valid;
    NSString *trackPoint = isRenew?@"029_087":@"029_079";
    JC_TrackWithparms(@"click",trackPoint,(@{@"source_page":UN_NIL(weakSelf.sourcePage),@"act_name":UN_NIL(weakSelf.eventTitle)}));
    if(NETWORK_STATE_ERROR){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
        return;
    }
    if(xy_isLogin){
        [weakSelf openVIPWith:proId  priceId:priceId  isSubcripe:isSubcripe operateType:1];
    }else{
        JC_TrackWithparms(@"show",@"029_159",(@{}));

#pragma mark - 如果已经存在游客订单，这里需要提示客户是否继续购买，否则上次购买的游客订单会丢失，导致不能绑定;
      if([XYCenter sharedInstance].isSupportAnonymityBuyVip){
        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01521",@"开通VIP")
                       message:XY_LANGUAGE_TITLE_NAMED(@"app100000360",@"登录精臣云打印账号购买，可跨平台享受会员权益及商城优惠券等超值福利")
             cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消")
              sureButtonTitle1:XY_LANGUAGE_TITLE_NAMED(@"app100000361",@"登录账号购买(推荐)")
               sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000362",@"游客身份购买") cancelBlock:^{
          JC_TrackWithparms(@"click",@"029_159_154",(@{}));
        } sureBlock1:^{
          JC_TrackWithparms(@"click",@"029_159_152",(@{}));
          [[JCLoginManager sharedInstance] presentLoginViewController:^{
            
          } viewController:self loginSuccessBlock:^{
            [weakSelf openVIPWith:proId  priceId:priceId isSubcripe:isSubcripe operateType:1];
          }];
        } sureBlock:^{
          JC_TrackWithparms(@"click",@"029_159_153",(@{}));
          if(jc_current_isDeviceVip){
            [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示")
                           message:XY_LANGUAGE_TITLE_NAMED(@"app100000378",@"当前设备已匿名购买过vip，请登录后购买")
                 cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消")
                   sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01191",@"立即登录") cancelBlock:^{
            } sureBlock:^{
              [[JCLoginManager sharedInstance] presentLoginViewController:^{
                
              } viewController:weakSelf loginSuccessBlock:^{
                
              }];
            }];
            return;
          }
          [weakSelf openVIPWith:proId  priceId:priceId isSubcripe:isSubcripe operateType:1];
        } alertType:9];
      }else{
        [[JCLoginManager sharedInstance] checkLogin:^{
        } viewController:self loginSuccessBlock:^{
          [weakSelf openVIPWith:proId  priceId:priceId isSubcripe:isSubcripe operateType:1];
        }];
      }
    }

}

/// 刷新VIP活动
- (void)refreshVIPQualification {
    // 只有登录且非VIP才调用
    if (xy_isLogin && !m_user_vip) {
        [JCVIPRetentionManager requestVipQualification:^(JCQualificationModel *model) {
            self.qualificationModel = model;
        } failure:^(NSString *msg, id model) {

        }];
    } else {
        self.qualificationModel = nil;
    }
}

- (void)openVIPSuccessRefresh {
    XYWeakSelf
    // 拉取新的VIPSKU信息
    [self getUserCenterInfo:^{
        // 刷新整个页面显示
        [weakSelf.mainView refreshVipRightModel:weakSelf.userVipInfoModel];
    } failure:nil isIgnoreSelectedProductState:NO];
    [self setVipCouponInfo:self.userVipInfoModel];
    [self.mainView refreshUserVIPInfo:m_userModel.vipInfo];
    [self.mainView refreshContentViewWith:self.vipInfoModel needLoadAll:NO];
    [self refreshVIPQualification];
}

- (void)refreshSKUInfo:(BOOL)isIgnoreSelectedProductState {
    XYWeakSelf
    // 拉取新的VIPSKU信息
    [self getUserCenterInfo:^{
        // 刷新整个页面显示
        [weakSelf.mainView refreshVipRightModel:weakSelf.userVipInfoModel];
    } failure:nil isIgnoreSelectedProductState:isIgnoreSelectedProductState];
    [self setVipCouponInfo:self.userVipInfoModel];
    [self refreshVIPQualification];
}

/// 倒计时结束通知刷新UI
- (void)refreshSKUInfo {
    [self refreshSKUInfo:NO];
}

- (void)openVIPWith:(NSString *)proId priceId:(NSString *)priceId isSubcripe:(NSString *)isSubcripe operateType:(NSInteger)opertateType{
    XYWeakSelf
    if(NETWORK_STATE_ERROR){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
        return;
    }
    if(xy_isLogin && STR_IS_NIL(m_userModel.phone) && STR_IS_NIL(m_userModel.email)){
        [[JCLoginManager sharedInstance] checkBindWithviewController:self withBindType:1 withResponse:^{} withComplete:^(id x) {
            [weakSelf openVIPWith:proId priceId:priceId  isSubcripe:isSubcripe operateType:opertateType];
        }];
        return;
    }

    NSMutableDictionary *gifts = @{}.mutableCopy;
    if(self.vipInfoModel && IsNotEmptyDictionary(self.vipInfoModel.gifts)){
        [gifts addEntriesFromDictionary:self.vipInfoModel.gifts];
    }

    [JCIAPHelper openOrRenewVIPWith:proId priceId:priceId operateType:opertateType success:^(NSString *orderId){
        if(xy_isLogin){
          [self openVIPSuccessRefresh];
        }
    } failure:^(NSString *msg, id model) {
        [MBProgressHUD showToastWithMessageDarkColor:msg];
    } sourceInfo:@{@"sourcePage":UN_NIL(self.sourcePage),@"act_name":UN_NIL(self.eventTitle),@"gifts":gifts,@"autoRenewal":isSubcripe}];
}


- (void)renewVip{
    XYWeakSelf
    if(NETWORK_STATE_ERROR){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
        return;
    }
    if(xy_isLogin && STR_IS_NIL(m_userModel.phone) && STR_IS_NIL(m_userModel.email)){
        [[JCLoginManager sharedInstance] checkBindWithviewController:self withBindType:1 withResponse:^{} withComplete:^(id x) {

        }];
        return;
    }
    [JCIAPHelper renewUserVIPWith:self showAllInfo:NO success:^{
        [weakSelf openVIPSuccessRefresh];
    } failure:^(NSString *msg, id model) {
        [MBProgressHUD showToastWithMessageDarkColor:msg];
    } sourceInfo:@{@"sourcePage":UN_NIL(self.sourcePage),@"act_name":UN_NIL(self.eventTitle)}];
}

- (void)restoreEvent{
    if(NETWORK_STATE_ERROR){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
        return;
    }
    if(app_TestFlight == 1){
        if([JCIAPHelper sharedInstance].isShowingUNIAP){
            return;
        }else{
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app01541", @"抱歉，此测试版本无法开通或续费VIP") cancelButtonTitle:@"" sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707",@"我知道了") cancelBlock:^{

                } sureBlock:^{
                    [JCIAPHelper sharedInstance].isShowingUNIAP = NO;
                }];
            });
            [JCIAPHelper sharedInstance].isShowingUNIAP = YES;
        }
        return;
    }
    MBProgressHUD *hub = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
    [[JCIAPHelper sharedInstance] restorePurchasesWithCompleteHandle:^(IAPPurchType type, NSDictionary *dict) {
        //此处重新获取用户VIP信息，刷新界面
        dispatch_async(dispatch_get_main_queue(), ^{
            if(type == IAPPurchVerSuccess){
                [hub hideAnimated:YES];
                if(xy_isLogin && [m_userModel.userid isEqualToString:dict[@"userId"]]){
                    [[XYCenter sharedInstance] getNewUserModelInfo:^(__kindof YTKBaseRequest *request, id model) {
                        [self openVIPSuccessRefresh];
                    } failure:^(NSString *msg, id model) {
                        [MBProgressHUD showToastWithMessageDarkColor:msg];
                    } isNeedRefreshLoginInfo:YES];
                }
            }else if(type == IAPPurchCancle){
                [hub hideAnimated:YES];
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01567", @"已取消")];
            }else{
                [hub hideAnimated:YES];
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01568", @"恢复失败")];
            }
        });
    }];
}

/// 获取用户SKU信息，包含角标、权益等
/// - Parameters:
///   - success: 成功回调
///   - fail: 失败回调
- (void)getUserCenterInfo:(XYNormalBlock)success failure:(void(^)(NSString *msg, id model))fail isIgnoreSelectedProductState:(BOOL)isIgnore {
    NSString *storeCountry = STR_IS_NIL(jc_current_store_country)?@"CN":jc_current_store_country;
    NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithDictionary:@{@"region":storeCountry, @"languageCode":XY_JC_LANGUAGE_REAL}];
    if (!STR_IS_NIL([XYCenter sharedInstance].siteCode)) {
        dic[@"siteCode"] = [XYCenter sharedInstance].siteCode;
    }
    if (self.userVipInfoModel != nil && !isIgnore) {
        for (JCVIPCostInfoModel *costInfo in self.userVipInfoModel.prices) {
            if(costInfo.isSelected.integerValue == 1){
                self.selectedProductId = costInfo.productId;
            }
        }
    }
    [dic java_getWithModelType:[JCUserCenterInfoModel class] Path:J_get_VIP_Cost hud:nil Success:^(__kindof YTKBaseRequest *request, JCUserCenterInfoModel *vipInfoModel) {
        vipInfoModel.vipPackageDTO = [[JCUserVIPInfoModel alloc]init];
        vipInfoModel.vipPackageDTO.prices = vipInfoModel.prices;
        vipInfoModel.vipPackageDTO.activities = vipInfoModel.activities;
        self.userVipInfoModel = vipInfoModel;
        self.vipInfoModel = self.userVipInfoModel.vipPackageDTO;
        if(!STR_IS_NIL(self.selectedProductId) && !isIgnore) {
            for (JCVIPCostInfoModel *costInfo in vipInfoModel.vipPackageDTO.prices) {
                if([costInfo.productId isEqualToString:self.selectedProductId]){
                    costInfo.isSelected = @"1";
                }else{
                    costInfo.isSelected = @"0";
                }
            }
        }else{
            if(!STR_IS_NIL(self.anchorSubscribeId)){
                for (JCVIPCostInfoModel *costInfo in vipInfoModel.vipPackageDTO.prices) {
                    if([costInfo.priceId isEqualToString:self.anchorSubscribeId]){
                        costInfo.isSelected = @"1";
                    }//
                }
            }else{
                for (JCVIPCostInfoModel *costInfo in vipInfoModel.vipPackageDTO.prices) {
                    if(costInfo.isRecommended.integerValue == 1){
                        costInfo.isSelected = @"1";
                    }
                }
            }
        }
        [self setVipCouponInfo:vipInfoModel];
        if(self.mainView == nil){
            [self initMainView];
        }
        [self getStoreProductInfo];
        [self.mainView refreshUserVIPInfo:m_userModel.vipInfo];

        // 成功回调
        if (success != nil) {
            success();
        }
        if(xy_isLogin){
            [[XYCenter sharedInstance] getNewUserModelInfo:^(__kindof YTKBaseRequest *request, id model) {

            } failure:^(NSString *msg, id model) {
            } isNeedRefreshLoginInfo:NO];
        }

    } failure:^(NSString *msg, id model) {
        if (fail != nil) {
            fail(msg, model);
        }
    }];
}

//收到产品返回信息

- (void)getStoreProductInfo
{
    NSArray *product = [XYCenter sharedInstance].storeProductArr;//获取到的商品信息列表
    float oldMonthPrice = 0;
    float oldYearPrice = 0;
    NSMutableDictionary *priceDic = [NSMutableDictionary dictionary];
    NSString *productId = @"";
    float priceMultiplier = 1;
    BOOL isCableVip = [JCIAPHelper sharedInstance].vipSence == CableVip;
    if(isCableVip){
        for (JCVIPCostInfoModel *costInfo in self.vipInfoModel.prices) {
            if(costInfo.autoRenewal.integerValue != 1 && [costInfo.durationUnit isEqualToString:@"YEARS"]){
                productId = costInfo.productId;
                priceMultiplier = costInfo.originalPrice.floatValue/costInfo.salePrice.floatValue;
            }
        }
    }else{
        for (JCVIPCostInfoModel *costInfo in self.vipInfoModel.prices) {
            if(costInfo.autoRenewal.integerValue != 1 && [costInfo.durationUnit isEqualToString:@"MONTHS"]){
                productId = costInfo.productId;
                priceMultiplier = costInfo.originalPrice.floatValue/costInfo.salePrice.floatValue;
            }
        }
    }
    for (SKProduct *pro in product) {
        if([[pro productIdentifier] isEqualToString:productId]){
            if(isCableVip){
                float priceYear = pro.price.floatValue;
                oldYearPrice = priceYear * priceMultiplier;
            }else{
                float priceMonth = pro.price.floatValue;
                oldMonthPrice = priceMonth * priceMultiplier;
            }
        }
    }
    uploadLogInfoFlutter(UN_NIL([XYCenter sharedInstance].productsInfo.xy_toJsonString), @"VIP_PRODUCT_INFO",^(id x,id y){},J_get_sls_Log_Token);
    for (SKProduct *pro in product) {
        NSNumberFormatter *formatter = [[NSNumberFormatter alloc] init];
        [formatter setFormatterBehavior:NSNumberFormatterBehavior10_4];
        [formatter setNumberStyle:NSNumberFormatterCurrencyStyle];
        [formatter setLocale:pro.priceLocale];
        NSLocale * storeLocale = pro.priceLocale;
        NSString * storeCountry = (NSString *)CFLocaleGetValue((CFLocaleRef)storeLocale,kCFLocaleCountryCode);
        if([storeCountry isEqualToString:@"CN"] && [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]){
            self.vipInfoModel.isCN = @"1";
        }
        NSString *introductoryPrice = @"";
        if (@available(iOS 11.2, *)) {
            introductoryPrice = pro.introductoryPrice.price.stringValue;
        }
        for (JCVIPCostInfoModel *costInfo in self.vipInfoModel.prices) {
            if([[pro productIdentifier] isEqualToString:costInfo.productId]){
                costInfo.priceUnit = [pro.priceLocale objectForKey:NSLocaleCurrencySymbol];
                costInfo.salePrice = pro.price.stringValue;
                if(!STR_IS_NIL(introductoryPrice)){
                    costInfo.introductoryPrice = introductoryPrice;
                }
            }
            NSInteger originalPrice = costInfo.originalPrice.integerValue;
            if(oldMonthPrice > 0 || oldYearPrice > 0){
                if(costInfo.autoRenewal.integerValue == 1){
                    if([costInfo.timeUnit isEqualToString:@"YEARS"]){
                        originalPrice = (NSInteger)ceil(isCableVip?oldYearPrice:oldMonthPrice * 12);
                        costInfo.originalPrice = StringFromInt(originalPrice);
                        [priceDic setValue:costInfo.salePrice forKey:@"yearly"];
                    }else if([costInfo.timeUnit isEqualToString:@"QUARTERS"]){
                        originalPrice = (NSInteger)ceil(isCableVip?oldYearPrice/4:oldMonthPrice * 3);
                        costInfo.originalPrice = StringFromInt(originalPrice);
                        [priceDic setValue:costInfo.salePrice forKey:@"quaterly"];
                    }else if([costInfo.timeUnit isEqualToString:@"MONTHS"]){
                        originalPrice = (NSInteger)ceil(isCableVip?oldYearPrice/12:oldMonthPrice);
                        costInfo.originalPrice = StringFromInt(originalPrice);
                        [priceDic setValue:costInfo.salePrice forKey:@"monthly"];
                    }
                }else{
                    if([costInfo.durationUnit isEqualToString:@"YEARS"]){
                        originalPrice = (NSInteger)ceil(isCableVip?oldYearPrice:oldMonthPrice * 12);
                        costInfo.originalPrice = StringFromInt(originalPrice);
                        [priceDic setValue:costInfo.salePrice forKey:@"year"];
                    }else if([costInfo.durationUnit isEqualToString:@"QUARTERS"]){
                        originalPrice = (NSInteger)ceil(isCableVip?oldYearPrice/4:oldMonthPrice * 3);
                        costInfo.originalPrice = StringFromInt(originalPrice);
                        [priceDic setValue:costInfo.salePrice forKey:@"quater"];
                    }else if([costInfo.durationUnit isEqualToString:@"MONTHS"]){
                        originalPrice = (NSInteger)ceil(isCableVip?oldYearPrice/12:oldMonthPrice);
                        costInfo.originalPrice = StringFromInt(originalPrice);
                        [priceDic setValue:costInfo.salePrice forKey:@"month"];
                    }
                }
            }
        }
    }
    NSLog(@"价格体系：%@",priceDic);
    [XYCenter sharedInstance].storePriceDic = priceDic;
    [self.mainView refreshContentViewWith:self.vipInfoModel needLoadAll:YES];
}

- (NSString *)getLocalePrice:(SKProduct *)product {
    if (product) {
        NSNumberFormatter *formatter = [[NSNumberFormatter alloc] init];
        [formatter setFormatterBehavior:NSNumberFormatterBehavior10_4];
        [formatter setNumberStyle:NSNumberFormatterCurrencyStyle];
        [formatter setLocale:product.priceLocale];
        return [formatter stringFromNumber:product.price];
    }
    return @"";
}

- (void)setVipCouponInfo:(JCUserCenterInfoModel *)vipInfoModel{
    JCVIPCostInfoModel *selectCostInfo = nil;
    for (JCVIPCostInfoModel *costInfo in vipInfoModel.vipPackageDTO.prices) {
        if(m_user_vip){
            UserModel *userModel = m_userModel;
            UserVipInfo *vipInfo = userModel.vipInfo;
            if(vipInfo.autoRenewal.integerValue == costInfo.autoRenewal.integerValue){
                if(vipInfo.autoRenewal.integerValue == 1){
                    if([vipInfo.unit isEqualToString:costInfo.timeUnit]){
                        selectCostInfo = costInfo;
                    }
                }else{
                    if([vipInfo.unit isEqualToString:costInfo.durationUnit]){
                        selectCostInfo = costInfo;
                    }
                }
            }
        }
    }
    if(selectCostInfo == nil){
        for (JCVIPCostInfoModel *costInfo in vipInfoModel.vipPackageDTO.prices) {
            if(costInfo.isSelected.integerValue == 1){
                selectCostInfo = costInfo;
                break;
            }
        }
    }
}

@end
