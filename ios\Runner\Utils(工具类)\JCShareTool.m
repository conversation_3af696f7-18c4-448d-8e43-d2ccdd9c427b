//
//  JCShareTool.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2018/9/12.
//  Copyright © 2018年 xiaoyao. All rights reserved.
//

#import "JCShareTool.h"
#import "JCLanguageDetailModel.h"
#import "JCShareDetailView.h"
#import "JCStartViewController.h"
#import "JCMyTemplateDetailViewController.h"
#import "XYTabBarController.h"
#import "JCTemplateData.h"
#import "JCShopBaseWebViewController.h"
#import "JCActivityCodeEditViewController.h"
#import "JCContentPopBaseAlert.h"
#import "JCOtherAppAlert.h"
#import <TencentOpenAPI/QQApiInterfaceObject.h>
#import <TencentOpenAPI/TencentOAuth.h>
#import <TencentOpenAPI/QQApiInterface.h>
#import <DTShareKit/DTOpenKit.h>
#import <DTShareKit/DTOpenAPIObject.h>
#import "WXApi.h"
#import "FlutterBoostUtility.h"
#import "FBFlutterViewContainer.h"

#import "FlutterBoostUtility.h"
#import "JCGrayManager.h"
#import "JCTemplateImageManager.h"
#import "Runner-swift.h"
#import "JCThirdAppTool.h"
#import "JCThirdAppView.h"
#import "JCShareModel.h"
#import "JCPCWireNumberShareAlert.h"
#import "JCAppEventChannel.h"
#import "JCBluetoothManager.h"

#define PENDING_CLIPBOARD_KEY @"pendingClipboardDataForMiniApp"

@implementation JCShareTool

+ (void)getInvitationPush:(BOOL)isEnterForeground
{
    NSString *pasteboardSetting = [JCKeychainTool load:@"pasteboardSettingSwitch"];
    if(pasteboardSetting.integerValue == 0){
        return;
    }
    [self checkClipboardForMiniAppData];
    UIViewController *currentVC = [XYTool getCurrentVC];
    if ([currentVC isKindOfClass:[UIAlertController class]]) return;
    // 非后台进入前台&&小程序VC
    if (!isEnterForeground && [currentVC isKindOfClass:[NBCAPBridgeViewController class]]) {
      return;
    }
    if ([NSStringFromClass([currentVC class]) isEqualToString:@"Runner.NBCAPBridgeViewController"]) {
      return;
    }
    AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
    if(!appDelegate.appLaunched) return;
    XYWeakSelf
    [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"getFlutterPage" arguments:@{} result:^(NSString *pageInfo) {
        if(pageInfo != nil && [pageInfo isKindOfClass:[NSString class]] && ![pageInfo isEqualToString:@"login"]){
            [self asyncReadPasteboardWithCompletion:^(NSString *pasteboardString) {
              [weakSelf readShareContentFromPasteboard: pasteboardString];
            }];
        }
    }
    ];


}

+ (void)asyncReadPasteboardWithCompletion: (void(^)(NSString *))completion {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        CFTimeInterval start = CACurrentMediaTime();
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        NSString *text = [pasteboard.string copy];
        CFTimeInterval duration = CACurrentMediaTime() - start;
        NSLog(@"读取剪切板耗时: %f", duration);
        // 主线程读取改为子线程读取
        //        [pasteboard setValue:@"" forPasteboardType:UIPasteboardNameGeneral];
        if (text == nil || [text isEqualToString:@""]) {
            return;
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            completion(text);
        });
    });
}

+ (void)readShareContentFromPasteboard: (NSString *)pasteboardString{
    // 首先检查是否是PC端线号机分享码
    if ([self checkPCWireNumberShareCode:pasteboardString]) {
        return;
    }

    NSInteger shareType = 0;
    NSString* pasteUserDefaultStr = [[NSUserDefaults standardUserDefaults]objectForKey:@"pasteboardString"];
    NSString *shareCode = @"";
    NSMutableArray *rang1 = [XYTool getRangeStr:pasteboardString findText:@"⊙"];
    NSMutableArray *rang2 = [XYTool getRangeStr:pasteboardString findText:@"◎"];
    if(rang1.count < 2 && rang2.count < 2)
    {
        return;
    }else if(rang1.count == 2)
    {
        shareType = 1;
    }else{
        shareType = 2;
    }
    if(shareType == 1){
        NSUInteger start = ((NSNumber *)[rang1 safeObjectAtIndex:0]).integerValue+1;
        NSUInteger end = ((NSNumber *)[rang1 safeObjectAtIndex:1]).integerValue;
        NSUInteger length = end - start;
        if(length < 11){
            return;
        }
        shareCode = [pasteboardString substringWithRange:NSMakeRange(start, length)];
    }else{
        NSUInteger start = ((NSNumber *)[rang2 safeObjectAtIndex:0]).integerValue+1;
        NSUInteger end = ((NSNumber *)[rang2 safeObjectAtIndex:1]).integerValue;
        NSUInteger length = end - start;
        if(length < 9){
            return;
        }
        shareCode = [pasteboardString substringWithRange:NSMakeRange(start, length)];
    }
    if(STR_IS_NIL(pasteUserDefaultStr) || ![pasteUserDefaultStr isEqualToString:pasteboardString]) {
        [UIPasteboard generalPasteboard].string = @"";
        if(shareType == 1){
            [self templateShareWith:shareCode];
        }else if(shareType == 2){
            [self folderShareWith:shareCode];
        }
    }
}

// Add this new method to check clipboard
+ (void)checkClipboardForMiniAppData {
    UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
    NSString *clipboardString = pasteboard.string;

    if (clipboardString != nil) {
        NSRange range1 = [clipboardString rangeOfString:@"◇"];
        if (range1.location != NSNotFound) {
            NSRange range2 = [clipboardString rangeOfString:@"◇" options:NSCaseInsensitiveSearch range:NSMakeRange(range1.location + range1.length, clipboardString.length - (range1.location + range1.length))];
            if (range2.location != NSNotFound) {
                // Found two markers
                // Store the entire clipboard string in UserDefaults
                [[NSUserDefaults standardUserDefaults] setObject:clipboardString forKey:PENDING_CLIPBOARD_KEY];
                NSLog(@"[Clipboard Check] Stored clipboard content: %@", clipboardString);

                // Broadcast a notification with the entire clipboard string
                [[NSNotificationCenter defaultCenter] postNotificationName:@"JCNOTICATION_MINI_APP_CLIPBOARD"
                                                                    object:nil
                                                                  userInfo:@{@"data": clipboardString}];
                NSLog(@"[Clipboard Check] Posted clipboard notification");

                // Clear clipboard after processing
                pasteboard.string = @"";
            }
        }
    }
}

+ (void)forceDissmissOtherView:(NSInteger)type{
    UIViewController *currentVC = [XYTool getCurrentVC];
    __block UINavigationController *navVc = currentVC.navigationController;
    if(type == 1){
        UIView *view1 = nil;
        UIView *view2 = nil;
        UIView *view3 = nil;
        UIView *view4 = nil;
        for (UIView *view in currentVC.view.subviews) {
            if(view.tag == 900001){
                view1 = view;
            }
            if(view.tag == 900002){
                view2 = view;
            }
        }
        for (UIView *view in navVc.view.subviews) {
            if(view.tag == 900001){
                view1 = view;
            }
            if(view.tag == 900002){
                view2 = view;
            }
        }
        [view1 removeFromSuperview];
        view1 = nil;
        [view2 removeFromSuperview];
        view2 = nil;
        [view3 removeFromSuperview];
        view3 = nil;
        [view4 removeFromSuperview];
        view4 = nil;
    }else{
        for(UIView *view in navVc.view.subviews){
            if([view isKindOfClass:[JCContentPopBaseAlert class]]){
                JCContentPopBaseAlert *alertView = (JCContentPopBaseAlert *)view;
                [alertView hiddenContentAlert];
            }
        }

        for(UIView *view in  [[[UIApplication sharedApplication] delegate] window].subviews){
            if([view isKindOfClass:[JCVIPOperateAlert class]]){
                JCVIPOperateAlert *alertView = (JCVIPOperateAlert *)view;
                [alertView hide];
            }
        }

        for (UIView* view in [UIApplication sharedApplication].keyWindow.subviews)
        {
            if([view isKindOfClass:[JCContentPopBaseAlert class]]){
                JCContentPopBaseAlert *alertView = (JCContentPopBaseAlert *)view;
                alertView.hidden = YES;
                [alertView hiddenContentAlert];
            }else if([view isKindOfClass:[JCOtherAppAlert class]]){
                JCOtherAppAlert *alertView = (JCOtherAppAlert *)view;
                [alertView hiddenContentAlert];
            }
        }
        if (currentVC.presentingViewController != nil) {
            // 查找底部的XYTabBarController
            while (currentVC != nil && !([currentVC isKindOfClass:[XYTabBarController class]])) {
                [currentVC.presentingViewController dismissViewControllerAnimated:false completion:nil];
                currentVC = currentVC.presentingViewController;
            }

            if (currentVC) {
                navVc = ((XYTabBarController *)currentVC).selectedViewController;
            }
        }
    }
}

//处理模板分享
+ (void)templateShareWith:(NSString *)shareCode{
    if(NETWORK_STATE_ERROR){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
        return;
    }
    YTKNetworkConfig *config = [YTKNetworkConfig sharedConfig];
    config.baseUrl = ServerURL;
    [@{@"shareCode":UN_NIL(shareCode)} java_postWithModelType:nil Path:J_template_share_decode hud:@"" Success:^(__kindof YTKBaseRequest *request, NSDictionary *info) {
        if(info == nil || info.count == 0) {
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01212", @"分享码失效")];
            return;
        }
        CGFloat width = [[info objectForKey:@"width"] floatValue];
        CGFloat height = [[info objectForKey:@"height"] floatValue];
        NSString *name = [info objectForKey:@"name"];
        NSString *detailString = [NSString stringWithFormat:@"%@（%.2f*%.2f）",UN_NIL(name),width,height];
        [self forceDissmissOtherView:1];
        NSString *thumbNail = [info objectForKey:@"thumb"];
        UIView *mainView = [UIApplication sharedApplication].keyWindow;
        [[JCAppEventChannel shareInstance] eventData:@"kShareDetailViewShow"];
        [JCShareDetailView showWithView:mainView message:detailString imageUrl:thumbNail block:^(id x) {
            if([x isEqualToString:@"2"]){
                [self forceDissmissOtherView:2];
                NSString *templateType = [[info objectForKey:@"templateType"] stringValue];
                NSString *tagId = [NSString stringWithFormat:@"%@",[info objectForKey:@"id"]];
                [JCTemplateFunctionHelper getTemplateDetailRequestById:tagId complate:^(JCTemplateData *templateData){
                  if(templateType.integerValue == 1){
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.4 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [FlutterBoostUtility gotoFlutterPage:@"labelDetailPage"
                                                   arguments:@{@"jsonData": templateData.toJSONString}
                                              onPageFinished:^(NSDictionary *dic) {

                        }];
                    });
                  }else{
                    TemplateSource source = TemplateSource_New;
                    JCTemplateData *templateDataCopy = templateData.copy;
                    if ([self isTemplateSelf:templateDataCopy]) {
                        source = TemplateSource_Mine;
                    }else{
                      templateDataCopy.idStr = [XYTool randomElementId];
                    }
                    if(NETWORK_STATE_ERROR){
                      [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
                      return;
                    }
                    [JCTemplateFunctionHelper toEdit:templateDataCopy];
                  }
                } needLoading:YES];
            }
        }];

    } failure:^(NSString *msg, id model) {

    }];
}

+ (BOOL)isTemplateSelf:(JCTemplateData *)templateData{
    BOOL isMyTemplate = YES;
    NSString *templateUserId = templateData.profile.extrain.userId;
    if((templateData.isCloudTemplate ||
       ![templateUserId isEqualToString:m_userModel.userId]) && templateData.localType != JCLocalType_OffLineCreate){
        isMyTemplate = NO;
    }
    return isMyTemplate;
}

//处理文件夹分享
+ (void)folderShareWith:(NSString *)shareCode{
    NSString *requestPath = [NSString stringWithFormat:@"%@/%@",J_folder_share_decode,shareCode];
    XYMultipleBlock folderShareOperate = ^(NSDictionary *inviteInfo,NSString *operateType,NSString *userId){
        [self forceDissmissOtherView:2];
        NSString *code = inviteInfo[@"code"];
        [[JCLoginManager sharedInstance] checkLogin:^{

        } viewController:[XYTool getCurrentVC] loginSuccessBlock:^{
            [@{@"invitingCode": UN_NIL(code),@"operation": operateType} java_postWithModelType:nil Path:J_folder_share_operate hud:@"" Success:^(__kindof YTKBaseRequest *request, NSDictionary *info) {
                if([userId isEqualToString:m_userModel.userId]) return;
                NSMutableArray *navPushControllers = [NSMutableArray array];
                UIViewController *currentViewController = [XYTool getCurrentVC];
                float delay = 0;
                for (UIViewController *viewController in currentViewController.navigationController.viewControllers) {
                    if([viewController isKindOfClass:[FBFlutterViewContainer class]] && [((FBFlutterViewContainer *)viewController).name isEqualToString:@"myTemplate"]){
                        delay = 1;
                    }else{
                        [navPushControllers addObject:viewController];
                    }
                }
                if(delay > 0){
                    currentViewController.navigationController.viewControllers = navPushControllers;
                }
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delay * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    NSDictionary *arguments = @{@"token": m_userModel.token, @"isPresent": @NO,@"isEnablePopGesture": @NO,@"fromShareCode":@1};
                    [FlutterBoostUtility gotoFlutterPage:@"myTemplate"
                                               arguments:arguments
                                          onPageFinished:^(NSDictionary *_) {
                                        // 页面结束回传数据
                                    }];
                });
            } failure:^(NSString *msg, id model) {
                if([model isKindOfClass:[NSString class]] && ((NSString *)model).integerValue == 501001){
                    if(((NSString *)model).integerValue == 501001){
                        [JCAlert showAlertView:@"" message:XY_LANGUAGE_TITLE_NAMED(@"app100001398", @"你最多可加入3个共享者的邀请，现已达上限") cancelButtonTitle:@"" sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707",@"我知道了") cancelBlock:^{

                        } sureBlock:^{
                        }];
                    }else{
                        [MBProgressHUD showToastWithMessageDarkColor:msg];
                    }
                }else{
                    [MBProgressHUD showToastWithMessageDarkColor:msg];
                }
            }];
        }];
    };
    [@{} java_getWithModelType:nil Path:requestPath hud:@"" Success:^(__kindof YTKBaseRequest *request, NSDictionary *inviteInfo) {
        if(inviteInfo.count > 0 && inviteInfo[@"ownerProfile"] != nil){
            NSDictionary *ownerProfile = inviteInfo[@"ownerProfile"];
            NSString *userId = ownerProfile[@"id"];
            if([userId isEqualToString:m_userModel.userId]) return;
            NSString *displayName = ownerProfile[@"displayName"];
            if(STR_IS_NIL(displayName)){
                displayName = ownerProfile[@"displayUId"];
            }
            NSDictionary *memberLimitInfo = inviteInfo[@"group"];
            if(memberLimitInfo.count > 0){
                NSNumber *currentMemberCount = memberLimitInfo[@"memberCount"];
                NSNumber *maxMemberCount = memberLimitInfo[@"maxMember"];
                if(currentMemberCount.integerValue >= maxMemberCount.integerValue){
                    [JCAlert showAlertView:@"" message:[XY_LANGUAGE_TITLE_NAMED(@"app100001400", @"该共享者的共享成员已达到上限$人") stringByReplacingOccurrencesOfString:@"$" withString:StringFromInt(maxMemberCount.integerValue)] cancelButtonTitle:@"" sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707",@"我知道了") cancelBlock:^{

                    } sureBlock:^{
                    }];
                    return;
                }
            }
            [JCAlert showAlertView:[XY_LANGUAGE_TITLE_NAMED(@"app100001342", @"\"$\"邀请你加入他共享的文件夹") stringByReplacingOccurrencesOfString:@"$" withString:displayName] message:[XY_LANGUAGE_TITLE_NAMED(@"app100001343",@"加入共享后，可打印所有由\"$\"共享的文件夹") stringByReplacingOccurrencesOfString:@"$" withString:displayName] cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01428",@"忽略") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app100001344",@"接受") cancelBlock:^{
//                folderShareOperate(inviteInfo,@"denied");
            } sureBlock:^{
                folderShareOperate(inviteInfo,@"accept",userId);
            } alertType:7];
        }
    } failure:^(NSString *msg, id model) {
        if([model isKindOfClass:[NSString class]] && (((NSString *)model).integerValue == 10406 || ((NSString *)model).integerValue == 501001)){
            if(((NSString *)model).integerValue == 501001){
                [JCAlert showAlertView:@"" message:XY_LANGUAGE_TITLE_NAMED(@"app100001398", @"你最多可加入3个共享者的邀请，现已达上限") cancelButtonTitle:@"" sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707",@"我知道了") cancelBlock:^{

                } sureBlock:^{
                }];
            }else if(((NSString *)model).integerValue == 10406){
                [JCAlert showAlertView:@"" message:XY_LANGUAGE_TITLE_NAMED(@"app100001353", @"共享邀请码已过期，请联系共享者重新邀请。") cancelButtonTitle:@"" sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707",@"我知道了") cancelBlock:^{

                } sureBlock:^{
                }];
            }else{
                [MBProgressHUD showToastWithMessageDarkColor:msg];
            }
        }else{
            [MBProgressHUD showToastWithMessageDarkColor:msg];
        }
    }];
}

+ (void)shareContentToThirdAppWithShareInfo:(NSDictionary *)shareInfo needCheckLogin:(BOOL)isNeedCheckLogin thirdAppType:(NSInteger)thirdAppType{
    if(thirdAppType == 0){
        [self shareContentToWechatWithShareInfo:shareInfo needCheckLogin:isNeedCheckLogin];
    }else if(thirdAppType == 1){
        [self shareContentToQQWithShareInfo:shareInfo needCheckLogin:isNeedCheckLogin];
    }else if(thirdAppType == 2){
        [self shareContentToDingTalkWithShareInfo:shareInfo needCheckLogin:isNeedCheckLogin];
    }
}

+ (void)shareContentToWechatWithShareInfo:(NSDictionary *)shareInfo needCheckLogin:(BOOL)isNeedCheckLogin{
    if(![WXApi isWXAppInstalled]){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01193",@"您没有安装手机微信")];
        return;
    }
    NSString *title = shareInfo[@"title"];
    NSString *description = shareInfo[@"description"];
    NSString *shareUrl = shareInfo[@"shareUrl"];
    NSString *icon = shareInfo[@"icon"];
    NSData * iconData = shareInfo[@"iconData"];
    NSString * fileExtension = shareInfo[@"fileExtension"];
    if(isNeedCheckLogin){
        NSNumber *needBindMainAccount = shareInfo[@"needBindMainAccount"];
        if(needBindMainAccount.boolValue){
            if(xy_isLogin && STR_IS_NIL(m_userModel.phone) && STR_IS_NIL(m_userModel.email)){
                UIViewController *currentVC = [XYTool getCurrentVC];
                [[JCLoginManager sharedInstance] checkBindWithviewController:currentVC withBindType:1 withResponse:^{

                } withComplete:^(id x) {
                    [self shareContentToWechatWithShareInfo:shareInfo needCheckLogin:isNeedCheckLogin];
                }];
                return;
            }
        }
    }
    if(iconData != nil){
        [self configWXShare:fileExtension title:title description:description data:iconData shareUrl:shareUrl];
    }else{
        NSURLSession *theURLSession = [NSURLSession sessionWithConfiguration:[NSURLSessionConfiguration defaultSessionConfiguration]];
        NSURLSessionDataTask *theURLSessionDataTask = [theURLSession dataTaskWithURL:XY_URLWithString(icon)
                                                                       completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error)
        {
            [self configWXShare:fileExtension title:title description:description data:data shareUrl:shareUrl];
        }];
        [theURLSessionDataTask resume];
    }

}

+(void)configWXShare:(NSString *)fileExtension title:(NSString *)title description:(NSString *)description data:(NSData *)data shareUrl:(NSString *)shareUrl{
    SendMessageToWXReq *sendReq = [[SendMessageToWXReq alloc] init];

    sendReq.bText = NO;//不使用文本信息

    sendReq.scene = 0;//0 = 好友列表 1 = 朋友圈 2 = 收藏

    //创建分享内容对象

    WXMediaMessage *urlMessage = [WXMediaMessage message];

    urlMessage.title = fileExtension.length > 0 ? [NSString stringWithFormat:@"%@.%@",title,fileExtension] : title;//分享标题

    urlMessage.description = description;//分享描述/分享图片,使用SDK的setThumbImage方法可压缩图片大小

    urlMessage.thumbData = data;
    //创建多媒体对象

    WXWebpageObject *webObj = [WXWebpageObject object];

    webObj.webpageUrl = shareUrl;//分享链接

    //完成发送对象实例

    urlMessage.mediaObject = webObj;

    sendReq.message = urlMessage;
    //发送分享信息

    [WXApi sendReq:sendReq completion:^(BOOL success) {
        if(!success){
            NSLog(@"分享调起失败");
        }
    }];
}

+ (void)shareContentToQQWithShareInfo:(NSDictionary *)shareInfo needCheckLogin:(BOOL)isNeedCheckLogin{
    if(![TencentOAuth iphoneQQInstalled]){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01192",@"您没有安装手机QQ")];
        return;
    }
//    TencentOAuth *auth = [[TencentOAuth alloc] initWithAppId:@"101553333" andDelegate:self];
    NSString *title = shareInfo[@"title"];
    NSString *description = shareInfo[@"description"];
    NSString *shareUrl = shareInfo[@"shareUrl"];
    NSString *icon = shareInfo[@"icon"];
    //创建分享内容对象
    QQApiNewsObject *newsObj=[QQApiNewsObject objectWithURL:[NSURL URLWithString:shareUrl] title:title description:description previewImageURL:[NSURL URLWithString:icon]];

    SendMessageToQQReq  *req = [SendMessageToQQReq reqWithContent:newsObj];
//    //将内容分享到qq
    QQApiSendResultCode sent = [QQApiInterface sendReq:req];
    [QQApiInterface startLogWithBlock:^(NSString *logStr) {
        NSLog(@"QQ分享日志:%@ ",logStr);
    }];
//    [QQApiInterface openQQ];
}

+ (void)shareContentToDingTalkWithShareInfo:(NSDictionary *)shareInfo needCheckLogin:(BOOL)isNeedCheckLogin{
    NSString *title = shareInfo[@"title"];
    NSString *description = shareInfo[@"description"];
    NSString *shareUrl = shareInfo[@"shareUrl"];
    NSString *icon = shareInfo[@"icon"];
    // 创建分享对象
    DTSendMessageToDingTalkReq *sendMessageReq = [[DTSendMessageToDingTalkReq alloc] init];

    DTMediaMessage *mediaMessage = [[DTMediaMessage alloc] init];
    DTMediaWebObject *webObject = [[DTMediaWebObject alloc] init];
    webObject.pageURL = shareUrl;

    mediaMessage.title = title;

    mediaMessage.thumbURL = icon;

    // Or Set a image data which less than 32K.
    // mediaMessage.thumbData = UIImagePNGRepresentation([UIImage imageNamed:@"open_icon"]);

    mediaMessage.messageDescription = description;
    mediaMessage.mediaObject = webObject;
    sendMessageReq.message = mediaMessage;

    BOOL result = [DTOpenAPI sendReq:sendMessageReq];
    if (result)
    {
        NSLog(@"Link 发送成功.");
    }
    else
    {
        NSLog(@"Link 发送失败.");
    }
}

#pragma mark - PC端线号机分享码处理

+ (BOOL)checkPCWireNumberShareCode:(NSString *)pasteboardString {
    if (STR_IS_NIL(pasteboardString)) {
        return NO;
    }

    // 检查是否包含PC端线号机分享码的特征
    // 格式：【222（1.5平方瓷白 长27mm）】，复制这条信息☺AHx6FmP07X51Eb4O8nTQpto=☺，打开👉精臣云打印👈
    NSRange range1 = [pasteboardString rangeOfString:@"☺"];
    if (range1.location == NSNotFound) {
        return NO;
    }

    NSRange range2 = [pasteboardString rangeOfString:@"☺" options:NSCaseInsensitiveSearch range:NSMakeRange(range1.location + range1.length, pasteboardString.length - (range1.location + range1.length))];
    if (range2.location == NSNotFound) {
        return NO;
    }

    // 提取两个☺符号之间的分享码
    NSUInteger start = range1.location + range1.length;
    NSUInteger length = range2.location - start;
    NSString *shareCode = [pasteboardString substringWithRange:NSMakeRange(start, length)];

    // 清空剪贴板
    [UIPasteboard generalPasteboard].string = @"";

    // 处理C1模板分享码
    [self handleC1TemplateShareCode:shareCode];

    return YES;
}

+ (void)showPCWireNumberShareAlert:(NSString *)shareContent {
    dispatch_async(dispatch_get_main_queue(), ^{
        JCPCWireNumberShareAlert *alert = [[JCPCWireNumberShareAlert alloc] initWithShareContent:shareContent];
        [alert show];
    });
}

+ (void)handleC1TemplateShareCode:(NSString *)shareCode {
    // 检查当前是否已经在C1页面
    if ([JCBluetoothManager sharedInstance].deviceType == JCBluetoothC1) {
        // 已经在C1页面，检查解码状态并发送事件
        [self checkC1TemplateShareDecodeStatusAndSendEvent:shareCode];
    } else {
        // 不在C1页面，显示弹窗
        [self showPCWireNumberShareAlert:shareCode];
    }
}

+ (void)checkC1TemplateShareDecodeStatusAndSendEvent:(NSString *)shareCode {
    // 检查C1模板分享解码状态
    // 对应Android端的 FlutterMethodInvokeManager.checkC1TemplateShareDecodeStatus
    [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"checkC1TemplateShareDecodeStatus" arguments:nil result:^(NSNumber *canShareDecode) {
        // 只有在不是编辑状态时才发送事件
        if (canShareDecode.boolValue) {
            dispatch_async(dispatch_get_main_queue(), ^{
                // 发送事件到Flutter端
                if ([JCAppEventChannel shareInstance].eventSink) {
                    [[JCAppEventChannel shareInstance] eventData:@{
                        @"C1TemplateShareCode": shareCode
                    }];
                }
            });
        }
    }];
}

+ (void)shareUserTemplate:(JCTemplateData *)templateData{
  UIViewController *currentVc = [XYTool getCurrentVC];
  if([templateData isNewCommodityTemplate] || [templateData isBusinessTemplate]){
      if([templateData isHaveCustomField]){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100001567", @"商品库模版不允许分享")];
        return;
      }

  }
  if (!xy_isLogin) {
      [[JCLoginManager sharedInstance] checkLogin:^{

      } viewController:currentVc loginSuccessBlock:^{

      }];
      return;
  }
  NSArray *thirdAppCanOpenArr = [JCThirdAppTool thirdShareAppCanOpen];
  JCThirdAppView *thirdView = [JCThirdAppView xy_xib];
  [thirdView showThirdAppViewWith:thirdAppCanOpenArr type:1 lastLogin:nil];
  __weak typeof(thirdView) weakthirdView = thirdView;
  [thirdView setThirdAppSelectBlock:^(NSNumber *selectIndex) {
      if(selectIndex.integerValue == -1){
          JC_TrackWithparms(@"click",@"012_036_045",(@{@"temp_id":UN_NIL(templateData.idStr)}));
      }else{
        [self getShareCodeRequest:thirdAppCanOpenArr[selectIndex.integerValue] template:templateData thirdSharView:weakthirdView];
      }
  }];
}

+ (void)getShareCodeRequest:(JCThirdAppInfo *)selectThirdApp template:(JCTemplateData *)templateData thirdSharView:(JCThirdAppView *)thirdView{
    XYWeakSelf
    NSString *appName = XY_LANGUAGE_TITLE_NAMED(selectThirdApp.appName, @"");
    JC_TrackWithparms(@"click",@"012_036_044",(@{@"temp_id":UN_NIL(templateData.idStr),@"chan":UN_NIL(appName)}));
    [JCRecordTool recordWithAction:click_share_template];
    if(NETWORK_STATE_ERROR){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
        return;
    }
    [@{@"id":UN_NIL(templateData.idStr)} java_postWithModelType:[JCShareModel class] Path:J_template_share_encode hud:@"" Success:^(__kindof YTKBaseRequest *request, JCShareModel *model) {
      [weakSelf showShareMsgView:selectThirdApp template:templateData shareCode:model.sharCode thirdSharView:thirdView];
    } failure:^(NSString *msg, id model) {
      if(NETWORK_STATE_ERROR){
          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
      }else{
          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app00404", @"您的分享口令生成失败，请重新点击分享")];
      }
    }];
}

+ (void)showShareMsgView:(JCThirdAppInfo *)selectThirdApp template:(JCTemplateData *)templateData shareCode:(NSString *)shareCode thirdSharView:(JCThirdAppView *)thirdView{
    XYWeakSelf
    NSString *templateSizeString = [NSString stringWithFormat:@"（%.0f*%.0f）",templateData.width,templateData.height];
    NSString *shareMsgString = [NSString stringWithFormat:@"【%@%@】，%@⊙%@⊙，%@👉%@👈。",templateData.name,templateSizeString,XY_LANGUAGE_TITLE_NAMED(@"app00399", @"复制这条信息"),shareCode,XY_LANGUAGE_TITLE_NAMED(@"app00400", @"打开"),XY_LANGUAGE_TITLE_NAMED(@"app00401", @"精臣打印APP")];
    [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00398", @"您的分享口令已生成") message:shareMsgString cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00402", @"不分享了") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00403", @"去粘贴") cancelBlock:nil sureBlock:^{
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        pasteboard.string = shareMsgString;
        [[NSUserDefaults standardUserDefaults]setObject:pasteboard.string forKey:@"pasteboardString"];
        [weakSelf toPasteShareCodeWithThirdApp:selectThirdApp];
        [thirdView hide];
    }];
}

+ (void)toPasteShareCodeWithThirdApp:(JCThirdAppInfo *)selectThirdApp{
    [[UIApplication sharedApplication] openURL:[NSURL URLWithString:selectThirdApp.openUrl] options:@{} completionHandler:^(BOOL success) {}];
}
@end
