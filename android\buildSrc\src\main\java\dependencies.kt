object AndroidX{
    const val appcompat = "androidx.appcompat:appcompat:1.4.1"
    const val palette = "androidx.palette:palette:1.0.0"
    const val coreKtx = "androidx.core:core-ktx:1.11.0-beta02"
    const val core = "androidx.core:core:1.11.0-beta02"
    const val legacy = "androidx.legacy:legacy-support-v4:1.0.0"
    const val multidex = "androidx.multidex:multidex:2.0.0"
    const val cardView = "androidx.cardview:cardview:1.0.0"
    const val recyclerview = "androidx.recyclerview:recyclerview:1.2.1"
    const val documentFile = "androidx.documentfile:documentfile:1.0.1"
    const val constraintLayout = "androidx.constraintlayout:constraintlayout:2.0.4"

    private const val nav_version = "2.3.5"
    const val navigation_fragment_ktx = "androidx.navigation:navigation-fragment-ktx:$nav_version"
    const val navigation_ui_ktx = "androidx.navigation:navigation-ui-ktx:$nav_version"

    const val fragment_ktx = "androidx.fragment:fragment-ktx:1.3.4"
    const val activity_ktx = "androidx.activity:activity-ktx:1.3.1"

    const val navigationGradlePlugin = "androidx.navigation:navigation-safe-args-gradle-plugin:$nav_version"
    const val work = "androidx.work:work-runtime-ktx:2.7.1"
}

object CameraX {
    private const val version = "1.1.0-alpha08"
    // CameraX core library using camera2 implementation
    const val camera2 = "androidx.camera:camera-camera2:$version"
    const val cameraCore = "androidx.camera:camera-core::$version"
    // CameraX Lifecycle Library
    const val cameraLifecycle = "androidx.camera:camera-lifecycle:$version"
    // CameraX View class
    const val cameraView = "androidx.camera:camera-view:1.0.0-alpha28"
}
object Kotlin{
    private const val version = "1.9.23"
    const val kotlinJava = "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$version"
    const val kotlin = "org.jetbrains.kotlin:kotlin-stdlib:$version"
    const val kotlinxCoroutinesAndroid = "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1"

    const val kotlinGradlePlugin = "org.jetbrains.kotlin:kotlin-gradle-plugin:$version"
//    const val kotlinAndroidExtension = "org.jetbrains.kotlin:kotlin-android-extensions:$version"
}

object Compose {
    /// Compose 与 Kotlin 的兼容性对应关系
    /// https://developer.android.com/jetpack/androidx/releases/compose-kotlin#kts
    const val compileVersion = "1.2.0-rc02"
    const val version = "1.1.0"
    const val runtime = "androidx.compose.runtime:runtime:$version"
    const val runtime_livedata = "androidx.compose.runtime:runtime-livedata:$version"

    const val foundation = "androidx.compose.foundation:foundation:$version"
    const val foundationLayout = "androidx.compose.foundation:foundation-layout:$version"
    const val ui = "androidx.compose.ui:ui:$version"
    const val material = "androidx.compose.material:material:$version"
    const val materialIconsExtended = "androidx.compose.material:material-icons-extended:$version"

    const val tooling = "androidx.compose.ui:ui-tooling:$version"
    const val animation = "androidx.compose.animation:animation:$version"
    const val constraintLayout = "androidx.constraintlayout:constraintlayout-compose:1.0.0-beta02"
    const val activityCompose = "androidx.activity:activity-compose:1.3.1"
}

object Accompanist{
    private const val version = "0.18.0"
    const val insets = "com.google.accompanist:accompanist-insets:$version"
    const val systemUiController = "com.google.accompanist:accompanist-systemuicontroller:$version"
    const val pager = "com.google.accompanist:accompanist-pager:$version"
    const val pagerIndicators = "com.google.accompanist:accompanist-pager-indicators:$version"
}

object Lifecycle {
    private const val version = "2.5.1"
    const val viewModelCompose = "androidx.lifecycle:lifecycle-viewmodel-compose:1.0.0-alpha07"
    const val viewModel = "androidx.lifecycle:lifecycle-viewmodel-ktx:$version"
    const val viewmodel_savedstate = "androidx.lifecycle:lifecycle-viewmodel-savedstate:$version"

    const val lifecycle_livedata_core_ktx = "androidx.lifecycle:lifecycle-livedata-core-ktx:$version"
    const val lifecycle_livedata_ktx = "androidx.lifecycle:lifecycle-livedata-ktx:$version"
    const val viewModelScope = "androidx.lifecycle:lifecycle-viewmodel-ktx:$version"
    const val lifecycleScope = "androidx.lifecycle:lifecycle-runtime-ktx:$version"
}

object DbLib{
    private const val version = "2.4.0"
    const val room_runtime = "androidx.room:room-runtime:$version"
    const val room_compiler = "androidx.room:room-compiler:$version"
    const val room_ktx = "androidx.room:room-ktx:$version"

    const val greendao = "org.greenrobot:greendao:3.3.0"
    const val greendaoGenerator = "org.greenrobot:greendao-generator:3.3.0"
}

object HiltLib{
    private const val version = "2.51.1"
    const val hiltGradlePlugin = "com.google.dagger:hilt-android-gradle-plugin:$version"
    const val hiltAndroid = "com.google.dagger:hilt-android:$version"
    const val hiltCompiler = "com.google.dagger:hilt-android-compiler:$version"
}

/**
 * 业务相关依赖库
 */
object ServiceLib{
    /**架构组一键登录和三方登陆库*/
    const val auth2Library = "com.niimbot.auth2library:auth2library:1.1.18"
    const val facebookLogin = "com.facebook.android:facebook-login:13.1.0"
    const val ucCrashSdk= "com.ucweb.wpk:crashsdk-java:3.2.0.1"
    const val tencentOpenSdk= "com.tencent.mm.opensdk:wechat-sdk-android:6.8.18"

    /**架构组支付库*/
    const val paymentLibrary = "com.niimbot.paymentlibrary:paymentlibrary:1.2.0"
    /**阿里SLS支持库*/
    const val aliLog = "com.aliyun.openservices:aliyun-log-android-sdk:2.6.11"
    /**阿里OSS支持库*/
    const val aliOSS = "com.aliyun.dpa:oss-android-sdk:2.9.5"

    /**google play内购结算库*/
    const val googleBilling = "com.android.billingclient:billing:7.0.0"
    const val googleBillingKt = "com.android.billingclient:billing-ktx:7.0.0"

    /**环信客服SDK*/
    const val easeKefu = "com.hyphenate:kefu-easeui-android:latest.release" //或者 compile 'com.hyphenate:kefu-easeui-android:1.1.9r2'
    const val easeSDK = "com.easemob:kefu-sdk:1.2.7.1"

    /**ionic支持库cordov*/
    const val cordovaSDK = "org.apache.cordova:framework:7.0.0"
}

/**
 * 网络相关第三方库
 */
object NetworkLib{
    const val retrofit2 = "com.squareup.retrofit2:retrofit:2.8.1"
    const val retrofit2Rxjava2 = "com.squareup.retrofit2:adapter-rxjava2:2.8.1"
    const val retrofit2Gson = "com.squareup.retrofit2:converter-gson:2.8.1"
    const val retrofit2Scalars = "com.squareup.retrofit2:converter-scalars:2.8.1"
    const val okhttp = "com.squareup.okhttp3:okhttp:4.9.0"
    const val okhttpLogInterceptor = "com.squareup.okhttp3:okhttp:logging-interceptor:4.9.0"
    const val okhttpV2 = "com.squareup.okhttp:okhttp:2.7.5"
    const val okGo = "com.lzy.net:okgo:3.0.4"

    private const val apolloVersion = "3.7.1"
    const val apolloGraphGL = "com.apollographql.apollo3:apollo-runtime:$apolloVersion"
    const val apolloGraphGLPlugin = "com.apollographql.apollo3:apollo-gradle-plugin:$apolloVersion"
}

/**
 * 图片相关第三方库
 */
object ImageLib{
    /**拍照，本地相册选择，裁剪等集合库*/
//    const val pictureSelector = "io.github.lucksiege:pictureselector:v2.7.3-rc10"
        const val pictureSelector = "io.github.lucksiege:pictureselector:v3.11.1"
    //    const val pictureSelector_compress = "io.github.lucksiege:compress:v3.11.1"
        const val pictureSelector_ucrop = "io.github.lucksiege:ucrop:v3.11.1"
    /**图片处理库*/
    const val luBan = "top.zibin:Luban:1.1.8"

    /**glide图片加载库*/
    private const val glide_version = "4.11.0"
    const val glide = "com.github.bumptech.glide:glide:$glide_version"
    const val glideOkhttp3 = "com.github.bumptech.glide:okhttp3-integration:$glide_version"
    const val glideCompiler = "com.github.bumptech.glide:compiler:$glide_version"

    const val coilCompose = "io.coil-kt:coil-compose:1.3.0"
}

/**
 * 控件相关第三方库
 */
object WidgetLib{

    const val design = "com.google.android.material:material:1.4.0"

    /**滚动选择控件*/
    const val pickView = "com.brucetoo.pickview:library:1.2.3"
    /**RecyclerView辅助类*/
    const val BaseRecyclerViewAdapterHelper = "com.github.CymChad:BaseRecyclerViewAdapterHelper:2.9.47-androidx"
    /**圆形进度条控件*/
    const val circleProgressbar = "com.dinuscxj:circleprogressbar:1.3.6"
    /**红点控件*/
    const val badge = "com.github.nekocode:Badge:2.1"
    /**banner控件*/
    const val banner = "com.github.bingoogolapple:BGABanner-Android:3.0.1"
}

object UtilLib{
    /**google提供的各国手机号合法性校验库*/
    const val phoneNumberLib = "com.lionscribe.open.libphonenumber:libphonenumber:8.12.18.1"
    /**二维码扫描库*/
//    const val zxingLite = "com.github.jenly1314:zxing-lite:2.1.1"
    /**开发辅助工具库*/
    const val utilCode = "com.blankj:utilcodex:1.31.1"
    const val leakcanaryAndroid = "com.squareup.leakcanary:leakcanary-android:2.6"
    /**事件主线工具*/
    const val eventbus = "org.greenrobot:eventbus:3.1.1"
    /**Gson字符串处理工具*/
    const val gson = "com.google.code.gson:gson:2.8.5"
    /**fastjson字符串处理工具*/
    const val fastjson = "com.alibaba:fastjson:1.2.83"
    /**google play市场应用内评分*/
    const val googlePlayCore = "com.google.android.play:review:2.0.1"
    const val googlePlayCoreKt = "com.google.android.play:review-ktx:2.0.1"

    const val mmkv = "com.tencent:mmkv:2.0.0-niimbot"
    const val googleServiceBase = "com.google.android.gms:play-services-base:17.6.0"

    /**gif图片显示工具*/
    const val gif = "pl.droidsonroids.gif:android-gif-drawable:1.2.25"
    /**版本号对比工具*/
    const val versionCompare = "net.swiftzer.semver:semver:1.1.2"
    /**lottie动画支持库*/
    const val lottie = "com.airbnb.android:lottie:5.2.0"
    /**bugly崩溃上报*/
    const val bugly = "com.tencent.bugly:crashreport:4.1.9.3"
    const val buglyNative = "com.tencent.bugly:nativecrashreport:3.9.2"
}

object ShareLib{
    const val dingding = "com.alibaba.android:ddsharesdk:1.2.2"
}

object CrashLib{
    const val googleService = "com.google.gms:google-services:4.3.15"
    const val firebaseCrashlytics = "com.google.firebase:firebase-crashlytics-gradle:2.9.2"
}
