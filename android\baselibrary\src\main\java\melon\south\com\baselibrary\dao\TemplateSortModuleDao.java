package melon.south.com.baselibrary.dao;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import melon.south.com.baselibrary.local.module.TemplateSortModule;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TEMPLATE_SORT_MODULE".
*/
public class TemplateSortModuleDao extends AbstractDao<TemplateSortModule, Long> {

    public static final String TABLENAME = "TEMPLATE_SORT_MODULE";

    /**
     * Properties of entity TemplateSortModule.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "_id");
        public final static Property TemplateId = new Property(1, String.class, "templateId", false, "TEMPLATE_ID");
        public final static Property UseTime = new Property(2, String.class, "useTime", false, "USE_TIME");
        public final static Property HasUpload = new Property(3, int.class, "hasUpload", false, "HAS_UPLOAD");
        public final static Property UserId = new Property(4, String.class, "userId", false, "USER_ID");
    }


    public TemplateSortModuleDao(DaoConfig config) {
        super(config);
    }
    
    public TemplateSortModuleDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TEMPLATE_SORT_MODULE\" (" + //
                "\"_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"TEMPLATE_ID\" TEXT UNIQUE ," + // 1: templateId
                "\"USE_TIME\" TEXT," + // 2: useTime
                "\"HAS_UPLOAD\" INTEGER NOT NULL ," + // 3: hasUpload
                "\"USER_ID\" TEXT);"); // 4: userId
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TEMPLATE_SORT_MODULE\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, TemplateSortModule entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String templateId = entity.getTemplateId();
        if (templateId != null) {
            stmt.bindString(2, templateId);
        }
 
        String useTime = entity.getUseTime();
        if (useTime != null) {
            stmt.bindString(3, useTime);
        }
        stmt.bindLong(4, entity.getHasUpload());
 
        String userId = entity.getUserId();
        if (userId != null) {
            stmt.bindString(5, userId);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, TemplateSortModule entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String templateId = entity.getTemplateId();
        if (templateId != null) {
            stmt.bindString(2, templateId);
        }
 
        String useTime = entity.getUseTime();
        if (useTime != null) {
            stmt.bindString(3, useTime);
        }
        stmt.bindLong(4, entity.getHasUpload());
 
        String userId = entity.getUserId();
        if (userId != null) {
            stmt.bindString(5, userId);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public TemplateSortModule readEntity(Cursor cursor, int offset) {
        TemplateSortModule entity = new TemplateSortModule( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // templateId
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // useTime
            cursor.getInt(offset + 3), // hasUpload
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4) // userId
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, TemplateSortModule entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setTemplateId(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setUseTime(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setHasUpload(cursor.getInt(offset + 3));
        entity.setUserId(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(TemplateSortModule entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(TemplateSortModule entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(TemplateSortModule entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
