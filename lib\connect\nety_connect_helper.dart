import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:nety/models/niimbot_bluetooth_printer.dart';
import 'package:nety/models/niimbot_wifi_printer.dart';
import 'package:nety/nety.dart';
import 'package:niimbot_print_setting_plugin/connect/connect_printer_manager.dart';
import 'package:niimbot_print_setting_plugin/connect/printer_setting_manager.dart';
import 'package:niimbot_print_setting_plugin/connect/search_printer_manager.dart';
import 'package:text/connect/machine_alias_manager.dart';
import 'package:text/model/print_device_info.dart';
import 'package:text/tools/rfid_manager.dart';
import 'package:text/tools/to_Native_Basic_Message_Channel.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';

class NetyConnectHelper {
  ///提供给原生调用的基于nety插件的封装方法
  static Future<dynamic> handleNetySearchConnect(String method, dynamic params) async {
    if (method == NetyConnectMethod.isConnectingState.methodName) {
      int? state = await ConnectPrinterManager().isConnectingState();
      return state ?? 0;
    } else if (method == NetyConnectMethod.discoverPrinter.methodName) {
      await SearchPrinterManager().discoverPrinter();
      return Future<Map>.value({});
    } else if (method == NetyConnectMethod.discoverBluetoothPrinter.methodName) {
      await SearchPrinterManager().discoverBluetoothPrinter();
      return Future<Map>.value({});
    } else if (method == NetyConnectMethod.discoverWIFIPrinter.methodName) {
      await SearchPrinterManager().discoverWIFIPrinter();
      return Future<Map>.value({});
    } else if (method == NetyConnectMethod.stopDiscoverPrinter.methodName) {
      await SearchPrinterManager().stopDiscoverPrinter();
      return Future<Map>.value({});
    } else if (method == NetyConnectMethod.stopDiscoverBluetoothPrinter.methodName) {
      int isSuccess = await SearchPrinterManager().stopDiscoverBluetoothPrinter();
      return Future.value(isSuccess);
    } else if (method == NetyConnectMethod.stopDiscoverWIFIPrinter.methodName) {
      await SearchPrinterManager().stopDiscoverWIFIPrinter();
      return Future<Map>.value({});
    } else if (method == NetyConnectMethod.connectBluetoothPrinter.methodName) {
      String name = params!['name'];
      String mac = params['mac'];
      NiimbotBluetoothPrinter printer = NiimbotBluetoothPrinter(name: name, mac: mac, rssi: 0);
      NiimbotBluetoothPrinter? connectedPrinter = await ConnectPrinterManager().connectBluetoothPrinter(printer);
      if (connectedPrinter != null) {
        final printer = connectedPrinter.copyWith(sn: connectedPrinter.getPrinterSerialNo());
        Map<String, dynamic> map = printer.toJson();
        RfidManager.instance.refreshConnectDevice(printer);
        map["supportAlias"] = RfidManager.instance.checkSupportAlias();
        map["alias"] = MachineAliasManager().aliasMap[printer.name] ?? "";
        String printerJson = jsonEncode(map);
        Future.delayed(Duration(seconds: 2), () {
          RfidManager.instance.buildRiskCheck(RiskRequestCode.Connected.value);
        });
        return Future.value(printerJson);
      } else {
        return Future.value(null);
      }
    } else if (method == NetyConnectMethod.connectWifiPrinter.methodName) {
      String name = params!['name'];
      String ip = params['ip'];
      int port = params['port'];
      int available = params['available'];
      NiimbotWIFIPrinter printer = NiimbotWIFIPrinter(name: name, ip: ip, port: port, available: available);
      NiimbotWIFIPrinter? connectedPrinter = await ConnectPrinterManager().connectWIFIPrinter(printer);
      if (connectedPrinter != null) {
        final printer = connectedPrinter.copyWith(sn: connectedPrinter.getPrinterSerialNo());
        Map<String, dynamic> map = printer.toJson();
        RfidManager.instance.refreshConnectDevice(printer.copyWith());
        map["supportAlias"] = RfidManager.instance.checkSupportAlias();
        map["alias"] = MachineAliasManager().aliasMap[printer.name] ?? "";
        String printerJson = jsonEncode(map);
        RfidManager.instance.buildRiskCheck(RiskRequestCode.Connected.value);
        return Future.value(printerJson);
      } else {
        return Future.value(null);
      }
    } else if (method == NetyConnectMethod.getCurrentPrinterSerialInfo.methodName) {
      PDeviceInfo? pDeviceInfo = RfidManager.connectedDevice;
      if (pDeviceInfo == null) {
        return Future.value(null);
      }
      Map<String, dynamic> map = pDeviceInfo.toJson(isNativeJson: true);
      map["antiCounterfeitKey"] = NiimbotPrintSDK().store.connectedPrinter?.antiCounterfeitKey ?? "";
      map["supportAlias"] = RfidManager.instance.checkSupportAlias();
      map["alias"] = MachineAliasManager().aliasMap[pDeviceInfo.name] ?? "";
      String printerJson = jsonEncode(map);
      return Future.value(printerJson);
    } else if (method == NetyConnectMethod.disconnectPrinter.methodName) {
      await ConnectPrinterManager().disconnectPrinter();
      return Future<Map>.value({});
    } else if (method == NetyConnectMethod.getHardwareSoftware.methodName) {
      Map map = await PrinterSettingManager().getHardwareSoftware();
      return map;
    } else if (method == NetyConnectMethod.getPackedPrinterInfo.methodName) {
      final stopwatch = Stopwatch()..start();
      // 如果支持WIFI，需要获取WIFI的配置参数
      bool isSupportWIFI = RfidManager.connectedDevice?.isWifi == 1;
      Map map = await PrinterSettingManager().getPackedPrinterInfo(isSupportWIFI: isSupportWIFI);
      String packedJson = jsonEncode(map);
      stopwatch.stop();
      debugPrint('someAsyncMethod 耗时: ${stopwatch.elapsedMilliseconds} 毫秒');
      return packedJson;
    } else if (method == NetyConnectMethod.setPositioningCalibration.methodName) {
      int result = await PrinterSettingManager().setPositioningCalibration(params);
      return result;
    } else if (method == NetyConnectMethod.setKeyFunction.methodName) {
      int key = params!["key"];
      int function = params["function"];
      int result = await PrinterSettingManager().setKeyFunction(key, function);
      return result;
    } else if (method == NetyConnectMethod.setPrinterTime.methodName) {
      int year = params["year"];
      int month = params["month"];
      int day = params["day"];
      int hour = params["hour"];
      int minute = params["minute"];
      int second = params["second"];
      DateTime dataTime = DateTime(year, month, day, hour, minute, second);
      int result = await PrinterSettingManager().setPrinterTime(dataTime);
      return result;
    } else if (method == NetyConnectMethod.setPrinterAutoShutdownTime.methodName) {
      int result = await PrinterSettingManager().setPrinterShutdownTime(params);
      return result;
    } else if (method == NetyConnectMethod.setDeviceVoice.methodName) {
      int result = await PrinterSettingManager().setDeviceVoice(params);
      return result;
    } else if (method == NetyConnectMethod.setBluetoothVoice.methodName) {
      int result = await PrinterSettingManager().setBluetoothVoice(params);
      return result;
    } else if (method == NetyConnectMethod.configWifi.methodName) {
      String name = params!["name"];
      String password = params!["password"];
      bool result = await PrinterSettingManager().configWifi(name, password);
      return result;
    } else if (method == NetyConnectMethod.getDeviceWifiConfig.methodName) {
      String? result = await PrinterSettingManager().getDeviceWiFiConfig();
      return result;
    } else if (method == NetyConnectMethod.setTubeCalibration.methodName) {
      int result = await PrinterSettingManager().setTubeCalibration();
      return result;
    } else if (method == NetyConnectMethod.setTubeAdjustLength.methodName) {
      int result = await PrinterSettingManager().setTubeAdjustLength(params);
      return result;
    }
  }

  /// nety回调事件监听
  static setNetyCallbackListener() {
    // SearchPrinterManager().bluetoothPrinterFoundCallback = (bluetoothPrinters) {
    //   if (bluetoothPrinters?.isNotEmpty ?? false) {
    //     for (NiimbotBluetoothPrinter? bluetoothPrinter in bluetoothPrinters!) {
    //       if (bluetoothPrinter != null) {
    //         handleFoundBluetoothPrinter(bluetoothPrinter);
    //       }
    //     }
    //   }
    // };
    SearchPrinterManager().bluetoothPrinterFoundCallback = (bluetoothPrinter) {
      handleFoundBluetoothPrinter(bluetoothPrinter);
    };
    SearchPrinterManager().wifiPrinterFoundCallback = (wifiPrinter) {
      handleFoundWifiPrinter(wifiPrinter);
    };
    SearchPrinterManager().bluetoothDiscoverFinishedCallback = () {
      handleDiscoverBluetoothPrinterFinished();
    };
    SearchPrinterManager().wifiDiscoverFinishedCallback = () {
      handleDiscoverWifiPrinterFinished();
    };
    ConnectPrinterManager.disconnectCallback = () {
      handleDisconnectCallback();
    };
    ConnectPrinterManager.electricityCallback = (value) {
      handleElectricityCallback(value);
    };
    ConnectPrinterManager.coverStatusCallback = (oldValue, newValue) {
      handleCoverStatusCallback(oldValue, newValue);
    };
    ConnectPrinterManager.paperStatusCallback = (value) {
      handlePaperStatusCallback(value);
    };
    ConnectPrinterManager.paperRfidStatusCallback = (oldValue, newValue) {
      handlePaperRfidStatusCallback(oldValue, newValue);
    };
    ConnectPrinterManager.ribbonStatusCallback = (value) {
      handleRibbonStatusCallback(value);
    };
    ConnectPrinterManager.ribbonRfidStatusCallback = (oldValue, newValue) {
      handleRibbonRfidStatusCallback(oldValue, newValue);
    };
    ConnectPrinterManager.wifiRssiCallback = (value) {
      handleWifiRssiCallback(value);
    };
  }

  /// 搜索到蓝牙打印机回调
  static handleFoundBluetoothPrinter(NiimbotBluetoothPrinter bluetoothPrinter) {
    Map<String, dynamic> printerInfo = {
      "name": bluetoothPrinter.name,
      "mac": bluetoothPrinter.mac,
      "rssi": bluetoothPrinter.rssi,
      "alias": MachineAliasManager().aliasMap[bluetoothPrinter.name] ?? ""
    };
    Map map = {"actionName": NetyConnectCallbackMethod.foundBluetoothPrinter.methodName, "printer": printerInfo};
    ToNativeBasicMessageChannel.sharedInstance().sendMessageToNative(jsonEncode(map));
  }

  /// 搜索到wifi打印机回调
  static handleFoundWifiPrinter(NiimbotWIFIPrinter wifiPrinter) {
    Map<String, dynamic> printerInfo = {
      "name": wifiPrinter.name,
      "ip": wifiPrinter.ip,
      "port": wifiPrinter.port,
      "available": wifiPrinter.available,
      "alias": MachineAliasManager().aliasMap[wifiPrinter.name] ?? ""
    };
    Map map = {"actionName": NetyConnectCallbackMethod.foundWifiPrinter.methodName, "printer": printerInfo};
    ToNativeBasicMessageChannel.sharedInstance().sendMessageToNative(jsonEncode(map));
  }

  /// 蓝牙搜索过程结束回调
  static handleDiscoverBluetoothPrinterFinished() {
    try {
      ToNativeMethodChannel.platform
          .invokeMethod(NetyConnectCallbackMethod.discoverBluetoothPrinterFinished.methodName);
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  /// wifi搜索过程结束回调
  static handleDiscoverWifiPrinterFinished() {
    try {
      ToNativeMethodChannel.platform.invokeMethod(NetyConnectCallbackMethod.discoverWifiPrinterFinished.methodName);
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  /// 打印机断开连接回调
  static handleDisconnectCallback() {
    try {
      ToNativeMethodChannel.platform.invokeMethod(NetyConnectCallbackMethod.disconnectCallback.methodName);
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  /// 打印机电量回调
  static handleElectricityCallback(int value) {
    try {
      ToNativeMethodChannel.platform.invokeMethod(NetyConnectCallbackMethod.electricityCallback.methodName, value);
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  /// 开合盖状态回调
  static handleCoverStatusCallback(int? oldValue, int newValue) {
    try {
      ToNativeMethodChannel.platform.invokeMethod(NetyConnectCallbackMethod.coverStatusCallback.methodName, newValue);
      if (oldValue != newValue && newValue == 1) {
        Future.delayed(Duration(milliseconds: 300), () {
          ///处理无芯片开盒盖RFID策略不获取问题
          // if (RfidManager.sdkRFIDLabelInfos.isEmpty) {
          //   RfidManager.instance.refreshSdkRfidInfos(newValue, isRibbonStates: false);
          // }
          ///B32等碳带机器，只有开合盖回调没有RibbonStatusCallback，
          ///开盖时原生端清理了rfid信息，盒盖时没有回调导致原生端判断错误
          RfidManager.instance.refreshSdkRfidInfos(newValue, isRibbonStates: false);
        });
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  /// 是否安装标签纸回调
  static handlePaperStatusCallback(int value) {
    try {
      ToNativeMethodChannel.platform.invokeMethod(NetyConnectCallbackMethod.paperStatusCallback.methodName, value);
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  /// 是否读取到标签纸rfid信息回调
  static handlePaperRfidStatusCallback(int? oldValue, int newValue) {
    try {
      ToNativeMethodChannel.platform
          .invokeMethod(NetyConnectCallbackMethod.paperRfidStatusCallback.methodName, newValue);
      if (oldValue != null && newValue == 1) {
        RfidManager.instance.buildRiskCheck(RiskRequestCode.COVER.value);
      }
      RfidManager.instance.refreshSdkRfidInfos(newValue, isRibbonStates: false);
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  /// 是否安装碳带回调
  static handleRibbonStatusCallback(int value) {
    try {
      ToNativeMethodChannel.platform.invokeMethod(NetyConnectCallbackMethod.ribbonStatusCallback.methodName, value);
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  /// 是否读取到碳带rfid信息回调
  static handleRibbonRfidStatusCallback(int? oldValue, int newValue) {
    try {
      ToNativeMethodChannel.platform
          .invokeMethod(NetyConnectCallbackMethod.ribbonRfidStatusCallback.methodName, newValue);
      if (oldValue != null && newValue == 1) {
        RfidManager.instance.buildRiskCheck(RiskRequestCode.COVER.value);
      }
      RfidManager.instance.refreshSdkRfidInfos(newValue, isRibbonStates: true);
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  /// wifi信号强度回调
  static handleWifiRssiCallback(int value) {
    Map map = {"actionName": NetyConnectCallbackMethod.wifiRssiCallback.methodName, "rssi": value};
    ToNativeBasicMessageChannel.sharedInstance().sendMessageToNative(jsonEncode(map));
  }
}

enum NetyConnectMethod {
  // 获取连接状态
  isConnectingState(methodName: "isConnectingState"),
  //同时搜索蓝牙打印机和wifi打印机
  discoverPrinter(methodName: "discoverPrinter"),
  //搜索蓝牙打印机
  discoverBluetoothPrinter(methodName: "discoverBluetoothPrinter"),
  //搜索wifi打印机
  discoverWIFIPrinter(methodName: "discoverWIFIPrinter"),
  //停止蓝牙搜索和wifi搜索
  stopDiscoverPrinter(methodName: "stopDiscoverPrinter"),
  //停止蓝牙搜索
  stopDiscoverBluetoothPrinter(methodName: "stopDiscoverBluetoothPrinter"),
  //停止wifi搜索
  stopDiscoverWIFIPrinter(methodName: "stopDiscoverWIFIPrinter"),
  //连接蓝牙打印机
  connectBluetoothPrinter(methodName: "connectBluetoothPrinter"),
  //连接wifi打印机
  connectWifiPrinter(methodName: "connectWifiPrinter"),
  //获取连接打印机的系列信息
  getCurrentPrinterSerialInfo(methodName: "getCurrentPrinterSerialInfo"),
  //断开打印机连接
  disconnectPrinter(methodName: "disconnectPrinter"),
  //取消关闭搜索连接
  cancelCloseSearchConnect(methodName: "cancelCloseSearchConnect"),
  //获取硬件版本和固件版本
  getHardwareSoftware(methodName: "getHardwareSoftware"),
  //获取硬件版本和固件版本
  getElectricity(methodName: "getElectricity"),
  //获取打印机设置页面打包的信息
  getPackedPrinterInfo(methodName: "getPackedPrinterInfo"),
  //设置走纸校准
  setPositioningCalibration(methodName: "setPositioningCalibration"),
  //开机按键配置
  setKeyFunction(methodName: "setKeyFunction"),
  // 打印机时间设置
  setPrinterTime(methodName: "setPrinterTime"),
  //设置自动关机时间
  setPrinterAutoShutdownTime(methodName: "setPrinterAutoShutdownTime"),
  //开启或关闭打印机开关机声音
  setDeviceVoice(methodName: "setDeviceVoice"),
  //开启或关闭打印机蓝牙连接断开声音
  setBluetoothVoice(methodName: "setBluetoothVoice"),
  //配置wifi
  configWifi(methodName: "configWifi"),
  //获取设备配置wifi信息
  getDeviceWifiConfig(methodName: "getDeviceWifiConfig"),
  //开始走管
  setTubeCalibration(methodName: "setTubeCalibration"),
  //设置走管校准长度
  setTubeAdjustLength(methodName: "setTubeAdjustLength");

  final String methodName;

  const NetyConnectMethod({required this.methodName});

  static bool matchAny(String methodName) {
    return NetyConnectMethod.values.any((it) => it.methodName == methodName);
  }
}

enum NetyConnectCallbackMethod {
  //搜索到蓝牙打印机回调
  foundBluetoothPrinter(methodName: "foundBluetoothPrinter"),
  //搜索到wifi打印机回调
  foundWifiPrinter(methodName: "foundWifiPrinter"),
  //蓝牙搜索过程结束回调
  discoverBluetoothPrinterFinished(methodName: 'discoverBluetoothPrinterFinished'),
  //wifi搜索过程结束回调
  discoverWifiPrinterFinished(methodName: "discoverWifiPrinterFinished"),
  //断开连接回调
  disconnectCallback(methodName: "disconnectCallback"),
  //电量回调
  electricityCallback(methodName: "electricityCallback"),
  //开合盖回调
  coverStatusCallback(methodName: "coverStatusCallback"),
  //标签纸是否安装状态回调
  paperStatusCallback(methodName: "paperStatusCallback"),
  //是否读取到标签纸rfid信息回调
  paperRfidStatusCallback(methodName: "paperRfidStatusCallback"),
  //碳带是否安装状态回调
  ribbonStatusCallback(methodName: "ribbonStatusCallback"),
  //是否读取到碳带rfid信息回调
  ribbonRfidStatusCallback(methodName: "ribbonRfidStatusCallback"),
  //wifi强度回调
  wifiRssiCallback(methodName: "wifiRssiCallback");

  final String methodName;

  const NetyConnectCallbackMethod({required this.methodName});
}
