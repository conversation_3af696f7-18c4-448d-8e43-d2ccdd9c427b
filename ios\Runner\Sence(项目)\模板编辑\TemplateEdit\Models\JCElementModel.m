//
//  JCElementModel.m
//  Runner
//
//  Created by xingling xu on 2020/3/11.
//  Copyright © 2020 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCElementModel.h"
#import "JCExcelTransUtil.h"
#import "JCElementModel+Transfer.h"
static NSArray* _elementTypes = nil;
@implementation JCElementModel
//
- (instancetype)init {
    self = [super init];
    if (self) {
        self.address = @"";
        self.elementId = @"";
        self.type = @"";
        self.templateType = 0;
        self.x = 0;
        self.y = 0;
        self.width = 0;
        self.height = 0;
        self.screenX = 0;
        self.screenY = 0;
        self.screenWidth = 0;
        self.screenHeight = 0;
        self.isLock = 0;
        self.rotate = 0;
        self.zIndex = 0;
        self.fieldName = @"";
        self.textAlignHorizonral = 0;
        self.textAlignVertical = 1;
        self.lineMode = 2;
        self.value = @"";
        self.wordSpacing = 0;
        self.letterSpacing = 0;
        self.lineSpacing = 1;
        self.fontFamily = @"";
        self.fontStyle = @[];
        self.fontSize = 3;

        self.dateFormat = @"";
        self.timeFormat = @"";
        self.dateIsRefresh = 0;
        self.associated = NO;
        self.timeOffset = 0;
        self.associateId = @"";
        self.validityPeriod = 1;
        self.validityPeriodNew = 1;
        self.validityPeriodUnit = @"Associated_month";
        self.isLive = NO;
        self.liveCodeId = @"";
        self.isForm = NO;
        self.formId = @"";
        self.prefix = @"";
        self.startNumber = @"01";
        self.suffix = @"";
        self.fontCode = @"";
        self.fixLength = 2;
        self.incrementValue = 0;
        self.dateOpen = YES;
        self.timeOpen = NO;
        self.graphType = 4;
        self.textPosition = 0;
        self.textHeight = 0;
        self.codeType = 20;

        self.correctLevel = 0;
        self.lineWidth = 1;

        self.lineType = 1;
        self.dashwidth = @[];
        self.cornerRadius = 2;

        self.imageData = @"";
        self.imageUrl = @"";
        self.materialType = @"1";
        self.timeUnit = @"";
        self.imageProcessingType = 1;
        self.imageProcessingValue = @[];
        self.typesettingParam = @[@0,@180];
        self.typesettingMode = 1;
        self.row = 0;
        self.column = 0;
        self.rowHeight = @[];
        self.columnWidth = @[];
        self.cells = @[].copy;
        self.combineId = @"";
        self.rowIndex = 0;
        self.columnIndex = 0;
        self.combineCells = @[].copy;
        self.isTitle = NO;
        self.isOpenMirror = NO;
        self.isHidden = NO;
        self.lineBreakMode = 0;
        self.elementColor = @[@255,@0,@0,@0];
        self.colorReverse = 0;
        self.paperColorIndex = 0;
        self.colorChannel = 0;
        self.lineColorChannel  = 0;
        self.contentColorChannel = 0;
        self.lineColor = @[@255,@0,@0,@0];
        self.contentColor = @[@255,@0,@0,@0];
        self.dataBind = @[];
    }
    return self;
}

- (void)setImageData:(NSString *)imageData{
  _imageData = imageData;
  if(!STR_IS_NIL(imageData)){
    NSLog(@"图片base64");
  }
}


- (void)setElementColor:(NSArray *)elementColor{
    if(_elementColor != nil){
//        NSLog(@"更改颜色");
    }
    _elementColor = elementColor;

}

- (NSInteger)validityPeriodNew{
  if([self.validityPeriodUnit isEqualToString:@"Associated_Day"]){
    _validityPeriodNew = self.validityPeriod - 1;
  }else{
    _validityPeriodNew = self.validityPeriod;
  }
  return _validityPeriodNew;
}

- (void)setPaperColorIndex:(NSInteger)paperColorIndex{
    _paperColorIndex = paperColorIndex;
}

+ (void)load {
    _elementTypes = @[@"text",@"barcode",@"qrcode",@"graph",@"table",@"line",@"image",@"date",@"serial"];
}

+(JSONKeyMapper *)keyMapper
{
    return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:@{@"elementId":@"id"}];
}

- (JCTemplateType)templateType {
    return [_elementTypes indexOfObject:self.type];
}

- (void)setTypesettingMode:(NSInteger)typesettingMode{
    _typesettingMode = typesettingMode;
}

- (void)setTypesettingParam:(NSArray *)typesettingParam{
    if([typesettingParam isKindOfClass:[NSArray class]]){
        _typesettingParam = typesettingParam;
    }else{
        _typesettingParam = @[@0,@180];
    }
}

-(void)setX:(CGFloat)x{
    if(isnan(x) || isinf(x)){
        x = 0;
    }
    _x = x;
}

-(void)setY:(CGFloat)y{
    if(isnan(y) || isinf(y)){
        y = 0;
    }
    _y = y;
}

-(void)setWidth:(CGFloat)width{
    if(isnan(width) || isinf(width)){
        width = 1;
    }
    _width = width;
}

-(void)setHeight:(CGFloat)height{
    if(isnan(height) || isinf(height)){
        height = 1;
    }
    _height = height;
}

//-(void)setScreenX:(CGFloat)screenX{
//    _screenX = screenX;
//    if (DrawBoardInfo.mm2pxScale > 0) {
//        _x = [self getRect].origin.x;
//    }
//}
//
//-(void)setScreenY:(CGFloat)screenY{
//    _screenY = screenY;
//    if (DrawBoardInfo.mm2pxScale > 0) {
//        _y = [self getRect].origin.y;
//    }
//}
//
//-(void)setScreenWidth:(CGFloat)screenWidth{
//    _screenWidth = screenWidth;
//    if (DrawBoardInfo.mm2pxScale > 0) {
//        _width = [self getRect].size.width;
//    }
//}
//
//-(void)setScreenHeight:(CGFloat)screenHeight{
//    _screenHeight = screenHeight;
//    if (DrawBoardInfo.mm2pxScale > 0) {
//        _height = [self getRect].size.height;
//    }
//    if (_height == 0) {
//        _height = 10;
//    }
//}


//实际转屏幕
- (CGRect)getScreenRect
{
    CGFloat scale = DrawBoardInfo.mm2pxScale; //1毫米多少屏幕像素
    CGFloat mm_x1 = self.x;
    CGFloat mm_y1 = self.y;
    CGFloat mm_w1 = self.width;   //打印实际毫米w
    CGFloat mm_h1 = self.height;  //打印实际毫米h
    CGFloat x1 = mm_x1 * scale;
    CGFloat y1 = mm_y1 * scale;
    CGFloat w1 = mm_w1 * scale;
    CGFloat h1 = mm_h1 * scale;
    return CGRectMake(x1, y1, w1, h1);
}

- (float)getScreenNumberWithRealNumber:(float)realNumber{
    CGFloat scale = DrawBoardInfo.mm2pxScale; //1毫米多少屏幕像素
    CGFloat mm_Number1 = realNumber / 8;
    CGFloat screenNumber = mm_Number1 * scale;
    return screenNumber;
}

- (float)getRealNumberWithScreenNumber:(float)screenNumber{
    CGFloat scale = DrawBoardInfo.mm2pxScale; //1毫米多少屏幕像素
    CGFloat mm_screenNumber = screenNumber / scale; //屏幕mm
    CGFloat realNumber = mm_screenNumber * 8;
    return realNumber;
}


//屏幕转实际
- (CGRect)getRect
{
    //打印像素转实际mm
    CGFloat scale = DrawBoardInfo.mm2pxScale; //1毫米多少屏幕像素
    CGFloat mm_x1 = self.screenX / scale; //屏幕mm
    CGFloat mm_y1 = self.screenY / scale; //屏幕mm
    CGFloat mm_w1 = self.screenWidth / scale; //屏幕mm
    CGFloat mm_h1 = self.screenHeight / scale; //屏幕mm
    CGFloat x1 = mm_x1;
    CGFloat y1 = mm_y1;
    CGFloat w1 = mm_w1;
    CGFloat h1 = mm_h1;
    return CGRectMake(x1, y1, w1, h1);
}

//实际转屏幕
- (NSString *)getScreenTextSize
{
    CGFloat scale = DrawBoardInfo.mm2pxScale; //1毫米多少屏幕像素
    CGFloat mm_x1 = self.fontSize * 0.125;
    CGFloat x1 = mm_x1 * scale;
    return [NSString stringWithFormat:@"%f", x1];
}


- (NSMutableArray *)arrWith:(NSString *)arrStr {
    NSString *numberStr = [arrStr substringWithRange:(NSRange){1,arrStr.length-2}];
    NSArray *numberArr = [numberStr componentsSeparatedByString:@","];
    NSMutableArray *result = [NSMutableArray arrayWithCapacity:numberArr.count];
    [numberArr enumerateObjectsUsingBlock:^(NSString *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        [result addObject:@(obj.integerValue)];
    }];
    return result;
}

//- (void)setValue:(NSString *)value{
//    [self fixValue:value];
//}

-(BOOL)isBindEelment{
    BOOL isDataBind = false;
    if(self.dataBind != nil && [self.dataBind isKindOfClass:[NSArray class]] && self.dataBind.count > 0){
        isDataBind = true;
    }
    return isDataBind;
}

-(BOOL)isCustomField{
    BOOL isCommdityCustomField = false;
    NSArray <NSString *> * supportType = @[@"text",@"barcode",@"qrcode",@"table"];
    if([supportType containsObject:self.type]){
        if([self.type isEqualToString:@"table"]){
            for (JCElementModel *cellModel in self.cells) {
                if([cellModel isBindEelment] && [self isNewCommdityCustomFieldWith:cellModel]){
                    isCommdityCustomField = true;
                    break;
                };
            }
            for (JCElementModel *cellModel in self.combineCells) {
                if([cellModel isBindEelment] && [self isNewCommdityCustomFieldWith:cellModel]){
                    isCommdityCustomField = true;
                    break;
                };
            }
        }else{
            if([self isBindEelment] && [self isNewCommdityCustomFieldWith:self]){
                isCommdityCustomField = true;
            };
        }
    }
    return  isCommdityCustomField;
}

-(BOOL)isTextElement{
    return  [self.type isEqualToString:@"text"];
}

-(BOOL)isNewCommdityCustomFieldWith:(JCElementModel *)element{
    NSNumber * column = [JCExcelTransUtil getCommodityColumnWith:element.value];
    if(column != nil && column.integerValue >= 9){
        return true;
    }
    return false;
}

-(void)setDataBind:(NSArray<NSString *> *)dataBind
{
    _dataBind = dataBind;
//    [self fixValue:_value];
}


- (void)fixValue:(NSString *)newValue {
    // 是否绑定数据源
    if([_dataBind isKindOfClass:[NSArray class]] && _dataBind.count > 0){
        // 是否以${开头
        if ([newValue hasPrefix:@"${"] != true) {
           if ([newValue containsString:@"⊙"]) {
                // 拆分3个部分，保证格式是0⊙0类型
                NSArray<NSString *> *listItems = [newValue componentsSeparatedByString:@"⊙"];
                // 2个元素避免遍历，直接判断类型
                if (listItems.count == 2 && [listItems.firstObject isPureNumandCharacters] && [listItems.lastObject isPureNumandCharacters]) {
                    _value = ServerTextValue(newValue);
                    return;
                }
           } else {
               _value = [JCExcelTransUtil transToOldJsonValue:newValue];
               return;
           }
        }
    }
    //兼容老版本数据库里面存储的0⊙1格式的新版本新画板显示异常
    _value = ServerTextValue(newValue);
}

- (void)fixValueExcel{
  [self fixValue:self.value];
}

- (BOOL)isPureNumber:(NSString *)string {
    NSCharacterSet *nonDigits = [[NSCharacterSet decimalDigitCharacterSet] invertedSet];
    return ([string rangeOfCharacterFromSet:nonDigits].location == NSNotFound);
}

- (NSString *)getDateString {
    /** 日期传给sdk 单独重组字符串，覆盖掉原来的value */
    NSString *dateFormat = self.dateFormat;
    NSString *timeFormate = self.timeFormat;
    // date
    NSTimeInterval interval = self.time;
    if(self.value.length == 13 && [self isPureNumber:self.value]){
      interval = [self.value doubleValue];
    }
    NSDate *date  = nil;
    if([self timeElementRefreshEnable]){
        date = [NSDate now];
        interval = [[XYTool timeStampFrom:date] doubleValue];
    }
    if(self.timeOffset != 0){
        interval = interval + self.timeOffset * 60 * 1000;
    }
    if(!STR_IS_NIL(self.associateId)){
        if(!self.associated && self.validityPeriodNew != 0){
            date = [XYTool dateFromMsTimeStamp:interval];
//            interval = interval + self.validityPeriod * 60 * 1000;
            NSCalendar *calendar = [NSCalendar currentCalendar];
            // 获取有效期的日期组件
            NSDateComponents *afterComponents = [[NSDateComponents alloc] init];
            if([self.validityPeriodUnit isEqualToString:@"Associated_Day"]){
                afterComponents.day = self.validityPeriodNew;
            }else if([self.validityPeriodUnit isEqualToString:@"Associated_Hour"]){
                afterComponents.hour = self.validityPeriodNew;
                // afterComponents.minute = -1;
            }else if([self.validityPeriodUnit isEqualToString:@"Associated_Year"]){
                afterComponents.year = self.validityPeriodNew;
                // afterComponents.day = -1;
            }else if([self.validityPeriodUnit isEqualToString:@"Associated_month"]){
                afterComponents.month = self.validityPeriodNew;
                // afterComponents.day = -1;
            }
            // 计算有效期后的日期
            date = [calendar dateByAddingComponents:afterComponents toDate:date options:0];
            interval = [XYTool timeStampFrom:date].doubleValue;
        }
    }
    date = [XYTool dateFromMsTimeStamp:interval];
    NSString *dateValue = @"",*timeValue = @"";
    // 日期
    if (!STR_IS_NIL(dateFormat)) {
        dateValue = [date stringWithDateFormat:dateFormat];
    }
    // 时间
    if (!STR_IS_NIL(timeFormate)) {
        timeValue = [date stringWithDateFormat:timeFormate];
    }
    NSString *componetString = (!STR_IS_NIL(dateValue) && !STR_IS_NIL(timeValue))?@" ":@"";
    NSString *value = [NSString stringWithFormat:@"%@%@%@",dateValue,componetString,timeValue];
    if(!STR_IS_NIL(self.timeUnit) && !STR_IS_NIL(timeFormate)){
        NSString *timeUnitDesc = @"";
        timeFormate = [timeFormate stringByReplacingOccurrencesOfString:@"H" withString:@"h"];
        NSString *tempTimeFormate = [NSString stringWithFormat:@"%@ a",timeFormate];
        if([[date stringWithDateFormat:tempTimeFormate] containsString:@"PM"]){
            self.timeUnit = @"afternoon";
        }else if([[date stringWithDateFormat:tempTimeFormate] containsString:@"AM"]){
            self.timeUnit = @"morning";
        }
        timeValue = [date stringWithDateFormat:timeFormate];
        if([self.timeUnit isEqualToString:@"afternoon"]){
            timeUnitDesc = XY_LANGUAGE_TITLE_NAMED(@"app100001460", @"下午$");
        }else{
            timeUnitDesc = XY_LANGUAGE_TITLE_NAMED(@"app100001461", @"上午$");
        }
        timeValue = [timeUnitDesc stringByReplacingOccurrencesOfString:@"$" withString:timeValue];
        value = [NSString stringWithFormat:@"%@%@%@",dateValue,componetString,timeValue];
    }
    NSLog(@"当前时间：%@ 当前镜像ID：%@",value,self.mirrorId);
    return value;
}

- (BOOL)timeElementRefreshEnable{
//    BOOL isTimeRefreshEnable = YES;
//    if(!m_user_vip){
//        NSDate *endTime = [NSDate dateWithString:UNVIPTimeElementSupportValite format:@"yyyy-MM-dd'T'HH:mm:ss.SSSXXX"];
//        if(endTime){
//            NSString *currentTime = (STR_IS_NIL(m_currentServerTime) || (NETWORK_STATE_ERROR))?[XYTool getCurrentTimes]:m_currentServerTime;
//            NSDate *nowTime = [NSDate dateWithString:currentTime format:@"yyyy-MM-dd'T'HH:mm:ss.SSSXXX"];
//            if([nowTime compare:endTime] == NSOrderedAscending){
//                isTimeRefreshEnable = YES;
//            }else{
//                isTimeRefreshEnable = NO;
//            }
//        }
//    }
//    return self.dateIsRefresh && isTimeRefreshEnable;
    return self.dateIsRefresh;
}

- (BOOL)canShowKeyboard {
    if (self.templateType == JCTemplateType_Text || self.templateType == JCTemplateType_BarCode || self.templateType == JCTemplateType_QRCode || self.templateType == JCTemplateType_Table) {
        return YES;
    }
    return NO;
}

+(BOOL)propertyIsOptional:(NSString*)propertyName
{
  return YES;
}

- (void)updateLocalProperty {
    self.screenX = [self getScreenRect].origin.x;
    self.screenY = [self getScreenRect].origin.y;
    self.screenWidth = [self getScreenRect].size.width;
    self.screenHeight = [self getScreenRect].size.height;
}

YYModel_Encode_And_Copy_Method_Implementation
- (id)copyWithZone:(NSZone *)zone {
    typeof(self) newOne = [self yy_modelCopy]; // 浅拷贝
    return newOne;
}

- (void)setFieldName:(NSString *)fieldName{
    _fieldName = fieldName;
}

@end
