class DebounceUtil {
  static DateTime? lastClickTime;

  static bool checkClick({int needTime = 2}) {
    if (null == lastClickTime || DateTime.now().difference(lastClickTime!) > Duration(seconds: needTime)) {
      lastClickTime = DateTime.now();
      return true;
    } else {
      return false;
    }
  }

  static bool checkTime({int needTime = 1000}) {
    if (null == lastClickTime || DateTime.now().difference(lastClickTime!) > Duration(milliseconds: needTime)) {
      lastClickTime = DateTime.now();
      return true;
    } else {
      return false;
    }
  }

  static void debounce(Function() action, {int needTime = 1}) {
    if (null == lastClickTime || DateTime.now().difference(lastClickTime!) > Duration(seconds: needTime)) {
      lastClickTime = DateTime.now();
      try {
        action.call();
      } catch (e) {}
    }
  }
  static void debounceMillisecond(Function() action, {int milliseconds = 1000}) {
    if (null == lastClickTime || DateTime.now().difference(lastClickTime!) > Duration(milliseconds: milliseconds)) {
      lastClickTime = DateTime.now();
      try {
        action.call();
      } catch (e) {}
    }
  }
}
