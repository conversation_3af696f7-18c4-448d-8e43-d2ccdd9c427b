//
//  JCCollectionViewCell.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2019/1/24.
//  Copyright © 2019 JingchenSoft. All rights reserved.
//

#import "JCCollectionViewCell.h"
@interface JCCollectionViewCell()
@property (weak, nonatomic) IBOutlet UIImageView *templateImageV;
@property (weak, nonatomic) IBOutlet UILabel *titleLabel;
@property (weak, nonatomic) IBOutlet UILabel *sizeLabel;
@property (weak, nonatomic) IBOutlet UIImageView *stateImage;
@property (weak, nonatomic) IBOutlet UIView *excelIndexView;
@property (weak, nonatomic) IBOutlet UILabel *excelIndexLabel;
@property (weak, nonatomic) IBOutlet UIView *bgV;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *stateImageW;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *titleLeading;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *excelHeight;
@property (weak, nonatomic) IBOutlet UIImageView *vipStateImage;
@property (weak, nonatomic) IBOutlet UIImageView *goodsStateImageView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *goodsStateImageLeftSpace;
@property (weak, nonatomic) IBOutlet UILabel *templateStateLabel;
@property (weak, nonatomic) IBOutlet UIView *templateStateView;
@property (weak, nonatomic) IBOutlet UIView *wholeBgView;
@property (weak, nonatomic) IBOutlet UIImageView *templateTypeView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *templateTypeViewWidth;
@property (weak, nonatomic) IBOutlet UIButton *printButton;


@end

@implementation JCCollectionViewCell

- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self)
    {
        self.isShowImport = YES;
        self.contentView.backgroundColor = [UIColor colorWithRed:(arc4random()%251)/255.0 green:(arc4random()%251)/255.0 blue:(arc4random()%251)/255.0 alpha:1];
    }
    
    return self;
}

- (void)awakeFromNib {
    [super awakeFromNib];
    self.bgV.layer.borderWidth = 0.5;
    self.bgV.layer.borderColor = HEX_RGBA(0x3C3C43, 0.09).CGColor;
    self.bgV.layer.cornerRadius = 12;
    self.bgV.layer.masksToBounds = YES;
    self.vipStateImage.hidden = YES;
    self.isShowImport = YES;
    UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:CGRectMake(0, 0, 60,20) byRoundingCorners:UIRectCornerTopRight | UIRectCornerBottomLeft cornerRadii:CGSizeMake(12, 12)];
    CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
    maskLayer.frame = CGRectMake(0, 0, 60,20);
    maskLayer.path = maskPath.CGPath;
    self.templateStateView.layer.mask = maskLayer;
    self.templateImageV.backgroundColor = UIColor.clearColor;
    self.vipStateImage.layer.cornerRadius = 4;
    self.vipStateImage.layer.masksToBounds = YES;
    self.wholeBgView.backgroundColor = HEX_RGB(0xF7F7FA);
    self.sizeLabel.textColor = HEX_RGBA(0x3C3C43, 0.6);
}
- (IBAction)printEvent:(id)sender {
  if(self.printBlock) {
    self.printBlock();
  }
}

- (void)setPrintButtonShow:(BOOL)isShowPrint{
  self.printButton.hidden = !isShowPrint;
}

- (void)setTemplateData:(JCTemplateData *)tmData {
    if (!tmData) return;
    BOOL isLocal = (tmData.localType == JCLocalType_OffLineCreate || tmData.localType == JCLocalType_OffLineUpdate);
    self.titleLabel.textAlignment = NSTextAlignmentLeft;
    self.excelIndexLabel.textAlignment = NSTextAlignmentLeft;
    NSString *imageUrlString = tmData.localThumbnail;
    if(!STR_IS_NIL(imageUrlString)){
      imageUrlString = [self replaceSandBoxRealPathWith:imageUrlString];
    }
    UIImage *placeholderImage = [[UIImage alloc] initWithContentsOfFile:imageUrlString];
    if (!placeholderImage) {
      placeholderImage = XY_IMAGE_NAMED(@"占位符");
      [self.templateImageV sd_setImageWithURL:[NSURL URLWithString:tmData.thumbnail]
                           placeholderImage:placeholderImage];
    }else{
      self.templateImageV.image = placeholderImage;
    }
    self.titleLabel.text = tmData.name;
    if(tmData.isEdited.integerValue == 2){
        self.templateStateView.hidden = NO;
        self.templateStateLabel.text = @"已编辑";
        self.templateStateView.backgroundColor = HEX_RGB(0x54FF9F);
    }else if(tmData.isEdited.integerValue == 1){
        self.templateStateView.hidden = NO;
        self.templateStateLabel.text = @"待编辑";
        self.templateStateView.backgroundColor = COLOR_NEW_THEME;
    }else if(tmData.isEdited.integerValue == 0){
        self.templateStateView.hidden = YES;
    }
    self.vipStateImage.hidden = !tmData.hasVipRes && !tmData.vip;
    self.goodsStateImageView.hidden = true;
    self.goodsStateImageLeftSpace.constant = (tmData.hasVipRes || tmData.vip)?25:0;
    BOOL isGoodsTemplate = tmData.profile.extrain.templateType.integerValue == 2;
    self.stateImage.hidden = NO;
    if(isLocal){
        self.stateImage.image = [UIImage imageNamed:@"local_corner"];
        self.stateImageW.constant = 14;
    }else if(tmData.localType == JCLocalType_Default){
        self.stateImage.image = [UIImage new];
        self.stateImageW.constant = 0;
    } else{
        self.stateImage.hidden = YES;
        self.stateImageW.constant = 0;
    }
    self.excelIndexView.hidden = YES;
    self.excelHeight.constant = 0.0;
  if((([tmData hasExcelElement] || tmData.totalPage > 1) && !isGoodsTemplate) ||
     isGoodsTemplate || tmData.dataSource.count > 0 || tmData.dataSources.count > 0){
        [self.templateTypeView setHidden:false];
        self.templateTypeViewWidth.constant = 14;
        [self.templateTypeView setImage:isGoodsTemplate ?  [UIImage imageNamed:@"goods_corner"] : [UIImage imageNamed:@"excel_corner"]];
    }else{
        [self.templateTypeView setHidden:true];
        self.templateTypeViewWidth.constant = 0;
    }
    
    
    if (self.stateImageW.constant == 0 && self.templateTypeViewWidth.constant == 0) {
        self.titleLeading.constant = 0;
    }else{
         self.titleLeading.constant = 2;
    }
    
    NSString *width = [XYTool getNumberFormatValue:tmData.width];
    NSString *height = [XYTool getNumberFormatValue:tmData.height];
    self.sizeLabel.text = [NSString stringWithFormat:@"%@x%@mm",width,height];
}

- (NSString *)replaceSandBoxRealPathWith:(NSString *)oldPath{
  // 获取当前沙盒 Documents 目录
  NSString *currentDocumentsPath = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES).firstObject;

  // 获取当前沙盒的 UUID 根路径（截取到 Documents）
  NSRange docRange = [currentDocumentsPath rangeOfString:@"/Documents"];
  NSString *currentUUIDPath = [currentDocumentsPath substringToIndex:docRange.location];

  // 获取旧路径中的 Documents 相对路径
  NSRange oldDocRange = [oldPath rangeOfString:@"/Documents"];
  NSString *relativePath = [oldPath substringFromIndex:oldDocRange.location];

  // 拼接新路径
  NSString *newPath = [currentUUIDPath stringByAppendingString:relativePath];

  NSLog(@"替换后的路径: %@", newPath);
  return newPath;
}
@end
