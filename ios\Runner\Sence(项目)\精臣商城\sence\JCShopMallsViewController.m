//
//  JCShopMallsViewController.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2019/2/22.
//  Copyright © 2019 JingchenSoft. All rights reserved.
//

#import "JCShopMallsViewController.h"
#import "QQLBXScanViewController.h"
#import <AlipaySDK/AlipaySDK.h>
#import "Global.h"
#import "StyleDIY.h"
#import "ONImagePickerController.h"
#import <ZLPhotoBrowser/ZLPhotoBrowser.h>


@interface JCShopMallsViewController ()<WKScriptMessageHandler, UIScrollViewDelegate>
@property (nonatomic,strong) LBXScanResult *scanResult;
@property (nonatomic,copy) NSString *lastUrl;
@property (nonatomic,assign) BOOL nativiFunctionCalled;
@property (nonatomic,assign) BOOL hasPreloading;
@property (nonatomic,strong) CLLocationManager *locationManager;
@end

@implementation JCShopMallsViewController

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.isNeedReload = YES;
        self.isNeedBackApp = NO;
        self.nativiFunctionCalled = NO;
        [self addStepLogDescr:@"商城首页初始化init"];
        self.jumpSource = @"y_page_main";
        self.entrance_type_id = @"1";
        [self.configuration.userContentController addScriptMessageHandler:self name:@"backHome"];
        [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_shopScan"];
        [self.configuration.userContentController addScriptMessageHandler:self name:@"scanCodeSearchLabelResult"];
        [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_Enter_Detail"];
        [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_Leave_Detail"];
        [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_Show_Tabbar"];
        [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_Go_Home"];
        [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_StatusBar_Color"];
        [self.configuration.userContentController addScriptMessageHandler:self name:@"gotoAmazonShop"];
        [self.configuration.userContentController addScriptMessageHandler:self name:@"tabShopShowNumChanged"];
        [self.configuration.userContentController addScriptMessageHandler:self name:@"nativeFunction"];
        [self.configuration.userContentController addScriptMessageHandler:self name:@"webEventTrack"];
        [self.configuration.userContentController addScriptMessageHandler:self name:@"loginOnToApp"];
        // 选择图片
        [self.configuration.userContentController addScriptMessageHandler:self name:@"getAlbum"];
        // 拍照
        [self.configuration.userContentController addScriptMessageHandler:self name:@"getPhotograph"];
        // 加载完毕通知
        [self.configuration.userContentController addScriptMessageHandler:self name:@"loadingEnd"];
        // 获取打印机名称
        [self.configuration.userContentController addScriptMessageHandler:self name:@"getPrinterType"];
        // 获取剪贴板内容
        [self.configuration.userContentController addScriptMessageHandler:self name:@"getClipboardContent"];
        // 请求定位权限
        [self.configuration.userContentController addScriptMessageHandler:self name:@"requestLocationPermission"];
        
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(reloadShopHomeView) name:LOGIN_CHANGED_SHOP object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(changeLanguage) name:JCNOTICATION_ChANGELANGUAGE object:nil];
        UIStatusBarStyle statusBarStyle = UIStatusBarStyleDefault;
        if (@available(iOS 13.0, *)) {
            statusBarStyle = UIStatusBarStyleDarkContent;
        }//
        self.statusBarStyle = statusBarStyle;
        self.statusBackColor = 0xFFFFFF;
        self.isHomePageShop = YES;
        NSString *userAgent = [[NSUserDefaults standardUserDefaults] objectForKey:@"niimbot-user-agent"];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(60 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if(self.logStepArr.count > 0 && !self.nativiFunctionCalled){
                NSString *stepLogDetail = [self.logStepArr componentsJoinedByString:@";\n"];
                stepLogDetail = [NSString stringWithFormat:@"商店未调用原生方法：\n%@ \n 信息：%@ \n日志生成时间：%@",stepLogDetail,userAgent,[XYTool getCurrentTimesWithoutTSZ]];
                uploadLogInfoFlutter(UN_NIL(stepLogDetail), @"shopErrorDetail",^(id x,id y){},J_get_sls_Log_Token);
            }
        });
        // 设置scrollView的代理，禁止WebView滚动，避免键盘弹出时页面滚动
        self.webView.scrollView.delegate = self;
    }
    return self;
}

#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    // 禁用WebView滚动，保持内容在初始位置
    if (scrollView == self.webView.scrollView) {
        [scrollView setContentOffset:CGPointZero animated:NO];
    }
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self shopDetailStateView];
    [self addStepLogDescr:@"商城首页ViewDidload"];
    self.view.backgroundColor = COLOR_WHITE;
    [self.view bringSubviewToFront:self.progressView];
    float stateViewheight = iPhoneX?44:20;
    [self.webView setFrame:CGRectMake(0, stateViewheight, kScreenWidth, kScreenHeight - stateViewheight - (iPhoneX?34:0))];
    // 禁用滚动反弹效果
    self.webView.scrollView.bounces = NO;
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshWeb) name:@"payBackToRefrshWeb" object:nil];
    self.webView.scrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    self.isNeedReload = YES;
}


- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self addStepLogDescr:@"商城首页viewWillAppear"];
    if (self.isNeedReload) {
        self.shopHomeHost = [XYCenter sharedInstance].shopHomeHost;
#ifdef DEBUG
        NSString *shopHost = [[NSUserDefaults standardUserDefaults] stringForKey:@"LaboratoryTypeShopDomain"];
        if (shopHost) {
            self.shopHomeHost = shopHost;
            [XYCenter sharedInstance].shopHomeHost = shopHost;
        }
#endif
        // 国内商城地址：【服务域名+/h5_project/#/?key=value&key=value】
        // 海外商城地址：【服务域名+/#/home?key=value&key=value】
        // path
        // 国内商城地址：【服务域名+/h5_project/#/path?key=value&key=value】
        // 海外商城地址：【服务域名+/#/path?key=value&key=value】
        self.lastUrl = [self.shopHomeHost stringByAppendingFormat: [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] ? @"h5_project/#/" : @"#/home"];
        [self loadUrl:self.lastUrl];
        self.isNeedReload = NO;
    }
    // 通知JS端界面将要展示
    NSString *jsStr = [NSString stringWithFormat:@"viewWillAppear()"];
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
      
    }];
}

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    [UIApplication sharedApplication].statusBarStyle = self.statusBarStyle;
    self.detailStateView.backgroundColor = HEX_RGB(self.statusBackColor);
    [self addStepLogDescr:@"商城首页viewDidAppear"];
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    [self addStepLogDescr:@"商城首页viewDidAppear"];
    // 通知JS端界面将要消失
    NSString *jsStr = [NSString stringWithFormat:@"viewWillDisappear()"];
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {

    }];
}

- (void)changeLanguage {
    // 标记需要重新加载
    self.isNeedReload = YES;
}

//支付完成刷新webview
-(void)refreshWeb{
    UIViewController *rootVC = [UIApplication sharedApplication].keyWindow.rootViewController;
    if([rootVC isKindOfClass:[UITabBarController class]]){
        UITabBarController *root = (UITabBarController*)rootVC;
        UINavigationController *naviVC = [root.viewControllers safeObjectAtIndex:root.selectedIndex];
        if(![naviVC.visibleViewController isKindOfClass:[self class]]){
            self.isNeedReload = YES;
        }else{
            NSString *myStr = @"jcydy://com.suofang.jcbqdy/orderlist";
            NSURL *url = [NSURL URLWithString:myStr];
            NSURLRequest *req = [NSURLRequest requestWithURL:url ];
            [self.webView loadRequest:req];
        }
    }
    
}

// 商城埋点
- (void)callH5Mothord{
    NSString *jsStr = @"appUserClickEvent()";
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSLog(@"%@----%@",result, error);
    }];
}

//刷新商城首页
- (void)reloadShopHomeView{
    if(xy_isLogin && !STR_IS_NIL(m_userModel.shopToken)){
        NSString *jsStr = [NSString stringWithFormat:@"AppSetToken('%@', '%@', '%@', '%@')",m_userModel.shopToken, [JCKeychainTool getDeviceIDInKeychain], m_userModel.userId, @"iOS_YDY"];
        [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
            NSLog(@"%@----%@",result, error);
        }];
    }else if(!xy_isLogin){
        NSString *jsStr = [NSString stringWithFormat:@"AppSetToken('', '%@', '', '%@', '%@')",[JCKeychainTool getDeviceIDInKeychain], @"iOS_YDY", @"1"];
        [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
            NSLog(@"%@----%@",result, error);
        }];
        NSString *url = self.shopHomeHost;
        if(![url containsString:@"#/"]){
            url = [NSString stringWithFormat:@"%@#/",url];
        }
        self.isNeedReload = YES;
    }
}

//刷新根据Url路径刷新商城
- (void)refreshWebViewWith:(NSString *)myUrlStr{
    NSLog(@"开始加载URL：%@",myUrlStr);
    if([myUrlStr rangeOfString:@"#/"].location != NSNotFound){
        if([myUrlStr rangeOfString:@"?"].location != NSNotFound){
            self.lastUrl = [NSString stringWithFormat:@"%@&", myUrlStr];
        }else{
            self.lastUrl = [NSString stringWithFormat:@"%@?", myUrlStr];
        }
    }else{
        self.lastUrl = [NSString stringWithFormat:@"%@#/", myUrlStr];
    }
}

//H5交互
#pragma mark - WKScriptMessageHandler
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message
{
    [self addStepLogDescr:@"商城首页开始调用原生方法"];
    NSString *userAgent = [[NSUserDefaults standardUserDefaults] objectForKey:@"niimbot-user-agent"];
    if(self.logStepArr.count > 0 && !self.nativiFunctionCalled){
        NSString *stepLogDetail = [self.logStepArr componentsJoinedByString:@";\n"];
        stepLogDetail = [NSString stringWithFormat:@"商店成功调用原生方法：\n%@ \n 信息：%@ \n日志生成时间：%@",stepLogDetail,userAgent,[XYTool getCurrentTimesWithoutTSZ]];
        uploadLogInfoFlutter(UN_NIL(stepLogDetail), @"shopErrorDetail",^(id x,id y){},J_get_sls_Log_Token);
    }
    self.isNeedReload = NO;
    self.nativiFunctionCalled = YES;
    self.progressView.alpha = 0;
    NSLog(@"商城首页开始调用原生方法:%@",message.name);
    if ([message.name isEqualToString:@"backHome"]) {//点击返回
        [self.navigationController popViewControllerAnimated:YES];
        if([m_userModel.is_FirstShop isEqualToString:@"1"]){
            [[XYCenter sharedInstance] firstShopRequestSuccess:^(id x) {
                
            }];
        }
    }else if ([message.name isEqualToString:@"jcdy_shopScan"]) {//商城扫描
        [self doSaoMa];
    }else if ([message.name isEqualToString:@"loginOnToApp"]) {//获取原生商城token
        [self getTokenFromNative];
    }else if ([message.name isEqualToString:@"scanCodeSearchLabelResult"]) {//商城扫描结果
        NSNumber *searchResult = message.body;
        [[XYCenter sharedInstance] scanTrackWithSearchResult:self.scanResult searchtype:@"2" searchResult:searchResult.boolValue?@"1":@"0" source:@"5"];
    }else if ([message.name isEqualToString:@"jcdy_StatusBar_Color"]) {//状态栏颜色
        NSString *messageBody = message.body;
        [self statusBarBackgroundColor:messageBody];
    }else if ([message.name isEqualToString:@"jcdy_Show_Tabbar"]) {//展示底部tabbar
        if(!self.isHomePageShop){
            return;
        }
        UIViewController *rootVC = ((AppDelegate *)[UIApplication sharedApplication].delegate).mainVC;
        if([rootVC isKindOfClass:[UITabBarController class]]){
            UITabBarController *root = (UITabBarController*)rootVC;
            UINavigationController *naviVC = [root.viewControllers safeObjectAtIndex:root.selectedIndex];
            if(![naviVC.visibleViewController isKindOfClass:[self class]]){
                return;
            }
        }
        NSNumber *num = message.body;
        if([@1 isEqualToNumber:num]){
            self.tabBarController.tabBar.hidden = NO;
        }else{
            self.tabBarController.tabBar.hidden = YES;
        }
        self.view.frame = UIScreen.mainScreen.bounds;
        float stateViewheight = iPhoneX?44:20;
        [self.webView setFrame:CGRectMake(0, stateViewheight, kScreenWidth, kScreenHeight - stateViewheight - (iPhoneX?34:0))];
    }else if ([message.name isEqualToString:@"jcdy_Go_Home"]) {//返回app首页
        NSArray *arr = self.tabBarController.viewControllers;
        UINavigationController *navi = [arr safeObjectAtIndex:1];
        UIViewController *vc = navi.visibleViewController;
        if([vc isKindOfClass:[self class]]){
            self.tabBarController.selectedIndex = 3;
            [self.navigationController popToRootViewControllerAnimated:NO];
        }
    }else if ([message.name isEqualToString:@"gotoAmazonShop"]) {//跳转亚马逊
        NSString *messageBody = message.body;
        NSDictionary *messageDic = [messageBody xy_toDictionary];
        if(messageDic != nil){
            NSString *schemUrl = [messageDic objectForKey:@"schemeUrl"];
            NSString *browserUrl = [messageDic objectForKey:@"browserUrl"];
            NSString *urlString = [NSString stringWithFormat:@"%@%@",@"com.amazon.mobile.shopping://",schemUrl];
            NSURL *url = [NSURL URLWithString:urlString];
            if([[UIApplication sharedApplication] canOpenURL:url]) {
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
            }else if(!STR_IS_NIL(browserUrl)){
                NSURL *URL = [NSURL URLWithString:browserUrl];
                [[UIApplication sharedApplication] openURL:URL options:@{} completionHandler:^(BOOL success) {
                      //  回调
                }];
            }
        }
    }else if ([message.name isEqualToString:@"nativeFunction"]) {//
        NSString *messageInfo = message.body;
        if(!STR_IS_NIL(messageInfo)){
            if([messageInfo containsString:@"?"]){
                NSDictionary *parmsDic = [XYTool dictionaryWithUrlString:messageInfo];
                NSString *actName = [parmsDic objectForKey:@"actName"];
                NSString *nativeRoute = [messageInfo componentsSeparatedByString:@"?"][0];
                [JCToNativeRouteHelp toNativePageWith:UN_NIL(nativeRoute) fromType:JC_H5Web eventTitle:actName origRout:messageInfo];
            }else{
                [JCToNativeRouteHelp toNativePageWith:messageInfo fromType:JC_H5Web eventTitle:@""];
            }
        }
    }else if ([message.name isEqualToString:@"webEventTrack"]) {//
        NSString *messageInfo = message.body;
        NSDictionary *messageDic = [messageInfo xy_toDictionary];
        if(messageDic.allKeys.count > 0){
            NSString *eventCode = messageDic[@"eventCode"];
            NSString *posCode = messageDic[@"posCode"];
            NSDictionary *eventTitle = messageDic[@"params"];
            if(eventTitle == nil || eventTitle.count == 0){
                eventTitle = @{};
            }
            JC_TrackWithparms(UN_NIL(eventCode),UN_NIL(posCode),eventTitle);
        }
    }else if ([message.name isEqualToString:@"tabShopShowNumChanged"]) {
        NSString *showNumber = message.body;
        if(!STR_IS_NIL(showNumber)){
            [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_SHOP_MALL_MSG_SHOW object:showNumber];
        }
    } else if ([message.name isEqualToString:@"getAlbum"]) {
        [self pickImageFromType:UIImagePickerControllerSourceTypePhotoLibrary maxSize:((NSNumber *)message.body).integerValue completion:nil];
    } else if ([message.name isEqualToString:@"getPhotograph"]) {
        [self pickImageFromType:UIImagePickerControllerSourceTypeCamera maxSize:((NSNumber *)message.body).integerValue completion:nil];
    } else if ([message.name isEqualToString:@"loadingEnd"]) {
        // 加载完毕，传递Token
        NSString *jsStr = xy_isLogin ? [NSString stringWithFormat:@"AppSetToken('%@', '%@', '%@', '%@')",m_userModel.shopToken, [JCKeychainTool getDeviceIDInKeychain], m_userModel.userId, @"iOS_YDY"] : [NSString stringWithFormat:@"AppSetToken('', '%@', '', '%@')",[JCKeychainTool getDeviceIDInKeychain], @"iOS_YDY"];
        [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
            NSLog(@"%@----%@",result, error);
        }];
    } else if ([message.name isEqualToString:@"getPrinterType"]) {
        NSString *printerName = [[NSUserDefaults standardUserDefaults] valueForKey:CENTERLASTCONNECTPRINTERNAME];
        NSString *printerType = [[JCBluetoothManager sharedInstance] printerTypeFromPrinterName:printerName isNeedShowChildType:NO];
        NSString *jsStr = [NSString stringWithFormat:@"iOSSetPrinterType('%@')", printerType];
        [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
            
        }];
    } else if ([message.name isEqualToString:@"getClipboardContent"]) {
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        NSString *clipboardText = pasteboard.string ?: @"";
        NSString *jsStr = [NSString stringWithFormat:@"clipboardContentCallBack('%@')", clipboardText];
        [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
            if (error) {
                NSLog(@"getClipboard JS执行错误: %@", error);
            } else {
                NSLog(@"剪贴板内容已发送到前端: %@", clipboardText);
            }
        }];
    } else if ([message.name isEqualToString:@"requestLocationPermission"]) {
        [self requestLocationPermission];
    }
}

//扫码事件处理
- (void)doSaoMa
{
    NSString *mediaType = AVMediaTypeVideo;//读取媒体类型
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];//读取设备授权状态
    if(authStatus == AVAuthorizationStatusRestricted || authStatus == AVAuthorizationStatusDenied){
        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app01310",@"请在“设置-隐私-相机” ， 允许精臣云打印访问你的手机相机") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00297",@"设置") cancelBlock:nil sureBlock:^{
            NSURL * url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            if([[UIApplication sharedApplication] canOpenURL:url]) {
                NSURL*url =[NSURL URLWithString:UIApplicationOpenSettingsURLString];
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
            }
        }];
        return;
    }else if(authStatus == AVAuthorizationStatusNotDetermined){
        [AVCaptureDevice requestAccessForMediaType:mediaType completionHandler:^(BOOL granted) {
            if(granted){
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self toScanGoodsCode];
                });
            }
        }];
    }else{
        [self toScanGoodsCode];
    }
}

- (void)toScanGoodsCode{
    QQLBXScanViewController *vc = [QQLBXScanViewController new];
    vc.scanCreateType = 4;
    vc.libraryType = [Global sharedManager].libraryType;
    vc.scanCodeType = [Global sharedManager].scanCodeType;
    
    vc.style = [StyleDIY qqStyle];
    vc.scanResultBlock = ^(LBXScanResult *result) {
        self.scanResult = result;
        NSString *code = result.strScanned;
        if (!STR_IS_NIL(code)) {
            NSString *jsStr = [NSString stringWithFormat:@"returnScanResult('%@')",code];
            [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
                NSLog(@"%@----%@",result, error);
            }];
        }else{
            //扫描取消
            NSString *jsStr = @"cancelScan()";
            [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
                NSLog(@"%@----%@",result, error);
            }];
            return;
        }
    };
    //镜头拉远拉近功能
    vc.isVideoZoom = YES;
    XYNavigationController *navVC = [[XYNavigationController alloc] initWithRootViewController:vc];
    [self presentViewController:navVC animated:YES completion:^{
        
    }];
}

//支付宝支付完成或取消时候回到App回调
- (void)webView:(WKWebView*)webView decidePolicyForNavigationAction:(WKNavigationAction*)navigationAction decisionHandler:(void(^)(WKNavigationActionPolicy))decisionHandler{
    __block WKNavigationActionPolicy actionPolicy = WKNavigationActionPolicyAllow;
    NSString *str = [navigationAction.request.URL absoluteString];
    NSString *wxPayRedirectUrl = [NSString stringWithFormat:@"%@://iwantbacktoapp/",SchemesWX];
    NSString *redirect_Url = [NSString stringWithFormat:@"redirect_url=%@",wxPayRedirectUrl];
    if([str hasPrefix:@"https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb"] && ![str hasSuffix:redirect_Url]){
        decisionHandler(WKNavigationActionPolicyCancel);
        NSString *redirectUrl = nil;
        if([str containsString:@"redirect_url="]){
            NSRange range = [str rangeOfString:@"redirect_url="];
            redirectUrl = [[str substringToIndex:range.location] stringByAppendingString:redirect_Url];
        }else{
            redirectUrl = [str stringByAppendingString:redirect_Url];
        }
        NSMutableURLRequest *newRequest = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:redirectUrl] cachePolicy:NSURLRequestUseProtocolCachePolicy timeoutInterval:30];
        newRequest.allHTTPHeaderFields = navigationAction.request.allHTTPHeaderFields;
        [newRequest setValue:wxPayRedirectUrl forHTTPHeaderField:@"Referer"];
        newRequest.URL = [NSURL URLWithString:redirectUrl];
        [webView loadRequest:newRequest];
        return;
    }
    if ([@"jcydy://com.suofang.jcbqdy/orderlist" isEqualToString:str]) {
        actionPolicy = WKNavigationActionPolicyCancel;
        NSString *url = [ShopURL stringByAppendingString: [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] ? @"h5_project/#/paySuccess?" : @"#/paySuccess?"];
        [self loadUrl:url];
    }
    if([str isEqualToString:[NSString stringWithFormat:@"%@://iwantbacktoapp/",SchemesWX]]){
        [[NSNotificationCenter defaultCenter] postNotificationName:@"payBackToRefrshWeb" object:nil];
    }
    BOOL isIntercepted = NO;
    isIntercepted = [[AlipaySDK defaultService] payInterceptorWithUrl:[navigationAction.request.URL absoluteString] fromScheme:@"JCYDY" callback:^(NSDictionary *result) {
        // 处理支付结果
        // isProcessUrlPay 代表 支付宝已经处理该URL
        if ([result[@"isProcessUrlPay"]boolValue]) {
            actionPolicy = WKNavigationActionPolicyCancel;
            NSString *url = [ShopURL stringByAppendingString: [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] ? @"h5_project/#/?" : @"#/home?"];
            [self loadUrl:url];
        }
    }];
    if(isIntercepted){
        actionPolicy =WKNavigationActionPolicyCancel;
    }
    
    NSString *urlString = [[navigationAction.request.URL absoluteString] stringByRemovingPercentEncoding];
    if ([urlString containsString:@"weixin://wap/pay?"]) {
        actionPolicy =WKNavigationActionPolicyCancel;
        //解决wkwebview weixin://无法打开微信客户端的处理
        NSURL*url = [NSURL URLWithString:urlString];
        BOOL bSucc = [[UIApplication sharedApplication] canOpenURL:url];
        if(!bSucc) {
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"",@"未检测到微信APP，请您先安装")];
        } else {
            [[UIApplication sharedApplication] openURL:url options:@{UIApplicationOpenURLOptionUniversalLinksOnly: @NO} completionHandler:^(BOOL success) {
            }];
        }
    }
    
    // 加载新链接，内部跳转
    if (navigationAction.targetFrame == nil) {
        [webView loadRequest:navigationAction.request];
    }
    
    decisionHandler(actionPolicy);
    
}

// MARK: - 选择图片 && 拍照 -
- (void)pickImageFromType:(UIImagePickerControllerSourceType)sourceType maxSize:(NSInteger)maxSize completion:(void (^)(NSString *))successBlock {
    void (^pickBlock)(void) = ^() {
        ONImagePickerController *imagePicker = [ONImagePickerController sharedInstance];
        float cutHeight = 0;
        cutHeight = kScreenWidth/1.6;
        [imagePicker showImagePickerWithPresentController:self sourceType:sourceType allowEdit:NO cutFrame:CGRectMake(0, (kScreenHeight - cutHeight)/2, kScreenWidth, cutHeight)];
        [imagePicker setChooseImageBlock:^(UIImage * _Nonnull image) {
            // 先Dismiss掉页面,前端展示Loading
            dispatch_async(dispatch_get_main_queue(), ^{
                [self dismissViewControllerAnimated:false completion:nil];
                NSString *jsStr = [NSString stringWithFormat:@"imageLoading()"];
                [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
                    
                }];
                
            });
            NSString *base64;
            NSData *pngData = UIImagePNGRepresentation(image);
            NSInteger scale = pngData.length / maxSize;
            if (scale > 1) {
                // 按照比例进行压缩
                CGSize size = CGSizeApplyAffineTransform(image.size, CGAffineTransformMakeScale(1.0 / scale, 1.0 / scale));
                UIImage *targetImage = [image imageByScalingAndCroppingToSize: size];
                NSData *targetPngData = UIImagePNGRepresentation(targetImage);
                if (targetPngData.length > maxSize) {
                    // 转化为JPG压缩
                    base64 = UIImageJPEGRepresentation(image, 0.8).jk_base64EncodedString;
                } else {
                    base64 = targetPngData.jk_base64EncodedString;
                }
            } else {
                base64 = UIImageJPEGRepresentation(image, 0.8).jk_base64EncodedString;
            }
            dispatch_async(dispatch_get_main_queue(), ^{
                NSString *jsStr = [NSString stringWithFormat:@"imageDataCallback('%@')", UN_NIL(base64)];
                [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
                    
                }];
            });
            if (successBlock) {
                successBlock(base64);
            }
        }];
    };
    
    if (sourceType == UIImagePickerControllerSourceTypeCamera) {
        [self checkCameraAuthorization:^(bool isAuthorization) {
            if (isAuthorization) {
                pickBlock();
            }
        }];
    } else {
        pickBlock();
    }
}

- (void)checkCameraAuthorization:(void (^)(bool))completion {
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo];//读取设备授权状态
    if(authStatus == AVAuthorizationStatusRestricted || authStatus == AVAuthorizationStatusDenied){
        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app01310",@"请在“设置-隐私-相机” ， 允许精臣云打印访问你的手机相机") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00297",@"设置") cancelBlock:^{
            
        } sureBlock:^{
            NSURL * url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            if([[UIApplication sharedApplication] canOpenURL:url]) {
                NSURL*url =[NSURL URLWithString:UIApplicationOpenSettingsURLString];
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
            }
        }];
        return;
    }else if(authStatus == AVAuthorizationStatusNotDetermined){
        [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
            if(granted){
                dispatch_async(dispatch_get_main_queue(), ^{
                    if (completion) {
                        completion(YES);
                    }
                });
            }
        }];
    }else{
        if (completion) {
            completion(YES);
        }
    }
}


- (void)uploadImage:(UIImage *)image success:(void (^)(NSString *))successBlock{
    NSData *imageData = UIImagePNGRepresentation(image);
    NSString *deviceId = [JCKeychainTool getDeviceIDInKeychain];
    [[JCOSSManager sharedManager] oss_uploadFileWithParmsDic:@{@"module":@"USER_IMG",@"param":deviceId} fullFileName:[NSString stringWithFormat:@"%@.png",[NSString jk_UUID]] fileData:imageData Success:^(NSString * _Nonnull key, NSString * _Nonnull url) {
            if (url && url.length > 0) {
                successBlock(url);
            }
        } failure:^(NSString * _Nonnull key, NSString * _Nonnull errMsg) {
            [MBProgressHUD showToastWithMessageDarkColor:errMsg];
    }];
}

// 请求定位权限
- (void)requestLocationPermission {
    CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
    
    if (status == kCLAuthorizationStatusAuthorizedWhenInUse || 
        status == kCLAuthorizationStatusAuthorizedAlways) {
        // 已有权限，直接回调
        [self notifyLocationPermissionStatus:YES];
    } 
    else if (status == kCLAuthorizationStatusNotDetermined) {
        // 未确定状态，请求权限
        self.locationManager = [[CLLocationManager alloc] init];
        self.locationManager.delegate = self;
        [self.locationManager requestWhenInUseAuthorization];
    } 
    else {
        // 已拒绝或受限制，提示用户
        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示")
                       message:XY_LANGUAGE_TITLE_NAMED(@"",@"请在“设置-隐私-定位服务”， 允许精臣云打印访问你的位置信息")
              cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消")
                sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00297",@"设置") 
                   cancelBlock:^{
                       [self notifyLocationPermissionStatus:NO];
                   } 
                     sureBlock:^{
                         NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
                         if([[UIApplication sharedApplication] canOpenURL:url]) {
                             [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
                         }
                         [self notifyLocationPermissionStatus:NO];
                     }];
    }
}

// 通知Web页面定位权限状态
- (void)notifyLocationPermissionStatus:(BOOL)hasPermission {
    NSString *jsStr = [NSString stringWithFormat:@"hasLocationPermission(%d)", hasPermission ? 1 : 0];
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        if (error) {
            NSLog(@"通知Web页面定位权限状态失败: %@", error);
        }
    }];
}

// 定位管理器代理方法
- (void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status {
    if (status == kCLAuthorizationStatusAuthorizedWhenInUse || 
        status == kCLAuthorizationStatusAuthorizedAlways) {
        [self notifyLocationPermissionStatus:YES];
    } else if (status == kCLAuthorizationStatusDenied || 
               status == kCLAuthorizationStatusRestricted) {
        [self notifyLocationPermissionStatus:NO];
    }
}

@end
