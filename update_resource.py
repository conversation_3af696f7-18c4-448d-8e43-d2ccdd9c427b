"""
脚本执行参考
拉取测试环境的多语言:                       python3 update_resource.py test
拉取生产环境的多语言(不传参数默认就是生产的):   python3 update_resource.py
"""

import sys
import os.path
import json

import requests

ANDROID_NIIMBOT_USER_AGENT = 'AppId/com.gengcon.android.jccloudprinter OS/Android AppVersionName/{} Model/HUAWEI-BRA-AL00 SystemVersion/12 DeviceId/0000000000000000 referer/CP001Mobile'
IOS_NIIMBOT_AGENT = 'AppId/com.suofang.jcbqdy OS/ios AppVersionName/{} Model/iPhone-15-Pro-Max SystemVersion/17.5.1 DeviceId/5EF4D144-D3FA-4394-84A4-E1528B481788 referer/CP001Mobile'

root_path = os.path.split(os.path.abspath(__file__))[0]

ios_lang_res_path = '/ios/Runner/Resource(资源)/defaultLanguageLproj/LanguageBundle.bundle/'
ios_device_res_path = '/ios/Runner/Resource(资源)/defaultCacheBaseData/'
android_resource_path = '/android/baselibrary/src/main/assets/'
flutter_resource_path = '/assets/config/'


lang_url = {
    'test': 'https://oss-print-fat.jc-test.cn/public_resources/static_resources/languagePack/{}.json',
    'prod': 'https://oss-print.niimbot.com/public_resources/static_resources/languagePack/{}.json',
}


device_url = {
    'test': 'https://oss-print-fat.jc-test.cn/public_resources/static_resources/devices.json',
    'prod': 'https://oss-print.niimbot.com/public_resources/static_resources/devices.json'
}

app_config_url = {
    'test': 'https://oss-print-fat.jc-test.cn/public_resources/static_resources/app_config.json',
    'prod': 'https://oss-print.niimbot.com/public_resources/static_resources/app_config.json'
}

language_lists = []
cross_domain_appid = set()
devices_res_list = [
    'en',
    'zh'
]

seriesModule_list = [
    'en',
    'zh'
]


def check_dir():
    # check & create folder
    for item in [ios_lang_res_path, ios_device_res_path, android_resource_path, flutter_resource_path]:
        dir_path = '{}{}'.format(root_path, item)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)


def get_inner_app_list(env, platform, app_version):
    url = {
        'test': 'https://print.jc-test.cn/graphql/',
        'prod': 'https://print.niimbot.com/graphql/',

    }
    _niimbot_user_agent = {
        'ios': IOS_NIIMBOT_AGENT.format(app_version),
        'android': ANDROID_NIIMBOT_USER_AGENT.format(app_version)
    }
    headers = {
        'niimbot-user-agent': _niimbot_user_agent[platform],
        'Content-Type': 'application/json',
    }
    payload = {
        'query':'query getInnerAppList { getInnerAppList { categoryName appList { id name icon badge badgeColor isVip isNew needLogin type router packageName dialogTitle dialogMessage appKey appSecret} } }'
    }
    r = requests.post(url[env], headers=headers, data=json.dumps(payload), verify=False)
    # print(r.text)
    write_data = r.json()['data']['getInnerAppList']
    return write_data


def get_guide_page_list(env, languagecode, app_version):
    # languagecode: zh-ch, en
    if languagecode == 'zh':
        languagecode = 'zh-cn'
    url = {
        'test': 'https://print.jc-test.cn/api/content/guidePage/list',
        'prod': 'https://print.niimbot.com/api/content/guidePage/list'
    }
    headers = {
        'niimbot-user-agent': ANDROID_NIIMBOT_USER_AGENT.format(app_version),
        'languagecode': languagecode,
    }
    r = requests.post(url[env], headers=headers)
    return r.json()['data']


def get_all_languages(env):
    url = {
        'test': 'https://print.jc-test.cn/api/system/language/listAllAppLanguages',
        'prod': 'https://print.niimbot.com/api/system/language/listAllAppLanguages'
    }

    headers = {
        'niimbot-user-agent': 'AppId/com.gengcon.android.jccloudprinter OS/Android AppVersionName/6.0.6 Model/HUAWEI-BRA-AL00 SystemVersion/12 DeviceId/0000000000000000 referer/CP001Mobile',
    }
    r = requests.post(url[env], headers=headers)
    data = r.json()
    # print(data)
    return data['data']


def update_flutter_printer_list(env):
    # update printer list
    r = requests.get(device_url[env])
    file_name = {
        'test': 'printerList_test.json',
        'prod': 'printerList.json'
    }
    output_file = '{}{}{}'.format(root_path, flutter_resource_path, file_name[env])
    print(output_file)
    with open(output_file, 'w') as f:
        f.write(str(json.dumps(r.json(), ensure_ascii=False, indent=4)))


def update_flutter_app_config(env):
    r = requests.get(app_config_url[env])
    file_name = 'app_config.json'
    output_file = '{}{}{}'.format(root_path, flutter_resource_path, file_name)
    print(output_file)
    with open(output_file, 'w') as f:
        f.write(str(json.dumps(r.json(), ensure_ascii=False, indent=4)))


def update_flutter_res(env):
    update_flutter_printer_list(env)
    update_flutter_app_config(env)


def update_ios_res(env, app_version):
    # update language list
    data = get_all_languages(env)
    output_file = '{}{}languageList.text'.format(root_path, ios_lang_res_path)
    print(output_file)
    with open(output_file, 'w') as f:
        f.write(str(json.dumps(data, ensure_ascii=False, indent=4)))

    # update language
    for lang in language_lists:
        url = lang_url[env].format(lang)
        r = requests.get(url)
        output_file = '{}{}{}.text'.format(root_path, ios_lang_res_path, lang)
        print(output_file)
        with open(output_file, 'w') as f:
            f.write(str(json.dumps(r.json(), ensure_ascii=False, indent=4)))

    # update printer list
    r = requests.get(device_url[env])
    list_data = r.json()
    data = {
        'limit': 1000,
        'list': list_data
    }
    file_name = {
        'test': 'printerList_test.text',
        'prod': 'printerList.text'
    }
    output_file = '{}{}{}'.format(root_path, ios_device_res_path, file_name[env])
    print(output_file)
    with open(output_file, 'w') as f:
        f.write(str(json.dumps(data, ensure_ascii=False, indent=4)))

    # update deviceList
    for lang in ['en', 'zh-cn']:
        data = get_guide_page_list(env, lang, app_version)
        file_name_ext = {
            'test': '_test',
            'prod': ''
        }
        output_file = '{}{}devicesList_{}{}.text'.format(root_path, ios_device_res_path, lang, file_name_ext[env])
        print(output_file)
        with open(output_file, 'w') as f:
            f.write(str(json.dumps(data, ensure_ascii=False, indent=4)))

    # update inner app list
    file_name = 'appCenterInfo.json'
    output_file = '{}{}{}'.format(root_path, ios_device_res_path, file_name)
    print(output_file)
    write_data = get_inner_app_list(env,'ios',app_version)
    with open(output_file, 'w') as f:
        f.write(str(json.dumps(write_data, ensure_ascii=False, indent=4)))


def update_android_res(env, app_version):
    # update language list
    data = get_all_languages(env)
    for item in data:
        item['delete'] = item.pop('isDelete')
    output_file = '{}{}language_list_local.json'.format(root_path, android_resource_path)
    print(output_file)
    with open(output_file, 'w') as f:
        f.write(str(json.dumps(data, ensure_ascii=False, indent=4)))

    # update language
    for lang in language_lists:
        url = lang_url[env].format(lang)
        r = requests.get(url)
        output_file = '{}{}language_online_{}.json'.format(root_path, android_resource_path, lang)
        print(output_file)
        with open(output_file, 'w') as f:
            f.write(str(json.dumps(r.json(), ensure_ascii=False, indent=4)))

        if env == 'prod':
            for k, v in r.json()['lang'].items():
                if 'jc-test' in v['value'] or 'jc-saas' in v['value']:
                    cross_domain_appid.add(k)

    # update devices
    r = requests.get(device_url[env])
    for lang in devices_res_list:
        output_file = '{}{}DevicesModule_{}.json'.format(root_path, android_resource_path, lang)
        print(output_file)
        with open(output_file, 'w') as f:
            f.write(str(json.dumps(r.json(), ensure_ascii=False, indent=4)))

    # update seriesModule
    for lang in seriesModule_list:
        data = get_guide_page_list(env, lang, app_version)

        # todo:  check if necessary
        for item in data:
            item['guide_image'] = item.pop('image')
            item['guide_name'] = item.pop('name')
            item['hardware_series_id'] = item.pop('hardwareIdStr')
            item['machine_name'] = item.pop('hardwareNameStr')
            item['sort'] = item.pop('orderNo')
            item['update_time'] = item.pop('createTime')

        output_file = '{}{}SeriesModule_{}_online.json'.format(root_path, android_resource_path, lang)
        print(output_file)
        with open(output_file, 'w') as f:
            f.write(str(json.dumps(data, ensure_ascii=False, indent=4)))

    # update inner app list
    file_name = 'inner_app_list.json'
    output_file = '{}{}{}'.format(root_path, android_resource_path, file_name)
    print(output_file)
    write_data = get_inner_app_list(env, 'android', app_version)
    with open(output_file, 'w') as f:
        f.write(str(json.dumps(write_data, ensure_ascii=False, indent=4)))


if __name__ == '__main__':
    check_dir()

    env = 'prod'
    app_version = '6.3.3'
    if len(sys.argv) > 1:
        env = sys.argv[1]
        app_version = sys.argv[2]
    print("更新资源包环境:", env)

    lang_list_data = get_all_languages(env)
    for item in lang_list_data:
        language_lists.append(item['code'])

    print(language_lists)

    update_android_res(env, app_version)
    update_ios_res(env,app_version)
    update_flutter_res(env)
    print("\n!!! cross domain appid list:  {}".format(cross_domain_appid))
