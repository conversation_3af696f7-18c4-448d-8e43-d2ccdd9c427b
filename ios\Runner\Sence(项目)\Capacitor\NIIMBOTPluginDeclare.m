//
//  NIIMBOTPlugin.m
//  Runner
//
//  Created by 杨瑞 on 2022/12/16.
//

#import <Capacitor/Capacitor.h>

CAP_PLUGIN(NIIMBOTPlugin, "NIIMBOT",
           CAP_PLUGIN_METHOD(getLaunchConfiguration, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(quit, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(downloadFile, CAPPluginReturnNone);
           CAP_PLUGIN_METHOD(removeFile, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(install, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(restart, CAPPluginReturnNone);
           CAP_PLUGIN_METHOD(request, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(navigateToMiniProgram, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(eventTrack, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(openBuyVIP, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(QRCodeToLiveCode, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(clientUploadFile, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(liveCodeOperateEvent, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(liveCodeInfoChanged, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(selectLiveCode, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(sheetInfoChanged, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(selectSheet, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(share, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(generateTemplatePreview, CAPPluginReturnNone);
           CAP_PLUGIN_METHOD(generateWifiCodePreview, CAPPluginReturnNone);
           CAP_PLUGIN_METHOD(navigateToWebPage, CAPPluginReturnNone);
           CAP_PLUGIN_METHOD(startSearchPrinter, CAPPluginReturnNone);
          // CAP_PLUGIN_METHOD(shareFile, CAPPluginReturnNone);
           CAP_PLUGIN_METHOD(startPrint, CAPPluginReturnNone);
           CAP_PLUGIN_METHOD(openScanPage, CAPPluginReturnNone);
           CAP_PLUGIN_METHOD(openPrintPage, CAPPluginReturnNone);
           CAP_PLUGIN_METHOD(disconnectPrinter, CAPPluginReturnNone);
           CAP_PLUGIN_METHOD(updatePrintTemplate, CAPPluginReturnNone);
           CAP_PLUGIN_METHOD(jumpToDrawingBoard, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(generateTemplatePreviewWithLayout, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(openPrintPageWithLayout, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(batchGeneratePreviewWithLayout, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(batchGeneratePreviewWithLayouts, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(startBatchPrint, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(generateTemplateWithLayout, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(closePrintPage, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(removeListeners, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(getIOSBasePath, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(cancelUploadTask, CAPPluginReturnPromise);
)
