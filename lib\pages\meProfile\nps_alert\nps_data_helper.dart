import 'package:flutter/foundation.dart';
import 'package:niimbot_print_setting_plugin/utils/Logger.dart';
import 'package:text/application.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';

const String NspShowDataKey = "NspShowDataKey";
const String NspShowIntervalKey = "NspShowIntervalKey";
const int kNspShowIntervalSubmitted = 90; // 已提交过，90 天
const int kNspShowIntervalUnsubmitted = 30; // 未提交过，30 天

class NpsDataHelper {
  static final Logger _logger = Logger("NpsDataHelper", on: kDebugMode);

  static closeNps({bool hasSubmit = false, String? uniAppId = ""}) {
    DateTime now = DateTime.now();
    int timestamp = now.millisecondsSinceEpoch;
    _logger.log("closeNps= hasSubmit=$hasSubmit timestamp=$timestamp");
    Application.sp.setInt(NspShowDataKey + (uniAppId ?? ""), timestamp);
    Application.sp.setInt(
        NspShowIntervalKey + (uniAppId ?? ""), hasSubmit ? kNspShowIntervalSubmitted : kNspShowIntervalUnsubmitted);
    if (uniAppId?.isEmpty ?? true) {
      Application.isCloseNps = true;
      ToNativeMethodChannel().closeNpsView();
    }
  }

  static bool isShowNps({String? uniAppId = ""}) {
    DateTime now = DateTime.now();
    int? beforeTimestamp = Application.sp.getInt(NspShowDataKey + (uniAppId ?? ""));
    int interval = Application.sp.getInt(NspShowIntervalKey + (uniAppId ?? "")) ?? kNspShowIntervalUnsubmitted;
    _logger.log("isShowNps beforeTimestamp=$beforeTimestamp interval=$interval");
    if (beforeTimestamp != null) {
      DateTime dateBefore = DateTime.fromMillisecondsSinceEpoch(beforeTimestamp);
      Duration difference = now.difference(dateBefore);
      int days = difference.inDays;
      bool isShow = days >= interval ? true : false;
      if (uniAppId?.isEmpty ?? true) {
        Application.isCloseNps = !isShow;
      }
      return isShow;
    } else {
      return true;
    }
  }
}
