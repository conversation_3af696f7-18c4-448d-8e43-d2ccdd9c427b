import 'dart:io';

import 'package:text/application.dart';

class HttpApi {
  ///设备列表
  static const deviceList = {"path": "/kraken/system/device/list", "needLogin": false, "method": "GET"};

  static const deviceSeriesList = {"path": "/content/guidePage/list", "needLogin": false, "method": "POST"};

  /// 获取用户excel数量
  static const excelCount = {"path": "/user/cloudFile/my", "needLogin": true, "method": "POST"};

  ///获取商品库数量
  static const goodsCount = {"path": "/product/customStorage/goodsCount", "needLogin": true, "method": "GET"};

  ///获取用户商城信息
  static const userShopInfo = {"path": "/client/user/index", "needLogin": true, "method": "GET"};

  ///获取用户商城最后购买一单信息
  static const userShopLastInfo = {"path": "/shop/user/order/lastInfo", "needLogin": true, "method": "POST"};

  ///添加商品到购物车
  static const addGoodsToCart = {"path": "/shop/user/cart/add", "needLogin": true, "method": "POST"};

  /// 拉取个人信息
  static const userInfo = {"path": '/user/logged', "needLogin": true};

  /// 获取oss的鉴权信息
  static const ossAuth = {"path": '/image-upload/aliyun/oss/policy', "needLogin": true};

  /// 拉取灰度配置
  static const grayConfig = {"path": "/system/user/stgStrategyConfig", "needLogin": false, "method": "GET"};

  /// 退出登录
  static const logout = {"path": '/user/logout', "needLogin": true};

  /// 获取app配置
  static const appConfig = {"path": '/system/app/configs', "needLogin": false, "method": "GET"};
}

/// 行业模版
extension IndustryTemplateApi on HttpApi {
  /// 行业模版分类
  static const industryTemplateCategories = {
    'method': 'POST',
    'path': '/industryTemplate/categories',
    'needLogin': false,
  };

  /// 分类下的模版列表
  static const industryTemplateListUnderCategory = {
    'method': 'POST',
    'path': '/industryTemplate/page',
    'needLogin': false,
  };

  /// 拉取个人信息
  static const userInfo = {"path": '/user/logged', "needLogin": true};

  /// 获取oss的鉴权信息
  static const ossAuth = {"path": '/image-upload/aliyun/oss/policy', "needLogin": true};

  /// 上传场景信息
  static const sendGraphql = {
    'method': 'POST',
    'path': '/graphql/',
    'needLogin': false,
  };

  /// 推荐的模版
  static const recommendIndustryTemplate = {
    'method': 'POST',
    'path': '/industryTemplate/recommend',
    'needLogin': false,
  };

  /// 搜索模版
  static const searchIndustryTemplate = {
    'method': 'POST',
    'path': '/industryTemplate/search',
    'needLogin': false,
  };

  /// 搜索模版
  static const searchCanvasIndustryTemplate = {
    'method': 'POST',
    'path': '/industryTemplate/search?from=drawboard',
    'needLogin': false,
  };

  /// 行业模版尺寸
  static const getTemplateSize = {
    'method': 'GET',
    'path': '/industryTemplate/sizes',
    'needLogin': false,
  };

  /// 获取广告列表
  static const getAdvertisementList = {
    'method': 'POST',
    'path': '/content/all-banners',
    'needLogin': false,
  };
}

/// 选择标签纸模板
extension LabelTemplateApi on HttpApi {
  /// 标签纸模板分类
  static const labelTemplateCategory = {
    'method': 'POST',
    'path': '/stdTemplate/listIndustryCategoryMachineAdaptInfo',
    'needLogin': false,
  };

  /// 标签纸模板分类
  static const labelTemplatePage = {
    'method': 'POST',
    'path': '/stdTemplate/page',
    'needLogin': false,
  };

  /// 最近使用标签纸
  static const recentUsedLabels = {
    'method': 'POST',
    'path': '/industryTemplate/recentUsedLabels',
    'needLogin': true,
  };
}

/// 我的模板
extension MyTemplateApi on HttpApi {
  static const createTemplate = {'method': 'POST', 'path': '/template/create', 'needLogin': true};

  static const updateTemplate = {'method': 'POST', 'path': '/template/update', 'needLogin': true};

  static const createEtagTemplate = {'method': 'POST', 'path': '/industryTemplate/etag/create', 'needLogin': true};

  static const updateEtagTemplate = {'method': 'POST', 'path': '/industryTemplate/etag/update', 'needLogin': true};

  ///获取模版详情
  static Map<String, dynamic> fetchTemplateDetailById(String templateId) {
    return {'method': 'GET', 'path': '/template/user/$templateId', 'needLogin': false};;
  }

  ///获取模版详情
  static Map<String, dynamic> fetchCustomTemplateDetailById(String templateId) {
    return {'method': 'POST', 'path': '/template/getCustomTemplate/$templateId', 'needLogin': false};;
  }

  ///获取共享给我的模版详情
  static Map<String, dynamic> fetchFolderShareTemplateDetailById(String templateId) {
    return {'method': 'GET', 'path': '/shared-inbox/user-templates/$templateId', 'needLogin': true};;
  }

  /// 通过id和更新时间获取模板详情(增量方式)
  static const fetchTemplateUpdateStatus = {
    'method': 'POST',
    'path': '/template/templateUpdateStatus',
    'needLogin': false,
  };

  /// 扫码获取云模版
  static const cloudTemplateByScanCode = {
    'method': 'POST',
    'path': '/template/getCloudTemplateByScanCode',
    'needLogin': false,
  };

  /// 获取文件夹列表
  static const cloudFolderList = {
    'method': 'POST',
    'path': '/user/cloudFolder/lists?shared=1',
    'needLogin': true,
  };

  /// 退出共享文件夹
  static const exitFolderList = {
    'method': 'DELETE',
    'path': '/collaboration/groups',
    'needLogin': true,
  };

  /// 获取我的共享文件夹列表
  static const shareOutFolderList = {
    'method': 'GET',
    'path': '/shared-assets/folders',
    'needLogin': true,
  };

  /// 获取共享给我文件夹列表
  static const shareMeFolderList = {
    'method': 'GET',
    'path': '/shared-inbox/folders',
    'needLogin': true,
  };

  /// 获取我的模板
  static const myTemplateList = {
    'method': 'POST',
    'path': '/template/myList',
    'needLogin': true,
  };


  /// 获取我的模板
  static const deleteTemplate = {
    'method': 'POST',
    'path': '/template/delete',
    'needLogin': true,
  };


  /// 获取我的模板
  static const moveTemplate = {
    'method': 'POST',
    'path': '/user/cloudFolder/moveTemplete',
    'needLogin': true,
  };

  /// 文件夹重命名
  static const folderRename = {
    'method': 'POST',
    'path': '/user/cloudFolder/rename',
    'needLogin': true,
  };

  /// 文件夹删除
  static const folderDelete = {
    'method': 'POST',
    'path': '/user/cloudFolder/delete',
    'needLogin': true,
  };

  /// 新建文件夹
  static const folderCreate = {
    'method': 'POST',
    'path': '/user/cloudFolder/create',
    'needLogin': true,
  };

  /// 共享文件夹
  static const folderShare = {
    'method': 'PUT',
    'path': '/shared-assets/folders',
    'needLogin': true,
  };

  /// 获取共享成员列表
  static const shareMemberList = {
    'method': 'GET',
    'path': '/collaboration/members',
    'needLogin': true,
  };

  /// 创建分享码
  static const createShareCode = {
    'method': 'PUT',
    'path': '/collaboration/inviting-codes',
    'needLogin': true,
  };

  /// 共享文件夹
  static Map<String, Object> cancelFolderShare = {
    'method': 'DELETE',
    'path': '/shared-assets/folders',
    'needLogin': true,
  };

  /// 模板删除
  static const templateDelete = {
    'method': 'POST',
    'path': '/template/delete',
    'needLogin': true,
  };

  /// 模板移动
  static const templateMove = {
    'method': 'POST',
    'path': '/user/cloudFolder/moveTemplete',
    'needLogin': true,
  };

  /// 模板重命名
  static const templateRename = {
    'method': 'POST',
    'path': '/template/updateName',
    'needLogin': true,
  };

  /// 分享码decode
  static const shareDecode = {
    'method': 'POST',
    'path': '/template/share/code/decode',
    'needLogin': false
  };
}

/// 选择标签纸模板
extension EtagTemplateApi on HttpApi {
  /// 保存屏幕内容
  static const saveNfcScreenInfo = {
    'method': 'POST',
    'path': '/applet/eTag/save',
    'needLogin': false,
  };

  /// 获取屏幕内容
  static Map<String, Object> getNfcScreenInfo = {
    'method': 'GET',
    'path': '/applet/eTag',
    'needLogin': false,
  };

  /// 获取模板尺寸
  static const getTemplateSize = {
    'method': 'GET',
    'path': '/industryTemplate/etag/sizes',
    'needLogin': false,
  };

  /// 获取模板类型
  static const getTemplateSort = {
    'method': 'GET',
    'path': '/industryTemplate/etag/categories',
    'needLogin': false,
  };

  /// 获取模板分页数据
  static const getTemplatePageList = {
    'method': 'POST',
    'path': '/industryTemplate/etag/page',
    'needLogin': false,
  };
}

/// 电子价签商品库相关接口
extension GoodsLibApi on HttpApi {
  /// 获取用户商品库商品分类下的商品列表
  static const userGoodList = {
    'method': 'POST',
    'path': '/product/customStorage/applist',
    'needLogin': true,
  };

  ///保存更新商品库
  static const updateGoods = {
    'method': 'POST',
    'path': '/product/customStorage/',
    'needLogin': true,
  };

  /// 获取用户商品库分类
  static const userGoodscategory = {
    'method': 'GET',
    'path': '/user-commodity/categories',
    'needLogin': true,
  };

  /// 添加用户商品库分类
  static const addGoodsCategory = {
    'method': 'POST',
    'path': '/user-commodity/categories',
    'needLogin': true,
  };

  /// 删除用户商品库分类
  static Map<String, Object> delGoodsCategory = {
    'method': 'DELETE',
    'path': '/user-commodity/categories',
    'needLogin': true,
  };

  /// 更新用户商品库分类
  static Map<String, Object> updateGoodsCategory = {
    'method': 'PUT',
    'path': '/user-commodity/categories',
    'needLogin': true,
  };

  /// 通过barcode获取商品信息
  static const getGoodsInfoByBarcode = {
    'method': 'GET',
    'path': '/product/customStorage',
    'needLogin': true,
  };

  /// 手动录入商品
  static const addGoodsInfo = {
    'method': 'POST',
    'path': '/product/customStorage',
    'needLogin': true,
  };

  /// 通过barcode修改已存在的商品
  static const updateAppGoodsInfo = {
    'method': 'POST',
    'path': '/product/customStorage/updateAppGoodsInfo',
    'needLogin': true,
  };

  /// 获取商品选择短链
  static const getSelectGoodsUrl = {
    'method': 'POST',
    'path': '/product/customStorage/user/goods/link',
    'needLogin': true,
  };

  /// 获取根据id商品信息
  static const getGoodsInfo = {
    'method': 'get',
    'path': '/product/customStorage/user/goods/list',
    'needLogin': true,
  };

  ///添加商品自定义字段
  static const addGoodsField = {
    'method': 'PUT',
    'path': '/product/custom-fields',
    'needLogin': true,
  };

  ///添加商品自定义字段
  static const getGoodsField = {
    'method': 'GET',
    'path': '/product/custom-fields',
    'needLogin': false,
  };
}

/// 创建模板
extension CreateLabelApi on HttpApi {
  /// 打印机列表
  static const hardwareList = {
    'method': 'POST',
    'path': '/hardware/list',
    'needLogin': false,
  };
}

extension HardwareList on HttpApi {
  /// 打印机列表
  static final hardwareListOSS = {
    'method': 'GET',
    'baseUrl':
        Application.appEnv != AppEnv.production ? 'https://oss-print-fat.jc-test.cn' : 'https://oss-print.niimbot.com',
    'path': '/public_resources/static_resources/devices.json',
    'needLogin': false,
  };
}

extension MaterialList on HttpApi {
  /// 所有vip素材id集合
  static const vipMaterialIdList = {
    'method': 'POST',
    'path': '/materialLib/get/vip-ids',
    'needLogin': false,
  };

  /// 素材图标分类列表
  static const materialList = {
    'method': 'POST',
    'path': '/materialIndCat/list',
    'needLogin': false,
  };

  /// 素材分类下子分类的数据
  static const listOfMaterial = {
    'method': 'POST',
    'path': '/materialLib/page',
    'needLogin': false,
  };

  /// 所有vip素材边框id集合
  static const vipMaterialBoderIdList = {
    'method': 'POST',
    'path': '/material-borders/vip-ids',
    'needLogin': false,
  };

  /// 素材线框分类列表
  static const materialBoderList = {
    'method': 'POST',
    'path': '/material-border-indcat/list',
    'needLogin': false,
  };

  /// 素材分类下子分类的数据
  static const listOfMaterialBoder = {
    'method': 'POST',
    'path': '/material-borders/page',
    'needLogin': false,
  };
}

extension OcrApi on HttpApi {
  /// ocr识别请求
  static Map<String, dynamic> ocrRecognition = {
    "method": 'POST',
    'path': '/ocr/recognition',
    'needLogin': true,
  };
}

extension LiveCode on HttpApi {
  /// 活码列表
  static Map<String, dynamic> liveCodeList = {
    "method": 'GET',
    'path': '/hippo/dynamic-qrcode/page',
    'needLogin': true,
  };
}

extension ExcelApi on HttpApi {
  /// 上传Excel文件到云服务器
  static Map<String, dynamic> uploadExcelFile = {
    "method": 'POST',
    'path': Platform.isAndroid ? '/user/cloudFile/create/2' : '/user/cloudFile/create/1',
    'needLogin': true,
  };

  static const excelFileList = {
    "method": 'POST',
    'path': '/user/cloudFile/my',
    'needLogin': true,
  };

  static const excelFileDetail = {
    "method": 'POST',
    'path': '/user/cloudFile/data',
    'needLogin': true,
  };

  static const cloudFileInfo = {
    "method": 'GET',
    'path': '/user/cloudFile',
    'needLogin': true,
  };

  static const deleteExcelFile = {
    "method": 'POST',
    'path': '/user/cloudFile/delete',
    'needLogin': true,
  };

  // static const uploadByCloudFileId = {
  //   "method": 'POST',
  //   'path': '/product/customStorage/uploadByCloudFileId',
  //   'needLogin': true,
  // };
  // static const userUploadGoodsConfirm = {
  //   "method": 'POST',
  //   'path': '/product/customStorage/userUploadGoodsConfirm',
  //   'needLogin': true,
  // };
  static const uploadByCloudFileId = {
    "method": 'POST',
    'path': '/product/customStorage/uploadByCloudFileIdv2',
    'needLogin': true,
  };
  static const userUploadGoodsConfirm = {
    "method": 'POST',
    'path': '/product/customStorage/userUploadGoodsConfirmV2',
    'needLogin': true,
  };

  static const transformExcel = {"method": 'POST', "path": "/excel-java/excel/transform", "needLogin": true};

  static const updateCloudFile = {"method": 'PUT', "path": "/user/cloudFile/{id}", "needLogin": true};
}

extension FontPanelApi on HttpApi {
  /// 所有字体分类集合
  static const fontClassifyList = {
    'method': 'POST',
    'path': '/content/fontlib/classifies',
    'needLogin': false,
  };

  /// 所有字体列表
  static final fontList = {
    'baseUrl':
        Application.appEnv != AppEnv.production ? 'https://oss-print-fat.jc-test.cn' : 'https://oss-print.niimbot.com',
    'method': 'GET',
    'path': '/public_resources/static_resources/font.json',
    'needLogin': false,
  };
}

extension RiskShieldApi on HttpApi {
  /// 风险监测
  static const riskCheck = {
    'method': 'POST',
    'path': '/system/shieldReflex',
    'needLogin': false,
  };
}

extension VipTrialApi on HttpApi {
  /// vip试用活动权益
  static const privilege = {
    'method': 'GET',
    'path': '/user/privilege',
    'needLogin': false,
  };

  /// 领取试用权益
  static Map<String, dynamic> probationPrivilege(String privilegeCode) {
    return {'method': 'POST', 'path': '/vip/probation/$privilegeCode', 'needLogin': true};
  }

  /// 使用权益
  static Map<String, dynamic> usePrivilege(String privilegeCode) {
    return {'method': 'PUT', 'path': '/vip/probation/use/$privilegeCode', 'needLogin': true};
  }
}

extension PrintLogApi on HttpApi {
  static var uploadPrintDataLog = {
    'method': 'POST',
    'baseUrl': Application.pbaUrl,
    'path': '/printed/record/report/secure',
    'needLogin': false
  };
}

extension PrintCountApi on HttpApi {
  //获取用户打印张数
  static var getUserPrintCount = {'method': 'GET', 'path': '/system/statistics/paperUsedQuantity', 'needLogin': true};
}

extension AdApi on HttpApi {
  static Map<String, dynamic> getPrintFinishAd = {
    "method": 'POST',
    'path': '/content/post-printing-ads',
    'needLogin': false,
  };
}

/// 小程序相关接口
extension CapApi on HttpApi {
  /**
   * 获取数据源行信息
   */
  static Map<String, dynamic> fetchDatasourceRowList(String datasourceId) {
    return {'method': 'POST', 'path': '/user-datasource/$datasourceId/rows/data', 'needLogin': true};
  }
}

/// 消息中心相关接口
extension MessageApi on HttpApi {
  /**
   * 消息列表
   */
  static const messageList = {
    'method': 'GET',
    'path': '/message/page',
    'needLogin': true,
  };

  /**
   *聚合消息列表
   */
  static const messageTabList = {
    'method': 'GET',
    'path': '/message/tab',
    'needLogin': true,
  };

  /**
   *获取消息未读数
   */
  static const messageUnreadCount = {
    'method': 'GET',
    'path': '/message/unreadCount',
    'needLogin': true,
  };

  /**
   * 更新消息已读状态
   */
  static const updateMessageStatus = {
    'method': 'PUT',
    'path': '/message/read',
    'needLogin': true,
  };

  /**
   * 一键已读
   */
  static const cleanAllMessageStatus = {
    'method': 'PUT',
    'path': '/message/clean',
    'needLogin': true,
  };

  /**
   * 批量更新消息已读状态
   */
  static Map<String, dynamic> batchUpdateMessageStatus(String category) {
    return {'method': 'PUT', 'path': '/message/read/batch?category=$category', 'needLogin': true};
  }
}

/// C1
extension C1Api on HttpApi {
  /// 扫码耗材
  static const searchByConsumableBarcode = {
    'method': 'POST',
    'path': '/labels/tube/scanBarcode',
    'needLogin': false,
  };
}

extension RFIDApi on HttpApi {
  static Map<String, dynamic> getRfidInfo = {
    "method": 'POST',
    'path': '/rfid/getRfid/V2',
    'needLogin': false,
  };
}

/// 账号信息
extension AccountApi on HttpApi {
  ///手机号换绑

  /// 验证账号信息是否相同
  static const checkPhone = {
    'method': 'POST',
    'path': '/user/check/phone',
    'needLogin': false,
  };

  /// 验证账号信息是否相同
  static const checkPhoneAndCode = {
    'method': 'POST',
    'path': '/oauth/operate/code',
    'needLogin': true,
  };

  /// 更换新手机号
  static const bindNewPhone = {
    'method': 'POST',
    'path': '/user/change/phone',
    'needLogin': true,
  };

  /// 获取一次性授权码
  static const getAuthCode = {
    'method': 'GET',
    'path': '/oauth/one-code',
    'needLogin': true,
  };

  /// 获取一次性授权码
  static const authScan = {
    'method': 'POST',
    'path': '/oauth/third-party/scan/status',
    'needLogin': true,
  };
}

/// 机器别名
extension MachineAliasApi on HttpApi {
  /// 修改机器别名
  static const modifyAlias = {
    'method': 'POST',
    'path': '/rfid/machine/alias',
    'needLogin': false,
  };

  /// 获取机器别名列表
  static const getAliasList = {
    'method': 'POST',
    'path': '/rfid/machines',
    'needLogin': false,
  };
}
