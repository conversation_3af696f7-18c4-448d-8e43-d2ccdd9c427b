//
//  JCVIPAutoRenewViewController.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/25.
//  Copyright © 2021 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCVIPAutoRenewViewController.h"

@interface JCVIPAutoRenewViewController ()
@property(nonatomic,strong) UIView *headView;
@property(nonatomic,copy) XYNormalBlock disBlock;
@end

@implementation JCVIPAutoRenewViewController

- (instancetype)initWithDismissBlock:(XYNormalBlock)block{
    self = [super init];
    if(self){
        self.disBlock = block;
    }
    return self;
}

- (void)viewDidDisappear:(BOOL)animated{
    [super viewDidDisappear:animated];
    if(self.disBlock){
        self.disBlock();
    }
}

- (void)backEvent{
    if(self.navigationController.viewControllers.count <= 1){
        [self.navigationController dismissViewControllerAnimated:YES completion:^{
            
        }];
    }else{
        [self.navigationController popViewControllerAnimated:YES];
    }
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
