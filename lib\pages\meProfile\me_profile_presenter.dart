import 'dart:convert';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:niim_login/login_plugin/utils/graphql_utils.dart';
import 'package:text/mvp/base_page_presenter.dart';
import 'package:text/mvp/base_page_state.dart';
import 'package:text/network/dio_utils.dart';
import 'package:text/network/http_api.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/log_utils.dart';
import 'package:text/utils/toast_util.dart';

import '../../application.dart';
import 'last_buy_info.dart';

class MeProfilePresenter extends BasePagePresenter<BasePageState> {
  ///0-不显示商城，1-国内商城，2-海外商城
  static int shopState = 0;
  static String shopSiteCode = "";

  static bool updateShopInfo(int state, String siteCode) {
    bool result = false;
    if (state != shopState) {
      shopState = state;
      NiimbotEventBus.getDefault().post("updateShopState");
      result = true;
    }
    shopSiteCode = siteCode;
    GraphQLUtils.sharedInstance().configCustomHeaders({"siteCode": shopSiteCode});
    return result;
  }

  Future<List> printInfoList() {
    return ToNativeMethodChannel.sharedInstance().getDeviceSeriesList();
    // return Future.value(valueList);
  }

  getDeviceSeriesList({Function? success, Function? error}) {
    DioUtils.instance.requestNetwork<Map>(Method.post, HttpApi.deviceSeriesList,
        queryParameters: null, isList: true, needLogin: false, onSuccessList: (List<Map> data) {
      if (success != null) {
        success(data);
        ToNativeMethodChannel.setNativeDeviceSeriesList(data);
      }
    }, onError: (code, message) {
      if (error != null) {
        error(code, message);
      }
    });
  }

  getExcelCount({Function? success, Function? error}) {
    DioUtils.instance.requestNetwork<Map>(Method.get, HttpApi.excelCount,
        queryParameters: null, isList: true, needLogin: false, onSuccessList: (List<Map> data) {
      if (success != null) {
        success(data);
      }
    }, onError: (code, message) {
      if (error != null) {
        error(code, message);
      }
    });
  }

  getGoodsCount({Function? success, Function? error}) {
    DioUtils.instance.requestNetwork<Map>(Method.get, HttpApi.goodsCount, queryParameters: null, needLogin: false,
        onSuccessList: (List<Map> data) {
      if (success != null) {
        success(data);
      }
    }, onError: (code, message) {
      if (error != null) {
        error(code, message);
      }
    });
  }

  getUserShopInfo({Function? success, Function? error}) {
    // DioUtils.instance
    //     .configCustomHeaders({"niimbot-user-agent": Application.agent});
    // DioUtils.instance.setAuthorization(Application.mallToken);
    DioUtils.instance.requestNetwork<Map<String, dynamic>>(Method.get, HttpApi.userShopInfo,
        queryParameters: null, isList: false, isShopApi: true, needLogin: true, onSuccess: (data) {
      if (success != null) {
        Log.d("获取商城信息成功：$data");
        success(data);
      }
    }, onError: (code, message) {
      if (error != null) {
        Log.d("获取商城信息失败：$code Message:$message");
        error(code, message);
      }
    });
  }

  getUserShopLastInfo({Function? success, Function? error}) {
    DioUtils.instance.requestNetwork<Map<String, dynamic>>(Method.post, HttpApi.userShopLastInfo,
        queryParameters: null, isList: false, isShopApi: false, needLogin: true, onSuccess: (data) {
      if (success != null) {
        Log.d("获取商城最后一单成功：$data");
        success(data);
      }
    }, onError: (code, message) {
      if (error != null) {
        Log.d("获取商城最后一单失败：$code Message:$message");
        error(code, message);
      }
    });
  }

  addGoodsToCart({Function? success, Function? error, List<GoodsList>? goodsList}) {
    String params = json.encode({"goods_list": goodsList});

    DioUtils.instance.requestNetwork<Map<String, dynamic>>(Method.post, HttpApi.addGoodsToCart,
        // queryParameters: params,
        params: params,
        isList: false,
        isShopApi: false,
        needLogin: true, onSuccess: (data) {
      if (success != null) {
        success(data);
      }
    }, onError: (code, message) {
      if (error != null) {
        error(code, message);
      }
    });
  }

  static getImLink(Map params, Function(String content) callback) {
    EasyLoading.show(dismissOnTap: true);
    String gql = '''
    query getIMLink(\$input: ImBusinessInput!) {
      getIMLink( input: \$input )
    }
    ''';
    bool isLogin = Application.user != null;
    GraphQLUtils.sharedInstance().configCustomHeaders({"languageCode": Application.currentAppLanguageType});
    GraphQLUtils.sharedInstance()
        .query(gql,
            variables: {'input': params}, authorization: isLogin, headers: {"niimbot-user-agent": Application.agent})
        .then((QueryResultWrapper wrapper) {
      // view?.closeProgress();
      EasyLoading.dismiss();

      QueryResult value = wrapper.queryResult;
      if (!value.hasException && value.data != null) {
        String? content = value.data!['getIMLink'];
        if (content != null) {
          callback(content);
        }
      } else {
        if (wrapper.error.isNotEmpty) {
          showToast(msg: wrapper.error);
        }
      }
    });
  }

  void requestImLink(Map params, Function(String content) callback) {
    // view?.showProgress();
    getImLink(params, callback);
  }

  getNpWebUrl({Function? success, String? languageCode}) {
    String langeType = languageCode ?? Application.currentAppLanguageType;
    String token = Application.token;
    String hardwareName = Application.printer?.name ?? "";
    Map<String, String>? data = {};
    if (Application.agent.length > 0) {
      data["niimbotUserAgent"] = Uri.encodeComponent(Application.agent);
    }
    if (token.length > 0) {
      data["token"] = Uri.encodeComponent(token);
    }
    if (hardwareName.length > 0) {
      data["hardwareName"] = Uri.encodeComponent(hardwareName);
    }
    if (langeType.length > 0) {
      data["languageCode"] = Uri.encodeComponent(langeType);
    }
    String webUrl = get(Application.npsH5Url, params: data); //https://n.jc-test.cn/nps
    String npsUniAppH5Url = get(Application.npsUniAppH5Url, params: data);
    success!({"url": webUrl, "uniAppUrl": npsUniAppH5Url});
  }

  getNpVipWebUrl({Function? success, String? languageCode}) {
    String langeType = languageCode ?? Application.currentAppLanguageType;
    String token = Application.token;
    String hardwareName = Application.printer?.name ?? "";
    Map<String, String>? data = {};
    data["source"] = "4";
    if (Application.agent.length > 0) {
      data["niimbotUserAgent"] = Uri.encodeComponent(Application.agent);
    }
    if (token.length > 0) {
      data["token"] = Uri.encodeComponent(token);
    }
    if (hardwareName.length > 0) {
      data["hardwareName"] = Uri.encodeComponent(hardwareName);
    }
    if (langeType.length > 0) {
      data["languageCode"] = Uri.encodeComponent(langeType);
    }
    String webUrl = get(Application.npsVipH5Url, params: data); //https://n.jc-test.cn/niimbot/vip-nps
    success!({"url": webUrl});
  }

  String get(String url, {Map<String, String>? params}) {
    if (params != null && params.isNotEmpty) {
      // 如果参数不为空，则将参数拼接到URL后面
      StringBuffer sb = StringBuffer("?");
      params.forEach((key, value) {
        sb.write("$key" + "=" + "$value" + "&");
      });
      String paramStr = sb.toString();
      paramStr = paramStr.substring(0, paramStr.length - 1);
      url += paramStr;
    }
    return url;
  }

  getNpsInfo({Function? success, Function? error, String? languageCode}) {
    if (Application.networkConnected == false) {
      error!(intlanguage("app100000354", "网络异常"));
      showToast(msg: intlanguage("app100000354", "网络异常")); //intlanguage("app100000354", "网络异常")
      return;
    }

    String gql = '''
       query FindNpsEvaluation {
  findNpsEvaluation {
     scoreNpsEvaluations {
      nodeId
      rootEvaluations {
        desc
        evaluationNodeDesc
        evaluationOptions {
          canInput
          desc
          nodeId
        }
        evaluationNodeId
        inputLimit
        nodeId
      }
      scores
      title
      retainDesc
      remarkDesc
    }
    retainTitleDesc
    thankStatementDesc
    title
  }
}
''';
    bool isLogin = Application.user != null;
    String langeType = languageCode ?? Application.currentAppLanguageType;
    GraphQLUtils.sharedInstance().setAuthorization(Application.token);
    GraphQLUtils.sharedInstance().configCustomHeaders(
        {"niimbot-user-agent": Application.agent, "languageCode": langeType, "countryCode": Application.deviceRegion});
    // print(Application.currentAppLanguageType);
    GraphQLUtils.sharedInstance()
        .query(gql,
            variables: {},
            authorization: isLogin,
            headers: {
              "niimbot-user-agent": Application.agent,
              "languageCode": langeType,
              "countryCode": Application.deviceRegion
            })
        .then((QueryResultWrapper wrapper) {
      var result = wrapper.queryResult;
      if (!result.hasException && result.data != null) {
        Map arguments = result.data!["findNpsEvaluation"];

        if (arguments.isNotEmpty) {
          success!(arguments);
        }
      } else {
        if (error != null) {
          if (wrapper.error.isNotEmpty) {
            showToast(msg: wrapper.error);
          }
          error(result);
        }
      }
    });
  }
}
