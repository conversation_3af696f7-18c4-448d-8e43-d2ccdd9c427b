//
//  JCElementModel+Transfer.m
//  Runner
//
//  Created by xingling xu on 2020/3/25.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCElementModel+Transfer.h"
#import "JCExcelForElement.h"
#import "JCGoodDetailInfo.h"
#import "JCTMDataBindGoodsInfoManager.h"
#import "JCExcelTransUtil.h"

#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"

#define SameJCPorpertyNumber(property)   [[NSString stringWithFormat:@"%.1f",self.property] isEqualToString:[NSString stringWithFormat:@"%.1f",anotherModel.property]]
#define SameFloat(property)   fabs(self.property - anotherModel.property) <= 2
//([[NSString stringWithFormat:@"%.0f",self.property] isEqualToString:[NSString stringWithFormat:@"%.0f",anotherModel.property]])
#define SameInteger(property)   (self.property == anotherModel.property)
#define SameString(property)    ([self.property isEqualToString:anotherModel.property])
#define SameArray(property)     ([self.property isEqualToArray:anotherModel.property])

/** 字符串属性 */
#define dict_set_key_value_string(key,string)   if (string){[dict setObject:string forKey:key];}
/** 字符串属性 */
#define dict_set_key_value_object(key,object)   if (object){[dict setObject:object forKey:key];}
/** 数字类型属性 */
#define dict_set_key_value_number(key,number)   [dict setObject:@(number) forKey:key]
/** bool属性转换 */
#define dict_set_key_value_bool(key,boolValue)  [dict setObject:boolValue?@(1):@(0) forKey:key]

NSString *const JCElementModel2SdkPropertyKey       = @"JCElementModel2SdkPropertyKey";
NSString *const JCElementData2CachePropertyKey      = @"JCElementData2CachePropertyKey";
NSString *const JCElementModelShowPlaceHolder       = @"JCElementModelShowPlaceHolder";
NSString *const KJCElementModelCurrentPageIndex     = @"KJCElementModelCurrentPageIndex";
NSString *const KJCElementModelVirtualPageIndex     = @"KJCElementModelVirtualPageIndex";
NSString *const KJCElementModelIsPrintPageOnly      = @"KJCElementModelIsPrintPageOnly";
NSString *const KJCElementModelExternalData         = @"KJCElementModelExternalData";
NSString *const KJCElementModelGoodsData            = @"KJCElementModelGoodsData";
NSString *const KJCElementModelTask                 = @"KJCElementModelTask";
NSString *const KJCElementModelCurrentCopyNumber    = @"KJCElementModelCurrentCopyNumber";
NSString *const KJCElementTotalCopyNumber           = @"KJCElementTotalCopyNumber";
NSString *const KJCElementModelModify               = @"KJCElementModelModify";
NSString *const KJCElementModelDataSource           = @"KJCElementModelDataSource";


@implementation JCElementModel (Transfer)

- (NSDictionary *)elementDict {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    [dict addEntriesFromDictionary:[self commonProperty]];
    NSString *type = self.type;
    NSAssert(type.length > 0, @"type must not nil");
    if ([type isEqualToString:@"text"]) {
        [dict addEntriesFromDictionary:[self textSeparateProperty]];
    } else if ([type isEqualToString:@"date"]) {
        [dict addEntriesFromDictionary:[self dateSeparateProperty]];
    } else if ([type isEqualToString:@"serial"]) {
        [dict addEntriesFromDictionary:[self serialSeparateProperty]];
    } else if ([type isEqualToString:@"barcode"]) {
        [dict addEntriesFromDictionary:[self barcodeSeparateProperty]];
    } else if ([type isEqualToString:@"qrcode"]) {
        [dict addEntriesFromDictionary:[self qrcodeSeparateProperty]];
    } else if ([type isEqualToString:@"line"]) {
        [dict addEntriesFromDictionary:[self lineSeparateProperty]];
    } else if ([type isEqualToString:@"graph"]) {
        [dict addEntriesFromDictionary:[self graphSeparateProperty]];
    } else if ([type isEqualToString:@"table"]) {
        [dict addEntriesFromDictionary:[self tableSeparateProperty]];
    } else if ([type isEqualToString:@"image"]) {
        [dict addEntriesFromDictionary:[self imageSeparateProperty]];
    }
    return dict;
}

/** 元素公共属性 */
- (NSDictionary *)commonProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:9];
    dict_set_key_value_string(@"id",self.elementId);
    dict_set_key_value_string(@"type",self.type);
    dict_set_key_value_number(@"x",self.x);
    dict_set_key_value_number(@"y",self.y);
    dict_set_key_value_number(@"width",self.width);
    dict_set_key_value_number(@"height",self.height);
    dict_set_key_value_number(@"rotate",self.rotate);
    dict_set_key_value_number(@"zIndex",self.zIndex);
    dict_set_key_value_number(@"colorReverse",self.colorReverse);
    dict_set_key_value_number(@"paperColorIndex",self.paperColorIndex);
    dict_set_key_value_number(@"colorChannel",self.paperColorIndex);
    dict_set_key_value_bool(@"isTitle", self.isTitle);
    dict_set_key_value_bool(@"isOpenMirror", self.isOpenMirror);
    dict_set_key_value_string(@"mirrorId",self.mirrorId);
    dict_set_key_value_string(@"elementVersion",self.elementVersion);
    if(JC_IS_CONNECTED_PRINTER && [JCBluetoothManager sharedInstance].multipleColors.count > 1){

    }else{
        if (![self.type isEqualToString:@"table"]) {
        }
    }
    if (![self.type isEqualToString:@"table"]) {
        if(JC_IS_CONNECTED_PRINTER && [JCBluetoothManager sharedInstance].multipleColors.count > 1){
            NSString *colorStr = [[JCBluetoothManager sharedInstance].multipleColors safeObjectAtIndex:self.paperColorIndex];
            if(!STR_IS_NIL(colorStr)){
                NSMutableArray *elementColor = [NSMutableArray array];
                [elementColor addObject:@255];
                for (NSString *colorValue in [colorStr componentsSeparatedByString:@"."]) {
                    [elementColor addObject:@(colorValue.integerValue)];
                }
                dict_set_key_value_object(@"elementColor",elementColor);
            }else{
                dict_set_key_value_object(@"elementColor",self.elementColor);
            }
        }else{
            dict_set_key_value_object(@"elementColor",self.elementColor);
        }
    }else{
        if(JC_IS_CONNECTED_PRINTER && [JCBluetoothManager sharedInstance].multipleColors.count > 1){
            NSString *lineColorStr = [[JCBluetoothManager sharedInstance].multipleColors safeObjectAtIndex:self.lineColorChannel];
            NSMutableArray *lineColor = [NSMutableArray array];
            [lineColor addObject:@255];
            for (NSString *colorValue in [lineColorStr componentsSeparatedByString:@"."]) {
                [lineColor addObject:@(colorValue.integerValue)];
            }
            NSString *contentColorStr = [[JCBluetoothManager sharedInstance].multipleColors safeObjectAtIndex:self.contentColorChannel];
            NSMutableArray *contentColor = [NSMutableArray array];
            [contentColor addObject:@255];
            for (NSString *colorValue in [contentColorStr componentsSeparatedByString:@"."]) {
                [contentColor addObject:@(colorValue.integerValue)];
            }
            dict_set_key_value_object(@"lineColor",lineColor);
            dict_set_key_value_object(@"contentColor",contentColor);
        }
    }
    if (![self forSdk]) {
        dict_set_key_value_bool(@"isLock",self.isLock);
        dict_set_key_value_string(@"fieldName", self.fieldName);
    }

    return dict;
}

/** 文本单独属性 */
- (NSDictionary *)textSeparateProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:10];
    dict_set_key_value_string(@"value",ServerTextValue(self.value));
    dict_set_key_value_string(@"contentTitle",self.contentTitle);
    if ([self forSdk]) {
        dict_set_key_value_string(@"fontFamily",self.fontCode);
    } else {
        dict_set_key_value_string(@"fontFamily",self.fontFamily);
    }
    dict_set_key_value_string(@"fontCode",self.fontCode);
    dict_set_key_value_number(@"lineMode",self.lineMode);
    dict_set_key_value_number(@"wordSpacing",self.letterSpacing);
    dict_set_key_value_number(@"lineBreakMode",self.lineBreakMode);
    dict_set_key_value_number(@"typesettingMode",self.typesettingMode);
    if ([self forSdk]) {
        if (!STR_IS_NIL(self.fieldName) && STR_IS_NIL(self.value) && [self getShowPlaceHolder] && [self getGoodsData].count == 0) {
            if(!STR_IS_NIL(self.contentTitle)){
                NSString *valueString = [NSString stringWithFormat:@"%@:%@",[JCTMDataBindGoodsInfoManager getPlaceHolderValueWithFieldName:self.fieldName],[JCTMDataBindGoodsInfoManager getDrawboardPlaceHolderValueWithFieldName:self.fieldName]];
                dict_set_key_value_string(@"value",valueString);
            }else{
                dict_set_key_value_string(@"value",[JCTMDataBindGoodsInfoManager getDrawboardPlaceHolderValueWithFieldName:self.fieldName]);
            }
        } else {
            NSString *content = [self elementValue:self.value withExcelIndex:[self getCurrentPageIndex] elementId:self.elementId isNeedTitle:true];
            dict_set_key_value_string(@"value",UN_NIL(content));
        }
        dict_set_key_value_number(@"letterSpacing",self.letterSpacing);
        dict_set_key_value_number(@"lineSpacing",self.lineSpacing);
        dict_set_key_value_number(@"fontSize",self.fontSize);
    } else {
        dict_set_key_value_number(@"fontSize",[([NSString stringWithFormat:@"%.1f",self.fontSize]) floatValue]);
        dict_set_key_value_number(@"letterSpacing",[([NSString stringWithFormat:@"%.1f",self.letterSpacing]) floatValue]);
        dict_set_key_value_number(@"lineSpacing",[([NSString stringWithFormat:@"%.1f",self.lineSpacing]) floatValue]);
    }
    dict_set_key_value_number(@"textAlignHorizonral",self.textAlignHorizonral);
    dict_set_key_value_number(@"textAlignVertical",self.textAlignVertical);
    dict_set_key_value_object(@"fontStyle",self.fontStyle);
    dict_set_key_value_object(@"typesettingParam",self.typesettingParam);
    dict_set_key_value_object(@"dataBind",self.dataBind);
    dict_set_key_value_string(@"boxStyle",self.boxStyle);
    dict_set_key_value_object(@"textStyle",self.textStyle);

    return dict;
}

/** 流水号单独属性 */
- (NSDictionary *)serialSeparateProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:16];
    [dict addEntriesFromDictionary:[self textSeparateProperty]];
    if (![self forSdk]) {
        dict_set_key_value_string(@"prefix",self.prefix);
        dict_set_key_value_string(@"suffix",self.suffix);
        dict_set_key_value_string(@"startNumber",self.startNumber);
        dict_set_key_value_number(@"fixLength",self.fixLength);
        dict_set_key_value_string(@"fixValue",self.fixValue);
        dict_set_key_value_number(@"incrementValue",self.incrementValue);
    } else {
        NSInteger totalCopyNumber = [self getTotalCopyNumber];
        NSInteger currentCopyNumber = [self getCurrentCopyNumber];
        NSInteger virtualPageIndex = [self getVirtualPageIndex];
        NSInteger incrementValue = self.incrementValue;
        NSInteger beginNumber;
        if (totalCopyNumber <= 1) {
            // 只有单份，按照当前的序号打印
            beginNumber = self.startNumber.integerValue + virtualPageIndex * incrementValue;
        } else {
            // 存在多份
            // 区分单份多次、起始页-终止页轮循几次
            // 只有序列号元素的时候是0011，其余均是0101，按组来区分
            if([self getPrintPageOnly]){
                beginNumber = self.startNumber.integerValue + virtualPageIndex * incrementValue;
            } else {
                // TODO: 初始值 + 当前起始页值 + page多份值
                beginNumber = self.startNumber.integerValue + virtualPageIndex * incrementValue;
            }
        }
        NSString *text = StringFromInt(beginNumber);
        if(STR_IS_NIL(self.startNumber)){
            text = @"";
            NSString *prefix = self.prefix;
            if (prefix && prefix.length > 0) {
                text = [NSString stringWithFormat:@"%@%@",prefix,text];
            }
            NSString *suffix = self.suffix;
            if (suffix && suffix.length > 0) {
                text = [NSString stringWithFormat:@"%@%@",text,suffix];
            }
        }else{
            NSMutableString *string = [NSMutableString string];
            if (text.length < self.fixLength) {
                NSInteger number = self.fixLength - text.length;
                for (NSInteger i = 0; i < number; i ++) {
                    [string appendString:@"0"];
                }
                self.fixValue = string;
            }else if(text.length < self.startNumber.length){
                NSInteger number = self.startNumber.length - text.length;
                for (NSInteger i = 0; i < number; i ++) {
                    [string appendString:@"0"];
                }
                self.fixValue = string;
            }
            NSLog(@"self.fixValue ---------------------- %@",self.fixValue);
            if (!STR_IS_NIL(string)) {// 补0操作
                text = [NSString stringWithFormat:@"%@%@",string,text];
            }
            NSLog(@"self.fixValue -----------text-------1----------- %@",text);
            NSString *prefix = self.prefix;
            if (prefix && prefix.length > 0) {
                text = [NSString stringWithFormat:@"%@%@",prefix,text];
            }
            NSLog(@"self.fixValue -----------text-------2----------- %@",text);
            NSString *suffix = self.suffix;
            if (suffix && suffix.length > 0) {
                text = [NSString stringWithFormat:@"%@%@",text,suffix];
            }
            NSLog(@"self.fixValue -----------text-------3----------- %@",text);
        }
        dict_set_key_value_string(@"value",UN_NIL(text));
    }
    dict_set_key_value_string(@"boxStyle",self.boxStyle);
    dict_set_key_value_object(@"textStyle",self.textStyle);
    return dict;
}

/** 日期单独属性 */
- (NSDictionary *)dateSeparateProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:13];
    [dict addEntriesFromDictionary:[self textSeparateProperty]];
    dict_set_key_value_string(@"dateFormat",self.dateFormat);
    dict_set_key_value_string(@"timeFormat",self.timeFormat);
    dict_set_key_value_bool(@"dateIsRefresh", self.dateIsRefresh);
    dict_set_key_value_bool(@"associated", self.associated);
    dict_set_key_value_bool(@"timeOpen", self.timeOpen);
    dict_set_key_value_bool(@"dateOpen", self.dateOpen);
    dict_set_key_value_number(@"timeOffset",self.timeOffset);
    dict_set_key_value_string(@"associateId",self.associateId);
    dict_set_key_value_number(@"validityPeriod",self.validityPeriod);
    dict_set_key_value_number(@"validityPeriodNew",self.validityPeriodNew);
    dict_set_key_value_number(@"time",self.time);
    dict_set_key_value_string(@"validityPeriodUnit",self.validityPeriodUnit);
    dict_set_key_value_string(@"timeUnit",self.timeUnit);
    // 将时间戳改为格式字符串
    NSString *value = [self getDateString];
    if(!STR_IS_NIL(self.contentTitle)){
        value = [NSString stringWithFormat:@"%@%@%@",self.contentTitle,XY_LANGUAGE_TITLE_NAMED(@"app100001507", @"："),value];
    }
    // sdk value传格式后的字符串  服务端传时间戳
    NSString *result = [self forSdk]?value:UN_NIL(self.value);
    dict_set_key_value_string(@"value", result);
    dict_set_key_value_string(@"boxStyle",self.boxStyle);
    dict_set_key_value_object(@"textStyle",self.textStyle);
    return dict;
}

/** 条码单独属性 */
- (NSDictionary *)barcodeSeparateProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:5];
    float textHeight = (self.textHeight > (float)17/8)?self.textHeight:(float)17/8;
    dict_set_key_value_string(@"value",ServerTextValue(self.value));
    if ([self forSdk]) {
        NSString *content = [self elementValue:self.value withExcelIndex:[self getCurrentPageIndex] elementId:self.elementId isNeedTitle:false];
        dict_set_key_value_string(@"value", UN_NIL(content));
    }
    dict_set_key_value_number(@"textPosition",self.textPosition);
    dict_set_key_value_number(@"codeType",self.codeType);
    dict_set_key_value_number(@"fontSize",self.fontSize);
    dict_set_key_value_number(@"textHeight",textHeight);
    dict_set_key_value_object(@"dataBind",self.dataBind);
    return dict;
}

/** 二维码单独属性 */
- (NSDictionary *)qrcodeSeparateProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:3];
    if ([self forSdk]) {
        NSString *value = [self elementValue:self.value withExcelIndex:[self getCurrentPageIndex] elementId:self.elementId isNeedTitle:false];
        dict_set_key_value_string(@"value",STR_IS_NIL(value)?@"0":UN_NIL(value));
    } else {
        dict_set_key_value_string(@"value",ServerTextValue(self.value));
    }
    dict_set_key_value_number(@"correctLevel",self.correctLevel);
    dict_set_key_value_number(@"codeType",self.codeType);
    dict_set_key_value_bool(@"isLive", self.isLive);
    dict_set_key_value_bool(@"isForm", self.isForm);
    dict_set_key_value_string(@"liveCodeId",ServerTextValue(self.liveCodeId));
    dict_set_key_value_string(@"formId",ServerTextValue(self.formId));
    if (self.codeType != JCCodeType_PDF417 && self.height != self.width) {
        dict_set_key_value_number(@"height",MIN(self.width, self.height));
    }
    dict_set_key_value_object(@"dataBind",self.dataBind);
    return dict;
}

/** 线条单独属性 */
- (NSDictionary *)lineSeparateProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:2];
    if ([self forSdk]) {
        dict_set_key_value_number(@"lineWidth",self.lineWidth);
        dict_set_key_value_number(@"height",self.lineWidth);
    } else {
        dict_set_key_value_number(@"lineWidth",[([NSString stringWithFormat:@"%.1f",self.lineWidth]) floatValue]);
    }
    dict_set_key_value_object(@"dashwidth", self.dashwidth);
    dict_set_key_value_number(@"lineType",self.lineType);
    return dict;
}

/** 几何图形单独属性 */
- (NSDictionary *)graphSeparateProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:3];
    dict_set_key_value_number(@"graphType",self.graphType);
    dict_set_key_value_number(@"lineType",self.lineType);
    if ([self forSdk]) {
        dict_set_key_value_number(@"lineWidth",self.lineWidth);
    } else {
        dict_set_key_value_number(@"lineWidth",[([NSString stringWithFormat:@"%.1f",self.lineWidth]) floatValue]);
    }
    if (self.dashwidth.count == 0) {
        CGFloat dsw = dash_line_default_width;
        NSArray *widths = @[@(dsw),@(dsw)];
        dict_set_key_value_object(@"dashwidth",widths);
    } else {
        dict_set_key_value_object(@"dashwidth",self.dashwidth);
    }
    CGFloat cornerRadius = self.cornerRadius;
    if ((self.graphType ==  JCGraphTypeRectRound && self.cornerRadius*2 > self.width) || self.cornerRadius * 2 > self.height) {
        cornerRadius = MIN(self.width/2, self.height/2)-0.1;
    }
    dict_set_key_value_number(@"cornerRadius",cornerRadius);
    return dict;
}

/** 图片单独属性 */
- (NSDictionary *)imageSeparateProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:4];
    // 点九图使用元素ID、普通的按照边框ID，没有则使用元素ID
    NSString *imageId = self.isNinePatch ? self.elementId : (STR_IS_NIL(self.materialId)?self.elementId:self.materialId);
    NSString *localPath = ElementLocalPath(imageId, NO);
    if ([self forSdk] || [self forCache]) {// 仅针对sdk提供 imageData 参数  服务端只保存url
        NSData *data = [NSData dataWithContentsOfFile:self.localUrl];
        if(data == nil){
            if(!STR_IS_NIL(self.localUrl)){
                NSData *dataLocal = [NSData dataWithContentsOfFile:localPath];
                [self saveLocalImagePathWithData:dataLocal localPath:localPath];
                self.localUrl = localPath;
            }
            NSLog(@"图片不存在");
            NSString * emptyString = @"";
            dict_set_key_value_string(@"imageData",emptyString);
        }else{
//            [self saveLocalImagePathWithData:data localPath:localPath];
            NSString * emptyString = @"";
            dict_set_key_value_string(@"imageData",emptyString);
//            self.localUrl = localPath;

        }
    }

    dict_set_key_value_string(@"imageUrl",UN_NIL(self.imageUrl));
    dict_set_key_value_string(@"localUrl",self.localUrl);
    dict_set_key_value_bool(@"isNinePatch",self.isNinePatch);
    dict_set_key_value_string(@"ninePatchLocalUrl",self.ninePatchLocalUrl);
    dict_set_key_value_string(@"ninePatchUrl",UN_NIL(self.ninePatchUrl));
    dict_set_key_value_string(@"localImageUrl",self.localUrl);
    dict_set_key_value_number(@"imageProcessingType",self.imageProcessingType);
    dict_set_key_value_object(@"imageProcessingValue", self.imageProcessingValue);
    dict_set_key_value_string(@"materialId",self.materialId);
    dict_set_key_value_string(@"materialType",self.materialType);
    return dict;
}

-(NSString *)saveLocalImagePathWithData:(NSData *)imageData localPath:(NSString *)localPath{
    NSArray *pathArr =  @[RESOURCE_IMAGE_THUMB_BACK_PATH,RESOURCE_IMAGE_CLOILD_THUMB_BACK_PATH,RESOURCE_IMAGE_CLOILD_THUMB_BACK_PATH,RESOURCE_ELEMENT_PATH];
       [pathArr enumerateObjectsUsingBlock:^(NSString  *rootPath, NSUInteger idx, BOOL * _Nonnull stop) {
           if(![[NSFileManager defaultManager] fileExistsAtPath:rootPath]) {
               [[NSFileManager defaultManager] createDirectoryAtPath:rootPath withIntermediateDirectories:YES attributes:nil error:nil];
           }
       }];
    //NSString * labelId = imageId;
   // NSString * pathKey = [NSString stringWithFormat:@"%@%ld%ld",labelId,self.multipleBackIndex,self.rotate];
    NSString * path = localPath;
//    if (![[NSFileManager defaultManager] fileExistsAtPath:path]) {
       [imageData writeToFile:path atomically:YES];
//    }
    return path;
}

/** 表格单独属性 */
- (NSDictionary *)tableSeparateProperty {
    XYWeakSelf
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:6];
    dict_set_key_value_number(@"imageProcessingType",self.imageProcessingType);
    dict_set_key_value_number(@"row",self.row);
    dict_set_key_value_number(@"column",self.column);
    dict_set_key_value_object(@"rowHeight", self.rowHeight);
    dict_set_key_value_object(@"columnWidth", self.columnWidth);
    dict_set_key_value_object(@"lineColor", self.lineColor);
    dict_set_key_value_object(@"contentColor", self.contentColor);
    dict_set_key_value_number(@"lineColorChannel", self.lineColorChannel);
    dict_set_key_value_number(@"contentColorChannel", self.contentColorChannel);
    dict_set_key_value_string(@"combineId",self.combineId);
    if ([self forSdk]) {
        dict_set_key_value_number(@"lineWidth",self.lineWidth);
    } else {
        dict_set_key_value_number(@"lineWidth",[([NSString stringWithFormat:@"%.1f",self.lineWidth]) floatValue]);
    }
    dict_set_key_value_number(@"lineType",self.lineType);
    NSMutableArray *cells = [NSMutableArray arrayWithCapacity:self.cells.count];
    [self.cells enumerateObjectsUsingBlock:^(JCElementModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        // 如果表格是往sdk传，那么内部的文本也往sdk转
        [obj injectExternalData:[weakSelf getExternalData] task:[weakSelf getTask] modify:[weakSelf getModifyData] dataSource:[weakSelf getDataSource]];
        // 配置sdk与否
        obj = obj.toSdk([weakSelf forSdk]);
        // 配置当前excel分页
        obj = obj.setCurrentPageIndex([weakSelf getCurrentPageIndex]);

        NSDictionary *temp = [weakSelf sigleGridProperty:obj];
        [cells addObject:temp];
    }];
    dict_set_key_value_object(@"cells", cells);
    NSMutableArray *combineCells = [NSMutableArray arrayWithCapacity:self.combineCells.count];
    [self.combineCells enumerateObjectsUsingBlock:^(JCElementModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        // 如果表格是往sdk传，那么内部的文本也往sdk转
        [obj injectExternalData:[weakSelf getExternalData] task:[weakSelf getTask] modify:[weakSelf getModifyData] dataSource:[weakSelf getDataSource]];
        // 配置sdk与否
        obj = obj.toSdk([weakSelf forSdk]);
        // 配置当前excel分页
        obj = obj.setCurrentPageIndex([weakSelf getCurrentPageIndex]);
        NSDictionary *temp = [weakSelf combineGridProperty:obj];
        [combineCells addObject:temp];
    }];
    dict_set_key_value_object(@"combineCells", combineCells);
    dict_set_key_value_object(@"dataBind", self.dataBind);
    return dict;
}

/** 单独的单元格文本信息 */
- (NSDictionary *)sigleGridProperty:(JCElementModel *)cellModel {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:14];
    dict_set_key_value_string(@"value",ServerTextValue(cellModel.value));
    NSString *type = @"text";
    dict_set_key_value_string(@"type",type);
    if ([cellModel forSdk]) {
        dict_set_key_value_string(@"fontFamily",cellModel.fontCode);
    } else {
        dict_set_key_value_string(@"fontFamily",cellModel.fontFamily);
    }
    dict_set_key_value_string(@"fontCode",cellModel.fontCode);
    dict_set_key_value_number(@"lineMode",cellModel.lineMode);
    dict_set_key_value_number(@"wordSpacing",cellModel.wordSpacing);
    dict_set_key_value_number(@"lineBreakMode",cellModel.lineBreakMode);
    dict_set_key_value_number(@"letterSpacing",cellModel.letterSpacing);
    dict_set_key_value_number(@"lineSpacing",cellModel.lineSpacing);
    dict_set_key_value_number(@"typesettingMode",cellModel.typesettingMode);
    dict_set_key_value_object(@"elementColor",cellModel.elementColor);
    dict_set_key_value_number(@"paperColorIndex",cellModel.paperColorIndex);
    dict_set_key_value_number(@"colorChannel",cellModel.colorChannel);
    dict_set_key_value_string(@"contentTitle",cellModel.contentTitle);
    if ([cellModel forSdk]) {
        NSString *content = [self elementValue:cellModel.value withExcelIndex:[cellModel getCurrentPageIndex] elementId:cellModel.elementId isNeedTitle:true];
        //新结构的数据title会在 [self elementValue:cellModel.value。。统一赋值
//        if(!STR_IS_NIL(cellModel.contentTitle) && ([self getModifyData] == nil || [self  getModifyData].allKeys.count == 0)){
//            NSString *titleString = cellModel.contentTitle.length > 100?[cellModel.contentTitle substringToIndex:100]:cellModel.contentTitle;
//            content = [NSString stringWithFormat:@"%@：%@",titleString,content];
//        }
        dict_set_key_value_string(@"value",UN_NIL(content));
        dict_set_key_value_number(@"fontSize",cellModel.fontSize);
    } else {
        dict_set_key_value_number(@"fontSize",cellModel.fontSize);
    }
    dict_set_key_value_number(@"textAlignHorizonral",cellModel.textAlignHorizonral);
    dict_set_key_value_number(@"textAlignVertical",cellModel.textAlignVertical);
    dict_set_key_value_object(@"fontStyle",cellModel.fontStyle);
    dict_set_key_value_object(@"typesettingParam",cellModel.typesettingParam);
    dict_set_key_value_string(@"id", cellModel.elementId);
    dict_set_key_value_string(@"combineId",cellModel.combineId);
    dict_set_key_value_number(@"rowIndex",cellModel.rowIndex);
    dict_set_key_value_number(@"columnIndex",cellModel.columnIndex);
    dict_set_key_value_object(@"dataBind", cellModel.dataBind);
    return dict;
}

/** 合并单元格信息 */
- (NSDictionary *)combineGridProperty:(JCElementModel *)cellModel {
    return [self sigleGridProperty:cellModel];
}

#pragma mark - sdk数据同服务端数据区分
- (JCElementModel *(^)(BOOL transfer2sdk))toSdk {
    return ^(BOOL transfer2sdk){
        objc_setAssociatedObject(self, &JCElementModel2SdkPropertyKey, @(transfer2sdk), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (BOOL)forSdk {
    id object = objc_getAssociatedObject(self, &JCElementModel2SdkPropertyKey);
    return [object integerValue] > 0 ? YES:NO;
}

- (JCElementModel *(^)(BOOL transfer2cache))toCache {
    return ^(BOOL transfer2cache){
        objc_setAssociatedObject(self, &JCElementData2CachePropertyKey, @(transfer2cache), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (BOOL)forCache {
    id object = objc_getAssociatedObject(self, &JCElementData2CachePropertyKey);
    return [object integerValue] > 0 ? YES:NO;
}

- (JCElementModel *(^)(BOOL showHolder))showPlaceHolder {
    return ^(BOOL showHolder){
        objc_setAssociatedObject(self, &JCElementModelShowPlaceHolder, @(showHolder), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (BOOL)getShowPlaceHolder {
    id object = objc_getAssociatedObject(self, &JCElementModelShowPlaceHolder);
    return [object integerValue] > 0 ? YES:NO;
}

#pragma mark - Excel数据相关
- (void)injectExternalData:(NSDictionary *)externalData task:(NSDictionary *)task modify:(NSDictionary *)modify dataSource:(NSArray <NSDictionary *> *)dataSource{
    if (externalData) {
        objc_setAssociatedObject(self, &KJCElementModelExternalData, externalData, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
    if (task) {
        objc_setAssociatedObject(self, &KJCElementModelTask, task, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
    if (modify) {
        objc_setAssociatedObject(self, &KJCElementModelModify, modify, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
    if([dataSource isKindOfClass:[NSArray class]] && dataSource.count > 0){
        objc_setAssociatedObject(self, &KJCElementModelDataSource, dataSource, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
}

-(NSArray <NSDictionary *>*)getDataSource{
    NSArray <NSDictionary *> * object = objc_getAssociatedObject(self, &KJCElementModelDataSource);
    if (object && [object isKindOfClass:[NSArray class]]) {
        return object;
    }
    return nil;
}

- (NSDictionary *)getExternalData {
    NSDictionary *object = objc_getAssociatedObject(self, &KJCElementModelExternalData);
    if (object && [object isKindOfClass:[NSDictionary class]]) {
        return object;
    }
    return nil;
}

- (NSDictionary *)getModifyData {
    NSDictionary *object = objc_getAssociatedObject(self, &KJCElementModelModify);
    if (object && [object isKindOfClass:[NSDictionary class]]) {
        return object;
    }
    return nil;
}

- (NSDictionary *)getTask {
    NSDictionary *object = objc_getAssociatedObject(self, &KJCElementModelTask);
    if (object && [object isKindOfClass:[NSDictionary class]]) {
        return object;
    }
    return nil;
}

- (void)injectGoodsData:(NSArray *)goodsArr{
    if (goodsArr) {
        objc_setAssociatedObject(self, &KJCElementModelGoodsData, goodsArr, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
}

- (NSArray *)getGoodsData {
    NSArray *object = objc_getAssociatedObject(self, &KJCElementModelGoodsData);
    if (object && [object isKindOfClass:[NSArray class]]) {
        return object;
    }
    return nil;
}

- (JCElementModel *(^)(NSInteger currentIndex))setCurrentPageIndex {
    return ^(NSInteger currentIndex){
        objc_setAssociatedObject(self, &KJCElementModelCurrentPageIndex, @(currentIndex), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (NSInteger)getCurrentPageIndex {
    id object = objc_getAssociatedObject(self, &KJCElementModelCurrentPageIndex);
    return [object integerValue];
}

- (JCElementModel *(^)(NSInteger currentIndex))setVirtualPageIndex {
    return ^(NSInteger currentIndex){
        objc_setAssociatedObject(self, &KJCElementModelVirtualPageIndex, @(currentIndex), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (NSInteger)getVirtualPageIndex {
    id object = objc_getAssociatedObject(self, &KJCElementModelVirtualPageIndex);
    if (object == nil) {
        return self.getCurrentPageIndex;
    } else {
        return [object integerValue];
    }
}

- (JCElementModel *(^)(BOOL isPrintPageOnly))setPrintPageOnly{
    return ^(BOOL isPrintPageOnly){
        objc_setAssociatedObject(self, &KJCElementModelIsPrintPageOnly, @(isPrintPageOnly), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (BOOL)getPrintPageOnly{
    id object = objc_getAssociatedObject(self, &KJCElementModelIsPrintPageOnly);
    return [object boolValue];
}

- (JCElementModel *(^)(NSInteger copyNumber))setCurrentCopyNumber {
    return ^(NSInteger copyNumber){
        objc_setAssociatedObject(self, &KJCElementModelCurrentCopyNumber, @(copyNumber), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (NSInteger)getCurrentCopyNumber {
    id object = objc_getAssociatedObject(self, &KJCElementModelCurrentCopyNumber);
    return [object integerValue];
}

- (JCElementModel *(^)(NSInteger copyNumber))setTotalCopyNumber {
    return ^(NSInteger copyNumber){
        objc_setAssociatedObject(self, &KJCElementTotalCopyNumber, @(copyNumber), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (NSInteger)getTotalCopyNumber {
    id object = objc_getAssociatedObject(self, &KJCElementTotalCopyNumber);
    return [object integerValue];
}

- (BOOL)hasExcelImport {
    __block BOOL value = NO;
    if (self.templateType == JCTemplateType_Table) {
        [self.cells enumerateObjectsUsingBlock:^(JCElementModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (IsExcelString(obj.value)) {
                value = YES;
                *stop = YES;
            }
        }];
        [self.combineCells enumerateObjectsUsingBlock:^(JCElementModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (IsExcelString(obj.value)) {
                value = YES;
                *stop = YES;
            }
        }];
    } else {
        value = IsExcelString(self.value);
    }
    return value;
}

- (NSInteger)currentExcelNumber {
    NSInteger excelNumber = 1;
    __block NSString *excelValue = @"";
    if (self.templateType == JCTemplateType_Table) {
        [self.cells enumerateObjectsUsingBlock:^(JCElementModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (IsExcelString(obj.value)) {
                excelValue = obj.value;
                *stop = YES;
            }
        }];
        [self.combineCells enumerateObjectsUsingBlock:^(JCElementModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (IsExcelString(obj.value)) {
                excelValue = obj.value;
                *stop = YES;
            }
        }];
    } else {
        excelValue = self.value;
    }
    NSString *simpleValue = [excelValue getExcelValue];
    NSArray *array = [simpleValue componentsSeparatedByString:excel_component];
    if (array.count == 2 && [simpleValue isJCExcelString]) {
        NSInteger columnIndex = [array.lastObject integerValue];
        excelNumber = [DrawBoardInfo getExcelNumbersWithExcelColumnInex:columnIndex];
    }
    return excelNumber;
}

- (BOOL)isSameWith:(JCElementModel *)anotherModel {
    if (!SameString(type)) return NO;
    BOOL la = SameFloat(x);
    la = SameFloat(y);
//    !la? NSLog(@"y is not same"):nil;
    la = SameFloat(width);
//    !la? NSLog(@"width is not same"):nil;
    la = SameFloat(height);
//    !la? NSLog(@"height is not same"):nil;
    la = SameInteger(rotate);
//    !la? NSLog(@"rotate is not same"):nil;
    la = SameString(fieldName);
//    !la? NSLog(@"fieldName is not same"):nil;
    if (self.templateType != JCTemplateType_SerialNumber) {
        BOOL valueIsSame = [[self.value getExcelValue] isEqualToString:[anotherModel.value getExcelValue]];
        la = valueIsSame & la;
//        !la? NSLog(@"value is not same"):nil;
    }
    BOOL value = SameFloat(x) && SameFloat(y) && (SameFloat(width) || SameFloat(height)) && SameInteger(rotate) && SameString(fieldName);
    if (![self.type isEqualToString:@"date"]) {
        value = value & la;
    }
    if (!value) return NO;
    if (self.templateType == JCTemplateType_Text || self.templateType == JCTemplateType_Date || self.templateType == JCTemplateType_SerialNumber) {
        value = SameInteger(textAlignHorizonral) && SameInteger(textAlignVertical) && SameInteger(paperColorIndex) && SameInteger(colorReverse) && SameInteger(lineMode) && SameJCPorpertyNumber(wordSpacing) && SameJCPorpertyNumber(letterSpacing) && SameJCPorpertyNumber(lineSpacing) && SameInteger(lineBreakMode) && SameInteger(typesettingMode) && SameString(fontCode) && SameArray(fontStyle) && SameArray(typesettingParam) && SameJCPorpertyNumber(fontSize);
        NSString *date1 = [self.dateFormat stringByTrimmingCharactersInSet:[NSCharacterSet characterSetWithCharactersInString:@"无"]];
        NSString *date2 = [anotherModel.dateFormat stringByTrimmingCharactersInSet:[NSCharacterSet characterSetWithCharactersInString:@"无"]];
        NSString *time1 = [self.timeFormat stringByTrimmingCharactersInSet:[NSCharacterSet characterSetWithCharactersInString:@"无"]];
        NSString *time2 = [anotherModel.timeFormat stringByTrimmingCharactersInSet:[NSCharacterSet characterSetWithCharactersInString:@"无"]];
        value = value && [date1 isEqualToString:date2] && [time1 isEqualToString:time2];
        value = value && SameString(prefix) && SameString(startNumber) && SameString(suffix) && SameInteger(incrementValue);
    } else if (self.templateType == JCTemplateType_BarCode) {
        value = SameInteger(textPosition) && SameFloat(textHeight) && SameInteger(codeType);
    } else if (self.templateType == JCTemplateType_QRCode) {
        value = SameInteger(correctLevel);
    } else if (self.templateType == JCTemplateType_Graph || self.templateType == JCTemplateType_Line) {
        value = SameJCPorpertyNumber(lineWidth) && SameInteger(lineType) && SameInteger(graphType);
    } else if (self.templateType == JCTemplateType_Image) {
        value = SameArray(imageProcessingValue);
    } else if (self.templateType == JCTemplateType_Table) {
        value = SameInteger(row) && SameInteger(column);
        if (!value) return NO;
        // 比较行高
        for (NSInteger i = 0; i < self.rowHeight.count; i ++) {
            NSString *v1 = [NSString stringWithFormat:@"%.0f",[self.rowHeight[i] floatValue]];
            NSString *v2 = [NSString stringWithFormat:@"%.0f",[anotherModel.rowHeight[i] floatValue]];
            if (![v1 isEqualToString:v2]) {
                return NO;
            }
        }
        // 比较列宽
        for (NSInteger i = 0; i < self.columnWidth.count; i ++) {
            NSString *v1 = [NSString stringWithFormat:@"%.0f",[self.columnWidth[i] floatValue]];
            NSString *v2 = [NSString stringWithFormat:@"%.0f",[anotherModel.columnWidth[i] floatValue]];
            if (![v1 isEqualToString:v2]) {
                return NO;
            }
        }
        // 比较cell
        NSArray *cell1 = self.cells;
        NSArray *cell2 = anotherModel.cells;
        __block BOOL __cellIsSame = YES;
        [cell1 enumerateObjectsUsingBlock:^(JCElementModel *cellModel1, NSUInteger idx, BOOL * _Nonnull stop) {
            JCElementModel *matchModel = [cell2 find:^BOOL(JCElementModel *obj) {
                return obj.rowIndex == cellModel1.rowIndex && obj.columnIndex == cellModel1.columnIndex;
            }];
            if (matchModel) {
                BOOL value = [cellModel1 tableCellIsSameWith:matchModel];
                if (!value) {
                    __cellIsSame = NO;
                    *stop = YES;
                }
            }
        }];
        if (!__cellIsSame) return NO;

        // 比较合并后的单元格
        NSArray *combineCell1 = self.combineCells;
        NSArray *combineCell2 = anotherModel.combineCells;
        __block BOOL __combineCellIsSame = YES;
        [combineCell1 enumerateObjectsUsingBlock:^(JCElementModel *cellModel1, NSUInteger idx, BOOL * _Nonnull stop) {
            JCElementModel *matchModel = [combineCell2 find:^BOOL(JCElementModel *obj) {
                return [obj.elementId isEqualToString:cellModel1.elementId];
            }];
            if (matchModel) {
                BOOL value = [cellModel1 tableCellIsSameWith:matchModel];
                if (!value) {
                    __combineCellIsSame = NO;
                    *stop = YES;
                }
            }
        }];
        if (!__combineCellIsSame) return NO;
    }
    return value;
}

// 单独比较表格的某个属性
- (BOOL)tableCellIsSameWith:(JCElementModel *)anotherModel {
    BOOL value = [[self.value getExcelValue] isEqualToString:[anotherModel.value getExcelValue]];
    if (!value) return NO;
    value = SameInteger(textAlignHorizonral) && SameInteger(textAlignVertical)/* && SameInteger(lineMode) */&& SameJCPorpertyNumber(wordSpacing) && SameJCPorpertyNumber(letterSpacing) && SameInteger(lineBreakMode) && SameInteger(typesettingMode) && SameJCPorpertyNumber(lineSpacing) && SameString(fontCode) && SameArray(fontStyle) && SameArray(typesettingParam) && SameJCPorpertyNumber(fontSize);
    return value;
}

#pragma mark - custom method
- (NSString *)elementValue:(NSString *)value withExcelIndex:(NSInteger)pageIndex elementId:(NSString *)elementId isNeedTitle:(BOOL)isNeedTitle{
    NSArray *array = [value componentsSeparatedByString:excel_component];
    NSString *result = value;
    if (array.count == 2 && [value isJCExcelString]) {
        // excel原始数据
        JCExcelForElement *excelInfo = [JCExcelForElement excelInfoWith:[self getExternalData]];
        if(([self getModifyData] == nil || [self getModifyData].allKeys.count == 0) && [self.dataBind count] == 0){
            NSInteger columnNumber = [array.lastObject integerValue];
            NSArray *columnInfo = [excelInfo.columnArr safeObjectAtIndex:columnNumber];
            result = [columnInfo safeObjectAtIndex:pageIndex];
            // 修改过的内容
            NSDictionary *task = [self getTask];
            NSDictionary * modify = [self getModifyData];
            NSDictionary *modifyData = [task objectForKey:@"modifyData"];
            if (modifyData && [modifyData isKindOfClass:[NSDictionary class]]) {
                NSDictionary *temp = [modifyData objectForKey:elementId];
                if ([temp count] > 0 && [temp.allKeys containsObject:StringFromInt(pageIndex)]) {
                    NSString *changeValue = [temp objectForKey:StringFromInt(pageIndex)];
                    result = changeValue;
                }
            }//
            if(!STR_IS_NIL(self.contentTitle)){
                NSString *titleString = self.contentTitle.length > 100?[self.contentTitle substringToIndex:100]:self.contentTitle;
                result = [NSString stringWithFormat:@"%@：%@",titleString,result];
            }
        }else{
            //新版本数据结构赋值 不在使用老的数据contentTitle
            //修改过的内容
            NSDictionary *task = [self getTask];
            NSDictionary * modify = [self getModifyData];
            NSInteger columnNumber = [array.lastObject integerValue];
            NSArray *columnInfo = [excelInfo.columnArr safeObjectAtIndex:columnNumber];
            //result 对应安卓excelContent
            result = [columnInfo safeObjectAtIndex:pageIndex];
            NSArray * excelHeaders = excelInfo.columnHeaders;
            NSString * excelHeader = [self getExcelHeader:excelHeaders columnNumber:columnNumber isDefaultHeader:true]; //columnNumber < 0 ? @"" : [excelHeaders objectAtIndex:columnNumber];
            NSDictionary * elementModify = [modify objectForKey:elementId];
            //Excel全局配置的数据 默认
            NSDictionary * elementModifyGlobal = [elementModify objectForKey:@"0"];
            //对应页码修改的数据
           // NSDictionary * elementModifyPage = [elementModify objectForKey:[NSString stringWithFormat:@"%ld",pageIndex + 1]];
            NSDictionary * elementModifyPage = [elementModify objectForKey:[NSString stringWithFormat:@"%ld",[self transModifyIndex:pageIndex+1]]];
            if(!((elementModifyPage == nil || elementModifyPage.allKeys.count == 0) && (elementModifyGlobal == nil || elementModifyGlobal.allKeys.count == 0))){
                //BOOL useTitle = [[elementModifyPage objectForKey:@"useTitle"] boolValue] == true ?: [[elementModifyGlobal objectForKey:@"useTitle"] boolValue] ?: false;
                BOOL useTitle = [self getShowBool:elementModifyPage secondDic:elementModifyGlobal key:@"useTitle" defaultValue:false];
                NSString * title = [self getShowValue:elementModifyPage secondDic:elementModifyGlobal key:@"title" defaultText:excelHeader];
                NSString * delimiter = [self getShowValue:elementModifyPage secondDic:elementModifyGlobal key:@"delimiter" defaultText:@"："];
                NSString * prefix = [self getShowValue:elementModifyPage secondDic:elementModifyGlobal key:@"prefix" defaultText:@""];
                NSString * suffix = [self getShowValue:elementModifyPage secondDic:elementModifyGlobal key:@"suffix" defaultText:@""];
                NSString * applyValue = [self getShowValue:elementModifyPage secondDic:elementModifyGlobal key:@"value" defaultText:result];
                if(useTitle){
                    if(title == nil || title.length == 0){
                        result = [NSString stringWithFormat:@"%@%@%@",prefix,applyValue,suffix];
                    }else{
                        if(isNeedTitle){
                            result = [NSString stringWithFormat:@"%@%@%@%@%@",title,delimiter,prefix,STR_IS_NIL(applyValue)?@"":applyValue,suffix];
                        }else{
                            result = [NSString stringWithFormat:@"%@%@%@",prefix,applyValue,suffix];
                        }

                    }
                }else{
                    result = [NSString stringWithFormat:@"%@%@%@",prefix,applyValue,suffix];
                }
            }
        }
    }
   else if(!STR_IS_NIL(self.fieldName)){
        NSArray *goodsArr = [self getGoodsData];
        if(goodsArr.count > pageIndex){
            JCGoodDetailInfo *goodInfo = [goodsArr safeObjectAtIndex:pageIndex];
            result = [goodInfo valueForKey:self.fieldName];
        }
        if(!STR_IS_NIL(self.contentTitle)){
            if(STR_IS_NIL(result)){
                result = [NSString stringWithFormat:@"%@：",self.contentTitle];
            }else{
                NSString *titleString = [JCTMDataBindGoodsInfoManager getPlaceHolderValueWithFieldName:self.fieldName];
                result = [NSString stringWithFormat:@"%@：%@",titleString,result];
            }
        }else{

        }
    }
    return result;
}

-(NSInteger)transModifyIndex:(NSInteger)page{
    NSDictionary * dataSource = [self getDataSource].firstObject;
    //NSArray * ranges = [dataSource objectForKey:@"range"];
//        NSArray * values = [[dataSource objectForKey:@"headers"] objectForKey:@"values"];
    NSDictionary * headerData = [dataSource objectForKey:@"headers"];
    BOOL isUnCustomHeader = false;
    if ([headerData isKindOfClass:[NSDictionary class]] && [headerData.allValues.firstObject isKindOfClass:[NSNumber class]]) {
        isUnCustomHeader = true;
    }
    return page + (isUnCustomHeader ? 1 : 0);
}


-(NSString *)getExcelHeader:(NSArray *)excelHeaders columnNumber:(NSInteger)columnNumber isDefaultHeader:(BOOL)isDefaultHeader
{
    NSString * header = columnNumber < 0 ? @"" : (excelHeaders.count > columnNumber ? [excelHeaders objectAtIndex:columnNumber] : @"");
    if(!([self isNullData:header] || [header isEqualToString:@""])){
        return header;
    }
    if(isDefaultHeader){
        return [JCExcelTransUtil isCommdityTemplateWith:[self getDataSource]] ? @"" :  [self getHeaderStringWith:columnNumber];
    }else{
        return @"";
    }
}

-(NSString *)getHeaderStringWith:(NSInteger)index
{
    return [NSString stringWithFormat:@"%@%@",XY_LANGUAGE_TITLE_NAMED(@"app100001121", @"列"),[JCExcelTransUtil IntToString:index+1]];
}

-(NSString *)getShowValue:(NSDictionary *)firstDic secondDic:(NSDictionary *)secondDic key:(NSString *)key defaultText:(NSString *)defaultText
{
    NSString * result = defaultText;
    NSString * firstText = [firstDic objectForKey:key];
    NSString * secondText = [secondDic objectForKey:key];
    if(![self isNullData:firstText]){
        result = firstText;
    }else if(![self isNullData:secondText]){
        result = secondText;
    }
    return UN_NIL(result);
}

-(BOOL)getShowBool:(NSDictionary *)firstDic secondDic:(NSDictionary *)secondDic key:(NSString *)key defaultValue:(BOOL)defaultValue
{
    BOOL result = defaultValue;
    NSString * firstValue = [firstDic objectForKey:key];
    NSString * secondValue = [secondDic objectForKey:key];
    if(!isNull(firstValue)){
        result = [firstValue boolValue] ;
    }else if(!isNull(secondValue)){
        result = [secondValue boolValue];
    }
    return result;
}

-(BOOL)isNullData:(NSString *)str
{
    return isNull(str) || [str isEqualToString:@"(null)"] || [str isEqualToString:@"null"];
}

- (NSArray *)getAllFontCodes {
    NSMutableArray *fontCodes = [NSMutableArray array];
    if (self.templateType == JCTemplateType_Table) {
        [self.cells enumerateObjectsUsingBlock:^(JCElementModel *cell, NSUInteger idx, BOOL * _Nonnull stop) {
            if (![fontCodes containsObject:cell.fontCode]) {
                [fontCodes addObject:cell.fontCode];
            }
        }];

        [self.combineCells enumerateObjectsUsingBlock:^(JCElementModel *combineCell, NSUInteger idx, BOOL * _Nonnull stop) {
            if (![fontCodes containsObject:combineCell.fontCode]) {
                [fontCodes addObject:combineCell.fontCode];
            }
        }];
    } else {
        [fontCodes addObject:self.fontCode];
    }
    return fontCodes;
}

@end

#pragma clang diagnostic pop
