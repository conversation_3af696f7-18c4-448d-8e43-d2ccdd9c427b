import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:niim_login/login_plugin/login_plugin_config.dart';
import 'package:niim_login/niim_login.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:text/Task_Queue.dart';
import 'package:text/flutter_bugly.dart';
import 'package:text/pages/login/LoginPluginHostApiNiimbot.dart';
import 'package:text/pages/industry_template/home/<USER>/advertisement_db_utils.dart';

import 'application.dart';
import 'comm_init.dart';
import 'log_utils.dart';
import 'package:text/utils/toast_util.dart';
import 'package:niim_login/login_plugin/macro/constant.dart';
import 'package:text/utils/common_fun.dart';

final taskQueue = TaskQueue();

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    var http = super.createHttpClient(context);
    http.findProxy = (url) {
      if (isErrorUrl(url.toString())) {
        taskQueue.addTask(() async {
          //showToast 桥接的原生windows层弹出 虽然是异步但是await不生效 因为原生层的处理没有真正等到5s后回调，所以延迟模拟回掉
          showToast(msg: "${url.toString()}  请求的域名不正确！！!", timeInSecForIosWeb: 5);
          await Future.delayed(Duration(seconds: 5));
        });
      }
      return  "DIRECT";
       // return 'PROXY ************:8888';
    };
    http.badCertificateCallback = (X509Certificate cert, String host, int port) => true;
    return http;
  }
}

//是否运行环境调用了测试环境的域名
bool isErrorUrl(String url) {
  if (Application.isProductEnv() && url.contains("-test")) {
    return true;
  }
  return false;
}

/// 编译环境配置
/// androidstudio --RunConfigrations--Additional run args 增加--dart-define=ENV=debug
/// vscode --launch.json -- flutter run ---args 增加"--dart-define", "ENV=debug"
const String env = String.fromEnvironment('ENV', defaultValue: 'release');

void main() async {
  Log.d("启动 main.dart");
  CustomFlutterBinding();
  WidgetsFlutterBinding.ensureInitialized();

  initAppCommon();

  // debugPaintSizeEnabled = true;
  if (Platform.isAndroid) {
    SystemUiOverlayStyle systemUiOverlayStyle = const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent, //设置为透明
    );
    SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
  }
}

void initAppCommon() {
  Application.initSp().then((e) {
    if (Application.isAppStore == false) {
      HttpOverrides.global = MyHttpOverrides();
    }
    LoginPluginConfig.sharedInstance().loginPluginHostApi = LoginPluginHostApiNiimbot();
    var apiBaseUrl = "https://print.niimbot.com/api";
    if (Application.appEnv != AppEnv.production) {
      apiBaseUrl = "https://print.jc-test.cn/api";
    }
    mainRun(apiBaseUrl);
    // FlutterBugly.postCatchedException(() {
    //   mainRun(apiBaseUrl);
    // }, debugUpload: false);
  });
}
