import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:keyboard_actions/keyboard_actions.dart';
import 'package:niimbot_excel/niimbot_excel_utils.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_element.dart';
import 'package:niimbot_flutter_canvas/src/model/template_data.dart' as CanvasTemplateData;
import 'package:niimbot_flutter_canvas/src/widgets/good_lib/good_field_manager.dart';
import 'package:text/application.dart';
import 'package:text/network/dio_utils.dart';
import 'package:text/network/http_api.dart';
import 'package:text/pages/canvas/impl/goods_import_impl.dart';
import 'package:text/pages/etag/goods_select/model/good_category.dart';
import 'package:text/pages/etag/goods_select/select_good_category_page.dart';
import 'package:text/pages/etag/home/<USER>/change_good_model.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/input_field.dart';
import 'package:text/utils/item_divider.dart';
import 'package:text/utils/plane_button.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/utils/toast_util.dart';

class GoodsChangeWidget extends StatefulWidget {
  final Map<String, dynamic> goods;
  GoodsEntry entry;
  var isAddGoods;
  String columnIndexGoodField;
  bool isAutoFocusFirstItem;
  Map<String, dynamic>? templateJson;

  GoodsChangeWidget({Key? key, required this.goods, this.entry = GoodsEntry.etag, this.isAddGoods = false,this.columnIndexGoodField = "",this.isAutoFocusFirstItem = false,required this.templateJson})
      : super(key: key);

  @override
  _GoodsChangeWidgetState createState() => _GoodsChangeWidgetState();
}

class _GoodsChangeWidgetState extends State<GoodsChangeWidget> with WidgetsBindingObserver {
  List<TextEditingController> controllers = [];
  double keyboardHeight = 0;
  FocusScopeNode node = FocusScopeNode();
  int catId = 0;
  String categoryName = "";
  List<GoodCategory> cats = [];
  final ScrollController _scrollController = ScrollController();
  ///模版上存在的商品字段
  List<ChangeGoodModel> templateGoodFields = [];
  ///全部的商品字段
  List<ChangeGoodModel> allGoodFields = [];
  bool panelStateFold = true;//面板折叠默认true

  KeyboardActionsConfig _buildConfig() {
    return KeyboardActionsConfig(
      keyboardBarColor: Colors.white,
      keyboardSeparatorColor: ThemeColor.divider,
      keyboardBarElevation: 0,
      nextFocus: false,
      actions: [
        KeyboardActionsItem(
          focusNode: node,
          onTapAction: () {},
        )
      ],
      defaultDoneWidget: Container(
        child: Text(
          intlanguage('app01031', '完成'),
          style: const TextStyle(
            color: ThemeColor.COLOR_4676EE,
            fontSize: 15.0,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    getGoodsCategory();
    WidgetsBinding.instance.addObserver(this);
    List<String> headers = CanvasTemplateData.TemplateData.goodsInfnFieldDescName();
    GoodFieldManager().goodFieldList.sort((a, b) => (a.columnIndex ?? 0).compareTo(b.columnIndex ?? 0));
    for (int i = 0; i < headers.length; i++) {
      //过滤删除列
      if(GoodFieldManager().goodFieldList[i].isDeleted == true){
        continue;
      }
      controllers.add(TextEditingController());
    }

    node.addListener(() {
      if (node.focusedChild == null) {
        node.unfocus();
      }
    });
    _processTemplateGoodFields();
  }
  ///获取商品字段列索引
  int _getGoodFieldColumnIndex(JsonElement element){
    int columnIndex = -1;
    var oldValue = element.value ?? "";
    if (oldValue.contains("⊙")) {
      var str = "${oldValue}";
      var value = NiimbotExcelUtils.getElementBindIndex(str);
      columnIndex = NiimbotExcelUtils.decodeCellIndex(value!).c;
    }else{
      columnIndex =  element.getBindingColumn();
    }
    return columnIndex;
  }

  ///添加模版上显示的商品字段
  _addTemplateGoodFields(List<String> headers,List<String> fields,int columnIndex){
    if(columnIndex<0){
      return;
    }
    if(columnIndex >= GoodFieldManager().goodFieldList.length){
      return;
    }
    //如果模版中的字段已经被删除，则修改时不显示删除的列
    if(GoodFieldManager().goodFieldList[columnIndex].isDeleted == true){
      return;
    }
    ChangeGoodModel goodModel = ChangeGoodModel(goodFieldTitle: headers[columnIndex], goodField: fields[columnIndex],columnIndex: columnIndex);
    if(!templateGoodFields.contains(goodModel)){
      ///品名固定在第一的位置
      if(fields[columnIndex] == 'name'){
        templateGoodFields.insert(0, goodModel);
      }else{
        templateGoodFields.add(goodModel);
      }
    }
  }

  _processTemplateGoodFields(){
    GoodFieldManager().goodFieldList.sort((a, b) => (a.columnIndex ?? 0).compareTo(b.columnIndex ?? 0));
    List<String> headers = CanvasTemplateData.TemplateData.goodsInfnFieldDescName();
    List<String> fields = CanvasTemplateData.TemplateData.goodsInfnFieldName();

    CanvasTemplateData.TemplateData templateData = CanvasTemplateData.TemplateData.fromJson(widget.templateJson ?? {});
    templateData.elements.forEach((element) {
      if(element.isBindingCommodity()){
        int columnIndex = -1;
        if(element.type == ElementItemType.table){
          TableElement tableElement = element as TableElement;
          tableElement.getBindingCommodidyCells().forEach((element) {
            columnIndex = _getGoodFieldColumnIndex(element);
            _addTemplateGoodFields(headers, fields, columnIndex);
          });
          tableElement.getBindingCommodidyCombineCells().forEach((element) {
            columnIndex = _getGoodFieldColumnIndex(element);
            _addTemplateGoodFields(headers, fields, columnIndex);
          });
        }else{
          columnIndex = _getGoodFieldColumnIndex(element);
          _addTemplateGoodFields(headers, fields, columnIndex);
        }
      }
    });
    //other字段
    List<ChangeGoodModel> otherGoodFields = [];
    for(var i= 0;i<headers.length;i++){
      //过滤删除列
      if(GoodFieldManager().goodFieldList[i].isDeleted == true){
        continue;
      }
      ChangeGoodModel? goodFieldModel = templateGoodFields.firstWhereOrNull((element) => element.goodFieldTitle == headers[i]);
      if(goodFieldModel == null){
        ChangeGoodModel goodModel = ChangeGoodModel(goodFieldTitle: headers[i], goodField: fields[i],columnIndex: i);
        otherGoodFields.add(goodModel);
      }
    }
    Map<String,int> fieldsSortInfoMap = GoodFieldManager().getFieldsSortInfoMap();
    ///预处理templateGoodFields，增加排序信息
    for(var i= 0;i<templateGoodFields.length;i++){
      ChangeGoodModel field = templateGoodFields[i];
      field.sortIndex = fieldsSortInfoMap[field.goodField];
    }
    ///对模版上的字段按sortIndex进行排序
    templateGoodFields.sort();
    ///预处理otherGoodFields，增加排序信息
    for(var i= 0;i<otherGoodFields.length;i++){
      ChangeGoodModel field = otherGoodFields[i];
      field.sortIndex = fieldsSortInfoMap[field.goodField];
    }
    ///对其他字段按sortIndex进行排序
    otherGoodFields.sort();

    allGoodFields.addAll(templateGoodFields);
    allGoodFields.addAll(otherGoodFields);
    if(templateGoodFields.length == 0){
       panelStateFold = false;
    }
  }

  @override
  void dispose() {
    super.dispose();
    _scrollController.dispose();
    GoodsImportImpl.goodCategories = cats;
    GoodsImportImpl.goodCategories.removeWhere((element) => element.id == 0 || element.id == -1);
    WidgetsBinding.instance.removeObserver(this);
    controllers.forEach((controller) {
      controller.dispose();
    });
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();

    // 键盘高度
    final double viewInsetsBottom = EdgeInsets.fromViewPadding(
            View.of(context).viewInsets, View.of(context).devicePixelRatio)
        .bottom;
    if (Platform.isIOS) {
      keyboardHeight = viewInsetsBottom - 30;
    } else {
      keyboardHeight = viewInsetsBottom;
    }
    // 打印键盘高度
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(bottom: MediaQuery.viewInsetsOf(context).bottom),
        decoration: const BoxDecoration(
          color: ThemeColor.listBackground,
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0)),
        ),
        height: widget.entry == GoodsEntry.etag
            ? MediaQuery.sizeOf(context).height * 0.54 + keyboardHeight
            : MediaQuery.sizeOf(context).height - 60,
        child: Column(
          children: [
            Container(
              height: 48,
              decoration: const BoxDecoration(
                color: ThemeColor.background,
                borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12)),
              ),
              child: _titleBarWidget(),
            ),
            widget.entry == GoodsEntry.etag ? Container() : _buildCategory(),
            Expanded(
              child: Container(
                //  height: 470,
                child: FocusScope(
                  node: node,
                  child: MediaQuery.removePadding(
                      removeTop: true,
                      context: context,
                      child: KeyboardActions(
                        config: _buildConfig(),
                        child: ListView(
                          controller: _scrollController,
                          children: [
                            _buildFormWidget(),
                          ],
                        ),
                      )),
                ),
              ),
            ),
            widget.entry == GoodsEntry.etag
                ? Container(
                    height: 0,
                  )
                : Container()
          ],
        ));
  }

  _titleBarWidget() {
    return Row(
      children: [
        Padding(
          padding: EdgeInsetsDirectional.fromSTEB(5, 0, 0, 0),
          child: PlaneButton(
          width: 60,
          child: Text(
            intlanguage('app100000692', '取消'),
            style: const TextStyle(color: ThemeColor.title, fontSize: 16, fontWeight: FontWeight.w600),
          ),
          onTap: (){//ToNativeMethodChannel
            ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "110_342_320", "ext": {}});
            node.unfocus();
             Future.delayed(const Duration(milliseconds: 300), () {
               Navigator.of(context).pop();
            });
          }
        ),
        ),

        const Spacer(),
        ConstrainedBox(
          constraints: BoxConstraints(maxWidth: MediaQuery.sizeOf(context).width - 200),
          child: Text(
            widget.isAddGoods ? intlanguage('app100001043', '添加商品') : intlanguage('app100000703', '修改信息'),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
            style: const TextStyle(color: ThemeColor.title, fontSize: 18, fontWeight: FontWeight.w600),
          ),
        ),
        const Spacer(),

        Padding(
          padding: EdgeInsetsDirectional.fromSTEB(0, 0, 5, 0),
          child:  PlaneButton(
          width: 60,
          child: Text(
            widget.isAddGoods ? intlanguage('app01031', '完成') : intlanguage('app00017', '保存'),
            style: const TextStyle(color: ThemeColor.brand, fontSize: 16, fontWeight: FontWeight.w600),
          ),
          onTap: () {
            ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "110_342_321", "ext": {}});
            node.unfocus();
            if (widget.goods["name"].toString().isNotEmpty) {
              if (widget.entry == GoodsEntry.etag) {
                Navigator.of(context).pop(widget.goods);
              } else {
                widget.goods["userCategoryId"] = catId;
                if (!Application.networkConnected) {
                   //showToast(msg:intlanguage("app100000354", "网络异常"));
                   showCenterToast(context, intlanguage("app100000354", "网络异常"));
                   return;
                }
                 EasyLoading.show();
                if (widget.isAddGoods) {
                  requestAddGood(widget.goods);
                } else {
                  updateGoodsMap(widget.goods, context);
                }
              }
            } else {
              showToast(msg: intlanguage('app100001039', '品名不能为空'));
            }
          },
        ),
        ),

      ],
    );
  }

  List<Widget> _buildGoodFieldsWidgets(List<ChangeGoodModel> goodModels){
    List<Widget> widgets = [];
    for(int i = 0;i<goodModels.length;i++){
      ChangeGoodModel goodFieldModel = goodModels[i];
      String fieldTitle = goodFieldModel.goodFieldTitle!;
      String fieldName = goodFieldModel.goodField!;
      Widget itemWidget = _goodsItemWidget(fieldTitle, widget.goods[fieldName] ?? "", controllers[i], (value) {
        widget.goods[fieldName] = value;
      }, isMust: fieldName == 'name',autoFocus:widget.isAutoFocusFirstItem ? i==0 :  widget.columnIndexGoodField == fieldName,isLast:!panelStateFold && i==goodModels.length-1);
      widgets.add(itemWidget);
    }
    return widgets;
  }

  Widget _buildOtherInfo(){
    return GestureDetector(
      onTap: (){
        setState(() {
          panelStateFold = false;
        });
      },
      child: Container(
        color: Colors.transparent,
        padding: const EdgeInsets.only(top: 18,bottom: 18),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[

            Text(
              intlanguage('app100001272', '其他信息'),
              style: TextStyle(
                fontSize: 14,
                color: ThemeColor.COLOR_999999,
                fontWeight: FontWeight.w400,
              ),
            ),
            const SvgIcon(
              'assets/images/e_tag/icon_fold_info.svg',
              height: 16,
              width: 16,
            ),
          ],
        ),
      ),
    );
  }


  _buildFormWidget() {
    List<Widget> widgets = [];
    if(panelStateFold){
      widgets.addAll(_buildGoodFieldsWidgets(templateGoodFields));
      if(templateGoodFields.length < CanvasTemplateData.TemplateData.goodsInfnFieldDescName().length){
        widgets.add(_buildOtherInfo());
      }
    }else{
      widgets.addAll(_buildGoodFieldsWidgets(allGoodFields));
    }
    return Container(
        color: ThemeColor.background,
        child: Column(
          children: [
            ...widgets
          ],
        ));
  }

  _goodsItemWidget(String title, String hint, TextEditingController controller, Function function,
      {bool isMust = false, bool isLast = false,bool autoFocus = false}) {
    // 减少在键盘弹起时的controller刷新，导致预选词抖动
    if (controller.text != hint && hint != null) {
      controller.text = hint;
      controller.selection = TextSelection.fromPosition(
        TextPosition(offset: hint.length),
      );
    }
    return Padding(
      padding: EdgeInsets.only(top: 0, left: 13),
      child: Column(
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              isMust
                  ? Text(
                      "*",
                      style: const TextStyle(color: ThemeColor.brand, fontSize: 14, fontWeight: FontWeight.w600),
                    )
                  : SizedBox(
                      width: 7,
                    ),
              const SizedBox(
                width: 2,
              ),
              Container(
                width: 117,
                child: Text(
                  title,
                  style: const TextStyle(color: ThemeColor.title, fontSize: 14, fontWeight: FontWeight.w600),
                ),
              ),
              const SizedBox(
                width: 14,
              ),
              Expanded(
                child: InputField(
                  height: 44,
                  textAlign: TextAlign.start,
                  autofocus: autoFocus,
                  textEditingController: controller,
                  contentPadding: EdgeInsets.zero,
                  hintText: intlanguage('app100000972', '请输入'),
                  // keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  onChanged: (value) {
                    function.call(value);
                  },
                  onEditingComplete: () {
                    if (node.focusedChild == node.children.last) {
                      _scrollController.animateTo(
                        0,
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                      node.nextFocus();
                    } else if (node.focusedChild == node.children.elementAt(node.children.length - 2)) {
                      _scrollController.animateTo(
                        _scrollController.position.maxScrollExtent,
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                      node.nextFocus();
                    } else {
                      node.nextFocus();
                    }
                  },
                  onTap: () {
                    if (node.focusedChild == node.children.last) {
                      _scrollController.animateTo(
                        _scrollController.position.maxScrollExtent,
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    }
                  },
                ),
              ),
              SizedBox(
                width: 20,
              )
            ],
          ),
          isLast ? Container() : const ItemDivider(),
        ],
      ),
    );
  }

  _buildCategory() {
    return GestureDetector(
      onTap: () {
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          isDismissible: false,
          enableDrag: false,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
          builder: (BuildContext context) {
            return SelectGoodCategoryPage(
                catId: catId,
                cats: cats,
                funcation: (categorys) {
                  cats.clear();
                  cats.addAll(categorys);
                  GoodCategory? cat = cats.firstWhereOrNull((element) => element.id == catId);
                  categoryName = cat?.name ?? intlanguage('app100001038', '未分类');
                  catId = cat?.id ?? 0;
                });
          },
        ).then((value) {
          setState(() {
            if (value != null && value is Map<String, dynamic>) {
              categoryName = value['name'];
              catId = value['id'];
            }
            // else {
            //   catId = goodModel.userCategoryId ?? 0;
            //   if (catName.isEmpty) {
            //     catName = intlanguage('app100001038', '未分类');
            //   }
            // }
          });
        });
      },
      child: Container(
        color: ThemeColor.background,
        height: 44,
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: 22, right: 12),
        margin: EdgeInsets.symmetric(vertical: 12),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            SizedBox(
              width: 117,
              child: Text(
                intlanguage('app100001042', '分类'),
                style: TextStyle(color: ThemeColor.title, fontSize: 14, fontWeight: FontWeight.w600),
              ),
            ),
            const SizedBox(
              width: 14,
            ),
            Expanded(
                child: Text(
              categoryName,
              style: const TextStyle(color: ThemeColor.title, fontSize: 14, fontWeight: FontWeight.w600),
              textAlign: TextAlign.start,
            )),
            Icon(
              Icons.chevron_right,
              color: ThemeColor.subtitle,
            )
          ],
        ),
      ),
    );
  }

  ///获取商品分类
  getGoodsCategory() async {
    cats.clear();
    cats.addAll(GoodsImportImpl.goodCategories);
    cats.insert(0, GoodCategory(id: 0, name: intlanguage('app100001038', '未分类')));
    int index = cats.indexWhere((fruit) => fruit.id == widget.goods["userCategoryId"]);
    if (index < 0) {
      categoryName = cats[0].name ?? "";
      catId = cats[0].id ?? 0;
    } else {
      categoryName = cats[index].name ?? "";
      catId = cats[index].id ?? 0;
    }
    setState(() {});

    List<GoodCategory> goodCategories = await _requestGoodsCategoryFromNet();
    try {
      goodCategories.sort((a, b) => DateTime.parse(b.updateTime!).compareTo(DateTime.parse(a.updateTime!)));
    } catch (e) {}
    cats.clear();
    cats.addAll(goodCategories);
    cats.insert(0, GoodCategory(id: 0, name: intlanguage('app100001038', '未分类')));
    index = cats.indexWhere((fruit) => fruit.id == widget.goods["userCategoryId"]);
    if (index < 0) {
      categoryName = cats[0].name ?? "";
      catId = cats[0].id ?? 0;
    } else {
      categoryName = cats[index].name ?? "";
      catId = cats[index].id ?? 0;
    }
    if(mounted) {
      setState(() {});
    }
  }

  Future<List<GoodCategory>> _requestGoodsCategoryFromNet() async {
    List<GoodCategory> categorys = [];
    Completer<List<GoodCategory>> completer = Completer<List<GoodCategory>>();
    DioUtils.instance.requestNetwork<Map<String, dynamic>>(Method.get, GoodsLibApi.userGoodscategory,
        isList: true, needLogin: false, onSuccessList: (data) {
      for (var e in data) {
        categorys.add(GoodCategory.fromJson(e));
      }
      completer.complete(categorys);
        }, onError: (code, message) {
      showToast(msg: message);
      completer.completeError(-1);
    });
    return completer.future;
  }

  ///更新商品数据
  updateGoodsMap(Map<String, dynamic> value, BuildContext context) {
    GoodFieldManager().correctionDataWith(value);
    value["sceneTags"] = [];
    DioUtils.instance.requestNetwork<String>(
        Method.put,
        {
          'method': 'POST',
          'path': '/product/customStorage/' + value["id"],
          'needLogin': true,
        },
        params: value,
        isList: false, onSuccess: (model) {
      Navigator.of(context).pop(widget.goods);
      EasyLoading.dismiss();
    }, onError: (code, message) {
      EasyLoading.dismiss();
      showToast(msg: message);
    });
  }

  /// 手动添加商品
  Future<bool> requestAddGood(Map<String, dynamic> value) async {
    GoodFieldManager().correctionDataWith(value);
    value["sceneTags"] = "";
    List<Map<String, dynamic>> parameters = [];
    parameters.add(value);
    Completer<bool> completer = Completer<bool>();
    DioUtils.instance.requestNetwork<Map>(Method.post, GoodsLibApi.addGoodsInfo,
        params: parameters, isList: true, needLogin: true, onSuccessList: (data) {
      Map map = data[0];
      widget.goods["id"] = map["id"];
      Navigator.of(context).pop(widget.goods);
      EasyLoading.dismiss();
      completer.complete(true);
    }, onError: (code, message) {
      EasyLoading.dismiss();
      showToast(msg: message);
      completer.completeError(-1);
    });
    return completer.future;
  }
}
