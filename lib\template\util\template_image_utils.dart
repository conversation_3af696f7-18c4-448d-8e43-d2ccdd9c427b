import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:niimbot_log_plugin/model/niimbot_log_result.dart';
import 'package:niimbot_log_plugin/niimbot_log_manager.dart';
import 'package:niimbot_template/models/copy_wrapper.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:text/application.dart';
import 'package:text/network/dio_utils.dart';
import 'package:text/template/util/template_path_generator_utils.dart';
import 'package:image/image.dart' as img;

class TemplateImageUtils {
  static const String OSS_MODULE_USER_TEMPLATE = "USER_TEMPLATE";
  //网络上标签纸背景图压缩后缀
  static const String BACKGROUND_IMAGE_SUFFIX = "?x-oss-process=image/resize,w_1000/quality,q_80";


  // 检查文件是否有效（存在且非空）
  static Future<bool> isFileValid(File file) async {
    // 判断文件是否存在且非空
    return await file.exists() && await file.length() > 0;
  }

  /**
   * 下载模版图片资源
   */
  static Future<TemplateData> downloadTemplateImages(TemplateData templateData) async {
    //下载模版背景
    List<String> localBackgrounds = templateData.localBackground;
    if(templateData.backgroundImage.isNotEmpty){
      List<String> backgrounds = templateData.backgroundImage.split(",");
      if (backgrounds.length != localBackgrounds.length) {
        localBackgrounds.clear();
        localBackgrounds = List.filled(backgrounds.length, '');
      }
      for (int i = 0; i < localBackgrounds.length; i++) {
        String labelId = templateData.id!;
        if(templateData.profile.extra.labelId?.isNotEmpty ?? false){
          labelId = templateData.profile.extra.labelId!;
        }
        File bgFile = await TemplatePathGeneratorUtils.getBackgroundFile(
            labelId, i.toString());
        localBackgrounds[i] = bgFile.absolute.path;
        File localBgFile = File(localBackgrounds[i]);
        bool isValidFile = await isFileValid(localBgFile);
        if (!isValidFile) {
          await localBgFile.create(recursive: true);
          String bgUrl = "${backgrounds[i]}$BACKGROUND_IMAGE_SUFFIX";
          await downloadImage(bgUrl, localBgFile.absolute.path);
        }
      }
    }


    //下载模版缩略图
    File localThumbFile =
        await TemplatePathGeneratorUtils.getThumbFile(templateData.id!, templateData.profile.extra.updateTime);
    bool isValidFile = await isFileValid(localThumbFile);
    if (!isValidFile) {
      await localThumbFile.create(recursive: true);
      await downloadImage(templateData.thumbnail, localThumbFile.absolute.path);
    }

    //下载image类型元素图
    List<BaseElement> baseElements = [];
    await Future.forEach(templateData.elements, (element) async {
      if (element is ImageElement) {
        // 区分普通图片和点9图片
        if (element.isNinePatch == true) {
          // 处理点9图元素
          String localImagePath = element.localImageUrl;
          bool isValidFile = await isFileValid(File(localImagePath));
          if (!isValidFile) {
            // 下载普通图片
            File localElementFile = await TemplatePathGeneratorUtils.getElementFile(templateData.id!, element.id);
            await localElementFile.create(recursive: true);
            await downloadImage(element.imageUrl, localElementFile.absolute.path);
            localImagePath = localElementFile.absolute.path;
          }

          String ninePatchLocalImagePath = element.ninePatchLocalUrl ?? "";
          bool isValidFile0 = await isFileValid(File(ninePatchLocalImagePath));
          if (!isValidFile0) {
            // 下载点9图
            File localNinepatchFile =
                await TemplatePathGeneratorUtils.getNinepatchElementFile(templateData.id!, element.id);
            await localNinepatchFile.create(recursive: true);
            await downloadImage(element.ninePatchUrl, localNinepatchFile.absolute.path);
            ninePatchLocalImagePath = localNinepatchFile.absolute.path;
          }

          ImageElement imageElement =
              element.copyWith(localImageUrl: localImagePath, ninePatchLocalUrl: ninePatchLocalImagePath);
          baseElements.add(imageElement);
        } else {
          // 处理普通图片元素
          String localImagePath = element.localImageUrl;
          bool isValidFile = await isFileValid(File(localImagePath));
          if (!isValidFile) {
            File localElementFile = await TemplatePathGeneratorUtils.getElementFile(templateData.id!, element.id);
            await localElementFile.create(recursive: true);
            await downloadImage(element.imageUrl, localElementFile.absolute.path,needReEncode: true);
            localImagePath = localElementFile.absolute.path;
          }

          ImageElement imageElement = element.copyWith(localImageUrl: localImagePath);
          baseElements.add(imageElement);
        }
      } else {
        baseElements.add(element);
      }
    });
    return templateData.copyWith(
        localThumbnail: localThumbFile.absolute.path, localBackground: localBackgrounds, elements: baseElements);
  }
  /// 下载图片并验证完整性，支持重试
  static Future<String> downloadImage(String? imageUrl, String savePath, {
    bool needReEncode = false,
    int maxRetries = 3, // 最大重试次数
  }) async {
    if (imageUrl == null || imageUrl.isEmpty) {
      return "";
    }

    int retryCount = 0;
    while (retryCount < maxRetries) {
      try {
        // 添加进度回调来监控下载进度
        int expectedSize = -1;
        await DioUtils.instance.getDio().download(
          imageUrl,
          savePath,
          options: Options(
            headers: {
              HttpHeaders.acceptEncodingHeader: '*', // 禁用压缩以获取准确的文件大小
            },
            responseType: ResponseType.bytes,
          ),
          onReceiveProgress: (received, total) {
            if (total != -1) {
              expectedSize = total;
              // 验证下载进度
              if (received < total) {
                debugPrint('下载进度: ${(received / total * 100).toStringAsFixed(0)}%  expectedSize=$expectedSize');
              }
            }
          },
        );

        // 验证下载的文件
        File downloadedFile = File(savePath);
        if (!await downloadedFile.exists() || await downloadedFile.length() < expectedSize) {
          throw Exception('文件不存在或为空');
        }

        if (needReEncode) {
          try {
            // final originalBytes = await downloadedFile.readAsBytes();
            // final decodedImage = img.decodeImage(originalBytes);
            final decodedImage = await compute(TemplateImageUtils.decodeImage, savePath);
            if (decodedImage == null) {
              throw Exception('图片解码失败');
            }

            // final encodedPng = img.encodePng(decodedImage);
            final encodedPng = await compute(TemplateImageUtils.encodePngFile, decodedImage);
            await downloadedFile.writeAsBytes(encodedPng);
          } catch (e) {
            debugPrint('图片重编码失败: $e');
            throw e;
          }
        }

        return savePath;
      } catch (e) {
        retryCount++;
        debugPrint('下载图片失败(第$retryCount次): $e');

        // 删除可能不完整的文件
        try {
          await File(savePath).delete();
        } catch (e) {
          debugPrint('删除失败文件出错: $e');
        }

        // 如果达到最大重试次数，返回空字符串
        if (retryCount >= maxRetries) {
          debugPrint('达到最大重试次数($maxRetries)，下载失败');
          return "";
        }
      }
    }

    return "";
  }

  static img.Image? decodeImage(String path) {
    return img.decodeImage(File(path).readAsBytesSync());
  }

  static Uint8List encodePngFile(img.Image image) {
    return img.encodePng(image);
  }


  static FutureOr<String> downloadImageTest(String? imageUrl, String savePath,{bool needReEncode = false}) async {
    if (imageUrl == null || imageUrl.isEmpty) {
      return "";
    }
    try {
      await DioUtils.instance.getDio().download(imageUrl, savePath);
      if(needReEncode){
        // Step 2: 读取为 byte[]
        final originalBytes = await File(savePath).readAsBytes();

        // Step 3: 解码为 Image 对象
        final decodedImage = img.decodeImage(originalBytes);
        if (decodedImage == null) {
          return savePath;
        }
        // Step 4: 编码为 PNG
        final encodedPng = img.encodePng(decodedImage);
        // Step 5: 覆盖保存为 PNG
        await File(savePath).writeAsBytes(encodedPng);
      }

    } catch (e) {
      debugPrint('下载图片失败: $e');
    }

    return savePath;
  }


  /**
   * 上传模版图片资源
   */
  static Future<TemplateData> uploadTemplateImages(TemplateData templateData) async {
    //上传缩略图
    String thumbnailUrl = await uploadImage(templateData.localThumbnail, OSS_MODULE_USER_TEMPLATE);
    //上传image类型元素图
    List<BaseElement> baseElements = [];
    await Future.forEach(templateData.elements, (element) async {
      if (element is ImageElement) {
        if (element.isNinePatch == true) {
          // 上传普通图片
          String imageUrl = await uploadImage(element.localImageUrl, OSS_MODULE_USER_TEMPLATE);
          // 上传点9图
          String ninePatchUrl = await uploadImage(element.ninePatchLocalUrl ?? '', OSS_MODULE_USER_TEMPLATE);

          ImageElement imageElement =
              element.copyWith(imageUrl: CopyWrapper.value(imageUrl), ninePatchUrl: ninePatchUrl);
          baseElements.add(imageElement);
        } else {
          String imageUrl = await uploadImage(element.localImageUrl, OSS_MODULE_USER_TEMPLATE);
          ImageElement imageElement = element.copyWith(imageUrl: CopyWrapper.value(imageUrl));
          baseElements.add(imageElement);
        }
      } else {
        baseElements.add(element);
      }
    });
    return templateData.copyWith(thumbnail: CopyWrapper.value(thumbnailUrl), elements: baseElements);
  }

  /**
   * 上传图片
   */
  static FutureOr<String> uploadImage(String localFilePath, String oSSModule) {
    if (localFilePath.isEmpty) {
      return "";
    }
    try {
      Completer<String> completer = Completer();
      NiimbotLogManager.instance.uploadFile(filePath: localFilePath, module: oSSModule,
          (NiimbotLogResultType resultCode, String value) {
        if (resultCode == NiimbotLogResultType.success) {
          completer.complete(value);
        } else {
          // completer.completeError(resultCode);
          completer.complete("");
        }
      });
      return completer.future;
    } catch (e) {
      return "";
    }
  }
}
