import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:flutter_canvas_plugins_interface/config/template_config.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/provider/template_changed_notifier.dart';
import 'package:niimbot_flutter_canvas/src/utils/canvas_helper.dart';
import 'package:niimbot_flutter_canvas/src/utils/template_utils.dart';
import 'package:niimbot_flutter_canvas/src/widgets/controller/canvas/canvas_init_model.dart';
import 'package:text/pages/canvas/controller/niimbot_canvas_state.dart';
import 'package:text/pages/canvas/model/NiimbotPrinter.dart';
import 'package:text/pages/canvas/setting/setting_label_page.dart';
import 'package:text/pages/industry_template/select_label/create_label_model.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/cachedImageUtil.dart';
import 'package:text/utils/hardware_manager.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/utils/toast_util.dart';

import '../../../application.dart';
import '../../../log_utils.dart';
import '../../../macro/color.dart';
import '../../../routers/fluro_navigator.dart';
import '../../../utils/common_fun.dart';
import '../../../utils/event_bus.dart';
import '../../create/model/printer_connect_state.dart';
import '../../industry_template/select_label/label_selector_page.dart';
import '../../typesetting/smart_typesetting_helper.dart';

typedef CheckedCallback = void Function(bool isConfirm, bool checked);

class NiimbotCanvasController extends GetxController {
  final NiimbotCanvasState state = NiimbotCanvasState();

  BuildContext? context;
  bool _hasShowRfidDialog = false;
  bool _hasShowLabelDialog = false;
  bool _hasConfirmRfidReplace = false;

  ///是否已经成功执行过弹出替换替换弹窗的逻辑
  bool hasPopShowRfidDialog = false;
  bool _initDevice = false;
  var pageShow = true.obs;
  int needRfidTemplate = 0;
  String source = '';
  String? jsonData;

  @override
  void onInit() async {
    super.onInit();
    pageShow = true.obs;
    NiimbotEventBus.getDefault().register(this, (data) async {
      // Log.d("======打印画板EventBus消息：$data");
      if (data is Map && data.containsKey("action")) {
        final action = data['action'];
        switch (action) {
          ///打印机连接状态通知
          case "printerConnectState":
            _updatePrinter(data);
            break;

          ///rfid读取结果通知
          case "setFlutterlabelData":
            setCanvasLabelData(data['labelData']);
            break;
          case "syncPrintSettingRfidReplace":
            _processRfidReplaceEvent();
            break;

          /// 同步原生端rfid替换标记
          case "syncRfidReplaceTag":
            _hasConfirmRfidReplace = data['rfidReplaceTag'];
            _processRfidReplaceEvent();
            break;

          /// 退出画板
          case "finishCanvas":
            Future.delayed(const Duration(milliseconds: 1000), () {
              CustomNavigation.pop();
            });
            break;
          case "saveUpdateCanvasData":
            String newCanvasJsonStr = data["saveSuccessData"];
            TemplateData oldTemplateData = TemplateData.fromJson(json.decode(jsonData!));
            TemplateUtils.transformTemplateJsonData(newCanvasJsonStr, showLoading: false).then((newCanvasJson) {
              TemplateData saveData = TemplateData.fromJson(json.decode(newCanvasJson));
              if ((saveData.id == oldTemplateData.id || data["oldId"] == oldTemplateData.id)) {
                jsonData = saveData.generateCanvasJson(savePrintTemplate: true);
                var map = <String, dynamic>{};
                map['updateCanvasPageJson'] = '';
                map['targetTemplate'] = data;
                NiimbotEventBus.getDefault().post(map);
              }
            });
            break;
          case "printSaveCanvasData":
            TemplateData oldTemplateData = TemplateData.fromJson(json.decode(jsonData!));
            String canvasJsonStr = data["templateData"];
            String oldId = data["oldId"];
            TemplateData templateData = TemplateData.fromJson(json.decode(canvasJsonStr));
            if (templateData.id == oldTemplateData.id || oldId == oldTemplateData.id) {
              jsonData = templateData.generateCanvasJson(savePrintTemplate: true);
            }
            CanvasEventBus.getDefault().post(data);
          case "RFIDShowStatus":
            // RFID的展示状态变更，主要是涉及到在画板内手动切换机型、系列等
            // B32R才显示RFID标签,此处判断id和name
            // 从硬件管理获取优先级排列下的机型模型
            // 中转到画板内处理
            state.printer = await HardWareManager.instance().getPrioritizationHardware();
            bool isShowRFID = state.printer?.isSupportRecordRfid() ?? false;
            CanvasEventBus.getDefault().post({'action': 'isShowRFID', 'isShowRFID': isShowRFID});
            break;
        }
      } else if (data is Map && data.containsKey("printerConnectState")) {
        _updatePrinter(data);
      }
    });
    ever(pageShow, (callback) {
      Log.d("====用来监听数字的变化,--$pageShow");
    });

    // 从硬件管理获取优先级排列下的机型模型
    state.printer = await HardWareManager.instance().getPrioritizationHardware();
    // 初始化画板的状态模型
    // B32R才显示RFID标签,此处判断id和name
    bool isShowRFID = state.printer?.isSupportRecordRfid() ?? false;
    state.initModel = CanvasInitModel(isShowRFID: isShowRFID);
  }

  @override
  void dispose() {
    NiimbotEventBus.getDefault().unregister(this);
    super.dispose();
  }


  _initSelectPrinter() {
    if (state.printer != null) {
      HardWareManager.instance().getPrioritizationHardware().then((onValue) {
        Application.selectPrinter = NiimbotPrinter.fromJson(onValue?.toJson());
      });
      // ToNativeMethodChannel().getPrinterById(state.printer!.id.toString()).then((value) {
      //   Log.d('================printer: $value');
      //   if (value != null && value.isNotEmpty) {
      //     Application.selectPrinter = niimbotPrinterFromJson(value);
      //   }
      // });
    }
  }

  /// 刷新当前连接打印机状态
  _updatePrinter(Map<dynamic, dynamic> data) async {
    var connectState = PrinterConnectState.fromJson(Map<String, dynamic>.from(data['printerConnectState']));
    num connected = connectState.connected ?? 0;
    Log.d("打印机状态变化, connected： ${connected}, initDevice: $_initDevice, pageShow: ${pageShow.isTrue}");
    if (connected == 0) {
      state.rfidTemplate = null;
      // 从硬件管理获取优先级排列下的机型模型
      state.printer = await HardWareManager.instance().getPrioritizationHardware();
    } else {
      if (connectState.rfidStatus == 0) {
        // 不具备识别功能,识别到的标签纸置空
        state.rfidTemplate = null;
      }
      // 用机器 id 查找
      String? machineId = connectState.machineId;
      state.printer = HardWareManager.instance().findHardwareModel(machineId: machineId ?? '');
      Log.d("打印机已连接--->printer: ${state.printer.toString()}");
    }
    _initDevice = false;

    /// 打印机连接状态发生变化
    var replaceEvent = <String, dynamic>{};
    replaceEvent['action'] = 'printStatusChange';
    CanvasEventBus.getDefault().post(replaceEvent);

    // 初始化画板的状态模型
    // B32R才显示RFID标签,此处判断id和name
    bool isShowRFID = state.printer?.isSupportRecordRfid() ?? false;
    CanvasEventBus.getDefault().post({'action': 'isShowRFID', 'isShowRFID': isShowRFID});
  }

  /// 获取当前模板适配机型，取第一个
  getSelectPrinter(String machineId, {bool isCustomTemplate = false}) {
    Future.delayed(const Duration(milliseconds: 300), () {
      try {
        // if (!isCustomTemplate) {
        //   Application.selectPrinter = null;
        //   return;
        // }
        if (machineId.isEmpty) {
          String jsonData = TemplateChangedNotifier().templateDataJsonData ?? "";
          Map<String, dynamic> canvasJsonMap = jsonDecode(jsonData);
          machineId = canvasJsonMap['profile']['machineId'];
        }
        machineId = machineId.split(",")[0];
        _initSelectPrinter();
        // ToNativeMethodChannel().getPrinterById(machineId).then((value) {
        //   if (value != null && value.isNotEmpty) {
        //     Application.selectPrinter = niimbotPrinterFromJson(value);
        //   }
        // });
      } catch (e, s) {
        Log.d('异常信息:\n $e');
        Log.d('调用栈信息:\n $s');
      }
    });
  }

  /// 画板同步rfid数据
  setCanvasLabelData(Map? data) {
    if (null != data && data.isNotEmpty) {
      Map<String, dynamic> labelData = Map<String, dynamic>.from(data);
      _updateRfidTemplate(labelData, checkRfid: true);
    } else {
      state.rfidTemplate = null;
    }
  }

  /// 更新rfid模板
  _updateRfidTemplate(Map<String, dynamic> data, {bool checkRfid = false}) {
    try {
      if (data.isNotEmpty) {
        // Log.e("======getRfidTemplate: $data");
        var record = TemplateData.fromJson(data);
        state.rfidTemplate = record;
        if (TemplateChangedNotifier().templateDataJsonData == null) return;
        hasPopShowRfidDialog = true;
        Map<String, dynamic> canvasJsonMap = jsonDecode(TemplateChangedNotifier().templateDataJsonData ?? "");
        TemplateData sourceTemplate = TemplateData.fromJson(canvasJsonMap);
        Log.d(
            "=======RFID替换弹窗, checkRfid: $checkRfid, context: $context, hasShowRfidDialog: $_hasShowRfidDialog, needRfidTemplate: $needRfidTemplate");
        if (checkRfid &&
            context != null &&
            !_hasShowRfidDialog &&
            needRfidTemplate == 1 &&
            !sourceTemplate.isMatchRFID(record.profile.barcode)) {
          if (_hasConfirmRfidReplace) {
            _replaceRfidTemplate(data);
            String? currentRouteName = BoostNavigator.instance.getTopPageInfo()?.pageName;
            if (currentRouteName == "canvas") {
              BoostNavigator.instance.popUntil(route: 'canvas');
            }

            ///自动替换时，回到画板
            // BoostNavigator.instance.popUntil(route: 'canvas');
            // if (CanvasHelper.canvasContext != null) {
            //   _hasShowRfidDialog = false;
            //   Navigator.of(CanvasHelper.canvasContext!).popUntil((route) {
            //     if (route.settings.name == '/') {
            //       return true;
            //     }
            //     return false;
            //   });
            // }
            String msg = "${intlanguage("app01463", "检测到标签纸")}${record.name}";
            if (pageShow.value) {
              showToast(msg: msg);
            }
          } else {
            Log.d("=======pageShow: ${pageShow.value}");
            if (!pageShow.value) return;
            _hasShowRfidDialog = true;
            _showRfidDialog(context!, record, (isConfirm, checked) {
              Log.d("=======RFID替换弹窗, 选择结果-->isConfirm: $isConfirm, checked: $checked");
              _hasShowRfidDialog = false;
              if (isConfirm) {
                if (checked) {
                  _hasConfirmRfidReplace = checked;
                  CanvasHelper.hasConfirmRfidReplace = checked;
                  syncRfidReplaceTag();
                }
                _replaceRfidTemplate(data);
              }
            });
          }
        }
      }
    } catch (e, s) {
      Log.d('异常信息:\n $e');
      Log.d('调用栈信息:\n $s');
    }
  }

  /// 获取当前打印机识别的标签
  getRfidTemplate() {
    Future.delayed(const Duration(milliseconds: 500), () {
      ToNativeMethodChannel().getPrinterLabelData().then((value) {
        // Log.d("获取当前打印机识别的标签：${value}");
        if (value is Map) {
          _updateRfidTemplate(Map<String, dynamic>.from(value), checkRfid: true);
        }
      });
    });
  }

  /// 替换rfid模板
  _replaceRfidTemplate(Map<String, dynamic> data) async {
    var replaceEvent = <String, dynamic>{};
    replaceEvent['action'] = 'replaceTemplate';
    var target = TemplateData.fromJson(data);
    var template = TemplateData.fromJson(jsonDecode(TemplateChangedNotifier().templateDataJsonData!));
    var map = await OcrUtils.generateRfidReplaceTemplate(template, target);
    // jsonData = jsonEncode(map);
    replaceEvent['targetTemplate'] = map;
    CanvasEventBus.getDefault().post(replaceEvent);
    if (_hasShowLabelDialog) {
      /// 在选纸页面，点击使用的话，需要pop当前选择页面, 注意双层弹出的情况，目前通过canpop可限制，在空白标签纸内替换标签纸可产生两次route入栈
      // NavigatorUtils.goBack(context!);
      // NavigatorUtils.goBack(context!);
      _hasShowLabelDialog = false;
    }
  }

  /// 根据code获取翻译
  String getI18nString(String stringCode, String defaultStr, {List<String>? param}) {
    return intlanguage(stringCode, defaultStr, param: param);
  }

  Future<TemplateData?> onLabelInfoClick(String? canvasJson, TemplateConfig? config, BuildContext context) async {
    this.context = context;
    Completer<TemplateData> completer = Completer<TemplateData>();
    try {
      // Log.e("onLabelInfoClick: $canvasJson");
      TemplateData templateData = TemplateData.fromJson(json.decode(canvasJson ?? ""));
      // 2023/5/9 Ice_Liu 画板上标签信息点击逻辑
      /// 新模板
      /// 1.如果为当前识别的纸，提示"当前打印机内安装本纸无需替换"
      /// 2.不是识别的纸，进入选纸页面，换纸
      /// 3.空白标签纸，进入设置页面
      /// 旧模板
      /// 1.有标签纸，进入选纸页面，换纸
      /// 2.空白标签纸，进入设置页面
      Log.d("画板LabelId：${templateData.profile.extrain.labelId}, rfid的labelId: ${state.rfidTemplate?.labelId}");
      if (templateData.profile.extrain.labelId?.isNotEmpty == true && templateData.paperType != 3) {
        _changeLabel(context, templateData, completer);
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "click",
          "posCode": "108_211",
          "ext": {"source": 1}
        });
      } else {
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "click",
          "posCode": "108_211",
          "ext": {"source": 3}
        });
        return await labelSetting(canvasJson, config, context);
      }
    } catch (e) {
      Log.d(e.toString());
    }
    return completer.future;
  }

  /// 更换标签纸，打开选择标签纸
  _changeLabel(BuildContext context, TemplateData source, Completer<TemplateData> completer) {
    _hasShowLabelDialog = true;
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      builder: (BuildContext context) {
        return LabelSelectorPage(
          isNeedPop: false,
          isFromCanvas: true,
          sence: LabelSelecSence.normalCanvas,
          onChanged: (value) {
            try {
              _hasShowLabelDialog = false;
              // Log.e("_changeLabel: $value");
              if (value.isEmpty) {
                completer.complete(null);
              } else {
                String templateId = value['id'].toString();
                int templateClass = value['profile']["extrain"]["templateClass"] is int
                    ? value['profile']["extrain"]["templateClass"]
                    : value['profile']["extrain"]["templateClass"] is String
                        ? int.parse(value['profile']["extrain"]["templateClass"])
                        : value['profile']["extrain"]["templateClass"].toInt();
                if (state.rfidTemplate != null && templateId != state.rfidTemplate?.id) {
                  showToast(msg: intlanguage("app100000974", "请先安装该标签纸到打印机"));
                  return;
                }
                EasyLoading.show(dismissOnTap: false);
                ToNativeMethodChannel()
                    .getTemplateDetailFromNative(templateId, templateClass: templateClass)
                    .then((data) async {
                  EasyLoading.dismiss();
                  Navigator.of(context).pop();
                  // Log.e("从原生获取的模板详情数据: $data");
                  if (data != null && data.isNotEmpty) {
                    TemplateData templateData = TemplateData.fromJson(json.decode(data));
                    TemplateData result = await OcrUtils.generateRfidReplaceTemplate(source, templateData);;
                    // Log.e("替换后的数据: ${json.encode(result)}");
                    // jsonData = result.generateCanvasJson();
                    completer.complete(result);
                    getSelectPrinter(result.profile.machineId, isCustomTemplate: false);
                  }
                });
              }
            } catch (e) {
              Log.d(e.toString());
            }
          },
        );
      },
    );
  }

  /// 同步rfid自动替换标记
  syncRfidReplaceTag({bool init = false}) {
    // ToNativeMethodChannel().syncRfidReplaceTag(_hasConfirmRfidReplace).then((value) {
    //   if (value == 1) {
    //     _hasConfirmRfidReplace = true;
    //   }
    //   if (init) getRfidTemplate();
    // });

    _hasConfirmRfidReplace = CanvasHelper.hasConfirmRfidReplace;
    if (init) getRfidTemplate();
  }

  /// 处理原生端rfid标记同步事件
  _processRfidReplaceEvent() {
    // if (null != context && _hasShowRfidDialog) {
    //   _hasShowRfidDialog = false;
    //   NavigatorUtils.goBack(context!);
    // }
    ///自动替换时，回到画板
    if (CanvasHelper.canvasContext != null) {
      _hasShowRfidDialog = false;
    }
    if (null != state.rfidTemplate) {
      _replaceRfidTemplate(jsonDecode(state.rfidTemplate!.generateCanvasJson()));
    }
  }

  _showRfidDialog(BuildContext context, TemplateData template, CheckedCallback callback) {
    BuildContext innerContext = context;
    if (CanvasHelper.canvasContext != null) {
      innerContext = CanvasHelper.canvasContext!;
    }

    ToNativeMethodChannel().sendTrackingToNative({"track": "show", "posCode": "108_176", "ext": {}});
    bool _checked = false;
    // 是否多背景
    bool isMutipleBack = template.localBackground.length > 1;
    File localImage = File(template.localBackground[0]);
    showDialog(
        context: innerContext,
        barrierDismissible: false,
        useRootNavigator: false,
        barrierColor: ThemeColor.mainTitle.withOpacity(0.35),
        builder: (BuildContext context) {
          return WillPopScope(
            onWillPop: () async => true,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {},
              child: Center(
                child: Container(
                  width: 270,
                  decoration: const BoxDecoration(
                      color: Colors.white, borderRadius: BorderRadiusDirectional.all(Radius.circular(14))),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Stack(
                        alignment: AlignmentDirectional.topEnd,
                        fit: StackFit.passthrough,
                        children: [
                          Positioned(
                            child: Row(
                              children: [
                                const Spacer(),
                                GestureDetector(
                                    behavior: HitTestBehavior.opaque,
                                    onTap: () {
                                      NavigatorUtils.goBack(context);
                                      callback.call(false, _checked);
                                    },
                                    child: const Padding(
                                      padding: EdgeInsetsDirectional.fromSTEB(50, 8, 9, 30),
                                      child: SvgIcon('assets/images/icon_rfid_dialog_close.svg'),
                                    )),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(30, 25, 30, 12),
                            child: Text(intlanguage("app100000533", "识别到已安装的标签纸"),
                                textAlign: TextAlign.center,
                                softWrap: true,
                                style: const TextStyle(
                                    color: KColor.title,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    decoration: TextDecoration.none)),
                          ),
                        ],
                      ),
                      Container(
                        height: 105,
                        margin: const EdgeInsetsDirectional.only(top: 0, bottom: 4, end: 45, start: 45),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: KColor.COLOR_FAFAFA,
                        ),
                        child: Center(
                            child: isMutipleBack
                                ? CacheImageUtil().netCacheImage(
                                    imageUrl: template.thumbnail,
                                    errorWidget: const SvgIcon('assets/images/label_create/label_placeholder.svg'),
                                    fit: BoxFit.contain,
                                    height: 80)
                                : Image.file(
                                    localImage,
                                    height: 80,
                                    fit: BoxFit.contain,
                                  )),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text(template.name,
                            textAlign: TextAlign.center,
                            softWrap: true,
                            style: const TextStyle(
                                color: KColor.COLOR_999999,
                                fontSize: 13,
                                fontWeight: FontWeight.w400,
                                decoration: TextDecoration.none)),
                      ),
                      Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(16, 14, 16, 0),
                          child: Text(intlanguage("app100000534", "为保证打印效果，建议使用此标签纸"),
                              textAlign: TextAlign.center,
                              softWrap: true,
                              style: const TextStyle(
                                  color: KColor.BLACK,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  decoration: TextDecoration.none))),
                      StatefulBuilder(
                        builder: (BuildContext context, void Function(void Function()) setState) {
                          return GestureDetector(
                              onTap: () {
                                setState(() {
                                  _checked = !_checked;
                                });
                              },
                              child: Padding(
                                padding: const EdgeInsetsDirectional.all(16),
                                child: Row(
                                  children: [
                                    Image.asset(
                                      _checked
                                          ? "assets/images/ic_check_checked.png"
                                          : "assets/images/ic_check_default.png",
                                      width: 16,
                                      height: 16,
                                    ),
                                    const SizedBox(
                                      width: 5,
                                    ),
                                    Expanded(
                                      child: Text(intlanguage("app100000535", "以后默认使用已识别到的标签纸"),
                                          softWrap: true,
                                          style: const TextStyle(
                                              color: KColor.COLOR_999999,
                                              fontSize: 13,
                                              fontWeight: FontWeight.w400,
                                              decoration: TextDecoration.none)),
                                    ),
                                  ],
                                ),
                              ));
                        },
                      ),
                      GestureDetector(
                          onTap: () {
                            // NavigatorUtils.goBack(context);
                            callback.call(true, _checked);
                            BoostNavigator.instance.popUntil(route: 'canvas');
                          },
                          child: Container(
                              margin: const EdgeInsetsDirectional.fromSTEB(20, 0, 20, 20),
                              padding: const EdgeInsets.symmetric(vertical: 11),
                              decoration: BoxDecoration(
                                color: KColor.RED,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Center(
                                child: Text(intlanguage("app100000536", "使用"),
                                    softWrap: true,
                                    style: const TextStyle(
                                        color: KColor.WHITE,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        decoration: TextDecoration.none)),
                              ))),
                    ],
                  ),
                ),
              ),
            ),
          );
        });
  }
}

/// 设置
extension Setting on NiimbotCanvasController {
  /// 标签纸设置页
  Future<TemplateData?> labelSetting(String? canvasJson, TemplateConfig? config, BuildContext context) async {
    this.context = context;
    TemplateData? result;
    try {
      TemplateData templateData = TemplateData.fromJson(json.decode(canvasJson ?? ""));
      if (templateData.profile.extrain.labelId?.isNotEmpty == true) {
        result = await _settingLabel(context, templateData, false);
      } else {
        result = await _settingLabel(context, templateData, true);
      }
    } catch (e) {
      Log.d(e.toString());
    }
    return result;
  }

  Future<TemplateData?> _settingLabel(BuildContext context, TemplateData source, bool isCustomLabel) async {
    _hasShowLabelDialog = true;
    var value = await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: false,
      enableDrag: false,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      builder: (BuildContext context) {
        return SettingLabelPage(
          initMode: CreateLabelMode.fromJson(source.toJson()),
          isCustomLabel: isCustomLabel,
          canvasData: source,
          canvasState: state,
        );
      },
    );
    _hasShowLabelDialog = false;
    if (value != null) {
      if (value is Map && value.isEmpty) return null;
      bool isCustom = value['isCustom'];
      dynamic templateData = value['templateData'];
      if (isCustom) {
        TemplateData target = TemplateData.fromJson(source.toJson());
        target.width = templateData['width'];
        target.height = templateData['height'];
        target.rotate = templateData['rotate'];
        TemplateData result = await OcrUtils.generateRfidReplaceTemplate(source, target);
        result.name = templateData['name'];
        result.consumableType = templateData['consumableType'];
        result.paperType = templateData['paperType'];
        result.cableLength = templateData['cableLength'];
        result.cableDirection = templateData['cableDirection'];
        result.paccuracyName = templateData['paccuracyName'] == null ? 203 : num.parse(templateData['paccuracyName']);
        result.profile.machineName = templateData['profile']['machineName'];
        result.profile.machineId = templateData['profile']['machineId'];
        getSelectPrinter(templateData['profile']['machineId'], isCustomTemplate: true);
        return result;
      } else {
        if (templateData is TemplateData) {
          // 覆盖掉原有name，否则后续merge会被rewrite掉
          source.name = templateData.name;
          // 未变更标签纸，不需要替换纸模型
          if (source.profile.extrain.labelId == templateData.profile.extrain.labelId) {
            source.width = templateData.width;
            source.height = templateData.height;
            return source;
          }
          // 已更换新的标签纸
          TemplateData result = await OcrUtils.generateRfidReplaceTemplate(source, templateData);
          getSelectPrinter(result.profile.machineId, isCustomTemplate: false);
          return result;
        }
      }
    }
    return null;
  }
}

extension Typesetting on NiimbotCanvasController {
  Future<List<bool>> ifShowSmartTypesetting(BuildContext context, String? source) async {
    return SmartTypesettingHelper().checkSmartTypesettingOpen(source);
  }

  Future<int> openSmartTypesetting(BuildContext context) async {
    return SmartTypesettingHelper().openSmartTypesetting(context);
  }

  Future<TemplateData?> getSmartTemplate(BuildContext context, String source) async {
    return SmartTypesettingHelper().getSelectedSmartTemplate(context, source);
  }
}
