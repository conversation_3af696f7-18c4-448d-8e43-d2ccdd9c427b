import 'dart:convert';
import 'dart:typed_data';

import 'package:pointycastle/api.dart';
import 'package:pointycastle/digests/sha1.dart';
import 'package:pointycastle/digests/sha256.dart';
import 'package:text/utils/string_utils.dart';

import '../log_utils.dart';
import '../network/entity/niimbot_user_agent.dart';

class EncryptUtil{
  static final _cipher = PaddedBlockCipher('AES/CBC/PKCS7');

  /// 解析用户标识
  static NiimbotUserAgent? parseUserAgent(String? userAgent) {
    if (userAgent == null) return null;
    final kvs = userAgent.split(" ");
    String preKey = "";
    final json = {};
    for (var d in kvs) {
      final kv = d.split("/");
      if (kv.length == 2) {
        json[kv[0]] = kv[1];
        preKey = kv[0];
      } else {
        if (preKey.isNotEmpty) {
          json[preKey] = '${json[preKey]} $d';
        }
      }
    }
    final count = int.tryParse(json['count'] ?? '');
    if (count != null) {
      return SecretNiimbotUserAgent(
        AppId: json['AppId'],
        OS: json['OS'],
        AppVersionName: json['AppVersionName'],
        Model: json['Model'],
        SystemVersion: json['SystemVersion'],
        DeviceId: json['DeviceId'],
        referer: json['referer'],
        count: count,
      );
    }
    return NiimbotUserAgent(
      AppId: json['AppId'],
      OS: json['OS'],
      AppVersionName: json['AppVersionName'],
      Model: json['Model'],
      SystemVersion: json['SystemVersion'],
      DeviceId: json['DeviceId'],
      referer: json['referer'],
    );
  }


  /// 数据加密
  static String? dataEncrypt(SecretNiimbotUserAgent agent, String value,
      {String? token}) {
    final params = _buildParams(agent, token);
    if (params != null) {
      _cipher.init(true, params);
      final decryptedBytes = _cipher.process(utf8.encode(value));
      return base64Encode(decryptedBytes);
    }
    return null;
  }

  /// 数据解密
  static dynamic dataDecrypt(SecretNiimbotUserAgent agent, String value,
      {String? token}) {
    final params = _buildParams(agent, token);
    if (params != null) {
      final data = base64Decode(value);
      _cipher.init(false, params);
      final decryptedData = utf8.decode(_cipher.process(data));
      return jsonDecode(decryptedData);
    }
    return null;
  }

  static dynamic dataDecryptInBackground(SecretNiimbotUserAgent agent, String value,
      {String? token}) {
    final params = _buildParams(agent, token);
    if (params != null) {
      final cipher = PaddedBlockCipher('AES/CBC/PKCS7');
      final data = base64Decode(value);
      cipher.init(false, params);
      final decryptedData = utf8.decode(cipher.process(data));
      return jsonDecode(decryptedData);
    }
    return null;
  }

  static PaddedBlockCipherParameters? _buildParams(
      SecretNiimbotUserAgent agent, String? token) {
    String ivStr = '';
    String keyStr = '';
    final arr = agent.count.toString().split('').map(int.parse).toList();
    for (var i = 0; i < arr.length; i++) {
      final index = arr[i];
      final keyVal = agent.getFieldValueByIndex(index);
      final ivVal = agent.getFieldFullValueByIndex(index);
      ivStr += ivVal;
      keyStr += keyVal;
    }
    if (keyStr.isNotEmpty) {
      final iv = StringUtils.padEnd(ivStr, 16, '0').substring(0, 16);
      String key = "";
      final tokenStr = token ?? "";
      final bytes = utf8.encode('$keyStr$tokenStr');
      if (agent.OS.toUpperCase() == 'IOS') {
        final keySHA256 = _bin2hex(SHA256Digest().process(bytes));
        final start = agent.count % 19;
        key = StringUtils.substring(keySHA256, start, start + 32);
      } else if (agent.OS.toUpperCase() == 'WINDOWS') {
        final keySHA256 = _bin2hex(SHA256Digest().process(bytes));
        final start = agent.count % 13;
        key = StringUtils.substring(keySHA256, start, start + 32);
      } else {
        final keySHA1 = _bin2hex(SHA1Digest().process(bytes));
        final start = agent.count % 17;
        key = StringUtils.substring(keySHA1, start, start + 32);
      }
      Log.d("===============iv：$iv, key: $key");
      return PaddedBlockCipherParameters(
          ParametersWithIV<KeyParameter>(
              KeyParameter(utf8.encode(StringUtils.padEnd(key, 32, '0'))),
              utf8.encode(StringUtils.padEnd(iv, 16, '0'))),
          null);
    }
    return null;
  }

  static String _bin2hex(Uint8List bytes, {String? separator, int? wrap}) {
    var len = 0;
    final buf = StringBuffer();
    for (final b in bytes) {
      final s = b.toRadixString(16);
      if (buf.isNotEmpty && separator != null) {
        buf.write(separator);
        len += separator.length;
      }

      if (wrap != null && wrap < len + 2) {
        buf.write('\n');
        len = 0;
      }

      buf.write('${(s.length == 1) ? '0' : ''}$s');
      len += 2;
    }
    return buf.toString();
  }
}
