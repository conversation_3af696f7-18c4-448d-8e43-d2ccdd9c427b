import 'dart:async';
import 'dart:io';

import 'package:common_utils/common_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

// import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_log_plugin/niimbot_log_plugin.dart';
import 'package:niimbot_log_plugin/niimbot_log_tool.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:text/application.dart';
import 'package:text/network/dio_utils.dart';
import 'package:text/network/http_api.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_state.dart';
import 'package:text/pages/my_template/controller/template_database_v2.dart';
import 'package:text/pages/my_template/model/my_template_list_model.dart';
import 'package:text/pages/my_template/model/template_data_extension.dart';
import 'package:text/template/util/template_detail_db_utils.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/toast_util.dart';

import '../../../utils/niimbot_template_db_manager.dart';
import '../my_template_state.dart';
import '../page/personal_template/controller/personal_template_logic.dart';
import 'package:flutter/widgets.dart';

enum TemplateDataWhere { search, delete }

class LoadingDebouncer {
  static bool _isScheduled = false;

  /// 防抖显示（防止在一帧内多次 show）
  static void scheduleShow(VoidCallback callback, {Duration delay = const Duration(milliseconds: 0)}) {
    if (_isScheduled) return;

    _isScheduled = true;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(delay, () {
        callback.call();
        _isScheduled = false;
      });
    });
  }
}

class SearchTemplateManager extends GetxController {
  /// 模版搜索结果
  var templatesResult = [].obs;

  /// 展示搜索页还是历史记录
  var fade = CrossFadeState.showFirst.obs;

  /// 存储实例
  late SharedPreferences sp;

  ///搜索状态
  var searchState = IndustryHomeState.loading;

  ///模板状态
  var operateType = TemplateOperateState.normal.obs;

  /// 管理状态模版选中结果
  var templatesSelectResult = [].obs;

  /// 搜索历史
  late var searchList = Rxn<List<String>>([]);

  /// 传输流
  late final _streamController = StreamController();

  final PersonalTemplateLogic logic = Get.find<PersonalTemplateLogic>();

  var isSearching = false.obs;

  var searchingKey = "".obs;

  var pageNumber = 1.obs;

  @override
  void onInit() async {
    //getHistroyList();
    super.onInit();
    logic.eventBusFunction = () {
      getSearchResult(
        page: pageNumber.value,
        searchKey: searchingKey.value,
        isDBOnly: true,
        dbresultClosure: (p0) {
          update();
        },
      );
    };
  }

  @override
  void onClose() {
    _streamController.close();
    super.onClose();
  }

  /// 获取搜索结果
  getSearchResult(
      {int page = 1,
      int limit = 20,
      String searchKey = '',
      bool isMoreData = false,
      bool isDBOnly = false,
      Function(List<TemplateData?>?)? dbresultClosure,
      Function(MyTemplateListModel?)? resultClosure}) async {
    if (searchKey.isEmpty) {
      templatesResult.value = [];
      searchState = IndustryHomeState.showContainer;
      dbresultClosure?.call([]);
      return;
    }
    searchingKey.value = searchKey;
    searchState = !isMoreData
        ? IndustryHomeState.loading
        : (templatesResult.isEmpty ? IndustryHomeState.loading : IndustryHomeState.showContainer);
    if (!isMoreData) {
      ToNativeMethodChannel().sendTrackingToNative({
        "track": "click",
        "posCode": "067_171",
        "ext": {"key_word": searchKey, "source": "2"}
      });
    }
    Map<String, dynamic> parameters = {
      'page': page,
      'limit': limit,
      'templateName': searchKey,
    };
    isSearching.value = true;
    if (isDBOnly || Application.user == null) {
      isSearching.value = false;
      _batchProcessing(
        searchKey,
        isMoreData,
        isMoreData ? page : 1,
      ).then((value) {
        NiimbotLogTool.uploadLogInstantTime({"templateList": value!.map((e) => e.id).toList()});
        templatesResult.value = (value)!;
        searchState = IndustryHomeState.showContainer;
        dbresultClosure?.call(value.length < page * 20 ? [] : value);
        if (templatesResult.isEmpty) {
          resultClosure?.call(null);
        }
      });
      return;
    }
    if (!isMoreData)
      EasyLoading.show();
    String lastRequestTimeKey = "";
    String? lastRequestTime = "";
    if (Platform.isAndroid) {
      if (Application.user!.id is int) {
        lastRequestTimeKey = '${Application.user!.id.toString()}_template/myList';
      } else {
        lastRequestTimeKey = '${(Application.user!.id as double).toInt().toString()}_template/myList';
      }
      var value = await ToNativeMethodChannel().getSPData(lastRequestTimeKey);
      lastRequestTime = value.toString();
    } else {
      if (Application.user!.id is int) {
        lastRequestTimeKey = '${Application.user!.id.toString()}_template/myList';
      } else {
        lastRequestTimeKey = '${Application.user!.id}_template/myList';
      }
      lastRequestTime = sp.getString(lastRequestTimeKey);
    }
    if (lastRequestTime == null) {
      DateTime modifiedDateTime = DateTime.now().subtract(Duration(days: 90));
      parameters["fromTimestamp"] = modifiedDateTime.millisecondsSinceEpoch.toString();
      parameters["toTimestamp"] = DateTime.now().millisecondsSinceEpoch.toString();
    } else {
      int timestamp = int.parse(lastRequestTime);
      DateTime originalDateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      DateTime modifiedDateTime = originalDateTime.subtract(Duration(hours: 1));
      parameters["fromTimestamp"] = modifiedDateTime.millisecondsSinceEpoch.toString();
      parameters["toTimestamp"] = DateTime.now().millisecondsSinceEpoch.toString();
    }
    DioUtils.instance.requestNetwork<Map>(Method.post, MyTemplateApi.myTemplateList,
        needLogin: false, params: parameters, isList: false, onSuccess: (data) async{
      isSearching.value = false;
      logic.state.needFreshMyTemplate = true;
      MyTemplateListModel model = await MyTemplateListModel.fromJson(data);
      var filterList = model.list?.where((item) => item.profile.extra.isDelete != true).toList();
      model = model.copyWith(list: filterList);
      _batchProcessing(searchKey, isMoreData, isMoreData ? page : 1, model: model).then((value) {
        NiimbotLogTool.uploadLogInstantTime({"templateList": value!.map((e) => e.id).toList()}, topic: "myTemplate");
        if (!isMoreData) EasyLoading.dismiss();
        templatesResult.value = (value)!;
        searchState = IndustryHomeState.showContainer;
        if (model.list!.isEmpty) {
          dbresultClosure?.call(value.length < page * 20 ? [] : value);
        } else {
          resultClosure?.call(model);
        }
      });
    }, onError: (code, message) {
      isSearching.value = false;
      logic.state.needFreshMyTemplate = true;
      _batchProcessing(
        searchKey,
        isMoreData,
        isMoreData ? page : 1,
      ).then((value) {
        NiimbotLogTool.uploadLogInstantTime({"templateList": value!.map((e) => e.id).toList()}, topic: "myTemplate");
        EasyLoading.dismiss();
        templatesResult.value = (value)!;
        searchState = IndustryHomeState.showContainer;
        dbresultClosure?.call(value.length < page * 20 ? [] : value);
        if (templatesResult.isEmpty) {
          searchState = IndustryHomeState.error;
          resultClosure?.call(null);
        }
        if (!isMoreData) {
          if (!Application.networkConnected) {
            if (templatesResult.isNotEmpty) showToast(msg: intlanguage('app100000354', '网络异常'));
          } else {
            showToast(msg: message);
          }
        }
      });
    });
  }
  // Future<List<TemplateData>?> _batchProcessing(String searchKey, bool isLoadMore, int page,
  //     {MyTemplateListModel? model}) async {
  //   if (model != null) {
  //     List<TemplateData>? templateList = model.list;
  //     if (!isLoadMore) {
  //       List<num>? deletedTemplates = model.deletedTemplates;
  //       List<Object?> deleteResult = await NiimbotTemplateDbManager.dbDeleteTemplateData([], ids: deletedTemplates);
  //       LogUtil.d("模板搜索--已删除$deleteResult个模板");
  //     }
  //     try {
  //       if (templateList!.isNotEmpty) {
  //         List<Object?> insertResult = await NiimbotTemplateDbManager.dbInsertTemplateData(templateList);
  //         LogUtil.d("模板搜索--已插入${insertResult.length}个 模板");
  //       }
  //     } catch (e) {
  //       LogUtil.d("模板搜索--已删除");
  //     }
  //   }
  //   Map queryWhereInfo = _templateSearchWhere(TemplateDataWhere.search, searchKey);
  //   List<TemplateData> templates = [];
  //   try {
  //     templates = await NiimbotTemplateDbManager.dbQueryLotBatchTemplateDataWhere(
  //       batchSize: Application.networkConnected ? null : 500 * page,
  //       where: queryWhereInfo["where"],
  //       whereArgs: queryWhereInfo["whereArgs"],
  //     );
  //   } catch (e) {
  //     templates = [];
  //   }
  //   LogUtil.d("模板搜索--已查询到${templates.length}个模板");
  //   List searchResult = [];
  //   for (var element in templates) {
  //     if (element.isContainKeyWords(searchKey)) searchResult.add(element);
  //   }
  //   LogUtil.d("模板搜索--已查询到${searchResult.length}个模板");
  //   List<TemplateData> templateDatas =
  //   List<TemplateData>.from(searchResult.length >= page * 20 ? searchResult.sublist(0, page * 20) : searchResult);
  //   return Future.value(templateDatas);
  //   return [];
  // }

  Future<List<TemplateData>?> _batchProcessing(String searchKey, bool isLoadMore, int page,
      {MyTemplateListModel? model}) async {
    TemplateDataBaseV2 _db = TemplateDataBaseV2();
    if (model != null) {
      List<TemplateData>? templateList = model.list;
      if (!isLoadMore) {
        List<num>? deletedTemplates = model.deletedTemplates;
        if(deletedTemplates != null){
          int delCount = await TemplateDetailDBUtils.batchDeleteTemplateByIds(deletedTemplates);
          LogUtil.d("模板搜索--已删除$delCount个模板");
        }
      }
      try {
        if (templateList!.isNotEmpty) {
          await TemplateDetailDBUtils.batchInsertTemplate(templateList);
        }
      } catch (e) {
        LogUtil.d("模板搜索--已删除");
      }
    }
    List<TemplateData> templates = [];
    templates = await _db.dbSearchTemplates(batchSize: 500 * page);
    LogUtil.d("模板搜索--已查询到${templates.length}个模板");
    List searchResult = [];
    for (var element in templates) {
      if (element.isContainKeyWords(searchKey)) searchResult.add(element);
    }
    LogUtil.d("模板搜索--已查询到${searchResult.length}个模板");
    List<TemplateData> templateDatas =
        List<TemplateData>.from(searchResult.length >= page * 20 ? searchResult.sublist(0, page * 20) : searchResult);
    return Future.value(templateDatas);
  }

  Map _templateSearchWhere(TemplateDataWhere whereType, String searchKey) {
    String where = "";
    List whereArgs = [];
    dynamic userId;
    userId = Application.user == null ? 0 : Application.user!.userId;
    if (Platform.isIOS) {
      where = "(name like ?";
      whereArgs.add("%$searchKey%");
      // where = where + " or barcodeCategoryMap like ?";
      // whereArgs.add("%*$searchKey*%");
      where = where + " or elements like ?)";
      // whereArgs.add("%$searchKey%");
      whereArgs.add('%\"value\":\"%$searchKey%\"%');
      where = where + " and templateType != ?";
      whereArgs.add("1");
      where = where + " and isPrintHistory != ?";
      whereArgs.add("1");
      if (whereType == TemplateDataWhere.search) {
        where = where + " and localType != ? ";
        whereArgs.add("3");
        where = where + " and (userId = ? or userId = ?)";
        whereArgs.add(userId);
        whereArgs.add("");
      } else if (whereType == TemplateDataWhere.delete) {
        where = where + " and userId = ?";
        whereArgs.add(userId);
        where = where + " and localType != ? ";
        whereArgs.add("1");
        where = where + " and localType != ? ";
        whereArgs.add("2");
        where = where + " and localType != ? ";
        whereArgs.add("3");
      }
    } else {
      where = "(NAME like ?";
      whereArgs.add("%$searchKey%");
      // where = where + " or BARCODE_CATEGORY_MAP like ?";
      // whereArgs.add("%*$searchKey*%");
      where = where + " or JSON like ?)";
      whereArgs.add('%\"value\":\"%$searchKey%\"%');
      where = where + " and TEMPLATE_TYPE != ?";
      whereArgs.add("1");
      if (whereType == TemplateDataWhere.search) {
        //TODO 安卓本地模板状态字段
        where = where + " and LOCAL_TYPE != ? ";
        whereArgs.add("3");
        where = where + " and (USER_ID = ? or USER_ID = ?)";
        whereArgs.add(userId);
        whereArgs.add(0);
      } else if (whereType == TemplateDataWhere.delete) {
        where = where + " and USER_ID = ?";
        whereArgs.add(userId);
        where = where + " and LOCAL_TYPE != ? ";
        whereArgs.add("1");
        where = where + " and LOCAL_TYPE != ? ";
        whereArgs.add("2");
        where = where + " and LOCAL_TYPE != ? ";
        whereArgs.add("4");
      }
    }
    return {"where": where, "whereArgs": whereArgs};
  }

  ///获取用户搜索历史(IOS 搜索历史存储方式plist不一致，因此从原生获取数据后删除原数据 保持和安卓统一 sp方式)
  updateHistroyList(state) async {
    sp = await SharedPreferences.getInstance();
    if (Platform.isIOS) {
      List<String> searchHistory = await ToNativeMethodChannel.getNativePersonalTemplateSearchHistory();
      if (searchHistory.isNotEmpty) {
        sp.setStringList('KEY_HISTORY_TEMPLATE', searchHistory);
      }
    }
    var histroyList = sp.getStringList('KEY_HISTORY_TEMPLATE') ?? [];
    if (histroyList.isNotEmpty) {
      searchList.value = histroyList;
    }
  }

  ///清空搜索记录
  clearHistroyList() {
    if (Application.user == null) {
      searchList.value = [];
      sp.setStringList('KEY_HISTORY_TEMPLATE', searchList.value ?? []);
    } else {
      searchList.value = [];
      sp.setStringList('KEY_HISTORY_TEMPLATE', searchList.value ?? []);
    }
  }
}

extension StreamEvent on SearchTemplateManager {
  addEventListen(Function(dynamic event) onData) {
    _streamController.stream.listen(onData);
  }

  sendEvent(String text) {
    _streamController.sink.add(text);
  }
}

extension SaveAndDelete on SearchTemplateManager {
  /// 存储搜索的key
  saveSearchText(state, String text) {
    /// 先进行去重
    searchList.value?.removeWhere((element) => element == text);

    /// 插入到最前面
    searchList.value?.insert(0, text);
    if (searchList.value?.length == 11) {
      searchList.value?.removeLast();
    }
    List<String>? searchHistory = searchList.value;
    sp.setStringList('KEY_HISTORY_TEMPLATE', searchHistory ?? []).then((value) {
      List<String>? history = sp.getStringList('KEY_HISTORY_TEMPLATE');
      LogUtil.d("保存成功");
    });
  }

  /// 清除所有历史记录
  deleteAllHistory() {
    clearHistroyList();
  }

  insertSearchTextToFront(int index) {
    searchList.value?.removeAt(index);
  }

  //刷新搜索页选中模板
  refereshSelectTemplate(int index) {
    List<TemplateData> list = logic.state.myTemplateSelectList;
    TemplateData model = templatesResult[index];
    if (list.any((element) => element.id == model.id)) {
      list.removeWhere((element) => element.id == model.id);
    } else {
      list.add(model);
    }
    update();
    logic.update([RefreshManager.templateSelect, RefreshManager.templateList]);
  }

  reportSceneInfo(Function success) {}
}
