// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'template_detail_model.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetNiimBotTemplateCanvasDataModelCollection on Isar {
  IsarCollection<NiimBotTemplateCanvasDataModel>
      get niimBotTemplateCanvasDataModels => this.collection();
}

const NiimBotTemplateCanvasDataModelSchema = CollectionSchema(
  name: r'NiimBotTemplateCanvasDataModel',
  id: -4698639193659085232,
  properties: {
    r'amazonCodeBeijing': PropertySchema(
      id: 0,
      name: r'amazonCodeBeijing',
      type: IsarType.string,
    ),
    r'amazonCodeWuhan': PropertySchema(
      id: 1,
      name: r'amazonCodeWuhan',
      type: IsarType.string,
    ),
    r'arrowInBoard': PropertySchema(
      id: 2,
      name: r'arrowInBoard',
      type: IsarType.bool,
    ),
    r'backgroundImage': PropertySchema(
      id: 3,
      name: r'backgroundImage',
      type: IsarType.string,
    ),
    r'barcode': PropertySchema(
      id: 4,
      name: r'barcode',
      type: IsarType.string,
    ),
    r'barcodeCategoryMap': PropertySchema(
      id: 5,
      name: r'barcodeCategoryMap',
      type: IsarType.string,
    ),
    r'bindExcel': PropertySchema(
      id: 6,
      name: r'bindExcel',
      type: IsarType.bool,
    ),
    r'cableDirection': PropertySchema(
      id: 7,
      name: r'cableDirection',
      type: IsarType.long,
    ),
    r'cableLength': PropertySchema(
      id: 8,
      name: r'cableLength',
      type: IsarType.double,
    ),
    r'canvasRotate': PropertySchema(
      id: 9,
      name: r'canvasRotate',
      type: IsarType.long,
    ),
    r'commodityCategoryId': PropertySchema(
      id: 10,
      name: r'commodityCategoryId',
      type: IsarType.string,
    ),
    r'commodityInfo': PropertySchema(
      id: 11,
      name: r'commodityInfo',
      type: IsarType.string,
    ),
    r'commodityTemplate': PropertySchema(
      id: 12,
      name: r'commodityTemplate',
      type: IsarType.bool,
    ),
    r'consumableType': PropertySchema(
      id: 13,
      name: r'consumableType',
      type: IsarType.long,
    ),
    r'createTime': PropertySchema(
      id: 14,
      name: r'createTime',
      type: IsarType.string,
    ),
    r'currentPage': PropertySchema(
      id: 15,
      name: r'currentPage',
      type: IsarType.long,
    ),
    r'dataSource': PropertySchema(
      id: 16,
      name: r'dataSource',
      type: IsarType.string,
    ),
    r'dataSourceBindInfo': PropertySchema(
      id: 17,
      name: r'dataSourceBindInfo',
      type: IsarType.string,
    ),
    r'dataSourceModifies': PropertySchema(
      id: 18,
      name: r'dataSourceModifies',
      type: IsarType.string,
    ),
    r'elements': PropertySchema(
      id: 19,
      name: r'elements',
      type: IsarType.string,
    ),
    r'extra': PropertySchema(
      id: 20,
      name: r'extra',
      type: IsarType.string,
    ),
    r'folderId': PropertySchema(
      id: 21,
      name: r'folderId',
      type: IsarType.string,
    ),
    r'goodsCode': PropertySchema(
      id: 22,
      name: r'goodsCode',
      type: IsarType.string,
    ),
    r'hardwareSeriesId': PropertySchema(
      id: 23,
      name: r'hardwareSeriesId',
      type: IsarType.string,
    ),
    r'hardwareSeriesName': PropertySchema(
      id: 24,
      name: r'hardwareSeriesName',
      type: IsarType.string,
    ),
    r'hasVipRes': PropertySchema(
      id: 25,
      name: r'hasVipRes',
      type: IsarType.bool,
    ),
    r'height': PropertySchema(
      id: 26,
      name: r'height',
      type: IsarType.double,
    ),
    r'industryId': PropertySchema(
      id: 27,
      name: r'industryId',
      type: IsarType.string,
    ),
    r'inputAreas': PropertySchema(
      id: 28,
      name: r'inputAreas',
      type: IsarType.string,
    ),
    r'isCable': PropertySchema(
      id: 29,
      name: r'isCable',
      type: IsarType.bool,
    ),
    r'isDelete': PropertySchema(
      id: 30,
      name: r'isDelete',
      type: IsarType.bool,
    ),
    r'isEdited': PropertySchema(
      id: 31,
      name: r'isEdited',
      type: IsarType.bool,
    ),
    r'isLabel': PropertySchema(
      id: 32,
      name: r'isLabel',
      type: IsarType.bool,
    ),
    r'isPrintHistory': PropertySchema(
      id: 33,
      name: r'isPrintHistory',
      type: IsarType.bool,
    ),
    r'labelId': PropertySchema(
      id: 34,
      name: r'labelId',
      type: IsarType.string,
    ),
    r'labelNames': PropertySchema(
      id: 35,
      name: r'labelNames',
      type: IsarType.string,
    ),
    r'layoutSchema': PropertySchema(
      id: 36,
      name: r'layoutSchema',
      type: IsarType.string,
    ),
    r'localBackground': PropertySchema(
      id: 37,
      name: r'localBackground',
      type: IsarType.string,
    ),
    r'localThumbnail': PropertySchema(
      id: 38,
      name: r'localThumbnail',
      type: IsarType.string,
    ),
    r'localType': PropertySchema(
      id: 39,
      name: r'localType',
      type: IsarType.long,
    ),
    r'machineId': PropertySchema(
      id: 40,
      name: r'machineId',
      type: IsarType.string,
    ),
    r'machineName': PropertySchema(
      id: 41,
      name: r'machineName',
      type: IsarType.string,
    ),
    r'margin': PropertySchema(
      id: 42,
      name: r'margin',
      type: IsarType.string,
    ),
    r'materialModelSn': PropertySchema(
      id: 43,
      name: r'materialModelSn',
      type: IsarType.string,
    ),
    r'multipleBackIndex': PropertySchema(
      id: 44,
      name: r'multipleBackIndex',
      type: IsarType.long,
    ),
    r'name': PropertySchema(
      id: 45,
      name: r'name',
      type: IsarType.string,
    ),
    r'names': PropertySchema(
      id: 46,
      name: r'names',
      type: IsarType.string,
    ),
    r'originTemplateId': PropertySchema(
      id: 47,
      name: r'originTemplateId',
      type: IsarType.string,
    ),
    r'paperColor': PropertySchema(
      id: 48,
      name: r'paperColor',
      type: IsarType.string,
    ),
    r'paperType': PropertySchema(
      id: 49,
      name: r'paperType',
      type: IsarType.long,
    ),
    r'platformCode': PropertySchema(
      id: 50,
      name: r'platformCode',
      type: IsarType.string,
    ),
    r'rotate': PropertySchema(
      id: 51,
      name: r'rotate',
      type: IsarType.long,
    ),
    r'sourceId': PropertySchema(
      id: 52,
      name: r'sourceId',
      type: IsarType.string,
    ),
    r'sparedCode': PropertySchema(
      id: 53,
      name: r'sparedCode',
      type: IsarType.string,
    ),
    r'supportedEditors': PropertySchema(
      id: 54,
      name: r'supportedEditors',
      type: IsarType.string,
    ),
    r'templateClass': PropertySchema(
      id: 55,
      name: r'templateClass',
      type: IsarType.long,
    ),
    r'templateId': PropertySchema(
      id: 56,
      name: r'templateId',
      type: IsarType.string,
    ),
    r'templateType': PropertySchema(
      id: 57,
      name: r'templateType',
      type: IsarType.string,
    ),
    r'templateVersion': PropertySchema(
      id: 58,
      name: r'templateVersion',
      type: IsarType.string,
    ),
    r'thumbnail': PropertySchema(
      id: 59,
      name: r'thumbnail',
      type: IsarType.string,
    ),
    r'totalPage': PropertySchema(
      id: 60,
      name: r'totalPage',
      type: IsarType.long,
    ),
    r'updateTime': PropertySchema(
      id: 61,
      name: r'updateTime',
      type: IsarType.string,
    ),
    r'userId': PropertySchema(
      id: 62,
      name: r'userId',
      type: IsarType.string,
    ),
    r'values': PropertySchema(
      id: 63,
      name: r'values',
      type: IsarType.string,
    ),
    r'vip': PropertySchema(
      id: 64,
      name: r'vip',
      type: IsarType.bool,
    ),
    r'virtualBarCode': PropertySchema(
      id: 65,
      name: r'virtualBarCode',
      type: IsarType.string,
    ),
    r'width': PropertySchema(
      id: 66,
      name: r'width',
      type: IsarType.double,
    )
  },
  estimateSize: _niimBotTemplateCanvasDataModelEstimateSize,
  serialize: _niimBotTemplateCanvasDataModelSerialize,
  deserialize: _niimBotTemplateCanvasDataModelDeserialize,
  deserializeProp: _niimBotTemplateCanvasDataModelDeserializeProp,
  idName: r'isarId',
  indexes: {
    r'templateId': IndexSchema(
      id: -5352721467389445085,
      name: r'templateId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'templateId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'userId': IndexSchema(
      id: -2005826577402374815,
      name: r'userId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'userId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'labelId': IndexSchema(
      id: -900959018140911029,
      name: r'labelId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'labelId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'sourceId': IndexSchema(
      id: 2155220942429093580,
      name: r'sourceId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'sourceId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'elements': IndexSchema(
      id: -472664845341263452,
      name: r'elements',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'elements',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'name': IndexSchema(
      id: 879695947855722453,
      name: r'name',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'name',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'folderId': IndexSchema(
      id: 6340065978996931043,
      name: r'folderId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'folderId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'isDelete': IndexSchema(
      id: 396795473387598781,
      name: r'isDelete',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'isDelete',
          type: IndexType.value,
          caseSensitive: false,
        )
      ],
    ),
    r'updateTime': IndexSchema(
      id: 397922507239516479,
      name: r'updateTime',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'updateTime',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _niimBotTemplateCanvasDataModelGetId,
  getLinks: _niimBotTemplateCanvasDataModelGetLinks,
  attach: _niimBotTemplateCanvasDataModelAttach,
  version: '3.1.0+1',
);

int _niimBotTemplateCanvasDataModelEstimateSize(
  NiimBotTemplateCanvasDataModel object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.amazonCodeBeijing;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.amazonCodeWuhan;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.backgroundImage;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.barcode;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.barcodeCategoryMap;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.commodityCategoryId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.commodityInfo;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.createTime;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.dataSource;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.dataSourceBindInfo;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.dataSourceModifies;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.elements;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.extra;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.folderId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.goodsCode;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.hardwareSeriesId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.hardwareSeriesName;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.industryId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.inputAreas;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.labelId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.labelNames;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.layoutSchema;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.localBackground;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.localThumbnail;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.machineId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.machineName;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.margin;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.materialModelSn;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.name;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.names;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.originTemplateId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.paperColor;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.platformCode;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.sourceId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.sparedCode;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.supportedEditors;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.templateId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.templateType;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.templateVersion;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.thumbnail;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.updateTime;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.userId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.values;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.virtualBarCode;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _niimBotTemplateCanvasDataModelSerialize(
  NiimBotTemplateCanvasDataModel object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.amazonCodeBeijing);
  writer.writeString(offsets[1], object.amazonCodeWuhan);
  writer.writeBool(offsets[2], object.arrowInBoard);
  writer.writeString(offsets[3], object.backgroundImage);
  writer.writeString(offsets[4], object.barcode);
  writer.writeString(offsets[5], object.barcodeCategoryMap);
  writer.writeBool(offsets[6], object.bindExcel);
  writer.writeLong(offsets[7], object.cableDirection);
  writer.writeDouble(offsets[8], object.cableLength);
  writer.writeLong(offsets[9], object.canvasRotate);
  writer.writeString(offsets[10], object.commodityCategoryId);
  writer.writeString(offsets[11], object.commodityInfo);
  writer.writeBool(offsets[12], object.commodityTemplate);
  writer.writeLong(offsets[13], object.consumableType);
  writer.writeString(offsets[14], object.createTime);
  writer.writeLong(offsets[15], object.currentPage);
  writer.writeString(offsets[16], object.dataSource);
  writer.writeString(offsets[17], object.dataSourceBindInfo);
  writer.writeString(offsets[18], object.dataSourceModifies);
  writer.writeString(offsets[19], object.elements);
  writer.writeString(offsets[20], object.extra);
  writer.writeString(offsets[21], object.folderId);
  writer.writeString(offsets[22], object.goodsCode);
  writer.writeString(offsets[23], object.hardwareSeriesId);
  writer.writeString(offsets[24], object.hardwareSeriesName);
  writer.writeBool(offsets[25], object.hasVipRes);
  writer.writeDouble(offsets[26], object.height);
  writer.writeString(offsets[27], object.industryId);
  writer.writeString(offsets[28], object.inputAreas);
  writer.writeBool(offsets[29], object.isCable);
  writer.writeBool(offsets[30], object.isDelete);
  writer.writeBool(offsets[31], object.isEdited);
  writer.writeBool(offsets[32], object.isLabel);
  writer.writeBool(offsets[33], object.isPrintHistory);
  writer.writeString(offsets[34], object.labelId);
  writer.writeString(offsets[35], object.labelNames);
  writer.writeString(offsets[36], object.layoutSchema);
  writer.writeString(offsets[37], object.localBackground);
  writer.writeString(offsets[38], object.localThumbnail);
  writer.writeLong(offsets[39], object.localType);
  writer.writeString(offsets[40], object.machineId);
  writer.writeString(offsets[41], object.machineName);
  writer.writeString(offsets[42], object.margin);
  writer.writeString(offsets[43], object.materialModelSn);
  writer.writeLong(offsets[44], object.multipleBackIndex);
  writer.writeString(offsets[45], object.name);
  writer.writeString(offsets[46], object.names);
  writer.writeString(offsets[47], object.originTemplateId);
  writer.writeString(offsets[48], object.paperColor);
  writer.writeLong(offsets[49], object.paperType);
  writer.writeString(offsets[50], object.platformCode);
  writer.writeLong(offsets[51], object.rotate);
  writer.writeString(offsets[52], object.sourceId);
  writer.writeString(offsets[53], object.sparedCode);
  writer.writeString(offsets[54], object.supportedEditors);
  writer.writeLong(offsets[55], object.templateClass);
  writer.writeString(offsets[56], object.templateId);
  writer.writeString(offsets[57], object.templateType);
  writer.writeString(offsets[58], object.templateVersion);
  writer.writeString(offsets[59], object.thumbnail);
  writer.writeLong(offsets[60], object.totalPage);
  writer.writeString(offsets[61], object.updateTime);
  writer.writeString(offsets[62], object.userId);
  writer.writeString(offsets[63], object.values);
  writer.writeBool(offsets[64], object.vip);
  writer.writeString(offsets[65], object.virtualBarCode);
  writer.writeDouble(offsets[66], object.width);
}

NiimBotTemplateCanvasDataModel _niimBotTemplateCanvasDataModelDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = NiimBotTemplateCanvasDataModel(
    amazonCodeBeijing: reader.readStringOrNull(offsets[0]),
    amazonCodeWuhan: reader.readStringOrNull(offsets[1]),
    arrowInBoard: reader.readBoolOrNull(offsets[2]),
    backgroundImage: reader.readStringOrNull(offsets[3]),
    barcode: reader.readStringOrNull(offsets[4]),
    barcodeCategoryMap: reader.readStringOrNull(offsets[5]),
    cableDirection: reader.readLongOrNull(offsets[7]),
    cableLength: reader.readDoubleOrNull(offsets[8]),
    canvasRotate: reader.readLongOrNull(offsets[9]),
    commodityCategoryId: reader.readStringOrNull(offsets[10]),
    commodityInfo: reader.readStringOrNull(offsets[11]),
    commodityTemplate: reader.readBoolOrNull(offsets[12]),
    consumableType: reader.readLongOrNull(offsets[13]),
    createTime: reader.readStringOrNull(offsets[14]),
    currentPage: reader.readLongOrNull(offsets[15]),
    dataSource: reader.readStringOrNull(offsets[16]),
    dataSourceBindInfo: reader.readStringOrNull(offsets[17]),
    dataSourceModifies: reader.readStringOrNull(offsets[18]),
    elements: reader.readStringOrNull(offsets[19]),
    extra: reader.readStringOrNull(offsets[20]),
    folderId: reader.readStringOrNull(offsets[21]),
    goodsCode: reader.readStringOrNull(offsets[22]),
    hardwareSeriesId: reader.readStringOrNull(offsets[23]),
    hardwareSeriesName: reader.readStringOrNull(offsets[24]),
    hasVipRes: reader.readBoolOrNull(offsets[25]),
    height: reader.readDoubleOrNull(offsets[26]),
    industryId: reader.readStringOrNull(offsets[27]),
    inputAreas: reader.readStringOrNull(offsets[28]),
    isCable: reader.readBoolOrNull(offsets[29]),
    isDelete: reader.readBoolOrNull(offsets[30]),
    isEdited: reader.readBoolOrNull(offsets[31]),
    isLabel: reader.readBoolOrNull(offsets[32]),
    isPrintHistory: reader.readBoolOrNull(offsets[33]),
    labelId: reader.readStringOrNull(offsets[34]),
    labelNames: reader.readStringOrNull(offsets[35]),
    layoutSchema: reader.readStringOrNull(offsets[36]),
    localBackground: reader.readStringOrNull(offsets[37]),
    localThumbnail: reader.readStringOrNull(offsets[38]),
    localType: reader.readLongOrNull(offsets[39]),
    machineId: reader.readStringOrNull(offsets[40]),
    machineName: reader.readStringOrNull(offsets[41]),
    margin: reader.readStringOrNull(offsets[42]),
    materialModelSn: reader.readStringOrNull(offsets[43]),
    multipleBackIndex: reader.readLongOrNull(offsets[44]),
    name: reader.readStringOrNull(offsets[45]),
    names: reader.readStringOrNull(offsets[46]),
    originTemplateId: reader.readStringOrNull(offsets[47]),
    paperColor: reader.readStringOrNull(offsets[48]),
    paperType: reader.readLongOrNull(offsets[49]),
    platformCode: reader.readStringOrNull(offsets[50]),
    rotate: reader.readLongOrNull(offsets[51]),
    sourceId: reader.readStringOrNull(offsets[52]),
    sparedCode: reader.readStringOrNull(offsets[53]),
    supportedEditors: reader.readStringOrNull(offsets[54]),
    templateClass: reader.readLongOrNull(offsets[55]),
    templateId: reader.readStringOrNull(offsets[56]),
    templateType: reader.readStringOrNull(offsets[57]),
    templateVersion: reader.readStringOrNull(offsets[58]),
    thumbnail: reader.readStringOrNull(offsets[59]),
    totalPage: reader.readLongOrNull(offsets[60]),
    updateTime: reader.readStringOrNull(offsets[61]),
    userId: reader.readStringOrNull(offsets[62]),
    values: reader.readStringOrNull(offsets[63]),
    vip: reader.readBoolOrNull(offsets[64]),
    width: reader.readDoubleOrNull(offsets[66]),
  );
  object.bindExcel = reader.readBoolOrNull(offsets[6]);
  object.virtualBarCode = reader.readStringOrNull(offsets[65]);
  return object;
}

P _niimBotTemplateCanvasDataModelDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readBoolOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (reader.readBoolOrNull(offset)) as P;
    case 7:
      return (reader.readLongOrNull(offset)) as P;
    case 8:
      return (reader.readDoubleOrNull(offset)) as P;
    case 9:
      return (reader.readLongOrNull(offset)) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (reader.readStringOrNull(offset)) as P;
    case 12:
      return (reader.readBoolOrNull(offset)) as P;
    case 13:
      return (reader.readLongOrNull(offset)) as P;
    case 14:
      return (reader.readStringOrNull(offset)) as P;
    case 15:
      return (reader.readLongOrNull(offset)) as P;
    case 16:
      return (reader.readStringOrNull(offset)) as P;
    case 17:
      return (reader.readStringOrNull(offset)) as P;
    case 18:
      return (reader.readStringOrNull(offset)) as P;
    case 19:
      return (reader.readStringOrNull(offset)) as P;
    case 20:
      return (reader.readStringOrNull(offset)) as P;
    case 21:
      return (reader.readStringOrNull(offset)) as P;
    case 22:
      return (reader.readStringOrNull(offset)) as P;
    case 23:
      return (reader.readStringOrNull(offset)) as P;
    case 24:
      return (reader.readStringOrNull(offset)) as P;
    case 25:
      return (reader.readBoolOrNull(offset)) as P;
    case 26:
      return (reader.readDoubleOrNull(offset)) as P;
    case 27:
      return (reader.readStringOrNull(offset)) as P;
    case 28:
      return (reader.readStringOrNull(offset)) as P;
    case 29:
      return (reader.readBoolOrNull(offset)) as P;
    case 30:
      return (reader.readBoolOrNull(offset)) as P;
    case 31:
      return (reader.readBoolOrNull(offset)) as P;
    case 32:
      return (reader.readBoolOrNull(offset)) as P;
    case 33:
      return (reader.readBoolOrNull(offset)) as P;
    case 34:
      return (reader.readStringOrNull(offset)) as P;
    case 35:
      return (reader.readStringOrNull(offset)) as P;
    case 36:
      return (reader.readStringOrNull(offset)) as P;
    case 37:
      return (reader.readStringOrNull(offset)) as P;
    case 38:
      return (reader.readStringOrNull(offset)) as P;
    case 39:
      return (reader.readLongOrNull(offset)) as P;
    case 40:
      return (reader.readStringOrNull(offset)) as P;
    case 41:
      return (reader.readStringOrNull(offset)) as P;
    case 42:
      return (reader.readStringOrNull(offset)) as P;
    case 43:
      return (reader.readStringOrNull(offset)) as P;
    case 44:
      return (reader.readLongOrNull(offset)) as P;
    case 45:
      return (reader.readStringOrNull(offset)) as P;
    case 46:
      return (reader.readStringOrNull(offset)) as P;
    case 47:
      return (reader.readStringOrNull(offset)) as P;
    case 48:
      return (reader.readStringOrNull(offset)) as P;
    case 49:
      return (reader.readLongOrNull(offset)) as P;
    case 50:
      return (reader.readStringOrNull(offset)) as P;
    case 51:
      return (reader.readLongOrNull(offset)) as P;
    case 52:
      return (reader.readStringOrNull(offset)) as P;
    case 53:
      return (reader.readStringOrNull(offset)) as P;
    case 54:
      return (reader.readStringOrNull(offset)) as P;
    case 55:
      return (reader.readLongOrNull(offset)) as P;
    case 56:
      return (reader.readStringOrNull(offset)) as P;
    case 57:
      return (reader.readStringOrNull(offset)) as P;
    case 58:
      return (reader.readStringOrNull(offset)) as P;
    case 59:
      return (reader.readStringOrNull(offset)) as P;
    case 60:
      return (reader.readLongOrNull(offset)) as P;
    case 61:
      return (reader.readStringOrNull(offset)) as P;
    case 62:
      return (reader.readStringOrNull(offset)) as P;
    case 63:
      return (reader.readStringOrNull(offset)) as P;
    case 64:
      return (reader.readBoolOrNull(offset)) as P;
    case 65:
      return (reader.readStringOrNull(offset)) as P;
    case 66:
      return (reader.readDoubleOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _niimBotTemplateCanvasDataModelGetId(NiimBotTemplateCanvasDataModel object) {
  return object.isarId;
}

List<IsarLinkBase<dynamic>> _niimBotTemplateCanvasDataModelGetLinks(
    NiimBotTemplateCanvasDataModel object) {
  return [];
}

void _niimBotTemplateCanvasDataModelAttach(IsarCollection<dynamic> col, Id id,
    NiimBotTemplateCanvasDataModel object) {}

extension NiimBotTemplateCanvasDataModelQueryWhereSort on QueryBuilder<
    NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel, QWhere> {
  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhere> anyIsarId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhere> anyIsDelete() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'isDelete'),
      );
    });
  }
}

extension NiimBotTemplateCanvasDataModelQueryWhere on QueryBuilder<
    NiimBotTemplateCanvasDataModel,
    NiimBotTemplateCanvasDataModel,
    QWhereClause> {
  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> isarIdEqualTo(Id isarId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: isarId,
        upper: isarId,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> isarIdNotEqualTo(Id isarId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: isarId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: isarId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: isarId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: isarId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> isarIdGreaterThan(Id isarId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: isarId, includeLower: include),
      );
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> isarIdLessThan(Id isarId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: isarId, includeUpper: include),
      );
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> isarIdBetween(
    Id lowerIsarId,
    Id upperIsarId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerIsarId,
        includeLower: includeLower,
        upper: upperIsarId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> templateIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'templateId',
        value: [null],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> templateIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'templateId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> templateIdEqualTo(String? templateId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'templateId',
        value: [templateId],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> templateIdNotEqualTo(String? templateId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'templateId',
              lower: [],
              upper: [templateId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'templateId',
              lower: [templateId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'templateId',
              lower: [templateId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'templateId',
              lower: [],
              upper: [templateId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> userIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'userId',
        value: [null],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> userIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'userId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> userIdEqualTo(String? userId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'userId',
        value: [userId],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> userIdNotEqualTo(String? userId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'userId',
              lower: [],
              upper: [userId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'userId',
              lower: [userId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'userId',
              lower: [userId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'userId',
              lower: [],
              upper: [userId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> labelIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'labelId',
        value: [null],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> labelIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'labelId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> labelIdEqualTo(String? labelId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'labelId',
        value: [labelId],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> labelIdNotEqualTo(String? labelId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'labelId',
              lower: [],
              upper: [labelId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'labelId',
              lower: [labelId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'labelId',
              lower: [labelId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'labelId',
              lower: [],
              upper: [labelId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> sourceIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'sourceId',
        value: [null],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> sourceIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'sourceId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> sourceIdEqualTo(String? sourceId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'sourceId',
        value: [sourceId],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> sourceIdNotEqualTo(String? sourceId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'sourceId',
              lower: [],
              upper: [sourceId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'sourceId',
              lower: [sourceId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'sourceId',
              lower: [sourceId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'sourceId',
              lower: [],
              upper: [sourceId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> elementsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'elements',
        value: [null],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> elementsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'elements',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> elementsEqualTo(String? elements) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'elements',
        value: [elements],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> elementsNotEqualTo(String? elements) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'elements',
              lower: [],
              upper: [elements],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'elements',
              lower: [elements],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'elements',
              lower: [elements],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'elements',
              lower: [],
              upper: [elements],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> nameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'name',
        value: [null],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> nameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'name',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> nameEqualTo(String? name) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'name',
        value: [name],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> nameNotEqualTo(String? name) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [],
              upper: [name],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [name],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [name],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [],
              upper: [name],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> folderIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'folderId',
        value: [null],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> folderIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'folderId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> folderIdEqualTo(String? folderId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'folderId',
        value: [folderId],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> folderIdNotEqualTo(String? folderId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'folderId',
              lower: [],
              upper: [folderId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'folderId',
              lower: [folderId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'folderId',
              lower: [folderId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'folderId',
              lower: [],
              upper: [folderId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> isDeleteIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'isDelete',
        value: [null],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> isDeleteIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'isDelete',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> isDeleteEqualTo(bool? isDelete) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'isDelete',
        value: [isDelete],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> isDeleteNotEqualTo(bool? isDelete) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'isDelete',
              lower: [],
              upper: [isDelete],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'isDelete',
              lower: [isDelete],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'isDelete',
              lower: [isDelete],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'isDelete',
              lower: [],
              upper: [isDelete],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> updateTimeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'updateTime',
        value: [null],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> updateTimeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'updateTime',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> updateTimeEqualTo(String? updateTime) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'updateTime',
        value: [updateTime],
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterWhereClause> updateTimeNotEqualTo(String? updateTime) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'updateTime',
              lower: [],
              upper: [updateTime],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'updateTime',
              lower: [updateTime],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'updateTime',
              lower: [updateTime],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'updateTime',
              lower: [],
              upper: [updateTime],
              includeUpper: false,
            ));
      }
    });
  }
}

extension NiimBotTemplateCanvasDataModelQueryFilter on QueryBuilder<
    NiimBotTemplateCanvasDataModel,
    NiimBotTemplateCanvasDataModel,
    QFilterCondition> {
  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeBeijingIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'amazonCodeBeijing',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeBeijingIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'amazonCodeBeijing',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeBeijingEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'amazonCodeBeijing',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeBeijingGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'amazonCodeBeijing',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeBeijingLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'amazonCodeBeijing',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeBeijingBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'amazonCodeBeijing',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeBeijingStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'amazonCodeBeijing',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeBeijingEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'amazonCodeBeijing',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      amazonCodeBeijingContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'amazonCodeBeijing',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      amazonCodeBeijingMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'amazonCodeBeijing',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeBeijingIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'amazonCodeBeijing',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeBeijingIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'amazonCodeBeijing',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeWuhanIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'amazonCodeWuhan',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeWuhanIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'amazonCodeWuhan',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeWuhanEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'amazonCodeWuhan',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeWuhanGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'amazonCodeWuhan',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeWuhanLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'amazonCodeWuhan',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeWuhanBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'amazonCodeWuhan',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeWuhanStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'amazonCodeWuhan',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeWuhanEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'amazonCodeWuhan',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      amazonCodeWuhanContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'amazonCodeWuhan',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      amazonCodeWuhanMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'amazonCodeWuhan',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeWuhanIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'amazonCodeWuhan',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> amazonCodeWuhanIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'amazonCodeWuhan',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> arrowInBoardIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'arrowInBoard',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> arrowInBoardIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'arrowInBoard',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> arrowInBoardEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'arrowInBoard',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> backgroundImageIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'backgroundImage',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> backgroundImageIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'backgroundImage',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> backgroundImageEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'backgroundImage',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> backgroundImageGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'backgroundImage',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> backgroundImageLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'backgroundImage',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> backgroundImageBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'backgroundImage',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> backgroundImageStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'backgroundImage',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> backgroundImageEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'backgroundImage',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      backgroundImageContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'backgroundImage',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      backgroundImageMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'backgroundImage',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> backgroundImageIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'backgroundImage',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> backgroundImageIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'backgroundImage',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'barcode',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'barcode',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'barcode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'barcode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'barcode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'barcode',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'barcode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'barcode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      barcodeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'barcode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      barcodeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'barcode',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'barcode',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'barcode',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeCategoryMapIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'barcodeCategoryMap',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeCategoryMapIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'barcodeCategoryMap',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeCategoryMapEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'barcodeCategoryMap',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeCategoryMapGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'barcodeCategoryMap',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeCategoryMapLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'barcodeCategoryMap',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeCategoryMapBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'barcodeCategoryMap',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeCategoryMapStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'barcodeCategoryMap',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeCategoryMapEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'barcodeCategoryMap',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      barcodeCategoryMapContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'barcodeCategoryMap',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      barcodeCategoryMapMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'barcodeCategoryMap',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeCategoryMapIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'barcodeCategoryMap',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> barcodeCategoryMapIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'barcodeCategoryMap',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> bindExcelIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'bindExcel',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> bindExcelIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'bindExcel',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> bindExcelEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'bindExcel',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> cableDirectionIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cableDirection',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> cableDirectionIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cableDirection',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> cableDirectionEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cableDirection',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> cableDirectionGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cableDirection',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> cableDirectionLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cableDirection',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> cableDirectionBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cableDirection',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> cableLengthIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cableLength',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> cableLengthIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cableLength',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> cableLengthEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cableLength',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> cableLengthGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cableLength',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> cableLengthLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cableLength',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> cableLengthBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cableLength',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> canvasRotateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'canvasRotate',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> canvasRotateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'canvasRotate',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> canvasRotateEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'canvasRotate',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> canvasRotateGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'canvasRotate',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> canvasRotateLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'canvasRotate',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> canvasRotateBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'canvasRotate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityCategoryIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'commodityCategoryId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityCategoryIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'commodityCategoryId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityCategoryIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'commodityCategoryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityCategoryIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'commodityCategoryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityCategoryIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'commodityCategoryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityCategoryIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'commodityCategoryId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityCategoryIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'commodityCategoryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityCategoryIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'commodityCategoryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      commodityCategoryIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'commodityCategoryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      commodityCategoryIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'commodityCategoryId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityCategoryIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'commodityCategoryId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityCategoryIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'commodityCategoryId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityInfoIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'commodityInfo',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityInfoIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'commodityInfo',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityInfoEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'commodityInfo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityInfoGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'commodityInfo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityInfoLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'commodityInfo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityInfoBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'commodityInfo',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityInfoStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'commodityInfo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityInfoEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'commodityInfo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      commodityInfoContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'commodityInfo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      commodityInfoMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'commodityInfo',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityInfoIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'commodityInfo',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityInfoIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'commodityInfo',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityTemplateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'commodityTemplate',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityTemplateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'commodityTemplate',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> commodityTemplateEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'commodityTemplate',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> consumableTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'consumableType',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> consumableTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'consumableType',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> consumableTypeEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'consumableType',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> consumableTypeGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'consumableType',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> consumableTypeLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'consumableType',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> consumableTypeBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'consumableType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> createTimeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createTime',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> createTimeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createTime',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> createTimeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> createTimeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> createTimeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> createTimeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> createTimeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'createTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> createTimeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'createTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      createTimeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'createTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      createTimeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'createTime',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> createTimeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createTime',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> createTimeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'createTime',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> currentPageIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'currentPage',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> currentPageIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'currentPage',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> currentPageEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'currentPage',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> currentPageGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'currentPage',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> currentPageLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'currentPage',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> currentPageBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'currentPage',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dataSource',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dataSource',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dataSource',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dataSource',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dataSource',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dataSource',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'dataSource',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'dataSource',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      dataSourceContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'dataSource',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      dataSourceMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'dataSource',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dataSource',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'dataSource',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceBindInfoIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dataSourceBindInfo',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceBindInfoIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dataSourceBindInfo',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceBindInfoEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dataSourceBindInfo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceBindInfoGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dataSourceBindInfo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceBindInfoLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dataSourceBindInfo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceBindInfoBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dataSourceBindInfo',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceBindInfoStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'dataSourceBindInfo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceBindInfoEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'dataSourceBindInfo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      dataSourceBindInfoContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'dataSourceBindInfo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      dataSourceBindInfoMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'dataSourceBindInfo',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceBindInfoIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dataSourceBindInfo',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceBindInfoIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'dataSourceBindInfo',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceModifiesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dataSourceModifies',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceModifiesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dataSourceModifies',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceModifiesEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dataSourceModifies',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceModifiesGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dataSourceModifies',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceModifiesLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dataSourceModifies',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceModifiesBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dataSourceModifies',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceModifiesStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'dataSourceModifies',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceModifiesEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'dataSourceModifies',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      dataSourceModifiesContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'dataSourceModifies',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      dataSourceModifiesMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'dataSourceModifies',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceModifiesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dataSourceModifies',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> dataSourceModifiesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'dataSourceModifies',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> elementsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'elements',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> elementsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'elements',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> elementsEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'elements',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> elementsGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'elements',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> elementsLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'elements',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> elementsBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'elements',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> elementsStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'elements',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> elementsEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'elements',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      elementsContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'elements',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      elementsMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'elements',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> elementsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'elements',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> elementsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'elements',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> extraIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'extra',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> extraIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'extra',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> extraEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'extra',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> extraGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'extra',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> extraLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'extra',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> extraBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'extra',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> extraStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'extra',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> extraEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'extra',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      extraContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'extra',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      extraMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'extra',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> extraIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'extra',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> extraIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'extra',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> folderIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'folderId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> folderIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'folderId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> folderIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'folderId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> folderIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'folderId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> folderIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'folderId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> folderIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'folderId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> folderIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'folderId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> folderIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'folderId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      folderIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'folderId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      folderIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'folderId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> folderIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'folderId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> folderIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'folderId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> goodsCodeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'goodsCode',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> goodsCodeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'goodsCode',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> goodsCodeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'goodsCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> goodsCodeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'goodsCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> goodsCodeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'goodsCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> goodsCodeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'goodsCode',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> goodsCodeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'goodsCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> goodsCodeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'goodsCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      goodsCodeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'goodsCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      goodsCodeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'goodsCode',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> goodsCodeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'goodsCode',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> goodsCodeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'goodsCode',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'hardwareSeriesId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'hardwareSeriesId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'hardwareSeriesId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'hardwareSeriesId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'hardwareSeriesId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'hardwareSeriesId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'hardwareSeriesId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'hardwareSeriesId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      hardwareSeriesIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'hardwareSeriesId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      hardwareSeriesIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'hardwareSeriesId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'hardwareSeriesId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'hardwareSeriesId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesNameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'hardwareSeriesName',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesNameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'hardwareSeriesName',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesNameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'hardwareSeriesName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesNameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'hardwareSeriesName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesNameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'hardwareSeriesName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesNameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'hardwareSeriesName',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesNameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'hardwareSeriesName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesNameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'hardwareSeriesName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      hardwareSeriesNameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'hardwareSeriesName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      hardwareSeriesNameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'hardwareSeriesName',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesNameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'hardwareSeriesName',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hardwareSeriesNameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'hardwareSeriesName',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hasVipResIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'hasVipRes',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hasVipResIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'hasVipRes',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> hasVipResEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'hasVipRes',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> heightIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'height',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> heightIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'height',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> heightEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'height',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> heightGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'height',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> heightLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'height',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> heightBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'height',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> industryIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'industryId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> industryIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'industryId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> industryIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'industryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> industryIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'industryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> industryIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'industryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> industryIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'industryId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> industryIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'industryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> industryIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'industryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      industryIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'industryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      industryIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'industryId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> industryIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'industryId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> industryIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'industryId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> inputAreasIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'inputAreas',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> inputAreasIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'inputAreas',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> inputAreasEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'inputAreas',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> inputAreasGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'inputAreas',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> inputAreasLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'inputAreas',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> inputAreasBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'inputAreas',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> inputAreasStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'inputAreas',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> inputAreasEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'inputAreas',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      inputAreasContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'inputAreas',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      inputAreasMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'inputAreas',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> inputAreasIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'inputAreas',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> inputAreasIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'inputAreas',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isCableIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'isCable',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isCableIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'isCable',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isCableEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isCable',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isDeleteIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'isDelete',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isDeleteIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'isDelete',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isDeleteEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isDelete',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isEditedIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'isEdited',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isEditedIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'isEdited',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isEditedEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isEdited',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isLabelIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'isLabel',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isLabelIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'isLabel',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isLabelEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isLabel',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isPrintHistoryIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'isPrintHistory',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isPrintHistoryIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'isPrintHistory',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isPrintHistoryEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isPrintHistory',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isarIdEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isarId',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isarIdGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'isarId',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isarIdLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'isarId',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> isarIdBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'isarId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'labelId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'labelId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'labelId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'labelId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'labelId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'labelId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'labelId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'labelId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      labelIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'labelId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      labelIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'labelId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'labelId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'labelId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelNamesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'labelNames',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelNamesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'labelNames',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelNamesEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'labelNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelNamesGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'labelNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelNamesLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'labelNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelNamesBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'labelNames',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelNamesStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'labelNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelNamesEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'labelNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      labelNamesContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'labelNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      labelNamesMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'labelNames',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelNamesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'labelNames',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> labelNamesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'labelNames',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> layoutSchemaIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'layoutSchema',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> layoutSchemaIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'layoutSchema',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> layoutSchemaEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'layoutSchema',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> layoutSchemaGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'layoutSchema',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> layoutSchemaLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'layoutSchema',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> layoutSchemaBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'layoutSchema',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> layoutSchemaStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'layoutSchema',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> layoutSchemaEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'layoutSchema',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      layoutSchemaContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'layoutSchema',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      layoutSchemaMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'layoutSchema',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> layoutSchemaIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'layoutSchema',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> layoutSchemaIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'layoutSchema',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localBackgroundIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'localBackground',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localBackgroundIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'localBackground',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localBackgroundEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localBackground',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localBackgroundGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localBackground',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localBackgroundLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localBackground',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localBackgroundBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localBackground',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localBackgroundStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'localBackground',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localBackgroundEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'localBackground',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      localBackgroundContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'localBackground',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      localBackgroundMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'localBackground',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localBackgroundIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localBackground',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localBackgroundIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'localBackground',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localThumbnailIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'localThumbnail',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localThumbnailIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'localThumbnail',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localThumbnailEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localThumbnail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localThumbnailGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localThumbnail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localThumbnailLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localThumbnail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localThumbnailBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localThumbnail',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localThumbnailStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'localThumbnail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localThumbnailEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'localThumbnail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      localThumbnailContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'localThumbnail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      localThumbnailMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'localThumbnail',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localThumbnailIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localThumbnail',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localThumbnailIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'localThumbnail',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'localType',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'localType',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localTypeEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localType',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localTypeGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localType',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localTypeLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localType',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> localTypeBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'machineId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'machineId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'machineId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'machineId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'machineId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'machineId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'machineId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'machineId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      machineIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'machineId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      machineIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'machineId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'machineId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'machineId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineNameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'machineName',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineNameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'machineName',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineNameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'machineName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineNameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'machineName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineNameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'machineName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineNameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'machineName',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineNameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'machineName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineNameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'machineName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      machineNameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'machineName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      machineNameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'machineName',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineNameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'machineName',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> machineNameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'machineName',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> marginIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'margin',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> marginIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'margin',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> marginEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'margin',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> marginGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'margin',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> marginLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'margin',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> marginBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'margin',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> marginStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'margin',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> marginEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'margin',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      marginContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'margin',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      marginMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'margin',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> marginIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'margin',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> marginIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'margin',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> materialModelSnIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'materialModelSn',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> materialModelSnIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'materialModelSn',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> materialModelSnEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'materialModelSn',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> materialModelSnGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'materialModelSn',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> materialModelSnLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'materialModelSn',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> materialModelSnBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'materialModelSn',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> materialModelSnStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'materialModelSn',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> materialModelSnEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'materialModelSn',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      materialModelSnContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'materialModelSn',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      materialModelSnMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'materialModelSn',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> materialModelSnIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'materialModelSn',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> materialModelSnIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'materialModelSn',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> multipleBackIndexIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'multipleBackIndex',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> multipleBackIndexIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'multipleBackIndex',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> multipleBackIndexEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'multipleBackIndex',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> multipleBackIndexGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'multipleBackIndex',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> multipleBackIndexLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'multipleBackIndex',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> multipleBackIndexBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'multipleBackIndex',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> nameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> nameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> nameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> nameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> nameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> nameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'name',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> nameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> nameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      nameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      nameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'name',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> nameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> nameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> namesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'names',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> namesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'names',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> namesEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'names',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> namesGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'names',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> namesLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'names',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> namesBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'names',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> namesStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'names',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> namesEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'names',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      namesContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'names',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      namesMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'names',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> namesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'names',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> namesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'names',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> originTemplateIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'originTemplateId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> originTemplateIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'originTemplateId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> originTemplateIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'originTemplateId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> originTemplateIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'originTemplateId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> originTemplateIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'originTemplateId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> originTemplateIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'originTemplateId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> originTemplateIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'originTemplateId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> originTemplateIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'originTemplateId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      originTemplateIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'originTemplateId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      originTemplateIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'originTemplateId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> originTemplateIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'originTemplateId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> originTemplateIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'originTemplateId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> paperColorIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'paperColor',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> paperColorIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'paperColor',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> paperColorEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'paperColor',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> paperColorGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'paperColor',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> paperColorLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'paperColor',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> paperColorBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'paperColor',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> paperColorStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'paperColor',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> paperColorEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'paperColor',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      paperColorContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'paperColor',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      paperColorMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'paperColor',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> paperColorIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'paperColor',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> paperColorIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'paperColor',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> paperTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'paperType',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> paperTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'paperType',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> paperTypeEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'paperType',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> paperTypeGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'paperType',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> paperTypeLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'paperType',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> paperTypeBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'paperType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> platformCodeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'platformCode',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> platformCodeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'platformCode',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> platformCodeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'platformCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> platformCodeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'platformCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> platformCodeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'platformCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> platformCodeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'platformCode',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> platformCodeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'platformCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> platformCodeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'platformCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      platformCodeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'platformCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      platformCodeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'platformCode',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> platformCodeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'platformCode',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> platformCodeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'platformCode',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> rotateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'rotate',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> rotateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'rotate',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> rotateEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'rotate',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> rotateGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'rotate',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> rotateLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'rotate',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> rotateBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'rotate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sourceIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'sourceId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sourceIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'sourceId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sourceIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sourceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sourceIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'sourceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sourceIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'sourceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sourceIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'sourceId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sourceIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'sourceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sourceIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'sourceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      sourceIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'sourceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      sourceIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'sourceId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sourceIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sourceId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sourceIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'sourceId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sparedCodeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'sparedCode',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sparedCodeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'sparedCode',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sparedCodeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sparedCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sparedCodeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'sparedCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sparedCodeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'sparedCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sparedCodeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'sparedCode',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sparedCodeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'sparedCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sparedCodeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'sparedCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      sparedCodeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'sparedCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      sparedCodeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'sparedCode',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sparedCodeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sparedCode',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> sparedCodeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'sparedCode',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> supportedEditorsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'supportedEditors',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> supportedEditorsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'supportedEditors',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> supportedEditorsEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'supportedEditors',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> supportedEditorsGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'supportedEditors',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> supportedEditorsLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'supportedEditors',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> supportedEditorsBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'supportedEditors',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> supportedEditorsStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'supportedEditors',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> supportedEditorsEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'supportedEditors',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      supportedEditorsContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'supportedEditors',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      supportedEditorsMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'supportedEditors',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> supportedEditorsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'supportedEditors',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> supportedEditorsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'supportedEditors',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateClassIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'templateClass',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateClassIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'templateClass',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateClassEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'templateClass',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateClassGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'templateClass',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateClassLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'templateClass',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateClassBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'templateClass',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'templateId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'templateId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'templateId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'templateId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'templateId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'templateId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'templateId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'templateId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      templateIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'templateId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      templateIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'templateId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'templateId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'templateId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'templateType',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'templateType',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateTypeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'templateType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateTypeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'templateType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateTypeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'templateType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateTypeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'templateType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateTypeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'templateType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateTypeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'templateType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      templateTypeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'templateType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      templateTypeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'templateType',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateTypeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'templateType',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateTypeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'templateType',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateVersionIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'templateVersion',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateVersionIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'templateVersion',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateVersionEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'templateVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateVersionGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'templateVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateVersionLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'templateVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateVersionBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'templateVersion',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateVersionStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'templateVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateVersionEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'templateVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      templateVersionContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'templateVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      templateVersionMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'templateVersion',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateVersionIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'templateVersion',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> templateVersionIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'templateVersion',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> thumbnailIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'thumbnail',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> thumbnailIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'thumbnail',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> thumbnailEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'thumbnail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> thumbnailGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'thumbnail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> thumbnailLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'thumbnail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> thumbnailBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'thumbnail',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> thumbnailStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'thumbnail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> thumbnailEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'thumbnail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      thumbnailContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'thumbnail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      thumbnailMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'thumbnail',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> thumbnailIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'thumbnail',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> thumbnailIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'thumbnail',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> totalPageIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'totalPage',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> totalPageIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'totalPage',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> totalPageEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalPage',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> totalPageGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalPage',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> totalPageLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalPage',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> totalPageBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalPage',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> updateTimeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updateTime',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> updateTimeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updateTime',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> updateTimeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updateTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> updateTimeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updateTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> updateTimeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updateTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> updateTimeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updateTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> updateTimeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'updateTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> updateTimeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'updateTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      updateTimeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'updateTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      updateTimeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'updateTime',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> updateTimeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updateTime',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> updateTimeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'updateTime',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> userIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'userId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> userIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'userId',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> userIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> userIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> userIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> userIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'userId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> userIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> userIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      userIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      userIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'userId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> userIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'userId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> userIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'userId',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> valuesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'values',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> valuesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'values',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> valuesEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'values',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> valuesGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'values',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> valuesLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'values',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> valuesBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'values',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> valuesStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'values',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> valuesEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'values',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      valuesContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'values',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      valuesMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'values',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> valuesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'values',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> valuesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'values',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> vipIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'vip',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> vipIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'vip',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> vipEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'vip',
        value: value,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> virtualBarCodeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'virtualBarCode',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> virtualBarCodeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'virtualBarCode',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> virtualBarCodeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'virtualBarCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> virtualBarCodeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'virtualBarCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> virtualBarCodeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'virtualBarCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> virtualBarCodeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'virtualBarCode',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> virtualBarCodeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'virtualBarCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> virtualBarCodeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'virtualBarCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      virtualBarCodeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'virtualBarCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
          QAfterFilterCondition>
      virtualBarCodeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'virtualBarCode',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> virtualBarCodeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'virtualBarCode',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> virtualBarCodeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'virtualBarCode',
        value: '',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> widthIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'width',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> widthIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'width',
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> widthEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'width',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> widthGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'width',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> widthLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'width',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterFilterCondition> widthBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'width',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }
}

extension NiimBotTemplateCanvasDataModelQueryObject on QueryBuilder<
    NiimBotTemplateCanvasDataModel,
    NiimBotTemplateCanvasDataModel,
    QFilterCondition> {}

extension NiimBotTemplateCanvasDataModelQueryLinks on QueryBuilder<
    NiimBotTemplateCanvasDataModel,
    NiimBotTemplateCanvasDataModel,
    QFilterCondition> {}

extension NiimBotTemplateCanvasDataModelQuerySortBy on QueryBuilder<
    NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel, QSortBy> {
  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByAmazonCodeBeijing() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'amazonCodeBeijing', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByAmazonCodeBeijingDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'amazonCodeBeijing', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByAmazonCodeWuhan() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'amazonCodeWuhan', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByAmazonCodeWuhanDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'amazonCodeWuhan', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByArrowInBoard() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'arrowInBoard', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByArrowInBoardDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'arrowInBoard', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByBackgroundImage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backgroundImage', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByBackgroundImageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backgroundImage', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByBarcode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'barcode', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByBarcodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'barcode', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByBarcodeCategoryMap() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'barcodeCategoryMap', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByBarcodeCategoryMapDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'barcodeCategoryMap', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByBindExcel() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bindExcel', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByBindExcelDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bindExcel', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByCableDirection() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cableDirection', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByCableDirectionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cableDirection', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByCableLength() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cableLength', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByCableLengthDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cableLength', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByCanvasRotate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'canvasRotate', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByCanvasRotateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'canvasRotate', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByCommodityCategoryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commodityCategoryId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByCommodityCategoryIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commodityCategoryId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByCommodityInfo() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commodityInfo', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByCommodityInfoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commodityInfo', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByCommodityTemplate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commodityTemplate', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByCommodityTemplateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commodityTemplate', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByConsumableType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumableType', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByConsumableTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumableType', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByCreateTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createTime', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByCreateTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createTime', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByCurrentPage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentPage', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByCurrentPageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentPage', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByDataSource() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dataSource', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByDataSourceDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dataSource', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByDataSourceBindInfo() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dataSourceBindInfo', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByDataSourceBindInfoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dataSourceBindInfo', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByDataSourceModifies() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dataSourceModifies', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByDataSourceModifiesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dataSourceModifies', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByElements() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'elements', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByElementsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'elements', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByExtra() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'extra', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByExtraDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'extra', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByFolderId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'folderId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByFolderIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'folderId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByGoodsCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'goodsCode', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByGoodsCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'goodsCode', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByHardwareSeriesId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hardwareSeriesId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByHardwareSeriesIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hardwareSeriesId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByHardwareSeriesName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hardwareSeriesName', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByHardwareSeriesNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hardwareSeriesName', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByHasVipRes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hasVipRes', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByHasVipResDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hasVipRes', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByHeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'height', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByHeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'height', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByIndustryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'industryId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByIndustryIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'industryId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByInputAreas() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inputAreas', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByInputAreasDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inputAreas', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByIsCable() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCable', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByIsCableDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCable', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByIsDelete() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isDelete', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByIsDeleteDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isDelete', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByIsEdited() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isEdited', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByIsEditedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isEdited', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByIsLabel() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isLabel', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByIsLabelDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isLabel', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByIsPrintHistory() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isPrintHistory', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByIsPrintHistoryDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isPrintHistory', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByLabelId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'labelId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByLabelIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'labelId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByLabelNames() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'labelNames', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByLabelNamesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'labelNames', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByLayoutSchema() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'layoutSchema', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByLayoutSchemaDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'layoutSchema', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByLocalBackground() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localBackground', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByLocalBackgroundDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localBackground', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByLocalThumbnail() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localThumbnail', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByLocalThumbnailDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localThumbnail', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByLocalType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localType', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByLocalTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localType', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByMachineId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'machineId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByMachineIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'machineId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByMachineName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'machineName', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByMachineNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'machineName', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByMargin() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'margin', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByMarginDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'margin', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByMaterialModelSn() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'materialModelSn', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByMaterialModelSnDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'materialModelSn', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByMultipleBackIndex() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'multipleBackIndex', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByMultipleBackIndexDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'multipleBackIndex', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByNames() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'names', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByNamesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'names', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByOriginTemplateId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'originTemplateId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByOriginTemplateIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'originTemplateId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByPaperColor() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'paperColor', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByPaperColorDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'paperColor', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByPaperType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'paperType', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByPaperTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'paperType', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByPlatformCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'platformCode', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByPlatformCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'platformCode', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByRotate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rotate', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByRotateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rotate', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortBySourceId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sourceId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortBySourceIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sourceId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortBySparedCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sparedCode', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortBySparedCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sparedCode', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortBySupportedEditors() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supportedEditors', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortBySupportedEditorsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supportedEditors', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByTemplateClass() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templateClass', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByTemplateClassDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templateClass', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByTemplateId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templateId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByTemplateIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templateId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByTemplateType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templateType', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByTemplateTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templateType', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByTemplateVersion() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templateVersion', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByTemplateVersionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templateVersion', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByThumbnail() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'thumbnail', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByThumbnailDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'thumbnail', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByTotalPage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalPage', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByTotalPageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalPage', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByUpdateTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updateTime', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByUpdateTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updateTime', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByUserIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByValues() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'values', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByValuesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'values', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByVip() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'vip', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByVipDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'vip', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByVirtualBarCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'virtualBarCode', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByVirtualBarCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'virtualBarCode', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByWidth() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'width', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> sortByWidthDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'width', Sort.desc);
    });
  }
}

extension NiimBotTemplateCanvasDataModelQuerySortThenBy on QueryBuilder<
    NiimBotTemplateCanvasDataModel,
    NiimBotTemplateCanvasDataModel,
    QSortThenBy> {
  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByAmazonCodeBeijing() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'amazonCodeBeijing', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByAmazonCodeBeijingDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'amazonCodeBeijing', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByAmazonCodeWuhan() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'amazonCodeWuhan', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByAmazonCodeWuhanDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'amazonCodeWuhan', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByArrowInBoard() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'arrowInBoard', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByArrowInBoardDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'arrowInBoard', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByBackgroundImage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backgroundImage', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByBackgroundImageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backgroundImage', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByBarcode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'barcode', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByBarcodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'barcode', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByBarcodeCategoryMap() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'barcodeCategoryMap', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByBarcodeCategoryMapDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'barcodeCategoryMap', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByBindExcel() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bindExcel', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByBindExcelDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bindExcel', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByCableDirection() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cableDirection', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByCableDirectionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cableDirection', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByCableLength() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cableLength', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByCableLengthDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cableLength', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByCanvasRotate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'canvasRotate', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByCanvasRotateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'canvasRotate', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByCommodityCategoryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commodityCategoryId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByCommodityCategoryIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commodityCategoryId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByCommodityInfo() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commodityInfo', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByCommodityInfoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commodityInfo', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByCommodityTemplate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commodityTemplate', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByCommodityTemplateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commodityTemplate', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByConsumableType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumableType', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByConsumableTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumableType', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByCreateTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createTime', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByCreateTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createTime', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByCurrentPage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentPage', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByCurrentPageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentPage', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByDataSource() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dataSource', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByDataSourceDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dataSource', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByDataSourceBindInfo() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dataSourceBindInfo', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByDataSourceBindInfoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dataSourceBindInfo', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByDataSourceModifies() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dataSourceModifies', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByDataSourceModifiesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dataSourceModifies', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByElements() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'elements', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByElementsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'elements', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByExtra() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'extra', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByExtraDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'extra', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByFolderId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'folderId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByFolderIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'folderId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByGoodsCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'goodsCode', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByGoodsCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'goodsCode', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByHardwareSeriesId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hardwareSeriesId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByHardwareSeriesIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hardwareSeriesId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByHardwareSeriesName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hardwareSeriesName', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByHardwareSeriesNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hardwareSeriesName', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByHasVipRes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hasVipRes', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByHasVipResDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hasVipRes', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByHeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'height', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByHeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'height', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByIndustryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'industryId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByIndustryIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'industryId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByInputAreas() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inputAreas', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByInputAreasDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inputAreas', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByIsCable() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCable', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByIsCableDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCable', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByIsDelete() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isDelete', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByIsDeleteDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isDelete', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByIsEdited() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isEdited', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByIsEditedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isEdited', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByIsLabel() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isLabel', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByIsLabelDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isLabel', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByIsPrintHistory() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isPrintHistory', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByIsPrintHistoryDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isPrintHistory', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByIsarId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isarId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByIsarIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isarId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByLabelId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'labelId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByLabelIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'labelId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByLabelNames() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'labelNames', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByLabelNamesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'labelNames', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByLayoutSchema() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'layoutSchema', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByLayoutSchemaDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'layoutSchema', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByLocalBackground() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localBackground', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByLocalBackgroundDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localBackground', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByLocalThumbnail() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localThumbnail', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByLocalThumbnailDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localThumbnail', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByLocalType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localType', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByLocalTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localType', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByMachineId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'machineId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByMachineIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'machineId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByMachineName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'machineName', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByMachineNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'machineName', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByMargin() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'margin', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByMarginDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'margin', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByMaterialModelSn() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'materialModelSn', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByMaterialModelSnDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'materialModelSn', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByMultipleBackIndex() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'multipleBackIndex', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByMultipleBackIndexDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'multipleBackIndex', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByNames() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'names', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByNamesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'names', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByOriginTemplateId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'originTemplateId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByOriginTemplateIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'originTemplateId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByPaperColor() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'paperColor', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByPaperColorDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'paperColor', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByPaperType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'paperType', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByPaperTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'paperType', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByPlatformCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'platformCode', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByPlatformCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'platformCode', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByRotate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rotate', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByRotateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rotate', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenBySourceId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sourceId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenBySourceIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sourceId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenBySparedCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sparedCode', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenBySparedCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sparedCode', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenBySupportedEditors() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supportedEditors', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenBySupportedEditorsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supportedEditors', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByTemplateClass() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templateClass', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByTemplateClassDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templateClass', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByTemplateId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templateId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByTemplateIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templateId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByTemplateType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templateType', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByTemplateTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templateType', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByTemplateVersion() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templateVersion', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByTemplateVersionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templateVersion', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByThumbnail() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'thumbnail', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByThumbnailDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'thumbnail', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByTotalPage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalPage', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByTotalPageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalPage', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByUpdateTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updateTime', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByUpdateTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updateTime', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userId', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByUserIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userId', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByValues() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'values', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByValuesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'values', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByVip() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'vip', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByVipDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'vip', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByVirtualBarCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'virtualBarCode', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByVirtualBarCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'virtualBarCode', Sort.desc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByWidth() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'width', Sort.asc);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QAfterSortBy> thenByWidthDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'width', Sort.desc);
    });
  }
}

extension NiimBotTemplateCanvasDataModelQueryWhereDistinct on QueryBuilder<
    NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel, QDistinct> {
  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByAmazonCodeBeijing({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'amazonCodeBeijing',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByAmazonCodeWuhan({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'amazonCodeWuhan',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByArrowInBoard() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'arrowInBoard');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByBackgroundImage({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'backgroundImage',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByBarcode({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'barcode', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByBarcodeCategoryMap({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'barcodeCategoryMap',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByBindExcel() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'bindExcel');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByCableDirection() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cableDirection');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByCableLength() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cableLength');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByCanvasRotate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'canvasRotate');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByCommodityCategoryId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'commodityCategoryId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByCommodityInfo({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'commodityInfo',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByCommodityTemplate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'commodityTemplate');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByConsumableType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'consumableType');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByCreateTime({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createTime', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByCurrentPage() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'currentPage');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByDataSource({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'dataSource', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByDataSourceBindInfo({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'dataSourceBindInfo',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByDataSourceModifies({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'dataSourceModifies',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByElements({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'elements', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByExtra({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'extra', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByFolderId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'folderId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByGoodsCode({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'goodsCode', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByHardwareSeriesId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'hardwareSeriesId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByHardwareSeriesName({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'hardwareSeriesName',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByHasVipRes() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'hasVipRes');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByHeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'height');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByIndustryId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'industryId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByInputAreas({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'inputAreas', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByIsCable() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isCable');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByIsDelete() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isDelete');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByIsEdited() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isEdited');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByIsLabel() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isLabel');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByIsPrintHistory() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isPrintHistory');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByLabelId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'labelId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByLabelNames({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'labelNames', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByLayoutSchema({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'layoutSchema', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByLocalBackground({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'localBackground',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByLocalThumbnail({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'localThumbnail',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByLocalType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'localType');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByMachineId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'machineId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByMachineName({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'machineName', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByMargin({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'margin', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByMaterialModelSn({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'materialModelSn',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByMultipleBackIndex() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'multipleBackIndex');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByName({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'name', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByNames({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'names', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByOriginTemplateId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'originTemplateId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByPaperColor({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'paperColor', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByPaperType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'paperType');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByPlatformCode({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'platformCode', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByRotate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'rotate');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctBySourceId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'sourceId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctBySparedCode({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'sparedCode', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctBySupportedEditors({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'supportedEditors',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByTemplateClass() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'templateClass');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByTemplateId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'templateId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByTemplateType({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'templateType', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByTemplateVersion({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'templateVersion',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByThumbnail({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'thumbnail', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByTotalPage() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalPage');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByUpdateTime({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updateTime', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByUserId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'userId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByValues({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'values', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByVip() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'vip');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByVirtualBarCode({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'virtualBarCode',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel,
      QDistinct> distinctByWidth() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'width');
    });
  }
}

extension NiimBotTemplateCanvasDataModelQueryProperty on QueryBuilder<
    NiimBotTemplateCanvasDataModel,
    NiimBotTemplateCanvasDataModel,
    QQueryProperty> {
  QueryBuilder<NiimBotTemplateCanvasDataModel, int, QQueryOperations>
      isarIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isarId');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      amazonCodeBeijingProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'amazonCodeBeijing');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      amazonCodeWuhanProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'amazonCodeWuhan');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, bool?, QQueryOperations>
      arrowInBoardProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'arrowInBoard');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      backgroundImageProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'backgroundImage');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      barcodeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'barcode');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      barcodeCategoryMapProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'barcodeCategoryMap');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, bool?, QQueryOperations>
      bindExcelProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'bindExcel');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, int?, QQueryOperations>
      cableDirectionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cableDirection');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, double?, QQueryOperations>
      cableLengthProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cableLength');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, int?, QQueryOperations>
      canvasRotateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'canvasRotate');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      commodityCategoryIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'commodityCategoryId');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      commodityInfoProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'commodityInfo');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, bool?, QQueryOperations>
      commodityTemplateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'commodityTemplate');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, int?, QQueryOperations>
      consumableTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'consumableType');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      createTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createTime');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, int?, QQueryOperations>
      currentPageProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'currentPage');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      dataSourceProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dataSource');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      dataSourceBindInfoProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dataSourceBindInfo');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      dataSourceModifiesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dataSourceModifies');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      elementsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'elements');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      extraProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'extra');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      folderIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'folderId');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      goodsCodeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'goodsCode');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      hardwareSeriesIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'hardwareSeriesId');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      hardwareSeriesNameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'hardwareSeriesName');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, bool?, QQueryOperations>
      hasVipResProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'hasVipRes');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, double?, QQueryOperations>
      heightProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'height');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      industryIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'industryId');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      inputAreasProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'inputAreas');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, bool?, QQueryOperations>
      isCableProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isCable');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, bool?, QQueryOperations>
      isDeleteProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isDelete');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, bool?, QQueryOperations>
      isEditedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isEdited');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, bool?, QQueryOperations>
      isLabelProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isLabel');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, bool?, QQueryOperations>
      isPrintHistoryProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isPrintHistory');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      labelIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'labelId');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      labelNamesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'labelNames');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      layoutSchemaProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'layoutSchema');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      localBackgroundProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localBackground');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      localThumbnailProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localThumbnail');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, int?, QQueryOperations>
      localTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localType');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      machineIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'machineId');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      machineNameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'machineName');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      marginProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'margin');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      materialModelSnProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'materialModelSn');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, int?, QQueryOperations>
      multipleBackIndexProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'multipleBackIndex');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      nameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'name');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      namesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'names');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      originTemplateIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'originTemplateId');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      paperColorProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'paperColor');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, int?, QQueryOperations>
      paperTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'paperType');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      platformCodeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'platformCode');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, int?, QQueryOperations>
      rotateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'rotate');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      sourceIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'sourceId');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      sparedCodeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'sparedCode');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      supportedEditorsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'supportedEditors');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, int?, QQueryOperations>
      templateClassProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'templateClass');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      templateIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'templateId');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      templateTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'templateType');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      templateVersionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'templateVersion');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      thumbnailProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'thumbnail');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, int?, QQueryOperations>
      totalPageProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalPage');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      updateTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updateTime');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      userIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'userId');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      valuesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'values');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, bool?, QQueryOperations>
      vipProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'vip');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, String?, QQueryOperations>
      virtualBarCodeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'virtualBarCode');
    });
  }

  QueryBuilder<NiimBotTemplateCanvasDataModel, double?, QQueryOperations>
      widthProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'width');
    });
  }
}
