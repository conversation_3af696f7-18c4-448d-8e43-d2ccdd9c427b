import 'dart:convert';

class LayoutUtil {
  /// 从template中获取inputAreas
  /// [template] 模板数据，可能包含inputAreas字段或layoutSchema字段
  /// 返回inputAreas列表，如果获取失败返回null
  static List<dynamic>? getInputAreasFromTemplate(Map<dynamic, dynamic>? template) {
    if (template == null) {
      return null;
    }

    // 首先尝试直接从template中获取inputAreas
    List<dynamic>? inputAreas = template['inputAreas'];

    // 如果直接获取不到，尝试从layoutSchema中获取
    if (inputAreas == null || inputAreas.isEmpty) {
      final layoutSchemaRaw = template['layoutSchema'];
      if (layoutSchemaRaw != null) {
        Map<String, dynamic>? layoutSchema;

        // 如果layoutSchema是字符串，需要先解码
        if (layoutSchemaRaw is String) {
          try {
            layoutSchema = jsonDecode(layoutSchemaRaw);
          } catch (e) {
            print('Failed to decode layoutSchema JSON: $e');
            return null;
          }
        } else if (layoutSchemaRaw is Map<String, dynamic>) {
          layoutSchema = layoutSchemaRaw;
        }

        if (layoutSchema != null) {
          inputAreas = layoutSchema['areas'];
        }
      }
    }

    return inputAreas;
  }

  static String initLabelInputAreas(List<dynamic>? inputAreas, double labelWidth, double labelHeight) {
    if (inputAreas?.isEmpty ?? true) {
      inputAreas = [
        {"y": 0, "x": 0, "width": labelWidth, "height": labelHeight}
      ];
    }
    final Map<String, dynamic> editRegion = {
      'width': labelWidth,
      'height': labelHeight,
      'editableRegion': inputAreas!.map((item) {
        return {
          for (var entry in item.entries)
            if (entry.key == 'h')
              'height': entry.value
            else if (entry.key == 'w')
              'width': entry.value
            else
              entry.key: entry.value
        };
      }).toList()
    };
    return jsonEncode(editRegion);
  }

  /// 解析inputAreas，输出每个item的name属性的值
  ///
  /// [inputAreas] 输入区域列表，每个item应包含name或label属性
  /// 返回所有item的name/label属性值列表，如果name/label为null或空则跳过
  static List<String> parseInputAreaNames(List<dynamic>? inputAreas) {
    if (inputAreas?.isEmpty ?? true) {
      return [];
    }

    List<String> names = [];
    for (var item in inputAreas!) {
      if (item is Map) {
        // 优先使用name字段，如果没有则使用label字段
        dynamic nameValue = item['name'];
        if (nameValue == null || (nameValue is String && nameValue.isEmpty)) {
          nameValue = item['label'];
        }
        if (nameValue != null && nameValue is String && nameValue.isNotEmpty) {
          names.add(nameValue);
        }
      }
    }

    return names;
  }
}
