//
//  JCWifiManager.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2023/2/28.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCWifiManager.h"
#import "JCAPI.h"
#import "JCBluetoothModel.h"
#import "JCDeviceSeriesHelp.h"
#import <ifaddrs.h>
#import <arpa/inet.h>
#import <SystemConfiguration/CaptiveNetwork.h>

static BOOL WifiOnoff = YES;

@interface JCWifiManager ()

//wifi局域网搜索设备
@property(nonatomic,strong) NSMutableArray *wifiDataArray;

@property(nonatomic,assign) BOOL isSearching;

@property(nonatomic,copy) searchRelustBlock serachBlock;

@property(nonatomic,copy) wifiConfigBlock configBlock;

/// Wi-Fi搜索结束的回调
@property(nonatomic,copy) XYNormalBlock searchEndBlock;

@end

@implementation JCWifiManager

+(id)shareInstance
{
    static JCWifiManager *wifiManager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        wifiManager = [[JCWifiManager alloc] init];
    });
    return wifiManager;
}

-(void)startScan{
    if (WifiOnoff) {
        return;
    }
//    [self.wifiDataArray removeAllObjects];
//    [self startWifiScanWithBleName:@"" style:1];
 
}

// 插入WIFI搜索到的设备
/// - Parameters:
///   - bleName: 蓝牙名称
///   - ip: IP地址
///   - port: 端口地址
///   - available: 剩余可连接数
///   - alias: 设备别名
- (void)insertData:(NSString *)bleName ip:(NSString *)ip port:(NSNumber *)port available:(NSNumber *)available alias:(NSString *)alias {
    JCBluetoothModel *model = [[JCBluetoothModel alloc] init];
    model.name = bleName;
    model.host = ip;
    model.state = JCConnectStateTypeWifi;
    if (available){
      model.availableClient = [available intValue];
    }
    model.alias = alias;
    JCSelectDeviceModel *deviceModel = [JCDeviceSeriesHelp selectModelWithDeviceName:bleName appId:[JCBluetoothManager sharedInstance].limitAppId];
    JCPrinterModel *printerModel = [JCDeviceSeriesHelp printerModelWithModel:model];
    if (deviceModel == nil || printerModel == nil) {
      return;
    }
    model.printerModel = deviceModel;
    model.printer = printerModel;
    [self.wifiDataArray addObject:model];
}


/// 结束WIFI搜索
- (void)discoverWifiPrinterFinished {
  // Call the searchEndBlock as before
  if (self.searchEndBlock) {
    self.searchEndBlock();
    self.searchEndBlock = nil;
    self.wifiDataArray = nil;
  }
}

//1.单纯搜索设备 2.通过搜索设备判断是否配网成功
- (void)startWifiScanWithBleName:(NSString*)bleName completion:(void(^)(NSArray *scanedPrinters))completion
{
    static BOOL isSearching = NO;
    if (isSearching) {
         return;
    }
    isSearching = YES;
    NSString *searchTime = [XYTool getCurrentTimesWithoutTSZ];
    NSLog(@"蓝牙- Wi-Fi：搜索Wi-Fi设备%@ 时间：%@",bleName,searchTime);
    // 保存回调
    XYWeakSelf
    self.searchEndBlock = ^{
      NSLog(@"蓝牙- Wi-Fi：搜索到Wi-Fi设备%@ 时间：%@ 搜索到Wi-Fi数据：%@",bleName,searchTime,weakSelf.wifiDataArray);
      NSMutableArray *printerArr = [NSMutableArray array];
      if (weakSelf.wifiDataArray.count > 0) {
        for (JCBluetoothModel *model in weakSelf.wifiDataArray) {
              if (!STR_IS_NIL(bleName) && [model.name isEqualToString:bleName]){
                  [printerArr removeAllObjects];
                  [printerArr addObject:model];
                  break;
              } else {
                  [printerArr addObject:model];
              }
          }
      } else {
          NSLog(@"蓝牙- Wi-Fi：未搜索到任何Wi-Fi设备");
      }
      completion(printerArr);
      isSearching = NO;
    };
    // 搜索WIFI设备
    [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"discoverWIFIPrinter"
                                                           arguments:nil result:^(id   _Nullable response) {

    }];
}

- (void)connectWith:(JCBluetoothModel *)model complate:(XYBlock)complate{
    model.state = JCConnectStateTypeWifi;
    [[JCBluetoothManager sharedInstance] connectWith:model connectType:CLOUDPRINTER_WIFI  complate:complate];
}

- (void)CheckconfigWithModel:(JCBluetoothModel*)model
{
   
    [self startWifiScanWithBleName:model.name completion:^(NSArray * _Nonnull scanedPrinterNames) {
        
    }];
   
}

- (NSDictionary*)isConfigedWifi:(JCBluetoothModel *)model
{

    BOOL isConfiged = NO;
    BOOL onLine  = NO;
    if (self.wifiDataArray.count > 0) {
        for (JCBluetoothModel *cloundModel in self.wifiDataArray) {
            if ([cloundModel.name isEqualToString:model.name]) {
                isConfiged = YES;
                onLine = cloundModel.onLine;
            }
        }

    }
    NSDictionary *dic = @{@"isConfiged":[NSNumber numberWithBool:isConfiged],@"onLine":[NSNumber numberWithBool:onLine]};
    return dic;

}

- (void)configWithWifiName:(NSString*)wifiName password:(NSString*)password completion:(void(^)(NSNumber *))callbackValue
{
  [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"configWifi"
                                                         arguments:@{@"name":wifiName,@"password":password} result:^(id   _Nullable response) {
    if ([response boolValue]) {
       callbackValue(@1);
    } else {
       callbackValue(@0);
    }
  }];
}

- (void)didWifiSerachEndWithResultBlock:(searchRelustBlock)result
{
    if (result) {
        self.serachBlock = result;
    }
}
- (void)didConfigWithResultBlock:(wifiConfigBlock)result
{
    if (result) {
        self.configBlock = result;
    }
}


-(NSMutableArray *)wifiDataArray
{
    if (!_wifiDataArray) {
        _wifiDataArray = [NSMutableArray array];
    }
    return _wifiDataArray;
}

- (BOOL)canWifiOperate
{
    return jc_is_connected_wifi && [JCDeviceSeriesHelp hasWifiFeature];
}

- (BOOL)isWiFiEnabled {
    return NO;
}
@end
