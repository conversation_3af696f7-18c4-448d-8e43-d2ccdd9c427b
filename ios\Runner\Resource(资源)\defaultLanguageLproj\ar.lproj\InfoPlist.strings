/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "يحتاج التطبيق إلى إذن استخدام الكاميرا لمسح الرموز والتعرف على النصوص والتصوير. هل ترغب بالسماح بذلك؟";
NSBluetoothPeripheralUsageDescription = "يحتاج التطبيق إلى إذن استخدام البلوتوث للاتصال بالطابعة. هل ترغب بالسماح بذلك؟";
NSBluetoothAlwaysUsageDescription = "يحتاج التطبيق إلى إذن استخدام البلوتوث للاتصال بالطابعة. هل ترغب بالسماح بذلك؟";
NSContactsUsageDescription = "يحتاج التطبيق إلى إذن الوصول إلى جهات الاتصال. هل ترغب بالسماح بذلك؟";
NSMicrophoneUsageDescription = "يحتاج التطبيق إلى إذن استخدام الميكروفون للتعرّف الصوتي. هل ترغب بالسماح بذلك؟";
NSPhotoLibraryUsageDescription = "هذا الإذن ضروري لطباعة الصور، والتعرف على الرموز، والرموز الثنائية (QR)، والنصوص، وتعيين صورة شخصية مخصصة.
 يُرجى السماح بالوصول إلى \"جميع الصور\" لضمان عمل التطبيق بشكل سليم.
 في حال اخترت \"تحديد صور...\"، فلن يتمكن التطبيق من الوصول إلى الصور غير المحددة أو الصور الجديدة لاحقًا.";
NSLocationWhenInUseUsageDescription = "من أجل تسهيل الاتصال بشبكات Wi-Fi القريبة، يطلب تطبيق NIIMBOT  إذن الموقع";
NSLocationAlwaysUsageDescription = "من أجل تسهيل الاتصال بشبكات Wi-Fi القريبة، يطلب تطبيق NIIMBOT  إذن الموقع";
NSLocationAlwaysAndWhenInUseUsageDescription = "من أجل تسهيل الاتصال بشبكات Wi-Fi القريبة، يطلب تطبيق NIIMBOT  إذن الموقع";
NSSpeechRecognitionUsageDescription = "يحتاج التطبيق إلى إذنك لتفعيل ميزة التعرّف الصوتي. هل ترغب بالسماح بذلك؟";
"UILaunchStoryboardName" = "LaunchScreen-Chinese";
