//
//  JCETagManager.m
//  Runner
//
//  Created by aiden on 2023/7/31.
//
#import "JCTemplateImageManager.h"
#import "ImageLibraryBridge.h"
#import "JCETagManager.h"
#import "JCFontManager.h"

@implementation JCETagManager

//局部静态 记录当前电子价签内忽略下载字体的模板ID 便于再次刷新时 不再弹出字体下载
static NSMutableArray *ignoreFontTemplateArr;

//局部静态 记录忽略下载的缺失字体
static NSMutableArray *fontSourceResoloveArr;

//局部静态 蓝牙连接单例实例
static JCBluetoothManager *bluetoothManager;

//电子价签设备信息
static JCAPIEtagDeviceInfosModel * _Nullable etagDeviceInfoModel;
// 电子价签小程序状态
+ (void)etagFlutterAppStatus:(BOOL)etagAppStatus{
    bluetoothManager = [JCBluetoothManager sharedInstance];
    if(etagAppStatus){
        ignoreFontTemplateArr = [NSMutableArray array];
        bluetoothManager.deviceType = JCBluetoothETag;
    }else{
        bluetoothManager.deviceType = JCBluetoothNormal;
    }
}

// 刷新电子价签连接状态
+ (void)refreshETagConnectStatus{
    //刷新当前蓝牙管理设备状态电子价签
    bluetoothManager = [JCBluetoothManager sharedInstance];
    bluetoothManager.deviceType = JCBluetoothETag;
    NSString *peripheralName = [[NSUserDefaults standardUserDefaults] valueForKey:LASTCONNECTETAGNAME];
    if(JC_IS_CONNECTED_PRINTER && ![JC_CURRENT_CONNECTED_PRINTER hasPrefix:@"ET10"]){
        [bluetoothManager closeConnectedByHand:YES deviceType:JCBluetoothNormal];
    }
    [[NSUserDefaults standardUserDefaults] removeObjectForKey:LASTCONNECTPRINTERNAME];
    if(JC_IS_CONNECTED_PRINTER){
        //获取设备信息
        NSLog(@"电子价签: 获取设备信息22 %@",[XYTool getCurrentTimesWithoutTSZ]);
        [JCAPI_Etag getDeviceInfos:^(JCAPIEtagDeviceInfosModel * _Nullable model) {
            NSLog(@"获取到设备信息");
            JCBluetoothModel *connectedModel = bluetoothManager.connectedModel;
            if(model != nil && connectedModel == nil){
                [self sendEtagStatusToFlutterWith:JCETagConnectSuccess connectedName:connectedModel.name];
            }else{
                [self sendEtagStatusToFlutterWith:JCETagDisConnect connectedName:connectedModel.name];
            }
        }];
    }else{
        //未连接状态 根据连接记录开启判断自动回连
        if([peripheralName hasPrefix:@"ET10"]){
            [bluetoothManager autoConnectPrinter];
        }
    }
}

//发送电子价签状态至flutter层刷新
+ (void)sendEtagStatusToFlutterWith:(JCETagConnectStatus)connectStatus connectedName:(NSString *)deviceName{
    //获取设备系列信息
    NSArray *printerInfoArr = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_PRINTERINFO dicOrModel:[JCPrinterModel class] whereFormat: @"where name = 'ET10'"];
    JCPrinterModel *printer = printerInfoArr.firstObject;
    NSArray *deviceInfoArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_MYDEVICES dicOrModel:[JCSelectDeviceModel class] whereFormat:[NSString stringWithFormat:@"where xyid = '%@'",printer.seriesId]];
    JCSelectDeviceModel *deviceModel = deviceInfoArr.firstObject;
    //通过eventChannel返回状态
    NSDictionary *etConnectEventInfo = @{@"eTagStatus":@"",@"type":@(connectStatus),@"deviceName":UN_NIL(deviceName),@"address":@"",@"imageUrl":deviceModel.guide_image};
    if ([JCAppEventChannel shareInstance].eventSink) {
        [JCAppEventChannel shareInstance].eventSink(etConnectEventInfo);
    }
}

//通过future层返回模版及商品数据 混合后返回预览图
+ (void)getET10GoodsMixImagesData:(NSString *)templateDataStr isBatchPreview:(BOOL)isBatchPreview complate:(XYBlock)complate{
    NSMutableArray *templateModels = [NSMutableArray array];
    NSData *jsonData = [templateDataStr dataUsingEncoding:NSUTF8StringEncoding];
    id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options: NSJSONReadingMutableContainers error:nil];
    if([jsonObject isKindOfClass:[NSArray class]]){
        NSArray *templateArr = jsonObject;
        for (NSDictionary *templateInfo in templateArr) {
            JCTemplateData *templateModel = [[JCTemplateData alloc] initWithDictionary:templateInfo error:nil];
            if(templateModel != nil){
                [templateModels addObject:templateModel];
            }
        }
        JCTemplateData *firstTemplateModel = templateModels.firstObject;
        if(firstTemplateModel != nil){
            //获取模板资源信息（背景图，素材等）
          [self downLoadImageImageAndFontsForData:@"" templateData:firstTemplateModel isBatchPreview:isBatchPreview complete:^(JCTemplateData *complateData){
                float displayMultiple = 10;
                float printMultiple = DrawBoardInfo.dpiScale;
                NSString *fontPath = [NSString stringWithFormat:@"%@/font",DocumentsFontPath];
                [ImageLibraryBridge initImageProcessing:fontPath error:nil];
                NSMutableArray *templateDataArr = [NSMutableArray array];
                //遍历模板数组，批量生成预览图后转化为字节流返回
                for (JCTemplateData *templateModel in templateModels) {
                    templateModel.idStr = complateData.idStr;
                    templateModel.localThumbnail = complateData.localThumbnail;
                    templateModel.localBackground = complateData.localBackground;
                    for (JCElementModel *elementModel in templateModel.elements) {
                      for (JCElementModel *elementModelComplate in complateData.elements) {
                        if([elementModel.elementId isEqualToString:elementModelComplate.elementId] && [elementModel.type isEqualToString:@"image"]){
                          elementModel.localUrl = elementModelComplate.localUrl;
                        }
                      }
                    }
                    NSInteger cPage = MAX((templateModel.currentPage-1),0);
                    JCTemplateData *jcData = templateModel.toSdk(YES).configureBase64Background(YES).setCurrentPageIndex(cPage).setTotalCopyNumber(templateModel.totalPage).setVirtualPageIndex(templateModel.currentPage).setCurrentCopyNumber(cPage);
                    NSString *jsonStr = [[jcData dataDict] xy_toJsonString];
                    UIImage *previewImage = [ImageLibraryBridge generatePrintPreviewImage:jsonStr
                                                                          displayMultiple:displayMultiple
                                                                            printMultiple:printMultiple
                                                                    printPreviewImageType:0
                                                                               themeColor:JC_CURRENT_PRINT_COLOR
                                                                                    error:nil];
                    if(previewImage != nil){
                        NSData *imageData = UIImagePNGRepresentation(previewImage);
                        [templateDataArr addObject:imageData];
                    }
                }
                complate(templateDataArr);
            }];
        }else{
            complate(@[]);
        }
    }
}

/**
 下载模板内的元素资源及字体
 */

+ (void)downLoadImageImageAndFontsForData:(NSString *)source templateData:(JCTemplateData *)data isBatchPreview:(BOOL)isBatchPreview complete:(void(^)(JCTemplateData *))completeBlock{
    fontSourceResoloveArr = @[].mutableArray;
    __block JCTemplateData *blockModel = data;
    [JCTemplateImageManager downLoadImagesForData:data options:Elements complete:^(JCTemplateData *resultData) {
        // Update the template data with the result
        if (resultData) {
          blockModel = resultData;
        }
        NSInteger noFontCount = [[JCFontManager sharedManager] needDownloadFont:data].count;
        NSInteger needChangLangCount = [[JCFontManager sharedManager] needChangLangDownloadFont:data].count;
        if([[JCFontManager sharedManager] isNeedDownloadFonts:data] && !isBatchPreview && ![ignoreFontTemplateArr containsObject:data.idStr]){
            if(noFontCount != 0){
                if(needChangLangCount == 0){
                    NSInteger noFontCount = [[JCFontManager sharedManager] needDownloadFont:data].count;
                    NSString *descrpString = [NSString stringWithFormat:@"%ld %@",noFontCount,XY_LANGUAGE_TITLE_NAMED(@"app01217",@"字体缺失，是否需要下载")];
                    [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01218",@"字体缺失") message:descrpString cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01220",@"下载") cancelBlock:^{
                      if (completeBlock) completeBlock(resultData);
                      [ignoreFontTemplateArr addObject:data.idStr];
                    } sureBlock:^{
                      [[JCFontManager sharedManager] downloadAllFonts:data downloadComplateBlock:^{
                          if (completeBlock) completeBlock(resultData);
                      } cancelBlock:^{
                          [ignoreFontTemplateArr addObject:data.idStr];
                      }];
                    } alertType:3];
                }else{
                    NSString *fontDownloadString = [NSString stringWithFormat:@"%ld %@",noFontCount,XY_LANGUAGE_TITLE_NAMED(@"app01246",@"字体需下载")];
                    NSString *needChangeLangString = [NSString stringWithFormat:@"%ld %@",needChangLangCount,XY_LANGUAGE_TITLE_NAMED(@"app01247",@"字体需切换语言后下载")];
                    NSString *descrpString = [NSString stringWithFormat:@"%@\n%@",fontDownloadString,needChangeLangString];
                    [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01218",@"字体缺失") message:descrpString cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01220",@"下载") cancelBlock:^{
                      if (completeBlock) completeBlock(resultData);
                      [ignoreFontTemplateArr addObject:data.idStr];
                    } sureBlock:^{
                      [[JCFontManager sharedManager] downloadAllFonts:data downloadComplateBlock:^{
                          if (completeBlock) completeBlock(resultData);
                      } cancelBlock:^{
                          [ignoreFontTemplateArr addObject:data.idStr];
                      }];
                    } alertType:3];
                }
            }else{
                if(needChangLangCount != 0 && ![fontSourceResoloveArr containsObject:data.idStr]){
                    NSString *descrpString = [NSString stringWithFormat:@"%@",XY_LANGUAGE_TITLE_NAMED(@"app01248",@"请切换语言下载字体")];
                    [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01218",@"字体缺失") message:descrpString cancelButtonTitle:@"" sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707",@"我知道了") cancelBlock:^{
                        if (completeBlock) completeBlock(resultData);
                        [ignoreFontTemplateArr addObject:data.idStr];
                    } sureBlock:^{
                        [fontSourceResoloveArr addObject:data.idStr];
                        if (completeBlock) completeBlock(resultData);
                        [ignoreFontTemplateArr addObject:data.idStr];
                    }];
                }else{
                    if (completeBlock) completeBlock(resultData);
                }
            }
        }else{
            if (completeBlock) completeBlock(resultData);
        }
    }];
}


//通过eventChannel同步SDK心跳返回的屏幕信息至flutter层
+ (void)refershETagScreenStatus:(JCAPIEtagHeartInfosModel *)etagHeartInfosModel{
    NSMutableArray *screenSizeInfoArr = [NSMutableArray array];
    for (NSDictionary *screenDot in etagHeartInfosModel.screenDots) {
        NSArray *screenSize = @[screenDot[@"width"],screenDot[@"height"]];
        [screenSizeInfoArr addObject:screenSize];
    }
    NSDictionary *etConnectEventInfo = @{@"action":@"eTagStatusWriterListener",@"eTagStatusWriterListener":@"",@"heartBeat":@{@"completeStatus":etagHeartInfosModel.screenDoneStatus,@"electric":@(etagHeartInfosModel.electric),@"exceptionStatus":etagHeartInfosModel.screenErrorStatus,@"resource":@"",@"screenInfo":screenSizeInfoArr,@"screenPullStatus":etagHeartInfosModel.screenPullStatus,@"screenWorkStatus":etagHeartInfosModel.screenWorkStatus}};
    if ([JCAppEventChannel shareInstance].eventSink) {
        [JCAppEventChannel shareInstance].eventSink(etConnectEventInfo);
    }
}

static BOOL isSycnData = NO;
//调用SDK连接电子价签
+ (void)connectEtagWith:(JCBluetoothModel *)model connectBlock:(XYBlock)connectBlock{
    bluetoothManager.deviceDict = nil;
    __block NSInteger batteryPower = [JCPrintDevice shareDevice].batteryPower.integerValue;
    NSLog(@"电子价签:屏幕开始连接 + 【%@】+  %@",model.name,[XYTool getCurrentTimesWithoutTSZ]);
    [JCAPI_Etag initEtagWith:model.name withSuccessBlock:^{
        connectBlock(@YES);
        JCNCPost2p(PrinterBatteryPowerNotification, @(batteryPower));
        [self getEtagDeviceDetail:^(NSDictionary * deviceInfo) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [bluetoothManager checkFirmUpdate];
            });
            NSLog(@"电子价签:连接成功注册状态心跳 + 【%@】+  %@",model.name,[XYTool getCurrentTimesWithoutTSZ]);
            isSycnData = NO;
            [JCAPI_Etag deviceStatusCallback:^(JCAPIEtagHeartInfosModel * _Nullable model) {
                if(model != nil){
                    [JCETagManager refershETagScreenStatus:model];
                }
                if([model.screenWorkStatus containsObject:@1] || isSycnData){
                    isSycnData = YES;
                    NSLog(@"电子价签:屏幕开始刷新 + 【%@】+  %@",[model.screenWorkStatus componentsJoinedByString:@","],[XYTool getCurrentTimesWithoutTSZ]);
                    if(![model.screenWorkStatus containsObject:@1]){
                        NSLog(@"电子价签:屏幕停止");
                        isSycnData = NO;
                    }
                }
                if([model.screenDoneStatus containsObject:@1]){
                    NSLog(@"电子价签:同步状态 + 【%@】+  %@",[model.screenDoneStatus componentsJoinedByString:@","],[XYTool getCurrentTimesWithoutTSZ]);
                }
                if(model.electric != batteryPower){
                    batteryPower = model.electric;
                    NSInteger intPower = batteryPower * 25;
                    NSInteger powerLever = intPower/10;
                    if(intPower%10 >= 5 || powerLever == 0){
                        powerLever++;
                    }
                    [JCPrintDevice shareDevice].batteryPower = StringFromInt(powerLever*10);
                    JCNCPost2p(PrinterBatteryPowerNotification, [JCPrintDevice shareDevice].batteryPower);
                }
            }];
        }];
    } withErrorBlock:^(int errorCode) {
        connectBlock(@NO);
        batteryPower = 0;
        etagDeviceInfoModel = nil;
        NSLog(@"电子价签:连接失败 + 【%@】+  %@",model.name,[XYTool getCurrentTimesWithoutTSZ]);
        [JCPrintDevice shareDevice].batteryPower = StringFromInt(batteryPower);
        JCNCPost2p(PrinterBatteryPowerNotification, @(batteryPower));
    }];
}

//断开电子价签
+ (void)disconnectETag:(BOOL)isByhand{
    [JCAPI_Etag disconnect];
}

//电子价签信息写入屏幕
+ (void)writeDataToEtagScreenWith:(NSInteger)index data:(JCTemplateData *)templateModel complate:(XYBlock)complate{
    if(templateModel == nil) {
        complate(@0);
#ifdef DEBUG
        [MBProgressHUD showToastWithMessageDarkColor:@"错误数据已复制到粘贴板"];
#else
        [MBProgressHUD showToastWithMessageDarkColor:[NSString stringWithFormat:@"%@02",XY_LANGUAGE_TITLE_NAMED(@"app100001006", @"数据异常，同步终止")]];
#endif
        return;
    }
    __block float width = 0;
    __block float height = 0;
    NSString *fontPath = [NSString stringWithFormat:@"%@/font",DocumentsFontPath];
    NSInteger cPage = MAX((templateModel.currentPage-1),0);
    JCTemplateData *jcData = templateModel.toSdk(YES).configureBase64Background(YES).setCurrentPageIndex(cPage);
    NSString *jsonStr = [[jcData dataDict] xy_toJsonString];
    [ImageLibraryBridge initImageProcessing:fontPath error:nil];
#ifdef DEBUG
    if(![[NSFileManager defaultManager] fileExistsAtPath:RESOURCE_ETAG_TEMPLATE_PATH]){
        [[NSFileManager defaultManager] createDirectoryAtPath:RESOURCE_ETAG_TEMPLATE_PATH withIntermediateDirectories:YES attributes:nil error:nil];
    }
    [[NSFileManager defaultManager] removeItemAtPath:RESOURCE_ETAG_TEMPLATE_PATH error:nil];
    [XYCenter writeToFile:RESOURCE_ETAG_TEMPLATE_PATH fileName:[NSString stringWithFormat:@"屏幕%ld.text",index] data:jsonStr];
#endif
    NSLog(@"电子价签: 发起写入获取屏幕信息 +  %ld + %@",index,[XYTool getCurrentTimesWithoutTSZ]);
    [JCAPI_Etag getScreenInfos:(unsigned int)index withComplete:^(JCAPIEtagScreenInfosModel * _Nullable model) {
        if(model !=  nil){
            float screenWidth = model.len;
            float ratio = screenWidth / templateModel.width;
            NSLog(@"电子价签: 调用图像库绘制写入数据 + %@",[XYTool getCurrentTimesWithoutTSZ]);
            NSError *error = nil;
            [ImageLibraryBridge generateDataFromPrintJson:jsonStr
                                                  preview:NO
                                                    ratio:ratio
                                               printRatio:ratio
                                                    width:&width
                                                   height:&height
                                            printerMargin:@[@0,@0,@0,@0]
                                            printerOffset:@[@0,@0]
                                              orientation:0
                                               themeColor:nil
                                                    error:&error
                                                     data:^(NSData * imageData) {
                NSLog(@"电子价签: 调用图像库绘制写入数据 + %@",[XYTool getCurrentTimesWithoutTSZ]);
                dispatch_async(dispatch_get_main_queue(), ^{
                    __block NSMutableDictionary *infoDic = [NSMutableDictionary dictionary];
                    if(imageData != nil){
                        NSLog(@"电子价签: 发送数据至SDK + %ld + %@",index,[XYTool getCurrentTimesWithoutTSZ]);
                        [JCAPI_Etag sendDataToDevice:imageData withWidth:width withHieght:height withIndentifiers:(unsigned int)index withComplete:^(int errorCode) {
                            NSLog(@"电子价签: 数据发送完成 + %@",[XYTool getCurrentTimesWithoutTSZ]);
                            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                                if(errorCode == 0){
                                    if(model !=  nil){
                                        NSString *margin = [NSString stringWithFormat:@"%d,%d,%d,%d",model.top,model.left,model.bottom,model.right];
                                        [infoDic setValue:@(model.len) forKey:@"width"];
                                        [infoDic setValue:@(model.width) forKey:@"height"];
                                        [infoDic setValue:margin forKey:@"margin"];
                                        [infoDic setValue:@(model.seeWidth) forKey:@"oriHeight"];
                                        [infoDic setValue:@(model.seeLen) forKey:@"oriWidth"];
                                        [infoDic setValue:@(model.mixs) forKey:@"pixelMixture"];
                                        [infoDic setValue:@(model.pixel) forKey:@"pixelPoint"];
                                        [infoDic setValue:@(index) forKey:@"portSerial"];
                                        [infoDic setValue:@(model.number) forKey:@"pxMerge"];
                                        [infoDic setValue:@(model.sacnWay) forKey:@"scanModel"];
                                        [infoDic setValue:model.serialNo  forKey:@"serial"];
                                        [infoDic setValue:@(model.dataWay) forKey:@"takeModel"];
                                        [infoDic setValue:@(1) forKey:@"type"];
                                        complate(infoDic.xy_toJsonString);
                                    }else{
                                        complate(@0);
                                    }
                                }else{
                                    complate(@0);
                                }
                            });
                        }];
                    }else{
                        complate(@0);
                    }
                });
            }
            ];
        }else{
            complate(@0);
        }
    }];
}

//取消屏幕同步
+ (void)cancelWriteDataToEtagScreen{
    NSLog(@"电子价签:取消屏幕同步+  %@",[XYTool getCurrentTimesWithoutTSZ]);
    [JCAPI_Etag endSendPictures:^(int errorCode) {
        NSLog(@"取消电子价签数据同步");
    }];
}

/**
 电子价签固件升级
 */
+ (void)updatePrinterWithsVersion:(NSString *)sVersion path:(NSString *)path
                         crcValue:(NSString *)crcValue hVersion:(NSString *)hVersion
                     withComplete:(void(^)(int))withComplete
                   withOnProgress:(void(^)(float))withOnProgress{
    if(STR_IS_NIL(sVersion) || STR_IS_NIL(path) || STR_IS_NIL(crcValue) || STR_IS_NIL(hVersion)) {
        withComplete(-1);
        return;
    }
    NSLog(@"电子价签:开始固件升级+  %@",[XYTool getCurrentTimesWithoutTSZ]);
    [JCAPI_Etag updatePrinterWithsVersion:sVersion hVersion:hVersion
                                 crcValue:crcValue path:path
                             withComplete:withComplete withOnProgress:withOnProgress];
}

/**
 设置电子价签声音开关
 */
+ (void)setVoice:(NSInteger)voiceValue withComplete:(void(^)(int))complete{
    /*    [JCAPI_Etag setVoice:voiceValue withComplete:^(int errorCode) {
     [JCAPI_Etag getVoice:^(JCAPIEtagVoiceInfosModel * _Nullable voicemModel) {
     complete(errorCode);
     }];
     }];
     */
    NSLog(@"电子价签:设置声音开关 + 【%ld】  %@",voiceValue,[XYTool getCurrentTimesWithoutTSZ]);
    [JCAPI_Etag setVoice:voiceValue withComplete:complete];
}

/**
 设置电子价签自动关机时间
 */
+ (void)setCloseTime:(NSInteger)closeTime withComplete:(void(^)(int))complete{
    /*    [JCAPI_Etag setVoice:voiceValue withComplete:^(int errorCode) {
     [JCAPI_Etag getVoice:^(JCAPIEtagVoiceInfosModel * _Nullable voicemModel) {
     complete(errorCode);
     }];
     }];
     */
    NSLog(@"电子价签:设置关机时间档位+  %@",[XYTool getCurrentTimesWithoutTSZ]);
    [JCAPI_Etag setCloseTime:closeTime withComplete:^(int errorCode) {
        [JCAPI_Etag getCloseTime:^(JCAPIEtagVoiceInfosModel * _Nullable model) {
            complete(errorCode);
        }];
    }];
    
}

/**
 获取电子价签设备信息
 */
+ (void)getEtagDeviceDetail:(void(^)(NSDictionary *))complete{
    NSLog(@"电子价签:获取设备信息111 +  %@",[XYTool getCurrentTimesWithoutTSZ]);
    [JCAPI_Etag getDeviceInfos:^(JCAPIEtagDeviceInfosModel * _Nullable model) {
        NSLog(@"电子价签:获取关机时间 +  %@",[XYTool getCurrentTimesWithoutTSZ]);
        etagDeviceInfoModel = model;
        [JCAPI_Etag getCloseTime:^(JCAPIEtagVoiceInfosModel * _Nullable closeModel) {
            NSLog(@"电子价签:获取声音 +  %@",[XYTool getCurrentTimesWithoutTSZ]);
            [JCAPI_Etag getVoice:^(JCAPIEtagVoiceInfosModel * _Nullable voicemModel) {
                NSMutableDictionary *deviceInfoDic = [NSMutableDictionary dictionary];
                NSString *peripheralName = [[NSUserDefaults standardUserDefaults] valueForKey:LASTCONNECTETAGNAME];
                [deviceInfoDic setValue:UN_NIL(model.hardVer) forKey:PRINT_DEVICE_HARDWARE_TYPE];
                [deviceInfoDic setValue:UN_NIL(model.softVer) forKey:PRINT_DEVICE_FIRMWARE_TYPE];
                [deviceInfoDic setValue:UN_NIL(peripheralName) forKey:PRINT_DEVICE_NAME];
                [deviceInfoDic setValue:UN_NIL(model.mac) forKey:PRINT_DEVICE_MAC_ADDRESS];
                if(voicemModel.code == 0){
                    [deviceInfoDic setValue:StringFromInt((NSInteger)voicemModel.voiceOpenStatus) forKey:PRINT_DEVICE_POWER_VOICE];
                    [deviceInfoDic setValue:StringFromInt((NSInteger)voicemModel.voiceBlueStatus) forKey:PRINT_DEVICE_BLUETOOTH_VOICE];
                }
                if(closeModel.code == 0){
                    [deviceInfoDic setValue:StringFromInt((NSInteger)closeModel.closeTime) forKey:PRINT_DEVICE_AUTO_SHUTDOWN_DURATION];
                }
                bluetoothManager.deviceDict = deviceInfoDic;
                [[NSUserDefaults standardUserDefaults] setObject:deviceInfoDic forKey:@"etagInfo"];
                [bluetoothManager getNewVersion:^(JCDeviceFirmwareRemoteModel *model){ //获取最新固件版本
                    
                }];
                complete(deviceInfoDic);
            }];
        }];
    }];
}

/**
 重置电子价签屏幕信息
 */
+(void)resetTagStatusComplete:(void(^)(NSInteger))complete{
    NSInteger screenCount = 6;
    if(etagDeviceInfoModel != nil){
        screenCount = etagDeviceInfoModel.numberOfScreens;
    }
    NSMutableArray *screenNumArr = [NSMutableArray array];
    for (NSInteger index = 1; index <= screenCount; index++) {
        [screenNumArr addObject:@(index)];
    }
    NSLog(@"电子价签:重置屏幕状态 +  %@",[XYTool getCurrentTimesWithoutTSZ]);
    [JCAPI_Etag resetTagStatus:screenNumArr withComplete:^(int errorCode) {
        NSLog(@"电子价签:结束刷屏 +  %@",[XYTool getCurrentTimesWithoutTSZ]);
        if(errorCode == 0){
            [JCAPI_Etag endSendPictures:^(int errorCode) {
                NSLog(@"取消电子价签数据同步");
                complete(errorCode);
            }];
        }else{
            complete(errorCode);
        }
    }];
}
@end
