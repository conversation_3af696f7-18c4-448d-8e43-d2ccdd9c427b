//
//  JCIAPHelper.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2021/8/25.
//  Copyright © 2021 Jingchen Technology Co.Ltd  . All rights reserved.

#import <StoreKit/StoreKit.h>
#import "JCIAPHelper.h"
#import "JCShopNormalVC.h"
#import "JCRedeemCodeViewController.h"
#import "JCVIPAutoRenewViewController.h"
#import "JCVIPDetailViewController.h"
#import "JCNoLoginPaySuccessView.h"
#import "JCVipSuccessTipView.h"
#import "JCVIPPayResultViewController.h"
//内购恢复过程
typedef NS_ENUM(NSInteger, ENUMRestoreProgress) {
    ENUMRestoreProgressStop = 0,                // 尚未开始请求
    ENUMRestoreProgressStart = 1,               // 开始请求
    ENUMRestoreProgressUpdatedTransactions = 2, // 更新了事务
    ENUMRestoreProgressFinish = 3,              // 完成请求
};

@interface JCIAPHelper () <SKPaymentTransactionObserver, SKProductsRequestDelegate> {
    NSString *_productId;
    NSString *_orderId;
    IAPCompletionHandle _handle;
}


// 判断一份交易获得验证的次数  key为随机值
@property(nonatomic, strong) NSMutableDictionary<NSString *, NSNumber *> *transactionCountMap;
// 需要验证的支付事务
@property(nonatomic, strong) NSMutableDictionary<NSString *, NSMutableSet<SKPaymentTransaction *> *> *transactionFinishMap;

@property(nonatomic, assign) ENUMRestoreProgress restoreProgress;

@end


@implementation JCIAPHelper

+ (instancetype)sharedInstance {
    static JCIAPHelper *_IAPInstabce = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _IAPInstabce = [[JCIAPHelper alloc] init];
    });
    return _IAPInstabce;
}

- (instancetype)init {
    if (self = [super init]) {
        // 购买监听写在程序入口,程序挂起时移除监听,这样如果有未完成的订单将会自动执行并回调 paymentQueue:updatedTransactions:方法
        [self addTransactionObserver];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReciveSynchNotification) name:JCNOTICATION_SYNCH_CHANGE object:nil];
        self.isShowingUNIAP = NO;
        self.isShowAllInfo = YES;
    }
    return self;
}

- (void)dealloc {
    [self removeTransactionObserver];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)clean{
    _productId = nil;
    _orderId = nil;
    _handle = nil;
}
- (void)setVipIAPSence:(VipSence)vipSence{
    if(vipSence == CableVip){
        self.isShowAllInfo = NO;
    }else{
        self.isShowAllInfo = YES;
    }
    self.vipSence = vipSence;
    [self.vipThemeConfig setThemeConfigWithSence:vipSence];
}

- (JCVipThemeConfig *)vipThemeConfig{
    if(!_vipThemeConfig){
        _vipThemeConfig = [[JCVipThemeConfig alloc] init];
    }
    return _vipThemeConfig;
}

- (void)setCableVipInfoModel:(JCUserCenterInfoModel *)cableVipInfoModel{
    _cableVipInfoModel = cableVipInfoModel;
    NSMutableArray *cableVipIdsArr = [NSMutableArray array];
    for (JCVIPCostInfoModel *priceModel in [JCIAPHelper sharedInstance].cableVipInfoModel.prices) {
        [cableVipIdsArr addObject:priceModel.productId];
    }
    _cableVipProductIds = cableVipIdsArr;
}

- (void)addTransactionObserver {
    [[SKPaymentQueue defaultQueue] addTransactionObserver:self];
}

- (void)removeTransactionObserver {
    [[SKPaymentQueue defaultQueue] removeTransactionObserver:self];
}

//从appstore 直接打开应用 “itms-services://?action=purchaseIntent&bundleId=bundleId&productIdentifier=productId”
- (BOOL)paymentQueue:(SKPaymentQueue *)queue shouldAddStorePayment:(SKPayment *)payment forProduct:(SKProduct *)product {
    // product里存放的有我们配置在App Store的产品ID，以及价格等等。
    UIViewController *rootVC = [XYTool getCurrentVC];
    if ([rootVC.presentedViewController isKindOfClass:[JCVIPDetailViewController class]]) {
        return NO;
    }
    AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
    if(!appDelegate.appLaunched){
        appDelegate.needToVip = YES;
    }else{
        JCVIPDetailViewController *vc = [[JCVIPDetailViewController alloc] init];
        XYNavigationController *nav = [[XYNavigationController alloc] initWithRootViewController:vc];
        [rootVC presentViewController:nav animated:YES completion:nil];
    }
    return NO;
}
#pragma mark - public method

// 订阅
- (void)startSubscribeWithProductId:(NSString *)productId orderId:(NSString *)orderId completeHandle:(IAPCompletionHandle )handle {
    if (!productId) {
        [self handleActionWithType:IAPPurchEmptyID data:@{@"userId":UN_NIL(m_userModel.userid),@"orderId":UN_NIL(orderId)}];
        return;
    }

    if (![SKPaymentQueue canMakePayments]) {
        [self handleActionWithType:IAPPurchNotAllow data:@{@"userId":UN_NIL(m_userModel.userid),@"orderId":UN_NIL(orderId)}];
        return;
    }

    _productId = productId;
    _orderId = orderId;
    _handle = handle;
    NSSet *set = [NSSet setWithArray:@[productId]];
    SKProductsRequest *request = [[SKProductsRequest alloc] initWithProductIdentifiers:set];
    request.delegate = self;
    [request start];
}

- (void)setVipSence:(VipSence)vipSence{
  _vipSence = vipSence;
}

- (BOOL)isSupportCurrrentCountryWithSKU:(NSString *)skuStr{
    BOOL isSupport = NO;
    NSArray *products = [XYCenter sharedInstance].storeProductArr;
    //未获取到所有内购信息
    if(products.count == 0){
        isSupport = YES;
    }else{
        //遍历所有SKU 当匹配到时支持当前SKU
        for (SKProduct *pro in products) {
            if([[pro productIdentifier] isEqualToString:skuStr]){
                isSupport = YES;
                break;
            }
        }
    }
    return isSupport;
}

#pragma mark - SKProductsRequestDelegate
//发送请求后 会回调  执行这个方法
- (void)productsRequest:(SKProductsRequest *)request didReceiveResponse:(SKProductsResponse *)response {
    NSArray *products = response.products;
    if ([products count] <= 0) {
        NSLog(@"--------------没有商品------------------");
        [self handleActionWithType:IAPPurchNoProduct data:@{@"userId":UN_NIL(m_userModel.userid),@"orderId":UN_NIL(_orderId),@"transactionId":@""}];
        return;
    }

    SKProduct *p = nil;
    for (SKProduct *pro in products) {
        if ([pro.productIdentifier isEqualToString:_productId]) {
            p = pro;
            break;
        }
    }
    NSLocale * locat = [NSLocale currentLocale];
    NSString * strlocate = [locat objectForKey:NSLocaleCountryCode];

    NSLog(@"设备国家==%@",strlocate);

    NSLocale * storeLocale = p.priceLocale;
    NSString * storeCountry = (NSString *)CFLocaleGetValue((CFLocaleRef)storeLocale,kCFLocaleCountryCode);
    NSLog(@"存储国家==%@",storeCountry);
    NSLog(@"productID:%@", response.invalidProductIdentifiers);
    NSLog(@"产品付费数量:%lu", (unsigned long) [products count]);
    NSLog(@"%@", [p description]);
    NSLog(@"%@", [p localizedTitle]);
    NSLog(@"%@", [p localizedDescription]);
    NSLog(@"%@", [p price]);
    NSLog(@"%@", [p priceLocale].localeIdentifier);
    NSLog(@"%@", [p productIdentifier]);
    NSLog(@"发送购买请求");
    // 生成可变订单
    SKMutablePayment *payment = [SKMutablePayment paymentWithProduct:p];
    // 设置用户ID
    if(xy_isLogin){

    }else{

    }
    payment.applicationUsername = UN_NIL(m_userModel.userid);

    [[SKPaymentQueue defaultQueue] addPayment:payment];
}



// 恢复购买
- (void)restorePurchasesWithCompleteHandle:(IAPCompletionHandle)handle {
    //开始恢复
    _restoreProgress = ENUMRestoreProgressStart;
    _handle = handle;
    _orderId = nil;
    [[SKPaymentQueue defaultQueue] restoreCompletedTransactions];
}

#pragma mark - SKPaymentTransactionObserver
// 队列操作后的回调
- (void)paymentQueue:(SKPaymentQueue *)queue updatedTransactions:(NSArray<SKPaymentTransaction *> *)transactions {
    //判断是否为恢复购买的请求
    if (_restoreProgress == ENUMRestoreProgressStart) {
        _restoreProgress = ENUMRestoreProgressUpdatedTransactions;
    }

    NSString *operationId = [[NSUUID UUID] UUIDString];

    [self.transactionFinishMap setValue:[NSMutableSet set] forKey:operationId];
    [self.transactionCountMap setValue:@(transactions.count) forKey:operationId];
    NSInteger index = 0;
    for (SKPaymentTransaction *tran in transactions) {
        switch (tran.transactionState) {
            case SKPaymentTransactionStatePurchased:{
                [[SKPaymentQueue defaultQueue] finishTransaction:tran];// 销毁本次操作，由本地数据库进行记录和恢复
                [self completeTransaction:tran operationId:operationId];
            } break;
            case SKPaymentTransactionStatePurchasing:{
                NSLog(@"正在购买");
            } break;
            case SKPaymentTransactionStateRestored:{
                NSLog(@"已经购买过商品%@",tran.transactionIdentifier);
                [[SKPaymentQueue defaultQueue] finishTransaction:tran];
                if(index >= 1) break;
                [self restoreTransaction:tran operationId:operationId];
                index ++;
            } break;
            case SKPaymentTransactionStateFailed:{
                [[SKPaymentQueue defaultQueue] finishTransaction:tran];
                [self failedTransaction:tran];
            } break;
            default:
                break;
        }
    }
}

// 恢复购买结束回调
- (void)paymentQueueRestoreCompletedTransactionsFinished:(SKPaymentQueue *)queue {
    // 没有进入- (void)paymentQueue:(SKPaymentQueue *)queue updatedTransactions:(NSArray<SKPaymentTransaction *> *)transactions 方法
    // 恢复产品数量为0  提前结束
    if(_restoreProgress != ENUMRestoreProgressUpdatedTransactions){
        [self handleActionWithType:IAPPurchRestoreNotBuy data:nil];
    }
    _restoreProgress = ENUMRestoreProgressFinish;
}

// 恢复购买失败
- (void)paymentQueue:(SKPaymentQueue *)queue restoreCompletedTransactionsFailedWithError:(NSError *)error {
    //恢复失败
    if(_restoreProgress != ENUMRestoreProgressUpdatedTransactions){
        [self handleActionWithType:IAPPurchRestoreFailed data:@{@"userId":UN_NIL(m_userModel.userid),@"orderId":UN_NIL(_orderId),@"error":error.localizedDescription}];
    }
    _restoreProgress = ENUMRestoreProgressFinish;

}


#pragma mark - transaction action
// 恢复购买
- (void)restoreTransaction:(SKPaymentTransaction *)transaction operationId:(NSString *)operationId {
    [self verifyPurchaseWithPaymentTransaction:transaction isTestServer:NO operationId:operationId];
}

// 完成交易
- (void)completeTransaction:(SKPaymentTransaction *)transaction operationId:(NSString *)operationId {
    [self verifyPurchaseWithPaymentTransaction:transaction isTestServer:NO operationId:operationId];
}

// 交易失败
- (void)failedTransaction:(SKPaymentTransaction *)transaction {
    if (transaction.error.code != SKErrorPaymentCancelled) {
        [self handleActionWithType:IAPPurchFailed data:@{@"userId":UN_NIL(m_userModel.userid),@"orderId":UN_NIL(_orderId),@"error":transaction.error.localizedDescription}];
    } else {
        [JCRecordTool recordWithAction:vip_open_cancel withContent:@"vip_open_cancel" isClickEvent:YES parmeters:@{}];
        [self handleActionWithType:IAPPurchCancle data:@{@"userId":UN_NIL(m_userModel.userid),@"orderId":UN_NIL(_orderId),@"transactionId":UN_NIL(transaction.transactionIdentifier)} invokeHandle:YES];
    }
    NSString *detail = @"";
    if (transaction.error != nil) {
        switch (transaction.error.code) {
            case SKErrorUnknown:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorUnknown",@"未知的错误，您可能正在使用越狱手机",transaction.error];
                break;
            case SKErrorClientInvalid:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorClientInvalid",@"当前苹果账户无法购买商品(如有疑问，可以询问苹果客服)",transaction.error];
                break;
            case SKErrorPaymentCancelled:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorPaymentCancelled",@"订单已取消",transaction.error];
                break;
            case SKErrorPaymentInvalid:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorPaymentInvalid",@"订单无效(如有疑问，可以询问苹果客服)",transaction.error];
                break;
            case SKErrorPaymentNotAllowed:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorPaymentNotAllowed",@"当前苹果设备无法购买商品(如有疑问，可以询问苹果客服)",transaction.error];
                break;
            case SKErrorStoreProductNotAvailable:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorStoreProductNotAvailable",@"当前商品不可用",transaction.error];
                break;
            case SKErrorCloudServicePermissionDenied:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorCloudServicePermissionDenied",@"user has not allowed access to cloud service information",transaction.error];
                break;
            case SKErrorCloudServiceNetworkConnectionFailed:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorCloudServiceNetworkConnectionFailed",@"- the device could not connect to the nework",transaction.error];
                break;
            case SKErrorCloudServiceRevoked:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorCloudServiceRevoked",@"user has revoked permission to use this cloud service",transaction.error];
                break;
            case SKErrorPrivacyAcknowledgementRequired:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorPrivacyAcknowledgementRequired",@" The user needs to acknowledge Apple's privacy policy",transaction.error];
                break;
            case SKErrorUnauthorizedRequestData:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorUnauthorizedRequestData",@"The app is attempting to use SKPayment's requestData property, but does not have the appropriate entitlement",transaction.error];
                break;
            case SKErrorInvalidOfferIdentifier:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorInvalidOfferIdentifier",@"The specified subscription offer identifier is not valid",transaction.error];
                break;
            case SKErrorInvalidSignature:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorInvalidSignature",@"The cryptographic signature provided is not valid",transaction.error];
                break;
            case SKErrorMissingOfferParams:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorMissingOfferParams",@"One or more parameters from SKPaymentDiscount is missing",transaction.error];
                break;
            case SKErrorInvalidOfferPrice:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorInvalidOfferPrice",@"The price of the selected offer is not valid (e.g. lower than the current base subscription price)",transaction.error];
                break;
            case SKErrorOverlayCancelled:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorOverlayCancelled",@"",transaction.error];
                break;
            case SKErrorOverlayInvalidConfiguration:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorOverlayInvalidConfiguration",@"",transaction.error];
                break;
            case SKErrorOverlayTimeout:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorOverlayTimeout",@"",transaction.error];
                break;
            case SKErrorIneligibleForOffer:
                detail = [NSString stringWithFormat:@"%@---%@---errorDescr:%@",@"SKErrorIneligibleForOffer",@"User is not eligible for the subscription offer",transaction.error];
                break;
            default:
                NSLog(@"No Match Found for error");
                detail = @"未知错误";
                break;
        }
    }
    uploadLogInfoFlutter(UN_NIL(detail), @"VIP_Log",^(id x,id y){},J_get_sls_Log_Token);
}

// 交易验证
- (void)verifyPurchaseWithPaymentTransaction:(SKPaymentTransaction *)transaction isTestServer:(BOOL)flag operationId:(NSString *)operationId {
    //交易验证
    NSURL *recepitURL = [[NSBundle mainBundle] appStoreReceiptURL];
    NSData *receipt = [NSData dataWithContentsOfURL:recepitURL];
    if (!receipt) {
        // 交易凭证为空验证失败
        [self handleActionWithType:IAPPurchVerFailed data:@{@"userId":UN_NIL(m_userModel.userid),@"orderId":UN_NIL(_orderId)}];
        return;
    }
    NSDictionary *receiptDict = [NSJSONSerialization JSONObjectWithData:receipt options:0 error:nil];
    // 购买成功将交易凭证发送给服务端进行再次校验
    NSError *error;
    NSString *productId = transaction.payment.productIdentifier;
    NSDate *transactionDate = transaction.transactionDate;
    NSString *timeStamp = [XYTool timeStampFrom:transactionDate];
    NSString *receiptData = [receipt base64EncodedStringWithOptions:0];
    NSDictionary *requestContents = @{@"receipt-data": receiptData};
    NSData *requestData = [NSJSONSerialization dataWithJSONObject:requestContents options:0 error:&error];
    NSString *transactionId = transaction.transactionIdentifier;
    NSString *originalTransactionId = @"";
    if (transaction.originalTransaction) {
        // 如果是自动续费的订单,originalTransaction会有内容
        originalTransactionId = transaction.originalTransaction.transactionIdentifier;
    }
    if (!requestData) {
        [self handleActionWithType:IAPPurchVerFailed data:nil];
        return;
    }
    if(!xy_isLogin && !STR_IS_NIL(_orderId) && !STR_IS_NIL(_productId) && [_productId isEqualToString:productId]){
        NSMutableDictionary *infoDic = [NSMutableDictionary dictionaryWithDictionary:receiptDict];
        [infoDic setObject:productId forKey:@"productId"];
        NSDictionary *currentOrderInf = @{@"receiptData":receiptData,@"orderInfo":@{@"niimbotOrderNo":UN_NIL(_orderId),
                                                                                    @"purchaseDateMs":timeStamp,
                                                                                    @"transactionId":UN_NIL(transactionId),
                                                                                    @"originalTransactionId":UN_NIL(originalTransactionId),
                                                                                    @"productIdentifier":UN_NIL(productId)
        }};
        [self saveDeviceIAPInfo:currentOrderInf];
        NSLog(@"匿名购买信息 购买时间:%@ 订单Id：%@ 产品Id%@ 交易Id：%@ 原始交易Id：%@",timeStamp,_orderId,productId,transactionId,originalTransactionId);
        return;
    }
    NSMutableDictionary *incompleteDic = [JCKeychainTool getIn_AppInfo].mutableCopy;
    if(incompleteDic == nil){
        incompleteDic = [NSMutableDictionary dictionary];
    }
    __block NSDictionary *inappInfo = nil;
    NSInteger appEnv = 0;
#ifdef  JCTest
    appEnv = app_laboratory_env;
#elif   Dev
    appEnv = 2;
#endif
    if(transaction.transactionState == SKPaymentTransactionStateRestored){
        inappInfo = @{@"userId":UN_NIL(m_userModel.userid),@"originalTransactionId":originalTransactionId,@"receiptData":receiptData,@"isRestored":@"1",@"env":@(appEnv)};
        if(![incompleteDic.allKeys containsObject:transactionId]){
            [incompleteDic setValue:inappInfo forKey:transaction.transactionIdentifier];
            [JCKeychainTool save:KEY_IN_APP_INSTEAD data:incompleteDic];
        }
        [self checkBySeverWith:transaction operationId:operationId];
    }else{
        //获取服务器时间
      inappInfo = @{@"userId":UN_NIL(m_userModel.userid),@"orderId":UN_NIL(_orderId),@"originalTransactionId":UN_NIL(originalTransactionId),@"transactionId":UN_NIL(transactionId),@"receiptData":receiptData,@"productId":UN_NIL(productId),@"time":UN_NIL(timeStamp),@"env":@(appEnv),@"vipSence":@(self.vipSence)};
        NSLog(@"验证订单数据%@",inappInfo.xy_toJsonString);
        if(![incompleteDic.allKeys containsObject:transactionId]){
            [incompleteDic setValue:inappInfo forKey:transaction.transactionIdentifier];
            [JCKeychainTool save:KEY_IN_APP_INSTEAD data:incompleteDic];
        }
        [self checkBySeverWith:transaction operationId:operationId];
    }
}

- (void)checkBySeverWith:(SKPaymentTransaction *)transaction operationId:(NSString *)operationId{
    NSDictionary *incompleteDic1 = [JCKeychainTool getIn_AppInfo];
    NSString *requestPath = J_order_verify;
    NSNumber *vipSenceNumber = incompleteDic1[@"vipSence"];
    // 交易凭证为空验证失败1
    NSString *transactionId = transaction.transactionIdentifier;
    NSDictionary *requestDic = [incompleteDic1 objectForKey:transactionId];
    NSMutableDictionary *incompleteDic = requestDic.mutableCopy;
    NSLog(@"匿名购买信息 内购验证上报接口调用1：%@",requestDic.xy_toJsonString);
    if(self.vipSence == AssetVip || vipSenceNumber.integerValue == AssetVip){
      if(STR_IS_NIL(_orderId)){
        [self handleActionWithType:IAPPurchVerSuccess data:@{} invokeHandle:YES];
        return;
      }else{
        requestPath = J_asset_order_verify;
      }
    }
    incompleteDic[@"needDesRequest"] = @"1";
    NSString *orderVerifyParms = incompleteDic.xy_toJsonString;
    [incompleteDic xy_postWithModelType:nil Path:requestPath hud:nil tag:0 timeoutInterval:30 Success:^(__kindof YTKBaseRequest *request, id model) {
        NSMutableDictionary *incompleteDic = [JCKeychainTool getIn_AppInfo].mutableCopy;
        if([incompleteDic.allKeys containsObject:transactionId]){
            [incompleteDic removeObjectForKey:transactionId];
            [JCKeychainTool save:KEY_IN_APP_INSTEAD data:incompleteDic];
        }
        // 订单总数量
        NSInteger totalCount = [[self.transactionCountMap valueForKey:operationId] integerValue];
        // 已执行数量
        NSMutableSet *finishSet = [self.transactionFinishMap valueForKey:operationId];
        [finishSet addObject:transaction];
        // 需在添加对象后获得对象数量 不然有极低的可能遇到并发问题 而导致不执行回调
        dispatch_async(dispatch_get_main_queue(), ^{
            if(_restoreProgress == ENUMRestoreProgressFinish){
                _restoreProgress = ENUMRestoreProgressStop;
                [self handleActionWithType:IAPPurchVerSuccess data:requestDic invokeHandle:YES];
            }else{
                [self handleActionWithType:IAPPurchVerSuccess data:requestDic invokeHandle:finishSet.count  == totalCount];
            }

        });
    } failure:^(NSString *msg, id model) {
        NSMutableDictionary *incompleteDic = [JCKeychainTool getIn_AppInfo].mutableCopy;
        BOOL isRestore = NO;
        if([incompleteDic.allKeys containsObject:transactionId]){
            NSDictionary *infoDic = incompleteDic[transactionId];
            if([infoDic[@"isRestored"] isEqualToString:@"1"]){
                isRestore = YES;
            }
        }
        if([msg isEqualToString:@"OK"] && isRestore){
            if([incompleteDic.allKeys containsObject:transactionId]){
                [incompleteDic removeObjectForKey:transactionId];
                [JCKeychainTool save:KEY_IN_APP_INSTEAD data:incompleteDic];
            }
            // 订单总数量
            NSInteger totalCount = [[self.transactionCountMap valueForKey:operationId] integerValue];
            // 已执行数量
            NSMutableSet *finishSet = [self.transactionFinishMap valueForKey:operationId];
            [finishSet addObject:transaction];
            if(finishSet.count == totalCount){
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self handleActionWithType:IAPPurchVerFailed data:requestDic invokeHandle:YES];
                });
            }else if(transaction.transactionState == SKPaymentTransactionStateRestored){
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self handleActionWithType:IAPPurchVerFailed data:requestDic invokeHandle:YES];
                });
            }
        }else{
            dispatch_async(dispatch_get_main_queue(), ^{
                [self handleActionWithType:IAPPurchVerFailed data:requestDic];
            });
        }
    }];
}

//请求失败
- (void)request:(SKRequest *)request didFailWithError:(NSError *)error {
    [self handleActionWithType:IAPPurchFailed data:@{@"userId":UN_NIL(m_userModel.userid),@"orderId":UN_NIL(_orderId),@"error":error.localizedDescription}];
    NSLog(@"------------------错误-----------------:%@", error);
    NSString *errorInfo = [NSString stringWithFormat:@"%@---errorDescr:%@",@"RequestErr",error];
    uploadLogInfoFlutter(UN_NIL(errorInfo), @"VIP_Log",^(id x,id y){},J_get_sls_Log_Token);
}

- (void)requestDidFinish:(SKRequest *)request {
    NSLog(@"------------反馈信息结束-----------------");
}


#pragma mark - private method

//适配器模式
- (void)handleActionWithType:(IAPPurchType)type data:(NSDictionary *)dict invokeHandle:(BOOL)invoke {
    NSString *orderIdString = dict[@"orderId"];


    if([orderIdString isEqualToString:_orderId]){
        _orderId = @"";
    }
    switch (type) {
        case IAPPurchSuccess:
            NSLog(@"购买成功");
            break;
        case IAPPurchFailed:
            NSLog(@"购买失败");
            [JCRecordTool recordWithAction:vip_open_faild withContent:@"vip_open_faild" isClickEvent:YES parmeters:@{}];
            break;
        case IAPPurchCancle:
            NSLog(@"用户取消购买");
            [JCRecordTool recordWithAction:vip_open_cancel withContent:@"vip_open_cancel" isClickEvent:YES parmeters:@{}];
            break;
        case IAPPurchVerFailed:
            NSLog(@"订单校验失败");
            break;
        case IAPPurchVerSuccess:
            NSLog(@"订单校验成功");
            [JCRecordTool recordWithAction:vip_open_success withContent:@"vip_open_success" isClickEvent:YES parmeters:@{}];
            break;
        case IAPPurchNotAllow:
            NSLog(@"不允许程序内付费");
            break;
        case IAPPurchRestoreNotBuy:
            NSLog(@"购买数量为0");
            break;
        case IAPPurchRestoreFailed:
            NSLog(@"内购恢复失败");
            break;
        case IAPPurchEmptyID:
            NSLog(@"商品ID为空");
            break;
        case IAPPurchNoProduct:
            NSLog(@"没有可购买商品");
            break;
        default:
            break;
    }
    //因为购买成功并不是最后一个步骤 没有意义 不进行处理,需要完成验证
    if (xy_isLogin && type == IAPPurchSuccess) {

        return;
    }

    if (invoke && _handle) {
        _handle(type, dict);
    }
}

//完成回调 自己的block
- (void)handleActionWithType:(IAPPurchType)type data:(NSDictionary *)dict {
    [self handleActionWithType:type data:dict invokeHandle:YES];
}

#pragma mark - getter & setter
- (NSMutableDictionary *)transactionFinishMap {
    if (!_transactionFinishMap) {
        _transactionFinishMap = [NSMutableDictionary dictionary];
    }
    return _transactionFinishMap;
}


- (NSMutableDictionary *)transactionCountMap {
    if (!_transactionCountMap) {
        _transactionCountMap = [NSMutableDictionary dictionary];
    }
    return _transactionCountMap;
}

- (void)didReciveSynchNotification{
    NSMutableDictionary *incompleteDic = [JCKeychainTool getIn_AppInfo].mutableCopy;
    if(incompleteDic.count == 0 || (NETWORK_STATE_ERROR)){
        return;
    }else{
        for (NSString *transactionId in incompleteDic.allKeys) {
            NSDictionary *requestDic = [incompleteDic objectForKey:transactionId];
            NSLog(@"匿名购买信息 内购验证上报接口调用1：%@",requestDic.xy_toJsonString);
            [requestDic xy_postWithModelType:nil Path:J_order_verify hud:nil tag:0 timeoutInterval:30 Success:^(__kindof YTKBaseRequest *request, id model) {
                NSMutableDictionary *incompleteDic = [JCKeychainTool getIn_AppInfo].mutableCopy;
                if([incompleteDic.allKeys containsObject:transactionId]){
                    [incompleteDic removeObjectForKey:transactionId];
                    [JCKeychainTool save:KEY_IN_APP_INSTEAD data:incompleteDic];
                }
                dispatch_async(dispatch_get_main_queue(), ^{
                    if([requestDic[@"userId"] isEqualToString:m_userModel.userid]){
                        [[XYCenter sharedInstance] getNewUserModelInfo:^(__kindof YTKBaseRequest *request, id model) {
                            [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_VIP_OPERATE_SUCCESS object:nil];
                        } failure:^(NSString *msg, id model) {

                        } isNeedRefreshLoginInfo:NO];
                    }
                });
            } failure:^(NSString *msg, id model) {

            }];
        }
    }
}

+ (void)checkLoginAndOpenUserVIP:(UIViewController *)sourceVC success:(void(^)(void))success failure:(void((^)(NSString *msg, id model)))failure{
    [[JCLoginManager sharedInstance] checkLogin:^{

    } viewController:sourceVC  loginSuccessBlock:^{
        //        [weakSelf openUserVIPWith:sourceVC success:success failure:failure];
    }];
}

+ (BOOL)getVipIsNeedReNew{
    BOOL isNeedRenewVIP = NO;
    if([JCIAPHelper sharedInstance].vipSence == CableVip)
    {
        if(m_userModel.products.count > 0){
            for (NSDictionary *vipInfo in m_userModel.products) {
                if(vipInfo[@"productCode"] != nil && [((NSString *)vipInfo[@"productCode"]) isEqualToString:@"CABLE_VIP"]){
                    isNeedRenewVIP = YES;
                    break;
                }
            }
        }else{
            isNeedRenewVIP = NO;
        }
    }else{
        isNeedRenewVIP = m_userModel.vipInfo != nil;
    }
    return isNeedRenewVIP;
}

+ (void)toVipAgmentWith:(UIViewController *)sourceVc targetVc:(UIViewController *)targetVc{
    XYNavigationController *nav = [[XYNavigationController alloc] initWithRootViewController:targetVc];
    UINavigationController *currentNav = [XYTool getCurrentNavVC];
    BOOL hasDirtyController = NO;
    if([sourceVc isKindOfClass:[FBFlutterViewContainer class]] && [((FBFlutterViewContainer *)sourceVc).name isEqualToString:@"vipTrial"]){
        hasDirtyController = YES;
    }
    if(!hasDirtyController && sourceVc.navigationController == nil){
        [sourceVc presentViewController:nav animated:YES completion:^{

        }];
    }else{
        if(hasDirtyController){
            [currentNav pushViewController:targetVc animated:YES];
        }else{
            [sourceVc.navigationController pushViewController:targetVc animated:YES];
        }
    }
}

+ (JCVIPOperateAlert *)openUserVIPWith:(UIViewController *)sourceVC success:(void(^)(void))success failure:(void((^)(NSString *msg, id model)))failure  sourceInfo:(NSDictionary *)sourceInfo{
    NSString *anchorSubscribeId = [sourceInfo objectForKey:@"anchorSubscribeId"];
    BOOL isNeedRenewVIP = [self getVipIsNeedReNew];
    JCVIPOperateAlert *vipOperateAlert = [[JCVIPOperateAlert alloc] initWithFrame:CGRectZero isNeedRenewVIP:isNeedRenewVIP showAllInfo:[JCIAPHelper sharedInstance].isShowAllInfo animation:YES];
    vipOperateAlert.sourcePage = [sourceInfo objectForKey:@"sourcePage"];
    vipOperateAlert.eventTitle = [sourceInfo objectForKey:@"act_name"];
    vipOperateAlert.anchorSubscribeId = anchorSubscribeId;
    __weak typeof(vipOperateAlert) weakVipOperateAlert = vipOperateAlert;
    [vipOperateAlert setOperateVIPBlock:^(NSString *priceId,NSString *proId, NSString *isSubcripe) {
        if(xy_isLogin && STR_IS_NIL(m_userModel.phone) && STR_IS_NIL(m_userModel.email)){
            [[JCLoginManager sharedInstance] checkBindWithviewController:sourceVC withBindType:1 withResponse:^{
                [weakVipOperateAlert hide];
            } withComplete:^(id x) {

            }];
            return;
        }
        NSMutableDictionary *newSourceInfo = @{}.mutableCopy;
        [newSourceInfo addEntriesFromDictionary:sourceInfo];
        [newSourceInfo setObject:isSubcripe forKey:@"autoRenewal"];
        if(!([[newSourceInfo allKeys] containsObject:@"gifts"] && IsNotEmptyDictionary(newSourceInfo[@"gifts"]) && [[XYCenter sharedInstance] isShowVipBenefits])){
            if(vipOperateAlert.vipInfoModel && IsNotEmptyDictionary(vipOperateAlert.vipInfoModel.gifts)){
                [newSourceInfo setObject:weakVipOperateAlert.vipInfoModel.gifts forKey:@"gifts"];
            }
        }

        [self acticityVIPWith:priceId productId:proId success:^{
            if(success){
                success();
            }
            [weakVipOperateAlert hide];
        } failure:^(NSString *msg, id model) {
            [MBProgressHUD showToastWithMessageDarkColor:msg];
        } closeAlertBlock:^{
            [weakVipOperateAlert hide];
        }sourceInfo:newSourceInfo sourceVC:sourceVC];
    }];
    [vipOperateAlert setVipEventBlock:^(NSString *link,NSString *title,NSString *typeCode) {
        if(STR_IS_NIL(link)){
            return;
        }
        if(![[NBCAPMiniAppManager sharedInstance] hasUniAppActivity] || sourceVC.navigationController == nil) [weakVipOperateAlert hide];
        [JCToNativeRouteHelp bannberJumpWith:typeCode routeString:link title:title sourceVC:sourceVC];
    }];
    VipSence currentSence = [JCIAPHelper sharedInstance].vipSence;
    [vipOperateAlert setVipAgreeBlock:^{
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }

        JCUserAgmentController *vc = [[JCUserAgmentController alloc] initWithDismissBlock:^{
            if(![[NBCAPMiniAppManager sharedInstance] hasUniAppActivity] || sourceVC.navigationController == nil) {
                [[JCIAPHelper sharedInstance] setVipIAPSence:currentSence];
                [JCIAPHelper openUserVIPWith:sourceVC success:success failure:failure sourceInfo:sourceInfo];
            }
        }];
        vc.sence = [JCIAPHelper sharedInstance].vipSence;
        vc.type = 3;//
        [weakVipOperateAlert hide];
        [self toVipAgmentWith:sourceVC targetVc:vc];
        NSLog(@"VIP协议");
    }];
    [vipOperateAlert setVipRenewAgreeBlock:^{
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        JCUserAgmentController *vc = [[JCUserAgmentController alloc] initWithDismissBlock:^{
            if(![[NBCAPMiniAppManager sharedInstance] hasUniAppActivity] || sourceVC.navigationController == nil) {
                [[JCIAPHelper sharedInstance] setVipIAPSence:currentSence];
                [JCIAPHelper openUserVIPWith:sourceVC success:success failure:failure sourceInfo:sourceInfo];
            }
        }];
        vc.sence = [JCIAPHelper sharedInstance].vipSence;
        vc.type = 4;
        [weakVipOperateAlert hide];
        [self toVipAgmentWith:sourceVC targetVc:vc];
        NSLog(@"VIP协议");
    }];
    [vipOperateAlert setVipRenewDescrBlock:^{
        NSDictionary *autoPriceDic = [XYCenter sharedInstance].autoNewPriceDic;
        if((NETWORK_STATE_ERROR) || autoPriceDic.count == 0){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        JCUserAgmentController *vc = [[JCUserAgmentController alloc] initWithDismissBlock:^{
            if(![[NBCAPMiniAppManager sharedInstance] hasUniAppActivity] || sourceVC.navigationController == nil) {
                [[JCIAPHelper sharedInstance] setVipIAPSence:currentSence];
                [JCIAPHelper openUserVIPWith:sourceVC success:success failure:failure sourceInfo:sourceInfo];
            }
        } parmDic:autoPriceDic];
        vc.type = 5;
        vc.sence = [JCIAPHelper sharedInstance].vipSence;
        [weakVipOperateAlert hide];
        [self toVipAgmentWith:sourceVC targetVc:vc];
        NSLog(@"VIP协议");
    }];
    [vipOperateAlert setRestoreBlock:^{
        if((NETWORK_STATE_ERROR)){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        MBProgressHUD *hub = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
        [[JCIAPHelper sharedInstance] restorePurchasesWithCompleteHandle:^(IAPPurchType type, NSDictionary *dict) {
            //此处重新获取用户VIP信息，刷新界面
            dispatch_async(dispatch_get_main_queue(), ^{
                if(type == IAPPurchVerSuccess){
                    [hub hideAnimated:YES];
                    if(xy_isLogin && [m_userModel.userid isEqualToString:dict[@"userId"]]){
                        [[XYCenter sharedInstance] getNewUserModelInfo:^(__kindof YTKBaseRequest *request, id model) {
                            if(success){
                                success();
                            }
                            [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_VIP_OPERATE_SUCCESS object:nil];
                        } failure:^(NSString *msg, id model) {
                            [MBProgressHUD showToastWithMessageDarkColor:msg];
                        } isNeedRefreshLoginInfo:NO];
                    }
                }else if(type == IAPPurchCancle){
                    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01567", @"已取消")];
                }else{
                    [hub hideAnimated:YES];
                    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01568", @"恢复失败")];
                }
            });
        }];
        NSLog(@"VIP协议");
    }];
    [vipOperateAlert setRedeemCodeBlock:^{
        if((NETWORK_STATE_ERROR)){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        JCRedeemCodeViewController *vc = [[JCRedeemCodeViewController alloc] initWithDismissBlock:^{
        }];
        vc.success = success;
        vc.failure = failure;
        [weakVipOperateAlert hide];
        [self toVipAgmentWith:sourceVC targetVc:vc];
        NSLog(@"VIP协议");
    }];
    [vipOperateAlert setCoupinListBlock:^{
        if((NETWORK_STATE_ERROR)){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopChildPath:@"myCoupon" options:nil];
        vc.entrance_type_id = @"3";
        vc.jumpSource = @"y_page_user_vip_detail";
        [weakVipOperateAlert hide];
        [self toVipAgmentWith:sourceVC targetVc:vc];
        NSLog(@"VIP协议");
    }];
    [vipOperateAlert setCancelAutoRenewBlock:^{
      if((NETWORK_STATE_ERROR)){
          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
          return;
      }
      JCVIPAutoRenewViewController *vc = [[JCVIPAutoRenewViewController alloc] initWithDismissBlock:^{
      }];
      NSString *url = [NSString stringWithFormat:@"%@#/unsubscribe",ServerURL1];
      vc.title = XY_LANGUAGE_TITLE_NAMED(@"app100000150", @"如何取消订阅服务？");
      [vc loadUrl:url];
      [weakVipOperateAlert hide];
      [self toVipAgmentWith:sourceVC targetVc:vc];
    }];
    [vipOperateAlert setVipDetailInfoBlock:^{
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        NSString *url = [NSString stringWithFormat:@"%@/#/niim/user-order-search",NewH5BaseURL];
        [[JCLoginManager sharedInstance] checkLogin:^{
            [weakVipOperateAlert hide];
        } viewController:sourceVC loginSuccessBlock:^{
            XYWKWebViewController *vipInfoVC = [[XYWKWebViewController alloc] initWithUrl:url];
            vipInfoVC.title = XY_LANGUAGE_TITLE_NAMED(@"app100000443", @"订单明细");
            vipInfoVC.isSupportShare = NO;
            [weakVipOperateAlert hide];
            [self toVipAgmentWith:sourceVC targetVc:vipInfoVC];
        }];
    }];
    [vipOperateAlert showVIPOperateAlert];
    return vipOperateAlert;
}

+ (void)openOrRenewVIPWith:(NSString *)proId priceId:(NSString *)priceId operateType:(NSInteger)opertateType success:(void(^)(NSString *))success failure:(void((^)(NSString *msg, id model)))failure sourceInfo:(NSDictionary *)sourceInfo{
    if(STR_IS_NIL(proId) || (opertateType != 2 && STR_IS_NIL(priceId))) return;
    if(NETWORK_STATE_ERROR){
        failure(XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常"),nil);
        return;
    }
    if(app_TestFlight == 1){
        if([JCIAPHelper sharedInstance].isShowingUNIAP){
            return;
        }else{
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{

                [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示")
                               message:XY_LANGUAGE_TITLE_NAMED(@"app01541", @"抱歉，此测试版本无法开通或续费VIP")
                     cancelButtonTitle:@""
                       sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707",@"我知道了")
                           cancelBlock:^{

                }
                             sureBlock:^{
                    [JCIAPHelper sharedInstance].isShowingUNIAP = NO;
                }];
            });
            [JCIAPHelper sharedInstance].isShowingUNIAP = YES;
        }
        return;
    }

    NSString *requestPath = nil;
    NSMutableDictionary *productInfoDic = nil;
    if(opertateType == 2){
      requestPath = J_create_asset_vip_order;
      productInfoDic = @{@"code":priceId,@"channel":@"APPLE"}.mutableCopy;
      [[JCIAPHelper sharedInstance] setVipIAPSence:AssetVip];
      if(STR_IS_NIL(priceId)){
        MBProgressHUD *hub = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
      #ifdef DEBUG
        hub.label.text = proId;
      #endif
        [[JCIAPHelper sharedInstance] startSubscribeWithProductId:proId orderId:@"" completeHandle:^(IAPPurchType type, NSDictionary *dict) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [hub hideAnimated:NO];
                [[JCIAPHelper sharedInstance] clean];
                if(type == IAPPurchVerSuccess){
                  success(@"");
                }else if(type == IAPPurchCancle){
                  failure([NSString stringWithFormat:@"[E2]%@",XY_LANGUAGE_TITLE_NAMED(@"app01567", @"已取消购买")],nil);
              }else{
                NSString *errorMsg = [NSString stringWithFormat:@"[E4]%@",XY_LANGUAGE_TITLE_NAMED(@"app01566", @"购买失败")];
                failure(errorMsg,nil);
              }
              [[JCIAPHelper sharedInstance] setVipIAPSence:NormalVip];
            });
        }];
        return;
      }
    }else{
      requestPath = xy_isLogin?J_create_niimbot_vip_order:J_create_niimbot_vip_order_anonymous;
      productInfoDic = [self productInfoDicWithProId:proId].mutableCopy;
      if(productInfoDic != nil){
          [productInfoDic setValue:UN_NIL(priceId) forKey:@"vipPriceId"];
      }else{
          NSString *priceInfoFailedTip = [NSString stringWithFormat:@"[E7]%@",XY_LANGUAGE_TITLE_NAMED(@"app100001061",@"价格信息获取失败，请重启App后再试")];
          [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032", @"提示")
                         message:priceInfoFailedTip
               cancelButtonTitle:@""
                 sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707",@"我知道了") cancelBlock:^{
          } sureBlock:^{

          }];
          [[JCIAPHelper sharedInstance] clean];
          return;
      }
      [productInfoDic setValue:UN_NIL([JCKeychainTool getDeviceIDInKeychain]) forKey:@"deviceId"];
    }
  MBProgressHUD *hub = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
#ifdef DEBUG
  hub.label.text = proId;
#endif
    [productInfoDic xy_postWithModelType:nil Path:requestPath hud:nil tag:0 timeoutInterval:30 Success:^(__kindof YTKBaseRequest *request, NSDictionary *requestDic) {
        NSString *orderId = [NSString stringWithFormat:@"%@",requestDic[@"id"]];
        if(xy_isLogin){
            NSLog(@"非匿名购买信息 用户购买创建订单接口调用：%@，订单ID：%@",productInfoDic,orderId);
        }else{
            NSLog(@"匿名购买信息 游客购买创建订单接口调用：%@，订单ID：%@",productInfoDic,orderId);
        }//
        if(STR_IS_NIL(orderId)){
            failure([NSString stringWithFormat:@"[E6]%@",XY_LANGUAGE_TITLE_NAMED(@"app01565", @"创建订单失败")] ,nil);
            [hub hideAnimated:NO];
            [[JCIAPHelper sharedInstance] clean];
        }else{
            [JCKeychainTool save:KEY_IN_APP_LAST_ORDER_ID data:orderId];
            [[JCIAPHelper sharedInstance] startSubscribeWithProductId:proId orderId:orderId completeHandle:^(IAPPurchType type, NSDictionary *dict) {
                //此处重新获取用户VIP信息，刷新界面
              if([JCIAPHelper sharedInstance].vipSence == AssetVip){
                [hub hideAnimated:NO];
                if(type == IAPPurchVerSuccess){
                  success(orderId);
                }else if (type == IAPPurchCancle){
                  failure([NSString stringWithFormat:@"[E2]%@",XY_LANGUAGE_TITLE_NAMED(@"app01567", @"已取消购买")],nil);
                }else{
                  NSString *errorMsg = [NSString stringWithFormat:@"[E4]%@",XY_LANGUAGE_TITLE_NAMED(@"app01566", @"购买失败")];
                  failure(errorMsg,nil);
                }
                [[JCIAPHelper sharedInstance] clean];
              }else{
                NSString *comeOrderId = dict[@"orderId"];
                if(!(comeOrderId && [comeOrderId length])){
                    if(hub){
                        [hub hideAnimated:NO];
                    }
                    [[JCIAPHelper sharedInstance] clean];
                    return;
                }
                dispatch_async(dispatch_get_main_queue(), ^{
                    if(type == IAPPurchVerSuccess){
                        if(xy_isLogin && [m_userModel.userid isEqualToString:dict[@"userId"]]){
                            [[XYCenter sharedInstance] getNewUserModelInfo:^(__kindof YTKBaseRequest *request, id model) {
                                [hub hideAnimated:YES];
                                [JCIAPHelper pullPayResultBillStatus:proId gifts:sourceInfo[@"gifts"] orderId:orderId transactionId:dict[@"transactionId"] isSubcripe:sourceInfo[@"autoRenewal"]];
                                if(success){
                                    success(orderId);
                                }

                                [[XYCenter sharedInstance] getAppStoreIntroductorySupport:^{
                                    [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_VIP_OPERATE_SUCCESS object:nil];
                                }];
                            } failure:^(NSString *msg, id model) {
                                [hub hideAnimated:YES];
                                failure(msg,model);
                                [[JCIAPHelper sharedInstance] clean];
                            } isNeedRefreshLoginInfo:NO];
                        }
                    }else if(!xy_isLogin && dict[@"userId"] == nil && type == IAPPurchSuccess){
                        NSLog(@"当前设备已开通会员");
                        [hub hideAnimated:NO];
                        [JCIAPHelper pullPayResultBillStatus:proId gifts:sourceInfo[@"gifts"] orderId:orderId transactionId:dict[@"transactionId"] isSubcripe:sourceInfo[@"autoRenewal"]];

                        if(success){
                            success(orderId);
                        }
                        [[JCIAPHelper sharedInstance] clean];
                        [[XYCenter sharedInstance] getAppStoreIntroductorySupport:^{
                            [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_VIP_OPERATE_SUCCESS object:nil];
                        }];
                    }
                    else if(type == IAPPurchCancle){
                        [hub hideAnimated:NO];
                        [[JCIAPHelper sharedInstance] clean];
                        failure([NSString stringWithFormat:@"[E2]%@",XY_LANGUAGE_TITLE_NAMED(@"app01567", @"已取消购买")],nil);
                    }else{
                        [hub hideAnimated:YES];
                        if([dict[@"orderId"] isEqualToString:orderId]){
                            if([dict[@"hasAutoSubscribe"] isEqualToString:@"1"]){
                                NSString *hasAutoSubscribe = [NSString stringWithFormat:@"[E8]%@",XY_LANGUAGE_TITLE_NAMED(@"app100000392",@"当前Apple ID已购买过连续包月（或连续包年），此前购买的会员套餐到期后将自动切换，请登录此前购买会员的账号进行查看。")];
                                [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032", @"提示")
                                               message:hasAutoSubscribe
                                     cancelButtonTitle:@""
                                       sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707",@"我知道了") cancelBlock:^{
                                } sureBlock:^{

                                }];
                            }else{
                                NSString *errorMsg = [NSString stringWithFormat:@"[E4]%@",XY_LANGUAGE_TITLE_NAMED(@"app01566", @"购买失败")];
                                if(![[JCIAPHelper sharedInstance] isSupportCurrrentCountryWithSKU:proId]){
                                    errorMsg = XY_LANGUAGE_TITLE_NAMED(@"app100001689", @"暂不支持当前国家或地区购买此产品");
                                }
                                failure(errorMsg,nil);
                            }
                        }
                        [[JCIAPHelper sharedInstance] clean];
                    }
                });
              }
            }];
        }
    } failure:^(NSString *msg, id model) {
        [hub hideAnimated:NO];
        if(STR_IS_NIL(msg)){
            failure([NSString stringWithFormat:@"[E6]%@",XY_LANGUAGE_TITLE_NAMED(@"app01565", @"创建订单失败")] ,nil);
        }else{
            failure(msg,nil);
        }
    }];
}

+ (NSDictionary *)productInfoDicWithProId:(NSString *)productId{
    NSDictionary *productInfoDic = nil;
    NSArray *products = [XYCenter sharedInstance].storeProductArr;
    NSDictionary *pricesDic = [XYCenter sharedInstance].storePriceDic;
    for (SKProduct *product in products) {
        if([product.productIdentifier isEqualToString:productId]){
            NSString *price = [product price].stringValue;
            NSLocale *storeLocale = product.priceLocale;
            NSString *storeCountry = (NSString *)CFLocaleGetValue((CFLocaleRef)storeLocale,kCFLocaleCountryCode);
            NSString *currency = [product priceLocale].localeIdentifier;
            if(currency != nil){
                NSArray *arr = [currency componentsSeparatedByString:@"="];
                currency = [arr safeObjectAtIndex:1];
            }
            productInfoDic = @{@"price":UN_NIL(price),@"region":UN_NIL(storeCountry),@"currency":UN_NIL(currency),@"prices":pricesDic};
            break;
        }
    }
    return productInfoDic;
}

+ (void)openViewWithAlert:(UIViewController *)sourceVC needOpenTip:(BOOL)needOpenTip
           isUseVipSource:(BOOL)isUseVipSource
                  success:(void(^)(void))success
                  failure:(void((^)(NSString *msg, id model)))failure
               sourceInfo:(NSDictionary *)sourceInfo{
    NSNumber * isPrintHistory = [sourceInfo objectForKey:@"isPrintHistory"];
    if(app_TestFlight == 1){
        [[JCIAPHelper sharedInstance] setVipIAPSence:NormalVip];
        if([JCIAPHelper sharedInstance].isShowingUNIAP){
            return;
        }else{
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app01541", @"抱歉，此测试版本无法开通或续费VIP") cancelButtonTitle:@"" sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707",@"我知道了") cancelBlock:^{

                } sureBlock:^{
                    [JCIAPHelper sharedInstance].isShowingUNIAP = NO;
                }];
            });
            [JCIAPHelper sharedInstance].isShowingUNIAP = YES;
        }
        return;
    }
    if(xy_isLogin){
        BOOL isNeedRenew = m_userModel.vipInfo != nil && !m_userModel.vipInfo.valid;
        if(needOpenTip){
            if(![XYCenter sharedInstance].isDeviceVip){
                NSString *message = isNeedRenew?XY_LANGUAGE_TITLE_NAMED(@"app01519", @"您的VIP会员已过期，无法继续使用VIP会员专属资源。"):XY_LANGUAGE_TITLE_NAMED(@"app01520", @"您正在试用VIP会员专属资源，请先开 通VIP会员即可享受。");
                NSString *cancelButtonTitle = isNeedRenew?XY_LANGUAGE_TITLE_NAMED(@"app01512", @"立即续费"):XY_LANGUAGE_TITLE_NAMED(@"app01521", @"开通VIP");
                [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032", @"提示") message:message cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707", @"知道了") sureButtonTitle:cancelButtonTitle cancelBlock:nil sureBlock:^{
                    if(isNeedRenew){
                        [JCIAPHelper renewUserVIPWith:sourceVC showAllInfo:[JCIAPHelper sharedInstance].isShowAllInfo success:success failure:failure sourceInfo:sourceInfo];
                    }else{
                        [JCIAPHelper openUserVIPWith:sourceVC success:success failure:failure sourceInfo:sourceInfo];
                    }
                } alertType:3];
                return;
            }


            [JCIAPHelper bindVIPRight:sourceVC finish:^(BOOL isSuccess) {
                if (isSuccess){
                    // FIXME: 接受回调的地方太多 全部改动影响太大 目前只改动打印记录相关
                    if(success != nil && isPrintHistory != nil){
                        success();
                    }
                }
            }];

        }else{

            if([XYCenter sharedInstance].isDeviceVip){
                [JCIAPHelper bindVIPRight:sourceVC finish:^(BOOL isSuccess) {
                    if (isSuccess){
                        // FIXME: 接受回调的地方太多 全部改动影响太大 目前只改动打印记录相关
                        if(success != nil && isPrintHistory != nil){
                            success();
                        }
                    }
                }];
            }else{
                JCVIPOperateAlert *vipAlert = nil;
                if(isNeedRenew){
                    vipAlert = [JCIAPHelper renewUserVIPWith:sourceVC showAllInfo:[JCIAPHelper sharedInstance].isShowAllInfo success:success failure:failure sourceInfo:sourceInfo];
                }else{
                    vipAlert = [JCIAPHelper openUserVIPWith:sourceVC success:success failure:failure sourceInfo:sourceInfo];
                }
            }
        }
    }else{



        if([XYCenter sharedInstance].isDeviceVip){
            [JCIAPHelper bindVIPRight:sourceVC finish:^(BOOL isSuccess) {
                if(isSuccess){
                    // FIXME: 接受回调的地方太多 全部改动影响太大 目前只改动打印记录相关
                    if(success != nil && isPrintHistory != nil){
                        success();
                    }
                }
            }];
        }else{
            JCVIPOperateAlert *vipAlert = nil;
            vipAlert = [JCIAPHelper openUserVIPWith:sourceVC success:success failure:failure sourceInfo:sourceInfo];
            if([XYCenter sharedInstance].isDeviceVip){
                [JCIAPHelper bindVIPRight:sourceVC vipAlert:vipAlert];
            }
        }
    }
}

/**
 *续费入口*
 *
 */
+ (JCVIPOperateAlert *)renewUserVIPWith:(UIViewController *)sourceVC showAllInfo:(BOOL)isShowAllInfo success:(void(^)(void))success failure:(void((^)(NSString *msg, id model)))failure sourceInfo:(NSDictionary *)sourceInfo{
    NSString *sourcePage = [sourceInfo objectForKey:@"sourcePage"];
    NSString *actName = [sourceInfo objectForKey:@"act_name"];
    NSString *anchorSubscribeId = [sourceInfo objectForKey:@"anchorSubscribeId"];
    JCVIPOperateAlert *vipOperateAlert = [[JCVIPOperateAlert alloc] initWithFrame:CGRectZero isNeedRenewVIP:YES showAllInfo:isShowAllInfo animation:YES];
    __weak typeof(vipOperateAlert) weakVipOperateAlert = vipOperateAlert;
    vipOperateAlert.sourcePage = sourcePage;
    vipOperateAlert.eventTitle = actName;
    vipOperateAlert.anchorSubscribeId = anchorSubscribeId;
    [vipOperateAlert setOperateVIPBlock:^(NSString *priceId,NSString *proId, NSString* isSubcripe) {
        if(xy_isLogin && STR_IS_NIL(m_userModel.phone) && STR_IS_NIL(m_userModel.email)){
            [[JCLoginManager sharedInstance] checkBindWithviewController:sourceVC withBindType:1 withResponse:^{
                [weakVipOperateAlert hide];
            } withComplete:^(id x) {


            }];
            return;
        }
        //vipInfoModel
        NSMutableDictionary *newSourceInfo = @{}.mutableCopy;
        [newSourceInfo addEntriesFromDictionary:sourceInfo];
        [newSourceInfo setObject:isSubcripe forKey:@"autoRenewal"];
        if(!([[newSourceInfo allKeys] containsObject:@"gifts"] && IsNotEmptyDictionary(newSourceInfo[@"gifts"]) && [[XYCenter sharedInstance] isShowVipBenefits])){
            if(vipOperateAlert.vipInfoModel && IsNotEmptyDictionary(vipOperateAlert.vipInfoModel.gifts)){
                [newSourceInfo setObject:weakVipOperateAlert.vipInfoModel.gifts forKey:@"gifts"];
            }
        }

        [self openOrRenewVIPWith:proId priceId:priceId operateType:1 success:^(NSString *orderId){
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                if(success){
                    success();
                }
                [weakVipOperateAlert hide];
            });
        } failure:^(NSString * _Nonnull msg, id  _Nonnull model) {
            [MBProgressHUD showToastWithMessageDarkColor:msg];
        }  sourceInfo:newSourceInfo];
    }];
    VipSence currentSence = [JCIAPHelper sharedInstance].vipSence;
    [vipOperateAlert setVipAgreeBlock:^{
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        JCUserAgmentController *vc = [[JCUserAgmentController alloc] initWithDismissBlock:^{
            if(![[NBCAPMiniAppManager sharedInstance] hasUniAppActivity] || sourceVC.navigationController == nil){
                [[JCIAPHelper sharedInstance] setVipIAPSence:currentSence];
                [JCIAPHelper renewUserVIPWith:sourceVC showAllInfo:isShowAllInfo success:success failure:failure sourceInfo:sourceInfo];
            }
        }];
        vc.type = 3;
        vc.sence = [JCIAPHelper sharedInstance].vipSence;
        [weakVipOperateAlert hide];
        [self toVipAgmentWith:sourceVC targetVc:vc];
        NSLog(@"VIP协议");
    }];
    [vipOperateAlert setVipEventBlock:^(NSString *link,NSString *title,NSString *typeCode) {
        if(STR_IS_NIL(link)){
            return;
        }
        if(![[NBCAPMiniAppManager sharedInstance] hasUniAppActivity] || sourceVC.navigationController == nil) [weakVipOperateAlert hide];
        [JCToNativeRouteHelp bannberJumpWith:typeCode routeString:link title:title sourceVC:sourceVC];
    }];
    [vipOperateAlert setVipRenewAgreeBlock:^{
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        JCUserAgmentController *vc = [[JCUserAgmentController alloc] initWithDismissBlock:^{
            if(![[NBCAPMiniAppManager sharedInstance] hasUniAppActivity] || sourceVC.navigationController == nil){
                [[JCIAPHelper sharedInstance] setVipIAPSence:currentSence];
                [JCIAPHelper renewUserVIPWith:sourceVC showAllInfo:isShowAllInfo success:success failure:failure sourceInfo:sourceInfo];
            }
        }];
        vc.type = 4;
        vc.sence = [JCIAPHelper sharedInstance].vipSence;
        [weakVipOperateAlert hide];
        [self toVipAgmentWith:sourceVC targetVc:vc];
        NSLog(@"VIP协议");
    }];
    [vipOperateAlert setVipRenewDescrBlock:^{
        NSDictionary *autoPriceDic = [XYCenter sharedInstance].autoNewPriceDic;
        if((NETWORK_STATE_ERROR) || autoPriceDic.count == 0){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        JCUserAgmentController *vc = [[JCUserAgmentController alloc] initWithDismissBlock:^{
            if(![[NBCAPMiniAppManager sharedInstance] hasUniAppActivity] || sourceVC.navigationController == nil){
                [[JCIAPHelper sharedInstance] setVipIAPSence:currentSence];
                [JCIAPHelper renewUserVIPWith:sourceVC showAllInfo:isShowAllInfo success:success failure:failure sourceInfo:sourceInfo];
            }
        } parmDic:autoPriceDic];
        vc.type = 5;
        vc.sence = [JCIAPHelper sharedInstance].vipSence;
        [weakVipOperateAlert hide];
        [self toVipAgmentWith:sourceVC targetVc:vc];
        NSLog(@"VIP协议");
    }];
    [vipOperateAlert setRestoreBlock:^{
        if((NETWORK_STATE_ERROR)){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        MBProgressHUD *hub = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
        [[JCIAPHelper sharedInstance] restorePurchasesWithCompleteHandle:^(IAPPurchType type, NSDictionary *dict) {
            //此处重新获取用户VIP信息，刷新界面
            dispatch_async(dispatch_get_main_queue(), ^{
                if(type == IAPPurchVerSuccess){
                    [hub hideAnimated:YES];
                    if(xy_isLogin && [m_userModel.userid isEqualToString:dict[@"userId"]]){
                        [[XYCenter sharedInstance] getNewUserModelInfo:^(__kindof YTKBaseRequest *request, id model) {
                            if(success){
                                success();
                            }
                            [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_VIP_OPERATE_SUCCESS object:nil];
                        } failure:^(NSString *msg, id model) {
                            [MBProgressHUD showToastWithMessageDarkColor:msg];
                        } isNeedRefreshLoginInfo:NO];
                    }
                }else if(!xy_isLogin && dict[@"userId"] == nil && type == IAPPurchSuccess){
                    NSLog(@"当前设备已开通会员");
                    [hub hideAnimated:NO];
                }else if(type == IAPPurchCancle){
                    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01567", @"已取消")];
                }else{
                    [hub hideAnimated:YES];
                    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01568", @"恢复失败")];
                }
            });
        }];
        NSLog(@"VIP协议");
    }];
    [vipOperateAlert setRedeemCodeBlock:^{
        if((NETWORK_STATE_ERROR)){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        JCRedeemCodeViewController *vc = [[JCRedeemCodeViewController alloc] initWithDismissBlock:^{
        }];
        vc.success = success;
        vc.failure = failure;
        [weakVipOperateAlert hide];
        [self toVipAgmentWith:sourceVC targetVc:vc];
        NSLog(@"VIP协议");
    }];
    [vipOperateAlert setCoupinListBlock:^{
        if((NETWORK_STATE_ERROR)){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopChildPath:@"myCoupon" options:nil];
        vc.entrance_type_id = @"3";
        vc.jumpSource = @"y_page_user_vip_detail";
        [weakVipOperateAlert hide];
        [self toVipAgmentWith:sourceVC targetVc:vc];
        NSLog(@"VIP协议");
    }];
    [vipOperateAlert setCancelAutoRenewBlock:^{
        if((NETWORK_STATE_ERROR)){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        JCVIPAutoRenewViewController *vc = [[JCVIPAutoRenewViewController alloc] initWithDismissBlock:^{
            if(![[NBCAPMiniAppManager sharedInstance] hasUniAppActivity] || sourceVC.navigationController == nil)  [JCIAPHelper renewUserVIPWith:sourceVC showAllInfo:isShowAllInfo success:success failure:failure sourceInfo:sourceInfo];
        }];
        NSString *url = [NSString stringWithFormat:@"%@#/unsubscribe",ServerURL1];
        vc.title = XY_LANGUAGE_TITLE_NAMED(@"app100000150", @"如何取消订阅服务？");
        [vc loadUrl:url];
        [weakVipOperateAlert hide];
        [self toVipAgmentWith:sourceVC targetVc:vc];
        NSLog(@"VIP协议");
    }];
    [vipOperateAlert setVipDetailInfoBlock:^{
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        NSString *url = [NSString stringWithFormat:@"%@/#/niim/user-order-search",NewH5BaseURL];
        [[JCLoginManager sharedInstance] checkLogin:^{
            [weakVipOperateAlert hide];
        } viewController:sourceVC loginSuccessBlock:^{
            XYWKWebViewController *vipInfoVC = [[XYWKWebViewController alloc] initWithUrl:url];
            vipInfoVC.title = XY_LANGUAGE_TITLE_NAMED(@"app100000443", @"订单明细");
            vipInfoVC.isSupportShare = NO;
            [weakVipOperateAlert hide];
            [self toVipAgmentWith:sourceVC targetVc:vipInfoVC];
        }];
    }];
    [vipOperateAlert showVIPOperateAlert];
    return vipOperateAlert;
}

- (void)saveDeviceIAPInfo:(NSDictionary *)currentIAPInfoDic{
    NSString *receiptData = currentIAPInfoDic[@"receiptData"];
    NSDictionary *currentIAPOrder = currentIAPInfoDic[@"orderInfo"];
    NSDictionary *keyChainInfo = [JCKeychainTool getIn_AppInfo_Device];
    NSMutableArray *orderInfos = [NSMutableArray array];
    NSMutableDictionary *deviceVipInfo = [NSMutableDictionary dictionary];
    if([keyChainInfo isKindOfClass:[NSDictionary class]]){
        deviceVipInfo = [NSMutableDictionary dictionaryWithDictionary:keyChainInfo];
    }
    [orderInfos addObject:currentIAPOrder];
    [deviceVipInfo setObject:UN_NIL(receiptData) forKey:@"receiptData"];
    [deviceVipInfo setObject:orderInfos forKey:@"orderInfos"];
    NSLog(@"当前设备中未同步订单：%@",deviceVipInfo.xy_toJsonString);
    //同步当前内购信息至服务器
    NSMutableArray *reportInfos = [JCKeychainTool getIn_AppInfo_Device_Will_Report].mutableArray;
    if(reportInfos == nil || ![reportInfos isKindOfClass:[NSArray class]]){
        reportInfos = [NSMutableArray array];
    }

    //这里应该是批量上报才对
    NSMutableDictionary *willReportIAPInfo = [NSMutableDictionary dictionaryWithDictionary:currentIAPOrder];
    [willReportIAPInfo setObject:receiptData forKey:@"receiptData"];
    [willReportIAPInfo setObject:UN_NIL([JCKeychainTool getDeviceIDInKeychain]) forKey:@"deviceId"];
    [willReportIAPInfo setObject:UN_NIL(_orderId) forKey:@"orderId"];
    [reportInfos addObject:willReportIAPInfo];
    [JCKeychainTool save:KEY_IN_APP_INSTEAD_DEVICE_WILL_REPORT data:reportInfos];
    [JCIAPHelper syncAnonymousIAPInfoToServer];
    [willReportIAPInfo setObject:@"1" forKey:@"needDesRequest"];
    NSLog(@"匿名购买信息 匿名购买信息上报接口调用：%@",willReportIAPInfo.xy_toJsonString);
    //验证此appleID 是否有连续订阅
    [willReportIAPInfo xy_postWithModelType:nil Path:J_vip_device_auto_valid hud:nil tag:0 timeoutInterval:30 Success:^(__kindof YTKBaseRequest *request, NSDictionary *requestDic) {
        if(requestDic.allKeys.count > 0){
            BOOL success = ((NSNumber *)requestDic[@"success"]).boolValue;
            NSString *requestCode = requestDic[@"customBizCode"];
            if(success){

                [self handleActionWithType:IAPPurchSuccess data:willReportIAPInfo];
                [JCKeychainTool save:KEY_IN_APP_INSTEAD_DEVICE data:deviceVipInfo];
                if ([JCAppEventChannel shareInstance].eventSink) {
                    [JCAppEventChannel shareInstance].eventSink(@{@"isDeviceVip":@(true)});
                }
            }else if(requestCode.integerValue == 40005){
                [willReportIAPInfo setObject:@"1" forKey:@"hasAutoSubscribe"];
                [willReportIAPInfo setObject:UN_NIL(self->_orderId) forKey:@"orderId"];
                [self handleActionWithType:IAPPurchFailed data:willReportIAPInfo];
            }else{
                [self handleActionWithType:IAPPurchFailed data:willReportIAPInfo];
            }
        }
    } failure:^(NSString *msg, id model) {
        [self handleActionWithType:IAPPurchFailed data:willReportIAPInfo];
    }];
}

+ (void)pullPayResultBillStatus:(NSString *)sku
                          gifts:(NSDictionary *)gifts
                        orderId:(NSString *)orderId
                  transactionId:(NSString *)transactionId
                     isSubcripe:(NSString *)isSubcripe{

    BOOL isSubcripteAction = NO;
    if(isSubcripe && [isSubcripe isKindOfClass:[NSString class]] && [isSubcripe boolValue]){
        isSubcripteAction = YES;
    }

    if(!(xy_isLogin)){
        if([[XYCenter sharedInstance] isShowVipBenefits] && IsNotEmptyDictionary(gifts)){
            [JCIAPHelper showPaySuccessAlertWith:sku gifts:gifts];
        }else{
            if(isSubcripteAction){
                //订阅
                [JCIAPHelper showPaySuccessAlertWith:sku gifts:@{}];
            }else{
                //非订阅
                [JCIAPHelper showPaySuccessAlertWith:sku gifts:@{}];
            }
        }

        [[JCIAPHelper sharedInstance] clean];
        return;
    }

    if([[XYCenter sharedInstance] isShowVipBenefits] && IsNotEmptyDictionary(gifts)){
        JCVIPPayResultViewController *resultViewController = [[JCVIPPayResultViewController alloc] initWithNibName:@"JCVIPPayResultViewController" bundle:nil];
        resultViewController.orderId = orderId;
        resultViewController.transactionId = transactionId;
        [resultViewController setFinishBlock:^(enum JCVIPPayResultBillStatus status, NSString * _Nonnull msg) {
            if(status == JCVIPPayResultBillSuccess){
                if([[XYCenter sharedInstance] isShowVipBenefits] && IsNotEmptyDictionary(gifts)){
                    [JCIAPHelper showPaySuccessAlertWith:sku gifts:gifts];
                }else{
                    if(xy_isLogin){
                        [JCIAPHelper showPaySuccessAlertWith:sku gifts:@{}];
                    }else{
                        [JCIAPHelper showPaySuccessAlertWith:sku gifts:@{}];
                    }

                }
            }else  if(status == JCVIPPayResultBillChange){//这里还需要跟产品和后台确认一下具体的显示状态
                [JCIAPHelper showPaySuccessAlertWith:sku gifts:@{}];
            }else  if(status == JCVIPPayResultBillFailed || status == JCVIPPayResultBillUnknown){
                JC_TrackWithparms(@"show",@"029_285",(@{}));
                [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app100000699",@"开通失败")
                               message:XY_LANGUAGE_TITLE_NAMED(@"app100000700",@"您可以尝试重新购买")
                     cancelButtonTitle:nil
                       sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707",@"我知道了") cancelBlock:^{
                } sureBlock:^{

                }];
#ifdef DEBUG
                [MBProgressHUD showToastWithMessageDarkColor:msg];
#endif
            }
        }];
        [[XYTool getCurrentVC].navigationController pushViewController:resultViewController animated:YES];
    }else{
        if(isSubcripteAction){
            //订阅
            [JCIAPHelper showPaySuccessAlertWith:sku gifts:@{}];
        }else{
            //非订阅
          VipSence vipSence = [JCIAPHelper sharedInstance].vipSence;
          if(vipSence != AssetVip){
            [JCIAPHelper showPaySuccessAlertWith:sku gifts:@{}];
          }
        }
    }
    [[JCIAPHelper sharedInstance] clean];

}

+ (void)showPaySuccessAlertWith:(NSString *)sku gifts:(NSDictionary *)gifts{
    //    [XYCenter sharedInstance].userModel
    NSArray *benefits = nil;

    if(sku && IsNotEmptyDictionary(gifts)){
        benefits = gifts[sku];
    }
    //支付成功弹窗处理
    dispatch_async(dispatch_get_main_queue(), ^{
        VipSence vipSence = [self getVipPaySuccessSenceWithSKU:sku];
        JCVipSuccessTipView *paySuccessVC = [[JCVipSuccessTipView alloc] initWithSence:vipSence withSku:sku];
        [paySuccessVC show];
    });

}

//根据SKU 获取支付场景
+ (VipSence)getVipPaySuccessSenceWithSKU:(NSString *)sku{
    if([[JCIAPHelper sharedInstance].cableVipProductIds containsObject:sku]){
        return CableVip;
    }else{
        return NormalVip;
    }
}

+ (void)bindVIPRight:(UIViewController *)sourceVC vipAlert:(JCVIPOperateAlert *)vipAlert{
    XYWeakSelf
    BOOL isH5Activity = [sourceVC isKindOfClass:[NSClassFromString(@"XYWKWebViewController") class]];
    if(!xy_isLogin){
        NSString *loginDesc = XY_LANGUAGE_TITLE_NAMED(@"app100000364",@"您正在使用VIP资源，请登录账号进行会员绑定。");
        if(isH5Activity){
            [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app100000363",@"请绑定会员账号")
                           message:loginDesc
                 cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消")
                   sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01191",@"立即登录") cancelBlock:^{

            } sureBlock:^{
                [vipAlert hide];
                [[JCLoginManager sharedInstance] presentLoginViewController:^{

                } viewController:sourceVC loginSuccessBlock:^{
                    [weakSelf moveVIPRightRequest:sourceVC complate:^{

                    }];
                }];
            }];
        }else{
            [[JCLoginManager sharedInstance] checkLogin:^{

            } viewController:sourceVC loginSuccessBlock:^{
                [weakSelf moveVIPRightRequest:sourceVC complate:^{

                }];
            }];
        }
        return;
    }
    NSString *bindDesc = XY_LANGUAGE_TITLE_NAMED(@"app100000365",@"您正在使用VIP资源，确定将会员绑定到当前账号？");
    if(isH5Activity) bindDesc = XY_LANGUAGE_TITLE_NAMED(@"app100001504",@"为了更好得为您服务，确定将会员绑定到当前账号？");
    [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app100000363",@"请绑定会员账号")
                   message:bindDesc
         cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消")
           sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000366",@"立即绑定") cancelBlock:^{
    } sureBlock:^{
        [self moveVIPRightRequest:sourceVC vipAlert:vipAlert];
    }];
}

+ (void)bindVIPRight:(UIViewController *)sourceVC finish:(void(^)(BOOL isSuccess))finish{
    XYWeakSelf
    BOOL isH5Activity = [sourceVC isKindOfClass:[NSClassFromString(@"XYWKWebViewController") class]];
    if(!xy_isLogin){
        NSString *loginDesc = XY_LANGUAGE_TITLE_NAMED(@"app100000364",@"您正在使用VIP资源，请登录账号进行会员绑定。");
        if(isH5Activity)loginDesc = XY_LANGUAGE_TITLE_NAMED(@"app100000364",@"您正在使用VIP资源，请登录账号进行会员绑定。");
        [[JCLoginManager sharedInstance] checkLogin:^{

        } viewController:sourceVC loginSuccessBlock:^{
            [weakSelf moveVIPRightRequest:sourceVC complate:^{
                finish(true);
            }];
        }];
        return;
    }
    NSString *bindDesc = XY_LANGUAGE_TITLE_NAMED(@"app100000365",@"您正在使用VIP资源，确定将会员绑定到当前账号？");
    if(isH5Activity) bindDesc = XY_LANGUAGE_TITLE_NAMED(@"app100001504",@"为了更好得为您服务，确定将会员绑定到当前账号？");
    [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app100000363",@"请绑定会员账号")
                   message:bindDesc
         cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消")
           sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000366",@"立即绑定") cancelBlock:^{
        finish(false);
    } sureBlock:^{
        [self moveVIPRightRequest:sourceVC complate:^{
            finish(true);
        }];
    }];
}


+ (void)moveVIPRight:(UIViewController *)sourceVC{
    if(!xy_isLogin){
        [[JCLoginManager sharedInstance] checkLogin:^{

        } viewController:sourceVC loginSuccessBlock:^{
            [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app100000376",@"权益绑定")
                           message:XY_LANGUAGE_TITLE_NAMED(@"app100000368",@"会员权益需绑定账号使用，是否确认绑定会员权益到当前账号？")
                 cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消")
                   sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000366",@"立即绑定") cancelBlock:^{
            } sureBlock:^{
                [self moveVIPRightRequest:sourceVC complate:^{

                }];
            }];
        }];
        return;
    }
    [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app100000376",@"权益绑定")
                   message:XY_LANGUAGE_TITLE_NAMED(@"app100000368",@"会员权益需绑定账号使用，是否确认绑定会员权益到当前账号？")
         cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消")
           sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000366",@"立即绑定") cancelBlock:^{
    } sureBlock:^{
        [self moveVIPRightRequest:sourceVC complate:^{

        }];
    }];
}

+ (void)moveVIPRightRequest:(UIViewController *)sourceVC vipAlert:(JCVIPOperateAlert *)vipAlert{
    if(xy_isLogin && STR_IS_NIL(m_userModel.phone) && STR_IS_NIL(m_userModel.email)){
        [[JCLoginManager sharedInstance] checkBindWithviewController:sourceVC withBindType:1 withResponse:^{
            [vipAlert hide];
        } withComplete:^(id x) {
            [self moveVIPRightRequest:sourceVC complate:^{
                [JCIAPHelper moveVIPRightRequest:sourceVC complate:^{

                }];
            }];
        }];
        return;
    }
    [self moveVIPRightRequest:sourceVC complate:^{
        if(vipAlert != nil){
            [vipAlert hide];
        }
    }];
}

+ (void)syncAnonymousIAPInfoToServer{//
    id data = [JCKeychainTool getIn_AppInfo_Device_Will_Report];
    if([data isKindOfClass:[NSArray class]]){
        NSArray *reportInfos = data;
        for (NSDictionary *reportInfo in reportInfos) {
            NSMutableDictionary *iapInfo = [NSMutableDictionary dictionaryWithDictionary:reportInfo];
            [iapInfo setObject:UN_NIL([JCKeychainTool getDeviceIDInKeychain]) forKey:@"deviceId"];
            if([[JCKeychainTool getDeviceIDInKeychain] isEqualToString:@"433831C3-613B-4517-A7F1-71651C03C538"]){
              continue;
            }
            [iapInfo setObject:@"1" forKey:@"needDesRequest"];
            NSLog(@"匿名购买信息 匿名购买信息上报接口调用：%@",iapInfo.xy_toJsonString);
            [iapInfo xy_postWithModelType:nil Path:J_vip_device_report hud:nil tag:0 timeoutInterval:30 Success:^(__kindof YTKBaseRequest *request, NSDictionary *requestDic) {
                [JCKeychainTool save:KEY_IN_APP_INSTEAD_DEVICE_WILL_REPORT data:nil];
            } failure:^(NSString *msg, id model) {

            }];
        }
    }
}

+ (void)moveVIPRightRequest:(UIViewController *)sourceVC complate:(XYNormalBlock)moveSuccess{
    if(xy_isLogin && STR_IS_NIL(m_userModel.phone) && STR_IS_NIL(m_userModel.email)){
        [[JCLoginManager sharedInstance] checkBindWithviewController:sourceVC withBindType:1 withResponse:^(){} withComplete:^(id x) {
            [self moveVIPRightRequest:sourceVC complate:^{
                moveSuccess();
            }];
        }];
        return;
    }
    NSDictionary *deviceVipInfo = [JCKeychainTool getIn_AppInfo_Device];
    if(deviceVipInfo == nil || ![deviceVipInfo isKindOfClass:[NSDictionary class]]){
        deviceVipInfo = @{};
    }
    NSMutableDictionary *bindInfo = [NSMutableDictionary dictionaryWithDictionary:deviceVipInfo];
    [bindInfo setObject:UN_NIL(m_userModel.userId) forKey:@"userId"];
    [bindInfo setObject:@"1" forKey:@"needDesRequest"];
    NSString *bindInfoStr = bindInfo.xy_toJsonString;
    NSLog(@"匿名购买信息 权益迁移接口调用：%@",bindInfoStr);
    [bindInfo xy_postWithModelType:nil Path:J_vip_right_move hud:XY_LANGUAGE_TITLE_NAMED(@"app100000370", @"权益绑定中...") tag:0 timeoutInterval:30
                             Success:^(__kindof YTKBaseRequest *request, NSDictionary *requestDic) {

        [[XYCenter sharedInstance] getNewUserModelInfo:^(__kindof YTKBaseRequest *request, id model) {
            if(requestDic.allKeys.count > 0){
                NSString *detail = requestDic[@"textId"];
                BOOL success = ((NSNumber *)requestDic[@"success"]).boolValue;
                NSString *title = success?XY_LANGUAGE_TITLE_NAMED(@"app100000371",@"权益绑定成功"):XY_LANGUAGE_TITLE_NAMED(@"app100000391",@"迁移权益失败");
                NSString *requestCode = requestDic[@"customBizCode"];
                switch (requestCode.integerValue) {
                    case 40001:
                    case 40002:
                    case 40003:
                    {
                        [JCKeychainTool save:KEY_IN_APP_INSTEAD_DEVICE data:nil];
                        if ([JCAppEventChannel shareInstance].eventSink) {
                            [JCAppEventChannel shareInstance].eventSink(@{@"isDeviceVip":@(false)});
                        }
                        break;
                    }
                    case 40004:
                        break;
                    default:
                        if(success){
                            [JCKeychainTool save:KEY_IN_APP_INSTEAD_DEVICE data:nil];
                            if ([JCAppEventChannel shareInstance].eventSink) {
                                [JCAppEventChannel shareInstance].eventSink(@{@"isDeviceVip":@(false)});
                            }
                        };
                        break;
                }
                [JCAlert showAlertView:title
                               message:XY_LANGUAGE_TITLE_NAMED(UN_NIL(detail),@"")
                     cancelButtonTitle:@""
                       sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707",@"我知道了") cancelBlock:^{
                } sureBlock:^{
                    if(success){
                        moveSuccess();
                    }
                }];
            }
            [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_VIP_OPERATE_SUCCESS object:nil];
        } failure:^(NSString *msg, id model) {

        } isNeedRefreshLoginInfo:NO];
    } failure:^(NSString *msg, NSString *requestCode) {
        [MBProgressHUD showToastWithMessageDarkColor:msg];
    }];
}

+ (void)acticityVIPWith:(NSString *)priceId productId:(NSString *)proId success:(void(^)(void))success failure:(void((^)(NSString *msg, id model)))failure closeAlertBlock:(XYNormalBlock)closeBlock sourceInfo:(NSDictionary *)sourceInfo sourceVC:(UIViewController *)sourceVC{
    XYWeakSelf
    if(xy_isLogin){
      [self openOrRenewVIPWith:proId priceId:priceId operateType:1 success:^(NSString *orderId){
        success();
      } failure:failure sourceInfo:sourceInfo];
    }else{
      JC_TrackWithparms(@"show",@"029_159",(@{}));
      VipSence vipSence = [JCIAPHelper sharedInstance].vipSence;
      if([XYCenter sharedInstance].isSupportAnonymityBuyVip){
        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01521",@"开通VIP")
                       message:XY_LANGUAGE_TITLE_NAMED(@"app100000360",@"登录精臣云打印账号购买，可跨平台享受会员权益及商城优惠券等超值福利")
             cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消")
              sureButtonTitle1:XY_LANGUAGE_TITLE_NAMED(@"app100000361",@"登录账号购买(推荐)")
               sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000362",@"游客身份购买") cancelBlock:^{
          JC_TrackWithparms(@"click",@"029_159_154",(@{}));
        } sureBlock1:^{
          closeBlock();
          JC_TrackWithparms(@"click",@"029_159_152",(@{}));
          [[JCLoginManager sharedInstance] presentLoginViewController:^{

          } viewController:sourceVC loginSuccessBlock:^{
            if(!m_user_vip){
              [[JCIAPHelper sharedInstance] setVipIAPSence:vipSence];
              [weakSelf openViewWithAlert:sourceVC needOpenTip:NO isUseVipSource:NO success:^{

              } failure:^(NSString *msg, id model) {

              } sourceInfo:sourceInfo];
            } else {
              // 登录的VIP账户，收起弹窗, 约处理为购买成功，对于Flutter端的VIP刷新有效
              success();
            }
          }];
        } sureBlock:^{
          JC_TrackWithparms(@"click",@"029_159_153",(@{}));
          if(jc_current_isDeviceVip){
            [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示")
                           message:XY_LANGUAGE_TITLE_NAMED(@"app100000378",@"当前设备已匿名购买过vip，请登录后重试")
                 cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消")
                   sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01191",@"立即登录") cancelBlock:^{
              if(success){
                success();
              }
            } sureBlock:^{
              if(success){
                success();
              }
              [[JCLoginManager sharedInstance] presentLoginViewController:^{

              } viewController:sourceVC loginSuccessBlock:^{

              }];
            }];

            return;
          }
          [weakSelf openOrRenewVIPWith:proId priceId:priceId operateType:1 success:^(NSString *orderId){
            if(!xy_isLogin){
              if(![[XYCenter sharedInstance] isChinaMainland]){
                //                        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app100000373",@"会员开通成功")
                //                                       message:XY_LANGUAGE_TITLE_NAMED(@"app100000374",@"登录账号可以在多设备访问购买的内容，更可享优惠券等超值福利")
                //                             cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消")
                //                               sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01191",@"立即登录") cancelBlock:^{
                //                            if(success){
                //                                success();
                //                            }
                //                        } sureBlock:^{
                //                            if(success){
                //                                success();
                //                            }
                //                            [[JCLoginManager sharedInstance] presentLoginViewController:^{
                //
                //                            } viewController:sourceVC loginSuccessBlock:^{
                //                                [JCIAPHelper moveVIPRightRequest:sourceVC complate:^{
                //
                //                                }];
                //                            }];
                //                        }];
                if(success){
                  success();
                }
              } else {
                // 中国大陆的商品，抛出success回调
                if (success) {
                  success();
                }
              }

            }

          } failure:failure sourceInfo:sourceInfo];
        } alertType:9];
      }else{
        JC_TrackWithparms(@"click",@"029_159_152",(@{}));
        [[JCLoginManager sharedInstance] checkLogin:^{
        } viewController:sourceVC sureBlock:^{
          closeBlock();
        } loginSuccessBlock:^{
          if(!m_user_vip){
            [[JCIAPHelper sharedInstance] setVipIAPSence:vipSence];
            [weakSelf openViewWithAlert:sourceVC needOpenTip:NO isUseVipSource:NO success:^{

            } failure:^(NSString *msg, id model) {

            } sourceInfo:sourceInfo];
          } else {
            // 登录的VIP账户，收起弹窗, 约处理为购买成功，对于Flutter端的VIP刷新有效
            success();
          }
        }];
      }
    }

}
@end
