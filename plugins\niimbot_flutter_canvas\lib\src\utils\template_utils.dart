import 'dart:convert';
import 'dart:io';
import 'dart:isolate';

import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_canvas_plugins_interface/user_center/canvas_user_center.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:niimbot_excel/models/data_bind_modify.dart';
import 'package:niimbot_excel/models/data_source.dart';
import 'package:niimbot_excel/models/excel_data.dart';
import 'package:niimbot_excel/models/excel_sheet_data.dart';
import 'package:niimbot_excel/models/interface.dart';
import 'package:niimbot_excel/models/range.dart';
import 'package:niimbot_excel/models/template_excel_data.dart';
import 'package:niimbot_excel/models/template_task.dart';
import 'package:niimbot_excel/modify_utils.dart';
import 'package:niimbot_excel/niimbot_data_source_utils.dart';
import 'package:niimbot_excel/niimbot_excel_utils.dart';
import 'package:niimbot_flutter_canvas/src/localization/localization_public.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/data_source_wrapper.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/dynamic_source_data.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/dynamic_source_data_manager.dart';
import "package:niimbot_flutter_canvas/src/model/excel/excel_covert_manager.dart";
import 'package:niimbot_flutter_canvas/src/model/excel/excel_transform_manager.dart';
import 'package:niimbot_flutter_canvas/src/model/stack/stack_manager.dart';
import 'package:niimbot_flutter_canvas/src/model/stack/template_task.dart' as tt;
import 'package:niimbot_flutter_canvas/src/widgets/components/custom_dialog.dart';
import 'package:niimbot_flutter_canvas/src/widgets/good_lib/good_field_manager.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

import '/src/model/advanceQRCode/advance_qr_code_manager.dart';
import '/src/model/element/json_element.dart';
import '/src/model/excel/excel_manager.dart';
import '/src/model/template_data.dart';
import '/src/model/template_external_data.dart';
import '../model/excel_bind_info.dart';
import '../widgets/box_frame/canvas_box_frame.dart';

Logger _logger = Logger("CanvasPluginManager", on: kDebugMode);

class TemplateUtils {
  static String documentPath = "";

  static Map<String, dynamic> decodeJson(String data) {
    return jsonDecode(data);
  }

  ///把旧的模版数据字符串 转换为 新的excel导入的模版数据结构的字符串表示
  static Future<String> transformTemplateJsonData(String oldTemplateJsonData, {bool showLoading = true}) async {
    Directory baseExcelFileDir = await getApplicationDocumentsDirectory();
    TemplateUtils.documentPath = baseExcelFileDir.path;
    ExcelTransformManager.sharedInstance().documentPath = baseExcelFileDir.path;

    /// 解析模板数据
    Map<String, dynamic> jsonMap = await compute(decodeJson, oldTemplateJsonData);
    //处理value dataBind
    List<Map<String, dynamic>>? elements = [];
    List? datas = (jsonMap['elements'] as List?) ?? [];
    for (var item in datas) {
      if (item != null) {
        elements.add(Map<String, dynamic>.from(item));
      }
    }
    TemplateData _templateData = TemplateData().transformJson(jsonMap);
    ExternalData? oldExcelData = _templateData.externalData;
    if (_templateData.dataSource?.isNotEmpty ?? false) {
      if (showLoading) {
        CanvasPluginManager()
            .loadingToastImpl
            ?.showLoading(msg: intlanguage("app100001126", "数据加载中..."), dismissOnTap: true);
      }
      DataSource dataSource = _templateData.dataSource![0];
      if (dataSource.type == DataSourceType.excel) {
        if (!dataSource.uri.startsWith("http")) {
          // CanvasPluginManager().loadingToastImpl?.showToast("dataSource uri格式不正确");
          bool excelFileExist = await File(dataSource.uri).exists();
          if(!excelFileExist){
            debugPrint("==========dataSource uri格式不正确");
            return oldTemplateJsonData;
          }

        }else{
          final covertResult =
          await ExcelCovertManager.covertExcel("", dataSource.name!, dataSource.hash, dataSource.uri);
          dataSource.hash = covertResult.md5;
          dataSource.uri = covertResult.downloadUrl;
          await NiimbotDataSourceUtils.processDataSource(dataSource);
        }

      }
      String sheetName = getSheetName(dataSource);
      if(dataSource.type == DataSourceType.excel) {
        if(dataSource.headers != null && dataSource.headers!.isNotEmpty) {
          Map<String, dynamic> newMap = dataSource.headers!.map((key, value) => MapEntry("$sheetName", value));
          dataSource.headers = newMap;
        }
        jsonMap['dataSource'] = [dataSource.toJson()];
      }
      //elements处理
      jsonMap['elements'] = elements.map((e) {
        transformExcelValue(dataSource, e, sheetName);
        //表格的话要处理下cell和combineCells的赋值情况
        String type = e['type'];
        if (type == ElementItemType.table) {
          if (e['cells'] != null && e['cells'] is List) {
            e['cells'].forEach((v) {
              var cell = v as Map<String, dynamic>;
              cell["type"] = "text";
              transformExcelValue(dataSource, cell, sheetName);
            });
          }

          if (e['combineCells'] != null && e['combineCells'] is List) {
            e['combineCells'].forEach((v) {
              var cell = v as Map<String, dynamic>;
              cell['type'] = 'text';
              transformExcelValue(dataSource, cell, sheetName);
            });
          }
        }
        return e;
      }).toList();
      //datasource解析一下，如果本地没有excel原文件的话，执行下载操作
      DataSourceWrapper? dataSourceWrapper = ExcelManager.sharedInstance().dataSourceWrapper;
      if (dataSource.type == DataSourceType.commodity && dataSourceWrapper != null) {
        GoodFieldManager().goodFieldList = await GoodFieldManager().updateGoodFields();
      } else {
        if (dataSource.type == DataSourceType.commodity) {
          GoodFieldManager().goodFieldList = await GoodFieldManager().updateGoodFields();
          dataSource.hash = Uuid().v4().replaceAll("-", "");
          jsonMap['dataSource'] = [dataSource.toJson()];
        }
        dataSourceWrapper = await parseExcelContent(DataSource.fromJson(dataSource.toJson()));
        if (dataSource.type == DataSourceType.commodity && dataSource.params != null) {
          List goodsIds = dataSource.params["ids"] is List ? dataSource.params["ids"] : [];
          Map<String, dynamic>? goodsInfo = dataSource.params["commodity"];
          if (goodsIds.length > 0) {
            //Todo 商品id数量超过700 接口请求报错
            // goodsIds = goodsIds.sublist(0, 600);
            final goodsImportImpl = CanvasPluginManager().goodsImportImpl;
            final contentRowData = await goodsImportImpl?.requestGoodsList(goodsIds.join(","), (p0) => null);
            dataSourceWrapper.rowData = contentRowData!;
          } else if (goodsInfo != null) {
            List<String> goodsFieldValus = GoodFieldManager().getGoodsInfoListWithGoods(goodsInfo);
            if (goodsInfo["id"] != null && (goodsInfo["id"] as String).isNotEmpty) {
              final goodsImportImpl = CanvasPluginManager().goodsImportImpl;
              List<List<String>> contentRowData =
                  await goodsImportImpl!.requestGoodsList(goodsInfo["id"], (p0) => null);
              dataSourceWrapper.rowData = contentRowData;
            } else {
              dataSourceWrapper.rowData = [goodsFieldValus];
            }
          }
          if (dataSourceWrapper.rowData.isEmpty) {
            jsonMap['currentPage'] = 1;
            jsonMap['totalPage'] = 1;
          } else {
            jsonMap['totalPage'] = dataSourceWrapper.rowData.length;
            if (jsonMap['currentPage'] == null || dataSourceWrapper.rowData.length < jsonMap['currentPage']) {
              jsonMap['currentPage'] = 1;
            }
          }
          Map? bindInfo = jsonMap["bindInfo"];
          bindInfo?["total"] = jsonMap['totalPage'];
          bindInfo?["page"] = jsonMap['currentPage'];
          jsonMap["bindInfo"] = bindInfo;
        }else{
          Map bindInfo = jsonMap["bindInfo"] ?? {};
          if (bindInfo.isEmpty || bindInfo['total'] == 0) {
            bindInfo["total"] = jsonMap['totalPage'];
            bindInfo["page"] = jsonMap['currentPage'];
          }
          jsonMap["bindInfo"] = bindInfo;
        }

        ExcelTransformManager().setDataSourceWrapper(dataSourceWrapper);
        ExcelManager.sharedInstance().dataSourceWrapper = dataSourceWrapper;
      }
      //去除旧的excel导入相关字段
      //此处影响原生用到externalData里面excelid相关逻辑
      jsonMap.remove('externalData');
      jsonMap.remove('task');

      ///兼容处理数据源模板不接收modify中的修改
      if (_templateData.commodityTemplate && _templateData.modify != null) {
        _templateData.modify?.forEach((key, value) {
          value.forEach((rowKey, dataBindModify) {
            dataBindModify.value = null;
          });
        });
        jsonMap['modify'] = _templateData.modify?.toJson();
      }

      var templateDataJsonStr = jsonEncode(jsonMap);
      return templateDataJsonStr;
    } else if (oldExcelData != null &&
        oldExcelData.externalDataList != null &&
        oldExcelData.externalDataList!.isNotEmpty) {
      if (showLoading) {
        CanvasPluginManager()
            .loadingToastImpl
            ?.showLoading(msg: intlanguage("app100001126", "数据加载中..."), dismissOnTap: true);
      }
      List<ExternalListModel> oldDataList = oldExcelData!.externalDataList!;
      List<ExcelData> list = [];
      for (var i = 0; i < oldDataList.length; i++) {
        ExternalListModel oldItem = oldDataList[i];
        ExcelData item = ExcelData(
            name: "Sheet1",
            data: ExcelSheetData(columnHeaders: oldItem.data!.columnHeaders, columns: oldItem.data!.columns));
        list.add(item);
      }
      //转换excel数据
      //externalData和task转换成新的结构
      TemplateExcelData excelData = TemplateExcelData(
          id: int.tryParse(oldExcelData.id ?? "0"), fileName: oldExcelData.fileName ?? "", list: list);

      uploadFile(List<int> fileBytes, String fileName, String contentHash) async {
        if (!CanvasUserCenter().isLogin || CanvasPluginManager().fontPanelImpl?.isNetReachable() == false) {
          return "";
        }
        return ExcelManager.sharedInstance().uploadOSSFunction(fileName, Uint8List.fromList(fileBytes), contentHash,createCloudFile: false);
      }

      getExcelCloudFileById(num id, String md5) async {
        if (!CanvasUserCenter().isLogin || CanvasPluginManager().fontPanelImpl?.isNetReachable() == false) {
          return null;
        }
        if ((id == 0) && md5.isEmpty) {
          return null;
        }
        var excelCloudFile = await ExcelManager.sharedInstance().getCloudFileInfo(id.toInt(), md5);
        if (excelCloudFile == null) {
          return null;
        }
        final covertData = await ExcelCovertManager.covertExcel(
            id.toString(), excelCloudFile.name, excelCloudFile.md5, excelCloudFile.downloadUrl);
        excelCloudFile.downloadUrl = covertData.downloadUrl;
        excelCloudFile.md5 = covertData.md5;
        final ret = Future.value(excelCloudFile);
        return ret;
      }

      final dataSource =
          await NiimbotExcelUtils.covertExcelDataToDataSource(excelData, getExcelCloudFileById, uploadFile);
      // dataSource.type = DataSourceType.commodity;

      ///读取sheet名字
      String sheetName = getSheetName(dataSource!);
      DataSourceWrapper dataSourceWrapper = await TemplateUtils.parseExcelContent(dataSource);
      dataSourceWrapper.excelId = excelData.id?.toString();
      ExcelTransformManager().setDataSourceWrapper(dataSourceWrapper);
      ExcelManager.sharedInstance().dataSourceWrapper = dataSourceWrapper;
      _logger.log("TemplateUtils dataSource name=${dataSource.name} hash=${dataSource.hash} uri=${dataSource.uri}");
      _templateData.dataSource = [dataSource];
      jsonMap['dataSource'] = [dataSource.toJson()];

      tt.TemplateTask? oldTask = _templateData.task;
      if (oldTask != null && oldTask.modifyData != null) {
        TemplateTask newTask = TemplateTask();
        newTask.modifyData = oldTask.modifyData;
        TemplateModify? modify = taskToModify(newTask);
        _templateData.modify = modify;
      }

      int totalPage = 1;
      List<List<String>> rowData = dataSourceWrapper.rowData;
      if (rowData.isNotEmpty) {
        List<Range>? ranges = dataSource.range;
        if (ranges != null && ranges.isNotEmpty) {
          int rowCount = 0;
          for (int i = 0; i < rowData.length; i++) {
            if (ExcelTransformManager.sharedInstance().isRowSelect(i)) {
              rowCount++;
            }
          }
          totalPage = rowCount;
        } else {
          totalPage = rowData.length;
        }
      }
      ExcelPageInfo pageInfo = /*_templateData.totalPage*/
          ExcelPageInfo(page: _templateData.currentPageIndex + 1, total: totalPage);
      _templateData.bindInfo = pageInfo;
      jsonMap['bindInfo'] = pageInfo.toJson();

      //转换elements
      jsonMap['elements'] = elements.map((e) {
        transformElementContentTitle(_templateData, e);
        transformExcelValue(dataSource, e, sheetName);
        //表格的话要处理下cell和combineCells的赋值情况
        String type = e['type'];
        if (type == ElementItemType.table) {
          if (e['cells'] != null && e['cells'] is List) {
            e['cells'].forEach((v) {
              var cell = v as Map<String, dynamic>;
              cell['type']= 'text';
              transformElementContentTitle(_templateData, cell);
              transformExcelValue(dataSource, cell, sheetName);
            });
          }

          if (e['combineCells'] != null && e['combineCells'] is List) {
            e['combineCells'].forEach((v) {
              var cell = v as Map<String, dynamic>;
              cell['type']= 'text';
              transformElementContentTitle(_templateData, cell);
              transformExcelValue(dataSource, cell, sheetName);
            });
          }
        }
        return e;
      }).toList();
      if (_templateData.modify != null) {
        jsonMap['modify'] = _templateData.modify?.toJson();
      }

      //去除旧的excel导入相关字段
      jsonMap.remove('externalData');
      jsonMap.remove('task');
      var templateDataJsonStr = jsonEncode(jsonMap);
      return templateDataJsonStr;
    } else {
      return oldTemplateJsonData;
    }
  }

  static String getSheetName(DataSource dataSource) {
    String sheetName = "";
    if (dataSource.type == DataSourceType.excel) {
      String filePath = NiimbotDataSourceUtils.getLocalDataSourcePath(documentPath, dataSource.hash);
      List<String>? sheets = getSheetsFix(filePath);
      if(sheets?.isNotEmpty ?? false) {
        sheetName = sheets!.first;
      }
      else {
        sheetName = "Sheet1";
      }
    }
    return sheetName;
  }

  ///将旧的excel导入占位符转换为新的占位 ----> ${1⊙3}转换为C1
  static transformExcelValue(DataSource dataSource, Map<String, dynamic> json, String sheetName) {
    var oldValue = json['value'].toString();
    var type = json['type'].toString();
    if (type != ElementItemType.text && type != ElementItemType.barcode && type != ElementItemType.qrcode) return;
    if (oldValue.contains("⊙")) {
      var str = "${oldValue}";
      var value = NiimbotExcelUtils.getElementBindIndex(str);
      json['value'] = value;
      if (value != null) {
        if (dataSource.type != DataSourceType.commodity) {
          json['dataBind'] = [dataSource.hash, sheetName];
        } else {
          json['dataBind'] = ["", DataSourceType.commodity.getStringValue()];
        }
      } else {
        json['dataBind'] = null;
      }
    }else if(dataSource.type == DataSourceType.excel){
      //有些老excel模版元素中的dataBind sheet和解析出来的不一致 更新下用于兼容
      if(json['dataBind'] != null && (json['dataBind'] is List) && (json['dataBind'] as List).isNotEmpty){
        json['dataBind'] = [dataSource.hash, sheetName];
      }

    }
  }

  ///将旧的excel导入占位符转换为新的占位 ----> ${1⊙3}转换为C1
  static transformElementContentTitle(TemplateData templateData, Map<String, dynamic> e) {
    //将contentTitle字段转换为modify对应元素o行
    String? contentTitle = e['contentTitle'];
    if (contentTitle != null && contentTitle.isNotEmpty) {
      Map<String, DataBindModify> elementModify = {};
      elementModify['0'] = DataBindModify(useTitle: true, delimiter: "：", title: contentTitle);
      var modify = templateData.modify;
      if (modify == null) {
        modify = {};
      }
      templateData.modify = modify;
      if (modify[e['id']] != null) {
        Map<String, DataBindModify>? modifyMap = modify[e['id']];
        modifyMap?.addAll(elementModify);
        modify[e['id']] = modifyMap!;
        //更新这个元素原有modify结构中，修改页数的useTitle值
        modifyMap.forEach((k, v) {
          v.useTitle = true;
          v.delimiter = "：";
          v.title = contentTitle;
        });
      } else {
        modify[e['id']] = elementModify;
      }
    }
  }

  ///克隆excel导入修改
  static TemplateModify? cloneModify(TemplateModify? modify) {
    if (modify == null) {
      return null;
    }
    TemplateModify cloneObj = TemplateModifyExtension.fromJson(jsonDecode(jsonEncode(modify.toJson())));
    return cloneObj;
  }

  ///克隆excel数据源
  static DataSource cloneDataSource(DataSource dataSource) {
    DataSource cloneObj = DataSource.fromJson(jsonDecode(jsonEncode(dataSource.toJson())));
    return cloneObj;
  }

  ///本地解析excel数据源
  static Future<DataSourceWrapper> parseExcelContent(DataSource? dataSource) async {
    List<String> headers = [];
    List<List<String>> excelContent = [];
    Directory baseExcelFileDir = await getApplicationDocumentsDirectory();
    TemplateUtils.documentPath = baseExcelFileDir.path;
    if (dataSource?.type == DataSourceType.commodity) {
      headers = TemplateData.goodsInfnFieldDescName();
    } else {
      final dynamicSourceData = await DynamicSourceDataManager().readDynamicSourceData(dataSource!.hash);
      if (dynamicSourceData != null) {
        headers = dynamicSourceData.headers;
        excelContent = dynamicSourceData.rowData;
      } else {
        String filePath = NiimbotDataSourceUtils.getLocalDataSourcePath(documentPath, dataSource.hash);
        List<String>? sheets = getSheetsFix(filePath);
        String sheetName = (sheets != null && sheets.length > 0) ? sheets.first : "Sheet1";
        List<List<String>> allExcelContent =
            await Isolate.run(() => NiimbotExcelUtils.getContent(filePath, sheetName).toList(growable: true));
        if (allExcelContent.isNotEmpty) {
          allExcelContent = _truncateList(allExcelContent);
          if (dataSource.headers?.isNotEmpty ?? false) {
            var headerInfo = dataSource.headers?[sheetName];

            ///代表设定第一行为表头
            // if (headerInfo is int) {
            //   headers = allExcelContent.first;
            //   allExcelContent.removeAt(0);
            // }
            ///如果是自定义表头，则直接使用该表头
            if (headerInfo is List<String>) {
              headers = headerInfo;
            } else {
              headers = allExcelContent.first;
              allExcelContent.removeAt(0);
            }
          }
          excelContent = allExcelContent;
        }

        ///如果headers为空，则根据实际列数填充默认列头
        if (headers.isEmpty && excelContent.isNotEmpty) {
          int columnCount = excelContent[0].length;
          for (var i = 0; i < columnCount; i++) {
            String columnLetter = NiimbotExcelUtils.indexToLetters(i + 1);
            // headers.add("${intlanguage("app00133", "列名")}${i + 1}");
            headers.add("${intlanguage("app100001121", "列")}${columnLetter}");
          }
        }
        if (headers.isNotEmpty && excelContent.isNotEmpty) {
          await DynamicSourceDataManager().cacheDynamicSourceData(
              DynamicSourceData(headers: headers, rowData: excelContent, sheetName: sheetName, hash: dataSource.hash));
        }
      }
    }
    DataSourceWrapper wrapper = DataSourceWrapper(headers: headers, rowData: excelContent, dataSource: dataSource!);
    return wrapper;
  }

  static List<String>? getSheetsFix(String filePath) {
    try {
      return NiimbotExcelUtils.getSheets(filePath);
    } on NoSuchMethodError catch (e) {

    } catch (e, s) {

    }
    return null;
  }

  static List<List<String>> _truncateList(List<List<String>> data) {
    if (data.length > 2000) {
      return data.sublist(0, 2001);
    } else {
      return data;
    }
  }

  ///变更元素修改（包含列名是否显示，列名修改，列值修改）
  static void changeElementModify(JsonElement? element, ElementModifyType modifyType,
      {bool displayHeader = false, String? afterTitle, String? afterValue, bool isGlobalEffective = false}) {
    if (element == null) {
      return;
    }
    final originModify = TemplateUtils.cloneModify(ExcelTransformManager().templateData?.modify);
    final modifyMap = ExcelTransformManager().getElementOriginModify(element.id);
    final globalModify = modifyMap?.putIfAbsent("0", () => DataBindModify(delimiter: "："));
    int excelRow = ExcelTransformManager.sharedInstance().getCurrentExcelRow();

    ///列名的显示与修改，全局生效
    if (modifyType == ElementModifyType.modifyUseTitle) {
      globalModify?.useTitle = displayHeader;
      // if (isGlobalEffective) {
      //   globalModify.useTitle = displayHeader;
      // } else {
      //   DataBindModify rowModify = modifyMap.putIfAbsent(currentPage.toString(), () => DataBindModify());
      //   rowModify.useTitle = displayHeader;
      // }
    } else if (modifyType == ElementModifyType.modifyTitle) {
      // DataBindModify rowModify = modifyMap.putIfAbsent(currentPage.toString(), () => DataBindModify());
      // rowModify.title = afterTitle;
      globalModify?.title = afterTitle;
    } else if (modifyType == ElementModifyType.modifyValue) {
      final rowModify = modifyMap?.putIfAbsent(excelRow.toString(), () => DataBindModify());
      rowModify?.value = afterValue;
    }
    final modifyAfter = TemplateUtils.cloneModify(ExcelTransformManager().templateData?.modify);
    //记录元素修改记录
    StackManager().stashTemplateModifyRecord(originModify, modifyAfter);
  }

  ///删除元素绑定关系
  static void delElementModify(JsonElement? element) {
    if (element == null) {
      return;
    }
    final originModify = TemplateUtils.cloneModify(ExcelTransformManager().templateData?.modify);
    final modifyMap = ExcelTransformManager().getElementOriginModify(element.id);
    modifyMap?.clear();
    final modifyAfter = TemplateUtils.cloneModify(ExcelTransformManager().templateData?.modify);
    //记录元素修改记录
    StackManager().stashTemplateModifyRecord(originModify, modifyAfter);
  }

  ///元素是否有标题
  static bool hasTitle(JsonElement? element) {
    if (element == null) {
      return false;
    }
    final modifyMap = ExcelTransformManager().getElementOriginModify(element.id);
    final globalModify = modifyMap?["0"];
    int excelRow = ExcelTransformManager.sharedInstance().getCurrentExcelRow();
    DataBindModify? rowModify = modifyMap?[excelRow.toString()];
    bool useTitle = rowModify?.useTitle ?? globalModify?.useTitle ?? false;
    return useTitle;
  }

  //处理离线或者断网 datasource url如果为空的时候，上传oss以及添加到云文件的逻辑
  static Future<bool> processDataSourceUrl() async {
    DataSource? dataSource = ExcelTransformManager().getDataSource();
    if (dataSource != null &&
        dataSource.type == DataSourceType.excel &&
        dataSource.hash.isNotEmpty &&
        !dataSource.uri.startsWith("http")) {
      if (CanvasPluginManager().fontPanelImpl?.isNetReachable() == false) {
        // CanvasPluginManager().loadingToastImpl.showToast("该模板无法离线保存，请连接网络！");
        CanvasPluginManager().loadingToastImpl?.showToast(intlanguage("app100001137", "该模板不支持离线保存，请连接网络！"));
        return false;
      }

      if (!CanvasUserCenter().isLogin) {
        String title = intlanguage('app00210', '当前未登录，请先登录！');
        String cancelDes = intlanguage('app00030', '取消');
        String confirmDes = intlanguage('app01191', '立即登录');
        showNimmbotDialog(canvasKey.currentContext!, title: title, cancelDes: cancelDes, confirmDes: confirmDes,
            confirmAction: () {
          CanvasUserCenter().handleLogin(canvasKey.currentContext!, () {});
        });
        return false;
      }
      CanvasPluginManager().loadingToastImpl?.showLoading();
      var excelCloudFile = await getExcelCloudFileById(int.tryParse(ExcelTransformManager().excelId ?? "") ?? 0);
      if (excelCloudFile != null) {
        dataSource.uri = excelCloudFile.downloadUrl;
      } else {
        String filePath = NiimbotDataSourceUtils.getLocalDataSourcePath(documentPath, dataSource.hash);
        File file = File(filePath);
        if(file.existsSync()){
          Uint8List fileBytes = await file.readAsBytes();
          String url =
          await ExcelManager.sharedInstance().uploadOSSFunction(dataSource.name!, fileBytes, dataSource.hash,createCloudFile: false);
          dataSource.uri = url;
        }else{
          CanvasPluginManager().loadingToastImpl?.dismissLoading();
          return false;
        }
      }
      CanvasPluginManager().loadingToastImpl?.dismissLoading();
      return true;
    }
    return true;
  }

  static getExcelCloudFileById(num id) async {
    if (!CanvasUserCenter().isLogin || CanvasPluginManager().fontPanelImpl?.isNetReachable() == false) {
      return null;
    }
    if (id == 0) {
      return null;
    }
    return ExcelManager.sharedInstance().getExcelDataById(id.toInt());
  }

  ///把新的模版数据字符串 转换为 可以兼容旧的excel导入的模版数据结构的字符串表示
  static String transformToCompactTemplateJsonData(String newTemplateJsonData,
      {bool dateSourceChange = false, bool getRealContentTitle = false}) {
    try {
      /// 解析模板数据
      Map<String, dynamic> jsonMap = jsonDecode(newTemplateJsonData);
      TemplateData _templateData = TemplateData.fromJson(jsonMap);
      if (_templateData.dataSource?.isNotEmpty ?? false) {
        _templateData.dataSource!.first.uri = (ExcelTransformManager().getDataSource()?.uri)!;
        //转换elements,兼容旧的模板结构，主要处理contentTitle value
        List<JsonElement> elements = _templateData.elements ?? [];
        for (JsonElement element in elements) {
          if (element.isTextElement()) {
            if (element.type == ElementItemType.date && element.contentTitle != null) {
              (element as TextElement).contentTitle = element.contentTitle! + "-save-";
            } else {
              (element as TextElement).contentTitle =
                  ExcelTransformManager().getElementModify(element, getRealContentTitle: getRealContentTitle)?.title;
            }
            if (element.type == ElementItemType.date && dateSourceChange) {
              element.value = element.value! + "#";
            }
          }
          if (element.isBindingElement()) {
            // data['value'] = '\${0⊙$bindingColumn}';
            int bindingColumn = element.getBindingColumn();
            if(bindingColumn > -1){
              element.value = '\${0⊙$bindingColumn}';
            }
          }
          if (element.type == ElementItemType.table) {
            TableElement tableElement = element as TableElement;
            tableElement.cells.forEach((cell) {
              cell.contentTitle = ExcelTransformManager().getElementModify(cell)?.title;
              if (cell.isBindingElement()) {
                int bindingColumn = cell.getBindingColumn();
                if(bindingColumn > -1){
                  cell.value = '\${0⊙$bindingColumn}';
                }
              }
            });

            tableElement.combineCells.forEach((cell) {
              cell.contentTitle = ExcelTransformManager().getElementModify(cell)?.title;
              if (cell.isBindingElement()) {
                int bindingColumn = cell.getBindingColumn();
                if(bindingColumn > -1){
                  cell.value = '\${0⊙$bindingColumn}';
                }
              }
            });
          }
        }
        if (_templateData.modify?.isNotEmpty ?? false) {
          _templateData.task = ExcelTransformManager().transformTemplateModifyToTemplateTask();
        }
        if (_templateData.dataSource?.isNotEmpty ?? false) {
          _templateData.externalData = ExcelTransformManager().transformDataSourceToExternalData();
        }
        if (_templateData.bindInfo != null) {
          _templateData.currentPageIndex = _templateData.bindInfo!.page == null ? 0 : _templateData.bindInfo!.page! - 1;
          _templateData.totalPage = _templateData.bindInfo!.total == null ? 1 : _templateData.bindInfo!.total!;
        }
        var templateDataJsonStr = jsonEncode(_templateData.toJson(
          compactOldTemplate: true,
        ));
        return templateDataJsonStr;
      }
    } catch (e, stack) {
      print("error==$stack");
    }
    return newTemplateJsonData;
  }

  ///把画板模板数据字符串 转换为 可以兼容旧的模版数据结构的字符串表示 主要处理contentTitle
  static String transformContentTitle(String newTemplateJsonData,
      {bool dateSourceChange = false, bool getRealContentTitle = false}) {
    try {
      /// 解析模板数据
      Map<String, dynamic> jsonMap = jsonDecode(newTemplateJsonData);
      TemplateData _templateData = TemplateData.fromJson(jsonMap);
      _templateData.dataSource = ExcelTransformManager().templateData?.dataSource;
      if (_templateData.dataSource?.isNotEmpty ?? false) {
        //转换elements,兼容旧的模板结构，主要处理contentTitle value
        List<JsonElement> elements = _templateData.elements ?? [];
        for (JsonElement element in elements) {
          if (element.isTextElement()) {
            if (element.type == ElementItemType.date && element.contentTitle != null) {
              (element as TextElement).contentTitle = element.contentTitle! + "-save-";
            } else {
              (element as TextElement).contentTitle =
                  ExcelTransformManager().getElementModify(element, getRealContentTitle: getRealContentTitle)?.title;
            }
          }
          if (element.type == ElementItemType.table) {
            TableElement tableElement = element as TableElement;
            tableElement.cells.forEach((cell) {
              cell.contentTitle = ExcelTransformManager().getElementModify(cell)?.title;
            });

            tableElement.combineCells.forEach((cell) {
              cell.contentTitle = ExcelTransformManager().getElementModify(cell)?.title;
            });
          }
        }
        var templateDataJsonStr = jsonEncode(_templateData.toJson(
          compactOldTemplate: true,
        ));
        return templateDataJsonStr;
      }
    } catch (e, stack) {
      print("error==$stack");
    }
    return newTemplateJsonData;
  }

  //通过商品信息转换成externalData
  static Map<String, dynamic> getExternalDataWithGoods(String goodJson) {
    final jsonData = (json.decode(goodJson) as List?);
    jsonData?.removeWhere((v) => v == null);
    List<Map<String, dynamic>>? goods = jsonData?.map((e) => Map<String, dynamic>.from(e)).toList();
    List<List<String>> selectedGoods = [];
    if (goods != null) {
      for (var fieldName in TemplateData.goodsInfnFieldName()) {
        List<String> fieldGoods = [];
        for (Map element in goods) {
          if (fieldName == 'id') {
            if (element["goodId"] != null && element["goodId"] is String && element["goodId"].isNotEmpty) {
              fieldGoods.add(element['goodId']);
            } else {
              fieldGoods.add(element[fieldName] ?? "");
            }
          } else {
            if (element[fieldName] != null && element[fieldName] is String) {
              fieldGoods.add(element[fieldName]);
            } else if (element[fieldName] != null && element[fieldName] is int) {
              fieldGoods.add(element[fieldName].toString());
            } else {
              fieldGoods.add('');
            }
          }
        }
        selectedGoods.add(fieldGoods);
      }
    }
    List<String> headers = TemplateData.goodsInfnFieldDescName();
    ExternalData externalData = ExternalData(externalDataList: [
      ExternalListModel(name: "", data: ExternalListModelData(columnHeaders: headers, columns: selectedGoods))
    ], id: "0", fileName: "");
    return externalData.toJson();
  }

  ///用于在模板详情直接打印时，转换pc新excel结构的数据,注意从原生端传过来的数据value已经变成了眼睛，需要再转换下
  static Future<String> transformPcNewExcelToCompactTemplateJsonData(String pcNewExcelTemplateJsonData) async {
    try {
      Directory baseExcelFileDir = await getApplicationDocumentsDirectory();
      TemplateUtils.documentPath = baseExcelFileDir.path;
      ExcelTransformManager().documentPath = baseExcelFileDir.path;
      ExcelTransformManager.sharedInstance().documentPath = baseExcelFileDir.path;

      /// 解析模板数据
      Map<String, dynamic> jsonMap = jsonDecode(pcNewExcelTemplateJsonData);

      ///临时解析下，主要是拿dataSource,做眼睛的转换
      TemplateData tempTemplateData = TemplateData.fromJson(jsonMap);
      AdvanceQRCodeManager().initAdvanceQRCodeCache(TemplateData.fromJson(jsonMap));
      if (tempTemplateData.dataSource?.isNotEmpty ?? false) {
        DataSource dataSource = tempTemplateData.dataSource!.first;
        String sheetName = getSheetName(dataSource);
        //处理value dataBind
        List<Map<String, dynamic>?> elements =
            (jsonMap['elements'] as List).map((e) => e == null ? null : Map<String, dynamic>.from(e)).toList() ?? [];
        jsonMap['elements'] = elements.map((e) {
          transformExcelValue(dataSource, e!, sheetName);
          //表格的话要处理下cell和combineCells的赋值情况
          String type = e['type'];
          if (type == ElementItemType.table) {
            if (e['cells'] != null && e['cells'] is List) {
              e['cells'].forEach((v) {
                var cell = v as Map<String, dynamic>;
                cell['type']= 'text';
                transformExcelValue(dataSource, cell, sheetName);
              });
            }

            if (e['combineCells'] != null && e['combineCells'] is List) {
              e['combineCells'].forEach((v) {
                var cell = v as Map<String, dynamic>;
                cell['type']= 'text';
                transformExcelValue(dataSource, cell, sheetName);
              });
            }
          }
          return e;
        }).toList();
      }

      ///此时jsonMap为纯净的pc excel新数据
      TemplateData _templateData = TemplateData.fromJson(jsonMap);
      ExcelTransformManager().templateData = _templateData;
      if (_templateData.dataSource?.isNotEmpty ?? false) {
        DataSource dataSource = _templateData.dataSource!.first;
        DataSourceWrapper dataSourceWrapper;
        if (dataSource.type == DataSourceType.commodity && dataSource.params != null) {
          await GoodFieldManager().getGoodFields();
          dataSourceWrapper = await parseExcelContent(dataSource);
          List goodsIds = dataSource.params["ids"] is List ? dataSource.params["ids"] : [];
          Map<String, dynamic>? goodsInfo = dataSource.params["commodity"];
          if (goodsIds != null && goodsIds.length > 0) {
            final goodsImportImpl = CanvasPluginManager().goodsImportImpl;
            final contentRowData = await goodsImportImpl?.requestGoodsList(goodsIds.join(","), (p0) => null);
            dataSourceWrapper.rowData = contentRowData!;
          } else if (goodsInfo != null) {
            List<String> goodsFieldValus = GoodFieldManager().getGoodsInfoListWithGoods(goodsInfo);
            dataSourceWrapper.rowData = [goodsFieldValus];
          }
          if (dataSourceWrapper.rowData.isEmpty) {
            jsonMap['currentPage'] = 1;
            jsonMap['totalPage'] = 1;
          } else {
            jsonMap['totalPage'] = dataSourceWrapper.rowData.length;
            if (dataSourceWrapper.rowData.length < jsonMap['currentPage']) {
              jsonMap['currentPage'] = 1;
            }
          }
          //保持bindinfo里面的page信息与模版里面的page信息一致
          if (jsonMap["bindInfo"] != null && jsonMap["bindInfo"].runtimeType.toString() == "_Map<String, dynamic>") {
            Map<String, dynamic> bindInfo = jsonMap["bindInfo"];
            bindInfo['page'] = jsonMap['currentPage'];
            bindInfo['total'] = jsonMap['totalPage'];
          }
        } else {
          final covertResult =
              await ExcelCovertManager.covertExcel("", dataSource.name!, dataSource.hash, dataSource.uri);
          dataSource.hash = covertResult.md5;
          dataSource.uri = covertResult.downloadUrl;
          await NiimbotDataSourceUtils.processDataSource(dataSource);
          dataSourceWrapper = await parseExcelContent(dataSource);
        }
        ExcelTransformManager().setDataSourceWrapper(dataSourceWrapper);
        var templateDataJsonStr = jsonEncode(jsonMap);
        String compactJson = transformToCompactTemplateJsonData(templateDataJsonStr);
        ExcelTransformManager().clearData();
        return compactJson;
      }
    } catch (e, stack) {
      ExcelTransformManager().clearData();
      print("error==$stack");
    }
    return pcNewExcelTemplateJsonData;
  }

  ///用于在模板详情直接打印时，转换pc新excel结构的数据,注意从原生端传过来的数据value已经变成了眼睛，需要再转换下
  static Future<List> getTemplateRowDataWith(Map<String, dynamic> dataSourceInfo) async {
    try {
      List rowDataHeader = [];
      List<List<String>>? rowData;
      DataSource dataSource = DataSource.fromJson(dataSourceInfo);
      List<String> headers = [];
      // DataSourceWrapper dataSourceWrapper = await parseExcelContent(DataSource.fromJson(dataSource.toJson()));
      // List<String> headers = dataSourceWrapper.headers;
      if (dataSource.type == DataSourceType.commodity && dataSource.params != null) {
        DataSourceWrapper dataSourceWrapper = await parseExcelContent(DataSource.fromJson(dataSource.toJson()));
        headers = dataSourceWrapper.headers;
        await GoodFieldManager().getGoodFields();
        List goodsIds = dataSource.params["ids"] is List ? dataSource.params["ids"] : [];
        Map<String, dynamic>? goodsInfo = dataSource.params["commodity"];
        if (goodsIds.length > 0) {
          final goodsImportImpl = CanvasPluginManager().goodsImportImpl;
          final contentRowData = await goodsImportImpl?.requestGoodsList(goodsIds.join(","), (p0) => null);
          rowData = contentRowData!;
        } else if (goodsInfo != null) {
          List<String> goodsFieldValus = GoodFieldManager().getGoodsInfoListWithGoods(goodsInfo);
          rowData = [goodsFieldValus];
        }
      }else if(dataSource.type == DataSourceType.commodity){
        Map headersAndRowData = await CanvasPluginManager().hostMethodImpl?.getCapHeaderAndRowData() ?? {};
        headers = headersAndRowData["headers"];
        rowData = headersAndRowData["rowData"];
      } else {
        if (!dataSource.uri.startsWith("http")) {
          bool excelFileExist = await File(dataSource.uri).exists();
          if(!excelFileExist){
            debugPrint("==========dataSource uri格式不正确");
          }
        }else{
          final covertResult =
          await ExcelCovertManager.covertExcel("", dataSource.name!, dataSource.hash, dataSource.uri);
          dataSource.hash = covertResult.md5;
          dataSource.uri = covertResult.downloadUrl;
          await NiimbotDataSourceUtils.processDataSource(dataSource);
        }

        DataSourceWrapper dataSourceWrapper = await parseExcelContent(dataSource);
        rowData = dataSourceWrapper.rowData;
        headers = dataSourceWrapper.headers;
      }
      CanvasPluginManager().loadingToastImpl?.dismissLoading();
      if (rowData == null || rowData.isEmpty) return [[], headers];
      rowData.insert(0, headers);
      rowDataHeader = [rowData, headers];
      return rowDataHeader;
    } catch (e, stack) {
      CanvasPluginManager().loadingToastImpl?.dismissLoading();
      print("error==$stack");
      return [];
    }
  }

  ///用于在模板详情直接打印时，转换pc新excel结构的数据,注意从原生端传过来的数据value已经变成了眼睛，需要再转换下
  static Future<List<String>> getTemplateHeaderWith(Map<String, dynamic> dataSourceInfo) async {
    try {
      List<String> headers;
      DataSource dataSource = DataSource.fromJson(dataSourceInfo);
      DataSourceWrapper dataSourceWrapper = await parseExcelContent(DataSource.fromJson(dataSource.toJson()));
      headers = dataSourceWrapper.headers;
      return headers;
    } catch (e, stack) {
      ExcelTransformManager().clearData();
      print("error==$stack");
      return [];
    }
  }

  ///把新的商品库模板转换为可以兼容旧模板的结构
  static Future<Map<String, dynamic>> transformGoodTemplateToCompactOldTemplateJsonData(
      Map<String, dynamic> goodTemplateJson, List<Map<String, dynamic>> goodList,
      {bool getRealContentTitle = false}) async {
    TemplateData _templateData = TemplateData.fromJson(goodTemplateJson);
    if (ExcelTransformManager().templateData == null) {
      ExcelTransformManager().templateData = _templateData;
    }
    if (_templateData.dataSource?.isNotEmpty ?? false) {
      DataSource dataSource = _templateData.dataSource![0];
      DataSourceWrapper? dataSourceWrapper;
      if (dataSource.type == DataSourceType.commodity && dataSource.params != null) {
        dataSourceWrapper =
            DataSourceWrapper(headers: TemplateData.goodsInfnFieldDescName(), rowData: [], dataSource: dataSource);
        if (goodList.isNotEmpty) {
          List<List<String>> goodsData = [];
          for (int i = 0; i < goodList.length; i++) {
            Map<String, dynamic> goodsInfo = goodList[i];
            List<String> goodsFieldValus = GoodFieldManager().getGoodsInfoListWithGoods(goodsInfo);
            goodsData.add(goodsFieldValus);
          }
          dataSourceWrapper.rowData = goodsData;
        } else {
          List goodsIds = dataSource.params["ids"] is List ? dataSource.params["ids"] : [];
          if (goodsIds.length > 0) {
            final goodsImportImpl = CanvasPluginManager().goodsImportImpl;
            final contentRowData = await goodsImportImpl?.requestGoodsList(goodsIds.join(","), (p0) => null);
            dataSourceWrapper.rowData = contentRowData!;
          }
        }
        if (dataSourceWrapper.rowData.isEmpty) {
          goodTemplateJson['currentPage'] = 1;
          goodTemplateJson['totalPage'] = 1;
        } else {
          goodTemplateJson['totalPage'] = dataSourceWrapper.rowData.length;
          if (goodTemplateJson['currentPage'] != null &&
              dataSourceWrapper.rowData.length < goodTemplateJson['currentPage']) {
            goodTemplateJson['currentPage'] = 1;
          }
        }
      }
      ExcelTransformManager().setDataSourceWrapper(dataSourceWrapper);
      var templateDataJsonStr = jsonEncode(goodTemplateJson);
      String compactJson =
          transformToCompactTemplateJsonData(templateDataJsonStr, getRealContentTitle: getRealContentTitle);
      Map<String, dynamic> compactTemplateJson = jsonDecode(compactJson);
      // ExcelTransformManager().clearData();
      return compactTemplateJson;
    }
    return goodTemplateJson;
  }

  ///对比模板中的modify信息
  static bool compairModifyInfo(TemplateData oldData, TemplateData canvasData) {
    TemplateModify oldModify = oldData.modify ?? {};
    TemplateModify newModify = canvasData.modify ?? {};
    if (oldModify.isEmpty && newModify.isEmpty) {
      return true;
    }
    for (var elementId in newModify.keys) {
      if (oldModify.containsKey(elementId)) {
        Map<String, DataBindModify>? oldElementModifyMap = oldModify[elementId];
        Map<String, DataBindModify>? newElementModifyMap = newModify[elementId];

        for (var rowIndex in newElementModifyMap!.keys) {
          if (oldElementModifyMap?.containsKey(rowIndex) ?? false) {
            DataBindModify? oldDataBindModify = oldElementModifyMap?[rowIndex];
            DataBindModify? newDataBindModify = newElementModifyMap[rowIndex];
            if (oldDataBindModify != newDataBindModify) {
              return false;
            }
          } else {
            return false;
          }
        }
      } else {
        return false;
      }
    }
    return true;
  }

  ///把旧的模版数据字符串 转换为 新的excel导入的模版数据结构的字符串表示
  static Future<Map<String, dynamic>> transformTemplateToStand(Map<String, dynamic> templateMap) async {
    /// 解析模板数据
    //处理value dataBind
    try {
      Map<String, dynamic> jsonMap = templateMap;
      List<Map<String, dynamic>>? elements = [];
      List? datas = (jsonMap['elements'] as List?) ?? [];
      for (var item in datas) {
        if (item != null) {
          elements.add(Map<String, dynamic>.from(item));
        }
      }
      TemplateData _templateData = TemplateData().transformJson(jsonMap);
      if (_templateData.dataSource?.isNotEmpty ?? false) {
        DataSource dataSource = _templateData.dataSource![0];
        if (dataSource.type == DataSourceType.excel) {
          if (!dataSource.uri.startsWith("http")) {
            // CanvasPluginManager().loadingToastImpl?.showToast("dataSource uri格式不正确");
            bool excelFileExist = await File(dataSource.uri).exists();
            if(!excelFileExist){
              debugPrint("==========dataSource uri格式不正确");
              return templateMap;
            }
          }else{
            final covertResult =
            await ExcelCovertManager.covertExcel("", dataSource.name!, dataSource.hash, dataSource.uri);
            dataSource.hash = covertResult.md5;
            dataSource.uri = covertResult.downloadUrl;
            await NiimbotDataSourceUtils.processDataSource(dataSource);
          }
        } else if (dataSource.type == DataSourceType.commodity) {
          templateMap["dataSource"][0] = dataSource.toJson();
        }
        String sheetName = getSheetName(dataSource);
        if(dataSource.type == DataSourceType.excel) {
          if(dataSource.headers != null && dataSource.headers!.isNotEmpty) {
            Map<String, dynamic> newMap = dataSource.headers!.map((key, value) => MapEntry("$sheetName", value));
            dataSource.headers = newMap;
          }
          jsonMap['dataSource'] = [dataSource.toJson()];
        }
        //elements处理
        elements = elements.map((e) {
          transformExcelValue(dataSource, e, sheetName);
          //表格的话要处理下cell和combineCells的赋值情况
          String type = e['type'];
          if (type == ElementItemType.table) {
            if (e['cells'] != null && e['cells'] is List) {
              e['cells'].forEach((v) {
                var cell = v as Map<String, dynamic>;
                cell['type']= 'text';
                transformExcelValue(dataSource, cell, sheetName);
              });
            }

            if (e['combineCells'] != null && e['combineCells'] is List) {
              e['combineCells'].forEach((v) {
                var cell = v as Map<String, dynamic>;
                cell['type']= 'text';
                transformExcelValue(dataSource, cell, sheetName);
              });
            }
          }
          return e;
        }).toList();
        templateMap['elements'] = elements;
        Map bindInfo = jsonMap["bindInfo"] ?? {};
        if (bindInfo.isEmpty || bindInfo['total'] == 0) {
          bindInfo["total"] = jsonMap['totalPage'];
          bindInfo["page"] = jsonMap['currentPage'];
        }
        templateMap["bindInfo"] = bindInfo;
        return templateMap;
      } else if (_templateData.externalData?.externalDataList != null &&
          _templateData.externalData!.externalDataList!.isNotEmpty) {
        List<ExternalListModel> oldDataList = _templateData.externalData!.externalDataList!;
        List<ExcelData> list = [];
        for (var i = 0; i < oldDataList.length; i++) {
          ExternalListModel oldItem = oldDataList[i];
          ExcelData item = ExcelData(
              name: "Sheet1",
              data: ExcelSheetData(columnHeaders: oldItem.data!.columnHeaders, columns: oldItem.data!.columns));
          list.add(item);
        }
        //转换excel数据
        //externalData和task转换成新的结构
        TemplateExcelData excelData = TemplateExcelData(
            id: int.tryParse(_templateData.externalData!.id ?? "0"),
            fileName: _templateData.externalData!.fileName ?? "",
            list: list);

        uploadFile(List<int> fileBytes, String fileName, String contentHash) async {
          if (!CanvasUserCenter().isLogin || CanvasPluginManager().fontPanelImpl?.isNetReachable() == false) {
            return "";
          }
          return ExcelManager.sharedInstance().uploadOSSFunction(fileName, Uint8List.fromList(fileBytes), contentHash,createCloudFile: false);
        }

        getExcelCloudFileById(num id, String md5) async {
          if (!CanvasUserCenter().isLogin || CanvasPluginManager().fontPanelImpl?.isNetReachable() == false) {
            return null;
          }
          if ((id == 0) && md5.isEmpty) {
            return null;
          }
          var excelCloudFile = await ExcelManager.sharedInstance().getCloudFileInfo(id.toInt(), md5);
          if (excelCloudFile == null) {
            return null;
          }
          final covertData = await ExcelCovertManager.covertExcel(
              id.toString(), excelCloudFile.name, excelCloudFile.md5, excelCloudFile.downloadUrl);
          excelCloudFile.downloadUrl = covertData.downloadUrl;
          excelCloudFile.md5 = covertData.md5;
          final ret = Future.value(excelCloudFile);
          return ret;
        }

        final dataSource =
            await NiimbotExcelUtils.covertExcelDataToDataSource(excelData, getExcelCloudFileById, uploadFile);
        templateMap["dataSource"] = [dataSource!.toJson()];

        tt.TemplateTask? oldTask = _templateData.task;
        if (oldTask != null && oldTask.modifyData != null) {
          TemplateTask newTask = TemplateTask();
          newTask.modifyData = oldTask.modifyData;
          TemplateModify? modify = taskToModify(newTask);
          _templateData.modify = modify;
        }
        elements = elements.map((e) {
          transformElementContentTitle(_templateData, e);
          transformExcelValue(dataSource, e, dataSource.name ?? "");
          //表格的话要处理下cell和combineCells的赋值情况
          String type = e['type'];
          if (type == ElementItemType.table) {
            if (e['cells'] != null && e['cells'] is List) {
              e['cells'].forEach((v) {
                var cell = v as Map<String, dynamic>;
                cell['type']= 'text';
                transformElementContentTitle(_templateData, cell);
                transformExcelValue(dataSource, cell, dataSource.name ?? "");
              });
            }

            if (e['combineCells'] != null && e['combineCells'] is List) {
              e['combineCells'].forEach((v) {
                var cell = v as Map<String, dynamic>;
                cell['type']= 'text';
                transformElementContentTitle(_templateData, cell);
                transformExcelValue(dataSource, cell, dataSource.name ?? "");
              });
            }
          }
          return e;
        }).toList();

        if (_templateData.modify != null) {
          jsonMap['modify'] = _templateData.modify?.toJson();
        }
        templateMap['elements'] = elements;
        Map bindInfo = jsonMap["bindInfo"] ?? {};
        if (bindInfo.isEmpty) {
          bindInfo["total"] = jsonMap['totalPage'];
          bindInfo["page"] = jsonMap['currentPage'];
        }
        templateMap["bindInfo"] = bindInfo;
        return templateMap;
      } else {
        return templateMap;
      }
    } catch (e, stack) {
      return templateMap;
    }
  }
}

enum ElementModifyType {
  /// 修改是否显示列名
  modifyUseTitle,

  /// 修改列名
  modifyTitle,

  /// 修改列值
  modifyValue
}
