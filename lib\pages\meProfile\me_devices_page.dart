import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:niim_login/login_plugin/macro/color.dart';
import 'package:niim_login/login_plugin/utils/image_utils.dart';
import 'package:text/application.dart';
import 'package:text/connect/machine_alias_manager.dart';
import 'package:text/macro/constant.dart';
import 'package:text/pages/industry_template/home/<USER>/hard_ware_serise_model.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/hardware_manager.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';

import '../../utils/cachedImageUtil.dart';

class MeDevice {
  String deviceImage;
  String title;
  HardwareModelState connectStatus;
  String seriesName;

  MeDevice(this.deviceImage, this.title, this.connectStatus, this.seriesName);
}

class MeDevicesPage extends StatefulWidget {
  MeDevicesPage({Key? key}) : super(key: key);

  @override
  _MeDevicesPageState createState() => _MeDevicesPageState();
}

class _MeDevicesPageState extends State<MeDevicesPage> with PageVisibilityObserver {
  MeDevice? meDevice = null;
  MeDevice? etagDevice = null;
  MeDevice? c1Device = null;
  var isWifi = 0;
  bool hasPrintUpdateNotify = Application.hasPrintUpdateNotify;

  var isShowPage = false;
  DateTime? lastClickTime = null;

  @override
  void initState() {
    super.initState();
    isShowPage = true;
    _initDevice();
    NiimbotEventBus.getDefault().register(this, (data) async {
      if (data is Map && data.containsKey("printerConnectState")) {
        _initDevice();
      }
      if (data is Map && data.containsKey("printUpdateNotify")) {
        if (mounted) {
          setState(() {
            hasPrintUpdateNotify = Application.hasPrintUpdateNotify;
          });
        }
      }
      if (data is Map && data.containsKey("userInfo")) {
        Timer(Duration(milliseconds: 1000), () {
          isShowPage = false;
          if (mounted) {
            setState(() {});
          }
        });
      }
      if (data == "modifyMachineAlias") {
        _initDevice();
      }
    });
  }

  @override
  void onPageShow() {
    setState(() {});
    Timer(Duration(milliseconds: 100), () {
      isShowPage = true;
      setState(() {});
    });
  }

  @override
  void onPageHide() {
    super.onPageHide();
  }

  @override
  void dispose() {
    PageVisibilityBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    PageVisibilityBinding.instance.addObserver(this, ModalRoute.of(context) as Route);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: ThemeColor.background,
      child: Column(
        children: [_topWidget(), meDevice != null && isShowPage ? _DevicesList() : SizedBox()],
      ),
    );
  }

  _topWidget() {
    return AppBar(
      centerTitle: true,
      backgroundColor: ThemeColor.background,
      elevation: 0,
      leading: InkWell(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 15),
            child: Image(
              image: ImageUtils.getAssetImage('pageback'),
              matchTextDirection: true,
            ),
          ),
          onTap: () {
            Navigator.of(context).pop();
          }),
      title: Text(
        intlanguage('app00662', '我的设备'),
        style: TextStyle(
          fontSize: 17,
          color: ThemeColor.title,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  _DevicesList() {
    return Expanded(
      child: Container(
          child: ListView(
        children: [
          _deviceItem(meDevice!, () {
            CustomNavigation.gotoPage("toBluetoothConnectEvent", {}).timeout(Duration(microseconds: 1), onTimeout: () {
              isShowPage = true;
              setState(() {});
            });
          }),
          etagDevice == null
              ? Container()
              : _specialDeviceItem(etagDevice!, () {
                  ToNativeMethodChannel().toEtagPage().timeout(Duration(microseconds: 1), onTimeout: () {
                    if (Application.user != null) {
                      isShowPage = false;
                      setState(() {});
                    }
                  });
                }),
          c1Device == null
              ? Container()
              : _specialDeviceItem(c1Device!, () async {
                  ToNativeMethodChannel.toC1HomePage().timeout(Duration(microseconds: 1), onTimeout: () {
                    if (Application.user != null) {
                      isShowPage = false;
                      setState(() {});
                    }
                  });
                })
        ],
      )),
    );
  }

  _deviceItem(MeDevice meDevice, Function function) {
    String printerName = meDevice.seriesName.split(' ').first;
    if(Application.isChineseLanguage) {
      if(Application.isSimpleChineseLanguage) {
        if(printerName == "一小台H1") {
          printerName = "H1";
        }
        else if(printerName == "一小台H1S") {
          printerName = "H1S";
        }
      }
      else {
        if(printerName == "一小薹H1") {
          printerName = "H1";
        }
        else if(printerName == "一小薹H1S") {
          printerName = "H1S";
        }
      }
    }
    return GestureDetector(
      onTap: () {
        if(lastClickTime == null || DateTime.now().difference(lastClickTime!) > Duration(milliseconds: 500)) {
          lastClickTime = DateTime.now();
          function.call();
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12), // 设置圆角半径
          color: ThemeColor.COLOR_FAFAFA, // 设置背景颜色
        ),
        margin: const EdgeInsets.fromLTRB(16, 8, 16, 8),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              margin: EdgeInsets.fromLTRB(8, 11, 6, 11),
              width: 60,
              height: 60,
              child: CacheImageUtil().netCacheImage(
                width: 66,
                height: 66,
                imageUrl: meDevice.deviceImage ?? '',
                fit: BoxFit.cover,
                errorWidget: Image.asset(
                  ImageUtils.getImgPath('printerImage/$printerName'),
                  width: 66,
                  height: 66,
                  errorBuilder: (_, __, ___) => Image.asset(
                    ImageUtils.getImgPath('printerImage/$printerName'),
                    width: 66,
                    height: 66,
                    errorBuilder: (_, __, ___) => ThemeWidget.placeholder(),
                  ),
                ),
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    meDevice.title,
                    style: TextStyle(fontSize: 15, color: ThemeColor.title, fontWeight: FontWeight.w600),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Row(
                    children: [
                      meDevice.connectStatus == HardwareModelState.using
                          ? SvgIcon(
                              isWifi == 1 ? 'assets/images/wifi.svg' : 'assets/images/bluetooth.svg',
                              color: ThemeColor.COLOR_17CB7B,
                              fit: BoxFit.contain,
                              width: 16,
                              height: 16,
                            )
                          : Container(),
                      meDevice.connectStatus != HardwareModelState.defaultHardware
                          ? Text(
                              meDevice.connectStatus == HardwareModelState.using
                                  ? intlanguage("app00198", "已连接")
                                  : intlanguage("app00190", "未连接"),
                              style: TextStyle(fontSize: 13, color: ThemeColor.subtitle, fontWeight: FontWeight.w400),
                            )
                          : SizedBox(),
                    ],
                  )
                ],
              ),
            ),
            hasPrintUpdateNotify
                ? Container(
                    width: 8,
                    height: 8,
                    decoration:
                        const BoxDecoration(color: KColor.RED, borderRadius: BorderRadius.all(Radius.circular(6))),
                  )
                : Container(),
            Container(
              margin: EdgeInsetsDirectional.only(start: 4, end: 20),
              child: Image.asset(
                ImageUtils.getImgPath("profile/arrow_right"),
                width: 16,
                height: 16,
                matchTextDirection: true,
              ),
            )
          ],
        ),
      ),
    );
  }

  _specialDeviceItem(MeDevice meDevice, Function function) {
    String printerName = meDevice.seriesName.split(' ').first;
    return GestureDetector(
      onTap: () {
        function.call();
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12), // 设置圆角半径
          color: ThemeColor.COLOR_FAFAFA, // 设置背景颜色
        ),
        margin: const EdgeInsets.fromLTRB(16, 8, 16, 8),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              margin: EdgeInsets.fromLTRB(8, 11, 6, 11),
              width: 60,
              height: 60,
              child: CacheImageUtil().netCacheImage(
                width: 66,
                height: 66,
                imageUrl: meDevice.deviceImage ?? '',
                fit: BoxFit.cover,
                errorWidget: Image.asset(
                  ImageUtils.getImgPath('printerImage/$printerName'),
                  width: 66,
                  height: 66,
                  errorBuilder: (_, __, ___) => Image.asset(
                    ImageUtils.getImgPath('printerImage/$printerName'),
                    width: 66,
                    height: 66,
                    errorBuilder: (_, __, ___) => ThemeWidget.placeholder(),
                  ),
                ),
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  meDevice.title,
                  style: TextStyle(fontSize: 15, color: ThemeColor.title, fontWeight: FontWeight.w600),
                ),
              ],
            ),
            const Spacer(),
            Container(
              margin: EdgeInsetsDirectional.only(end: 20),
              child: Image.asset(
                ImageUtils.getImgPath("profile/arrow_right"),
                width: 16,
                height: 16,
                matchTextDirection: true,
              ),
            ),
          ],
        ),
      ),
    );
  }

  _initDevice() async {
    HardWareManager.instance().getPrinterSeriesInfo().then((List<HardWareSeriesModel>? hardwareSeriseList) async {
      var device = hardwareSeriseList!.firstWhereOrNull((element) =>
          element.hardwareNameStr!.split(",").contains('ET10') || element.name!.split(",").contains('电子价签'));
      if (device != null)
        etagDevice =
            MeDevice(device.image ?? "", device.name ?? "", HardwareModelState.defaultHardware, device.name ?? "");

      var c1 = hardwareSeriseList!.firstWhereOrNull(
          (element) => element.hardwareNameStr!.split(",").contains('C1') || element.name!.split(",").contains('线号机'));
      if (c1 != null) {
        c1Device = MeDevice(c1.image ?? "", c1.name ?? intlanguage("app100001426", "线号机"),
            HardwareModelState.defaultHardware, c1.hardwareNameStr ?? "C1");
      }

      Map? value = await ToNativeMethodChannel().getPrinterConnectState();
      if (value!["connected"] == 1) {
        isWifi = value['isWifi'];
        var deviceConnect =
            hardwareSeriseList.firstWhereOrNull((element) => element.id!.split(",").contains(value['seriesId']));
        String machineName = value['machineName'];
        if (machineName.startsWith('ET10')) {
          return;
        }
        if (machineName.startsWith('C1')) {
          return;
        }
        String displayName = machineName;
        String alias = MachineAliasManager().aliasMap[machineName] ?? '';
        if(alias.isNotEmpty) {
          displayName = alias;
        }
        meDevice = MeDevice(
            deviceConnect?.image ?? "", displayName, HardwareModelState.using, deviceConnect?.name ?? "");
      } else {
        String? hardwareModel = Application.sp.getString(ConstantKey.latestHardwareName);
        String defaultHardwareModel = "B21";
        String defaultHardwareName = "B21";
        String? defaultHardwarImage = "";
        var image;
        var seriesName;
        hardwareSeriseList.forEach((element) {
          element.hardwareNameStr?.split(",").forEach((t) {
            if (hardwareModel == t) {
              image = element.image;
              seriesName = element.name;
            }
            if (defaultHardwareModel == t) {
              defaultHardwarImage = element.image;
            }
          });
        });
        if ((seriesName ?? '').isEmpty) {
          hardwareModel = defaultHardwareModel;
          seriesName = defaultHardwareName;
          image = defaultHardwarImage;
        }
        meDevice = MeDevice(image ?? "", hardwareModel ?? "", HardwareModelState.historySelected, seriesName ?? "");
      }
      if (mounted) {
        setState(() {});
      }
    });
  }
}
