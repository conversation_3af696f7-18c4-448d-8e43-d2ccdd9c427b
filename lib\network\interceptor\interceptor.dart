import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/utils.dart';
import 'package:text/business/app/offline_manager.dart';
import 'package:text/pages/C1/model/c1_file_business.dart';
import 'package:text/pages/meProfile/me_profile_presenter.dart';
import 'package:text/utils/log_utils.dart';

import 'package:dio/dio.dart';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import '../../application.dart';

class AuthInterceptor extends Interceptor {
  BuildContext? context;
  String? deviceUUID;
  Dio? dio;

  AuthInterceptor(this.context);

  @override
  onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) {
    if (options.extra['needLogin'] as bool) {
      //需要登录
      if (options.extra['isShopApi'] as bool) {
        Log.d("设置商城token ${Application.mallToken}");
        options.headers['Authorization'] = Application.mallToken;
      } else {
        if (!Application.token.isCaseInsensitiveContains('bearer')) {
          options.headers['Authorization'] = "bearer ${Application.token}";
        } else {
          options.headers['Authorization'] = Application.token;
        }
      }
    } else {
      if (Application.hasToken()) {
        //不需要登录,如果有token,那么也传,不然在不需要登录页面的登录接口会有问题:比如广场详情页面点在之后,详情页刷新无法获取点赞状态
        // options.headers['Authorization'] =  Application.token;
        if (!Application.token.isCaseInsensitiveContains('bearer')) {
          options.headers['Authorization'] = "bearer ${Application.token}";
        } else {
          options.headers['Authorization'] = Application.token;
        }
      }
    }
    options.headers['niimbot-user-agent'] = Application.agent;
    options.headers['anonymous_id'] = Application.anonymousId;
    options.headers['languageCode'] = Application.currentAppLanguageType;
    options.headers['shopToken'] = Application.mallToken;
    if (MeProfilePresenter.shopSiteCode.isNotEmpty) {
      options.headers['siteCode'] = MeProfilePresenter.shopSiteCode;
    }
    Log.d("AuthInterceptor 请求header：${options.headers}");
    // if(Application.user != null && Application.user.countryCode != null){//服务端有返回用户地区,直接使用,没有就以用户选择的地区+当前使用的语言使用'-'分隔
    //   options.headers['languageCode'] = (Application.usedLanguage['lan'] ?? 'zh') + "-" + (Application.user.countryCode ?? 'cn');
    //   // options.headers['countryCode'] =  Application.user.countryCode ?? 'cn';
    // }else {
    //   options.headers['languageCode'] = (Application.usedLanguage['lan'] ?? 'zh') + "-" + (Application.userRegion?.toLowerCase() ?? 'cn');
    //   options.headers['countryCode'] = Application.userRegion?.toLowerCase() ?? 'cn';
    // }
    return super.onRequest(options, handler);
  }
}

class LoggingInterceptor extends Interceptor {
  late DateTime startTime;
  late DateTime endTime;

  @override
  onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) {
    startTime = DateTime.now();
    return super.onRequest(options, handler);
  }

  @override
  onResponse(
    Response response,
    ResponseInterceptorHandler handler,
  ) {
    RequestOptions options = response.requestOptions;
    endTime = DateTime.now();
    int duration = endTime.difference(startTime).inMilliseconds;
    return super.onResponse(response, handler);
  }

  @override
  onError(
    DioError err,
    ErrorInterceptorHandler handler,
  ) {
    return super.onError(err, handler);
  }
}

class ServerTimeInterceptor extends Interceptor {
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (response.requestOptions.extra["useServerTime"]) {
      Headers headers = response.headers;
      int userId = Application.user?.userId ?? 0;
      if (userId > 0 && headers["date"] != null && headers["date"]!.isNotEmpty) {
        String date = headers["date"]![0];
        DateFormat dateFormat = DateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", 'en');
        int serverTime = dateFormat.parse(date).millisecondsSinceEpoch;
        String time = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.fromMillisecondsSinceEpoch(serverTime));
        C1FileBusiness.instance.saveRequestFileListServerTime(userId, serverTime);
      }
    }
    super.onResponse(response, handler);
  }
}

class OutOfServiceInterceptor extends Interceptor {

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 判断网络状态
    if (NiimbotNetworkManager().isOutOfService()) {
      // 自定义网络异常
      handler.reject(
        DioException(
          requestOptions: options,
          error: "Niimbot Out Of Service",
          type: DioExceptionType.connectionError, // 自定义错误类型
        ),
      );
    } else {
      // handler.next(options);
      return super.onRequest(options, handler);
    }
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    try {
      final status = response.statusCode;
      Map<String, dynamic> result = json.decode(response.data);
      final code = result['code'];
      if (status == 503 && code == 503000) { //网关熔断
        NiimbotNetworkManager().notifyStatus(NiimbotNetworkType.outOfService);
      }
    }  catch (e, s) {
      debugPrint('异常信息: $e\n调用栈信息: $s');
    }
    super.onResponse(response, handler);
  }
}

/// 请求去重拦截器
class RequestDeduplicationInterceptor extends Interceptor {
  final Map<String, RequestInterceptorHandler> _pendingRequests = {};

  /// 生成请求唯一key
  String _generateKey(RequestOptions options) {
    final data = json.encode(options.data ?? {});
    final params = json.encode(options.queryParameters ?? {});
    final raw = '${options.method}_${options.path}_$params\_$data';
    return md5.convert(utf8.encode(raw)).toString();
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final key = _generateKey(options);
    options.extra['request_key'] = key;
    if (_pendingRequests.containsKey(key)) {
      options.cancelToken?.cancel();
      debugPrint("触发request cancel： ${options.path}");
      // 已有相同请求，直接返回pending的Future
      handler.reject(
        DioError(
          requestOptions: options,
          error: 'Duplicate request filtered',
          type: DioErrorType.cancel,
        ),
        true,
      );
      return;
    }
    // 标记为pending
    _pendingRequests[key] = handler;
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    final key = response.requestOptions.extra['request_key'];
    if (_pendingRequests.containsKey(key)) {
      // 移除pending状态
      _pendingRequests.remove(key);
    }
    handler.next(response);
  }

  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    final key = err.requestOptions.extra['request_key'];
    if (_pendingRequests.containsKey(key)) {
      // 移除pending状态
      _pendingRequests.remove(key);
    }
    handler.next(err);
  }
}
