/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "このアプリは、バーコードスキャン、文字認識、撮影機能でカメラへのアクセスを要求します。カメラの使用を許可しますか？";
NSBluetoothPeripheralUsageDescription = "このアプリは、プリンター接続サービスでBluetoothへのアクセスを要求します。Bluetoothの使用を許可しますか？";
NSBluetoothAlwaysUsageDescription = "このアプリは、プリンター接続サービスでBluetoothへのアクセスを要求します。Bluetoothの使用を許可しますか？";
NSContactsUsageDescription = "このアプリは、連絡先読み取りサービスで連絡先へのアクセスを要求します。連絡先の使用を許可しますか？";
NSMicrophoneUsageDescription = "このアプリは、音声認識サービスでマイクへのアクセスを要求します。マイクの使用を許可しますか？";
NSPhotoLibraryUsageDescription = "この権限は、画像素材の印刷、バーコード認識、QRコード認識、文字認識、カスタムアイコンの設定などの場面で使用されます。NIIMBOTでアルバムにアクセスできるよう、「すべての写真へのアクセスを許可」を選択してください。「写真を選択…」を使用した場合、選択されなかった写真や今後追加される写真はNIIMBOTでアクセスできません。";
NSLocationAlwaysUsageDescription = "近くのWi-Fiネットワークを便利にご利用いただくため、NIIMBOTは位置情報権限を要求します";
NSLocationWhenInUseUsageDescription = "近くのWi-Fiネットワークを便利にご利用いただくため、NIIMBOTは位置情報権限を要求します";
NSLocationAlwaysAndWhenInUseUsageDescription = "近くのWi-Fiネットワークを便利にご利用いただくため、NIIMBOTは位置情報権限を要求します";
NSSpeechRecognitionUsageDescription = "このアプリが音声認識にアクセスするには同意が必要です。音声認識の使用を許可しますか？";

"UILaunchStoryboardName" = "LaunchScreen";
