//
//  JCLabelApplicationViewController.m
//  Runner
//
//  Created by Echo Ma on 2022/7/17.
//

#import "JCLabelApplicationViewController.h"
#import "JCActivityCodeEditViewController.h"
#import "JCLabelApplicationView.h"
#import "JCLabelAppTableViewCell.h"
#import "QQLBXScanViewController.h"
#import "JCTemplateImageManager.h"
#import "NSData+JKEncrypt.h"
#import "JCFontManager.h"
#import "StyleDIY.h"
#import "Global.h"
#import "JCLabelAppModel.h"
#import "JCOtherAppAlert.h"
#import "ImageLibraryBridge.h"
#import "JCDeviceSeriesHelp.h"
#import "NBCAPMiniAppManager.h"



@interface JCLabelApplicationViewController ()<UITableViewDelegate,UITableViewDataSource>

@property(nonatomic,strong) JCLabelApplicationView *mainView;

@property(nonatomic,strong) NSMutableArray *appCategarysArr;

@property(nonatomic,assign) long long lastRequestTime;

@property(nonatomic,assign) MBProgressHUD *progressHUD;

@property(nonatomic,strong) NSMutableArray *fontSourceResoloveArr;

@property(nonatomic,strong) NSString *currentUniAppId;
@end

@implementation JCLabelApplicationViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.lsl_prefersNavigationBarHidden = YES;
}

- (void)initUI{
    
    [super initUI];
    [self.view addSubview:self.mainView];
    XYWeakSelf
    [self.mainView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(weakSelf.view);
    }];
}

- (void)dealloc{
    NSLog(@"JCLabelApplicationViewController ----------------- dealloc!!!");
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [self.mainView.contentTableView reloadData];
    JC_TrackWithparms(@"view",@"004",(@{}));
    long long currentTime = [XYTool getNowTimeTimestamp].longLongValue;
    if(self.lastRequestTime > 0 && currentTime - self.lastRequestTime > 1000 * 48 * 3600){
        [self getNiimbotApplications];
    }
}

- (JCLabelApplicationView *)mainView{
    if(!_mainView){
        
        _mainView = [[JCLabelApplicationView alloc] initWithFrame:CGRectZero];
        _mainView.contentTableView.dataSource = self;
        _mainView.contentTableView.delegate = self;
        [_mainView.contentTableView registerClass:[JCLabelAppTableViewCell class] forCellReuseIdentifier:@"JCLabelAppTableViewCell"];
        XYWeakSelf
        [_mainView setRefreshContentBlock:^(id x) {
            [weakSelf getNiimbotApplications];
        }];
    }
    return _mainView;
}

- (NSMutableArray *)appCategarysArr{
    if(!_appCategarysArr){
        _appCategarysArr = [NSMutableArray array];
    }
    return _appCategarysArr;
}

- (void)initNet{
    [super initNet];
    self.fontSourceResoloveArr = [NSMutableArray array];
    [self getNiimbotApplications];
}

- (void)getNiimbotApplications{
    NSString *appCenterPath = [NSString stringWithFormat:@"%@/%@",DocumentsPath,@"appCenter"];
    NSString *file = [NSString stringWithFormat:@"%@/appCenterInfo.text",appCenterPath];
    NSString *jsonString = [NSString stringWithContentsOfFile:file encoding:NSUTF8StringEncoding error:nil];
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    if(jsonData){
        NSArray *innerAppList = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingAllowFragments error:nil];
        if(innerAppList.count > 0){
            [self analyticAppcenterInfo:innerAppList needWriteToLocal:NO];
        }
    }
    NSString *graphqlStr = @"query getInnerAppList { getInnerAppList { categoryName appList { id name icon badge badgeColor isVip isNew needLogin type router packageName dialogTitle dialogMessage appKey appSecret} } }";
    [@{@"cachePolicy":@"2"} jc_graphQLRequestWith:graphqlStr hud:nil graphQLType:@"query" Success:^(__kindof YTKBaseRequest *request, NSDictionary *requestDic) {
        if([requestDic isKindOfClass:[NSDictionary class]]){
            [self.appCategarysArr removeAllObjects];
            NSArray *innerAppList = requestDic[@"getInnerAppList"];
            [self analyticAppcenterInfo:innerAppList needWriteToLocal:YES];
            self.lastRequestTime = [XYTool getNowTimeTimestamp].longLongValue;
        }
    } failure:^(NSString *msg, id model) {
//        [MBProgressHUD showToastWithMessageDarkColor:msg];
        if(self.appCategarysArr.count == 0){
            [self.mainView showNoDataView:YES];
        }
    }];
}

- (void)analyticAppcenterInfo:(NSArray *)innerAppList needWriteToLocal:(BOOL)needWriteToLocal{
    if((NETWORK_STATE_ERROR) && innerAppList.count == 0){
        [self.mainView showNoDataView:YES];
    }else{
        [self.mainView showNoDataView:NO];
    }
    NSMutableArray *updateNewAppIdArr = @[].mutableCopy;
    NSMutableArray *appArr = @[].mutableCopy;
    for (NSDictionary *appListInfoDic in innerAppList) {
        JCLabelAppListModel *listModel = [[JCLabelAppListModel alloc] initWithDictionary:appListInfoDic error:nil];
        NSArray *appInfosDic = appListInfoDic[@"appList"];
        NSMutableArray *appInfoArr = [NSMutableArray array];
        for (NSDictionary *appInfoDic in appInfosDic) {
            JCLabelAppModel *appModel = [[JCLabelAppModel alloc] initWithDictionary:appInfoDic error:nil];
            [appInfoArr addObject:appModel];
            if(appModel.isNew.integerValue == 1){
                [updateNewAppIdArr addObject:appModel.appId];
            }
            [appArr addObject:appModel];
        }
        listModel.appList = appInfoArr;
        [self.appCategarysArr addObject:listModel];
    }
    [XYCenter sharedInstance].appArr = appArr;
    if(updateNewAppIdArr.count > 0){
        NSString *recodedNewAppId = [[NSUserDefaults standardUserDefaults] objectForKey:@"tabbarUpdateNewAppId"];
        if(STR_IS_NIL(recodedNewAppId)){
            recodedNewAppId = [updateNewAppIdArr componentsJoinedByString:@","];
        }else{
            NSMutableArray *recodedNewAppIdsArr = [recodedNewAppId componentsSeparatedByString:@","].mutableCopy;
            NSMutableArray *unRecodedIdArr = [NSMutableArray array];
            for(NSString *updateAppId in updateNewAppIdArr){
                if(![recodedNewAppIdsArr containsObject:updateAppId]){
                    [unRecodedIdArr addObject:updateAppId];
                }
            }
            [recodedNewAppIdsArr addObjectsFromArray:unRecodedIdArr];
            recodedNewAppId = [recodedNewAppIdsArr componentsJoinedByString:@","];
        }
        [[NSUserDefaults standardUserDefaults] setObject:recodedNewAppId forKey:@"tabbarUpdateNewAppId"];
    }
    if(needWriteToLocal){
        NSString *appCenterPath = [NSString stringWithFormat:@"%@/%@",DocumentsPath,@"appCenter"];
        NSLog(@"============%@",appCenterPath);
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:innerAppList options:NSJSONWritingPrettyPrinted error:nil];
        NSString *appCenterInfoString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
        [XYCenter writeToFile:appCenterPath fileName:[NSString stringWithFormat:@"appCenterInfo.text"] data:appCenterInfoString];
    }
    [self.mainView.contentTableView reloadData];
}
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    NSInteger rowsCount = self.appCategarysArr.count;
    return rowsCount;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    return 15;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section{
    UIView *footView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 15)];
    return footView;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    JCLabelAppListModel *appListModel = [self.appCategarysArr safeObjectAtIndex:indexPath.row];
    NSInteger appCount = appListModel.appList.count;
    NSInteger linesNumber = 1;
    if(appCount%4 == 0){
        linesNumber = appCount/4;
    }else{
        linesNumber = appCount/4 + 1;
    }
    float itemHeight = XY_AutoWidth(65) + 10 + 15;
    for (JCLabelAppModel *appModel in appListModel.appList) {
        float minWidth = appModel.isNew.integerValue == 1?(XY_AutoWidth(65) - 7):XY_AutoWidth(65);
        float height1 = [@"精" jk_sizeWithFont:MY_FONT_Regular(12) constrainedToWidth:minWidth].height;
        float height = [XY_LANGUAGE_TITLE_NAMED(appModel.name, @"") jk_sizeWithFont:MY_FONT_Regular(12) constrainedToWidth:minWidth].height;
        if(height > height1 + 3){
            itemHeight = XY_AutoWidth(65) + 15 + 20;
        }
    }
    return itemHeight * linesNumber + 5 * (linesNumber - 1) + 46;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    JCLabelAppListModel *appListModel = [self.appCategarysArr safeObjectAtIndex:indexPath.row];
    JCLabelAppTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"JCLabelAppTableViewCell"];
    cell.indexPath = indexPath;
    [cell setAppListModel:appListModel];
    [cell setClickBlock:^(NSNumber *sectionIndex, NSNumber *rowIndex) {
        [self selectAppEventWith:sectionIndex.integerValue rowIndex:rowIndex.integerValue];
    }];
    if(indexPath.row == self.appCategarysArr.count - 1){
        [cell showBottomLine:NO];
    }else{
        [cell showBottomLine:YES];
    }
    return cell;
}

- (void)selectAppEventWith:(NSInteger)sectionIndex rowIndex:(NSInteger)rowIndex{
    
    JCLabelAppListModel *appListModel = [self.appCategarysArr safeObjectAtIndex:sectionIndex];
    JCLabelAppModel *appModel = [appListModel.appList safeObjectAtIndex:rowIndex];
    if(appModel.needLogin.integerValue == 1){
        BOOL originIsLogin = xy_isLogin;
        
        [[JCLoginManager sharedInstance] checkLogin:^{
                    
        } viewController:self loginSuccessBlock:^{
            if (originIsLogin) {
                [self jumpToObjectVC:appModel];
            } else {
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self jumpToObjectVC:appModel];
                });
            }
        }];
    }else{
        [self jumpToObjectVC:appModel];
    }
    
    JC_TrackWithparms(@"click",@"004_007",(@{@"b_name":XY_LANGUAGE_TITLE_NAMED(appModel.name, @""),@"b_category":XY_LANGUAGE_TITLE_NAMED(appListModel.categoryName,@"")}));
}

- (void)jumpToObjectVC:(JCLabelAppModel*)appModel{
    if(!STR_IS_NIL(appModel.badge) && [appModel.badge isEqualToString:XY_LANGUAGE_TITLE_NAMED(@"app100000241", @"")]){
        if(!m_storeTrialTimeIsValite){
            [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01439", @"提醒") message:XY_LANGUAGE_TITLE_NAMED(@"app100000082", @"限时免费体验已结束，为保证您的正常使用，请更新APP到最新版本。") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000081", @"不用了") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00487", @"更新") cancelBlock:nil sureBlock:^{
                [[UIApplication sharedApplication] openURL:XY_URLWithString(app_download_Url) options:@{} completionHandler:^(BOOL success) {
                    
                }];
            } alertType:3];
            return;
        }
    }
    if(appModel.type.integerValue == 0){
        [JCToNativeRouteHelp toNativePageWith:appModel.router fromType:JC_AppCenter eventTitle:@""];
    }else if(appModel.type.integerValue == 1){
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        JCActivityCodeEditViewController *vc = [[JCActivityCodeEditViewController alloc] initWithUrl:appModel.router];
        [self.navigationController pushViewController:vc animated:YES];
    }else if (appModel.type.integerValue == 4 && [appModel.router hasPrefix:@"__CAP"]) {
        // Capacitor 小程序
        if (NETWORK_STATE_ERROR) {
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        [[NBCAPMiniAppManager sharedInstance] openMiniApp:appModel keepAlive:YES subpath:nil extra:nil dataReceived:nil];
    }else if(appModel.type.integerValue == 3){
        [JCToNativeRouteHelp jumpToOtherAppWithAppModel:appModel];
    }else{
        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"", @"暂不支持此应用，我们正在努力优化") cancelButtonTitle:@"" sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707",@"我知道了") cancelBlock:^{
        } sureBlock:^{
        }];
        return;
    }
    NSString *recodedNewAppId = [[NSUserDefaults standardUserDefaults] objectForKey:@"appCenterUpdateNewAppId"];
    NSMutableArray *recodedNewAppIdArr = [recodedNewAppId componentsSeparatedByString:@","].mutableCopy;
    if(![recodedNewAppIdArr containsObject:appModel.appId]){
        if(recodedNewAppIdArr == nil){
            recodedNewAppIdArr = [NSMutableArray array];
        }
        [recodedNewAppIdArr addObject:appModel.appId];
        recodedNewAppId = [recodedNewAppIdArr componentsJoinedByString:@","];
        [[NSUserDefaults standardUserDefaults] setObject:recodedNewAppId forKey:@"appCenterUpdateNewAppId"];
    }
}

@end
