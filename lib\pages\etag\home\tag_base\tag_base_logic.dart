import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:niimbot_excel/models/data_bind_modify.dart';
import 'package:niimbot_excel/models/data_source.dart';
import 'package:niimbot_excel/models/interface.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/escape_utils.dart';
import 'package:niimbot_flutter_canvas/src/model/excel_bind_info.dart';
import 'package:niimbot_flutter_canvas/src/model/template_data.dart' as CanvasTemplateData;
import 'package:niimbot_flutter_canvas/src/utils/template_utils.dart';
import 'package:niimbot_flutter_canvas/src/widgets/good_lib/good_field_manager.dart';
import 'package:text/application.dart';
import 'package:text/macro/constant.dart';
import 'package:text/network/dio_utils.dart';
import 'package:text/network/entity/user.dart';
import 'package:text/network/http_api.dart';
import 'package:text/pages/etag/goods_select/select_goods_home.dart';
import 'package:text/pages/etag/home/<USER>/goods_change_widget.dart';
import 'package:text/pages/etag/home/<USER>/sync_page.dart';
import 'package:text/pages/etag/home/<USER>/templates_preview_page.dart';
import 'package:text/pages/etag/home/<USER>/etag_card_statuss_model.dart';
import 'package:text/pages/etag/home/<USER>/etag_connect_status_model.dart';
import 'package:text/pages/etag/home/<USER>/etag_template_size_model.dart';
import 'package:text/pages/etag/home/<USER>/etag_template_sort_model.dart';
import 'package:text/pages/etag/home/<USER>/screen_info_model.dart';
import 'package:text/pages/etag/template_select/template_select_widget.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_category_list_model.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/tools/gray_config_manager.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/utils/toast_util.dart';
import 'package:text/utils/vip_helper.dart';
import 'package:uuid/uuid.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import 'tag_base_state.dart';

class TagBaseLogic extends GetxController {
  final TagBaseState state = TagBaseState();
  BuildContext? context;

  @override
  void onInit() {
    super.onInit();
    state.isInit = true;
    Timer(Duration(seconds: 2), () {
      state.isInit = false;
    });
    GoodFieldManager().updateGoodFields();
    _tagBaseStateListener();
    _tagBaseStateInit();
    _refreshPreviewWidget();
    etagCardListener(true);
  }

  setContext(context) {
    this.context = context;
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  Future<void> onClose() async {
    super.onClose();
    etagCardListener(false);
    _etagDisConnect();
    _saveLocalMsg();

    NiimbotEventBus.getDefault().unregister(this);
  }

  _tagBaseStateInit() {
    ToNativeMethodChannel.sharedInstance().getETagConnect();
    var sort = Application.sp.getString(ConstantKey.latestEtagTemplateSort + Application.currentAppLanguageType);
    if (sort != null) {
      state.etagTemplateSortSelect = EtagTemplateSortModel.fromJson(jsonDecode(sort));
    }

    var size = Application.sp.getString(ConstantKey.latestEtagTemplateSize + Application.currentAppLanguageType);
    if (size != null) {
      state.etagTemplateSizeSelect = EtagTemplateSizeModel.fromJson(jsonDecode(size));
    }
    var userId = Application.user?.uid ?? "";
    var localTemplate =
        Application.sp.getString(ConstantKey.latestEtagTemplate + Application.currentAppLanguageType + userId);
    if (localTemplate != null) {
      state.goodsTemplate = jsonDecode(localTemplate);
    }
  }

  _tagBaseStateListener() {
    NiimbotEventBus.getDefault().register(this, (data) {
      if (data is Map && data.containsKey('eTagStatus')) {
        _eTagStatusChange(data);
      }
      if (data is Map &&
          data.containsKey('eTagStatusWriterListener') &&
          state.goodsMixList.isNotEmpty &&
          state.baseConnectState == BaseState.success) {
        _eTagCardStatus(data);
      }
      // 刷新模版信息
      if (data is Map && data.containsKey("saveSuccessData")) {
        print(data["saveSuccessData"]);
        state.goodsTemplate = jsonDecode(data["saveSuccessData"]);
        _goodsMixTemplate();
      }
      //刷新商品信息
      if (data is Map && data.containsKey("etagRowData")) {
        print(data["etagRowData"]);
        List<List<String>> goods = data["etagRowData"];
        List<Map<String, dynamic>> goodMapList = [];
        for (int i = 0; i < goods.length; i++) {
          List<String> fields = goods[i];
          int j = 0;
          Map<String, dynamic> goodMap = {};
          for (var fieldName in CanvasTemplateData.TemplateData.goodsInfnFieldName()) {
            goodMap[fieldName] = fields[j++];
          }
          goodMapList.add(goodMap);
        }
        state.goodsList = goodMapList;
        _goodsMixTemplate();
      }
      if (data is Map && data.containsKey("updateGoodsField")) {
        _goodsMixTemplate();
      }
    });
  }

  ///保存本地数据信息
  _saveLocalMsg() {
    Application.sp.setString(ConstantKey.latestEtagTemplateSort + Application.currentAppLanguageType,
        jsonEncode(state.etagTemplateSortSelect?.toJson()));
    Application.sp.setString(ConstantKey.latestEtagTemplateSize + Application.currentAppLanguageType,
        jsonEncode(state.etagTemplateSizeSelect?.toJson()));
    var userId = Application.user?.uid ?? "";
    Application.sp.setString(
        ConstantKey.latestEtagTemplate + Application.currentAppLanguageType + userId, jsonEncode(state.goodsTemplate));
  }

  ///价签机连接状态
  _eTagStatusChange(Map data) {
    state.device.clear();
    switch (data['type']) {
      case 0:
        state.baseConnectState = BaseState.success;
        state.device.toModel(data);
        break;
      case 1:
        if (state.isInit) {
          return;
        }
        state.baseConnectState = BaseState.fail;
        state.lastConnectStatus = BaseState.fail;
        showToast(msg: intlanguage('app100001005', '基座连接失败'));
        break;
      case 2:
        if (state.isInit) {
          return;
        }
        state.baseConnectState = BaseState.disConnect;
        state.lastConnectStatus = BaseState.disConnect;

        if (state.isClosePageShow) {
          state.isClosePageShow = false;
          if (state.isSyncPageShow) {
            showCustomDialog(context!, "", intlanguage('app100001056', '蓝牙已连接断开，请确认基座是否需要充电'),
                justSureButton: true,
                dismissOutSideTouch: false,
                contentTextStyle: TextStyle(
                  fontSize: 16,
                  color: ThemeColor.mainTitle,
                  fontWeight: FontWeight.w600,
                ),
                rightFunStr: intlanguage('app01584', '关闭'),
                rightTextColor: ThemeColor.COLOR_537FB7, rightFunCall: () {
              state.goodsIndex = 0;
              state.connectIndex = 0;
              state.connectStatusModel.clear();
              state.writeGoodsMixList.clear();
              state.writeGoodsMixList.addAll(state.goodsMixList);
              saveEtagScreenInfo();
            });
          } else {
            showToast(msg: intlanguage('app100001004', '基座已断开'));
          }
        } else {
          if (!state.isSyncPageShow) {
            showToast(msg: intlanguage('app100001004', '基座已断开'));
          }
        }
        break;
      case 3:
        state.baseConnectState = BaseState.connecting;
        break;
      default:
        break;
    }
    update(['eTagStatus']);
    WakelockPlus.enabled.then((value) {
      if (value) {
        WakelockPlus.disable();
      }
    });
  }

  ///价签状态
  _eTagCardStatus(Map data) {
    state.cardStatus.clear();
    var deviceStatus = EtagCardStatussModel.fromJson(data["heartBeat"]);
    for (int i = 0; i < deviceStatus.completeStatus!.length; i++) {
      var screen = deviceStatus.screenInfo![i];
      state.screenStatus[(i + 1).toString()] = screen[0];
      if (deviceStatus.screenPullStatus![i] == 0) {
        state.cardStatus.insert(i, EtagCardState.empty);
      } else if (deviceStatus.exceptionStatus![i] == 1) {
        state.cardStatus.insert(i, EtagCardState.error);
      } else if (deviceStatus.completeStatus![i] == 1) {
        state.cardStatus.insert(i, EtagCardState.complete);
      } else if (deviceStatus.screenWorkStatus![i] == 1) {
        state.cardStatus.insert(i, EtagCardState.setting);
      } else if (!sizeCheck(state.goodsMixList[0]["width"].toString(), screen[0])) {
        state.cardStatus.insert(i, EtagCardState.sizeError);
      } else {
        state.cardStatus.insert(i, EtagCardState.size);
      }
    }
    update(["progress", "cardStatus"]);
    _setProgress();
    if (!state.isStart) {
      update(["connectState"]);
    }
    _setToastStatus();
  }

  sizeCheck(String goodsMixListWidth, screen) {
    switch (goodsMixListWidth) {
      case "66.9":
        return screen == 296;
      case "84.8":
        return screen == 400;
      default:
        return false;
    }
  }

  _setProgress() {
    state.connectStatusModel.forEach((element) {
      if (EtagCardState.complete == state.cardStatus[element.cardIndex]) {
        element.cardType = EtagCardState.complete;
      }
    });
    var clearSize = 0;
    state.goodsMixList.forEach((element) {
      for (int j = 0; j < state.connectStatusModel.length; j++) {
        if (state.connectStatusModel[j].connectId == element['goodsId'] &&
            state.connectStatusModel[j].cardType == EtagCardState.complete) {
          clearSize += 1;
        }
      }
    });
    state.progress = clearSize + state.lastProgress;
    var stateBool = false;
    state.cardStatus.forEach((element) {
      if (element == EtagCardState.setting) {
        stateBool = true;
        return;
      }
    });

    if (state.progress != state.goodsMixList.length) {
      if (!stateBool) {
        state.connectState = CardConnectState.pause;
      } else {
        state.connectState = CardConnectState.normal;
      }
    } else {
      state.connectState = CardConnectState.complete;
    }
  }

  _setToastStatus() {
    state.cardStatus.forEach((element) {
      if (element == EtagCardState.empty) {
        int index = state.cardStatus.indexOf(element);
        state.previewCards[index + 1] = null;
      }
    });

    if (state.connectState == CardConnectState.complete) {
      state.toastState = SyncToastState.success;
    } else if (state.progress > 0 && state.connectState == CardConnectState.pause) {
      var isEmpty = true;
      state.cardStatus.forEach((element) {
        if (element == EtagCardState.complete) {
          isEmpty = false;
        }
      });
      if (isEmpty) {
        state.toastState = SyncToastState.empty;
      } else {
        if (state.toastState != SyncToastState.next &&
            state.connectState != CardConnectState.complete &&
            state.isSyncPageShow) {
          showCustomDialog(context!, "", intlanguage('app100000983', '请将当前价签全部取出，并放入下一批价签同步'),
              justSureButton: true,
              contentTextStyle: TextStyle(
                fontSize: 16,
                color: ThemeColor.mainTitle,
                fontWeight: FontWeight.w600,
              ),
              rightFunStr: intlanguage('app00707', '我知道了'),
              rightTextColor: ThemeColor.COLOR_537FB7,
              rightFunCall: () {});
        }

        state.toastState = SyncToastState.next;
      }
    } else {
      var isSetting = false;
      state.cardStatus.forEach((element) {
        if (element == EtagCardState.setting || element == EtagCardState.size) {
          isSetting = true;
        }
      });
      if (isSetting) {
        state.toastState = SyncToastState.setting;
      } else {
        var isempty = true;
        state.cardStatus.forEach((element) {
          if (element != EtagCardState.empty) {
            isempty = false;
          }
        });
        if (isempty) {
          state.toastState = SyncToastState.empty;
        } else {
          state.toastState = SyncToastState.sizeError;
        }
      }
    }
  }

  ///商品数据选择
  goodsSelectPage(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: false,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      builder: (BuildContext context) {
        return SelectGoodsHome();
      },
    ).then((value) {
      if (value != null) {
        if (value != null && value is List<Map<String, dynamic>>) {
          state.goodsList = value;
        }
        state.previewIndex = 0;
        _refreshPreviewWidget();
        state.connectIndex = 0;
        state.goodsIndex = 0;
        update(["eTagStatus"]);
      }
    });
  }

  ///模板数据选择
  tmplateSelectPage(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      builder: (BuildContext context) {
        return TemplateSelectWidget();
      },
    ).then((value) {
      if (value != null) {
        state.writeGoodsMixList.clear();
        _refreshPreviewWidget();
        state.connectIndex = 0;
        state.goodsIndex = 0;
        update(["eTagStatus"]);
      }
    });
  }

  ///同步界面
  syncPage(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: false,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      builder: (BuildContext context) {
        return SyncPage();
      },
    ).then((value) {});
  }

  ///修改商品信息
  changeGoodsMsg(BuildContext context) async {
    await GoodFieldManager().getGoodFields();
    update(["preview"]);
    var json = jsonEncode(state.goodsList[state.previewIndex]);
    Map<String, dynamic> map = jsonDecode(json);
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: false,
      barrierColor: Colors.black.withOpacity(0.35),
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      builder: (BuildContext context) {
        return GoodsChangeWidget(
          goods: map,
          templateJson: jsonDecode(jsonEncode(state.goodsTemplate)),
        );
      },
    ).then((value) {
      if (value != null) {
        // state.alonePreviewLoading = true;
        updateGoodsMap(value, context);
      } else {}
    });
  }

  bool isNewCanvasBoard() {
    return GrayConfigManager().isNewDrawingBoard();
  }

  ///模板批量预览
  tmplatesPreview(BuildContext context) async {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      builder: (BuildContext context) {
        return TemplatesPreviewPage();
      },
    ).then((value) {
      update(["preview"]);
    });
  }

  ///刷新预览视图
  _refreshPreviewWidget() {
    _goodsMixTemplate();
  }

  ///单商品数据融合
  aloneGoodsMixTemplate(int index) async {
    state.alonePreviewLoading = true;
    update(["preview"]);
    Map<String, dynamic> data = {
      'goodsMixList': jsonEncode([state.goodsMixList[index]])
    };
    state.alonePreview = await ToNativeMethodChannel.sharedInstance().goodsMixList(data);
    state.alonePreviewLoading = false;
    update(["preview", "eTagStatus"]);
  }

  ///批量商品数据融合
  Future allGoodsMixTemplate() async {
    Map<String, dynamic> data = {'goodsMixList': jsonEncode(state.goodsMixList)};
    state.previewList = await ToNativeMethodChannel.sharedInstance().goodsMixListAll(data);
    return state.previewList;
  }

  ///批量商品数据融合带分页
  Future allGoodsMixTemplateByPageIndex(int startIndex, int endIndex) async {
    endIndex = min(endIndex, state.goodsMixList.length);
    List<Map<String, dynamic>> sublist = state.goodsMixList.sublist(startIndex, endIndex);
    Map<String, dynamic> data = {'goodsMixList': jsonEncode(sublist)};
    List<dynamic> previewImagesByPage = await ToNativeMethodChannel.sharedInstance().goodsMixListAll(data);
    if (startIndex == 0) {
      state.previewList.clear();
      state.previewList.addAll(previewImagesByPage);
    } else {
      state.previewList.addAll(previewImagesByPage);
    }
    return state.previewList;
  }

  ///商品数据融合模板
  _goodsMixTemplate() async {
    state.goodsMixList.clear();
    if(state.goodsTemplate.isEmpty){
      return;
    }
    if (state.goodsList.isEmpty) {
      // var templateMap = jsonEncode(state.goodsTemplate);
      // Map<String, dynamic> template1 = jsonDecode(templateMap);
      // var templateModel = TemplateData.fromJson(template1);
      // state.goodsMixList.add(templateModel.rawData);
      await _processGoodTemplate(state.goodsTemplate, state.goodsList);
      var templateMap = jsonEncode(state.goodsTemplate);
      Map<String, dynamic> tempTemplate = jsonDecode(templateMap);
      tempTemplate['currentPageIndex'] = 0;
      tempTemplate['currentPage'] = 1;
      if (tempTemplate['bindInfo'] != null) {
        tempTemplate['bindInfo']['page'] = 1;
      } else {
        //处理异常中断流程 保持和线上的异常逻辑一致
        return;
      }
      state.goodsMixList.add(tempTemplate);
    } else {
      await _processGoodTemplate(state.goodsTemplate, state.goodsList);
      for (var i = 0; i < state.goodsList.length; i++) {
        //copy一份进行修改
        var templateMap = jsonEncode(state.goodsTemplate);
        Map<String, dynamic> tempTemplate = jsonDecode(templateMap);
        tempTemplate['currentPageIndex'] = i;
        tempTemplate['currentPage'] = i + 1;
        if (tempTemplate['bindInfo'] != null) {
          tempTemplate['bindInfo']['page'] = i + 1;
        }
        tempTemplate["goodsId"] = state.goodsList[i]['id'];
        state.goodsMixList.add(tempTemplate);
      }
      // state.goodsList.forEach((element) {
      //   var templateMap = jsonEncode(state.goodsTemplate);
      //   Map<String, dynamic> template1 = jsonDecode(templateMap);
      //   var templateModel = TemplateData.fromJson(template1);
      //   state.goodsMixList.add(templateModel.goodsMix(element));
      // });
    }

    state.writeGoodsMixList.clear();
    state.writeGoodsMixList.addAll(state.goodsMixList);
    aloneGoodsMixTemplate(state.previewIndex);
  }

  ///将模板转换为可以兼容旧画板的商品库模板结构
  _processGoodTemplate(Map<String, dynamic> goodTemplate, List<Map<String, dynamic>> goodsList) async {
    try {
      List<String> goodIds = goodsList.map((e) => e['id'].toString()).toList();
      var templateModel = TemplateData.fromJson(goodTemplate);
      var rawDataList = templateModel.rawData["elements"] as List<dynamic>;
      // 构建商品库动态数据源  List<DataSource> dataSource
      List<String> headers = CanvasTemplateData.TemplateData.goodsInfnFieldName();
      DataSource dataSource = DataSource(
          type: DataSourceType.commodity,
          uri: "",
          name: "",
          headers: {},
          hash: Uuid().v4().replaceAll("-", ""),
          params: {"ids": goodIds});
      // TemplateModify modify = TemplateModify();
      //选取电子价签模板返回的模板结构--也就是服务端返回的模板结构可能存在dataSourceModifies字段
      var dataSourceModifies = templateModel.rawData["dataSourceModifies"];
      var template_modify = templateModel.rawData["modify"];
      if (dataSourceModifies != null && dataSourceModifies is Map && dataSourceModifies.isNotEmpty) {
        template_modify = dataSourceModifies;
      }
      if (template_modify == null || !(template_modify is Map)) {
        template_modify = {};
      }
      TemplateModify modify = TemplateModifyExtension.fromJson(Map<String, dynamic>.from(template_modify));
      ExcelPageInfo bindInfo = ExcelPageInfo(page: 1, total: goodsList.length);
      processJsonElementWithFieldName(rawDataList, headers, modify);
      templateModel.rawData["modify"] = modify.toJson();
      templateModel.rawData["dataSource"] = [dataSource.toJson()];
      templateModel.rawData["bindInfo"] = bindInfo.toJson();
      Map<String, dynamic> newGoodTemplateJson = templateModel.rawData;
      Map<String, dynamic> compactGoodTemplateJson =
          await TemplateUtils.transformGoodTemplateToCompactOldTemplateJsonData(newGoodTemplateJson, goodsList,
              getRealContentTitle: true);
      state.goodsTemplate = compactGoodTemplateJson;
    } catch (e, s) {
      debugPrint('调用栈信息:\n $s');
    }
  }

  processJsonElementWithFieldName(List<dynamic> jsonElements, List<String> headers, TemplateModify modify) {
    jsonElements.forEach((element) {
      var elementType = element["type"];
      if (elementType == "table") {
        var cells = element["cells"] as List<dynamic>;
        var combineCells = element["combineCells"] as List<dynamic>;
        processJsonElementWithFieldName(cells, headers, modify);
        processJsonElementWithFieldName(combineCells, headers, modify);
      } else {
        if (elementType == "date") {
          element["value"] = element["value"] + "#";
        }
        var fieldName = element["fieldName"];
        var value = element["value"];
        int? fieldIndex = -1;
        if (element["dataBind"] == null && fieldName != null && fieldName != "") {
          fieldIndex = headers.indexOf(fieldName);
          if (fieldIndex >= 0) {
            //fieldName转换为列信息A0
            element["value"] = EscapeUtils.getCellPlaceHolder(fieldIndex);
            //添加元素绑定信息List<String> dataBind
            element["dataBind"] = ["", "commodity"];
            //针对老模板处理contentTitle属性，不为空的话 修改TemplateModify modify结构 添加表头信息的显示;
            if (!modify.containsKey(element["id"]) || modify[element["id"]]!.isEmpty) {
              var contentTitle = element['contentTitle'];
              if (contentTitle != null && contentTitle != "") {
                modify[element["id"]] = Map<String, DataBindModify>();
                DataBindModify globalModify =
                    modify[element["id"]]!.putIfAbsent("0", () => DataBindModify(delimiter: "："));
                globalModify.useTitle = true;
              }
            }
          }
        } else if (value != null && value.startsWith('\${0⊙') && value.endsWith('}')) {
          String index = value.replaceAll('\${0⊙', '').replaceAll('}', '');
          fieldIndex = int.tryParse(index);
          if (fieldIndex != null && fieldIndex >= 0) {
            //fieldName转换为列信息A0
            element["value"] = EscapeUtils.getCellPlaceHolder(fieldIndex);
            //添加元素绑定信息List<String> dataBind
            element["dataBind"] = ["", "commodity"];
          }
        }
      }
    });
  }

  vipChecker(BuildContext context, Function(bool checktResult)? checkCallback) {
    CanvasTemplateData.TemplateData _templateData =
        CanvasTemplateData.TemplateData.fromJson(jsonDecode(jsonEncode(state.goodsTemplate)));
    print("need vip ${_templateData.needVip()} isVip=${VipHelper.isVip()}");
    if (_templateData.needVip() && !VipHelper.isVip()) {
      VIPType vipType = VipHelper.getVipType();
      if (vipType == VIPType.expired) {
        VipHelper.showVipExpiredDialog(context, (buyResult) {
          checkCallback?.call(buyResult);
        });
      } else {
        VipHelper.showOpenVipDialog(context, (buyResult) {
          checkCallback?.call(buyResult);
        });
      }
    } else {
      checkCallback?.call(true);
    }
  }

  updatePreview(int index) {
    if (index < 0 || index > state.goodsMixList.length - 1) return;
    state.previewIndex = index;
    aloneGoodsMixTemplate(state.previewIndex);
  }

  etagCardListener(bool isOpen) {
    ToNativeMethodChannel.sharedInstance().eTagStatusListener(isOpen);
  }

  ///断开价签机连接
  _etagDisConnect() {
    ToNativeMethodChannel.sharedInstance().etagDisConnect();
  }

  ///重置价签状态
  resetEtagStatus() {
    state.isAllowConnect = false;
    var stateBool = false;
    state.cardStatus.forEach((element) {
      if (element == EtagCardState.setting) {
        stateBool = true;
      }
    });
    if (stateBool) {
      Timer(const Duration(seconds: 2), () {
        if (!state.isAllowConnect) {
          ToNativeMethodChannel.sharedInstance().resetEtagStatus();
        }
      });
    } else {
      ToNativeMethodChannel.sharedInstance().resetEtagStatus();
    }
  }

  ///价签信息同步屏幕
  writeScreenData(bool isNext, BuildContext context) {
    state.isStart = true;
    state.connectState = CardConnectState.normal;
    state.isErrorToast = true;
    Timer(Duration(seconds: 2), () {
      state.isStart = false;
    });
    this.context = context;
    state.lastState = EtagCardState.empty;
    state.writeGoodsMixList.clear();
    state.writeGoodsMixList.addAll(state.goodsMixList);
    if (state.cardStatus[state.connectIndex] != EtagCardState.size) {
      if (state.writeGoodsMixList.length < 1 && !isNext) {
        state.connectState = CardConnectState.complete;
        state.writeGoodsMixList.clear();
        state.goodsIndex += 1;
        state.progress = 0;
        state.connectStatusModel.clear();
        update(["progress", "connectState", "eTagStatus"]);
      } else {
        _isConnected();
        update(["progress", "connectState", "eTagStatus"]);
      }
    }

    changeIndex();
  }

  ///价签同步
  changeIndex() {
    if (state.cardStatus[state.connectIndex] == EtagCardState.size &&
        state.lastState != EtagCardState.size &&
        state.cardStatus.length > state.connectIndex &&
        state.isAllowConnect &&
        state.goodsIndex < state.goodsMixList.length) {
      state.lastState = EtagCardState.size;
      state.cardStatus[state.connectIndex] = EtagCardState.setting;
      ToNativeMethodChannel().writeScreenData({
        'index': state.connectIndex + 1,
        'isNfc': false,
        'screenData': state.goodsMixList[state.goodsIndex]
      }).then((value) async {
        if (value is int || value == null) {
          if (state.isErrorToast) {
            state.isErrorToast = false;
            Fluttertoast.showToast(msg: intlanguage('app100000928', '设备忙碌'));
          }

          _isConnected();
          state.lastState = EtagCardState.error;
        } else {
          state.connectStatusModel.add(ETagConnectStatusModel(
              state.goodsMixList[state.goodsIndex]["goodsId"].toString(), state.connectIndex, EtagCardState.setting));
          Map<String, dynamic> data = {
            'goodsMixList': jsonEncode([state.goodsMixList[state.goodsIndex]])
          };
          var preview = await ToNativeMethodChannel.sharedInstance().goodsMixListAll(data);
          state.previewCards[state.connectIndex + 1] = preview[0];

          Map<String, dynamic> map = jsonDecode(value);
          var screenInfo = ScreenInfoModel.fromJson(map);
          Map<String, dynamic> parameters = {
            "userId": Application.user?.id.toInt().toString(),
            "templateId": state.goodsTemplate["id"].toString(),
            "commodityId": state.goodsMixList[state.goodsIndex]["goodsId"],
            "sn": screenInfo.serial
          };

          state.uploadList.add(parameters);

          state.goodsIndex += 1;
          state.connectIndex += 1;
          _isConnected();
        }
        state.lastState = EtagCardState.error;
        //  update(["progress", "connectState", "eTagStatus"]);
        changeIndex();
      });
    }
  }

  nextConnect(BuildContext context) {
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "111_220",
    });
    if (state.baseConnectState != BaseState.success) {
      CustomNavigation.gotoNextPage('DeviceConnectTagBasePage', {'uniappId': 'FLUTTER_TAG'});
    } else {
      state.connectState = CardConnectState.normal;
      state.connectIndex = 0;
      state.lastProgress = state.progress;

      ///收集上一批同步失败的商品数据
      List<String> failstatusModel = [];
      state.connectStatusModel.forEach((element) {
        if (element.cardType != EtagCardState.complete) {
          failstatusModel.add(element.connectId);
        }
      });

      ///从商品数据集删除同步失败的商品并收集
      List<Map<String, dynamic>> failGoodsList = [];
      state.goodsMixList.removeWhere((element) {
        if (failstatusModel.contains(element["goodsId"])) {
          failGoodsList.add(element);
          return true;
        } else {
          return false;
        }
      });

      ///重新对同步指针进行移动
      state.goodsIndex = state.goodsIndex - failGoodsList.length;
      if (state.goodsIndex < 0) {
        state.goodsIndex = 0;
      }

      ///将失败数据重新插入到将要同步的商品数据前面
      state.goodsMixList.insertAll(state.goodsIndex, failGoodsList);

      state.connectStatusModel.clear();
      var isShowToast = true;
      state.cardStatus.forEach((element) {
        if (element == EtagCardState.size) {
          isShowToast = false;
        }
      });
      if (isShowToast) {
        showIconToast();
        return;
      }
      update(["connectState"]);
      state.isAllowConnect = true;
      writeScreenData(false, context);
    }
  }

  showIconToast() {
    if (state.goodsMixList[0]["width"] == 84.8) {
      showToast(msg: intlanguage('app100000978', '请插入4.2寸价签后继续同步'));
    } else {
      showToast(msg: intlanguage('app100000979', '请插入2.9寸价签后继续同步'));
    }
  }

  _isConnected() {
    if (state.connectIndex < state.cardStatus.length - 1) {
      if (state.cardStatus[state.connectIndex] != EtagCardState.size) {
        state.connectIndex += 1;
        _isConnected();
      }
    }
  }
}

extension tagBaseNet on TagBaseLogic {
  ///上传价签数据
  saveEtagScreenInfo() {
    state.isStart = true;
    DioUtils.instance.requestNetwork<String>(Method.post, EtagTemplateApi.saveNfcScreenInfo,
        params: state.uploadList.toList(), isList: false, onSuccess: (model) {
      state.uploadList.clear();
    }, onError: (code, message) {
      showToast(msg: message);
    });
    resetEtagStatus();
    CustomNavigation.pop();
  }

  ///更新商品数据
  updateGoodsMap(Map<String, dynamic> value, BuildContext context) {
    GoodFieldManager().correctionDataWith(value);
    value["sceneTags"] = ["etag"];
    Map<String, dynamic> parameters = value;
    DioUtils.instance.requestNetwork<String>(
        Method.put,
        {
          'method': 'POST',
          'path': '/product/customStorage/' + value["id"],
          'needLogin': true,
        },
        params: parameters,
        isList: false, onSuccess: (model) {
      state.alonePreviewLoading = true;
      state.goodsList[state.previewIndex] = value;
      _goodsMixTemplate();
    }, onError: (code, message) {
      showToast(msg: message);
    });
  }
}
