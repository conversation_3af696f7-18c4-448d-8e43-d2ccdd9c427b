import 'dart:typed_data';

import 'package:nety/models/sdk/sdk_result.dart';
import 'package:nety/nety.dart';

class PrinterSettingManager{
  static PrinterSettingManager? _instance;

  // Avoid self instance
  PrinterSettingManager._();

  factory PrinterSettingManager() {
    _instance ??= PrinterSettingManager._();
    return _instance!;
  }

  /// 获取打印机硬件版本和固件版本
  Future<Map> getHardwareSoftware() async{
    String? hardwareVersion = await NiimbotPrintSDK().getHardwareVersion();
    String? softwareVersion = await NiimbotPrintSDK().getSoftwareVersion();
    Map<String, String> map = {};
    if(hardwareVersion != null){
      map["hardwareVersion"] = hardwareVersion;
    }
    if(softwareVersion != null){
      map["softwareVersion"] = softwareVersion;
    }
    return map;
  }

  /// 获取打印机设置页面打包的设备信息
  Future<Map> getPackedPrinterInfo({bool isSupportWIFI = false}) async {
    Map map = await NiimbotPrintSDK().getPackedPrinterInfo(isSupportWIFI: isSupportWIFI);
    return map;
  }

  /// 获取自动关机时间设置
  Future<int?> getAutoShutdownTime() async {
    int? level = await NiimbotPrintSDK().getAutoShutdownTime();
    return level;
  }

  /// 获取打印机开关提示音是否开启状态
  Future<int?> getDeviceVoice() async {
    int? deviceVoice = await NiimbotPrintSDK().getDeviceVoice();
    return deviceVoice;
  }

  /// 获取打印机蓝牙断连提示音是否开启状态
  Future<int?> getBluetoothVoice() async {
    int? bluetoothVoice = await NiimbotPrintSDK().getBluetoothVoice();
    return bluetoothVoice;
  }

  /// 获取打印机支持的按键功能列表
  Future<List<PrinterKeyProperty>?> getPrinterProperty() async {
    List<PrinterKeyProperty>? printerKeyProperties = await NiimbotPrintSDK().getPrinterProperty();
    return printerKeyProperties;
  }

  /// 获取打印机当前配置的按键功能项
  Future<List<PrinterKeyFunction>?> getKeyFunction() async {
    List<PrinterKeyFunction>? printerKeyFunctions = await NiimbotPrintSDK().getKeyFunction();
    return printerKeyFunctions;
  }

  /// 走纸校准
  /// [labelType] 标签纸类型
  Future<int> setPositioningCalibration(int labelType) async {
    int result = await NiimbotPrintSDK().setPositioningCalibration(labelType);
    return result;
  }

  /// 设置开机按键功能
  /// [key]
  /// [function]
  Future<int> setKeyFunction(int key, int function) async {
    int result = await NiimbotPrintSDK().setKeyFunction(key, function);
    return result;
  }

  /// 设置当前时间
  /// [time] 时间参数
  Future<int> setPrinterTime(DateTime time) async {
    int result = await NiimbotPrintSDK().setPrinterTime(time);
    return result;
  }

  /// 设置自动关机时间
  /// [level] 自动关机时间档位
  Future<int> setPrinterShutdownTime(int level) async {
    int result = await NiimbotPrintSDK().setPrinterShutdownTime(level);
    return result;
  }

  /// 开启或关闭打印机开关机声音
  /// [status] 打印机开关机声音开启状态
  Future<int> setDeviceVoice(int status) async {
    int result = await NiimbotPrintSDK().setDeviceVoice(status);
    return result;
  }

  /// 开启或关闭打印机蓝牙连接断开声音
  /// [status] 打印机连接断开声音开启状态
  Future<int> setBluetoothVoice(int status) async {
    int result = await NiimbotPrintSDK().setBluetoothVoice(status);
    return result;
  }

  /// 打印机配置wifi网络
  /// [name] wifi名称
  /// [password] wifi密码
  Future<bool> configWifi(String name, String password) async {
    try {
      bool result = await NiimbotPrintSDK().configWiFi(name, password);
      return result;
    }
    catch (e){
      return false;
    }
  }

  /// 获取设备配置的wifi信息
  Future<String?> getDeviceWiFiConfig() async {
    String? wifiName = await NiimbotPrintSDK().getDeviceWiFiConfig();
    return wifiName;
  }

  /// 开始走管
  Future<int> setTubeCalibration() async {
    int result = await NiimbotPrintSDK().setTubeCalibration();
    return result;
  }

  /// 设置走管校准长度
  /// [length] 走管校准长度
  Future<int> setTubeAdjustLength(double length) async {
    int result = await NiimbotPrintSDK().setTubeAdjustLength(length);
    return result;
  }

  /// 反向走管
  Future<int> reversePrinterFeed() async {
    final result = await NiimbotPrintSDK().reversePrinterFeed();
    return result;
  }

  /// 获取是否支持本地模板
  Future<bool> getLocalTemplateSupport() async {
    final result = await NiimbotPrintSDK().getLocalTemplateSupport();
    return result?.isSupportTemplate ?? false;
  }

  /// 设置模板
  /// [data] 模板缩略图
  /// [width] 缩略图宽度
  /// [height] 缩略图高度
  /// [templateId] 模板id
  Future<int> setLocalTemplateData(Uint8List data, int width, int height, String templateId) async {
    final result = await NiimbotPrintSDK().setLocalTemplateData(data, width, height, templateId);
    return result;
  }

  ///  设置模板参数
  ///  timeSequence 时间序号：从1开始
  ///  orientation 旋转角度：0-不旋转，1-90度，2-180度，3-270度
  ///  isInverse 是否反白：0-不反白，1-反白
  ///  languageCode 多语言：1-中文，2-繁体，3-英语，4-日语，5-俄语，6-意大利语，7-法语，8-德语，9-西班牙语，10-韩语，
  ///  11-波兰语，12-捷克语，13-泰语，14-葡萄牙语，15-荷兰语，16-印尼语，17-阿拉伯语，18-土耳其语，19-马来语，20-印地语
  ///  fontSize 字号：1-2mm 2-3mm 3-4mm 4-5mm 5-6mm 6-7mm 7-8mm 8-9mm 9-10mm 10-14mm 11-19mm 12-25mm
  ///  hourFormat 小时制：0-24小时 1-12小时
  ///  columnIndex 列号，像素点
  ///  rowIndex 行号，像素点
  ///  width 宽度，像素点
  ///  height 高度，像素点
  ///  dateFormat 日期格式：0，不显示  1，2024年11月12日  2，2024年11月  3，11月12日  4，20241112  5，2023-11-12  6，2024-11  7，11-12  8，2024/11/12  9，2024/11  10，11/12
  ///  11，11-12-2024  12，12-11-2024  13，12/11/2024  14，NOV 12,2024  15，November 12,2024  16，12 NOV,2024  17，12 November,2024  18，12.11.2024  19：2024.11.12  20：2024 11 12
  ///  timeFormat 时间格式：0-不显示，1-时:分，2-时:分:秒，3-分:秒
  ///  displayWeek 是否显示星期：0-不显示，1-显示
  ///  weekColumnIndex 星期列号
  ///  weekRowIndex 星期行号
  ///  add 有效期增减情况：0-加，1-减
  ///  expirationDate 有效期秒数：该时间序号下需要固件根据当前时间累加有效期计算得到时间信息时，需下发；否则为0
  ///  showX 时间显示起始位置x
  ///  showY 时间显示起始位置y
  ///  align 对齐方式：1-左对齐，2-居中对齐，3-右对齐
  ///  bold 是否加粗：0-不加粗，1-加粗
  ///  underline 是否添加下划线：0-不添加，1-添加
  ///  expirationUnit 有效期单位：0-没有，1-月，2-年
  ///  expirationValue 有效期偏移值（0-99）
  Future<int> setLocalTemplateSettings(List<Map<String, dynamic>> params) async {
    final result = await NiimbotPrintSDK().setLocalTemplateSettings(params);
    return result;
  }

  /// 查询模板参数信息
  Future<String?> getTemplateInfo() async {
    final result = await NiimbotPrintSDK().getTemplateInfo();
    return result?.templateInfo;
  }

  /// 设置实时时间参数
  /// timeType 时间格式：1，年-月-日  2，年-月-日 时:分  3，日-月-年  4，日-月-年 时:分  5，月-日-年  6，月-日-年 时:分
  Future<int> setLocalTimeFormat(int timeType) async {
    final result = await NiimbotPrintSDK().setLocalTimeFormat(timeType);
    return result;
  }

  /// 查询实时时间参数信息
  Future<int?> getTimeFormat() async {
    final result = await NiimbotPrintSDK().getTimeFormat();
    return result?.timeFormat;
  }
}
