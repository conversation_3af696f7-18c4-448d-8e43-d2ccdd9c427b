//
//  JCBluetoothCell.m
//  Runner
//
//  Created by j c on 2019/1/22.
//  Copyright © 2019年 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCBluetoothCell.h"
#import "JCBluetoothModel.h"


@interface JCBluetoothCell()

@property(nonatomic,strong) UIImageView *iconView;
@property (nonatomic,strong) UILabel *titleLabel;
@property (nonatomic,strong) UILabel *connectStateLabel;
@property (nonatomic,strong) UIImageView *connectStateImage;
@property (nonatomic,strong) UIButton *btnConnected;
@property (nonatomic,strong) UIView *line;
@property(nonatomic,strong) UIButton *defaultButton;
@property(nonatomic,strong) UIView *backView;
@property (nonatomic,strong) UIButton *selectBtn;

@property(nonatomic,strong) JCBluetoothModel *model;


@end

@implementation JCBluetoothCell



-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if(self){
         self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setUp];
    }
    return self;
}


-(void)setUp{
    [self.contentView addSubview:self.backView];
    [self.backView addSubview:self.iconView];
    [self.backView addSubview:self.titleLabel];
    [self.backView addSubview:self.connectStateImage];
    [self.backView addSubview:self.connectStateLabel];
    [self.backView addSubview:self.btnConnected];
   
    XYWeakSelf
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(weakSelf.contentView);
        make.trailing.equalTo(weakSelf.contentView);
        make.top.bottom.equalTo(weakSelf.contentView);
    }];
    [self.iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(weakSelf.backView).offset(15);
        make.size.mas_equalTo(58);
        make.centerY.equalTo(weakSelf.backView);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(weakSelf.iconView.mas_trailing).offset(8);
        make.top.equalTo(weakSelf.iconView.mas_top).offset(5);
        make.trailing.equalTo(weakSelf.backView).offset(-6);
    }];
    
    [self.connectStateImage mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(weakSelf.iconView.mas_trailing);
        make.bottom.equalTo(weakSelf.iconView.mas_bottom);
        make.size.mas_equalTo(18);
    }];
    
    [self.btnConnected mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(weakSelf.backView).offset(-6);
        make.bottom.equalTo(weakSelf.iconView.mas_bottom).offset(-2);;
        make.height.mas_equalTo(24);
        make.width.mas_greaterThanOrEqualTo(50);
    }];
    
    [self.connectStateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(weakSelf.iconView.mas_trailing).offset(8);
        make.centerY.equalTo(self.btnConnected);
        make.trailing.mas_lessThanOrEqualTo(self.btnConnected.mas_leading).offset(-30);
    }];
}


- (void)setModel:(JCBluetoothModel *)Model
{
    JCBluetoothModel *originalModel = _model;
    _model = Model;
    self.titleLabel.text = STR_IS_NIL(_model.alias) ? _model.name : _model.alias;
    JCSelectDeviceModel *printerModel = Model.printerModel;
    self.titleLabel.textColor = printerModel.isNotSupport.integerValue == 1?HEX_RGBA(0x262626, 0.3):HEX_RGB(0x262626);
    UIImage *defaoutImage = XY_IMAGE_NAMED(printerModel.guide_name);
    if(!defaoutImage){
        NSString *url = printerModel.guide_image.lastPathComponent;
        if([url hasSuffix:@".png"]){
            NSRange range = [url rangeOfString:@".png"];
            NSString *imgName = [url substringToIndex:range.location];
            defaoutImage = XY_IMAGE_NAMED(imgName);
        }else if([url hasSuffix:@".jpg"]){
            NSRange range = [url rangeOfString:@".jpg"];
            NSString *imgName = [url substringToIndex:range.location];
            defaoutImage = XY_IMAGE_NAMED(imgName);
        }
        if(!defaoutImage){
            NSString *printerSeriesName = [[printerModel.series_name componentsSeparatedByString:@" "] safeObjectAtIndex:0];
            defaoutImage = XY_IMAGE_NAMED(printerSeriesName);
        }
    }
    
    if(originalModel && originalModel.printerModel && originalModel.printerModel.guide_image && [originalModel.printerModel.guide_image isEqualToString:printerModel.guide_image]){
        return;
    }
    [self.iconView sd_setImageWithURL:XY_URLWithString(printerModel.guide_image) placeholderImage:(defaoutImage != nil?defaoutImage:[UIImage new]) completed:^(UIImage *image, NSError *error, SDImageCacheType cacheType, NSURL *imageURL) {
        
    }];
      
}

- (void)setIsConnect:(BOOL)isConnect
{
    XYWeakSelf
    JCSelectDeviceModel *printerModel = self.model.printerModel;
    NSString *title = isConnect ? XY_LANGUAGE_TITLE_NAMED(@"app00789", @"断开连接"):XY_LANGUAGE_TITLE_NAMED(@"app00034",@"连接");
    self.btnConnected.hidden = false;//!isConnect;
    
    [self.btnConnected setTitle:title forState:UIControlStateNormal];
    [self.connectStateImage mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(@(isConnect?18:0));
    }];
    
    [self.connectStateLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(weakSelf.iconView.mas_trailing).offset((isConnect?(18 + 18 + 10):8));
    }];
    [self.btnConnected setHidden:printerModel.isNotSupport.integerValue == 1];
    if(printerModel.isNotSupport.integerValue == 1){
        self.connectStateLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app100000255", @"暂不支持该机器");
    }else{
        self.connectStateLabel.text = isConnect?XY_LANGUAGE_TITLE_NAMED(@"app00198", @"已连接"):XY_LANGUAGE_TITLE_NAMED(@"app00190", @"未连接");
        float btnWidth = [XY_LANGUAGE_TITLE_NAMED(@"app00034",@"连接") jk_sizeWithFont:MY_FONT_Regular(13) constrainedToWidth:1000].width;
        [self.btnConnected mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(@(btnWidth + 24));
        }];
    }
}

-(void)refreshConnectBtn:(BOOL)isConnecting{
    [self.btnConnected setTitleColor:isConnecting ? [HEX_RGB(0xFB4B42) colorWithAlphaComponent:0.3] : HEX_RGB(0xFB4B42)  forState:UIControlStateNormal];
}


- (void)clickAction:(UIButton*)button
{
    if (self.connectBlock) {
        self.connectBlock();
    }
}



#pragma lazy
-(UIImageView *)iconView
{
    if (!_iconView) {
        _iconView = [[UIImageView alloc] initWithImage:XY_IMAGE_NAMED(@"blueTooth_connect")];
      }
   return _iconView;
}

- (UIView *)backView{
    if(!_backView){
        _backView = [[UIView alloc] init];
        _backView.backgroundColor = HEX_RGB(0xFFFFFF);
        _backView.layer.cornerRadius = 12;
    }
    return _backView;
}

-(UIButton *)btnConnected{
    if(!_btnConnected){
        _btnConnected = [UIButton buttonWithType:UIButtonTypeCustom];
        [_btnConnected setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00034",@"连接") forState:UIControlStateNormal];
        [_btnConnected setTitleColor:HEX_RGB(0xFB4B42) forState:UIControlStateNormal];
        _btnConnected.titleLabel.font = MY_FONT_Bold(13);
        _btnConnected.contentHorizontalAlignment = UIControlContentHorizontalAlignmentCenter;
       // [_btnConnected addTarget:self action:@selector(clickAction:) forControlEvents:UIControlEventTouchUpInside];
        _btnConnected.backgroundColor = HEX_RGB(0xFAFAFA);
        _btnConnected.layer.cornerRadius = 12;
        [_btnConnected setUserInteractionEnabled:false];
        _btnConnected.hidden = false;//YES;
    }
    return _btnConnected;
}

-(UILabel *)titleLabel{
    if(!_titleLabel){
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = MY_FONT_Bold(16);
        _titleLabel.textColor = HEX_RGB(0x262626);
        _titleLabel.textAlignment = NSTextAlignmentLeft;
        _titleLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    }
    return _titleLabel;
}

-(UILabel *)connectStateLabel{
    if(!_connectStateLabel){
        _connectStateLabel = [[UILabel alloc] init];
        _connectStateLabel.font = MY_FONT_Medium(14);
        _connectStateLabel.textColor = HEX_RGB(0x999999);
        _connectStateLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _connectStateLabel;
}

-(UIImageView *)connectStateImage
{
    if (!_connectStateImage) {
        _connectStateImage = [[UIImageView alloc] initWithImage:XY_IMAGE_NAMED(@"connectSuccess")];
        _connectStateImage.hidden = YES;
      }
   return _connectStateImage;
}

@end
