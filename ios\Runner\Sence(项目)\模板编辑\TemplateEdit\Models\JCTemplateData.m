//
//  JCTemplateData.m
//  Runner
//
//  Created by xingling xu on 2020/3/11.
//  Copyright © 2020 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCTemplateData.h"
#import "JCExcelTransUtil.h"
#import "JCTMDataBindGoodsInfoManager.h"
@implementation JCTemplateData
@synthesize sourceOfIndustryTemplateId = _sourceOfIndustryTemplateId;

-(instancetype)initWithDictionary:(NSDictionary *)dict error:(NSError *__autoreleasing *)err
{
    JCTemplateData * data = nil;
    if(dict != nil && [dict isKindOfClass:[NSDictionary class]]){
        NSMutableDictionary * fixDic = [[NSMutableDictionary alloc]initWithDictionary:dict];
        id dataSourceBindInfo = [fixDic objectForKey:@"dataSourceBindInfo"];
        if( dataSourceBindInfo != nil   &&  ![dataSourceBindInfo isKindOfClass:[NSDictionary class]]){
            [fixDic setValue:@{} forKey:@"dataSourceBindInfo"];
        }
        id dataSource = [fixDic objectForKey:@"dataSource"];
        if(dataSource != nil && ![dataSource isKindOfClass:[NSArray class]]){
            [fixDic setValue:@[] forKey:@"dataSource"];
        }
        id dataSources = [fixDic objectForKey:@"dataSources"];
        if(dataSources != nil && ![dataSources isKindOfClass:[NSArray class]]){
            [fixDic setValue:@[] forKey:@"dataSources"];
        }
        // hasVIPRes字段存在双端不一致的情况，需要重新判断
        id hasVIPRes = [fixDic objectForKey:@"hasVIPRes"];
        if (hasVIPRes != nil && [hasVIPRes isKindOfClass:[NSNumber class]]) {
           [fixDic setValue:hasVIPRes forKey:@"hasVipRes"];
        }
        data = [super initWithDictionary:fixDic error:err];
    }else{
        data = [super initWithDictionary:dict error:err];
    }
    return data;

}

-(void)clearTemplateNewJsonData{
    self.dataSource = @[];
    self.dataSources=@[];
    self.modify=@{};
    self.dataSourceModifies=@{};
    self.bindInfo=@{};
    self.dataSourceBindInfo=@{};
}

- (void)setBindInfo:(NSDictionary *)bindInfo{
  _bindInfo = bindInfo;
  if(self.totalPage == 0 && bindInfo[@"total"] != nil){
    NSNumber *totalNumber = bindInfo[@"total"];
    self.totalPage = totalNumber.integerValue > 0?totalNumber.integerValue:self.totalPage;
  }
}
+ (void)checkVipResourceVersion:(JCTemplateData *)tData{
    BOOL hasVipLogo  = NO;
    BOOL hasActivityCode  = NO;
    BOOL hasArcText  = NO;
    BOOL hasVertiText  = NO;
    BOOL hasForm  = NO;
    __block BOOL hasBigText = NO;
    for (JCElementModel *elementModel in tData.elements) {
        if(!STR_IS_NIL(elementModel.materialId)){
            NSString *materialId = elementModel.materialId;
            if([jc_vip_logo_ids containsObject:materialId]){
                hasVipLogo = YES;
            }
        }
        if([elementModel.type isEqual:@"qrcode"] && elementModel.isLive){
            hasActivityCode = YES;
        }
        if([elementModel.type isEqual:@"text"] && elementModel.typesettingMode == 2){
            hasVertiText = YES;
        }
        if([elementModel.type isEqual:@"qrcode"] && elementModel.isForm){
            hasForm = YES;
        }
        //是否包含大字体
        if(elementModel.fontSize > 25.4){
            hasBigText = YES;
        }

        if(IsNotEmptyArray(elementModel.cells)){
            [[elementModel cells] enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                JCElementModel*cell = (JCElementModel*)obj;
                if(cell.fontSize > 25.4){
                    hasBigText = YES;
                }
            }];
        }
    }
    NSString *resourceVersion = @"";
    if(hasArcText || hasVipLogo || hasActivityCode || tData.canvasRotate != 0){
        resourceVersion = @"5.3.5";
    }
    if(hasVertiText){
        resourceVersion = @"5.7.4";
    }
    if(hasForm){
        resourceVersion = @"5.8.3";
    }
    if(hasBigText){
        resourceVersion = @"5.9.0";
    }
    if(([tData.dataSource isKindOfClass:[NSArray class]] && tData.dataSource.count > 0)){
        resourceVersion = @"5.10.0";
    }
    tData.profile.extrain.resourceVersion = resourceVersion;
}

- (NSString *)getCurrentTemplateVersion{
    //注意：每一次涉及到版本号新增需要升级 MaxSupportJSONVersion
    NSString *currentTemplateVersion = CurrentSaveJSONVersion;
    //时间组件相关 时间组件模版版本号 *******
    BOOL hasNewDateForm = NO;
    for (JCElementModel *elementModel in self.elements) {
        if([elementModel.type isEqualToString:@"date"]){
            hasNewDateForm = YES;
        }
    }

    if(hasNewDateForm && ![self isEqualByComparefunction:RealTimeJSONVersion withCurrentVersion:currentTemplateVersion]){
        currentTemplateVersion = RealTimeJSONVersion;
    }
    //商品库模版相关 模版版本号 1.7.0.2
    if([self isNewCommodityTemplate] && ![self isEqualByComparefunction:CustomCommodityJSONVersion withCurrentVersion:currentTemplateVersion]){
        if( [self isHaveCustomField]){
            currentTemplateVersion = CustomCommodityJSONVersion;
        }
    }
    /**
     * Excel模版版本检查 (1.7.0.3)
     * 1. 检查是否为Excel模板
     * 2. 如果是且当前版本低于Excel所需版本，则更新版本号
     */
    if([self isNewExcelTemplate] && ![self isEqualByComparefunction:CustomExcelJSONVersion withCurrentVersion:currentTemplateVersion]){
        currentTemplateVersion = CustomExcelJSONVersion;
    }
    return currentTemplateVersion;
}

//是否包含商品库自定义字段
-(BOOL)isHaveCustomField{
    for (JCElementModel *elementModel in self.elements) {
        if([elementModel isCustomField]){
            return true;
        }
    }
    return false;
}

//是否包含商品库自定义字段
-(BOOL)isHaveTextElement{
    for (JCElementModel *elementModel in self.elements) {
        if([elementModel isTextElement]){
            return true;
        }
    }
    return false;
}

-(BOOL)isOnlyHaveTextElement{
    for (JCElementModel *elementModel in self.elements) {
        if(![elementModel isTextElement]){
            return false;
        }
    }
    return true;
}

-(void)resetTemplateLocalPath{
//    NSMutableArray *imagesPaths = [NSMutableArray array];
//    NSString *imageId = self.idStr;
//    NSString *currentBackImgPath;
//    UIImage *image;
//    BOOL isCloud = YES;
//    image = [[UIImage alloc] initWithContentsOfFile:currentBackImgPath];
//    if(image == nil){
//        imageId = self.profile.extrain.labelId;
//        if ([self.backgroundImage componentsSeparatedByString:background_image_component].count > 1) {
//            // set base64 string
//            currentBackImgPath = MutipleBackImageLocalPath(imageId,isCloud,0);
//        } else {
//            currentBackImgPath = SigleBackImageLocalPath(imageId,isCloud);
//        }
//        image = [[UIImage alloc] initWithContentsOfFile:currentBackImgPath];
//    }
//    if(image == nil){
//        imageId = self.idStr;
//        isCloud = [self isCloudTemplate];
//        if(imageId.length > 12) isCloud = NO;
//        if ([self.backgroundImage componentsSeparatedByString:background_image_component].count > 1) {
//            // set base64 string
//            currentBackImgPath = MutipleBackImageLocalPath(imageId,isCloud,0);
//        } else {
//            currentBackImgPath = SigleBackImageLocalPath(imageId,isCloud);
//        }
//        image = [[UIImage alloc] initWithContentsOfFile:currentBackImgPath];
//    }
//    NSArray *bubbleArr = [self.backgroundImage componentsSeparatedByString:@","];
//    if (bubbleArr.count > 1) {
//        for (NSInteger i = 0; i < bubbleArr.count; i ++) {
//            NSString *imagePath = MutipleBackImageLocalPath(imageId,isCloud,i);
//            [imagesPaths addObject:imagePath];
//        }
//    } else {
//        NSString *imagePath = SigleBackImageLocalPath(imageId,isCloud);
//        [imagesPaths addObject:imagePath];
//    }
//    if(self.canvasRotate != 0 && self.needLoadRotateImage){
//        NSString * currentRotateBackImgPath;
//        if ([self.backgroundImage componentsSeparatedByString:background_image_component].count > 1) {
//            // set base64 string
//            currentRotateBackImgPath = MutipleBackImageLocalPathRotate(imageId,isCloud,0,self.canvasRotate);
//        } else {
//            currentRotateBackImgPath = SigleBackImageLocalPathRotate(imageId,isCloud,self.canvasRotate);
//        }
//        UIImage *rotateImage = [[UIImage alloc] initWithContentsOfFile:currentRotateBackImgPath];
//        if(rotateImage == nil && image != nil){
//            [self copyImageToRotateImage:imagesPaths imageId:imageId isCloud:isCloud];
//        }
//        [imagesPaths removeAllObjects];
//        NSArray *bubbleArr = [self.backgroundImage componentsSeparatedByString:@","];
//        if (bubbleArr.count > 1) {
//            for (NSInteger i = 0; i < bubbleArr.count; i ++) {
//                NSString *imagePath = MutipleBackImageLocalPathRotate(imageId,isCloud,i,self.canvasRotate);
//                [imagesPaths addObject:imagePath];
//            }
//        } else {
//            NSString *imagePath = SigleBackImageLocalPathRotate(imageId,isCloud,self.canvasRotate);
//            [imagesPaths addObject:imagePath];
//        }
//        self.needLoadRotateImage = NO;
//    }
//    self.localBackground = imagesPaths;
//    for (JCElementModel *elementModel in self.elements) {
//        if([elementModel.type isEqualToString:@"image"]){
//            if(![[NSFileManager defaultManager] fileExistsAtPath:elementModel.localUrl]){
//                // 点九图使用元素ID、普通的按照边框ID，没有则使用元素ID
//                NSString *localUrlId = elementModel.isNinePatch ? elementModel.elementId : (!STR_IS_NIL(elementModel.materialId) ? elementModel.materialId : elementModel.elementId);
//                elementModel.localUrl = ElementLocalPath(localUrlId, NO);
//                // 离线的情况下，localUrl并没有保存，所以存在localUrl路径不存在，此时不可清除imageData，否则离线打开造成文件丢失
//                if ([[NSFileManager defaultManager] fileExistsAtPath:elementModel.localUrl]) {
//                    elementModel.imageData = @"";
//                }else{
//                   elementModel.localUrl = @"";
//                }
//            }
//          // 点9图路径处理
//          if (elementModel.isNinePatch && ![[NSFileManager defaultManager] fileExistsAtPath:elementModel.ninePatchLocalUrl]) {
//            elementModel.ninePatchLocalUrl = NinePatchElementLocalPath(!STR_IS_NIL(elementModel.materialId) ? elementModel.materialId : elementModel.elementId, NO);
//          }
//        }
//    }
//    self.goodsList = @[];
}

- (void)copyImageToRotateImage:(NSArray *)localImages imageId:(NSString *)imageId isCloud:(BOOL)isCloud{
    NSArray *bubbleArr = [self.backgroundImage componentsSeparatedByString:@","];
    if (bubbleArr.count > 1) {
        for (NSInteger i = 0; i < bubbleArr.count; i ++) {
            NSString *imagePath = MutipleBackImageLocalPath(imageId,isCloud,i);
            NSString *rotateImagePath = MutipleBackImageLocalPathRotate(imageId,isCloud,i,self.canvasRotate);
            UIImage *image = [[UIImage alloc] initWithContentsOfFile:imagePath];
            UIImage *rotateImage = [image jk_imageRotatedByDegrees:self.canvasRotate];
            NSData *npgData = UIImagePNGRepresentation(rotateImage);
            [npgData writeToFile:rotateImagePath atomically:YES];
        }
    } else {
        NSString *imagePath = SigleBackImageLocalPath(imageId,isCloud);
        NSString *rotateImagePath = SigleBackImageLocalPathRotate(imageId,isCloud,self.canvasRotate);
        UIImage *image = [[UIImage alloc] initWithContentsOfFile:imagePath];
        UIImage *rotateImage = [image jk_imageRotatedByDegrees:self.canvasRotate];
        NSData *npgData = UIImagePNGRepresentation(rotateImage);
        [npgData writeToFile:rotateImagePath atomically:YES];
    }
}

//获取第一支持场景
-(NSString *)getFirstEditor{
    NSString *firstEditor = @"";
    if(self.supportedEditors.count > 0){
        firstEditor = self.supportedEditors.firstObject;
    }
    return firstEditor;
}

- (BOOL)isCableLabel{
    if([[self getFirstEditor] isEqualToString:@"cable"] && !STR_IS_NIL(self.layoutSchema)){
        return YES;
    }else{
        return NO;
    }
}

/// 是否危废标签纸
- (BOOL)isDangerLabel{
    if([[self getFirstEditor] isEqualToString:@"hazardous"] && !STR_IS_NIL(self.layoutSchema) && !(NETWORK_STATE_ERROR)) {
        return YES;
    }else{
        return NO;
    }
}

-(BOOL)isHaveGray16Element{
    BOOL haveGray16Element = NO;
    for (JCElementModel *model in self.elements) {
        if(model.imageProcessingType == 2){
            haveGray16Element = YES;
            break;
        }
    }
    return haveGray16Element;
}

-(BOOL)isHaveSingleColorGrayElement{
    BOOL isSingleColorGray = NO;
    for (JCElementModel *model in self.elements) {
        if(model.imageProcessingType != 1){
            isSingleColorGray = YES;
            break;
        }
    }
    return isSingleColorGray;
}

-(void)resetUnGray16Element{
    for (JCElementModel *model in self.elements) {
        if(model.imageProcessingType == 2){
            model.imageProcessingType = 3;
        }
    }
}

- (instancetype)init {
    self = [super init];
    if (self) {
        self.idStr = @"";
        self.name = @"";
        self.thumbnail = @"";
        self.backgroundImage = @"";
        self.previewImage = @"";
        self.des = @"";
        self.rotate = 0;
        self.cableLength = 0;
        self.cableDirection = 0;
        self.paperType = 1;
        self.consumableType = 1;
        self.isCable = NO;
        self.isSelected = NO;
        self.width = 0;
        self.height = 0;
        self.margin = [NSArray array];
        self.usedFonts = [self getCurrentFonts];
        self.unit = @"";
        self.externalData = [NSDictionary dictionary];
        self.commodityInfo = [NSDictionary dictionary];
        self.task = [NSDictionary dictionary];
        self.currentPage = 1;
        self.totalPage = 1;
        self.elements = [NSArray array].copy;
        self.profile = [JCProfile new];
        self.isMutableBackground = NO;
        self.multipleBackIndex = 0;
        self.margin = [NSArray array];
        self.localType = JCLocalType_Default;
        self.printMethodCode = @"0";
        self.printedProcessList = (NSArray<JCPrintRecordModel> *)[NSArray array];
        self.version = @"2.1.4";
        self.paccuracyName = 203;
        self.goodsList = @[];
        self.localBackground = @[];
        self.names = @[];
        self.labelNames = @[];
        self.inputAreas = @[];
        self.hasVipRes = NO;
        self.vip = NO;
        self.commodityTemplate = NO;
        self.isEdited = @"0";
        self.canvasRotate = 0;
        self.originTemplateId = @"";
        self.sourceOfIndustryTemplateId = @"";
        self.cloudTemplateId = @"";
        self.paperColor = @[@"0.0.0",@"230.0.18"];
        self.isPrintHistory = NO;
        //        self.dataSources = [NSMutableArray arrayWithArray:@[]];
        //        self.dataSource = [NSMutableArray arrayWithArray:@[]];
        self.dataSourceModifies = @{};
        self.modify = @{};
        self.bindInfo = @{};
        self.supportedEditors = @[];
        self.isCableLabel = NO;
    }
    return self;
}

- (void)setDataSources:(NSArray<NSDictionary *> *)dataSources
{
    _dataSources = dataSources;
    if([dataSources isKindOfClass:[NSArray class]] && dataSources.count > 0){
        _dataSource = dataSources;
        for (JCElementModel *element in _elements) {
          if([element.dataBind isKindOfClass:[NSArray class]] && element.dataBind.count > 0){
            [element fixValueExcel];
          }
        }
    }
}
-(void)setDataSource:(NSArray<NSDictionary *> *)dataSource
{
    _dataSource = dataSource;
    if([dataSource isKindOfClass:[NSMutableArray class]] && dataSource.count > 0){
      for (JCElementModel *element in _elements) {
        if([element.dataBind isKindOfClass:[NSArray class]] && element.dataBind.count > 0){
          [element fixValueExcel];
        }
      }
    }
}
//服务器下发字段的时候赋值
-(void)setDataSourceBindInfo:(NSDictionary *)dataSourceBindInfo
{
    _dataSourceBindInfo = dataSourceBindInfo;
    if([_dataSourceBindInfo isKindOfClass:[NSDictionary class]] && _dataSourceBindInfo.allKeys.count > 0){
        _bindInfo = dataSourceBindInfo;
    }
}
//服务器下发字段的时候赋值
-(void)setDataSourceModifies:(NSDictionary *)dataSourceModifies
{
    _dataSourceModifies = dataSourceModifies;
    if([_dataSourceModifies isKindOfClass:[NSDictionary class]] && _dataSourceModifies.allKeys.count > 0){
        _modify = _dataSourceModifies;
    }
}

//
- (NSDictionary *)getDefaultDataBindInfo{
    return @{@"total":@1,@"page":@1};
}

//
- (NSDictionary *)getDefaultDataSourceModifies{
    NSMutableDictionary *dataSourceModifies = [NSMutableDictionary dictionary];
    for (JCElementModel *elementModel in self.elements) {
        if(!STR_IS_NIL(elementModel.fieldName)){
            if([elementModel.fieldName isEqualToString:@"barcode"]){
                elementModel.value = @"A0";
            }
            if([elementModel.fieldName isEqualToString:@"name"]){
                elementModel.value = @"B0";
            }
            if([elementModel.fieldName isEqualToString:@"originPlace"]){
                elementModel.value = @"C0";
            }
            if([elementModel.fieldName isEqualToString:@"unit"]){
                elementModel.value = @"D0";
            }
            if([elementModel.fieldName isEqualToString:@"norm"]){
                elementModel.value = @"E0";
            }
            if([elementModel.fieldName isEqualToString:@"level"]){
                elementModel.value = @"F0";
            }
            if([elementModel.fieldName isEqualToString:@"retailPrice"]){
                elementModel.value = @"G0";
            }
            if([elementModel.fieldName isEqualToString:@"rushPrice"]){
                elementModel.value = @"H0";
            }
            if([elementModel.fieldName isEqualToString:@"priceOfficer"]){
                elementModel.value = @"I0";
            }
            elementModel.fieldName = @"";
            elementModel.dataBind = @[@"",@""];
            [dataSourceModifies setValue:@{@"0":@{@"useTitle":@NO,@"delimiter":@"："}} forKey:elementModel.elementId];
        }
    }
    return dataSourceModifies;
}

- (BOOL)isEqualByComparefunction:(NSString *)function withCurrentVersion:(NSString *)currentVersion {
    NSMutableArray *functionArray = [[function componentsSeparatedByString:@"."] mutableCopy];
    NSMutableArray *currentVersionArray = [[currentVersion componentsSeparatedByString:@"."] mutableCopy];
    int modifyCount = abs((int)(functionArray.count - currentVersionArray.count));
    if (functionArray.count > currentVersionArray.count) {
        for (int index = 0; index < modifyCount; index ++) {
            [currentVersionArray addObject:@"0"];
        }
    } else if (functionArray.count < currentVersionArray.count) {
        for (int index = 0; index < modifyCount; index ++) {
            [functionArray addObject:@"0"];
        }
    }
    for (int index = 0; index < functionArray.count; index++) {
        if ([currentVersionArray[index] integerValue] > [functionArray[index] integerValue]) {
            return YES;
        } else if ([currentVersionArray[index] integerValue] < [functionArray[index] integerValue]) {
            return NO;
        }
    }
    return NO;
}

//是否是pc新保存的模版数据
-(BOOL)isFromPcExcel{
    bool fromPcExcel = false;
    if(self.templateVersion.length > 0 && [self.dataSource isKindOfClass:[NSArray class]] && self.dataSource.count > 0 && (self.externalData == nil || self.externalData.allKeys.count == 0)){
        fromPcExcel = true;
//        if(self.profile.extrain.templateType.integerValue ==2){
//            fromPcExcel = false;
//        }
    }
    return fromPcExcel;
}

//是否是新商品模板
-(BOOL)isNewCommodityTemplate{
   return [JCExcelTransUtil isCommdityTemplateWith:self.dataSource];
}

/**
 * 判断当前模板是否为Excel表格模板
 * @return YES-是Excel模板，NO-不是Excel模板
 * @note 使用JCExcelTransUtil工具类判断dataSource
 */
-(BOOL)isNewExcelTemplate{
    return [JCExcelTransUtil isExcelTemplateWith:self.dataSource];
}


- (void)setThumbnail:(NSString *)thumbnail{
    if(STR_IS_NIL(thumbnail)){

    }
    _thumbnail = thumbnail;
}

- (void)setSourceOfIndustryTemplateId:(NSString *)industryTemplateId{
    _sourceOfIndustryTemplateId = industryTemplateId;
    _cloudTemplateId = industryTemplateId;
}

- (NSString *)sourceOfIndustryTemplateId{
    return _cloudTemplateId;
}

//yc to do
- (void)setElements:(NSArray<JCElementModel> *)elements{
    if(![elements isKindOfClass:[NSArray class]]) return;
    _elements = elements;
  if(([_dataSources isKindOfClass:[NSArray class]] && _dataSources.count > 0) || ([_dataSource isKindOfClass:[NSArray class]] && _dataSource.count > 0)){
      for (JCElementModel *element in elements) {
        if([element.dataBind isKindOfClass:[NSArray class]] && element.dataBind.count > 0){
          [element fixValueExcel];
        }
      }
    }
    NSMutableArray *elementIdArr = [NSMutableArray array];
    [elements enumerateObjectsUsingBlock:^(JCElementModel *elementModel, NSUInteger idx, BOOL * _Nonnull stop) {
        if(![elementIdArr containsObject:elementModel.elementId]){
            [elementIdArr addObject:elementModel.elementId];
        }else{
            elementModel.elementId = [NSString stringWithFormat:@"%@_%ld",elementModel.elementId,idx];
        }
    }];
}

- (NSArray *)paperColor{
    if(_paperColor.count <= 1){
        _paperColor = @[@"0.0.0",@"230.0.18"];
    }
    return _paperColor;
}

- (void)setExternalData:(NSDictionary *)externalData{
    if(externalData == nil){
        _externalData = @{};
    }else{
        _externalData = externalData;
    }
}

- (void)setTask:(NSDictionary *)task{
    if(task == nil){
        _task = @{};
    }else{
        _task = task;
    }
}

- (void)setRotate:(NSInteger)rotate{
    if(_rotate != 0  && _rotate <= 270 && rotate == 0){
        NSLog(@"出纸方向重制");
    }
    _rotate = rotate;
}

- (void)setPaccuracyName:(NSInteger)paccuracyName{
    if(paccuracyName <= 0){
        NSLog(@"精度设置错误");
        paccuracyName = 203;
    }
    _paccuracyName = paccuracyName;
}

- (void)setIsSelected:(BOOL)isSelected{
    _isSelected = isSelected;
}

- (void)setVip:(BOOL)vip{
    _vip = vip;
}

- (void)setHasVipRes:(BOOL)hasVipRes{
    _hasVipRes = hasVipRes;
    if(hasVipRes){
//        NSLog(@"有vip资源");
    }
}

- (BOOL)isCloudTemplate{
    BOOL isCloud = NO;
    if([self.profile.extrain.templateType isEqualToString:@"1"] || ([self.profile.extrain.templateType isEqualToString:@"2"] && (STR_IS_NIL(self.profile.extrain.userId) || !STR_IS_NIL(self.recentUserId)))){
        isCloud = YES;
    }
    return isCloud;
}

-(BOOL)isNewOrOldCommodityTemplate{
    return [self isBusinessTemplate] || [self isNewCommodityTemplate];
}

-(BOOL)templateHasUpdateWith:(JCTemplateData *)newTemplateDat{
    return ![self.profile.extrain.updateTime isEqualToString:newTemplateDat.profile.extrain.updateTime];
}

- (BOOL)hasLabelInfo{
    BOOL hasInfo = NO;
    if(!STR_IS_NIL(self.profile.extrain.labelId)){
        hasInfo = YES;
    }else{
        if(!STR_IS_NIL(self.requestBarCode)) hasInfo = YES;
    }
    return hasInfo;
}

- (NSString *)requestBarCode{
    NSString *barCode = self.profile.barcode;
    if(STR_IS_NIL(barCode)){
        for (NSString *code in self.profile.extrain.barcodeCategoryMap.allValues) {
            if(!STR_IS_NIL(code)){
                barCode = code;
            }
        }
    }
    return barCode;
}

- (BOOL)isBusinessTemplate{
    BOOL ret  = NO;

    for (JCElementModel *model in self.elements){
        if(!STR_IS_NIL(model.fieldName)){
            ret = YES;
            break;
        }
    }

    if([self.profile.extrain.templateType isEqualToString:@"2"]){
        ret = YES;
    }

    return ret;
}

- (NSDictionary *)usedFonts {
    return [self getCurrentFonts];
}

- (NSDictionary *)getNeedFonts {
    return _usedFonts;
}

- (void)setCurrentPage:(NSInteger)currentPage{
    _currentPage = currentPage;
}

- (NSString<Optional> *)localThumbnail{
  if(!STR_IS_NIL(_localThumbnail)){
    _localThumbnail = [self replaceSandBoxRealPathWith:_localThumbnail];
  }
  return _localThumbnail;
}

- (NSString *)replaceSandBoxRealPathWith:(NSString *)oldPath{
  // 获取当前沙盒 Documents 目录
  NSString *currentDocumentsPath = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES).firstObject;

  // 获取当前沙盒的 UUID 根路径（截取到 Documents）
  NSRange docRange = [currentDocumentsPath rangeOfString:@"/Documents"];
  NSString *currentUUIDPath = [currentDocumentsPath substringToIndex:docRange.location];

  // 获取旧路径中的 Documents 相对路径
  NSRange oldDocRange = [oldPath rangeOfString:@"/Documents"];
  NSString *relativePath = [oldPath substringFromIndex:oldDocRange.location];

  // 拼接新路径
  NSString *newPath = [currentUUIDPath stringByAppendingString:relativePath];

  NSLog(@"替换后的路径: %@", newPath);
  return newPath;
}

-(JCTemplateData *)refreshWithRfidData:(JCTemplateData *)data{
    JCTemplateData *templateRFIDData = data;
    templateRFIDData = [JCTMDataBindGoodsInfoManager templateDataWith:templateRFIDData goodInfo:nil];
    templateRFIDData.elements = self.elements;
    templateRFIDData.vip = self.vip;
    templateRFIDData.hasVipRes = self.hasVipRes;
    templateRFIDData.externalData = self.externalData;
    templateRFIDData.totalPage = self.totalPage;
    templateRFIDData.currentPage = self.currentPage;
    templateRFIDData.sourceOfIndustryTemplateId = self.sourceOfIndustryTemplateId;
    templateRFIDData.task = self.task;
    templateRFIDData.dataSource = self.dataSource;
    templateRFIDData.dataSourceModifies = self.dataSourceModifies;
    templateRFIDData.dataSourceBindInfo = self.dataSourceBindInfo;
    templateRFIDData.templateVersion = self.templateVersion;
    templateRFIDData.consumableTypeTextId = self.consumableTypeTextId;
    templateRFIDData.layoutSchema = self.layoutSchema;
    templateRFIDData.supportedEditors = self.supportedEditors;
    templateRFIDData.modify = self.modify;
    templateRFIDData.bindInfo = self.bindInfo;

    if(self.canvasRotate != 0){
        if((self.canvasRotate == 90 || self.canvasRotate == 270)){
            float width = templateRFIDData.width;
            templateRFIDData.width = templateRFIDData.height;
            templateRFIDData.height = width;
        }
        templateRFIDData.canvasRotate = self.canvasRotate;
        NSInteger rotate = templateRFIDData.rotate + self.canvasRotate/90 * 270;
        templateRFIDData.rotate = rotate%360;
    }
    templateRFIDData.goodsList = self.goodsList;

    [DrawBoardInfo updateInfoWith:templateRFIDData];
    if (DrawBoardInfo.template_width_mm == 0) {
        DrawBoardInfo.template_width_mm = self.width;
    }
    if (DrawBoardInfo.boardWidth == 0) {
        DrawBoardInfo.boardWidth = SCREEN_WIDTH-45;
    }

    return templateRFIDData;
}

- (NSDictionary *)getCurrentFonts {
    NSString *fontPath = [NSString stringWithFormat:@"%@/font",DocumentsFontPath];
    NSArray *fontNameArr = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:fontPath error:nil];
    NSMutableDictionary *temp = [NSMutableDictionary dictionaryWithCapacity:fontNameArr.count];
    [fontNameArr enumerateObjectsUsingBlock:^(NSString *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        // 把 name.ttf 拆分出
        NSArray *componets = [obj componentsSeparatedByString:@"."];
        NSString *fontName = componets.firstObject;
        if (obj && fontName && fontName.length > 0) {
            [temp setObject:obj forKey:fontName];
        }
    }];
    [temp setObject:[NSString stringWithFormat:@"%@.ttf",text_default_font_code] forKey:@"fontDefault"];
    return temp;
}

+ (BOOL)propertyIsOptional:(NSString*)propertyName
{
  return YES;
}

- (void)resetElementIndex{
    NSMutableArray<JCElementModel*> *elements = ( NSMutableArray<JCElementModel*> *)[NSMutableArray array];
    NSInteger index = 0;

    //边框类型的图片和图形放到最底层
    for (JCElementModel *element in self.elements) {
        if(([element.type isEqualToString:@"image"] && [element.materialType isEqualToString:@"2"]) || [element.type isEqualToString:@"graph"]) {
            element.zIndex = index;
            [elements addObject:element];
            index ++;
        }
    }

//    for (JCElementModel *element in self.elements) {
//        if([element.type isEqualToString:@"graph"]){
//            element.zIndex = index;
//            [elements addObject:element];
//            index ++;
//        }
//    }

    for (JCElementModel *element in self.elements) {
        if(([element.type isEqualToString:@"table"] || [element.type isEqualToString:@"image"]) && ![element.materialType isEqualToString:@"2"]) {
            element.zIndex = index;
            [elements addObject:element];
            index ++;
        }
    }
    for (JCElementModel *element in self.elements) {
        if(![element.type isEqualToString:@"graph"] && ![element.type isEqualToString:@"table"]
           && ![element.type isEqualToString:@"image"]){
            element.zIndex = index;
            [elements addObject:element];
            index ++;
            if([element.type isEqualToString:@"text"] && STR_IS_NIL(element.fieldName)
               && STR_IS_NIL(element.value) && element.dataBind.count == 0){
                element.value = XY_LANGUAGE_TITLE_NAMED(@"app00364", @"双击文本框编辑");
            }
        }
    }
    NSArray *correctElement = elements;
    self.elements = correctElement;
}

- (void)resetElementValue{
    for (JCElementModel *element in self.elements) {
        if([element.type isEqualToString:@"text"] && STR_IS_NIL(element.fieldName)
           && STR_IS_NIL(element.value) && element.dataBind.count == 0){
            element.value = XY_LANGUAGE_TITLE_NAMED(@"app00364", @"双击文本框编辑");
        }
    }
}
//使用标签进入新画板 创建默认文本元素
- (void)createDefaultTextModel{
    JCElementModel *model = [JCElementModel new];
    model.elementId = [NSString jk_UUID];
    model.elementVersion = @"";
    model.type = @"text";
    model.value = XY_LANGUAGE_TITLE_NAMED(@"app00364",@"双击文本框编辑");
    model.fontFamily = XY_LANGUAGE_TITLE_NAMED(text_default_font_name, @"默认字体");
    model.fontCode = text_default_font_code;
    // 6.0.4版本更换为顶对齐
    model.textAlignVertical = 0;
    model.textAlignHorizonral = 1;
    model.lineMode = JCTextLineModeWidthFixed;
    model.lineBreakMode = 1;
    model.letterSpacing = 0;
    model.lineSpacing = 0;
    model.fontStyle = @[];
    model.typesettingMode = 1;
    model.typesettingParam = @[@0,@180];
    model.boxStyle = @"auto-width";
    float fontSize = 3.2;
    float x = 0;
    float y = self.height / 2 - 3.75 / 2;
    float width = self.width;
    float height = 3.75;
    float singleLineWidthWith5 = 24;
    float singleLineHeightWith5 = 26.25;
    float singleLineWidthWith4 = 37.84;
    if(width > singleLineWidthWith4){
        fontSize = 4.2;
        height = 5.76;
        y = (self.height - height)/2;

    }else if(width < singleLineWidthWith5){
        if(self.height > singleLineWidthWith5){
            width = 3.75;
            height = singleLineHeightWith5;
            x = (self.width - width)/2;
            y = (self.height - singleLineHeightWith5)/2;
        }else{
            width = 17.47;
            height = 2.68;
            x = (self.width - width) > 0?(self.width - width)/2:0;
            y = (self.height - height)/2;
            fontSize = 2.3;
        }
    }
    model.x = x;
    model.y = y;
    model.width = width;
    model.height = height;
    model.fontSize = fontSize;
    self.elements = (NSArray<JCElementModel> *)@[model];
}

/** 更新本地化的字段 */
- (void)updateLocalProperty {
    for (JCElementModel *element in self.elements) {
        [element updateLocalProperty];
    }
}

#pragma mark - yymodel protocol
+(JSONKeyMapper *)keyMapper
{
  return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:@{@"idStr":@"id",@"des":@"description",@"localThumbnail":@"local_thumb"}];
}

YYModel_Encode_And_Copy_Method_Implementation

- (id)copyWithZone:(NSZone *)zone {
    typeof(self) newOne = [self yy_modelCopy]; // 浅拷贝
    newOne.profile = self.profile.copy;
    return newOne;

}

- (void)dealloc {

}

@end

@implementation JCProfile
- (instancetype)init {
    self = [super init];
    if (self) {
        self.barcode = @"";
        self.keyword = @"";
        self.hardwareSeriesId = @"";
        self.hardwareSeriesName = @"";
        self.machineName = @"";
        self.machineId = @"";
        self.extrain = [JCProfileExtrain new];
    }
    return self;
}

+(BOOL)propertyIsOptional:(NSString*)propertyName
{
  return YES;
}

YYModel_Encode_And_Copy_Method_Implementation
- (id)copyWithZone:(NSZone *)zone {
    typeof(self) newOne = [self yy_modelCopy]; // 浅拷贝
    newOne.extrain = self.extrain.copy;
    return newOne;

}
@end

@implementation JCProfileExtrain
+(BOOL)propertyIsOptional:(NSString*)propertyName
{
  return YES;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        self.isCustom = NO;
        self.folderId = @"0";
        self.adaptPlatformCode = @"";
        self.adaptPlatformName = @"";
        self.userId = @"";
        self.industryId = @"";
        self.commodityCategoryId = @"";
        self.downloadCount = @"";
        self.isDelete = NO;
        self.createTime = @"";
        self.updateTime = @"";
        self.isHot = NO;
        self.sortDependency = @"";
        self.clickNum = @"";
        self.templateType = @"0";
        self.templateClass = @"0";
        self.isPrivate = NO;
        self.amazonCodeBeijing = @"";
        self.amazonCodeWuhan = @"";
        self.sparedCode = @"";
        self.virtualBarCode = @"";
        self.isNewPath = NO;
        self.isMobileTemplete = NO;
        self.resourceVersion  = @"";
        self.phone = @"";
        self.barcodeCategoryMap = [NSMutableDictionary dictionary];
        self.materialModelSn = @"";
    }
    return self;
}

- (void)setUserId:(NSString *)userId{
    if(!STR_IS_NIL(userId)){
//        NSLog(@"设置useriD");
    }
    _userId = userId;
}

- (void)setTemplateType:(NSString *)templateType{
    _templateType = templateType;
    if(templateType.integerValue == 2){

    }
}

- (void)setBarcodeCategoryMap:(NSDictionary *)barcodeCategoryMap{
    if([barcodeCategoryMap isKindOfClass:[NSString class]] || barcodeCategoryMap.allKeys.count == 0){
        _barcodeCategoryMap = [NSMutableDictionary dictionary];
        return;
    }
    _barcodeCategoryMap =  barcodeCategoryMap;
}

YYModel_Encode_And_Copy_Method_Implementation
- (id)copyWithZone:(NSZone *)zone {
    typeof(self) newOne = [self yy_modelCopy]; // 浅拷贝
    return newOne;

}

@end

