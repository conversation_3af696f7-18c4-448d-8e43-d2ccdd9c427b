//
//  JCNPSWebviewAlert.h
//  Runner
//
//  Created by ch<PERSON><PERSON> on 2024/3/6.
//

#import <UIKit/UIKit.h>
#import "MMPopupView.h"
#define NPSMiniWebKey @"NPSMiniProgramWebKey"  // 小程序专用
#define NPSWebKey @"NPSWebKey"
#define NPSWebVIPKey @"NPSWebVIPKey"
NS_ASSUME_NONNULL_BEGIN

@interface JCNPSWebviewAlert : UIView
 
-(instancetype)initWithFrame:(CGRect)frame webKey:(NSString *)webKey sku:(NSString *)sku source:(NSString *)source;

@property(nonatomic,copy)XYNormalBlock closeClick;

/// 小程序id
@property(nonatomic,copy)NSString *uniAppId;

- (void)show;

-(void)hide;

@end


NS_ASSUME_NONNULL_END
