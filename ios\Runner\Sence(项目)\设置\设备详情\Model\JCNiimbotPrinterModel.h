//
//  JCNiimbotPrinterModel.h
//  Runner
//
//  Created by Chance on 2024/11/12.
//

#import <JSONModel/JSONModel.h>
#import "JCNiimbotRFIDInfo.h"
#import "JCNiimbotPrinterPrintProgressInfo.h"
#import "JCNiimbotFirmwareUpgradeInfo.h"


NS_ASSUME_NONNULL_BEGIN

@protocol JCNiimbotRFIDInfo;

@interface JCNiimbotPrinterModel : JSONModel

/// 打印机硬件编码
@property(nonatomic, copy) NSString<Optional> *code;

/// 打印机类型
@property(nonatomic, copy) NSString<Optional> *type;

/// 打印机状态
@property(nonatomic, copy) NSString<Optional> *status;

/// 打印机SN号
@property(nonatomic, copy) NSString<Optional> *sn;

/// 打印机MAC地址
@property(nonatomic, copy) NSString<Optional> *macAddress;

/// 电量数值 0-100
@property(nonatomic, copy) NSString<Optional> *electricity;

/// 1-盒盖打开，0-盒盖关闭
@property(nonatomic, copy) NSString<Optional> *coverStatus;

/// 1-标签未安装，0-标签已安装
@property(nonatomic, copy) NSString<Optional> *paperStatus;

/// 1-读取到rfid标签，0-未读取到rfid标签
@property(nonatomic, copy) NSString<Optional> *paperRfidStatus;

/// 1-碳带未安装，0-碳带已安装
@property(nonatomic, copy) NSString<Optional> *ribbonStatus;

/// 1-读取到rfid碳带，0-未读取到rfid碳带
@property(nonatomic, copy) NSString<Optional> *ribbonRfidStatus;

/// 耗材RFID信息
@property(nonatomic, strong) NSArray<JCNiimbotRFIDInfo, Optional>* consumablesRFID;

/// 是否正在读取耗材RFID数据
@property(nonatomic, assign) BOOL readingConsumablesRFID;

/// 打印进度信息
@property(nonatomic, strong) JCNiimbotPrinterPrintProgressInfo<Optional> *progressInfo;

/// 固件更新进度信息
@property(nonatomic, strong) JCNiimbotFirmwareUpgradeInfo<Optional> *firmwareUpgradeInfo;

/// 硬件版本号
@property(nonatomic, copy) NSString<Optional> *hardwareVersion;

/// 固件版本号
@property(nonatomic, copy) NSString<Optional> *firmwareVersion;

/// 打印模式
@property(nonatomic, copy) NSString<Optional> *printMode;

/// 打印浓度
@property(nonatomic, copy) NSString<Optional> *printDensity;

/// 纸张类型
@property(nonatomic, copy) NSString<Optional> *paperType;

/// 打印速度
@property(nonatomic, copy) NSString<Optional> *printSpeed;

/// 打印机语言类型
@property(nonatomic, copy) NSString<Optional> *lang;

/// 打印机自动关机时间级别
@property(nonatomic, copy) NSString<Optional> *autoShutDownTimeLevel;

/// 打印质量支持
@property(nonatomic, strong) NSArray<Optional> *printQualitySupport;

/// 打印质量模式
@property(nonatomic, copy) NSString<Optional> *printQualityMode;

/// 打印机支持的颜色模式
@property(nonatomic, strong) NSArray<NSNumber *> *colorModeSupport;

/// 打印机已配置的WiFi名称
@property(nonatomic, copy) NSString<Optional> *configuredWiFiName;

/// 打印机打印倍率
@property(nonatomic, copy) NSString<Optional> * multiple;

/// 防伪密钥
@property(nonatomic, copy) NSString<Optional> *antiCounterfeitKey;

/// 区域码
@property(nonatomic, copy) NSString<Optional> *areaCode;

/// 设备别名 (Flutter新增参数)
@property(nonatomic, copy) NSString<Optional> *alias;

/// 是否支持设备别名修改 (Flutter新增参数)
@property(nonatomic, assign) BOOL supportAlias;

@end

NS_ASSUME_NONNULL_END
