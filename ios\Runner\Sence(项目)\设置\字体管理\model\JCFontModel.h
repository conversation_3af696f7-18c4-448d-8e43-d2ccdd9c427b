//
//  JCFontModel.h
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2018/6/16.
//  Copyright © 2018年 xiaoyao. All rights reserved.
//

#import "XYBaseModel.h"

@protocol JCFontModel
@end

@interface JCFontModel : XYBaseModel
@property (nonatomic, strong) NSArray <Optional,JCFontModel> *zh;
@property (nonatomic, strong) NSArray <Optional,JCFontModel> *en;
@property (nonatomic, copy) NSString <Optional> *langType;
@property (nonatomic, copy) NSString <Optional> *dbId;
@property (nonatomic, copy) NSString <Optional> *xyid;
//@property (nonatomic, copy) NSString <Optional> *version;
@property (nonatomic, copy) NSString <Optional> *url;
@property (nonatomic, copy) NSString <Optional> *localPath;
@property (nonatomic, copy) NSString <Optional> *release_time;
@property (nonatomic, copy) NSString <Optional> *release_description;
@property (nonatomic, copy) NSString <Optional> *share_title;
@property (nonatomic, copy) NSString <Optional> *share_icon;
@property (nonatomic, copy) NSString <Optional> *type;
@property (nonatomic, copy) NSString <Optional> *platform;
@property (nonatomic, copy) NSString <Optional> *machine_id;
@property (nonatomic, copy) NSString <Optional> *fontCode;
@property (nonatomic, copy) NSString <Optional> *name;
@property (nonatomic, copy) NSString <Optional> *fileName;
@property (nonatomic, copy) NSString <Optional> *fontRight;
@property (nonatomic, copy) NSString <Optional> *position;
@property (nonatomic, copy) NSString <Optional> *status;
@property (nonatomic, copy) NSString <Optional> *sort;
@property (nonatomic, copy) NSString <Optional> *font_type;
@property (nonatomic, copy) NSString <Optional> *isVip;
@property (nonatomic, copy) NSString <Optional> *percentForDownLoad;
@property (nonatomic, copy) NSString <Optional> *typeDownloadState;
@property (nonatomic, copy) NSString <Optional> *thumbnailUrl;
@property (nonatomic, copy) NSString <Optional> *language;
@property (nonatomic, copy) NSString <Optional> *classifyId;
@property (nonatomic, copy) NSString <Optional> *classifyName;
@property (nonatomic, copy) NSString <Optional> *priority;
@property (nonatomic, copy) NSString <Optional> *fontThumbnailSuccess;
@property (nonatomic, copy) NSString <Optional> *agreementUrl;
@property (nonatomic, copy) NSString <Optional> *agreementName;
@property (nonatomic, copy) NSString <Optional> *copyright;
@property (nonatomic, copy) NSString <Optional> *userId;
@property (nonatomic, copy) NSString <Optional> *usageDatetime;
@property (nonatomic, copy) NSString <Optional> *deleted;
/// 字体最后被使用的时间戳: 用来显示排序
@property(nonatomic,copy)NSString <Optional> *lastUseTimeStamp;
//是否已经下载
@property (nonatomic, copy) NSString <Optional> *isDownload;
// 是否正在下载
@property (nonatomic, assign) BOOL  isDownLoading;
// 是否已经下载
@property (nonatomic, assign) BOOL  hasDownLoad;

@property (nonatomic, assign) BOOL  isNeedRefreshDownLoad;
/// 是否显示系统名称
@property (nonatomic, assign) BOOL showSystemName;

@property (nonatomic, copy) NSString <Optional> *width;
@property (nonatomic, copy) NSString <Optional> *downloadTime;

+ (JCFontModel *)defaultCnFontWithLang:(NSString *)language;

+ (JCFontModel *)defaultEnFont;

- (BOOL)fontHasDownload;

+ (BOOL)fontHasDownLoad:(NSString *)fontCode;

- (BOOL)isEqualTo:(JCFontModel *)otherModel;

+ (NSString *)dowloadTimeForCode:(NSString *)fontCode;

+ (void)saveTime:(NSString *)time fontCode:(NSString *)fontCode;

+ (NSMutableArray *)getDownLoadFontCodeArr;

+ (void)saveProgress:(CGFloat)progress fontCode:(NSString *)fontCode;

+ (CGFloat)progressForCode:(NSString *)fontCode;
@end
