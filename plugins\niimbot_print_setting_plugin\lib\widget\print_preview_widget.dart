import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;
import 'dart:typed_data';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart';
import 'package:niimbot_print_setting_plugin/utils/file_back_image.dart';
import 'package:niimbot_print_setting_plugin/utils/page_view_util.dart' as PageViewUtil;
import 'package:niimbot_print_setting_plugin/utils/print_preview_image_util.dart';
import 'package:niimbot_print_setting_plugin/utils/svg_icon.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/template/template_data_source_range.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/rfid_bind/rfid_info_manager.dart';
import 'package:niimbot_template/rfid_bind/rfid_repository.dart';

import '../model/update_field_manager.dart';

class PrintPreviewWidget extends StatefulWidget {
  PrintSettingLogic logic;

  PrintPreviewWidget(this.logic, {Key? key}) : super(key: key);

  @override
  _PrintPreviewWidgetState createState() => _PrintPreviewWidgetState(logic);
}

class _PrintPreviewWidgetState extends State<PrintPreviewWidget> with SingleTickerProviderStateMixin {
  PrintSettingLogic logic;
  List<double> areaList = [];
  bool liveCodeErrorToast = false;
  String thumbnail = "";
  String localThumbnail = "";
  String count = "";
  Uint8List? imageData;
  Uint8List? imageBackGroundData;
  PageViewUtil.PageController? _pageController;
  bool isInit = true;
  var previeWidth;
  var isPreview = false;
  AnimationController? _controller;
  Animation<double>? _animation;

  _PrintPreviewWidgetState(this.logic);

  Future<void> _setPreview(int selectIndex) async {
    if(logic.parameter!.templateMoudleNew == null){
      return;
    }
    if (logic.taskType == PrintTaskType.batch) {
      String id = logic.parameter!.batchtIds![selectIndex]["id"];
      count = logic.parameter!.batchtIds![selectIndex]["printPage"].toString();
      Map<String, dynamic> detail = await logic.channel.getBatchPrintTemplate(id);
      thumbnail = detail["thumbnail"];
      localThumbnail = detail["local_thumb"] ?? "";
      logic.parameter!.templateMoudle = detail;
      liveCodeErrorToast = await liveCodeIsDelete(detail);
    } else {
      // TemplateData templateData = logic.parameter!.templateMoudleNew!;
      TemplateData cuttentTemplateData = logic.parameter!.templateMoudleNew!.copyWith();
      if (logic.parameter!.pdfBindInfo != null) {
        List<BaseElement> baseElements = [];
        for (var element in cuttentTemplateData.elements) {
          if (element is ImageElement &&
              logic.parameter?.pdfBindInfo != null &&
              logic.parameter!.pdfBindInfo!.elementIsBindPdf(element.id)) {
            List<String> imagePaths = logic.parameter!.pdfBindInfo!.getPDFImagePathsWithElementId(element.id);
            String localImagePath = selectIndex >= imagePaths.length ? "" : imagePaths[selectIndex];
            ImageElement imageElement = element.copyWith(localImageUrl: localImagePath);
            baseElements.add(imageElement);
          } else {
            baseElements.add(element);
          }
        }
        cuttentTemplateData = logic.parameter!.templateMoudleNew!.copyWith(elements: baseElements);
      }
      bool isSupport16Gray = logic.checkCurrentIsSupportGray16(cuttentTemplateData);
      List<Color?>? RfidColor = _getPrintColor(isSupport16Gray);
      Uint8List? dangerImageData;
      if (RfidColor?.length != 2) {
        Uint8List onValue = await PrintPreviewImageUtil.generateContentPreviewImage(cuttentTemplateData,
            page: selectIndex + 1, firstRfidColor: isSupport16Gray ? null : RfidColor?[0], secondRfidColor: null, isSupportGray16: isSupport16Gray);
        liveCodeErrorToast = await liveCodeIsDelete(cuttentTemplateData.toJson());
        imageData = onValue;
        imageBackGroundData = await PrintPreviewImageUtil.generateBackgroundPreviewImage(cuttentTemplateData,
            page: selectIndex + 1, firstRfidColor: isSupport16Gray ? null : RfidColor?[0], secondRfidColor: null);
        if (logic.taskType == PrintTaskType.miniApp) {
          dangerImageData = await PrintPreviewImageUtil.generatePreviewImage(cuttentTemplateData,
              page: selectIndex + 1,
              firstRfidColor: isSupport16Gray ? null : RfidColor?[0],
              secondRfidColor: null, isSupportGray16: isSupport16Gray,correctRatio: 15);
        }
      } else {
        Uint8List onValue = await PrintPreviewImageUtil.generatePreviewImage(cuttentTemplateData,
            page: selectIndex + 1,
            firstRfidColor: isSupport16Gray ? null : RfidColor?[0],
            secondRfidColor: RfidColor?[1], isSupportGray16: isSupport16Gray,correctRatio: 15);
        liveCodeErrorToast = await liveCodeIsDelete(cuttentTemplateData.toJson());
        imageData = onValue;
        dangerImageData = onValue;
        imageBackGroundData = await PrintPreviewImageUtil.generateBackgroundPreviewImage(cuttentTemplateData,
            page: selectIndex + 1,
            firstRfidColor: isSupport16Gray ? null : RfidColor?[0],
            secondRfidColor: RfidColor?[1]);
      }
      if (logic.taskType == PrintTaskType.miniApp) {
        String base64String = base64Encode(dangerImageData!);
        logic.channel.sendCapGenerateTemplatePreviewEvent(base64String);
      }
      _controller!.forward();
    }
    logic.update([UpdateFieldManager.previewCount]);
  }

  int parseStringToInt(String input) {
    final number = double.tryParse(input) ?? 1.0;
    int copies = number.round();
    if(copies < 1){
      copies = 1;
    }
    return math.min(copies, 999);
  }


  /// 获取打印颜色
  List<Color?>? _getPrintColor(bool isSupportGray16) {
    Map<String, dynamic> colorInfo = logic.channel.getPaperRibbonColor();
    String paperColor = colorInfo["paperColor"] ?? "";
    String ribbonColor = colorInfo["ribbonColor"] ?? "";
    List<int> rgb = [];
    if (ribbonColor.isNotEmpty && ribbonColor.split(",").length == 2) {
      List<Color?> ribbonDoubleColor = [];
      ribbonColor.split(",").forEach((action) {
        if (action.isNotEmpty && action.split(".").length == 3) {
          List<int> ribbonRgb = action.split(".").map((element) {
            return int.parse(element);
          }).toList();
          ribbonDoubleColor.add(Color.fromARGB(255, ribbonRgb[0], ribbonRgb[1], ribbonRgb[2]));
        }
      });
      return ribbonDoubleColor;
    } else {
      if (ribbonColor.isNotEmpty && ribbonColor.split(".").length == 3) {
        rgb = ribbonColor.split(".").map((element) {
          return int.parse(element);
        }).toList();
      } else if (paperColor.isNotEmpty && paperColor.split(".").length == 3) {
        rgb = paperColor.split(".").map((element) {
          return int.parse(element);
        }).toList();
        if (paperColor == '159.160.160' && !isSupportGray16) {
          rgb = [0, 0, 0];
        }
      }
      if (rgb.isNotEmpty) {
        return [Color.fromARGB(255, rgb[0], rgb[1], rgb[2])];
      }
    }
    return null;
  }

  Future<void> getPrintCarbonColor() async {
    if (logic.parameter!.templateMoudleNew == null) {
      return;
    }
    var deviceMap = await logic.channel.getConnectDevice();
    if (deviceMap != null) {
      logic.style.isCarbon = deviceMap["isCarbon"] is int ? deviceMap["isCarbon"] == 1 : deviceMap["isCarbon"] ?? false;
      logic.style.carbonColor = deviceMap["carbonColors"] ?? "";
      logic.style.isConnectDevice = deviceMap["connected"] == 1;
    } else {
      logic.style.isConnectDevice = false;
    }
  }

  liveCodeIsDelete(Map<String, dynamic> templateJson) async {
    return await logic.channel.liveCodeIsDelete(templateJson);
  }

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(seconds: 1),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller!);
    int currentPage = logic.parameter!.templateMoudle?["currentPage"] ?? 1;
    logic.style.pageIndex = (currentPage < 1 ? 1 : currentPage) - 1;
    _pageController =
        PageViewUtil.PageController(initialPage: logic.style.pageIndex, keepPage: true, viewportFraction: 1);
    logic.stream.listen((data) async {
      if (data["action"] == "printerConnectState") {
        logic.style.isConnectDevice = data["printerConnectState"]["connected"] == 1;
        // 延迟1秒后重新调用getDevice函数以更新设备信息，确保最新数据
        if (logic.style.isConnectDevice) {
          Future.delayed(Duration(milliseconds: 1000), () {
            getPrintCarbonColor().then((onValue) {
              logic.channel.getPrinterLabelData().then((onValue){
                _getStackHeight();
                logic.update([UpdateFieldManager.preview]);
              });
            });
          });
        } else {
          _pageController?.animateToPage(logic.style.pageIndex,
              duration: Duration(milliseconds: 10), curve: Curves.ease);
          if (logic.taskType == PrintTaskType.batch) {
            count = logic.parameter!.batchtIds![logic.style.pageIndex]["printPage"].toString();
            logic.update([UpdateFieldManager.previewCount]);
          }
        }
      } else if (data["action"] == "refreshTemplate") {
        //  logic.isMultipleTemplates();
        getPrintCarbonColor().then((onValue) {
          if (data["changPageIndex"]) {
            // _pageController.
            _pageController?.animateToPage(logic.style.pageIndex,
                duration: Duration(milliseconds: 10), curve: Curves.ease);
            if (logic.taskType == PrintTaskType.batch) {
              count = logic.parameter!.batchtIds![logic.style.pageIndex]["printPage"].toString();
              logic.update([UpdateFieldManager.previewCount]);
            }
          } else {
            logic.update([UpdateFieldManager.preview]);
          }
        });
      } else if (data["action"] == "refreshTemplatePreview" ||
          data["action"] == "setFlutterlabelData" ||
          data['action'] == 'replaceLabelEvent') {
        _pageController?.animateToPage(logic.style.pageIndex, duration: Duration(milliseconds: 10), curve: Curves.ease);
        if (logic.taskType == PrintTaskType.batch) {
          count = logic.parameter!.batchtIds![logic.style.pageIndex]["printPage"].toString();
        }
        logic.update([UpdateFieldManager.preview]);
      } else if (data['action'] == 'currentPrintColor') {
        _updateRfidColor();
      } else if (data['action'] == 'paperSupportColors') {
        _updateRfidColor();
      }
    });

    ///处理流水线在预览切换时因为PageView缓存机制导致的页码错乱问题
    // Timer(Duration(milliseconds: 1000), () {
    //   setState(() {
    //     isInit = false;
    //   });
    // });
  }

  _updateRfidColor() async {
    var selectIndex = logic.style.pageIndex;
    TemplateData cuttentTemplateData = logic.parameter!.templateMoudleNew!.copyWith();
    if (logic.parameter!.pdfBindInfo != null) {
      List<BaseElement> baseElements = [];
      for (var element in cuttentTemplateData.elements) {
        if (element is ImageElement &&
            logic.parameter?.pdfBindInfo != null &&
            logic.parameter!.pdfBindInfo!.elementIsBindPdf(element.id)) {
          List<String> imagePaths = logic.parameter!.pdfBindInfo!.getPDFImagePathsWithElementId(element.id);
          String localImagePath = selectIndex >= imagePaths.length ? "" : imagePaths[selectIndex];
          ImageElement imageElement = element.copyWith(localImageUrl: localImagePath);
          baseElements.add(imageElement);
        } else {
          baseElements.add(element);
        }
      }
      cuttentTemplateData = logic.parameter!.templateMoudleNew!.copyWith(elements: baseElements);
    }
    bool isSupport16Gray = logic.checkCurrentIsSupportGray16(cuttentTemplateData);
    List<Color?>? RfidColor = _getPrintColor(isSupport16Gray);

    if (RfidColor?.length != 2) {
      Uint8List onValue = await PrintPreviewImageUtil.generateContentPreviewImage(cuttentTemplateData,
          page: selectIndex + 1, firstRfidColor: isSupport16Gray ? null : RfidColor?[0], secondRfidColor: null, isSupportGray16: isSupport16Gray);
      liveCodeErrorToast = await liveCodeIsDelete(cuttentTemplateData.toJson());
      imageData = onValue;
      imageBackGroundData = await PrintPreviewImageUtil.generateBackgroundPreviewImage(cuttentTemplateData,
          page: selectIndex + 1, firstRfidColor: isSupport16Gray ? null : RfidColor?[0], secondRfidColor: null);
    } else {
      Uint8List onValue = await PrintPreviewImageUtil.generatePreviewImage(cuttentTemplateData,
          page: selectIndex + 1,
          firstRfidColor: isSupport16Gray ? null : RfidColor?[0],
          secondRfidColor: RfidColor?[1], isSupportGray16: isSupport16Gray);
      liveCodeErrorToast = await liveCodeIsDelete(cuttentTemplateData.toJson());
      imageData = onValue;
      imageBackGroundData = await PrintPreviewImageUtil.generateBackgroundPreviewImage(cuttentTemplateData,
          page: selectIndex + 1,
          firstRfidColor: isSupport16Gray ? null : RfidColor?[0],
          secondRfidColor: RfidColor?[1]);
    }
    if (logic.taskType == PrintTaskType.miniApp) {
      String base64String = base64Encode(imageData!);
      logic.channel.sendCapGenerateTemplatePreviewEvent(base64String);
    }
    logic.update([UpdateFieldManager.previewImage]);
  }

  _getStackHeight() {
    if (previeWidth != 0) {
      areaList = logic.channel.getPrintArea(
          logic.parameter!.templateMoudle!['width'].toDouble(),
          logic.parameter!.templateMoudle!['height'].toDouble(),
          logic.parameter!.templateMoudle!['consumableType'].toString(),
          logic.parameter!.templateMoudle!['paperType'].toString(),
          logic.parameter!.templateMoudle!['rotate'].toInt(),
          previeWidth.toInt(),
          logic.parameter!.templateMoudle!['cableDirection'],
          logic.parameter!.templateMoudle!['cableLength'].toDouble(),
          logic.parameter!.templateMoudle!['canvasRotate'].toInt());
    }
  }

  @override
  void dispose() {
    _pageController?.dispose();
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: GetBuilder<PrintSettingLogic>(
          id: UpdateFieldManager.preview,
          builder: (logic) {
            return Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                GetBuilder<PrintSettingLogic>(
                    id: UpdateFieldManager.previewImage,
                    builder: (logic) {
                      return _direction();
                    }),
                GetBuilder<PrintSettingLogic>(
                    id: UpdateFieldManager.previewImage,
                    builder: (logic) {
                      return _buildRFIDWidget(context, logic.style.pageIndex);
                    }),
                _previewMonitor(),
                GetBuilder<PrintSettingLogic>(
                    id: UpdateFieldManager.previewCount,
                    builder: (logic) {
                      return liveCodeErrorToast ? _liveCodeError() : SizedBox.shrink();
                    }),
                GetBuilder<PrintSettingLogic>(
                    id: UpdateFieldManager.device,
                    builder: (logic) {
                      return _carbon();
                    }),
                GetBuilder<PrintSettingLogic>(
                    id: UpdateFieldManager.previewCount,
                    builder: (logic) {
                      return _countNumber();
                    }),
                logic.parameter?.customData?["batchGoodsList"] != null &&
                        logic.parameter?.customData?["batchGoodsList"]!.isNotEmpty
                    ? Container()
                    : _buildChangePrintDataWidget(logic),
              ],
            );
          }),
    );
  }

  Widget _buildChangePrintDataWidget(PrintSettingLogic logic) {
    if (logic.parameter?.taskType == PrintTaskType.canvas ||
        logic.parameter?.taskType == PrintTaskType.batch ||
        logic.parameter?.taskType == PrintTaskType.printSceneHistory ||
        logic.parameter?.taskType == PrintTaskType.folderShare) {
      return const SizedBox.shrink();
    }
    bool changeDataVisibile = false;
    TemplateData? templateData = logic.parameter?.templateMoudleNew;
    bool isExcelTemplate = logic.isExcelTemplate();
    bool isGoodTemplate = logic.isGoodTemplate();
    if (isGoodTemplate) {
      changeDataVisibile = true;
    } else if (isExcelTemplate) {
      changeDataVisibile = logic.isConnected() || logic.hasExcelFileCache;
    } else {
      changeDataVisibile = logic.isAllTextElement();
    }
    if (!changeDataVisibile) {
      return const SizedBox.shrink();
    }
    String changePrintDataText = "";
    int jumpType = 0;
    if (isExcelTemplate) {
      jumpType = 1;
      changePrintDataText = logic.getI18nString("app100001721", "已选\$条数据", param: [templateData!.totalPage.toString()]);
      logic.channel.trackEvent("show", "024_339");
    } else if (isGoodTemplate) {
      logic.channel.trackEvent("show", "024_354");
      jumpType = 2;
      int goodsCount = templateData!.totalPage;
      // if((templateData.dataSources??[]).isNotEmpty)
      // {
      //     TemplateDataSource dataSource = templateData.dataSources![0];
      //     if(dataSource.rowData.length > 1 && goodsCount != dataSource.rowData.length - 1){
      //       goodsCount = dataSource.rowData.length - 1;
      //     }
      //     logic.style.pageMax = goodsCount;
      //     logic.printData.pageEnd = goodsCount;
      // }

      changePrintDataText = logic.getI18nString("app100001722", "已选\$个商品", param: [goodsCount.toString()]);
    } else {
      logic.channel.trackEvent("show", "024_338");
      jumpType = 3;
      changePrintDataText = logic.getI18nString("app100001723", "更改内容");
    }
    return GestureDetector(
      onTap: () {
        if (isExcelTemplate) {
          logic.channel.trackEvent("click", "024_339");
        } else if (isGoodTemplate) {
          logic.channel.trackEvent("click", "024_354");
        } else {
          logic.channel.trackEvent("click", "024_338");
        }
        logic.channel.showChangePrintData(context, jsonEncode(logic.parameter?.templateMoudle), jumpType);
      },
      child: Container(
        color: Colors.transparent,
        padding: const EdgeInsetsDirectional.only(top: 4, bottom: 4),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              changePrintDataText,
              style: const TextStyle(color: Color(0xfffb4b42), fontSize: 14.0, fontWeight: FontWeight.w400),
            ),
            const SizedBox(
              width: 1,
            ),
            SvgIcon(
              logic.style.arrowRightIcon,
              side: 16,
              matchTextDirection: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _direction() {
    String formatExcelPageInfo;
    if (Directionality.of(context) == TextDirection.rtl) {
      formatExcelPageInfo = '${logic.style.pageMax}/${logic.style.pageIndex + 1}';
    } else {
      formatExcelPageInfo = '${logic.style.pageIndex + 1}/${logic.style.pageMax}';
    }
    return GetBuilder<PrintSettingLogic>(
        id: UpdateFieldManager.previewImage,
        builder: (logic) {
          return Container(
              padding: EdgeInsetsDirectional.fromSTEB(16, 12, 16, 0),
              child: Row(
                children: [
                  (logic.style.pageMax > 1 || logic.style.isDataSource)
                      ? GestureDetector(
                          onTap: () {
                            logic.channel.trackEvent("click", "024_068_096");
                            logic.showBatchCopies(context);
                          },
                          child: Container(
                            padding: EdgeInsetsDirectional.symmetric(horizontal: 10, vertical: 5),
                            decoration: BoxDecoration(
                              color: Colors.white, // 背景色
                              border: Border.all(
                                color: Color(0x173C3C43), // 边框颜色
                                width: 1, // 边框宽度
                              ),
                              borderRadius: BorderRadius.circular(15), // 圆角半径
                            ),
                            child: Row(
                              children: [
                                Text(
                                  logic.getI18nString("app01102", "预览") + ": " + formatExcelPageInfo,
                                  style: logic.style.liveCodeErrorStyle,
                                  textDirection: Directionality.of(context),
                                ),
                                SizedBox(
                                  width: 2,
                                ),
                                SvgIcon(
                                  logic.style.previewDownArrowIcon,
                                  color: logic.style.printSettingThemeColor,
                                  matchTextDirection: true,
                                )
                              ],
                            ),
                          ))
                      : Text(
                          logic.getI18nString("app01102", "预览"),
                          style: logic.style.previewTtileStyle,
                        ),
                  Expanded(child: SizedBox()),
                  GetBuilder<PrintSettingLogic>(
                      id: UpdateFieldManager.previewCount,
                      builder: (logic) {
                        return Container(
                          child: Row(
                            children: [
                              Text(
                                logic.getI18nString("app01137", "出纸方向"),
                                style: logic.style.carbonStyle,
                              ),
                              SizedBox(
                                width: 2,
                              ),
                              Transform.rotate(
                                angle: -(logic.parameter!.templateMoudle!["rotate"].toDouble()) * (math.pi / 180),
                                child: SvgIcon(
                                  logic.style.paperArrowIcon,
                                  matchTextDirection: true,
                                ),
                              )
                            ],
                          ),
                        );
                      })
                ],
              ));
        });
  }

  Widget _buildRFIDWidget(BuildContext context, int index) {
    // rfidContent生成
    rfidContent({RFIDShowType? type, required String rfidValue}) {
      List<Widget> rfidContent = [SizedBox.shrink()];
      switch (type) {
        case RFIDShowType.NotMatch:
          rfidContent = [
            SvgIcon(
              logic.style.rfidIcon,
              width: 18,
              height: 12,
              fit: BoxFit.contain,
            ),
            const SizedBox(
              width: 3,
            ),
            SvgIcon(
              logic.style.rfidWarningIcon,
              width: 16,
              height: 16,
            ),
            const SizedBox(
              width: 3,
            ),
            Flexible(
              child: Text(
                logic.style.rfidSoureUnCompliance,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFFF8473E)),
              ),
            ),
          ];
          break;
        case RFIDShowType.NotBinding:
          rfidContent = [
            SvgIcon(
              logic.style.rfidIcon,
              width: 18,
              height: 12,
              fit: BoxFit.contain,
            ),
            const SizedBox(
              width: 3,
            ),
            SvgIcon(
              logic.style.rfidWarningIcon,
            ),
            const SizedBox(
              width: 4,
            ),
            Text(
              logic.pageManager.style.rfidSoureUnBind,
              style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFFF8473E)),
            ),
          ];
          break;
        case RFIDShowType.Normal:
          rfidContent = [
            Padding(
              // 此处的Paddding是为了处理文本和SvgIcon的baseline对齐的问题
              padding: const EdgeInsetsDirectional.only(top: 2),
              child: SvgIcon(
                logic.style.rfidIcon,
                width: 18,
                height: 12,
                fit: BoxFit.contain,
              ),
            ),
            const SizedBox(
              width: 3,
            ),
            Flexible(
              child: Text(
                rfidValue,
                style: logic.style.labelNameStyle,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ];
          break;
        default:
          break;
      }
      return rfidContent;
    }

    RfidShowItem rfidShowItem = RfidRepository().getRfidShowItem(index + 1);
    return logic.isShowRFID
        ? Padding(
            padding: const EdgeInsetsDirectional.only(start: 60, top: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                ...rfidContent(type: rfidShowItem.rfidShowType, rfidValue: rfidShowItem.rfidContent),
              ],
            ),
          )
        : const SizedBox.shrink();
  }

  //模板视图显示
  Widget _previewMonitor() {
    return Container(
      padding: EdgeInsetsDirectional.symmetric(horizontal: 16, vertical: 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GetBuilder<PrintSettingLogic>(
              id: UpdateFieldManager.previewImage,
              builder: (logic) {
                return logic.style.pageMax > 1
                    ? (logic.style.pageIndex == 0
                        ? const SizedBox(
                            width: 36,
                            height: 36,
                          )
                        : GestureDetector(
                            onTap: () {
                              if (logic.style.pageIndex > 0) {
                                logic.channel.trackEvent("click", "024_068_097");
                                logic.style.pageIndex -= 1;
                                // _setPreview(logic.style.pageIndex);
                                _pageController?.animateToPage(logic.style.pageIndex,
                                    duration: Duration(milliseconds: 700),
                                    curve: Curves.ease);
                              }
                              ;
                            },
                            child: Container(
                              width: 36,
                              height: 36,
                              decoration: BoxDecoration(
                                color: Color(0x14747480), // 背景色
                                borderRadius: BorderRadius.circular(50.0), // 圆角半径
                              ),
                              child: SvgIcon(
                                logic.style.switchLeftIcon,
                                color: logic.style.pageIndex > 0 ? Color(0xFF161616) : Color(0x3C3C434D),
                                matchTextDirection: true,
                              ),
                            ),
                          ))
                    : SizedBox();
              }),
          Expanded(
              child: Container(
            padding: const EdgeInsetsDirectional.symmetric(horizontal: 10, vertical: 10),
            height: MediaQuery.sizeOf(context).height * 0.22 > 200 ? 200 : MediaQuery.sizeOf(context).height * 0.22,
            child:GetBuilder<PrintSettingLogic>(
    id: UpdateFieldManager.previewImage,
    builder: (logic) {
      return PageViewUtil.PageView.builder(
          physics: const BouncingScrollPhysics(),
          controller: _pageController,
          pageSnapping: true,
          itemCount: logic.style.pageMax,
          onPageChanged: (index) {
            logic.style.pageIndex = index;
            logic.update([UpdateFieldManager.previewImage]);
          },
          itemBuilder: (context, index) {
            var pageIndex = logic.style.pageIndex;
            debugPrint("当前预览页" + logic.style.pageIndex.toString());
            // List<Color> colors = [Colors.red,Colors.green,Colors.orange,Colors.brown,Colors.black,Colors.blue,Colors.yellow];
            // return Container(color: colors[index],);
            //影响携带页面位置的数据源模板的刷新
            // if (isInit) {
            //   pageIndex = 0;
            // }
            return FutureBuilder(
              future: _setPreview(pageIndex),
              builder: (context, snapshot) {
                return Padding(
                  padding: const EdgeInsets.all(1.0),
                  child: Stack(
                    alignment: AlignmentDirectional.center,
                    children: [
                      Container(
                        height: 10,
                      ),
                      LayoutBuilder(
                        builder: (BuildContext context, BoxConstraints constraints) {
                          double previewHeight = constraints.maxHeight;
                          if (logic.parameter!.templateMoudle != null) {
                            if (logic.parameter!.templateMoudle!['height'].toDouble() >=
                                logic.parameter!.templateMoudle!['width'].toDouble()) {
                              previeWidth = constraints.maxHeight /
                                  logic.parameter!.templateMoudle!['height'].toDouble() *
                                  logic.parameter!.templateMoudle!['width'].toDouble();
                            } else {
                              previeWidth = math.max<double>(0.0, MediaQuery.of(context).size.width - 72);
                              double tempHeight = (previeWidth * logic.parameter!.templateMoudle!['height']) /
                                  logic.parameter!.templateMoudle!['width'];
                              if (tempHeight > previewHeight) {
                                previeWidth = constraints.maxHeight /
                                    logic.parameter!.templateMoudle!['height'].toDouble() *
                                    logic.parameter!.templateMoudle!['width'].toDouble();
                              } else {
                                if (logic.style.pageMax > 1) {
                                  previeWidth = math.max<double>(0.0, previeWidth - 56);
                                }
                              }
                            }
                            // if (logic.style.pageMax > 1 &&
                            //     logic.parameter!.templateMoudle!['height'].toDouble() <
                            //         logic.parameter!.templateMoudle!['width'].toDouble()) {
                            //   previeWidth = previeWidth - 72;
                            // }
                            if (imageData != null || logic.taskType == PrintTaskType.batch) {
                              _getStackHeight();
                            }
                          }
                          return previeWidth == MediaQuery.of(context).size.width - 72
                              ? Container(
                              margin: EdgeInsets.symmetric(horizontal: 5),
                              width: previeWidth,
                              child: Stack(
                                children: [
                                  imageBackGroundData == null || logic.taskType == PrintTaskType.batch
                                      ? SizedBox()
                                      : Padding(
                                    padding: const EdgeInsets.all(0.0),
                                    child: Image.memory(
                                      imageBackGroundData!,
                                      gaplessPlayback: true,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                  ClipRect(
                                    clipper: logic.taskType == PrintTaskType.batch || imageData == null
                                        ? null
                                        : _CustomRectClipper(areaList),
                                    child: Stack(
                                      alignment: AlignmentDirectional.center,
                                      // key: _stackKey,
                                      children: [
                                        logic.taskType == PrintTaskType.batch
                                            ? localThumbnail.isEmpty
                                            ? SizedBox()
                                            : Padding(
                                          padding: const EdgeInsets.all(0.0),
                                          child: FallbackImage(
                                            localImagePath: localThumbnail, // 本地图片路径
                                            networkImageUrl: thumbnail, // 网络图片URL
                                          ),
                                        )
                                            : imageData == null
                                            ? SizedBox()
                                            : FadeTransition(
                                          opacity: _animation!,
                                          child: Image.memory(
                                            imageData!,
                                            gaplessPlayback: true,
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                        areaList.length > 1
                                            ? Positioned.fill(
                                          child: Directionality(
                                            textDirection: TextDirection.ltr,
                                            child: Padding(
                                              padding: EdgeInsets.fromLTRB(
                                                  areaList[2] + 1,
                                                  areaList[0] + 1,
                                                  areaList[3] + 1,
                                                  areaList[1] + 1),
                                              child: FadeTransition(
                                                opacity: _animation!,
                                                child: DottedBorder(
                                                  color: Colors.red,
                                                  strokeWidth: 1,
                                                  child: Container(),
                                                ),
                                              ),
                                            ),
                                          ),
                                        )
                                            : SizedBox()
                                      ],
                                    ),
                                  ),
                                ],
                              ))
                              : Stack(
                            children: [
                              imageBackGroundData == null || logic.taskType == PrintTaskType.batch
                                  ? SizedBox()
                                  : Padding(
                                padding: const EdgeInsets.all(0.0),
                                child: Image.memory(
                                  imageBackGroundData!,
                                  gaplessPlayback: true,
                                  fit: BoxFit.cover,
                                ),
                              ),
                              ClipRect(
                                clipper: logic.taskType == PrintTaskType.batch || imageData == null
                                    ? null
                                    : _CustomRectClipper(areaList),
                                child: Stack(
                                  alignment: AlignmentDirectional.center,
                                  // key: _stackKey,
                                  children: [
                                    logic.taskType == PrintTaskType.batch
                                        ? localThumbnail.isEmpty
                                        ? SizedBox()
                                        : Padding(
                                      padding: const EdgeInsets.all(0.0),
                                      child: FallbackImage(
                                        localImagePath: localThumbnail, // 本地图片路径
                                        networkImageUrl: thumbnail, // 网络图片URL
                                      ),
                                    )
                                        : imageData == null
                                        ? SizedBox()
                                        : FadeTransition(
                                      opacity: _animation!,
                                      child: Image.memory(
                                        imageData!,
                                        gaplessPlayback: true,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                    areaList.length > 1
                                        ? Positioned.fill(
                                      child: Directionality(
                                        textDirection: TextDirection.ltr,
                                        child: Padding(
                                          padding: EdgeInsets.fromLTRB(
                                              areaList[2] + 1,
                                              areaList[0] + 1,
                                              areaList[3] + 1,
                                              areaList[1] + 1),
                                          child: DottedBorder(
                                            color: Colors.red,
                                            strokeWidth: 1,
                                            child: Container(),
                                          ),
                                        ),
                                      ),
                                    )
                                        : SizedBox()
                                  ],
                                ),
                              )
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                );
              },
            );
          });
    }

            ) )),
          GetBuilder<PrintSettingLogic>(
              id: UpdateFieldManager.previewImage,
              builder: (logic) {
                return logic.style.pageMax > 1
                    ? (logic.style.pageIndex == logic.style.pageMax - 1
                        ? const SizedBox(
                            width: 36,
                            height: 36,
                          )
                        : GestureDetector(
                            onTap: () {
                              if (logic.style.pageIndex + 1 < logic.style.pageMax) {
                                logic.channel.trackEvent("click", "024_068_097");
                                logic.style.pageIndex += 1;
                                //  _setPreview(logic.style.pageIndex);
                                _pageController?.animateToPage(logic.style.pageIndex,
                                    duration: Duration(milliseconds: 700),
                                    curve: Curves.ease);
                              }
                              ;
                            },
                            child: Container(
                              width: 36,
                              height: 36,
                              decoration: BoxDecoration(
                                color: Color(0x14747480), // 背景色
                                borderRadius: BorderRadius.circular(50.0), // 圆角半径
                              ),
                              child: SvgIcon(
                                logic.style.switchRightIcon,
                                color: logic.style.pageIndex + 1 < logic.style.pageMax
                                    ? Color(0xFF161616)
                                    : Color(0x3C3C434D),
                                matchTextDirection: true,
                              ),
                            ),
                          ))
                    : SizedBox();
              }),
        ],
      ),
    );
  }

  Widget _liveCodeError() {
    return Container(
      padding: EdgeInsetsDirectional.only(top: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgIcon(
            logic.style.liveCodeErrorIcon,
            matchTextDirection: true,
          ),
          SizedBox(
            width: 2,
          ),
          Text(
            logic.style.liveCodeErrorTitle,
            style: logic.style.liveCodeErrorStyle,
          )
        ],
      ),
    );
  }

  Widget _countNumber() {
    String printCount = "1";
    if(logic.isExcelTemplate()){
      if(logic.printCopiesMode == PrintCopiesMode.settingCopies){
        printCount = logic.printData.printCount.toString();
      }else if(logic.printCopiesMode == PrintCopiesMode.linkCopies){
        try{
          List<List<String>> rowAndHeaderData = logic.parameter?.templateMoudleNew?.dataSources?[0]?.rowData ?? [];

          List<TemplateDataSourceRange> ranges = logic.parameter?.templateMoudleNew?.dataSources?[0].range ?? [];
          if(ranges.isEmpty && logic.parameter?.templateMoudleNew?.dataSourceBindInfo != null){
            ranges = [TemplateDataSourceRange(s: 1, e: logic.parameter!.templateMoudleNew!.dataSourceBindInfo!.total)];
          }
          List<int> rangeIndex = rangeToArr(ranges);
          // 根据 rangeIndex 提取对应行的数据，生成新列表
          List<List<String>> result = [];
          for (int index in rangeIndex) {
            if (index >= 0 && index < rowAndHeaderData.length) {
              result.add(rowAndHeaderData[index]);
            }
          }
          int currentCopiesLinkFieldIndex = logic.currentCopiesLinkFieldIndex;
          if(currentCopiesLinkFieldIndex < 0){
            printCount = "1";
          }else{
            //四舍五入
            String fieldValue = result[logic.style.pageIndex][currentCopiesLinkFieldIndex];
            printCount = parseStringToInt(fieldValue).toString();
          }

        }catch(e){
          printCount = "1";
        }

      }

    }else if(logic.taskType == PrintTaskType.batch){
      printCount = (int.parse(count.isEmpty ? "1" : count) * logic.printData.printCount).toString();
    }
    return logic.taskType == PrintTaskType.batch || logic.isExcelTemplate()
        ? Container(
            padding: EdgeInsetsDirectional.only(top: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  logic.getI18nString("app100001249", "打印\$页",
                      param: [printCount]),
                  style: logic.style.carbonStyle,
                )
              ],
            ),
          )
        : SizedBox();
  }

  Widget _carbon() {
    var colorsStr = logic.style.carbonColor.split(",");
    List<List<int>> matrix = [];
    colorsStr.forEach((value) {
      if (value.length > 2) {
        List<int> color = value.split('.').map((e) => int.parse(e)).toList();
        color.insert(0, 255);
        matrix.add(color);
      }
    });
    return (logic.style.isCarbon && logic.style.isConnectDevice && matrix.length > 0)
        ? Container(
            padding: EdgeInsetsDirectional.only(top: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  logic.style.carbonTitle,
                  style: logic.style.carbonStyle,
                ),
                SizedBox(
                  width: 3,
                ),
                matrix.length == 1
                    ? Container(
                        width: 12,
                        height: 8,
                        color: Color.fromARGB(matrix[0][0], matrix[0][1], matrix[0][2], matrix[0][3]),
                      )
                    : Row(
                        children: [
                          Container(
                            width: 6,
                            height: 8,
                            color: Color.fromARGB(matrix[0][0], matrix[0][1], matrix[0][2], matrix[0][3]),
                          ),
                          Container(
                            width: 6,
                            height: 8,
                            color: Color.fromARGB(matrix[1][0], matrix[1][1], matrix[1][2], matrix[1][3]),
                          )
                        ],
                      )
              ],
            ),
          )
        : SizedBox.shrink();
  }
}

// 自定义裁剪区域
class _CustomRectClipper extends CustomClipper<Rect> {
  final List<double> areaList;

  _CustomRectClipper(this.areaList);

  @override
  Rect getClip(Size size) {
    // 根据 areaList 确定裁剪区域
    // areaList[0]: top, areaList[1]: bottom, areaList[2]: left, areaList[3]: right
    return Rect.fromLTRB(
      areaList[2], // left
      areaList[0], // top
      size.width - areaList[3], // right
      size.height - areaList[1], // bottom
    );
  }

  @override
  bool shouldReclip(covariant CustomClipper<Rect> oldClipper) {
    return true; // 如果需要动态调整裁剪区域，返回 true
  }
}
