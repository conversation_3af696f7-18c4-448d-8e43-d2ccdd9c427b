import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:text/pages/C1/common/custom_popup_route.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';

typedef ItemsSelectedChanged = void Function(int selectedIndex);
typedef CheckItemSelectedCondition = bool Function(int selectedIndex, Function hidePopMenu);

class ItemsSelectorPopUpWidget extends StatefulWidget {
  final List<String> items;
  final int initializeIndex;
  final ItemsSelectedChanged itemsSelectedChanged;
  final CheckItemSelectedCondition? checkItemSelectedCondition;
  final int popHeight;
  final int popWidth;
  final Color? anchorColor; //锚点控件颜色
  final TextAlign anchorTextAlign; //弹出框基于锚点的对齐方式（居左or居右）
  final TextStyle? anchorTextStyle; //锚点控件样式
  final EdgeInsetsGeometry anchorEdgeInsets; //锚点控件边距
  final Offset popOffset;
  final bool dropDown;
  final bool firstItemSpecialDivider;
  final bool isEnableClick;
  final bool checkKeyboardStatus;
  final bool initKeyboardStatus;

  const ItemsSelectorPopUpWidget(
      {super.key,
      required this.items,
      required this.initializeIndex,
      required this.itemsSelectedChanged,
      this.popHeight = 225,
      this.popWidth = 200,
      this.anchorColor = ThemeColor.COLOR_FFBFBFBF,
      this.anchorTextAlign = TextAlign.end,
      this.anchorTextStyle,
      this.anchorEdgeInsets = const EdgeInsets.fromLTRB(12, 12, 12, 12),
      this.popOffset = const Offset(16, 0),
      this.dropDown = false,
      this.isEnableClick = true,
      this.firstItemSpecialDivider = false,
      this.checkItemSelectedCondition = null,
      this.checkKeyboardStatus = false,
      this.initKeyboardStatus = false});

  @override
  State<StatefulWidget> createState() {
    return ItemsSelectorPopUpState();
  }
}

class ItemsSelectorPopUpState extends State<ItemsSelectorPopUpWidget> {
  late int _selectedIndex;
  bool _keyboardShow = false;

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.initializeIndex;
    _keyboardShow = widget.initKeyboardStatus;
    if(widget.checkKeyboardStatus) {
      // 键盘监听
      var keyboardVisibilityController = KeyboardVisibilityController();
      // Subscribe
      keyboardVisibilityController.onChange.listen((bool visible) {
        _keyboardShow = visible;
      });
    }
  }

  @override
  void didUpdateWidget(covariant ItemsSelectorPopUpWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _selectedIndex = widget.initializeIndex;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (widget.isEnableClick) {
          if(widget.checkKeyboardStatus && _keyboardShow){
            FocusScope.of(context).requestFocus(FocusNode());
            Future.delayed(const Duration(milliseconds: 300), (){
              _didClickItem();
            });
          }
          else {
            _didClickItem();
          }
        }
      },
      child: Container(
        alignment:
            widget.anchorTextAlign == TextAlign.end ? AlignmentDirectional.centerEnd : AlignmentDirectional.centerStart,
        padding: widget.anchorEdgeInsets,
        child: Row(mainAxisSize: MainAxisSize.min, children: [
          Expanded(
            child: Text(
              (_selectedIndex < 0) ? intlanguage('app100000033', '请选择') : widget.items[_selectedIndex],
              textAlign: widget.anchorTextAlign,
              style: widget.anchorTextStyle != null
                  ? widget.anchorTextStyle
                  : TextStyle(
                      color: (_selectedIndex >= 0) ? ThemeColor.COLOR_595959 : ThemeColor.hint,
                      fontSize: 15,
                      fontWeight: FontWeight.w400,),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(
            width: 4,
          ),
          Offstage(
            offstage: !widget.isEnableClick,
            child: SvgIcon(
              'assets/images/C1/unfold.svg',
              color: widget.anchorColor,
            ),
          )
        ]),
      ),
    );
  }

  void _didClickItem() {
    Navigator.push(
      context,
      CustomPopRoute(
        child: Popup(
          items: widget.items,
          initializeIndex: _selectedIndex,
          itemsSelectedChanged: (int index) {
            widget.itemsSelectedChanged(index);
          },
          parentBuildContext: context,
          popHeight: widget.popHeight,
          popWidth: widget.popWidth,
          textAlign: widget.anchorTextAlign,
          popOffset: widget.popOffset,
          dropDown: widget.dropDown,
          firstItemSpecialDivider: widget.firstItemSpecialDivider,
          checkItemSelectedCondition: widget.checkItemSelectedCondition,
        ),
      ),
    );
  }
}

///编码类型选择page页面
class Popup extends StatefulWidget {
  final List<String> items;
  final int initializeIndex;
  final ItemsSelectedChanged itemsSelectedChanged;
  final CheckItemSelectedCondition? checkItemSelectedCondition;
  final BuildContext parentBuildContext;
  final int popHeight;
  final int popWidth;
  final TextAlign textAlign;
  final Offset popOffset;
  final bool dropDown;
  final bool firstItemSpecialDivider;

  Popup(
      {required this.items,
      required this.initializeIndex,
      required this.itemsSelectedChanged,
      required this.parentBuildContext,
      required this.popHeight,
      required this.popWidth,
      required this.textAlign,
      required this.popOffset,
      required this.dropDown,
      required this.firstItemSpecialDivider,
      this.checkItemSelectedCondition = null});

  PopupState createState() => PopupState();
}

class PopupState extends State<Popup> {
  // 声明对象
  late RenderBox ancorBox;
  late Offset offset;
  late Size size;

  @override
  void initState() {
    super.initState();
    // 找到并渲染对象button
    ancorBox = widget.parentBuildContext.findRenderObject() as RenderBox;
    offset = ancorBox.localToGlobal(Offset.zero);
    size = ancorBox.size;
  }

  @override
  Widget build(BuildContext context) {
    final top = widget.dropDown ? offset.dy + size.height - 8 : offset.dy - widget.popHeight + 8; // 顶部位置
    final maxPopHeight = MediaQuery.of(context).size.height - top - 8;
    return Material(
      type: MaterialType.transparency, // Material类型设置
      child: GestureDetector(
        child: Stack(
          children: <Widget>[
            Container(
              // 设置一个容器组件，是整屏幕的。
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              color: Colors.transparent, // 它的颜色为透明色
            ),
            PositionedDirectional(
              child: PopListWidget(
                items: widget.items,
                initializeIndex: widget.initializeIndex,
                itemsSelectedChanged: widget.itemsSelectedChanged,
                parentBuildContext: context,
                popHeight: widget.dropDown ? min(widget.popHeight, maxPopHeight.toInt()) : widget.popHeight,
                popWidth: widget.popWidth,
                firstItemSpecialDivider: widget.firstItemSpecialDivider,
                checkItemSelectedCondition: widget.checkItemSelectedCondition,
              ),
              top: top, // 顶部位置
              end: widget.textAlign == TextAlign.start ? null : 16,
              start: widget.textAlign == TextAlign.end ? null : widget.popOffset.dx, // 边界位置
            )
          ],
        ),
        onTap: () => Navigator.of(context).pop(), //点击空白处直接返回
      ),
    );
  }
}

class PopListWidget extends StatefulWidget {
  final List<String> items;
  final int initializeIndex;
  final ItemsSelectedChanged itemsSelectedChanged;
  final CheckItemSelectedCondition? checkItemSelectedCondition;
  final BuildContext parentBuildContext;
  final int popHeight;
  final int popWidth;
  final bool firstItemSpecialDivider;

  const PopListWidget(
      {super.key,
      required this.items,
      required this.initializeIndex,
      required this.itemsSelectedChanged,
      required this.parentBuildContext,
      required this.popHeight,
      required this.popWidth,
      required this.firstItemSpecialDivider,
      this.checkItemSelectedCondition = null});

  @override
  State<StatefulWidget> createState() {
    return PopListState();
  }
}

class PopListState extends State<PopListWidget> {
  late int _index;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _index = widget.initializeIndex;
    _scrollController = new ScrollController();
    if (_index > 3) {
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        _scrollController.animateTo(((_index - 3) * 50).toDouble(),
            duration: new Duration(microseconds: 10), curve: Curves.linear);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.popHeight.toDouble(),
      width: widget.popWidth.toDouble(),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(16)),
          boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.15), offset: const Offset(0, 5), blurRadius: 50)]),
      child: ScrollConfiguration(
        behavior: CupertinoScrollBehavior(),
        child: ListView.separated(
          physics: ClampingScrollPhysics(),
          padding: EdgeInsets.zero,
          controller: _scrollController,
          separatorBuilder: (BuildContext context, int index) {
            return Container(
              height: index == 0 && widget.firstItemSpecialDivider ? 3.0 : 0.5,
              color: const Color(0xFFEBEBEB),
            );
          },
          itemCount: widget.items.length,
          itemBuilder: (BuildContext context, int index) {
            return InkWell(
              onTap: () {
                if (widget.checkItemSelectedCondition != null &&
                    !widget.checkItemSelectedCondition!(index, hidePopMenu)) {
                  return;
                }
                setState(() {
                  _index = index;
                });
                Navigator.pop(context);
                widget.itemsSelectedChanged(index);
              },
              child: Container(
                height: 50,
                child: Row(
                  children: [
                    Container(
                      padding: EdgeInsetsDirectional.symmetric(horizontal: 10),
                      child: Visibility(
                          visible: _index == index,
                          maintainSize: true,
                          maintainAnimation: true,
                          maintainState: true,
                          child: SvgIcon('assets/images/C1/tick.svg',))
                    ),
                    Expanded(
                        child: Text(
                      widget.items[index],
                      style: TextStyle(color: ThemeColor.title, fontSize: 16, fontWeight: FontWeight.w400),
                    )),
                    SizedBox(
                      width: 16,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  hidePopMenu() {
    Navigator.pop(context);
  }
}
