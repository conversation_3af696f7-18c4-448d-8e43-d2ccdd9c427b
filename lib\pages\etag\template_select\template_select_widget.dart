import 'dart:convert';
import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:niim_login/login_plugin/utils/image_utils.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';
import 'package:text/network/dio_utils.dart';
import 'package:text/network/http_api.dart';
import 'package:text/pages/etag/home/<USER>/tag_base_logic.dart';
import 'package:text/pages/etag/home/<USER>/etag_template_size_model.dart';
import 'package:text/pages/etag/home/<USER>/etag_template_sort_model.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_category_list_model.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_state.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/DebounceUtil.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/item_divider.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/widget/normal_button.dart';

import '../../../utils/cachedImageUtil.dart';

class TemplateSelectWidget extends StatefulWidget {
  const TemplateSelectWidget({Key? key}) : super(key: key);

  @override
  _TemplateSelectWidgetState createState() => _TemplateSelectWidgetState();
}

class _TemplateSelectWidgetState extends State<TemplateSelectWidget> {
  PanelController panelController = PanelController();
  final ScrollController _scrollController = ScrollController();
  RefreshController refreshController = RefreshController();
  var tempalteSizeList = <EtagTemplateSizeModel>[];
  var tempalteSortList = <EtagTemplateSortModel>[];
  var sceneSelect = IndustrySelectedState.none;
  var etagCategoryListModel = <TemplateData>[];
  var isClear = true;
  var isFirst = true;
  var selectPage = 1;
  var topSelectHeight = 0;
  var etagHomeState = IndustryHomeState.loading;
  double lastPosition = 0.0;

  @override
  void initState() {
    super.initState();
    _getSizeList();
    _getSortList();
    // _changeIndustry();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Container(
        padding: EdgeInsets.only(bottom: MediaQuery.viewInsetsOf(context).bottom),
        decoration: const BoxDecoration(
          color: ThemeColor.background,
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
        height: MediaQuery.sizeOf(context).height - 60,
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              child: Stack(
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: GestureDetector(
                      child: Text(
                        intlanguage('app100000692', '取消'),
                        style: const TextStyle(color: ThemeColor.title, fontSize: 16, fontWeight: FontWeight.w600),
                      ),
                      onTap: () => Navigator.of(context).pop(),
                    ),
                  ),
                  Align(
                    alignment: Alignment.center,
                    child: ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: MediaQuery.sizeOf(context).width - 200),
                      child: Text(
                        intlanguage('app100000919', '更换模板样式'),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                        style: const TextStyle(color: ThemeColor.mainTitle, fontSize: 17, fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const ItemDivider(),
            Container(
              color: ThemeColor.listBackground,
              child: Column(
                children: [
                  /// 顶部筛选按钮
                  _etagCheckFactorWidge(context),

                  /// 顶部弹窗及内容组件
                  _etagSlidingUpPanel(context),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  _etagCheckFactorWidge(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
      decoration: const BoxDecoration(
        color: ThemeColor.background,
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              NormalButton(
                title: Get.find<TagBaseLogic>().state.etagTemplateSizeSelect?.name,
                width: context.width / 2.2,
                height: 32,
                isSelected: sceneSelect == IndustrySelectedState.modelSize ? true : false,
                iconData: Icons.arrow_drop_down_sharp,
                iconColor: sceneSelect == IndustrySelectedState.modelSize ? ThemeColor.brand : ThemeColor.COLOR_666666,
                textColor: sceneSelect == IndustrySelectedState.modelSize ? ThemeColor.brand : ThemeColor.COLOR_595959,
                backgroundColor:
                    sceneSelect == IndustrySelectedState.modelSize ? ThemeColor.COLOR_FFEDEC : ThemeColor.COLOR_F5F5F5,
                selectedBackgroundColor: ThemeColor.COLOR_FFEDEC,
                selectedTextColor: ThemeColor.brand,
                selectedClosure: () {
                  topSelectHeight = tempalteSizeList.length * 46;
                  topDialogClickListener(IndustrySelectedState.modelSize);
                },
              ),
              SizedBox(
                width: 0,
              ),
              NormalButton(
                title: Get.find<TagBaseLogic>().state.etagTemplateSortSelect?.name,
                width: context.width / 2.2,
                height: 32,
                isSelected: sceneSelect == IndustrySelectedState.industryScenes ? true : false,
                iconData: Icons.arrow_drop_down_sharp,
                iconColor:
                    sceneSelect == IndustrySelectedState.industryScenes ? ThemeColor.brand : ThemeColor.COLOR_666666,
                textColor:
                    sceneSelect == IndustrySelectedState.industryScenes ? ThemeColor.brand : ThemeColor.COLOR_595959,
                backgroundColor: sceneSelect == IndustrySelectedState.industryScenes
                    ? ThemeColor.COLOR_FFEDEC
                    : ThemeColor.COLOR_F5F5F5,
                selectedBackgroundColor: ThemeColor.COLOR_FFEDEC,
                selectedTextColor: ThemeColor.brand,
                selectedClosure: () {
                  topSelectHeight = tempalteSortList.length * 46;
                  topDialogClickListener(IndustrySelectedState.industryScenes);
                },
              ),
            ],
          ),
          Container(
            height: 2,
            color: ThemeColor.background,
          )
        ],
      ),
    );
  }

  _etagSlidingUpPanel(BuildContext context) {
    return SlidingUpPanel(
      controller: panelController,
      minHeight: 0,
      maxHeight: min(topSelectHeight.toDouble(), context.height * 0.6),
      slideDirection: SlideDirection.DOWN,
      isDraggable: false,
      backdropEnabled: true,
      backdropColor: Colors.black,
      boxShadow: const <BoxShadow>[
        BoxShadow(
          blurRadius: 0.0,
          color: Color.fromRGBO(0, 0, 0, 0.0),
        )
      ],
      backdropOpacity: 0.2,
      borderRadius: const BorderRadius.only(
        bottomLeft: Radius.circular(12.0),
        bottomRight: Radius.circular(12.0),
      ),
      onPanelSlide: (position) {
        if (lastPosition > position + 0.01) {
          sceneSelect = IndustrySelectedState.none;
        }
        lastPosition = position;
        setState(() {});
      },
      panel: Container(
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(12.0),
              bottomRight: Radius.circular(12.0),
            ),
          ),
          child: _changeTopContainer(context)),
      body: Container(
        color: ThemeColor.background,
        padding: const EdgeInsets.fromLTRB(14, 0, 14, 140),
        child: _templatePageState(context),
      ),
    );
  }

  ///顶部选择栏
  _changeTopContainer(BuildContext context) {
    switch (sceneSelect) {
      case IndustrySelectedState.modelSize:
        return _sizePage();
      case IndustrySelectedState.industryScenes:
        return _sortPage(context);
      default:
        return Container();
    }
  }

  _sizePage() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const ItemDivider(),
        Expanded(
            child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: MediaQuery.removePadding(
                  removeTop: true,
                  context: context,
                  child: ListView.separated(
                    controller: _scrollController,
                    itemCount: tempalteSizeList.length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      return GestureDetector(
                        child: Container(
                            child: Column(
                          children: [
                            Row(
                              children: [
                                Text(tempalteSizeList[index].name!,
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: Get.find<TagBaseLogic>().state.etagTemplateSizeSelect?.height ==
                                                  tempalteSizeList[index].height &&
                                              Get.find<TagBaseLogic>().state.etagTemplateSizeSelect?.width ==
                                                  tempalteSizeList[index].width
                                          ? FontWeight.w600
                                          : FontWeight.w400,
                                      color: Get.find<TagBaseLogic>().state.etagTemplateSizeSelect?.height ==
                                                  tempalteSizeList[index].height &&
                                              Get.find<TagBaseLogic>().state.etagTemplateSizeSelect?.width ==
                                                  tempalteSizeList[index].width
                                          ? ThemeColor.brand
                                          : ThemeColor.title,
                                    )),
                                Expanded(
                                    child: SizedBox(
                                  height: 45,
                                  child: Container(
                                    color: ThemeColor.background,
                                  ),
                                )),
                                Get.find<TagBaseLogic>().state.etagTemplateSizeSelect?.height ==
                                            tempalteSizeList[index].height &&
                                        Get.find<TagBaseLogic>().state.etagTemplateSizeSelect?.width ==
                                            tempalteSizeList[index].width
                                    ? SvgIcon('assets/images/e_tag/select_template.svg')
                                    : Container()
                              ],
                            ),
                            tempalteSizeList.length == index + 1
                                ? Container()
                                : Divider(height: 1.0, color: ThemeColor.divider, thickness: 0.5)
                          ],
                        )),
                        onTap: () {
                          panelController.close();
                          Get.find<TagBaseLogic>().state.etagTemplateSizeSelect = EtagTemplateSizeModel(
                              name: tempalteSizeList[index].name,
                              height: tempalteSizeList[index].height,
                              width: tempalteSizeList[index].width);
                          selectPage = 1;
                          _changeIndustry();
                        },
                      );
                    },
                    separatorBuilder: (context, index) {
                      return Container();
                    },
                  ),
                )))
      ],
    );
  }

  _sortPage(BuildContext context) {
    return Column(
      children: [
        const ItemDivider(),
        Expanded(
            child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: MediaQuery.removePadding(
                  removeTop: true,
                  context: context,
                  child: ListView.separated(
                    controller: _scrollController,
                    itemCount: tempalteSortList.length,
                    itemBuilder: (context, index) {
                      return GestureDetector(
                        child: Container(
                            child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Text(tempalteSortList[index].name!,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: Get.find<TagBaseLogic>().state.etagTemplateSortSelect?.id ==
                                                tempalteSortList[index].id
                                            ? FontWeight.w600
                                            : FontWeight.w400,
                                        color: Get.find<TagBaseLogic>().state.etagTemplateSortSelect?.id ==
                                                tempalteSortList[index].id
                                            ? ThemeColor.brand
                                            : ThemeColor.title,
                                      )),
                                ),
                                SizedBox(
                                  height: 45,
                                  width: 1,
                                ),
                                Get.find<TagBaseLogic>().state.etagTemplateSortSelect?.id == tempalteSortList[index].id
                                    ? SvgIcon('assets/images/e_tag/select_template.svg')
                                    : Container(
                                        width: 24,
                                      )
                              ],
                            ),
                            tempalteSortList.length == index + 1
                                ? Container()
                                : Divider(height: 1.0, color: ThemeColor.divider, thickness: 0.5)
                          ],
                        )),
                        onTap: () {
                          panelController.close();
                          Get.find<TagBaseLogic>().state.etagTemplateSortSelect =
                              EtagTemplateSortModel(id: tempalteSortList[index].id, name: tempalteSortList[index].name);
                          selectPage = 1;
                          _changeIndustry();
                          ToNativeMethodChannel().sendTrackingToNative({
                            "track": "click",
                            "posCode": "120_269_245",
                            "ext": {"b_name": tempalteSortList[index].name}
                          });
                        },
                      );
                    },
                    separatorBuilder: (context, index) {
                      return Container();
                    },
                  ),
                )))
      ],
    );
  }

  ///首页状态显示
  _templatePageState(BuildContext context) {
    switch (etagHomeState) {
      case IndustryHomeState.empty:
        return Container(
            padding: const EdgeInsets.only(bottom: 155),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  ImageUtils.getImgPath('no_data'),
                  fit: BoxFit.contain,
                ),
                Text(
                  intlanguage('app100000515', '暂无相关结果'),
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
                ),
              ],
            ));
      case IndustryHomeState.loading:
        return Container(
            child: Stack(
          children: [
            MediaQuery.removePadding(
              child: MasonryGridView.count(
                crossAxisCount: 2,
                mainAxisSpacing: 10,
                crossAxisSpacing: 10,
                itemCount: etagCategoryListModel.length,
                //  shrinkWrap: true,
                itemBuilder: (context, index) {
                  return EtagIndustryTemplateItem(
                    model: etagCategoryListModel[index],
                    index: index,
                  );
                },
              ),
              context: context,
              removeTop: true,
            ),
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.only(bottom: 80),
              child: const CupertinoActivityIndicator(
                radius: 20,
                animating: true,
              ),
            ),
          ],
        ));
      case IndustryHomeState.error:
        return Container(
            padding: const EdgeInsets.only(bottom: 180),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(ImageUtils.getImgPath('no_net'), fit: BoxFit.contain),
                Text(
                  intlanguage('app100000625', '当前网络状态异常'),
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 20),
                  child: NormalButton(
                    title: intlanguage('app100000626', '重新试试	'),
                    height: 40,
                    width: context.width / 3.3,
                    textColor: ThemeColor.brand,
                    decoration:
                        BoxDecoration(color: ThemeColor.listBackground, borderRadius: BorderRadius.circular(30)),
                    selectedClosure: () {
                      _getSizeList();
                      _getSortList();
                      selectPage = 1;
                      _changeIndustry();
                    },
                  ),
                ),
              ],
            ));
      case IndustryHomeState.showContainer:
        return Container(
            color: ThemeColor.background,
            child: MediaQuery.removePadding(
              context: context,
              removeTop: true,
              child: SmartRefresher(
                controller: refreshController,
                enablePullUp: true,
                onRefresh: () {
                  onRefresh();
                },
                onLoading: () {
                  onLoading();
                },
                child: MasonryGridView.count(
                  crossAxisCount: 2,
                  mainAxisSpacing: 15,
                  crossAxisSpacing: 15,
                  itemCount: etagCategoryListModel.length,
                  itemBuilder: (context, index) {
                    return EtagIndustryTemplateItem(
                      model: etagCategoryListModel[index],
                      index: index,
                    );
                  },
                ),
              ),
            ));
      default:
        return Container();
    }
  }

  ///获取模板尺寸
  _getSizeList() {
    DioUtils.instance.requestNetwork<EtagTemplateSizeModel>(Method.get, EtagTemplateApi.getTemplateSize, isList: true,
        onSuccessList: (list) {
      tempalteSizeList = list;
    }, onError: (code, message) {});
  }

  _getSortList() {
    DioUtils.instance.requestNetwork<EtagTemplateSortModel>(Method.get, EtagTemplateApi.getTemplateSort, isList: true,
        onSuccessList: (list) {
      tempalteSortList = list;
      if (tempalteSortList != null && tempalteSortList.isNotEmpty) {
        EtagTemplateSortModel? lastSortModel = Get.find<TagBaseLogic>().state.etagTemplateSortSelect;
        EtagTemplateSortModel? lastSortModelFromServer =
            tempalteSortList?.firstWhereOrNull((model) => model.id == lastSortModel?.id);
        Get.find<TagBaseLogic>().state.etagTemplateSortSelect =
            lastSortModelFromServer == null ? tempalteSortList.first : lastSortModelFromServer;
        _changeIndustry();
      }else{
        _changeIndustry();
      }
    }, onError: (code, message) {
          _changeIndustry();
        });
  }

  ///模板数据分页加载
  _changeIndustry({isClear = true, Function(bool, bool)? result}) {
    etagHomeState = IndustryHomeState.loading;
    if (result == null) {
      refreshController.resetNoData();
    }
    if (isClear) {
      setState(() {});
    }
    Map<String, dynamic> params = {
      'page': selectPage,
      'height': Get.find<TagBaseLogic>().state.etagTemplateSizeSelect?.height,
      'width': Get.find<TagBaseLogic>().state.etagTemplateSizeSelect?.width,
      'categoryId': Get.find<TagBaseLogic>().state.etagTemplateSortSelect?.id,
      'limit': 20
    };
    DioUtils.instance.requestNetwork<IndustryTemplateCategoryListModel>(
        Method.post, EtagTemplateApi.getTemplatePageList,
        params: params, isList: false, onSuccess: (value) {
      if (isClear) {
        etagCategoryListModel.clear();
      }
      // 填充数据
      if (value!.list.isNotEmpty) {
        for (var element in value.list) {
          etagCategoryListModel.add(element);
        }
        // 显示数据，刷新状态
        etagHomeState = IndustryHomeState.showContainer;
      } else {
        if (result == null || (isClear && value!.list.isEmpty)) {
          etagHomeState = IndustryHomeState.empty;
        } else {
          // 显示数据，刷新状态, 没有更多数据
          etagHomeState = IndustryHomeState.showContainer;
          result.call(false, false);
          setState(() {});
          return;
        }
      }
      result?.call(true, false);
      setState(() {});
    }, onError: (code, message) {
      etagHomeState = IndustryHomeState.error;
      if (result != null) {
        etagHomeState = IndustryHomeState.showContainer;
        result.call(false, true);
      }
      setState(() {});
    });
  }

  topDialogClickListener(IndustrySelectedState value) {
    if (sceneSelect == value) {
      panelController.isPanelOpen ? panelController.close() : panelController.open();
    } else {
      panelController.open();
    }
    sceneSelect = value;
    setState(() {});
  }

  onRefresh() {
    selectPage = 1;
    refreshController.resetNoData();
    _changeIndustry(result: (isSuccess, isError) {
      isSuccess ? refreshController.refreshCompleted() : refreshController.refreshFailed();
    });
  }

  onLoading() {
    var errorPage = 0;
    selectPage++;
    errorPage = selectPage;
    _changeIndustry(
        isClear: false,
        result: (isSuccess, isError) {
          if (!isError) {
            isSuccess ? refreshController.loadComplete() : refreshController.loadNoData();
          } else {
            selectPage = errorPage - 1;
            refreshController.loadFailed();
          }
        });
  }
}

///模板列表子widget
class EtagIndustryTemplateItem extends StatelessWidget {
  final TemplateData model;
  final int index;

  EtagIndustryTemplateItem({Key? key, required this.model, required this.index}) : super(key: key);

  _tapAction() {
    if (!DebounceUtil.checkClick()) return;
    int templateClass = model.profile!.extrain!.templateClass!.toInt();
    var rawDataList = model.rawData["elements"] as List<dynamic>;
    var elementFieldNameMap = <String, String>{};
    rawDataList.forEach((element) {
      var fieldName = element["fieldName"];
      if (fieldName != null && fieldName != "") {
        elementFieldNameMap.putIfAbsent(element["id"], () => fieldName);
      }
    });
    ToNativeMethodChannel()
        .getTemplateDetailFromNative(model.id.toString(), templateClass: templateClass, saveRecord: false)
        .then((data) async {
      if (data != null && data.isNotEmpty) {
        TemplateData templateData = TemplateData.fromJson(jsonDecode(data));
        var newRawDataList = templateData.rawData["elements"] as List<dynamic>;
        newRawDataList.forEach((element) {
          if(elementFieldNameMap.containsKey(element["id"])){
            element["fieldName"] = elementFieldNameMap[element["id"]];
          }
        });
        if (templateData.rawData["templateVersion"] != null && templateData.rawData["templateVersion"].isNotEmpty) {
          String isCanOpen =
              await ToNativeMethodChannel().isCanOpenTemplate(templateVersion: templateData.rawData["templateVersion"]);
          if (isCanOpen == "0") {
            Fluttertoast.showToast(msg: intlanguage("app100000343", "您的软件版本过低，请升级"));
            return;
          }
          Get.find<TagBaseLogic>().state.goodsTemplate = model.rawData;
          CustomNavigation.pop();
        }
      } else {
        Get.find<TagBaseLogic>().state.goodsTemplate = model.rawData;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    var itemWidth = (MediaQuery.sizeOf(context).width - 45) / 2 - 30;
    var itemHeight = itemWidth / model.width * model.height;
    return GestureDetector(
      onTap: _tapAction,
      child: Container(
        decoration: BoxDecoration(
            border: Border.all(color: ThemeColor.border, width: 0.5), borderRadius: BorderRadius.circular(12)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              decoration: const BoxDecoration(
                  color: ThemeColor.imageBackground,
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12))),
              child: Stack(
                children: [
                  Center(
                    child: Padding(
                        padding: EdgeInsets.symmetric(
                            vertical:
                                model.vip || model.hasVipRes || model.rawData['profile']['extrain']['templateType'] == 2
                                    ? 30
                                    : 20,
                            horizontal: 14),
                        child: Container(
                          constraints: const BoxConstraints(maxHeight: 242),
                          height: itemHeight + 4,
                          child: Stack(
                            alignment: AlignmentDirectional.center,
                            children: [
                              Container(
                                child: Positioned.fill(child: LayoutBuilder(builder: (_, BoxConstraints cons) {
                                  // debugPrint('-------------Positioned--$cons-------------');
                                  return RotatedBox(
                                    quarterTurns: model.canvasRotate ~/ 90,
                                    child: LayoutBuilder(
                                      builder: (_, BoxConstraints cons) {
                                        // debugPrint('-------------RotatedBox--$cons-------------');
                                        return Container(color: ThemeColor.background);
                                      },
                                    ),
                                  );
                                })),
                              ),
                              if (model.contentThumbnail.isNotEmpty) ...[
                                Container(
                                  child: Image.network(model.contentThumbnail ?? ''),
                                )
                              ],
                              Container(
                                child: CacheImageUtil().netCacheImage(
                                    fit: BoxFit.fitWidth,
                                    imageUrl: model.previewImage ?? '',
                                    filterQuality: FilterQuality.high,
                                    useOldImageOnUrlChange: true,
                                    errorWidget: const SvgIcon(
                                      'assets/images/industry_template/home/<USER>',
                                    )),
                              )
                            ],
                          ),
                        )),
                  ),
                  _isVipTemplate(model)
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text.rich(TextSpan(
                    children: [
                      ..._buildTemplateFlagWidget(model),
                      TextSpan(
                          text: model.name ?? '',
                          style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w600, color: Color(0xFF262626))),
                    ],
                  )),
                  const SizedBox(height: 2),
                  Text(
                    '${model.width}x${model.height}mm',
                    style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFF999999)),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  List<WidgetSpan> _buildTemplateFlagWidget(TemplateData model) {
    List<WidgetSpan> widgets = [];
    if (model.rawData['profile']['extrain']['templateType'] == 2) {
      widgets.add(WidgetSpan(
        alignment: PlaceholderAlignment.middle,
        child: Padding(
          padding: const EdgeInsetsDirectional.only(end: 2),
          child: SvgIcon(
            'assets/images/icon_flag_good_template.svg',
          ),
        ),
      ));
    }
    return widgets;
  }

  _isVipTemplate(TemplateData model) {
    if (model.vip! || model.hasVipRes) {
      return PositionedDirectional(
          top: 4,
          start: 4,
          child: Container(
              child: SvgIcon(
            'assets/images/icon_flag_vip_template.svg',
          )));
    } else {
      return Container();
    }

    // if (model.vip || model.hasVipRes) {
    //   if (model.rawData['profile']['extrain']['templateType'] == 2) {
    //     return Positioned(
    //         bottom: 0,
    //         child: Container(
    //             child: Row(
    //           children: [
    //             Padding(
    //               padding: EdgeInsets.only(right: 3),
    //               child: Image.asset(
    //                 'assets/images/print_history/vip_bottom.png',
    //                 height: 15,
    //               ),
    //             ),
    //             Image.asset(
    //               'assets/images/print_history/store.png',
    //               height: 15,
    //             ),
    //           ],
    //         )));
    //   } else {
    //     return Positioned(
    //       bottom: 0,
    //       child: Container(
    //         child: Image.asset(
    //           'assets/images/print_history/vip_bottom.png',
    //           //    fit: BoxFit.cover,
    //           height: 15,
    //         ),
    //       ),
    //     );
    //   }
    // } else if (model.rawData['profile']['extrain']['templateType'] == 2) {
    //   return Positioned(
    //     bottom: 0,
    //     child: Container(
    //       child: Image.asset(
    //         'assets/images/print_history/store.png',
    //         height: 15,
    //       ),
    //     ),
    //   );
    // } else {
    //   return Container();
    // }
  }
}
