import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:collection/collection.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/user_center/canvas_user_center.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:niimbot_excel/models/excel_cloud_file.dart';
import 'package:niimbot_excel/niimbot_excel_utils.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/data_source_wrapper.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/excel_detail.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/excel_info.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/excel_transform_manager.dart';
import 'package:niimbot_flutter_canvas/src/model/template_external_data.dart';
import 'package:niimbot_flutter_canvas/src/utils/template_utils.dart';
import 'package:niimbot_flutter_canvas/src/widgets/good_lib/good_field_manager.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '/src/model/excel/excel_covert_manager.dart';
import '/src/model/excel/data_source_wrapper.dart';
import '/src/model/excel/excel_detail.dart';
import '/src/model/excel/excel_info.dart';
import '/src/model/template_external_data.dart';
import '/src/utils/template_utils.dart';

Logger _logger = Logger("ExcelManager", on: kDebugMode);

/// Excel文件管理
class ExcelManager {
  static const int One_Page_Size = 200;
  static const String Excel_List_Key_Prefix = "excel_list_user";
  static const String Excel_File_Directory = "/excel";

  factory ExcelManager() => sharedInstance();

  static ExcelManager? _excelManager;

  static ExcelManager sharedInstance() {
    if (_excelManager == null) {
      _excelManager = new ExcelManager._internal();
    }
    return _excelManager!;
  }

  ExcelManager._internal();

  SharedPreferences? sp;

  /// 用户id：用户所属Excel文件列表键值对
  Map<int, List<ExcelInfo>> excelInfoListMap = {};

  /// Excel文件内容详情
  List<ExcelDetail> excelDetailList = [];

  ///临时存放当前点击的excel信息
  DataSourceWrapper? dataSourceWrapper;

  init(SharedPreferences sharedPreferences) {
    if (sp == null) {
      sp = sharedPreferences;
    }
  }

  String _getExcelFileListKey(int userId) {
    return "${Excel_List_Key_Prefix}_$userId";
  }

  int _getCurrentUserId() {
    if (!CanvasUserCenter().isLogin) {
      throw StateError("进行Excel相关操作需要登录");
    }
    return CanvasUserCenter().userId;
  }
}

/// 本地Excel管理
extension LocalExcelManager on ExcelManager {
  /// 从本地文件加载Excel列表
  loadExcelInfo() async {
    if (sp == null) {
      throw StateError("ExcelManager没有初始化（调用init)");
    }
    int userId = _getCurrentUserId();
    final excelList = _getCacheExcelFileList(userId);
    if (excelList != null) {
      excelInfoListMap[userId] = excelList;
      for (int i = 0; i < excelList.length; i++) {
        ExcelInfo excelInfo = excelList[i];
        if (!excelDetailList
            .any((element) => element.id == excelInfo.id.toString())) {
          final excelDetail =
              await _readExcelDetailFromLocal(userId, excelInfo);
          if (excelDetail != null) {
            if (!excelDetailList
                .any((element) => element.id == excelInfo.id.toString())) {
              excelDetailList.add(excelDetail);
            }
          }
        }
      }
    }
  }

  /// 查询当前用户缓存的Excel文件列表
  List<ExcelInfo>? getCurrentUserExcelList() {
    int userId = _getCurrentUserId();
    if (excelInfoListMap.containsKey(userId)) {
      return excelInfoListMap[userId];
    }
    return null;
  }

  /// 加载当前用户缓存的Excel文件列表
  Future<List<ExcelInfo>?> loadCurrentUserExcelList() async {
    int userId = _getCurrentUserId();
    await loadExcelInfo();
    if (excelInfoListMap.containsKey(userId)) {
      return excelInfoListMap[userId];
    }
    return null;
  }

  /// 通过excelId查询Excel详情
  ExcelDetail? getExcelDetailById(int excelId) {
    return excelDetailList
        .firstWhereOrNull((element) => element.id == excelId.toString());
  }

  /// 获取Excel的抬头列表
  List<String>? getExcelHeaders(int excelId, {ExternalData? externalData}) {
    final excelDetail = getExcelDetailById(excelId);
    if (excelDetail != null) {
      return excelDetail.getExcelHeaders();
    }
    if (externalData != null && externalData.id == excelId.toString()) {
      final externalDataList = externalData.externalDataList;
      if (externalDataList != null && externalDataList.isNotEmpty) {
        return externalDataList[0].data?.columnHeaders;
      }
    }
    return null;
  }

  List<List<String>> getColumnData(int excelId, {ExternalData? externalData}) {
    List<List<String>> columnData = [];
    final excelDetail = getExcelDetailById(excelId);
    if (excelDetail != null) {
      int columnSize = 0;
      excelDetail.list.forEach((element) {
        if (element.length > columnSize) {
          columnSize = element.length;
        }
      });
      for (int i = 0; i < columnSize; i++) {
        List<String> oneColumnData = [];
        for (int j = 1; j < excelDetail.list.length; j++) {
          List<String> rowData = excelDetail.list[j];
          oneColumnData.add(rowData[i]);
        }
        columnData.add(oneColumnData);
      }
      return columnData;
    }
    if (externalData != null && externalData.id == excelId.toString()) {
      final externalDataList = externalData.externalDataList;
      final columns = externalDataList?[0].data?.columns;
      if (columns != null) columnData.addAll(columns);
      return columnData;
    }
    return columnData;
  }

  /// 获取Excel指定列的抬头
  /// [column]索引从0开始
  String? getExcelGivenColumnHeader(int excelId, int column,
      {ExternalData? externalData}) {
    final excelDetail = getExcelDetailById(excelId);
    if (excelDetail != null) {
      return excelDetail.getGivenColumnHeader(column);
    }
    if (externalData != null && externalData.id == excelId.toString()) {
      final externalDataList = externalData.externalDataList;
      if (externalDataList != null && externalDataList.isNotEmpty) {
        final columnHeaders = externalDataList[0].data?.columnHeaders;
        if (columnHeaders != null && column < columnHeaders.length) {
          return columnHeaders[column];
        }
      }
    }
    return null;
  }

  /// 获取Excel指定列指定行单元格的内容
  /// [column]索引从0开始
  /// [row]索引从1开始
  String? getExcelSheetContent(int excelId, int column, int row,
      {ExternalData? externalData}) {
    final excelDetail = getExcelDetailById(excelId);
    if (excelDetail != null) {
      return excelDetail.getSheetContent(column, row);
    }
    if (externalData != null && externalData.id == excelId.toString()) {
      final externalDataList = externalData.externalDataList;
      if (externalDataList != null && externalDataList.isNotEmpty) {
        final columns = externalDataList[0].data?.columns;
        if (columns != null && column < columns.length) {
          List<String> columnData = columns[column];
          if (row <= columnData.length) {
            return columnData[row - 1];
          }
        }
      }
    }
    return null;
  }

  int getExcelRowCount(int excelId) {
    final excelDetail = getExcelDetailById(excelId);
    if (excelDetail == null) {
      return 0;
    }
    return excelDetail.list.length;
  }

  /// 获取展示的Excel文件名
  /// [excelId] Excel文件id
  /// [type] 0-全名包括扩展名，1-全名不包括扩展名，2-只获取扩展名
  String? getExcelDisplayNameById(int excelId, {int type = 0}) {
    final excelList = getCurrentUserExcelList();
    if (excelList == null ||
        !excelList.any((element) => element.id == excelId)) {
      return "";
    }
    ExcelInfo excelInfo =
        excelList.firstWhere((element) => element.id == excelId);
    return getExcelDisplayNameByFullName(excelInfo.name, type: type);
  }

  /// 获取展示的Excel文件名
  /// [excelInfo] Excel文件信息
  /// [type] 0-全名包括扩展名，1-全名不包括扩展名，2-只获取扩展名
  String? getExcelDisplayNameByFileInfo(ExcelInfo excelInfo, {int type = 0}) {
    return getExcelDisplayNameByFullName(excelInfo.name, type: type);
  }

  /// 获取展示的Excel文件名
  /// [fullName] Excel文件全名
  /// [type] 0-全名包括扩展名，1-全名不包括扩展名，2-只获取扩展名
  String getExcelDisplayNameByFullName(String? fullName, {int type = 0}) {
    if (type == 1) {
      if (fullName == null) {
        return "";
      }
      if (fullName.endsWith(".xls")) {
        return fullName.substring(0, fullName.length - 4);
      } else if (fullName.endsWith(".xlsx")) {
        return fullName.substring(0, fullName.length - 5);
      } else {
        return fullName;
      }
    } else if (type == 2) {
      if (fullName == null) {
        return "";
      }
      if (fullName.endsWith(".xls")) {
        return ".xls";
      } else if (fullName.endsWith(".xlsx")) {
        return ".xlsx";
      } else {
        return "";
      }
    }
    return fullName!;
  }

  /// 保存Excel列表信息
  _cacheExcelList(int userId, List<ExcelInfo> excelList) {
    excelInfoListMap[userId] = excelList;
    String key = _getExcelFileListKey(userId);
    sp?.setString(key, json.encode(excelList.map((e) => e.toJson()).toList()));
  }

  List<ExcelInfo>? _getCacheExcelFileList(int userId) {
    String saveKey = _getExcelFileListKey(userId);
    if (sp != null && sp!.containsKey(saveKey)) {
      List<ExcelInfo> excelList = [];
      final excelListJson = sp?.getString(saveKey);
      if (excelListJson != null && excelListJson.isNotEmpty) {
        json.decode(excelListJson).forEach((e) {
          excelList.add(ExcelInfo.fromJson(e));
        });
      }
      return excelList;
    }
    return null;
  }

  Future updateExcelInfo(String excelId, String md5, String downloadUrl) async {
    int userId = _getCurrentUserId();
    final excelInfo = excelInfoListMap[userId]
        ?.firstWhereOrNull((element) => excelId == element.id.toString());
    if (excelInfo != null) {
      excelInfo.md5 = md5;
      excelInfo.path = downloadUrl;
      _cacheExcelList(userId, excelInfoListMap[userId]!);
    }
  }

  Future _deleteExcels(int userId, List<int> excelIds) async {
    final userExcelInfo = excelInfoListMap[userId];
    userExcelInfo?.removeWhere(
        (element) => excelIds.any((excelId) => excelId == element.id));
    if (userExcelInfo != null) _cacheExcelList(userId, userExcelInfo);
    excelDetailList.removeWhere((element) =>
        excelIds.any((excelId) => excelId.toString() == element.id));
    for (int i = 0; i < excelIds.length; i++) {
      String path =
          await _getExcelDetailCachePath(userId, excelIds[i].toString());
      File file = File(path);
      if (file.existsSync()) {
        await file.delete(recursive: true);
      }
    }
  }

  /// Excel文件缓存到本地文件
  Future _cacheExcelDetail(int userId, ExcelDetail excelDetail) async {
    if (!excelDetailList.any((element) => element.id == excelDetail.id)) {
      excelDetailList.add(excelDetail);
    }
    String path = await _getExcelDetailCachePath(userId, excelDetail.id);
    File file = File(path);
    if (file.existsSync()) {
      await file.delete(recursive: true);
    }
    await file.create(recursive: true);
    String content = json.encode(excelDetail.toJson());
    await file.writeAsString(content);
  }

  /// 从本地文件读取Excel文件内容详情
  Future<ExcelDetail?> _readExcelDetailFromLocal(
      int userId, ExcelInfo excelInfo) async {
    String path =
        await _getExcelDetailCachePath(userId, excelInfo.id.toString());
    File file = File(path);
    if (!file.existsSync()) {
      return null;
    }
    String content = await file.readAsString();
    if (content.isEmpty) {
      return null;
    }
    return ExcelDetail.fromJson(json.decode(content));
  }

  /// Excel文件内容详情本地保存路径
  Future<String> _getExcelDetailCachePath(int userId, String excelId) async {
    String localFileName = "excel_${excelId}.json";
    return (await getApplicationDocumentsDirectory()).path +
        ExcelManager.Excel_File_Directory +
        "/$userId" +
        "/$localFileName";
  }
}

/// 网络Excel管理
extension NetworkExcelManager on ExcelManager {
  /// 从三方app上传Excel文件到云服务器
  uploadExcelFile(String fileName, Uint8List data, Function(dynamic)? success,
      Function(int, String)? fail) {
    final excelImportIml = CanvasPluginManager().excelImportImpl;
    if (excelImportIml == null) {
      throw StateError("ExcelImportInterface未实现");
    }
    excelImportIml.uploadExcelFile(fileName, data, (map) {
      success?.call(map);
    }, (errorCode, errorMsg) {
      fail?.call(errorCode, errorMsg);
    });
  }

  Future<String> uploadOSSFunction(
      String fileName, Uint8List data, String contentHash,{bool createCloudFile = true}) {
    final excelImportIml = CanvasPluginManager().excelImportImpl;
    if (excelImportIml == null) {
      throw StateError("ExcelImportInterface未实现");
    }
    Completer<String> completer = Completer<String>();
    excelImportIml.uploadExcelFile(fileName, data, (data) {
      // ExcelCloudFile file = ExcelCloudFile.fromJson(data);
      // completer.complete(file.downloadUrl);
      completer.complete(data?["downloadUrl"]);
    }, (errorCode, errorMsg) {
      completer.completeError(-1);
    }, createCloudFile: createCloudFile, contentHash: contentHash);
    return completer.future;
  }

  /// 从服务器请求Excel文件列表
  requestExcelFiles(
      Function(List<ExcelInfo>)? success, Function(int, String)? fail) {
    final excelImportIml = CanvasPluginManager().excelImportImpl;
    if (excelImportIml == null) {
      throw StateError("ExcelImportInterface未实现");
    }
    int userId = _getCurrentUserId();
    Map<String, dynamic> params = {};
    params["page"] = 1;
    params["limit"] = ExcelManager.One_Page_Size;
    params["appCode"] = Platform.isAndroid ? "2" : "1";
    excelImportIml.requestExcelFiles(params, (data) {
      List<ExcelInfo> excelList = [];
      data.forEach((element) {
        excelList.add(ExcelInfo.fromJson(element));
      });
      _cacheExcelList(userId, excelList);
      success?.call(excelList);
    }, (errorCode, errorMsg) {
      fail?.call(errorCode, errorMsg);
    });
  }

  /// 从服务器请求Excel文件的详情
  requestCloudFileDetail(
      int excelId, Function? success, Function(int, String)? fail) {
    final excelImportIml = CanvasPluginManager().excelImportImpl;
    if (excelImportIml == null) {
      throw StateError("ExcelImportInterface未实现");
    }
    int userId = _getCurrentUserId();
    Map<String, dynamic> params = {"idList": excelId.toString()};
    excelImportIml.requestExcelFileDetail(params, (data) {

      ExcelDetail excelDetail = ExcelDetail.fromJson(data as Map<String,dynamic>);
      NiimbotExcelUtilsWithCovert.buildDataSourceFromExcelInfoWithCovert(
              excelDetail.id,
              excelDetail.name,
              excelDetail.md5,
              excelDetail.downloadUrl)
          .then((dataSource) {
        if (dataSource != null) {
          TemplateUtils.parseExcelContent(dataSource).then((wrapper) {
            dataSourceWrapper = wrapper;
            _cacheExcelDetail(userId, excelDetail).then((value) {
              if (success != null) {
                success();
              }
            });
          });
        }
      });
      // _cacheExcelDetail(userId, excelDetail).then((value) {
      //   if (success != null) {
      //     success();
      //   }
      // });
    }, (errorCode, errorMsg) {
      fail?.call(errorCode, errorMsg);
    });
  }

  /// 从服务器请求Excel文件的详情
  Future<ExcelCloudFile> getExcelDataById(int excelId) {
    final excelImportIml = CanvasPluginManager().excelImportImpl;
    if (excelImportIml == null) {
      throw StateError("ExcelImportInterface未实现");
    }

    Completer<ExcelCloudFile> completer = Completer<ExcelCloudFile>();
    Map<String, dynamic> params = {"idList": excelId.toString()};
    excelImportIml.requestExcelFileDetail(params, (data) {
      ExcelCloudFile excelDetail = ExcelCloudFile.fromJson(data as Map<String,dynamic>);
      completer.complete(excelDetail);
    }, (errorCode, errorMsg) {
      completer.completeError(-1);
    });
    return completer.future;
  }

  /// 从服务器请求云文件信息
  Future<ExcelCloudFile?> getCloudFileInfo(int excelId, String fileHash) {
    final excelImportIml = CanvasPluginManager().excelImportImpl;
    if (excelImportIml == null) {
      throw StateError("ExcelImportInterface未实现");
    }

    Completer<ExcelCloudFile?> completer = Completer<ExcelCloudFile?>();
    Map<String, dynamic> params = {};
    if (excelId != 0) {
      params["id"] = excelId.toString();
    }
    if (fileHash.isNotEmpty) {
      params["md5"] = fileHash;
    }
    excelImportIml.requestCloudFileInfo(params, (data) {
      if (data == null) {
        completer.complete(null);
      } else {
        ExcelCloudFile excelDetail =
            ExcelCloudFile.fromJson(data as Map<String, dynamic>);
        completer.complete(excelDetail);
      }
    }, (errorCode, errorMsg) {
      // completer.completeError(-1);
      completer.complete(null);
    });
    return completer.future;
  }

  /// 删除单个云文件
  deleteCloudFile(int excelId, Function success, Function(int, String) fail) {
    deleteCloudFiles([excelId], success, fail);
  }

  /// 批量删除云文件
  deleteCloudFiles(
      List<int> excelIds, Function success, Function(int, String) fail) {
    final excelImportIml = CanvasPluginManager().excelImportImpl;
    if (excelImportIml == null) {
      throw StateError("ExcelImportInterface未实现");
    }
    int userId = _getCurrentUserId();
    String idList = "";
    for (int i = 0; i < excelIds.length; i++) {
      if (i == excelIds.length - 1) {
        idList += "${excelIds[i]}";
      } else {
        idList += "${excelIds[i]},";
      }
    }
    Map<String, dynamic> params = {"idList": idList};
    excelImportIml.deleteCloudFile(params, () {
      _logger.log("xuhao deleteCloudFile fileId=$idList ");
      _deleteExcels(userId, excelIds).then((value) {
        success();
            });
    }, (errorCode, errorMsg) {
      fail(errorCode, errorMsg);
        });
  }

  /// 请求上传商品库数据通过excelId
  requestUploadByCloudFileId(String downloadUrl, Function(Map<dynamic,dynamic>) success, Function(int, String) fail) {
    final excelImportIml = CanvasPluginManager().excelImportImpl;
    if (excelImportIml == null) {
      throw StateError("ExcelImportInterface未实现");
    }
    // Map<String, dynamic> params = {"id": excelId.toString()};
    Map<String, dynamic> params = {"url": downloadUrl};
    excelImportIml.requestUploadByCloudFileId(params, (data) {
      if (success != null) {
        GoodFieldManager().updateGoodFields().then((value) {
          if (value != null) {
            GoodFieldManager().updateGoodFieldsWithData(value);
          }
          success(data);
        });

      }
    }, (errorCode, errorMsg) {
      if (fail != null) {
        fail(errorCode, errorMsg);
      }
    });
  }

  /// 用户商品库导入EXCEL文件存在重复确认操作  OK
  requestUserUploadGoodsConfirm(bool confirm,
      Function(Map<dynamic, dynamic>) success, Function(int, String) fail) {
    final excelImportIml = CanvasPluginManager().excelImportImpl;
    if (excelImportIml == null) {
      throw StateError("ExcelImportInterface未实现");
    }
    Map<String, dynamic> params = {"confirm": true};
    excelImportIml.requestUserUploadGoodsConfirm(params, (data) {
      success(data);
        }, (errorCode, errorMsg) {
      fail(errorCode, errorMsg);
        });
  }
}
