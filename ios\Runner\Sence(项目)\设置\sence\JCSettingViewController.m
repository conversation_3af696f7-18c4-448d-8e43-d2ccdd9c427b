//
//  JCSettingViewController.m
//  Runner
//
//  Created by xy on 2018/3/9.
//  Copyright © 2018年 xiaoyao. All rights reserved.
//

#import "JCSettingViewController.h"
#import "XYTitleCell2.h"
#import "JCLoginOutCell.h"
#import "XYTitleSwitchCell.h"
#import <CoreBluetooth/CoreBluetooth.h>
#import "NSData+dataUpload.h"
#import "JCPrintDevice.h"
#import "AYCheckManager.h"
//跳转界面
#import "JCQAViewController.h"
#import "JCLanguageSetingViewController.h"
#import "JCExcelListViewController.h"
#import "JCMyTemplateDetailViewController.h"
#import "JCFontManagerController.h"
#import "JCUserCancellationView.h"
#import "JCAboutUsViewController.h"
#import "JCUserAgmentController.h"
#import "JCShopNormalVC.h"
#import "NBCAPMiniAppEngine.h"
#import "LaboratoryViewController.h"
#import "NiimBotAlert.h"
#import "ImageLibraryBridge.h"
#import "JCPersonalSettingViewController.h"
@interface JCSettingViewController ()<GroupShadowTableViewDelegate,GroupShadowTableViewDataSource>
@property (nonatomic, strong) NSArray *contentArr;
@property (nonatomic, strong) NSDictionary *logUpdateInfo;
@property (nonatomic, strong) MBProgressHUD *progressHUD;
@property (nonatomic, strong) NSString *logUUID;
@property (nonatomic,strong) UIButton *cancellationBtn;
@end

@implementation JCSettingViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self refreshOperateList];
    [self.view addSubview:self.cancellationBtn];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loginChanged:) name:LOGIN_CHANGED object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(changeLanguage:) name:JCNOTICATION_ChANGELANGUAGE object:nil];
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
//    [self refreshOperateList];
}

- (void)initData{
}

- (void)initUI
{
    [super initUI];
    self.title = XY_LANGUAGE_TITLE_NAMED(@"app00297",@"设置");
    self.view.backgroundColor = HEX_RGB(0xF5F5F5);
#ifdef DEBUG
    //    NSString *sdkfileBundlePath = [[NSBundle mainBundle] pathForResource:@"JCAPI" ofType:@"a"];
    //    NSData *fileData = [NSData dataWithContentsOfFile:sdkfileBundlePath];
    //    NSString *md5 = fileData.MD5String;
    //
    //    UILabel *networkErrTipLabel = [[UILabel alloc] init];
    //    networkErrTipLabel.font = MY_FONT_Bold(14);
    //    networkErrTipLabel.numberOfLines = 0;
    //    networkErrTipLabel.textColor = HEX_RGB(0x262626);
    //    networkErrTipLabel.textAlignment = NSTextAlignmentCenter;
    //    networkErrTipLabel.text = [NSString stringWithFormat:@"当前应用内SDK MD5:\n%@",md5];
    //    [self.view addSubview:networkErrTipLabel];
    //    [networkErrTipLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    //        make.leading.equalTo(weakSelf.view).offset(100);
    //        make.bottom.equalTo(weakSelf.view).offset(-100);
    //        make.right.equalTo(weakSelf.view).offset(-100);
    //    }];
#endif
}

- (void)initNavigationBar{
#ifdef DEBUG
    [self showBarButton:NAV_RIGHT title:@"加密控制" fontColor:HEX_RGB(0x262626)];
#endif
}

- (void)initTableView
{
    XYWeakSelf
    [super initTableView];
    CGFloat navHeight = iPhoneX?88:64;
    CGFloat bottomHeight = iPhoneX?88:54;
    self.groupShadowTableView.groupShadowDelegate = self;
    self.groupShadowTableView.groupShadowDataSource = self;
    self.groupShadowTableView.showSeparator = NO;
    [self.groupShadowTableView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(weakSelf.view).offset(0);
        make.top.equalTo(weakSelf.view).offset(0);
        make.trailing.equalTo(weakSelf.view).offset(0);
        make.height.mas_equalTo(SCREEN_HEIGHT - navHeight-bottomHeight);
    }];
    [self.groupShadowTableView registerNib:@"JCLoginOutCell"];
    [self.groupShadowTableView registerNib:@"XYTitleCell2"];
    [self.groupShadowTableView registerClass:[XYTitleSwitchCell class] forCellReuseIdentifier:@"XYTitleSwitchCell"];
}

- (UIButton *)cancellationBtn{
    if(!_cancellationBtn){
        _cancellationBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_cancellationBtn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00957", @"用户注销") forState:UIControlStateNormal];
        [_cancellationBtn  setTitleColor:HEX_RGB(0x999999) forState:UIControlStateNormal];
        _cancellationBtn.titleLabel.font = MY_FONT_Medium(16);
        _cancellationBtn.frame = CGRectMake(15, SCREEN_HEIGHT - 70 - (iPhoneX?88:64), SCREEN_WIDTH - 30, 54);
        [_cancellationBtn addTarget:self action:@selector(cancellationEvent) forControlEvents:UIControlEventTouchUpInside];
        _cancellationBtn.hidden = xy_isLogin?NO:YES;
    }
    return _cancellationBtn;
}

- (void)cancellationEvent{
    //
    XYWeakSelf
    NSLog(@"注销账号");
    [JCUserCancellationView showWithView:weakSelf.navigationController.view title:XY_LANGUAGE_TITLE_NAMED(@"app00953", @"账号注销确认") message:XY_LANGUAGE_TITLE_NAMED(@"app00954", @"   当您确认注销账号后，您的一切数据将 从系统中删除，如果在注销后您还希望 继续使用精臣云打印服务，需要注册新 账号，您确定要继续账户注销流程么？") block:^(id x) {
        if([x isEqualToString:@"0"]){
            [weakSelf cancellationRequest];
        }else{
            NSLog(@"2222");
        }
    }];
}

- (void)cancellationRequest{

    XYWeakSelf
    NSString *router;
    if(m_userModel.isSocialAccount){//社交
        router = @"socialAccountLogout";
    }else{//主账号
        router = @"mainAccountLogout";
    }
    [self gotoFlutterPage:router arguments:@{
        @"isPresent": @NO,
        @"token": m_userModel.token,
    } onPageFinished:^(NSDictionary *res) {
        if(res == nil || [res[@"result"] boolValue] == false) return;
        [[NSUserDefaults standardUserDefaults] removeObjectForKey:@"pasteboardString"];
        if ([JCAppEventChannel shareInstance].eventSink) {
            [JCAppEventChannel shareInstance].eventSink(@{@"userInfo":@{}});
        }
        [XYCenter sharedInstance].userModel = nil;
        [weakSelf postNotification:LOGIN_CHANGED];
        [weakSelf postNotification:LOGIN_CHANGED_SHOP];
        [JCLoginManager sharedInstance].hasLogined = NO;
        [weakSelf refreshOperateList];
        [weakSelf deleteWebCache];
        [weakSelf.navigationController popViewControllerAnimated:NO];
    }];
}

- (void)rightButtonTouch:(UIButton *)sender{
    [JCKeychainTool save:KEY_APP_SUPPORT_VIP data:@"0"];
    [JCKeychainTool save:KEY_APP_UNSUPPORT_VIP_COUNTRYS data:@""];
    [XYCenter sharedInstance].isSupportGrade = YES;
    [JCKeychainTool save:KEY_IN_APP_INSTEAD data:@{}];
    [JCKeychainTool save:KEY_IN_APP_INSTEAD_DEVICE data:@{}];
    if ([JCAppEventChannel shareInstance].eventSink) {
        [JCAppEventChannel shareInstance].eventSink(@{@"isDeviceVip":@(false)});
    }
    [JCKeychainTool save:KEY_IN_APP_INSTEAD_DEVICE_WILL_REPORT data:@[]];
    [[NSUserDefaults standardUserDefaults] setValue:@NO forKey:@"vip_nps_state"];
    [JCKeychainTool save:@"pasteboardSettingSwitch" data:@""];
}

- (void)changeLanguage{
//    self.navigationItem.title = XY_LANGUAGE_TITLE_NAMED(@"app00297",@"设置");
//    self.tabBarItem.title = @"app";
//    if (self.isViewLoaded && !self.view.window) {
//        self.view = nil;
//    }
}

-(void)loginOut:(UIButton *)sender{
    XYWeakSelf
    [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app00313",@"确定退出吗？") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00207",@"退出登录") cancelBlock:nil sureBlock:^{
        NSDictionary *parameter = STR_IS_NIL(m_userModel.shopToken) ? @{} : @{@"shopToken": m_userModel.shopToken};
        [self java_postWithValues:parameter ModelType:nil Path:J_log_out hud:@"" Success:^(__kindof YTKBaseRequest *request, id model) {
            [[NSUserDefaults standardUserDefaults] removeObjectForKey:@"pasteboardString"];
            if ([JCAppEventChannel shareInstance].eventSink) {
                [JCAppEventChannel shareInstance].eventSink(@{@"userInfo":@{}});
            }
            [XYCenter sharedInstance].userModel = nil;
            [JCLoginManager sharedInstance].hasLogined = NO;
            [weakSelf postNotification:LOGIN_CHANGED];
            [weakSelf postNotification:LOGIN_CHANGED_SHOP];
            [weakSelf postNotification:LOGOUT_SUCCESS];
            [weakSelf refreshOperateList];
//            [weakSelf deleteWebCache];
//            [weakSelf cleanWKWebView];
//            [weakSelf clearWbCache];
            [weakSelf.navigationController popViewControllerAnimated:YES];
        } failure:^(NSString *msg, id model) {
            [MBProgressHUD showToastWithMessageDarkColor:msg];
        }];
    } alertType:1];
}

- (void)deleteWebCache {

    if ([[UIDevice currentDevice].systemVersion floatValue] >= 9.0) {
        if (@available(iOS 9.0, *)) {
            NSSet *websiteDataTypes
            = [NSSet setWithArray:@[
                WKWebsiteDataTypeOfflineWebApplicationCache,
                WKWebsiteDataTypeMemoryCache,
                WKWebsiteDataTypeLocalStorage,
                WKWebsiteDataTypeCookies,
                WKWebsiteDataTypeSessionStorage,
                WKWebsiteDataTypeIndexedDBDatabases,
                WKWebsiteDataTypeWebSQLDatabases
            ]];
            //// All kinds of data
            //NSSet *websiteDataTypes = [WKWebsiteDataStore allWebsiteDataTypes];
            //// Date from
            NSDate *dateFrom = [NSDate dateWithTimeIntervalSince1970:0];
            //// Execute
            [[WKWebsiteDataStore defaultDataStore] removeDataOfTypes:websiteDataTypes modifiedSince:dateFrom completionHandler:^{
                // Done
            }];
        } else {
            // Fallback on earlier versions
        }

    } else {

        NSString *libraryPath = [NSSearchPathForDirectoriesInDomains(NSLibraryDirectory, NSUserDomainMask, YES) objectAtIndex:0];
        NSString *cookiesFolderPath = [libraryPath stringByAppendingString:@"/Cookies"];
        NSError *errors;
        [[NSFileManager defaultManager] removeItemAtPath:cookiesFolderPath error:&errors];

    }
}

- (NSInteger)numberOfSectionsInGroupShadowTableView:(GroupShadowTableView *)tableView
{
    NSInteger count = self.contentArr.count;
    return count;
}

- (NSInteger)groupShadowTableView:(GroupShadowTableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    NSArray *arr = [self.contentArr safeObjectAtIndex:section];
    return arr.count;
}

- (CGFloat)groupShadowTableView:(GroupShadowTableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return indexPath.row == 5 ? (xy_isLogin ? 60 : 0) : 60;
}

- (CGFloat)groupShadowTableView:(GroupShadowTableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return 15.f;
}

- (UIView *)groupShadowTableView:(GroupShadowTableView *)tableView viewForHeaderInSection:(NSInteger)section{
    UIView *headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 15)];
    return headerView;
}

- (CGFloat)groupShadowTableView:(GroupShadowTableView *)tableView heightForFooterInSection:(NSInteger)section{
    if(section == self.contentArr.count-1){
        return 85.f;
    }else{
        return 0.1;
    }
}

- (UIView *)groupShadowTableView:(GroupShadowTableView *)tableView viewForFooterInSection:(NSInteger)section{
    if(section == self.contentArr.count-1){
        UIView *footerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 85)];
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        btn.frame = CGRectMake(15, 25, SCREEN_WIDTH - 30, 55);
        btn.backgroundColor = COLOR_WHITE;
        btn.layer.cornerRadius = 10.f;
        [btn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00207", @"退出登录") forState:UIControlStateNormal];
        [btn setTitleColor:COLOR_NEW_THEME forState:UIControlStateNormal];
        btn.titleLabel.font = MY_FONT_Regular(16);
        btn.hidden = !xy_isLogin;
        [btn addTarget:self action:@selector(loginOut:) forControlEvents:UIControlEventTouchUpInside];
        [footerView addSubview:btn];
        return footerView;
    }else{
        UIView *footerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 0.1)];
        return footerView;
    }
}

- (UITableViewCell *)groupShadowTableView:(GroupShadowTableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    XYWeakSelf
    tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    NSArray *arr = [self.contentArr safeObjectAtIndex:indexPath.section];
    NSString *title = [arr safeObjectAtIndex:indexPath.row];
    XYTitleCell2 *cell1 = [tableView dequeueReusableCellWithIdentifier:@"XYTitleCell2"];
    XYTitleSwitchCell *cell2 = [tableView dequeueReusableCellWithIdentifier:@"XYTitleSwitchCell"];
    cell1.selectionStyle = UITableViewCellSelectionStyleDefault;
    cell1.backgroundColor = COLOR_WHITE;
    cell1.rightTitle.textColor = HEX_RGB(0x666666);
    cell1.rightTitle.font = [UIFont fontWithName:@"PingFang-SC-Medium" size:16];
    cell1.leftTitle.textColor = XY_HEX_RGB(0x333333);
    cell1.leftTitle.font = [UIFont fontWithName:@"PingFang-SC-Medium" size:16];
    cell1.leftTitleWidthConstraint.constant = SCREEN_WIDTH/2;
    cell1.jiantouImage.image = [XY_IMAGE_NAMED(@"set_return") imageFlippedForRightToLeftLayoutDirection];
    cell1.leftImage.hidden = YES;
    cell1.leftSpaceConstraint.constant = -20;
    cell1.connectStatusInamge.hidden = YES;
    [cell1 showRight:NO topline:YES bottomline:NO jiantou:YES];
    if(xy_isLogin && indexPath.section == 1){
        [cell1 showRight:NO topline:NO bottomline:NO jiantou:YES];
    }else{
        switch (indexPath.row) {
            case 0:
                // 字体管理
                [cell1 showRight:NO topline:NO bottomline:NO jiantou:YES];
            case 1:
                // 语言切换
                [cell1 showRight:NO topline:YES bottomline:NO jiantou:YES];
                break;
            case 2:
                // 清除缓存
            {
                NSUInteger bytesCache = [[SDImageCache sharedImageCache] totalDiskSize];
                NSUInteger bytesCloudCache = [XYTool sizeAtPath:RESOURCE_IMAGE_CLOULD_TEMPLATE_PATH];
                NSUInteger bytesLogCache = [XYTool sizeAtPath:RESOURCE_IMAGE_LOG_PATH];
                NSUInteger bytesCAPMiniAppCache = [XYTool sizeAtPath:RESOURCE_CAPAPP_HOME_PATH];
                NSUInteger bytesUniappPathCache = [XYTool sizeAtPath:RESOURCE_UNIAPP_PATH];
                //换算成 MB (注意iOS中的字节之间的换算是1000不是1024)
                float MBCache = 0;
                if (@available(iOS 11.0, *)) {
                    MBCache = (bytesCache + bytesCloudCache + bytesLogCache + bytesCAPMiniAppCache + bytesUniappPathCache)/1000.0/1000.0;
                }else{
                    MBCache = (bytesCache + bytesCloudCache + bytesLogCache + bytesCAPMiniAppCache + bytesUniappPathCache)/1024.0/1024.0;
                }
                NSString *rightTitle = [NSString stringWithFormat:MBCache == 0 ? @"%0.0fMB" : @"%.2fMB", MBCache]; //获取缓存图片的大小(字节)
                cell1.rightTitle.text = rightTitle;
                [cell1 showRight:YES topline:YES bottomline:NO jiantou:YES];
                break;
            }
            case 3:
                // 当前版本
            {
                [cell1 showRight:YES topline:YES bottomline:YES jiantou:NO];
                cell1.rightTitle.text = [NSString stringWithFormat:@"V%@", [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"]];
                if ([AYCheckManager sharedCheckManager].isNeedUpDate) {
                    cell1.connectStatusInamge.hidden = NO;
                    cell1.connectHeight.constant = 8;
                    cell1.connectWidth.constant = 8;
                    cell1.connectStatusInamge.image = XY_IMAGE_NAMED(@"redPoint_icn");

                }
                break;
            }
            case 4:
                // 剪切板设置
            {
                [cell1 showRight:NO topline:YES bottomline:NO jiantou:YES];
                break;
            }
            case 5:
                // 个性化设置
            {
                [cell1 showRight:NO topline:YES bottomline:NO jiantou:YES];
                break;
            }
            case 6:
                // 应用信息
            {
                [cell1 showRight:NO topline:YES bottomline:NO jiantou:YES];
                break;
            }
            case 7:
                // 调试模式
            {
                cell2.leftTitle.text = title;
                cell2.openSwitch.on = jc_log_switch_open;
                [cell2 setSwitchChangedBlock:^(NSNumber *valueNumber) {
                    [weakSelf logSwitchOperate:valueNumber];
                }];
                return cell2;
                break;
            }
#ifdef DEBUG
            case 8:
                // 网络抓包
            {
                NSString *encryptedSwitch = [[NSUserDefaults standardUserDefaults] objectForKey:@"encryptedSwitch"];
                cell2.leftTitle.text = title;
                cell2.openSwitch.on = encryptedSwitch.integerValue != 1;
                [cell2 setSwitchChangedBlock:^(NSNumber *valueNumber) {
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        if(valueNumber.boolValue){
                            [[NSUserDefaults standardUserDefaults] setObject:@"0" forKey:@"encryptedSwitch"];
                            [MBProgressHUD showToastWithMessageDarkColor:@"可抓包 重启手机后测试"];
                        }else{
                            [[NSUserDefaults standardUserDefaults] setObject:@"1" forKey:@"encryptedSwitch"];
                            [MBProgressHUD showToastWithMessageDarkColor:@"不可抓包 重启手机后测试"];
                        }
                    });
                }];
                return cell2;
                break;
            }

            case 9:
                // 接口加密
            {
                cell2.leftTitle.text = title;
                cell2.openSwitch.on = [XYCenter sharedInstance].isSupportDesRequest;
                [cell2 setSwitchChangedBlock:^(NSNumber *valueNumber) {
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [XYCenter sharedInstance].isSupportDesRequest = valueNumber.boolValue;
                        if(valueNumber.boolValue){
                            [MBProgressHUD showToastWithMessageDarkColor:@"已开启接口加密"];
                        }else{
                            [MBProgressHUD showToastWithMessageDarkColor:@"已关闭接口加密"];
                        }
                    });
                }];
                return cell2;
                break;
            }
#endif
            default:
                break;
        }
    }

    cell1.selectionStyle = UITableViewCellSelectionStyleNone;
    cell1.bottomLine.hidden = YES;
    cell1.leftTitle.text = title;
    cell1.indexPath = indexPath;
    [cell1 setHidden:indexPath.row == 5 ? (xy_isLogin ? false : true) : false];
    return cell1;
}

- (void)logSwitchOperate:(NSNumber *)valueNumber{
    jc_log_switch_open = valueNumber.boolValue;
    __block NSString *logFilePath = [self getCurrentOperateSDKLog];
    if(valueNumber.boolValue){
        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032", @"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app01451", @"您将打开调试模式，有助于协助诊断异常出错。该模式可能会涉及收集此次app运行时相关数据。") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030", @"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00048", @"确定") cancelBlock:^{
            jc_log_switch_open = NO;
            [self.groupShadowTableView reloadData];
        } sureBlock:^{
            if ([[NSFileManager defaultManager] fileExistsAtPath:logFilePath]) {
                [[NSFileManager defaultManager] removeItemAtPath:logFilePath error:nil];
            }
//            [[JCAPI new] setValue:@"127" forKey:@"YMY_Log_Lever"];
        } alertType:3];
    }else{
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        NSString *recordTime = [XYTool getCurrentTimesWithoutTSZ];
        NSString *deviceId = [JCKeychainTool getDeviceIDInKeychain];
        NSString *printNumber = JC_CURRENT_CONNECTED_PRINTER;
        logFilePath = [self getCurrentOperateSDKLog];
        NSData *fileData = [NSData dataWithContentsOfFile:logFilePath];
        self.progressHUD = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
        self.progressHUD.label.text = XY_LANGUAGE_TITLE_NAMED(@"app01453", @"日志上传中...");
        self.logUpdateInfo = @{@"recordTime":UN_NIL(recordTime),@"deviceId":UN_NIL(deviceId),@"printNumber":UN_NIL(printNumber)};
//        [[JCOSSManager sharedManager] oss_uploadFileWithParmsDic:@{@"module":@"USER_ANONYMOUS_APP_LOG",@"param":deviceId} fullFileName:[logFilePath lastPathComponent] fileData:fileData Success:^(NSString * _Nonnull key, NSString * _Nonnull url) {
//            [self uploadLogInfoWithFileUrl:url];
//            [[NSFileManager defaultManager] removeItemAtPath:logFilePath error:nil];
//        } failure:^(NSString * _Nonnull key, NSString * _Nonnull errMsg) {
//            NSLog(@"日志文件上传失败");
//            [self.progressHUD hideAnimated:NO];
//        }];
        XYBlock1 uploadBlock = ^(NSNumber *resultBlock,NSString *errMsg){
            if(resultBlock.boolValue){
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self.progressHUD hideAnimated:NO];
                    if(self.logUpdateInfo && [[XYTool getCurrentVC] isKindOfClass:[JCSettingViewController class]]){
                        NSString *message = [NSString stringWithFormat:@"%@：%@\n%@：%@\n%@：%@",XY_LANGUAGE_TITLE_NAMED(@"app01454", @"记录时间"),self.logUpdateInfo[@"recordTime"],XY_LANGUAGE_TITLE_NAMED(@"app01455", @"设备编号"),self.logUpdateInfo[@"deviceId"],XY_LANGUAGE_TITLE_NAMED(@"app01456", @"打印机编号"),self.logUpdateInfo[@"printNumber"]];
                        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01452", @"日志上传成功") message:message cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01457", @"一键复制") cancelBlock:nil sureBlock:^{
                            UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
                            pasteboard.string = message;
                        } alertType:3];
                    }

                });
            }else{
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self.progressHUD hideAnimated:NO];
                    [MBProgressHUD showToastWithMessageDarkColor:errMsg];
                });
            }
        };
        __block BOOL uploadResult = NO;
        __block NSString *errMsg = @"";
        dispatch_group_t group = dispatch_group_create();
        dispatch_group_enter(group);
        [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"uploadLogFileToSls" arguments:@{} result:^(id  _Nullable result) {
            NSDictionary *resultInfo = result;
            uploadResult = uploadResult?uploadResult:((NSNumber *)resultInfo[@"writeLogInfoSuccess"]).boolValue;
            errMsg = !STR_IS_NIL(errMsg)?errMsg:resultInfo[@"errorMsg"];
            dispatch_group_leave(group);
        }];
        if(!STR_IS_NIL(logFilePath) && fileData.length > 0){
            dispatch_group_enter(group);
            [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"uploadLogFileToSls" arguments:@{@"localPath":logFilePath,@"topic":@"SDKLog"} result:^(id  _Nullable result) {
                NSDictionary *resultInfo = result;
                uploadResult = uploadResult?uploadResult:((NSNumber *)resultInfo[@"writeLogInfoSuccess"]).boolValue;
                errMsg = !STR_IS_NIL(errMsg)?errMsg:resultInfo[@"errorMsg"];
                dispatch_group_leave(group);
            }];
        }
        dispatch_group_notify(group, dispatch_get_main_queue(), ^{
            [self.progressHUD hideAnimated:NO];
            uploadBlock(@(uploadResult),errMsg);
        });

    }
}

- (NSString *)getCurrentOperateSDKLog{
    NSString *fileFolderPath = [NSString stringWithFormat:@"%@/%@",RESOURCE_SDKLOG_PATH,[XYTool getCurrentDate]];
    NSArray *filesArr = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:fileFolderPath error:nil];
    NSString *fileName = [filesArr safeObjectAtIndex:0];
    if(!STR_IS_NIL(fileName)){
        return [NSString stringWithFormat:@"%@/%@",fileFolderPath,fileName];
    }else{
        return nil;
    }

}

- (void)uploadLogInfoWithFileUrl:(NSString *)fileUrl{
    XYBlock1 uploadBlock = ^(NSNumber *resultBlock,NSString *errMsg){
        if(resultBlock.boolValue){
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.progressHUD hideAnimated:NO];
                if(self.logUpdateInfo && [[XYTool getCurrentVC] isKindOfClass:[JCSettingViewController class]]){
                    NSString *message = [NSString stringWithFormat:@"%@：%@\n%@：%@\n%@：%@",XY_LANGUAGE_TITLE_NAMED(@"app01454", @"记录时间"),self.logUpdateInfo[@"recordTime"],XY_LANGUAGE_TITLE_NAMED(@"app01455", @"设备编号"),self.logUpdateInfo[@"deviceId"],XY_LANGUAGE_TITLE_NAMED(@"app01456", @"打印机编号"),self.logUpdateInfo[@"printNumber"]];
                    [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01452", @"日志上传成功") message:message cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01457", @"一键复制") cancelBlock:nil sureBlock:^{
                        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
                        pasteboard.string = message;
                    } alertType:3];
                }

            });
        }else{
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.progressHUD hideAnimated:NO];
                [MBProgressHUD showToastWithMessageDarkColor:errMsg];
            });
        }
    };
    uploadLogInfoFlutter(UN_NIL(fileUrl), @"log_file_url",uploadBlock,J_get_sls_Log_Token);
}


- (void)groupShadowTableView:(GroupShadowTableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    if(indexPath.section == 1 && xy_isLogin){
    }else{
        switch (indexPath.row) {
            case 0:
                // 字体管理
            {
                JCFontManagerController *fontVC = [[JCFontManagerController alloc] init];
                [self.navigationController pushViewController:fontVC animated:YES];
                break;
            }
            case 1:
                // 语言切换
            {
                JCLanguageSetingViewController *langVC = [[JCLanguageSetingViewController alloc] init];
                [self.navigationController pushViewController:langVC animated:YES];
                break;
            }
            case 2:
                // 清除缓存
            {
                NSLog(@"清除缓存");
                XYWeakSelf
                [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app00208",@"确定要清除缓存？") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00209",@"清除") cancelBlock:nil sureBlock:^{
                    [weakSelf deleteCache];
                    [self.groupShadowTableView reloadData];
                }];
                break;
            }
            case 3:
                // 当前版本
            {
                if ([AYCheckManager sharedCheckManager].isNeedUpDate) {
                    [[AYCheckManager sharedCheckManager] showUpdateAleart:NO];
                }
                break;
            }
            case 4:
                // 剪切板设置
            {
                [self gotoFlutterPage:@"pasteboardSetting"
                            arguments:@{@"isPresent": @NO}
                       onPageFinished:^(NSDictionary *_) {
                    // 页面结束回传数据
                }];
            }
                break;
            case 5:
                // 个性化设置
            {
                if(NETWORK_STATE_ERROR){
                    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
                    return;
                }
                JCPersonalSettingViewController * personalVc = [[JCPersonalSettingViewController alloc] init];
                [self.navigationController pushViewController:personalVc animated:YES];
            }
                break;

            case 6:
                // 应用信息
            {
                [self gotoFlutterPage:@"showAppInfo"
                            arguments:@{@"isPresent": @NO}
                       onPageFinished:^(NSDictionary *_) {
                    // 页面结束回传数据
                }];
            }
                break;
#ifdef DEBUG
            case 10:{
                LaboratoryViewController *laboratoryViewController = [[LaboratoryViewController alloc] init];
                [self.navigationController pushViewController:laboratoryViewController animated:YES];
                break;
            }
#endif
            default:
                break;
        }
    }
}

- (void)logout{//
    XYWeakSelf
    [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app00313",@"确定退出吗？") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00207",@"退出登录") cancelBlock:nil sureBlock:^{
        [self java_postWithValues:@{} ModelType:nil Path:J_log_out hud:@"" Success:^(__kindof YTKBaseRequest *request, id model) {
            [[NSUserDefaults standardUserDefaults] removeObjectForKey:@"pasteboardString"];
            if ([JCAppEventChannel shareInstance].eventSink) {
                [JCAppEventChannel shareInstance].eventSink(@{@"userInfo":@{}});
            }
            [XYCenter sharedInstance].userModel = nil;
            [weakSelf postNotification:LOGIN_CHANGED];
            [weakSelf postNotification:LOGIN_CHANGED_SHOP];
            [weakSelf refreshOperateList];
            [JCLoginManager sharedInstance].hasLogined = NO;
            [weakSelf.navigationController popViewControllerAnimated:YES];
        } failure:^(NSString *msg, id model) {
            [MBProgressHUD showToastWithMessageDarkColor:msg];
        }];
    } alertType:2];
}

- (void)deleteCache{
    XYWeakSelf
    NSUInteger bytesCache = [[SDImageCache sharedImageCache] totalDiskSize];
    NSUInteger bytesCloudCache = [XYTool sizeAtPath:RESOURCE_IMAGE_CLOULD_TEMPLATE_PATH];
    NSUInteger bytesLogCache = [XYTool sizeAtPath:RESOURCE_IMAGE_LOG_PATH];
    NSUInteger bytesCAPMiniAppCache = [XYTool sizeAtPath:RESOURCE_CAPAPP_HOME_PATH];
    NSUInteger bytesUseImageCache = [XYTool sizeAtPath:RESOURCE_IMAGE_TEMPLATE_PATH];
    NSUInteger bytesUniappPathCache = [XYTool sizeAtPath:RESOURCE_UNIAPP_PATH];
    NSInteger countSizeNum = bytesCache + bytesCloudCache + bytesLogCache  + bytesUseImageCache + bytesUniappPathCache + bytesCAPMiniAppCache;

    if(countSizeNum > 0){
        __block MBProgressHUD *hud= [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
        [[SDImageCache sharedImageCache] clearDiskOnCompletion:nil];
        [[SDImageCache sharedImageCache] clearMemory];
        NSString *imageDoucPath = RESOURCE_IMAGE_CLOULD_TEMPLATE_PATH;
        NSDirectoryEnumerator *enumerator = [[NSFileManager defaultManager] enumeratorAtPath:imageDoucPath];
        for (NSString *fileName in enumerator) {
            [[NSFileManager defaultManager] removeItemAtPath:[imageDoucPath stringByAppendingPathComponent:fileName] error:nil];
        }
        [[XYCenter sharedInstance] cleanUniappCapCache];
        NSString *userImageDoucPath = RESOURCE_IMAGE_TEMPLATE_PATH;
        NSDirectoryEnumerator *userImageEnumerator = [[NSFileManager defaultManager] enumeratorAtPath:userImageDoucPath];
        for (NSString *fileName in userImageEnumerator) {
            [[NSFileManager defaultManager] removeItemAtPath:[userImageDoucPath stringByAppendingPathComponent:fileName] error:nil];
        }
        NSArray *dbFiles = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:RESOURCE_DB_PATH error:nil];
        for (NSString *fileString in dbFiles) {
            [[JCFMDB shareDatabase:fileString] jc_deleteTable:TABLE_LOGINFO whereFormat:@""];
        }

        // 清除用户标签纸使用记录
        [JCTemplateDBManager db_deletLabelUseInfo];

        [weakSelf postNotification:CLEAN_CAECH];
        NSString *logImageDoucPath = RESOURCE_IMAGE_LOG_PATH;
        NSDirectoryEnumerator *enumerator2 = [[NSFileManager defaultManager] enumeratorAtPath:logImageDoucPath];
        for (NSString *fileName in enumerator2) {
            [[NSFileManager defaultManager] removeItemAtPath:[logImageDoucPath stringByAppendingPathComponent:fileName] error:nil];
        }
        [weakSelf cleanWKWebView];
        [weakSelf clearWbCache];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if([JCBluetoothManager sharedInstance].rfidModel){
                [[NSNotificationCenter defaultCenter] postNotificationName:RfidPrinterConnectedNotification object:nil];
            }
            hud.label.text = XY_LANGUAGE_TITLE_NAMED(@"app00439",@"清除成功");
            [hud hideAnimated:YES];
            [weakSelf.groupShadowTableView reloadData];
        });
    }else{
    }
    // 清除Flutter缓存
    [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"clearCache" arguments:nil result:nil];
    [weakSelf.groupShadowTableView reloadData];
}

- (void)cleanWKWebView{
    NSArray *types = @[WKWebsiteDataTypeMemoryCache,WKWebsiteDataTypeDiskCache];
    NSSet *websiteDataTypes = [NSSet setWithArray:types];
    NSDate *dateFrom = [NSDate dateWithTimeIntervalSince1970:0];
    [[WKWebsiteDataStore defaultDataStore]removeDataOfTypes:websiteDataTypes modifiedSince:dateFrom completionHandler:^{
        NSLog(@"WKWebView清理完成");
    }];
}

- (void)clearWbCache {
    [[NSURLCache sharedURLCache] removeAllCachedResponses];
    [[NSURLCache sharedURLCache] setDiskCapacity:0];
    [[NSURLCache sharedURLCache] setMemoryCapacity:0];
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
}

- (void)refreshOperateList{
    self.navigationItem.title = XY_LANGUAGE_TITLE_NAMED(@"app00297",@"设置");
    NSString *languageSwitch = XY_LANGUAGE_TITLE_NAMED(@"app00337",@"语言切换");
    NSString *fontManager = XY_LANGUAGE_TITLE_NAMED(@"app00201",@"字体管理");
    NSString *cleanCeath = XY_LANGUAGE_TITLE_NAMED(@"app00204",@"清除缓存");
    NSString *currentVersion = XY_LANGUAGE_TITLE_NAMED(@"app00203",@"当前版本");
    NSString *logDebug = XY_LANGUAGE_TITLE_NAMED(@"app01459", @"调试模式");
    NSString *pasteboardSetting = XY_LANGUAGE_TITLE_NAMED(@"app100000457", @"剪切板设置");
    //app100001677
    NSString *userSetting = XY_LANGUAGE_TITLE_NAMED(@"app100001677", @"个性化设置");
    NSString *zhuabao = XY_LANGUAGE_TITLE_NAMED(@"", @"网络请求抓包");
    NSString *jiami = XY_LANGUAGE_TITLE_NAMED(@"", @"接口加密");
    NSString *infoMation = XY_LANGUAGE_TITLE_NAMED(@"app100001150", @"应用信息");

//  NSDictionary *languageDic = XY_LANGUAGE_DETAIL_DIC;
//  NSDictionary *languageInfo = languageDic[@""];
//  NSString *value = languageInfo[@"value"];
  self.contentArr = @[@[fontManager,languageSwitch,cleanCeath,currentVersion,pasteboardSetting,userSetting,infoMation,logDebug]];

#ifdef DEBUG
    NSString *laboratory = XY_LANGUAGE_TITLE_NAMED(@"", @"实验室");
    self.contentArr = @[@[fontManager,languageSwitch,cleanCeath,currentVersion,pasteboardSetting,userSetting,infoMation,logDebug,zhuabao,jiami,laboratory]];
#endif

    [self.cancellationBtn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00957", @"用户注销") forState:UIControlStateNormal];
    [self.groupShadowTableView reloadData];
}

- (void)loginChanged:(NSNotification *)notification{
    [self refreshOperateList];
}

- (void)changeLanguage:(NSNotification *)notification{
    [self refreshOperateList];
}
#pragma mark - 系统蓝牙状态更换
- (void)dealloc
{
    JCNCRemoveOb(self, PrinterStatusNotification, nil);
    JCNCRemoveOb(self, LOGIN_CHANGED, nil);
    JCNCRemoveOb(self, LOGOUT_SUCCESS, nil);
}

@end
