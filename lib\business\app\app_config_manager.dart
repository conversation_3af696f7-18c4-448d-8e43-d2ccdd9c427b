import 'package:flutter/services.dart';
import 'package:niimbot_cache_manager/niimbot_cache_manager.dart';

import '../../application.dart';
import '../../macro/constant.dart';

class AppConfigManager {
  static final AppConfigManager _instance = AppConfigManager._internal();

  factory AppConfigManager() {
    return _instance;
  }

  AppConfigManager._internal();

  Future<Map?> getAppConfigInfo() async {
    String? defaultConfig = await rootBundle.loadString("assets/config/app_config.json");
    final offlineConfig = await NiimbotCacheManager().getAppConfigInfo(defaultConfigInfo: defaultConfig);
    saveAppConfig(offlineConfig);
    return offlineConfig;
  }


  void saveAppConfig(Map? appConfig) {
    if (appConfig != null) {
      Application.sp.setString(ConstantKey.sentrySampleRate, (appConfig['CloudPrintFlutterSentrySampleRate'] ?? 0.0001).toString());
      Application.sp.setString(ConstantKey.sentryTracesSampleRate, (appConfig['CloudPrintFlutterSentryTracesSampleRate'] ?? 0.0001).toString());
      Application.sp.setStringList(ConstantKey.notSupportAliasList, List<String>.from(appConfig['abnormalMachine'] ?? []));
    }
  }

  double getSentrySampleRate() {
    return double.parse(Application.sp.getString(ConstantKey.sentrySampleRate) ?? '0.0001');
  }
  double getSentryTracesSampleRate() {
    return double.parse(Application.sp.getString(ConstantKey.sentryTracesSampleRate) ?? '0.0001');
  }

  List<String> getNotSupportAliasList() {
    return Application.sp.getStringList(ConstantKey.notSupportAliasList) ?? [];
  }
}
