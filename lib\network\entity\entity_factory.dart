import 'package:text/connect/machine_alias.dart';
import 'package:text/pages/etag/home/<USER>/etag_template_size_model.dart';
import 'package:text/pages/etag/home/<USER>/etag_template_sort_model.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_category_list_model.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_model.dart';
import 'package:text/pages/industry_template/home/<USER>/template_size_model.dart';
import 'package:text/pages/industry_template/select_label/barcode_consumable.dart';

import '../../pages/industry_template/select_label/hardware_list_model.dart';
import '../../pages/industry_template/select_label/label_category_list_model.dart';
import '../../pages/industry_template/select_label/label_category_model.dart';

class EntityFactory {
  static T? generateOBJ<T>(json) {
    if (json == null) {
      return null;
    } else if (T == HardwareListModel) {
      return HardwareListModel.fromJson(json) as T;
    } else if (T == LabelCategoryListModel) {
      return LabelCategoryListModel.fromJson(json) as T;
    } else if (T == LabelCategoryModel) {
      return LabelCategoryModel.fromJson(json) as T;
    } else if (T == IndustryTemplateCategoryListModel) {
      return IndustryTemplateCategoryListModel.fromJson(json) as T;
    } else if (T == TemplateData) {
      return TemplateData.fromJson(json) as T;
    } else if (T == IndustryTemplateModel) {
      return IndustryTemplateModel.fromJson(json) as T;
    } else if (T == HardwareModelList) {
      return HardwareModelList.fromJson(json) as T;
    } else if (T == EtagTemplateSizeModel) {
      return EtagTemplateSizeModel.fromJson(json) as T;
    } else if (T == EtagTemplateSortModel) {
      return EtagTemplateSortModel.fromJson(json) as T;
    } else if (T == TemplateSizeModel) {
      return TemplateSizeModel.fromJson(json) as T;
    } else if (T == BarCodeConsumable) {
      return BarCodeConsumable.fromJson(json) as T;
    } else if (T == MachineAlias) {
      return MachineAlias.fromJson(json) as T;
    } else {
      return json;
    }
  }
}
