//
//  JCNPSWebView.h
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2024/3/7.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface JCNPSWebView : UIView

//load url
-(void)loadUrl:(NSString *)url;
//close
@property(nonatomic,copy)XYBlock closeClick;

@property(nonatomic,assign)BOOL loadFailed;

@property(nonatomic,assign)BOOL loadSuccess;

@property(nonatomic,copy)NSString * loadUrl;

@property(nonatomic,copy)NSString * sku;

@property(copy,nonatomic) NSString *source;

@property(copy,nonatomic) NSString *uniAppId;


-(void)reload;
@end

NS_ASSUME_NONNULL_END
