/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "Uygulamanın barkod okuma, metin tanıma ve fotoğraf çekme işlevleri için kamera iznine erişmesi gerekmektedir. Onaylıyor musunuz?";
NSBluetoothPeripheralUsageDescription = "Uygulamanın yazıcıya bağlanmak için bluetooth izinlerine erişmesi gerekmektedir. Onaylıyor musunuz?";
NSBluetoothAlwaysUsageDescription = "Uygulamanın yazıcıya bağlanmak için bluetooth izinlerine erişmesi gerekmektedir. Onaylıyor musunuz?";
NSContactsUsageDescription = "Uygulamanın kişileri ekleme işlevini kullanabilmesi için Telefon izinlerine erişmesi gerekmektedir. Onaylıyor musunuz?";
NSMicrophoneUsageDescription = "Uygulamanın ses tanıma işlevini gerçekleştirmesi için mikrofon iznine erişmesi gerekmektedir. Onaylıyor musunuz?";
NSPhotoLibraryUsageDescription = "Bu izne erişim fotoğrafların basımı, barkod tanıma, karekod okuma, metin tanıma, özel avatar ayarlama ve benzeri işlevler için gereklidir. Lütfen Niimbot Bulut Baskı'nın albüme normal bir şekilde erişebilmesi için \"Tüm Fotoğraflara İzin Ver\" seçeneğini seçin. \"Fotoğrafları Seç\" seçeneğini seçerseniz, Niimbot Bulut Baskı eklenecek diğer fotoğraflara erişemeyecektir.";
NSLocationWhenInUseUsageDescription = "Yakınınızdaki kablosuz ağların kullanımını kolaylaştırmak için Niimbot Bulut Baskı'nın konum iznine erişim izni verin.";
NSLocationAlwaysUsageDescription = "Yakınınızdaki kablosuz ağların kullanımını kolaylaştırmak için Niimbot Bulut Baskı'nın konum iznine erişim izni verin.";
NSLocationAlwaysAndWhenInUseUsageDescription = "Yakınınızdaki kablosuz ağların kullanımını kolaylaştırmak için Niimbot Bulut Baskı'nın konum iznine erişim izni verin.";
NSSpeechRecognitionUsageDescription = "Uygulama ses tanıma işlevini gereken izinlere eriştiği zaman yerine getirebilecektir. Onaylıyor musunuz?";
"UILaunchStoryboardName" = "LaunchScreen";
