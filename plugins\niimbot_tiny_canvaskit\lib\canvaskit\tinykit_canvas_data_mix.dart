import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/config/element_create_required_model.dart';
import 'package:flutter_canvas_plugins_interface/plugin/advance_qr_code_model.dart';
import 'package:flutter_canvas_plugins_interface/plugin/excel_data.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:flutter_canvas_plugins_interface/utils/display_util.dart';
import 'package:flutter_canvas_plugins_interface/utils/precision_num.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:netal_plugin/netal_plugin.dart';
import 'package:netal_plugin/niimbot_netal.dart';
import 'package:netal_plugin/utils/index.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/components/interactive_element.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/model/canvas_element.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/model/element/bar_code_element.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/model/element/date_element.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/model/element/json_element.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/model/element/qr_code_element.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/model/element/text_element.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/model/element/text_element_bo.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/model/template_data.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/model/tinykit_box_frame_info.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/provider/elements_data_changed_notifier.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/tinykit_canvas_box_frame.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/tinykit_canvas_core.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/tinykit_lego_utils.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/tinykit_theme_color.dart';
import 'package:provider/provider.dart';

Logger _logger = Logger("MixNbCanvasData", on: kDebugMode);

/// 画板数据层
class CanvasDataMix extends StatefulWidget {
  const CanvasDataMix({
    super.key,
    this.startOffset,
    required this.templateData,
    this.onElementFocusChanged,
    required this.onElementValueEditorDisplay,
    required this.onElementMaterialClicked,
    required this.labelInfo,
    required this.onLabelChange,
    required this.onAdvanceQRCodeInfoChanged,
    required this.onAttrPanelChange,
    required this.onElementFocusState,
    required this.rfidBind,
    required this.onElementMaterialBoderClicked,
    required this.onToLabelDetail,
    required this.frameInfo,
  });

  final Offset? startOffset;
  final TemplateData? templateData;

  /// 焦点变动 (元素选中状态变化)   changetype    1 拖拽 0 其他, -1 点击背景
  final void Function(List<CanvasElement>, int changetype)? onElementFocusChanged;

  /// 输入焦点变动
  /// [cursorElement] 当前编辑的元素，表格时 .data 是 cell element
  /// [focusedElement] 选中态元素，表格时 .data 是 table element，其余元素与 [cursorElement] 相同
  final void Function(CanvasElement cursorElement, CanvasElement focusedElement, {bool isDoubleClick})
      onElementValueEditorDisplay;

  //素材点击
  final void Function() onElementMaterialClicked;

  //边框素材点击
  final void Function() onElementMaterialBoderClicked;

  //去标签详情页
  final void Function() onToLabelDetail;

  final String labelInfo;

  final void Function() onLabelChange;
  final void Function() onAttrPanelChange;
  final Function() onAdvanceQRCodeInfoChanged;

  // 元素状态
  final ElementFocusState Function() onElementFocusState;

  /// rfid绑定
  final void Function() rfidBind;

  final TinyKitBoxFrameInfo frameInfo;

  @override
  CanvasDataMixState createState() => CanvasDataMixState();
}

class CanvasDataMixState extends State<CanvasDataMix> {
  ValueNotifier<bool> isTranslatingNotifier = ValueNotifier(true);

  bool _multiSelect = false;

  List<CanvasElement> _focusedElements = [];
  GlobalKey<CanvasCoreState> _nbCanvasTemplateKey = GlobalKey();

  late TemplateData? _templateData;

  void _setTemplateData(String canvasJson) {
    Map<String, dynamic> canvasJsonMap = jsonDecode(canvasJson);
    _templateData = TemplateData.fromJson(canvasJsonMap);
    //String ? trackJson = _templateData?.generateCanvasJson(savePrintTemplate: true);
    //_logger.log("trackJson===========> ${trackJson}");
  }

  List<CanvasElement>? get _canvasElements => _templateData?.canvasElements;

  List<CanvasElement> get focusedElements => _focusedElements;

  set multiSelect(bool value) {
    if (_multiSelect != value) {
      _multiSelect = value;
      if (value == false) {
        _focusedElements.clear();
      } else {}
    }
  }

  clearMultiSelectAndFocused({bool needFresh = false}) {
    if (needFresh) {
      setState(() {
        _logger.log("==============调用clearMultiSelectAndFocused---刷新");
        _multiSelect = false;
        _focusedElements.clear();
      });
    } else {
      _logger.log("==============调用clearMultiSelectAndFocused");
      _multiSelect = false;
      _focusedElements.clear();
    }
  }

  ///获取画板结果数据(预览数据/新添加元素)
  String? getCanvasJsonDATA() {
    return _templateData?.generateCanvasJson(savePrintTemplate: false);
  }

  void resetTemplate(TemplateData templateData) {
    _templateData = templateData;
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    _templateData = widget.templateData;
    _logger.log("=============canvas_core_mix组件的initState =============");
  }

  @override
  void dispose() {
    // canvasUpdateBloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _logger.log("=============canvas_core_mix组件的build =============");
    return Consumer<ElementsDataChangedNotifier>(
        builder: (BuildContext context, ElementsDataChangedNotifier value, Widget? child) {
      /// 将正在编辑文本的 tableCell 对象转换为 table 对象
      final dataChangedCanvasElements = value.dataChangedCanvasElements?.map((e) {
        return e;
      }).toList();

      /// 处理元素属性更新
      handleElementsUpdated(dataChangedCanvasElements);

      if (dataChangedCanvasElements != null && dataChangedCanvasElements.length > 0) {
        /// 黑色工具条已显示的状态下，切换文本方向需要刷新工具条
        /// 检查是否文本元素方向改变
        List<CanvasElement> needAddAssociateElements = [];
        dataChangedCanvasElements.forEach(
          (_element) {
            _logger.log("aft element==============> ${_element.data.toJson()}");
            CanvasElement? needAddElement = getAssociateElement(_element);
            if (needAddElement != null) {
              needAddAssociateElements.add(needAddElement);
            }
          },
        );
        if (needAddAssociateElements.isNotEmpty) dataChangedCanvasElements.addAll(needAddAssociateElements);
      }

      /// 重置元素数据变动的数组
      value.resetDataChangedElement();

      ///刷新元素属性和内容
      _handleLegoElementsRefresh(dataChangedCanvasElements);

      return CanvasCore(
        key: _nbCanvasTemplateKey,
        templateWidth: _templateData?.designWidth,
        templateHeight: _templateData?.designHeight ?? 0,
        backgroundImage: _templateData?.backgroundImage ?? "",
        multipleBackIndex:
            (_templateData?.multipleBackIndex.toInt() ?? 0) < 0 ? 0 : _templateData!.multipleBackIndex.toInt(),
        localBackgrounds: _templateData?.localBackground ?? [],
        canvasRotate: _templateData?.canvasRotate.toDouble() ?? 0,
        readOnly: false,
        canvasElements: _canvasElements ?? [],
        focusedElements: _focusedElements,
        // multiSelect: _multiSelect,
        isMultiSelect: _isMultiSelectStatus,
        onTranslated: _handleBoxTranslated,
        onTranslateEnded: _handleBoxTranslateEnded,
        onBoxEdit: _handleBoxEdit,
        onBoxDeleted: _handleBoxDeleted,
        onBoxTap: _handleSelectionChanged,
        onBoxCopied: _handleBoxCopied,
        onBoxLock: _handleBoxLock,
        onBoxRotate: _handleBoxRotate,
        onBoxAlign: _handleBoxAlign,
        onCellAction: _handleCellAction,
        onMergeCells: _handleMergeCells,
        onSplitCell: _handleSplitCells,
        onItemDoubleTap: _handleBoxDoubleTap,
        onMultiSelectChanged: _handleMultiSelectChanged,
        onSelectAll: _handleSelectAll,
        onSelectAllText: _handleSelectAllText,
        onSelectAllLine: _handleSelectAllLine,
        idBuilder: (element) => element.data.id,
        isItemFocused: isItemFocused,
        isLock: isLock,
        isMirror: isMirror,
        isAssociateElement: isAssociateElement,
        isResizeEnable: isResizeEnable,
        multiElementsRect: multiElementsRect,
        itemControlsBuilder: itemControlsBuilder,
        itemBuilder: itemBuilder,
        startOffset: widget.startOffset,
        // autoSizeModel: snapshot.data,
        onInteractionUpdate: _onInteractionUpdate,
        fetchBatchInfo: () {},
        onBackgroundImageChange: _onTemplateBackgroundChangeTap,
        labelInfo: widget.labelInfo,
        onLabelChange: widget.onLabelChange,
        onElementFocusState: widget.onElementFocusState,
        isShowHandleBtn: _isShowRightCorner(),
        rfidBind: widget.rfidBind,
        materialModelSn: "",
      );
    });
  }

  bool _isMultiSelectStatus() {
    return _multiSelect;
  }

  resetElementMirrorLocation(JsonElement item) {
    String value = "";
    double templateWidth = _templateData?.width?.toDouble() ?? 0;
    double templateHeight = _templateData?.height?.toDouble() ?? 0;

    /// 画板中心点
    Offset templateCenter = Offset(templateWidth / 2.0, templateHeight / 2.0);
    if (item.isOpenMirror == 1) {
      JsonElement jsonElement = item;
      String mirrorId = jsonElement.generateFixedMirrorId();
      for (var mirrorElement in (_templateData?.canvasElements ?? [])) {
        if (mirrorId == mirrorElement.data.id) {
          JsonElement elementModel = mirrorElement.data;
          Offset distanceToCenter = templateCenter -
              Offset(jsonElement.x.toDouble() + jsonElement.width, jsonElement.y.toDouble() + jsonElement.height);

          Offset mirrorPosition = templateCenter + distanceToCenter;
          if (jsonElement.mirrorType == 1) {
            /// 1: 画板中心 y 轴镜像
            elementModel.y = mirrorPosition.dy;
          } else if (jsonElement.mirrorType == 2) {
            /// 2: 画板中心 x 轴镜像
            elementModel.x = mirrorPosition.dx;
          } else {
            /// 0: 画板中心点镜像
            elementModel.x = mirrorPosition.dx;
            elementModel.y = mirrorPosition.dy;
          }
          elementModel.width = jsonElement.width;
          elementModel.height = jsonElement.height;
          break;
        }
      }
    }
  }

  /// 新增isCleanImageWidget参数，代表是否只返回干净的Image组件，防止wrap-Stack导致的包裹尺寸问题
  Widget itemBuilder(CanvasElement canvasElement, {bool isCleanImageWidget = false}) {
    JsonElement item = canvasElement.data;
    String elementId = canvasElement.elementId;

    if (item.width <= 0 || item.height <= 0) {
      return Container();
    }
    CanvasElement? _focusedElement = _focusedElements.singleWhereOrNull((element) => element.data.id == item.id);

    BoxConstraints boxConstraints = item.getBoxConstrains(context);
    if (!(item is TextElement && (item).isSupportBoxStyle())) {
      /// 文本框样式不能调整宽高，否则移动偏移错误
      if (item.width.mm2dp() < boxConstraints.minWidth) {
        item.width = boxConstraints.minWidth.dp2mm();
      } else if (item.width.mm2dp() > boxConstraints.maxWidth) {
        item.width = boxConstraints.maxWidth.dp2mm();
      }

      if (item.height.mm2dp() < boxConstraints.minHeight) {
        item.height = boxConstraints.minHeight.dp2mm();
      } else if (item.height.mm2dp() > boxConstraints.maxHeight) {
        item.height = boxConstraints.maxHeight.dp2mm();
      }
    }

    if (item.type == ElementItemType.text) {
      // _templateData?.updateUsedFont(item as TextElement);
      TextElement textElement = item as TextElement;

      ///判断文本框内容如果是请输入，改变"请输入" 默认颜色为指定值 76, 60, 60, 67
      if (textElement.value == '' || textElement.value == '请输入...') {
        textElement.value = '';

        ///特殊需求
        if (_canvasElements!.isNotEmpty) {
          CanvasElement _ce = _canvasElements![0];
          if (_ce.elementId == item.id) {
            textElement.value = '请输入...';
          }
        }

        textElement.elementColor = [76, 0, 0, 0];
        //canvasElement.resetImageCache();
      } else {
        textElement.elementColor = [255, 0, 0, 0];
      }

      CanvasElement? currentElement =
          _canvasElements?.singleWhereOrNull((element) => element.elementId == elementId && element.data.id == item.id);

      bool cmpRet = false;
      if (currentElement != null) {
        cmpRet = TinyKitLegoUtils.instance.areMapsEqual(item.toJson(), currentElement.data.toJson());
      }
      if (!cmpRet) {
        canvasElement.resetImageCache();
      }
    }

    NetalImageResult imageData = getElementImage(canvasElement);
    if (imageData.pixels.isNotEmpty) {
      /// 按图像库返回重新校正组件的宽高
      //文字方向做特殊处理
      if (item.isTextElement() && (item as TextElement).specialRelocation()) {
        double fixedHeight = imageData.height.toDouble().px2mm().toDouble();
        double fixedWidth = imageData.width.toDouble().px2mm().toDouble();
        //竖排文字，保持中心点不变，宽高调换后更新
        TextElement textElement = item;
        if (textElement.lastHeight != fixedHeight || textElement.lastWidth != fixedWidth) {
          Offset center = Offset(item.x.toDouble(), item.y.toDouble()) +
              Offset((textElement.lastWidth?.toDouble() ?? 0) / 2, (textElement.lastHeight?.toDouble() ?? 0) / 2);
          item.y = center.dy - fixedHeight / 2;
          item.x = center.dx - fixedWidth / 2;
          textElement.lastHeight = fixedHeight;
          textElement.lastWidth = fixedWidth;
        }

        item.height = fixedHeight;
        item.width = fixedWidth;
        if ((item).isSupportBoxStyle()) {
          /// 使用图像库返回字号
          //(item)
          //    .updateFontSize(imageData.fontSize, imageData.fontSizeThreshold);
          (item).updateBoxStyleWithInt(imageData.boxStyle, canvasElement);
        }
      } else if (item.isTextElement()) {
        double fixedHeight = imageData.height.toDouble().px2mm().toDouble();
        double fixedWidth = imageData.width.toDouble().px2mm().toDouble();
        if ((item as TextElement).isSupportBoxStyle()) {
          (item).handleTextElementPostion(fixedWidth, fixedHeight);

          /// 使用图像库返回字号
          //(item).updateFontSize(imageData.fontSize, imageData.fontSizeThreshold);
          (item).updateBoxStyleWithInt(imageData.boxStyle, canvasElement);
        } else {
          if (item.height != fixedHeight || item.width != fixedWidth) {
            // 文本旋转状态下宽高变化后校正 x,y
            double differenceHeight = fixedHeight - item.height;
            double differenceWidth = fixedWidth - item.width;

            Offset center = Offset(item.x.toDouble(), item.y.toDouble()) +
                Offset(item.width.toDouble() / 2, item.height.toDouble() / 2);

            if (item.rotate == 90) {
              Offset offsetCenter = center + Offset(-differenceHeight, differenceWidth) / 2;
              item.y = (offsetCenter - Offset(fixedWidth, fixedHeight) / 2).dy;
              item.x = (offsetCenter - Offset(fixedWidth, fixedHeight) / 2).dx;
            } else if (item.rotate == 180) {
              item.y -= (fixedHeight - item.height);
              item.x -= (fixedWidth - item.width);
            } else if (item.rotate == 270) {
              Offset offsetCenter = center + Offset(differenceHeight, -differenceWidth) / 2;
              item.y = (offsetCenter - Offset(fixedWidth, fixedHeight) / 2).dy;
              item.x = (offsetCenter - Offset(fixedWidth, fixedHeight) / 2).dx;
            }
          }
          _logger.log(
              "text position x=${item.x} y=${item.y} w=${item.width} h=${item.height} imagew=$fixedWidth imageh=$fixedHeight");
          item.height = fixedHeight;
          item.width = fixedWidth;
        }
      }

      if (item.isTextElement()) {
        //先变换宽高 然后在切换文本方向的话，修复中心点不对的问题
        TextElement textElement = item as TextElement;
        textElement.lastWidth = item.width.toDouble();
        textElement.lastHeight = item.height.toDouble();
      }
      resetElementMirrorLocation(item);

      ///barcode qrcode 合规性检测
      if (item.type == ElementItemType.barcode || item.type == ElementItemType.qrcode) {
        String? contentValue = "";
        // if (item.isBinding == 1) {
        if (item.isBindingElement()) {
          // contentValue = _templateData?.escapeBindingValue(
          //     item, _templateData?.currentPageIndex,
          //     addContentTitle: false, parseContentAffix: true);
        } else {
          contentValue = item.value ?? '';
        }
        if (item.type == ElementItemType.barcode && contentValue.isNotEmpty) {
          item.checkResult =
              NetalUtils.barCodeContentCheck((item as BarCodeElement).toNetal(contentValue: contentValue)) == false
                  ? 1
                  : 0;
        } else if (item.type == ElementItemType.qrcode && contentValue != null && contentValue.isNotEmpty) {
          item.checkResult =
              NetalUtils.qrCodeContentCheck((item as QrCodeElement).toNetal(contentValue: contentValue)) == false
                  ? 1
                  : 0;
          _logger.log("check result ${item.checkResult} code type==${(item).codeType} contentValue=$contentValue");
        }
        if (item.isOpenMirror == 0 && item.mirrorId.isNotEmpty) {
          //获取主元素
          CanvasElement? element = _canvasElements?.singleWhereOrNull((element) => element.elementId == item.mirrorId);
          if (element != null) {
            item.checkResult = (element.data).checkResult;
          }
        }
      }
      // if (item.type == ElementItemType.qrcode &&  (item as QrCodeElement).codeType == QrcodeType.PDF417) {
      if (item.type == ElementItemType.qrcode) {
        item.height = imageData.height.toDouble().px2mm();
      }
      double width;
      double height;
      double scale;
      // if (item is ImageElement) {
      //   double imagePxRatio = ImageElementHelper.getImagePxRatio(item);
      //   width = imageData.width * DisplayUtil.dpRatio / imagePxRatio;
      //   height = imageData.height * DisplayUtil.dpRatio / imagePxRatio;
      //   scale = imagePxRatio / DisplayUtil.dpRatio;
      // } else {
      width = imageData.width.px2dp();
      height = imageData.height.px2dp();
      scale = DisplayUtil.pxRatio / DisplayUtil.dpRatio;
      // }
      // 是否只返回干净的Image组件
      if (isCleanImageWidget) {
        var alignment = Alignment.center;
        alignment = adjustTextElementAlign(item, alignment);
        return Image.memory(
          imageData.pixels,
          /** 避免闪动 */
          gaplessPlayback: true,
          alignment: alignment,
          fit: item.isTextElement() ? BoxFit.none : BoxFit.contain,
          width: width,
          height: height,
          scale: scale,
        );
      } else {
        var alignment = Alignment.topLeft;
        alignment = adjustTextElementAlign(item, alignment);
        return Stack(
          clipBehavior: Clip.none,
          children: [
            Positioned(
                left: 0.0,
                top: 0.0,
                child: Image.memory(
                  imageData.pixels,
                  /** 避免闪动 */
                  gaplessPlayback: true,
                  alignment: alignment,
                  fit: item.isTextElement() ? BoxFit.none : BoxFit.fill,
                  width: width,
                  height: height,
                  scale: scale,
                )),
            Container(),
          ],
        );
      }
    }
    return Container();
  }

  /// 垂直模式必须调整填充布局模式，否则会出现闪动问题
  Alignment adjustTextElementAlign(JsonElement item, Alignment alignment) {
    if (item is TextElement && item.isSupportBoxStyle()) {
      // TextElement textElement = item;
      // if (textElement.getTextMode() == TextMode.Vertical ||
      //     textElement.getTextMode() == TextMode.Horizontal_90) {
      //   alignment = Alignment.topRight;
      // } else if (textElement.getTextMode() == TextMode.Horizontal) {
      //   if (textElement.textAlignHorizontal == 0) {
      //     alignment = Alignment.topLeft;
      //   } else if (textElement.textAlignHorizontal == 1) {
      //     alignment = Alignment.topCenter;
      //   } else if (textElement.textAlignHorizontal == 2) {
      //     alignment = Alignment.topRight;
      //   }
      // }
    }
    return alignment;
  }

  Widget getBarcodeWarningWidget(int imageWidth, int imageHeight, {bool isAdvanceQRCode = false}) {
    double dpW = imageWidth.px2dp();
    double dpH = imageHeight.px2dp();
    double warningTextW = 115;
    double warningTextH = 26;
    if (dpW > warningTextW && !isAdvanceQRCode) {
      double offsetX = (dpW - warningTextW) / 2;
      double offsetY = (dpH - warningTextH) / 2;
      return Positioned(
        left: offsetX,
        top: offsetY,
        child: Container(
          width: warningTextW,
          height: warningTextH,
          decoration: BoxDecoration(color: TinyKitThemeColor.COLOR_FB4B42, borderRadius: BorderRadius.circular(40)),
          child: Center(child: Text("不符合编码规范")),
        ),
      );
    } else {
      double warningImageW = 26;
      double warningImageH = 26;
      double offsetX = (dpW - warningImageW) / 2;
      double offsetY = (dpH - warningImageH) / 2;
      return Positioned(
          left: offsetX,
          top: offsetY,
          child: Center(
            child: Image.asset(
              "assets/common/icon_warning.png",
              package: "niimbot_flutter_canvas",
              fit: BoxFit.cover,
              width: warningImageW,
              height: warningImageW,
            ),
          ));
    }
  }

  ///定时刷新实时时间
  updateAllRefreshDates() {
    ///用户没有开通vip不用刷新
    // if (CanvasUserCenter().vipType != VIPType.valid) {
    //   return;
    // }
    List<CanvasElement> dateList = [];
    _templateData?.canvasElements.forEach((element) {
      if (element.data is DateElement) {
        DateElement dateElement = element.data as DateElement;
        if (dateElement.isRefreshDateEnable()) {
          dateElement.time = DateTime.now().millisecondsSinceEpoch;

          ///为了节省性能，只刷新带有时间格式的
          if ((dateElement.timeFormat?.isNotEmpty ?? false)) {
            dateList.add(element);
          }
        }
      }
    });
    if (dateList.isNotEmpty) {
      // Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements(dateList);
      dateList.forEach((element) {
        element.resetImageCache();
      });
      Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements([]);
    }
  }

  getElementImage(CanvasElement canvasElement) {
    int start1 = DateTime.now().millisecondsSinceEpoch;
    String printColor = TinyKitCanvasObjectSharedWidget.printColorOf(context);
    Color? contentColor = Colors.black;
    if (printColor.isNotEmpty) {
      contentColor = Color.fromARGB(255, int.parse(printColor.split(".")[0]), int.parse(printColor.split(".")[1]),
          int.parse(printColor.split(".")[2]));
    } else {
      contentColor = null;
    }
    JsonElement item = canvasElement.data;
    if (item.width <= 0 || item.height <= 0) {
      return null;
    }
    int paperColorIndex = item.paperColorIndex ?? 0;
    if (paperColorIndex >= (_templateData?.paperColor ?? []).length) paperColorIndex = 0;
    item.elementColor ??=
        ("255." + (_templateData?.paperColor[paperColorIndex] ?? "")).split(".").map((e) => int.parse(e)).toList();
    _logger.log("元素颜色:${canvasElement.data.elementColor}");
    if (item is BarCodeElement && item.codeType == BarcodeType.CODE_BAR) {
      if ((item.value?.length ?? 0) > 57) {
        item.value = item.value?.substring(0, 57);
      }
    }

    NetalImageResult imageData;
    if (canvasElement.imageCache == null) {
      /// 图像库预览时旋转由于应用层处理，将 rotate 处理成 0 给图像库
      int originRotate = item.rotate;
      item.rotate = 0;
      _templateData?.usedFonts = TinyKitCanvasObjectSharedWidget.canvasDataOf(context)?.usedFonts;
      var canvasData = TemplateData(
          width: _templateData?.width ?? 0,
          height: _templateData?.height ?? 0,
          canvasElements: [canvasElement],
          usedFonts: _templateData?.usedFonts ?? {});
      var elementCanvasDataJsonStr = jsonEncode(canvasData.toJson(
          escapeValue: true,
          savePrintTemplate: true,
          escapeBindingMethod: (JsonElement jsonElement) {
            return _templateData?.escapeBindingValue(jsonElement, 1, parseContentAffix: true);
          }));
      item.rotate = originRotate;

      String printColor = TinyKitCanvasObjectSharedWidget.printColorOf(context);
      int start = DateTime.now().millisecondsSinceEpoch;
      _logger.log("==========getElementImage，运算时长： ${start - start1}ms");
      _logger.log("==========getElementImage elementCanvasDataJsonStr = ${elementCanvasDataJsonStr}");
      _logger.log("==========getElementImage contentColor = ${contentColor}");

      double pxRatio;
      pxRatio = DisplayUtil.pxRatio;
      imageData = NiimbotNetal.generateImageFromElementJson(
          jsonString: elementCanvasDataJsonStr, ratio: pxRatio, color: contentColor);
      canvasElement.imageCache = imageData;
      _logger.log(
          "==========图像库，渲染时长： ${DateTime.now().millisecondsSinceEpoch - start}ms  , imageData = ${imageData.size}");
    } else {
      imageData = canvasElement.imageCache!;
    } //
    return imageData;
  }

  Widget itemControlsBuilder(item) {
    return ValueListenableBuilder(
        valueListenable: isTranslatingNotifier,
        child: Container(
          width: 300,
          height: 200,
          color: Colors.blue,
        ),
        builder: (_, bool isTranslating, cachedChild) {
          if (isTranslating) return Container();
          return cachedChild!;
        });
  }

  Rect? multiElementsRect() {
    if (_focusedElements.length > 1) {
      List<CanvasElement> multiElements = []..addAll(_focusedElements);

      /// 查找选中元素的镜像元素
      List<String> ids = _focusedElements.map((e) => e.data.id).toList();
      multiElements.addAll((_canvasElements ?? []).where((element) => ids.contains(element.data.mirrorId)));

      Rect? rect;
      multiElements.forEach((element) {
        Rect elementRect = element.rect;
        if (rect == null) {
          rect = elementRect;
        } else {
          rect = rect?.expandToInclude(elementRect);
        }
      });
      return rect;
    }
    return null;
  }

  bool isMirror(element) {
    return element.data.isOpenMirror == 0 && (element.data.mirrorId ?? '').length > 0;
  }

  bool isAssociateElement(element) {
    JsonElement currentElement = (element as CanvasElement).data;
    bool isAssociate = (currentElement is DateElement &&
        !currentElement.associated &&
        currentElement.associateId != null &&
        currentElement.associateId.isNotEmpty);
    return isAssociate;
  }

  CanvasElement? getAssociateElement(CanvasElement canvasElement) {
    CanvasElement? associateElement = null;
    if (canvasElement.data is! DateElement) return associateElement;
    DateElement dateElement = canvasElement.data as DateElement;
    if (dateElement.associateId.isEmpty) return associateElement;
    List<CanvasElement> associateElements = [];
    for (var element in _canvasElements!) {
      if (element.data is DateElement) {
        DateElement dateElement1 = element.data as DateElement;
        if (dateElement1.associateId == dateElement.associateId) {
          associateElements.add(element);
        }
      }
    }
    if (associateElements.length == 2) {
      //未开启镜像
      associateElements.forEach((element) {
        DateElement dateElement1 = element.data as DateElement;
        if (dateElement1.id != dateElement.id) {
          associateElement = element;
        }
      });
    } else if (associateElements.length == 3) {
      //未开启镜像
      associateElements.forEach((element) {
        DateElement dateElement1 = element.data as DateElement;
        if (dateElement1.id != dateElement.id && dateElement.mirrorId != dateElement1.id) {
          associateElement = element;
        }
      });
    } else if (associateElements.length == 4) {
      //开启镜像
      associateElements.forEach((element) {
        DateElement dateElement1 = element.data as DateElement;
        if (dateElement1.isOpenMirror == 1 && dateElement1.id != dateElement.id) {
          associateElement = element;
        }
      });
    } else if (associateElements.length == 1) {
      //开启镜像
      DateElement dateElement1 = associateElements.first.data as DateElement;
      if (dateElement1.id != dateElement.id) associateElement = associateElements.first;
    }
    return associateElement;
  }

  refreshAssociateElement(CanvasElement canvasElement) {
    // return;
    _logger.log(
        "===============原始元素==> x:${canvasElement.data.x} y:${canvasElement.data.y} width:${canvasElement.data.width} height:${canvasElement.data.height}");
    if (canvasElement.data is DateElement) {
      DateElement dateElement = canvasElement.data as DateElement;
      CanvasElement? associateCanvasElement = getAssociateElement(canvasElement);
      if (associateCanvasElement != null) {
        DateElement associateElement = associateCanvasElement.data as DateElement;
        _canvasElements?.forEach((element) {
          num x = associateElement.x;
          num y = associateElement.y;
          bool associated = associateElement.associated;
          String id = associateElement.id;
          int hashCode = associateElement.hashCode;
          String? contentTitle = associateElement.contentTitle;
          int isLock = associateElement.isLock;
          if (element.elementId == associateElement.id) {
            DateElement refreshElement = dateElement.clone() as DateElement;
            refreshElement.x = associateElement.x;
            refreshElement.y = associateElement.y;
            refreshElement.associated = associateElement.associated;
            refreshElement.id = associateElement.id;
            refreshElement.validityPeriodUnit = associateElement.validityPeriodUnit;
            refreshElement.validityPeriod = associateElement.validityPeriod;
            refreshElement.validityPeriodNew = associateElement.validityPeriodNew;
            refreshElement.isOpenMirror = associateElement.isOpenMirror;
            refreshElement.mirrorId = associateElement.mirrorId;
            refreshElement.contentTitle = associateElement.contentTitle;
            refreshElement.isLock = associateElement.isLock;
            refreshElement.lastHeight = associateElement.lastHeight;
            refreshElement.lastWidth = associateElement.lastWidth;
            refreshElement.tagForRelocation = associateElement.tagForRelocation;
            element.data = refreshElement;
            element.resetImageCache();
          }
        });
      }
    }
  }

  bool isResizeEnable(element) {
    if (element.data is TextElement && (element.data as TextElement).typesettingMode == 3) return false;
    return true;
  }

  bool isLock(element) {
    /// 当前元素被锁定 or 当前为镜像但主体被锁定
    return element.data.isLock == 1 ||
        (_canvasElements ?? []).where((e) => e.data.mirrorId == element.data.id && e.data.isLock == 1).length > 0;
  }

  bool isItemFocused(element) {
    bool isFocused = _focusedElements.contains(element) ||
        _focusedElements.where((focusedElement) => focusedElement.data.id == element.data.id).length > 0;
    return isFocused;
  }

  ///刷新本地画板和图形库渲染json
  void _handleLegoElementsRefresh(List<CanvasElement>? dataChangedCanvasElements) {
    if ((dataChangedCanvasElements ?? []).length == 0) {
      return;
    }
    _logger.log("=====================_handleLegoElementsRefresh刷新lego元素");
    for (var changedElement in dataChangedCanvasElements!) {
      JsonElement jsonElement = changedElement.data;
      _templateData?.canvasElements.forEach((element) {
        if (element.data is TextElement && jsonElement is TextElement && jsonElement.id == element.data.id) {
          TextElement textElement = element.data as TextElement;

          debugPrint(
              "_templateData ========> isEditing = ${jsonElement.isEditing} ,isEdited = ${textElement.isEdited}");
          if (jsonElement.isEditing) {
            textElement.value = jsonElement.value;
            textElement.textAlignHorizontal = jsonElement.textAlignHorizontal;
            textElement.fontStyle = jsonElement.fontStyle;
            textElement.isEditing = jsonElement.isEditing;
            textElement.isEdited = true;
          } else {
            textElement.textAlignHorizontal = jsonElement.textAlignHorizontal;
            textElement.fontStyle = jsonElement.fontStyle;
            textElement.isEdited = false;
          }
          debugPrint(
              "_templateData2222 ========> isEditing = ${jsonElement.isEditing} ,isEdited = ${textElement.isEdited}");
        }
      });
    }

    _focusedElements.forEach((element) {
      _logger.log("=====================_handleLegoElementsRefresh _focusedElements = ${element.data.toJson()}");
    });

    ///版式数据更新

    double canvasWidth = widget.frameInfo.canvasWidth;
    double canvasHeight = widget.frameInfo.canvasHeight;

    String? trackerJson = _templateData?.generateCanvasJson(savePrintTemplate: true);
    String canvasJson = TinyKitLegoUtils.instance.parseCanvasJson(trackerJson!, canvasWidth, canvasHeight);
    _setTemplateData(canvasJson);
  }

  void _handleMirrorElementsRefresh(List<CanvasElement>? dataChangedCanvasElements) {
    if ((dataChangedCanvasElements ?? []).length == 0) {
      return;
    }

    _logger.log("=====================_handleMirrorElementsRefresh刷新镜像元素");
    for (var changedElement in dataChangedCanvasElements!) {
      /// 检测镜像是否需要刷新
      changedElement.data.isOpenMirror = 1;
      if (changedElement.mirrorNeedRefresh == true || changedElement.data.isOpenMirror == 1) {
        JsonElement jsonElement = changedElement.data;

        /// 移除旧的镜像体
        _canvasElements?.removeWhere((element) {
          // String mirrorId = jsonElement.generateFixedMirrorId();
          bool needRemove = element.data.mirrorId == jsonElement.id;
          if (!needRemove &&
              jsonElement is DateElement &&
              jsonElement.associateId.isNotEmpty &&
              jsonElement.id != element.elementId &&
              element.data is DateElement) {
            CanvasElement? canvasElement = getAssociateElement(changedElement);
            DateElement dateElement = element.data as DateElement;
            if (canvasElement != null) {
              DateElement associateElement = canvasElement.data as DateElement;
              needRemove = (associateElement.mirrorId != null && associateElement.mirrorId == dateElement.id);
            }
          }
          return needRemove;
        });
      }
    }
    dataChangedCanvasElements.removeWhere((element) {
      if (element.data.type == "date") {
        ///操作完整关联元素 移除第二元素
        DateElement associateElement = element.data as DateElement;
        return (!associateElement.associated &&
            associateElement.associateId.isNotEmpty &&
            _containsAssociateElement(dataChangedCanvasElements, element));
      } else {
        return false;
      }
    });
    for (var changedElement in dataChangedCanvasElements) {
      /// 检测镜像是否需要刷新
      if (changedElement.mirrorNeedRefresh == true || changedElement.data.isOpenMirror == 1) {
        JsonElement jsonElement = changedElement.data;

        /// 创建新的镜像体
        if (jsonElement.isOpenMirror == 1) {
          JsonElement mirrorElement = createMirrorElement(context, jsonElement);
          _canvasElements?.add(mirrorElement.toCanvasElement());
          if (jsonElement is DateElement && jsonElement.associateId.isNotEmpty) {
            CanvasElement? canvasElement = getAssociateElement(changedElement);
            if (canvasElement != null) {
              DateElement associateElement = canvasElement.data as DateElement;
              associateElement.isOpenMirror = 1;
              JsonElement mirrorElement = createMirrorElement(context, associateElement);
              _canvasElements?.add(mirrorElement.toCanvasElement());
            }
          }
        } else if (jsonElement is DateElement && jsonElement.associateId.isNotEmpty) {
          /// 遍历删除关联元素的镜像体
          _canvasElements?.forEach((element) {
            if (element.data is DateElement) {
              DateElement dateElement = element.data as DateElement;
              if (dateElement.associateId == jsonElement.associateId) {
                dateElement.isOpenMirror = 0;
                dateElement.mirrorId = "";
              }
            }
          });
        }
      }
    }
    // dataChangedCanvasElements.forEach((element) {});
  }

  ///检查是否包含完整关联元素
  bool _containsAssociateElement(List<CanvasElement> canvasElements, CanvasElement currentCanvas) {
    bool isContains = false;
    if (currentCanvas.data is! DateElement || canvasElements.length < 2) return isContains;
    DateElement dateElement = currentCanvas.data as DateElement;
    if (dateElement.associateId.isEmpty) return isContains;
    canvasElements.removeWhere((element) =>
        (element.data.isOpenMirror == 0 && element.data.mirrorId != null && element.data.mirrorId.isNotEmpty));
    List<CanvasElement> elements = List<CanvasElement>.from(canvasElements.where((element) {
      if (element.data.type != "date") {
        return false;
      } else {
        DateElement dateElement2 = element.data as DateElement;
        if (dateElement2.associateId == dateElement.associateId) {
          return true;
        } else {
          return false;
        }
      }
    }));
    if (elements != null && elements.length >= 2) {
      isContains = true;
    }
    return isContains;
  }

  JsonElement createMirrorElement(BuildContext context, JsonElement jsonElement) {
    double templateWidth = TinyKitCanvasObjectSharedWidget.canvasDataOf(context)?.width?.toDouble() ?? 0;
    double templateHeight = TinyKitCanvasObjectSharedWidget.canvasDataOf(context)?.height?.toDouble() ?? 0;

    /// 画板中心点
    Offset templateCenter = Offset(templateWidth / 2.0, templateHeight / 2.0);

    JsonElement mirrorElement = jsonElement.clone();
    mirrorElement.id = jsonElement.generateFixedMirrorId();
    jsonElement.mirrorId = mirrorElement.id;
    mirrorElement.mirrorId = jsonElement.id;
    mirrorElement.isOpenMirror = 0;
    if (jsonElement.isBindingExcel()) {
      // _templateData.cloneElementSyncExcelTask(jsonElement.id, mirrorElement.id);
    }
    if ((jsonElement.mirrorType ?? 0) > 0) {
      /// x、y 轴镜像方式下，内容不做旋转
      mirrorElement.rotate = mirrorElement.rotate;
    } else {
      mirrorElement.rotate = (jsonElement.rotate + 180) % 360;
    }

    Offset distanceToCenter = templateCenter -
        Offset(jsonElement.x.toDouble() + jsonElement.width, jsonElement.y.toDouble() + jsonElement.height);

    Offset mirrorPosition = templateCenter + distanceToCenter;
    if (jsonElement.mirrorType == 1) {
      /// 1: 画板中心 y 轴镜像
      mirrorElement.y = mirrorPosition.dy;
    } else if (jsonElement.mirrorType == 2) {
      /// 2: 画板中心 x 轴镜像
      mirrorElement.x = mirrorPosition.dx;
    } else {
      /// 0: 画板中心点镜像
      mirrorElement.x = mirrorPosition.dx;
      mirrorElement.y = mirrorPosition.dy;
    }

    return mirrorElement;
  }

  var printMargins = [0, 0, 0, 0];
  var printOffsets = [0, 0, 0, 0];

  void _handleBoxTranslated(CanvasElement? canvasElement, DragUpdate dragUpdate, bool ignoreElement) {
    _logger.log("dragUpdate==${dragUpdate.toString()}");

    if (!ignoreElement && null == canvasElement) {
      _logger.log("Error: canvasElement不可为空");
      return;
    }

    /// 待处理的元素集合, 主要处理多选模式下, 其他选中元素的属性及镜像更新
    List<CanvasElement> pendingCanvasElements = [];

    /// 单选拖动时, 不显示工具条
    if (!_multiSelect) {}

    if (dragUpdate.dragType == DragType.size || dragUpdate.dragType == DragType.horizontalSize) {
      if (canvasElement!.data is TextElement && (canvasElement.data as TextElement).isSupportBoxStyle()) {
        if (dragUpdate.dragType == DragType.size) {
          (canvasElement.data as TextElement).handleTextElementDiagonal();
        } else if (dragUpdate.dragType == DragType.horizontalSize) {
          (canvasElement.data as TextElement).handleTextElementHorizontal();
        }

        /// 处理镜像数据
        if ((canvasElement.data.mirrorId ?? '').length > 0) {
          CanvasElement? otherCanvasElement =
              _canvasElements?.singleWhereOrNull((e) => e.data.id == canvasElement.data.mirrorId);
          if (dragUpdate.dragType == DragType.size) {
            (otherCanvasElement?.data as TextElement).handleTextElementDiagonal();
          } else if (dragUpdate.dragType == DragType.horizontalSize) {
            (otherCanvasElement?.data as TextElement).handleTextElementHorizontal();
          }

          ///处理关联元素及镜像
          CanvasElement? associateCanvasElement = getAssociateElement(canvasElement);
          if (associateCanvasElement != null) {
            CanvasElement? otherCanvasElement1 =
                _canvasElements?.singleWhere((e) => e.data.id == associateCanvasElement.data.mirrorId);
            if (dragUpdate.dragType == DragType.size) {
              (otherCanvasElement1?.data as TextElement).handleTextElementDiagonal();
            } else if (dragUpdate.dragType == DragType.horizontalSize) {
              (otherCanvasElement1?.data as TextElement).handleTextElementHorizontal();
            }
          }
        }
      }

      /// 校正旋转状态下的组件拉伸偏移
      Offset difference = dragUpdate.size - canvasElement.size as Offset;
      Offset center = canvasElement.rect.center;
      canvasElement.size = dragUpdate.size;
      if (canvasElement.elementType == ElementItemType.table) {
        //拉伸table 行高列宽及时变化
        widget.onAttrPanelChange.call();
      }
      if (canvasElement.rotate == 90) {
        /// 向左下延伸
        Offset offsetCenter = center + Offset(-difference.dy, difference.dx) / 2;
        canvasElement.offset = offsetCenter - Offset(canvasElement.size.width, canvasElement.size.height) / 2;
      } else if (canvasElement.rotate == 180) {
        /// 向左上
        canvasElement.offset = canvasElement.offset - difference;
      } else if (canvasElement.rotate == 270) {
        /// 向右上延伸
        Offset offsetCenter = center + Offset(difference.dy, -difference.dx) / 2;
        canvasElement.offset = offsetCenter - Offset(canvasElement.size.width, canvasElement.size.height) / 2;
      } else {
        /// 向右下延伸
        canvasElement.offset = dragUpdate.position;
      }
      pendingCanvasElements.add(canvasElement);
      CanvasElement? associateElement = getAssociateElement(canvasElement);
      if (associateElement != null) {
        Offset difference = (dragUpdate.size - associateElement.size) as Offset;
        Offset center = associateElement.rect.center;
        associateElement.size = dragUpdate.size;
        if (associateElement.elementType == ElementItemType.table) {
          //拉伸table 行高列宽及时变化
          widget.onAttrPanelChange?.call();
        }
        if (associateElement.rotate == 90) {
          /// 向左下延伸
          Offset offsetCenter = center + Offset(-difference.dy, difference.dx) / 2;
          associateElement.offset =
              offsetCenter - Offset(associateElement.size.width, associateElement.size.height) / 2;
        } else if (associateElement.rotate == 180) {
          /// 向左上
          associateElement.offset = associateElement.offset - difference;
        } else if (associateElement.rotate == 270) {
          /// 向右上延伸
          Offset offsetCenter = center + Offset(difference.dy, -difference.dx) / 2;
          associateElement.offset =
              offsetCenter - Offset(associateElement.size.width, associateElement.size.height) / 2;
        } else {
          /// 向右下延伸
          // refreshAssociateElement(canvasElement);
          // associateElement.offset = dragUpdate.position;
        }
        refreshAssociateElement(canvasElement);
        pendingCanvasElements.add(associateElement);
      }
    } else {
      /// 位移差值
      Offset offset = ignoreElement ? dragUpdate.position : dragUpdate.position - canvasElement!.offset;

      /// 多选状态下, 所有选中元素跟随位移
      if (_multiSelect) {
        /// 如果多选状态下移动的是镜像元素，位移取反
        if (canvasElement?.data.isOpenMirror == 0 && (canvasElement?.data.mirrorId ?? '').length > 0) {
          offset = -offset;
        }
        _focusedElements.forEach((e) {
          /// 排除当前元素和镜像元素
          if (canvasElement != null &&
              e.elementId != canvasElement.elementId &&
              e.data.mirrorId != canvasElement.elementId) {
            /// 多选状态下移动的是镜像元素，位移取反
            if (e.data.isOpenMirror == 0 && (e.data.mirrorId ?? '').length > 0) {
              e.offset = e.offset - offset;
            } else {
              e.offset = e.offset + offset;
            }
            pendingCanvasElements.add(e);
          }
        });
      }

      if (!ignoreElement) {
        /// 更新当前元素
        canvasElement!.offset = dragUpdate.position;
        pendingCanvasElements.add(canvasElement);

        /// 未选中, 则自动选中
        CanvasElement? entityElement = canvasElement;
        if (canvasElement.data.isOpenMirror == 0 && (canvasElement.data.mirrorId ?? '').length > 0) {
          entityElement =
              _canvasElements?.singleWhereOrNull((element) => element.data.id == canvasElement.data.mirrorId);
        }
        if (!_focusedElements.contains(entityElement)) {
          if (!_multiSelect) {
            _focusedElements.clear();
          }
          if (entityElement != null) {
            _focusedElements.add(entityElement);
            _logger.log("多选元素，滑动选择更新悬浮条数量：${_focusedElements.length}");
            // FloatingBarHelper().selectSizeChanged(_focusedElements, true);
          }
        }
      }
    }

    double templateWidth = TinyKitCanvasObjectSharedWidget.canvasDataOf(context)?.width?.toDouble() ?? 0;
    double templateHeight = TinyKitCanvasObjectSharedWidget.canvasDataOf(context)?.height?.toDouble() ?? 0;

    /// 画板中心点
    Offset templateCenter = Offset(templateWidth / 2.0, templateHeight / 2.0);
    for (CanvasElement element in pendingCanvasElements) {
      // /// 自由旋转角度, 暂未使用
      // element.rotate = dragUpdate.rotate.toInt();

      /// 处理镜像数据
      if ((element.data.mirrorId ?? '').length > 0) {
        CanvasElement? otherCanvasElement =
            (_canvasElements ?? []).singleWhereOrNull((e) => e.data.id == element.data.mirrorId);

        /// 清理预览图片缓存
        if (dragUpdate.dragType == DragType.size || dragUpdate.dragType == DragType.horizontalSize) {
          otherCanvasElement?.resetImageCache();
        }

        Offset mirrorPosition;

        if (element.data.isOpenMirror == 1) {
          /// 当前移动组件为主体
          Offset distanceToCenter = templateCenter -
              Offset(element.data.x.toDouble() + element.data.width, element.data.y.toDouble() + element.data.height);
          mirrorPosition = templateCenter + distanceToCenter;
        } else {
          /// 当前移动组件为镜像体
          Offset distanceToCenter =
              Offset(element.data.x.toDouble() + element.data.width, element.data.y.toDouble() + element.data.height) -
                  templateCenter;
          mirrorPosition = templateCenter - distanceToCenter;
        }

        if (element.data.mirrorType == 1) {
          /// 1: 画板中心 y 轴镜像
          otherCanvasElement!.data.x = element.data.x;
          otherCanvasElement.data.y = mirrorPosition.dy;
        } else if (element.data.mirrorType == 2) {
          /// 2: 画板中心 x 轴镜像
          otherCanvasElement!.data.x = mirrorPosition.dx;
          otherCanvasElement.data.y = element.data.y;
        } else {
          /// 0: 画板中心点镜像
          otherCanvasElement!.data.x = mirrorPosition.dx;
          otherCanvasElement.data.y = mirrorPosition.dy;
        }

        otherCanvasElement.data.width = element.data.width;
        otherCanvasElement.data.height = element.data.height;
      }
    }

    setState(() {
      if (!ignoreElement) {
        canvasElement!.isDragging = true;
      }
    });
  }

  void _handleBoxTranslateEnded(CanvasElement? canvasElement, bool ignoreElement) async {
    if (!ignoreElement && null == canvasElement) {
      _logger.log("Error: canvasElement不可为空");
      return;
    }

    /// 拖动结束
    if (canvasElement != null) canvasElement.isDragging = false;

    /// 待处理的元素集合, 主要处理多选模式下, 其他选中元素的属性及镜像更新
    List<CanvasElement> pendingCanvasElements = ignoreElement ? [] : [canvasElement!];

    /// 多选状态下, 所有选中元素跟随位移
    if (_multiSelect) {
      _focusedElements.forEach((e) {
        /// 排除当前元素
        if (canvasElement != null && e.elementId != canvasElement.elementId) {
          pendingCanvasElements.add(e);
        } else {
          pendingCanvasElements.add(e);
        }
      });
    } else {
      /// 如果当前拖动为锁定元素，切换选中状态
      if (canvasElement!.data.isLock == 1) {
        _focusedElements.clear();
        _focusedElements.add(canvasElement);
      }
    }

    /// 镜像数据替换为主体进行镜像缓存
    for (int index = 0; index < pendingCanvasElements.length; index++) {
      CanvasElement pendingCanvasElement = pendingCanvasElements[index];
      if ((pendingCanvasElement.data.mirrorId ?? '').length > 0 && pendingCanvasElement.data.isOpenMirror != 1) {
        /// 当前移动组件为镜像体
        CanvasElement? otherCanvasElement =
            _canvasElements?.singleWhereOrNull((element) => element.data.id == pendingCanvasElement.data.mirrorId);
        pendingCanvasElement = otherCanvasElement!;
        pendingCanvasElements[index] = pendingCanvasElement;
      }
    }

    // setState(() {});
    widget.onElementFocusChanged?.call(_focusedElements, 1);

    /// 注销松开手指弹出工具条的逻辑
    // if (!_multiSelect) {
    //   // floatingBarVisible = true;
    //   FloatingBarVisibleNotifier().forceUpdate();
    // }

    if (canvasElement == null || canvasElement.data.isLock != 1) {
      /// 撤销、恢复
      List<CanvasElement> needAddAssociateElements = [];
      pendingCanvasElements.forEach(
        (element) {
          CanvasElement? needAddElement = getAssociateElement(element);
          if (needAddElement != null) needAddAssociateElements.add(needAddElement);
        },
      );
      if (needAddAssociateElements.isNotEmpty) pendingCanvasElements.addAll(needAddAssociateElements);
    }
  }

  void _handleBoxEdit(List<CanvasElement> canvasElements) {
    // if (canvasElements.first.elementType == ElementItemType.table) {
    //   TableElement tableElement = canvasElements.first.data as TableElement;
    //   List<TableCellElement> focusCells = tableElement.getFocusedCells();
    //   if (focusCells.length == 1) {
    //     widget.onElementValueEditorDisplay.call(
    //         focusCells.first.toCanvasElement(), canvasElements.first,
    //         isDoubleClick: true);
    //     FloatingBarHelper().dismissFloatingBar();
    //   }
    // } else {
    widget.onElementValueEditorDisplay.call(canvasElements.first, canvasElements.first, isDoubleClick: true);
    // FloatingBarHelper().dismissFloatingBar();
    // }
  }

  void _handleBoxDeleted(List<CanvasElement> canvasElements) {}

  void selectDefaultElementWithType(String defaultElementWithType) {
    CanvasElement? needSelectElement;
    for (var element in _canvasElements!) {
      if (element.data.type == defaultElementWithType) {
        needSelectElement = element;
        break;
      }
    }
    _handleSelectionChanged(
      needSelectElement,
      voidCallback: () {
        //FloatingBarHelper().dismissFloatingBar();
      },
    );
  }

  void _handleSelectionChanged(CanvasElement? canvasElement,
      {bool fromDoubleTap = false, bool needDelay = true, VoidCallback? voidCallback}) {
    _logger.log("_handleSelectionChanged============================>");
    final setStateBlock = () {
      setState(() {
        var entityElement = canvasElement;

        if (canvasElement == null) {
          /// 点击背景
          if (_multiSelect) {
            this.multiSelect = false;
          }
          _focusedElements.clear();
        } else {
          /// 选中镜像则代表选中实体
          if (canvasElement.data.isOpenMirror == 0 && (canvasElement.data.mirrorId ?? '').length > 0) {
            entityElement =
                _canvasElements?.singleWhereOrNull((element) => element.data.id == canvasElement.data.mirrorId);
          }
          // if (canvasElement.data is DateElement) {
          //   DateElement dateElement = canvasElement.data;
          //   if (!dateElement.associated && (dateElement.associateId ?? '').length > 0) {
          //     entityElement = _canvasElements.singleWhere((element) {
          //       if (element.data is DateElement) {
          //         DateElement dateElement1 = element.data;
          //         return dateElement1.associated && dateElement1.associateId == dateElement.associateId;
          //       } else {
          //         return false;
          //       }
          //     }, orElse: () => null);
          //   }
          // }

          /// 多选状态不可选中锁定元素
          if (_multiSelect) {
            if (isLock(canvasElement)) {
              return;
            }
          } else {
            if (_focusedElements.contains(entityElement)) {
              ///2023/12/20 Ice_Liu 选中状态下的元素，再次点击触发输入
              widget.onElementValueEditorDisplay.call(entityElement!, entityElement,
                  isDoubleClick: _focusedElements.first.data.isBindingCommodity() ? true : false);
              return;
            } else {
              _focusedElements.clear();
            }
          }

          if (entityElement != null) {
            if (_focusedElements.contains(entityElement)) {
              _focusedElements.remove(entityElement);
            } else {
              _focusedElements.add(entityElement);

              // /// 智能提示
              // Future.delayed(Duration(milliseconds: 500), () {
              //   SmartTipsNotifier().handleElementOnTap(entityElement.data);
              // });
            }
          }

          if (!_multiSelect && !fromDoubleTap) {
            // if (canvasElement.data.type == ElementItemType.text && fromDoubleTap) {
            //   // FloatingBarVisibleNotifier().forceUpdate();
            // } else {
            //   FloatingBarVisibleNotifier().forceUpdate();
            // }
            voidCallback?.call();
            return;
          } else {
            ///判断选中是否为空
            if (_focusedElements.isEmpty) {
              multiSelect = false;
            } else {
              _logger.log("多选元素，更新悬浮条数量：${_focusedElements.length}");
              // FloatingBarHelper().selectSizeChanged(_focusedElements, true);
            }
          }
        }
      });
      if (canvasElement != null && isLock(canvasElement)) {
        widget.onElementFocusChanged?.call(_focusedElements, _multiSelect ? 2 : 0);
        // FloatingBarVisibleNotifier().forceUpdate();
        return;
      }
      if (canvasElement == null) {
        /// 点击背景
        widget.onElementFocusChanged?.call(_focusedElements, _multiSelect ? 2 : -1);
      } else {
        /// 判断是否绑定元素是为了实现选中状态下再次点击，进入输入状态
        widget.onElementFocusChanged?.call(
            _focusedElements,
            _focusedElements.isEmpty
                ? 0
                : _multiSelect
                    ? 2
                    : _focusedElements.first.data.isBindingElement()
                        ? 1
                        : 0);
      }
    };
    if (needDelay) {
      Future.delayed(Duration.zero, () {
        setStateBlock();
      });
    } else {
      setStateBlock();
    }
    if (canvasElement != null) {
      _handleBoxDoubleTap(canvasElement!);
    }
  }

  //是否显示右下角拖拽的小圆点 多选状态下 当选中的元素大于等于2的时候隐藏调节杆
  bool _isShowRightCorner() {
    bool isShow = true;
    if (_multiSelect == true) {
      //isShow = !((FloatingBarHelper().selectedCanvasElements?.length ?? 0) > 1);
    }
    return isShow;
  }

  void _handleBoxDoubleTap(CanvasElement canvasElement) {
    _logger.log("_handleBoxDoubleTap=========> ${canvasElement.data.toJson()}");
    Future.delayed(Duration.zero, () {
      this.multiSelect = false;
      if (canvasElement.data.isLock != 1) {
        // FloatingBarHelper().dismissFloatingBar();
      }

      CanvasElement? hostElement = null;
      if (canvasElement.data.isOpenMirror == 0 && (canvasElement.data.mirrorId ?? '').length > 0) {
        hostElement =
            _canvasElements?.singleWhere((element) => element.data.id == canvasElement.data.mirrorId, orElse: null);
        if (hostElement?.data.isLock == 1) {
          //FloatingBarHelper().dismissFloatingBar();
          return;
        }
      }

      /// 锁定元素不响应双击
      if (canvasElement.data.isLock == 1) {
        // FloatingBarHelper().dismissFloatingBar();
        return;
      }

      if ((hostElement == null && !_focusedElements.contains(canvasElement)) ||
          (hostElement != null && !_focusedElements.contains(hostElement))) {
        _handleSelectionChanged(canvasElement, fromDoubleTap: true, needDelay: false);
      }

      // if (canvasElement.data.type == ElementItemType.table) {
      //   /// 表格元素双击即选中
      //   _handleSelectionChanged(canvasElement);
      // } else
      if (canvasElement.data.canDoubleClickToEditValue()) {
        CanvasElement focusElement = canvasElement;
        if (canvasElement.data.mirrorId.isNotEmpty && canvasElement.data.isOpenMirror == 0) {
          String mirrorId = canvasElement.data.mirrorId;
          for (var element in (_canvasElements ?? [])) {
            if (element.data.id == mirrorId) {
              focusElement = element;
            }
          }
        }
        widget.onElementValueEditorDisplay.call(focusElement, focusElement, isDoubleClick: true);
      }
    });
  }

  void _handleMultiSelectChanged(bool value) {
    // setState(() {
    //   if (value == false) {
    //     _focusedElements.clear();
    //   }
    //   this.multiSelect = value;
    // });
  }

  /// 选中所有元素
  void _handleSelectAll() {
    // _handleMultiSelectSubAction((element) => true);
  }

  /// 选中所有文本类元素（文本、时间和序列号）
  void _handleSelectAllText() {
    //_handleMultiSelectSubAction((element) => element.isTextElement());
  }

  /// 选中所有线条
  void _handleSelectAllLine() {
    // _handleMultiSelectSubAction(
    //     (element) => element.type == ElementItemType.line);
  }

  void _handleBoxCopied(List<CanvasElement> canvasElements) {}

  void _handleBoxRotate(List<CanvasElement> canvasElements) {
    canvasElements.forEach((element) {
      // element.data.rotate += 90;
      // element.data.rotate %= 360;
      element.data.rotate += 180;
      element.data.rotate %= 360;
    });

    Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements(canvasElements);

    widget.onAttrPanelChange.call();
  }

  void _handleBoxLock(List<CanvasElement> canvasElements) {}

  void _handleBoxAlign(int action, List<CanvasElement> canvasElements) {}

  /// 处理表格cell的悬浮条操作
  ///
  /// action说明：
  ///       0：删除选中行
  ///       1：选中行下方插入行
  ///       2：删除选中列
  ///       3：选中列右侧插入列
  ///       4：清空已选中cell的内容
  void _handleCellAction(int action, List<CanvasElement> canvasElements) {}

  void _handleMergeCells(List<CanvasElement> canvasElements) {}

  void _handleSplitCells(List<CanvasElement> canvasElements) {}

  void _onInteractionUpdate(Offset offset, double scale) {
    // _logger.log(
    //     'Interaction Update - Focal point: $offset , Scale: $scale , initRulerOffset = $initRulerOffset');
    // _horizontalRulerKey.currentState
    //     .jump((_initRulerOffset - offset) * scale, scale);
    // _verticalRulerKey.currentState.jump(offset * scale, scale);
    // if (!_multiSelect) {
    //   floatingBarVisible = false;
    // }
    if (!_multiSelect) {
      // FloatingBarHelper().dismissFloatingBar();
    }
  }

  void clearCanvas() {
    this.multiSelect = false;
    _canvasElements?.clear();
    _focusedElements.clear();

    widget.onElementFocusChanged?.call(_focusedElements, 0);
  }

  ///调起修改背景图片流程
  _onTemplateBackgroundChangeTap(int index) {
    _logger.log("call 多背景修改--->index：$index");
    if (_templateData?.multipleBackIndex != index) {
      setState(() {
        _templateData?.multipleBackIndex = index;
      });
    }
  }

  /// 业务新建元素入口, 即业务自定义创建按钮进行的创建
  /// isSelectedFirst代表是否默认选中第一个元素, 默认选中最后一个
  void addElementFromBusiness(List<ElementCreateRequiredModel> elementCreateList,
      {bool ocrClick = false, bool isSelectedFirst = false}) {
    List<CanvasElement> canvasElements = [];
    double offsetY = 5.0;
    elementCreateList.forEach((e) {
      Rect? location = e.location;
      if ((location ?? Rect.zero) == Rect.zero) {
        location = Rect.fromLTWH(5.0, offsetY, 30.0, 10.0);
      }
      double x = location!.left;
      double y = location.top;
      if (!ocrClick) {
        Point point = _getAddElementPosition();
        x = point.x.toDouble();
        y = point.y.toDouble();
      }
      if (e.bindElementType == ElementItemType.text) {
        canvasElements.add(TextElement(
          id: JsonElement.generateId(),
          x: x,
          y: y,
          width: location.width,
          height: location.height,
          value: e.value ?? '',
          fontSize: 3.2,
          rotate: 0,
          fontStyle: [],
          textAlignHorizontal: Directionality.of(context) == TextDirection.rtl ? 2 : 0,
          letterSpacing: 0,
          lineSpacing: 0,
          wordSpacing: 0,
          lineMode: 2,
          textAlignVertical: 0,
          // 6.0.4版本更换为顶对齐
          zIndex: 0,
        ).toCanvasElement());
        offsetY += (location.height - 5.0);
      } else if (e.bindElementType == ElementItemType.barcode) {
        canvasElements.add(BarCodeElement(
                id: JsonElement.generateId(),
                x: x,
                y: y,
                width: location.width,
                height: location.width / 2.0,
                value: e.value ?? '',
                rotate: 0,
                fontSize: 3.2,
                textPosition: 0,
                textHeight: 3.4,
                codeType: e.codeType)
            .toCanvasElement());
        offsetY += (location.width / 2.0 + 3.0);
      } else if (e.bindElementType == ElementItemType.qrcode) {
        canvasElements.add(QrCodeElement(
                id: JsonElement.generateId(),
                x: x,
                y: y,
                width: location.width,
                height: location.width,
                value: e.value ?? '',
                rotate: 0,
                correctLevel: 0,
                codeType: e.codeType)
            .toCanvasElement());
        offsetY += (location.width + 3.0);
      }
    });
    if (canvasElements.length > 0) {
      addCanvasElements(canvasElements, isSelectedFirst: isSelectedFirst, fromBusiness: true);
    }
  }

  List<CanvasElement> generateCanvasElements(List<ElementCreateRequiredModel> elementCreateList) {
    List<CanvasElement> canvasElements = [];
    double offsetY = 5.0;
    elementCreateList.forEach((e) {
      Rect location = e.location!;
      if ((location ?? Rect.zero) == Rect.zero) {
        location = Rect.fromLTWH(5.0, offsetY, 30.0, 10.0);
      }
      if (e.bindElementType == ElementItemType.text) {
        canvasElements.add(TextElement(
          id: JsonElement.generateId(),
          x: location.left,
          y: location.top,
          width: location.width,
          height: location.height,
          value: e.value ?? '',
          fontSize: 3.2,
          rotate: 0,
          fontStyle: [],
          textAlignHorizontal: Directionality.of(context) == TextDirection.rtl ? 2 : 0,
          letterSpacing: 0,
          lineSpacing: 0,
          wordSpacing: 0,
          lineMode: 2,
          textAlignVertical: 0,
          // 6.0.4版本更换为顶对齐
          zIndex: 0,
        ).toCanvasElement());
        offsetY += (location.height - 5.0);
      } else if (e.bindElementType == ElementItemType.barcode) {
        canvasElements.add(BarCodeElement(
                id: JsonElement.generateId(),
                x: location.left,
                y: location.top,
                width: location.width,
                height: location.width / 2.0,
                value: e.value ?? '',
                rotate: 0,
                fontSize: 3.2,
                textPosition: 0,
                textHeight: 3.4,
                codeType: e.codeType)
            .toCanvasElement());
        offsetY += (location.width / 2.0 + 3.0);
      } else if (e.bindElementType == ElementItemType.qrcode) {
        canvasElements.add(QrCodeElement(
                id: JsonElement.generateId(),
                x: location.left,
                y: location.top,
                width: location.width,
                height: location.width,
                value: e.value ?? '',
                rotate: 0,
                correctLevel: 0,
                codeType: e.codeType)
            .toCanvasElement());
        offsetY += (location.width + 3.0);
      }
    });
    return canvasElements;
  }

  ///从高级二维码表单创建
  void addAdvanceQRCodeElement(Map advanceQRCodeInfo) {
    AdvanceQRCodeModel advanceQRCode = advanceQRCodeInfo["data"];
    _logger.log("Directionality.of(context)： ${Directionality.of(context) == TextDirection.rtl}");
    final id = JsonElement.generateId();
    // List<CanvasElement> elements =
    //     _canvasElements.where((element) => element.elementType == ElementItemType.qrcode).toList();
    // double x = 1;
    // double y = 1;
    // if (elements != null && elements.length > 0) {
    //   x = (elements.length + 1) * 16.0.dp2mm();
    //   y = (elements.length + 1) * 16.0.dp2mm();
    // }
    Point point = _getAddElementPosition();
    double x = point.x.toDouble();
    double y = point.y.toDouble();
    bool isLive = false;
    String liveCodeId = "";
    bool isForm = false;
    String formId = "";
    if (advanceQRCode.isForm ?? false) {
      isForm = true;
      formId = advanceQRCode.id!;
    } else {
      isLive = true;
      liveCodeId = advanceQRCode.id!;
    }
    String qrcodeValue = advanceQRCode.shortUrl!;
    addCanvasElement(QrCodeElement(
            isLive: isLive,
            liveCodeId: liveCodeId,
            isForm: isForm,
            formId: formId,
            value: qrcodeValue,
            id: id,
            width: 10.0,
            height: 10.0,
            x: x,
            y: y,
            rotate: 0,
            correctLevel: 0,
            codeType: QrcodeType.QR_CODE)
        .toCanvasElement());
  }

  /// 工具栏基础元素新建入口
  void addElementBox(String identifier) {
    _logger.log("Directionality.of(context)： ${Directionality.of(context) == TextDirection.rtl}");
    final id = JsonElement.generateId();
    // List<CanvasElement> elements = _canvasElements.where((element) => element.elementType == identifier).toList();
    // double x = 1;
    // double y = 1;
    // if (elements != null && elements.length > 0) {
    //   x = (elements.length + 1) * 16.0.dp2mm();
    //   y = (elements.length + 1) * 16.0.dp2mm();
    // }
    Point point = _getAddElementPosition();
    double x = point.x.toDouble();
    double y = point.y.toDouble();
    if (ElementItemType.text == identifier) {
      addCanvasElement(TextElement(
              id: id,
              width: 24.0,
              height: 8.019293,
              x: x,
              y: y,
              fontSize: 3.2,
              rotate: 0,
              fontStyle: [],
              type: ElementItemType.text.toString(),
              textAlignHorizontal: Directionality.of(context) == TextDirection.rtl ? 2 : 0,
              letterSpacing: 0,
              lineSpacing: 0.0,
              value: '请输入...',
              wordSpacing: 0,
              lineMode: 2,
              textAlignVertical: 0,
              // 6.0.4版本更换为顶对齐
              zIndex: 0,
              boxStyle: TextElementBO.defaultBoxStyle(),
              textStyle: TextElementBO.defaultTextStyles())
          .toCanvasElement());
    } else if (ElementItemType.qrcode == identifier) {
      addCanvasElement(QrCodeElement(
              value: "123456",
              id: id,
              width: 10.0,
              height: 10.0,
              x: x,
              y: y,
              rotate: 0,
              correctLevel: 0,
              codeType: QrcodeType.QR_CODE)
          .toCanvasElement());
    } else if (ElementItemType.barcode == identifier) {
      addCanvasElement(BarCodeElement(
              id: id,
              width: 20,
              height: 10,
              x: x,
              y: y,
              rotate: 0,
              fontSize: 3.2,
              value: "123456",
              textPosition: 0,
              textHeight: 3.4,
              codeType: BarcodeType.CODE128)
          .toCanvasElement());
    }
  }

  Point _getAddElementPosition() {
    double x;
    double y;
    List<JsonElement> jsonElements = (_canvasElements ?? [])
        .map((e) => e.data)
        .where((element) => !element.isMirrorElement() && _isElementInCanvas(element))
        .toList();
    if (jsonElements.isNotEmpty) {
      x = 0;
      y = 0;
      jsonElements.forEach((jsonElement) {
        double tempX;
        double tempY;
        if (jsonElement.rotate == 90 || jsonElement.rotate == 270) {
          tempX = jsonElement.x.toDouble() + 0.5 * (jsonElement.width.toDouble() - jsonElement.height.toDouble());
          tempY = jsonElement.y.toDouble() + 0.5 * (jsonElement.height.toDouble() + jsonElement.width.toDouble());
        } else {
          tempX = jsonElement.x.toDouble();
          tempY = jsonElement.y.toDouble() + jsonElement.height.toDouble();
        }
        if (tempY > y) {
          x = tempX;
          y = tempY;
        }
      });
    } else {
      x = 16.0.dp2mm().toDouble();
      y = 16.0.dp2mm().toDouble();
    }
    return Point(x, y);
  }

  /// 元素是否整个包含在画板内
  bool _isElementInCanvas(JsonElement jsonElement) {
    double templateWidth = _templateData?.width?.toDouble() ?? 0;
    double templateHeight = _templateData?.height?.toDouble() ?? 0;
    double x = jsonElement.x.toDouble();
    double y = jsonElement.y.toDouble();
    double elementWidth = jsonElement.width.toDouble();
    double elementHeight = jsonElement.height.toDouble();
    if (jsonElement.rotate == 90 || jsonElement.rotate == 270) {
      x = x + 0.5 * (elementWidth - elementHeight);
      y = y + 0.5 * (elementHeight - elementWidth);
      double temp = elementWidth;
      elementWidth = elementHeight;
      elementHeight = temp;
    }
    return y >= 0 && y + elementHeight <= templateHeight;
  }

  void reset() {
    _nbCanvasTemplateKey.currentState?.animateResetInitialize();
    _nbCanvasTemplateKey.currentState?.handleBgPressed();
  }

  void cancelElementFocused() {
    _nbCanvasTemplateKey.currentState?.handleBgPressed();
  }

  Rect getMaterialPositionInfo(int width, int height,
      {bool isNeedFill = false, String materialId = "", bool isBoder = false}) {
    double templateWidth = TinyKitCanvasObjectSharedWidget.canvasDataOf(context)?.width?.toDouble() ?? 0;
    double templateHeight = TinyKitCanvasObjectSharedWidget.canvasDataOf(context)?.height?.toDouble() ?? 0;
    double elementWidth = width.px2mm().toDouble();
    double elementHeight = height.px2mm().toDouble();
    double originElementWidth = elementWidth;
    double x = 0;
    double y = 0;
    // ///添加素材时，默认大小和二维码一样
    if (materialId.isNotEmpty && isBoder == false) {
      elementWidth = 10.0;
      elementHeight = (10.0 * elementHeight) / originElementWidth;
    } else {
      if (elementWidth / elementHeight >= templateWidth / templateHeight) {
        ///图片宽高比大于等于画板宽高比，图片宽度与画板宽度进行比较
        //if (elementWidth > templateWidth || isNeedFill) {
        ///图片宽度大于画板宽度，图片按照画板宽度进行等比缩放
        elementWidth = templateWidth;
        elementHeight = elementWidth * height / width;
        // }else{

        // }
      } else {
        ///图片宽高比小于画板宽高比，图片高度与画板高度进行比较
        // if (elementHeight > templateHeight || isNeedFill) {
        ///图片高度大于画板高度，图片按照画板高度进行等比缩放
        elementHeight = templateHeight;
        elementWidth = elementHeight * width / height;
        // }
      }
    }
    x = max(0.5 * (templateWidth - elementWidth), 0);
    y = max(0.5 * (templateHeight - elementHeight), 0);
    Rect rect = Rect.fromLTWH(x, y, elementWidth, elementHeight);
    return rect;
  }

  ///从动态源批量添加元素到画板
  addCanvasElementsFromDynamicSource(List<CanvasElement> canvasElements, List<ExcelBindPair> bindPairs,
      {bool focus = false}) {
    _canvasElements?.addAll(canvasElements);

    ///从元素导入，导入多个元素后直接切到数据面板，导入单个元素则还是到样式
    ///换excel导入，直接切到数据面板
    _focusedElements.clear();
    if (bindPairs.length == 1 && focus == true && _canvasElements?.first != null) {
      _focusedElements.add(_canvasElements!.first);
      widget.onElementFocusChanged?.call(_focusedElements, 0);
    }

    // if (canvasElements.length > 1) {
    //   _handleMultiSelectChanged(true);
    //   _focusedElements.addAll(canvasElements);
    //   Future.delayed(Duration(milliseconds: 100), () {
    //     widget.onElementFocusChanged?.call(_focusedElements, 2);
    //     FloatingBarVisibleNotifier().forceUpdate();
    //   });
    //   return;
    // } else {
    //   _focusedElements.add(_canvasElements.last);
    //   widget.onElementFocusChanged?.call(_focusedElements, 0);
    // }
  }

  addCanvasElement(CanvasElement canvasElement, {bool focused = true, bool stack = true, bool fromBusiness = false}) {
    /// 新建默认不显示工具条
    addCanvasElements([canvasElement], focused: focused, stack: stack, fromBusiness: fromBusiness);
  }

  addCanvasElements(List<CanvasElement> canvasElements,
      {bool isSelectedFirst = false, bool focused = true, bool stack = true, bool fromBusiness = false}) {
    _canvasElements?.addAll(canvasElements);

    ///导入多个元素后多选状态处理
    if (focused == false && stack == false && fromBusiness == true) {
      _handleMultiSelectChanged(true);
      _focusedElements.addAll(canvasElements);
      Future.delayed(Duration(milliseconds: 100), () {
        widget.onElementFocusChanged?.call(_focusedElements, 2);
      });
      return;
    }

    if (!_multiSelect) {
      _focusedElements.clear();
    }
    if (focused) {
      CanvasElement? element = isSelectedFirst ? canvasElements.first : _canvasElements?.last;
      if (element != null) {
        _focusedElements.add(element);
      }
    }

    widget.onElementFocusChanged?.call(_focusedElements, 0);
    if (_focusedElements.length == 1 && _focusedElements.first.data.type == ElementItemType.text /*&& !fromBusiness*/) {
      if (!fromBusiness) {
        widget.onElementValueEditorDisplay.call(_focusedElements.first, _focusedElements.first, isDoubleClick: false);
      }
    } else {}
  }

  /// 处理元素属性更新
  handleElementsUpdated(List<CanvasElement>? elements) {
    _logger.log("=====================handleElementsUpdated--刷新镜像元素");

    /// 重置数据变动 element 的图像缓存
    elements?.forEach((element) {
      //refreshAssociateElement(element);
      element.resetImageCache();
    });

    /// 检测镜像是否需要刷新
    //_handleMirrorElementsRefresh(elements);
    ///关联元素
    //_handleLegoElementsRefresh(elements);
  }

  /// 处理元素属性更新
  handleElementsUpdated2(List<CanvasElement>? elements) {
    _logger.log("=====================handleElementsUpdated2--刷新镜像元素");

    /// 重置数据变动 element 的图像缓存
    elements?.forEach((element) {
      element.resetImageCache();
    });

    /// 检测镜像是否需要刷新
    _handleMirrorElementsRefresh(elements);

    if (elements != null && elements.length == 1) {
      _focusedElements.clear();
      _focusedElements.addAll(elements);
    }
  }

  /// 输入焦点切换
  switchCursorFocus(CanvasElement canvasElement) {
    _handleBoxDoubleTap(canvasElement);
  }

  /// 重置画板，类似点击画板外区域
  void resetOperablePanel() {
    _handleSelectionChanged(null);
  }
}
