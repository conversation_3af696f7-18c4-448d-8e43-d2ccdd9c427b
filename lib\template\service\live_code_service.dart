import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:niim_login/login_plugin/utils/graphql_utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:text/template/model/Live_code.dart';

import '../../application.dart';
import '../../tools/to_Native_Method_Channel.dart';

/**
 * 活码接口请求
 */
class LiveCodeService {
  static String KEY_LIVECODE_PRE = "key_live_code_";

  /**
   * 下载活码信息
   */
  static Future<void> getLiveCodeByIds(List<String> ids) async{
    String gql = '''
    query liveCodeById(\$deleted: Boolean, \$ids: [GraphQLObjectIDScalar!]!) {
      dynamicQRCodeById(deleted: \$deleted, ids: \$ids) {
        id
        userId
        title
        contentSummary
        shortUrl
        contentId
        deleted
        gmtCreated
        gmtModified
        schemaVersion
    template
    viewCount
      }
    }
    ''';
    bool isLogin = Application.user != null;
    GraphQLUtils.sharedInstance().configCustomHeaders({"languageCode": Application.currentAppLanguageType});
    QueryResultWrapper wrapper = await GraphQLUtils.sharedInstance()
        .query(gql,
            variables: {/*'deleted':deleted,*/'ids': ids}, authorization: isLogin, headers: {"niimbot-user-agent": Application.agent});
    QueryResult value = wrapper.queryResult;
    if (!value.hasException && value.data != null && value.data!['dynamicQRCodeById'] != null) {
      List<dynamic> jsonList = value.data!['dynamicQRCodeById'];
      List<LiveCode> liveCodeList = jsonList.map((json) => LiveCode.fromJson(json)).toList();

      final liveCodeDataList = liveCodeList.map((livecode){
        return {'id': livecode.id, 'item': liveCodeToJson(livecode)};
      }).toList();
      await ToNativeMethodChannel.cacheAdvanceQRCodeInfo({"codeType":"liveCode","data":liveCodeDataList});
      // SharedPreferences sp = await SharedPreferences.getInstance();
      // await Future.forEach(liveCodeList, (liveCode) async{
      //   await sp.setString("$KEY_LIVECODE_PRE${liveCode.id}", liveCodeToJson(liveCode));
      // });

    } else {
      if (wrapper.error.isNotEmpty) {}
    }
  }
}
