
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_template/models/copy_wrapper.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_enum.dart';
import 'package:niimbot_template/models/template_data.dart' as NiimbotTemplateData;
import 'package:path_provider/path_provider.dart';
import 'package:text/pages/my_template/model/template_data_extension.dart';
import 'package:text/template/util/template_detail_db_utils.dart';
import 'package:text/template/util/template_transform_utils.dart';
import 'package:niimbot_excel/niimbot_data_source_utils.dart';

import '../../application.dart';

class TemplateMiscUtils {

  /**
   * 检查模版资源是否完整
   */
  static Future<bool> checkTemplateResourceComplate(List<String> ids) async{
    for (String templateId in ids) {
      NiimbotTemplateData.TemplateData? templateData = await TemplateDetailDBUtils.queryTemplateById(templateId);
      if (templateData == null) {
        return false;
      }

      TemplateData canvasTemplateData = await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(templateData);
      List<String> notDownloadFontCodes = await canvasTemplateData.checkNotDownloadFontCodes();
      bool canEdit = await templateData.canEdit();
      if (!canEdit || notDownloadFontCodes.isNotEmpty) {
        return false;
      }
    }
    return true;
  }

  static String getUserId() {
    if (Application.user == null) {
      return "0";
    } else {
      var userId = Application.user!.id;
      if (userId == null) {
        return "0";
      } else if (userId is double) {
        return userId.toInt().toString();
      } else if (userId is int) {
        return userId.toString();
      } else {
        return userId.toString();
      }
    }
  }

  /**
   * 判断是否有excel文件缓存
   */
  static Future<bool> hasExcelCache(String? excelHash) async{
    if(excelHash == null || excelHash.isEmpty){
      return false;
    }
    String excelFilePath = await NiimbotDataSourceUtils.buildLocalDataSourcePath(excelHash);
    final file = File(excelFilePath);
    bool isExists = await file.exists();
    return isExists;
  }

  /// 版本比较工具方法
  /// 基于Android VersionCompareUtils.compareVersion逻辑实现
  /// 返回值: -1表示templateVersion < appSupportVersion, 0表示相等, 1表示templateVersion > appSupportVersion
  static int compareTemplateVersion(String templateVersion, String appSupportVersion) {
    List<String> v1 = templateVersion.split(".");
    List<String> v2 = appSupportVersion.split(".");

    if (v1.length != 4 || v2.length != 4) {
      return -1;
    }

    List<String> v1List = List<String>.from(v1);
    List<String> v2List = List<String>.from(v2);

    // 版本号从4位变为3位进行比较，去除图像库修复的版本号(移除第3位)
    v1List.removeAt(2);
    v2List.removeAt(2);

    for (int i = 0; i < v1List.length; i++) {
      try {
        int num1 = int.parse(v1List[i]);
        int num2 = int.parse(v2List[i]);

        if (num1 < num2) {
          return -1;
        } else if (num1 > num2) {
          return 1;
        }
      } catch (e) {
        // 解析错误，返回-1
        return -1;
      }
    }

    return 0;
  }


  ///处理老excel datasource-uri
  static Future<NiimbotTemplateData.TemplateData> processDataSourceUrl(NiimbotTemplateData.TemplateData templateData) async {
    List<TemplateDataSource>? dataSources = templateData.dataSources;
    TemplateDataSource? dataSource = dataSources?.firstOrNull;

    if (dataSource != null &&
        dataSource.type == TemplateDataSourceType.excel &&
        dataSource.hash.isNotEmpty &&
        !dataSource.uri.startsWith("http")) {
      Directory baseExcelFileDir = await getApplicationDocumentsDirectory();
      String  documentPath = baseExcelFileDir.path;

      String filePath = NiimbotDataSourceUtils.getLocalDataSourcePath(documentPath, dataSource.hash);
      File file = File(filePath);
      if(file.existsSync()){
        Uint8List fileBytes = await file.readAsBytes();
        String url =
        await ExcelManager.sharedInstance().uploadOSSFunction(dataSource.name!, fileBytes, dataSource.hash,createCloudFile: false);
        dataSource.uri = url;
      }
      return templateData.copyWith(dataSources: CopyWrapper.value(dataSources));
    }
    return templateData;
  }



}
