import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:text/pages/industry_template/home/<USER>/label_usage_record.dart';

class PersonalTemplateState {
  var selectIndex = 999999;
  var showToast = "";
  var showTopToast = "";
  var smartIsPop = false;

  /// 是否正在展示VIP试用弹窗
  var isShowVIPTrailing = false;

  /// 批量打印防重复点击标志
  var isBatchPrinting = false;

  /// 是否连接网络
  var connectState = true.obs;

  GlobalKey batchPrintKey = GlobalKey();

  /// 当前连接的标签纸
  LabelUsageRecord? ConnectingModel;
  PersonalTemplateState();
}
