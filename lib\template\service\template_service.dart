import 'dart:async';
import 'dart:convert';

import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/niimbot_template.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:text/network/dio_utils.dart';
import 'package:text/network/http_api.dart';
import 'package:text/template/model/api_error.dart';
import 'package:text/template/model/template_list_model.dart';
import 'package:text/template/util/template_transform_utils.dart';
import 'package:text/utils/toast_util.dart';

import '../../application.dart';

/**
 * 模版相关接口请求
 */
class TemplateService {
  Future<TemplateData> updateTemplateToService(TemplateData template, bool isEtag) {
    String templateJson = jsonEncode(template.toJson());
    Map<String, dynamic> urlMap = {};
    if (isEtag) {
      urlMap = MyTemplateApi.updateEtagTemplate;
    } else {
      urlMap = MyTemplateApi.updateTemplate;
    }
    Completer<TemplateData> completer = Completer();
    DioUtils.instance.requestNetwork<String>(Method.post, urlMap, params: templateJson, isList: false,
        onSuccess: (data) {
      completer.complete(template);
    }, onError: (int code, String msg) {
      completer.completeError(ApiError(code, msg));
    });
    return completer.future;
  }

  ///服务端新增模板
  Future<String> createTemplateToService(TemplateData localTemplate, bool isEtag) {
    Map<String, dynamic> urlMap = {};
    if (isEtag) {
      urlMap = MyTemplateApi.createEtagTemplate;
    } else {
      urlMap = MyTemplateApi.createTemplate;
    }
    Completer<String> completer = Completer();
    String templateJson = jsonEncode(localTemplate.toJson());
    DioUtils.instance.requestNetwork<Map<String, dynamic>>(Method.post, urlMap, params: templateJson, isList: false,
        onSuccess: (data) {
      String serviceId = data!['id'].toString();
      completer.complete(serviceId);
    }, onError: (int code, String msg) {
      completer.completeError(ApiError(code, msg));
    });
    return completer.future;
  }

  ///服务端删除模板
  Future<bool> deleteTemplateToService(TemplateData template, {bool fromBackground = false}) {
    Map<String, dynamic> map = {"ids": template.id!};
    Completer<bool> completer = Completer();
    DioUtils.instance.requestNetwork<dynamic>(Method.post, MyTemplateApi.deleteTemplate, params: map, isList: false,
        onSuccess: (data) {
      completer.complete(true);
    }, onError: (int code, String msg) {
      completer.completeError(ApiError(code, msg));
    });
    return completer.future;
  }

  Future<bool> modifyTemplateNameToService(String templateId, String name, String updateTime,
      {Function? success, Function(int, String)? fail}) {
    Completer<bool> completer = Completer();
    Map<String, dynamic> map = {};
    map["id"] = templateId;
    map["name"] = name;
    map["updateTime"] = updateTime;
    DioUtils.instance.requestNetwork(Method.post, MyTemplateApi.templateRename, params: map, isList: false,
        onSuccess: (data) {
      completer.complete(true);
    }, onError: (int code, String msg) {
      completer.completeError(ApiError(code, msg));
    });
    return completer.future;
  }

  /**
   * 根据模版id获取模版详情
   */
  Future<TemplateData?> fetchTemplateDetailById(String templateId, {bool isNewApi = true}) {
    Completer<TemplateData?> completer = Completer();
    Map<String, dynamic> urlMap = {};
    if (isNewApi) {
      //新接口
      urlMap = MyTemplateApi.fetchTemplateDetailById(templateId);
    } else {
      urlMap = MyTemplateApi.fetchCustomTemplateDetailById(templateId);
    }
    DioUtils.instance.requestNetwork<Map<String, dynamic>>(isNewApi ? Method.get : Method.post, urlMap, isList: false,
        onSuccess: (data) async {
      if (data != null) {
        if(data['totalPage'] == null){
          data['totalPage'] = 1;
        }
        TemplateData templateData = await TemplateTransformUtils.parseServiceTemplateJsonToNiimbotTemplate(data);
        completer.complete(templateData);
      } else {
        completer.complete(null);
        return;
      }
    }, onError: (int code, String msg) {
      // showToast(msg: "$code $msg");
      completer.completeError(ApiError(code, msg));
    });
    return completer.future;
  }

  /**
   * 扫码获取云模版信息
   */
  Future<TemplateData?> fetchCloudTemplateByScanCode(String onecode) {
    Completer<TemplateData?> completer = Completer();
    Map<String, dynamic> map = {};
    map["oneCode"] = onecode;
    DioUtils.instance.requestNetwork<Map<String, dynamic>>(Method.post, MyTemplateApi.cloudTemplateByScanCode,
        params: map, isList: false, onSuccess: (data) async {
      if (data == null || data.isEmpty) {
        completer.complete(null);
      } else {
        TemplateData templateData = await TemplateTransformUtils.parseServiceTemplateJsonToNiimbotTemplate(data);
        completer.complete(templateData);
      }
    }, onError: (int code, String msg) {
      completer.completeError(ApiError(code, msg));
    });
    return completer.future;
  }

  /**
   * 通过id和更新时间获取模板详情(增量方式)
   */
  Future<TemplateData?> fetchTemplateUpdateStatus(String id,String currentTime) {
    Completer<TemplateData?> completer = Completer();
    Map<String, dynamic> map = {};
    map["currentTime"] = currentTime;
    map["id"] = id;
    DioUtils.instance.requestNetwork<Map<String, dynamic>>(Method.post, MyTemplateApi.fetchTemplateUpdateStatus,
        params: map, isList: false, onSuccess: (data) async {
          if (data == null || data.isEmpty) {
            completer.complete(null);
          } else {
            TemplateData templateData = await TemplateTransformUtils.parseServiceTemplateJsonToNiimbotTemplate(data);
            completer.complete(templateData);
          }
        }, onError: (int code, String msg) {
          completer.completeError(ApiError(code, msg));
        });
    return completer.future;
  }

  /**
   * 根据模版id获取共享给我的模版详情
   */
  Future<TemplateData> fetchFolderShareTemplateDetailById(String templateId) {
    Completer<TemplateData> completer = Completer();
    DioUtils.instance.requestNetwork<Map<String, dynamic>>(
        Method.get, MyTemplateApi.fetchFolderShareTemplateDetailById(templateId), isList: false,
        onSuccess: (data) async {
      TemplateData templateData = await TemplateTransformUtils.parseServiceTemplateJsonToNiimbotTemplate(data!);
      completer.complete(templateData);
    }, onError: (int code, String msg) {
      completer.completeError(ApiError(code, msg));
    });
    return completer.future;
  }

  Future<TemplateListModel> fetchMyGoodTemplateList(
      int page, int limit, int folderId, int commodityTemplate, String templateName) {
    Completer<TemplateListModel> completer = Completer();
    Map<String, dynamic> map = {};
    map["page"] = page;
    map["limit"] = limit;
    if (commodityTemplate >= 0) {
      map['commodityTemplate'] = commodityTemplate;
    } else {
      map['folderId'] = folderId;
    }
    if (templateName.isNotEmpty) {
      map['templateName'] = templateName;
    }
    DioUtils.instance.requestNetwork<Map<String, dynamic>>(Method.post, MyTemplateApi.myTemplateList,
        params: map, isList: false, onSuccess: (data) async {
      TemplateListModel templateListModel = await TemplateListModel.fromJson(data);
      completer.complete(templateListModel);
    }, onError: (int code, String msg) {
      completer.completeError(ApiError(code, msg));
    });
    return completer.future;
  }

  Future<TemplateListModel> fetchMyTemplateList(int page, int limit, {int folderId = -1}) async {
    Completer<TemplateListModel> completer = Completer();
    Map<String, dynamic> map = {};
    map["page"] = page;
    map["limit"] = limit;
    if (folderId != -1) {
      map['folderId'] = folderId;
    }
    SharedPreferences sp = await SharedPreferences.getInstance();
    String lastRequestTimeKey = "";
    String? lastRequestTime = "";
    if (Application.user!.id is int) {
      lastRequestTimeKey = '${Application.user!.id.toString()}_template/myList';
    } else {
      lastRequestTimeKey = '${(Application.user!.id as double).toInt().toString()}_template/myList';
    }
    lastRequestTime = await sp.getString(lastRequestTimeKey);
    if (lastRequestTime == null) {
      DateTime modifiedDateTime = DateTime.now().subtract(Duration(days: 90));
      map["fromTimestamp"] = modifiedDateTime.millisecondsSinceEpoch.toString();
      map["toTimestamp"] = DateTime.now().millisecondsSinceEpoch.toString();
    } else {
      int timestamp = int.parse(lastRequestTime);
      DateTime originalDateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      DateTime modifiedDateTime = originalDateTime.subtract(Duration(hours: 1));
      map["fromTimestamp"] = modifiedDateTime.millisecondsSinceEpoch.toString();
      map["toTimestamp"] = DateTime.now().millisecondsSinceEpoch.toString();
    }
    DioUtils.instance.requestNetwork<Map<String, dynamic>>(Method.post, MyTemplateApi.myTemplateList,
        params: map, isList: false, onSuccess: (data) async {
      sp.setString(lastRequestTimeKey, DateTime.now().millisecondsSinceEpoch.toString());
      TemplateListModel templateListModel = await TemplateListModel.fromJson(data);
      completer.complete(templateListModel);
    }, onError: (int code, String msg) {
      completer.completeError(ApiError(code, msg));
    });
    return completer.future;
  }
}
