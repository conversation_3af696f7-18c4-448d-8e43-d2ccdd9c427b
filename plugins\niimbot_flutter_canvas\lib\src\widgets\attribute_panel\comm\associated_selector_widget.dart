import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/svg_icon.dart';
import 'package:popover/popover.dart';

import '../../../localization/localization_public.dart';
import '../../../utils/theme_color.dart';

class AssociatedModel {
  String key;
  String unit;
  int deviation;
  int? realyValue;
  bool? isSelected;
  AssociatedModel(this.key, this.unit, this.deviation, {this.realyValue});

  String getDateTimeDescrptime() {
    String dateTimeValue = realyValue?.toString() ?? "0";
    if (key == DateElementHelper.Associated_Hour) {
      return intlanguage('app100001949', '\$小时', param: [dateTimeValue]);
    } else if (key == DateElementHelper.Associated_Day) {
      return intlanguage('app100001425', '\$天', param: [dateTimeValue]);
    } else if (key == DateElementHelper.Associated_Day) {
      return intlanguage('app100001435', '\$月', param: [dateTimeValue]);
    } else if (key == DateElementHelper.Associated_Day) {
      return intlanguage('app100001435', '\$年', param: [dateTimeValue]);
    } else {
      return "";
    }
  }
}

class AssociatedSelectorWidget extends StatefulWidget {
  int validityPeriodNew;
  String validityPeriodUnit;
  final Function completed;

  AssociatedSelectorWidget(
      {Key? key, required this.validityPeriodNew, required this.validityPeriodUnit, required this.completed})
      : super(key: key);

  @override
  _AssociatedSelectorWidgetState createState() => _AssociatedSelectorWidgetState();
}

class _AssociatedSelectorWidgetState extends State<AssociatedSelectorWidget> {
  late String associatedTime;
  late AssociatedModel associatedModel;
  GlobalKey associatedAnchorkey = GlobalKey();
  late int subIndex;
  var _associatedMap = [
    AssociatedModel(DateElementHelper.Associated_Hour, intlanguage('app100001435', '小时'), 60),
    AssociatedModel(DateElementHelper.Associated_Day, intlanguage('app100001420', '天'), 1440),
    AssociatedModel(DateElementHelper.Associated_month, intlanguage('app100001436', '个月'), 43200),
    AssociatedModel(DateElementHelper.Associated_Year, intlanguage('app100000680', '年'), 525600),
  ];

  @override
  void initState() {
    super.initState();
    _parseDateTime();
  }

  @override
  void didUpdateWidget(covariant AssociatedSelectorWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _parseDateTime();
  }

  void _parseDateTime() {
    _associatedMap.asMap().forEach((index, element) {
      if (element.key == widget.validityPeriodUnit) {
        subIndex = index; // 外层数组的下标
        associatedModel = AssociatedModel(element.key, element.unit, element.deviation);
        associatedTime = widget.validityPeriodNew.toString();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
          "track": "click",
          "posCode": "108_301",
        });
        _didClickSelectedGender();
      },
      child: Container(
        padding: EdgeInsetsDirectional.fromSTEB(12, 8, 12, 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SvgIcon(
              'assets/element/attribute/value_add.svg',
              color: ThemeColor.brand,
              fit: BoxFit.cover,
            ),
            SizedBox(
              width: 7,
            ),
            Text(
              associatedTime,
              key: associatedAnchorkey,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w600,
                color: ThemeColor.brand,
              ),
            ),
            Text(
              associatedModel.unit,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w600,
                color: ThemeColor.brand,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _didClickSelectedGender() {
    showPopover(
        context: associatedAnchorkey.currentContext!,
        bodyBuilder: (context) {
          return AssociatedPopoverWidget(associatedTime, associatedModel, subIndex, _associatedMap, (offset, key) {
            widget.completed(offset, key);
          });
        },
        shadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 55,
            spreadRadius: 0.1,
          ),
        ],
        radius: 12,
        direction: PopoverDirection.bottom,
        width: 250,
        arrowHeight: 10,
        arrowWidth: 18,
        contentDyOffset: 0,
        barrierColor: Colors.transparent,
        rootNavigator: false,
        onPop: () {});
  }
}

class AssociatedPopoverWidget extends StatefulWidget {
  String _associatedTime = '';
  int _subIndex = 0;
  int _time = 0;
  AssociatedModel _associatedUnit;
  List<AssociatedModel> _associatedMap;
  List<int> _list = List.generate(99, (index) => index + 1);
  final Function(int timeOffset, String key) completed;

  AssociatedPopoverWidget(
    this._associatedTime,
    this._associatedUnit,
    this._subIndex,
    this._associatedMap,
    this.completed,
  );

  @override
  State<AssociatedPopoverWidget> createState() => _AssociatedPopoverWidgetState();
}

class _AssociatedPopoverWidgetState extends State<AssociatedPopoverWidget> {
  bool isFormatPickerScrolling = false;
  bool isDayPickerScrolling = false;

  get isScrolling => isFormatPickerScrolling || isDayPickerScrolling;

  @override
  void initState() {
    super.initState();
    widget._time = int.parse(widget._associatedTime) - 1;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 180,
      padding: EdgeInsetsDirectional.fromSTEB(15, 0, 15, 0),
      child: Stack(
        alignment: Alignment.center, // 设置 Stack 的对齐方式为居中
        children: [
          Container(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 30,
                ),
                Container(
                  height: 150,
                  width: 45,
                  child: NotificationListener<ScrollNotification>(
                    onNotification: (ScrollNotification notification) {
                      if (notification is ScrollStartNotification) {
                        isFormatPickerScrolling = true;
                      } else if (notification is ScrollEndNotification) {
                        isFormatPickerScrolling = false;
                        _analysisTime();
                      }
                      return false;
                    },
                    child: CupertinoPicker(
                      selectionOverlay: Container(
                        decoration: BoxDecoration(
                          color: Colors.transparent, // 设置横条颜色
                        ),
                      ),
                      scrollController: FixedExtentScrollController(initialItem: widget._time),
                      // diameterRatio: 1.5,
                      // offAxisFraction: 0.2, //轴偏离系数
                      // useMagnifier: true, //使用放大镜
                      // magnification: 1.5, //当前选中item放大倍数
                      itemExtent: 34,
                      looping: true,
                      onSelectedItemChanged: (value) {
                        widget._time = value;
                      },
                      children: widget._list
                          .map((e) => Center(
                                child: Text(
                                  e.toString(),
                                  style: TextStyle(
                                    color: ThemeColor.title,
                                    fontSize: 20,
                                  ),
                                ),
                              ))
                          .toList(),
                    ),
                  ),
                ),
                Container(
                  height: 100,
                  width: 70,
                  child: NotificationListener<ScrollNotification>(
                    onNotification: (ScrollNotification notification) {
                      if (notification is ScrollStartNotification) {
                        isDayPickerScrolling = true;
                      } else if (notification is ScrollEndNotification) {
                        isDayPickerScrolling = false;
                        _analysisTime();
                      }
                      return false;
                    },
                    child: CupertinoPicker(
                      selectionOverlay: Container(
                        decoration: BoxDecoration(
                          color: Colors.transparent, // 设置横条颜色
                        ),
                      ),
                      scrollController: FixedExtentScrollController(initialItem: widget._subIndex),
                      // diameterRatio: 1.5,
                      // offAxisFraction: 0.2, //轴偏离系数
                      // useMagnifier: true, //使用放大镜
                      // magnification: 1.5, //当前选中item放大倍数
                      itemExtent: 34,
                      onSelectedItemChanged: (value) {
                        widget._subIndex = value;
                      },
                      children: widget._associatedMap
                          .map((e) => Center(
                                child: Text(
                                  e.unit,
                                  style: TextStyle(
                                    color: ThemeColor.title,
                                    fontSize: 20,
                                  ),
                                ),
                              ))
                          .toList(),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Positioned(
              child: IgnorePointer(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6), // 设置圆角半径为 20
                color: ThemeColor.COLOR_BLACK_10.withAlpha(15),
              ),
              alignment: Alignment.center,
              height: 32,
            ),
          ))
        ],
      ),
    );
  }

  _analysisTime() {
    if (isScrolling) {
      return;
    }
    var model = widget._associatedMap[widget._subIndex];
    var timeOffset = model.deviation * (widget._time + 1);
    widget.completed(widget._time + 1, model.key);
  }
}
