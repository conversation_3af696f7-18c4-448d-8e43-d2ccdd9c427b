import 'dart:convert';
import 'dart:typed_data';

import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_template/models/parse/parse_resource.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_template/models/template_data.dart' as NiimbotTemplateData;
import 'package:niimbot_template/template_parse.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';
import 'package:text/application.dart';
import 'package:text/pages/canvas/niimbot_canvas_page.dart';

import 'package:niimbot_excel/models/excel_data.dart';
import 'package:niimbot_excel/models/excel_sheet_data.dart';
import 'package:niimbot_excel/models/interface.dart';
import 'package:niimbot_excel/models/range.dart';
import 'package:niimbot_excel/models/template_excel_data.dart';
import 'package:niimbot_excel/models/template_task.dart';
import 'package:niimbot_excel/modify_utils.dart';
import 'package:niimbot_excel/niimbot_excel_utils.dart';
import 'package:niimbot_flutter_canvas/src/model/stack/template_task.dart' as tt;
import "package:niimbot_flutter_canvas/src/model/excel/excel_covert_manager.dart";
import "package:niimbot_flutter_canvas/src/model/excel_bind_info.dart";

class TemplateTransformUtils {
  /**
   * 插件模版数据---转换成---画板模版数据
   */
  static Future<TemplateData> niimbotTemplateToCanvasTemplate(NiimbotTemplateData.TemplateData niimbotTemplate) async{
    Map<String, dynamic> niimbotTemplateJson = niimbotTemplate.toJson();
    if ((niimbotTemplateJson['dataSourceModifies'] is TemplateDataSourceModifies)) {
      TemplateDataSourceModifies modifies = niimbotTemplateJson['dataSourceModifies'] as TemplateDataSourceModifies;
      niimbotTemplateJson['modify'] = modifies.toJson();
    }else{
      niimbotTemplateJson['modify'] = niimbotTemplateJson['dataSourceModifies'];
    }
    niimbotTemplateJson['bindInfo'] = niimbotTemplateJson['dataSourceBindInfo'];
    niimbotTemplateJson['dataSource'] = niimbotTemplateJson['dataSources'];
    niimbotTemplateJson['currentPageIndex'] = niimbotTemplate.currentPageIndex;
    niimbotTemplateJson['currentPage'] = niimbotTemplate.currentPageIndex+1;
    niimbotTemplateJson['local_thumb'] = niimbotTemplate.localThumbnail;
    TemplateData canvasTemplate = TemplateData.fromJson(niimbotTemplateJson);
    canvasTemplate.localThumb = niimbotTemplateJson['local_thumb'];
    return canvasTemplate;
  }

  /**
   * 画板json--转换成--插件模版数据
   */
  static Future<NiimbotTemplateData.TemplateData> canvasJsonToNiimbotTemplate(String canvasJson,{bool needParseElements = true}) async{
    Map<String,dynamic> jsonMap = jsonDecode(canvasJson);
    //转换成插件中的templateData结构
    jsonMap['dataSourceModifies'] = jsonMap['modify'];
    jsonMap['dataSourceBindInfo'] = jsonMap['bindInfo'];
    jsonMap['dataSources'] = jsonMap['dataSource'];
    int currentPageIndex = 0;
    if (jsonMap['currentPage'] is num && jsonMap['currentPage'] > 0) {
      currentPageIndex = jsonMap['currentPage']-1;
    }
    jsonMap['currentPageIndex'] = currentPageIndex;
    if(jsonMap['local_type'] == null){
      jsonMap['local_type'] = jsonMap['localType']??0;
    }

    NiimbotTemplateData.TemplateData templateData = await parseServiceTemplateJsonToNiimbotTemplate(jsonMap,needParseElements: needParseElements);
    return templateData;
  }

  static Future<NiimbotTemplateData.TemplateData> parseServiceTemplateJsonToNiimbotTemplate(Map<String,dynamic> templateJson,{bool needParseElements = true}) async{
    // Map<String,dynamic> jsonMap = await processOldExcelTemplateField(templateJson);
    Map<String,dynamic> jsonMap = templateJson;
    NiimbotTemplateData.TemplateData templateData = await TemplateParse.parseFromMap(jsonMap,parseLocalImageResources: (url) {

      final backgroundImage =
      TemplateParseUtils.parseStringFromJSON(jsonMap['backgroundImage']);
      if(backgroundImage != null){
        final urlList = backgroundImage.split(',');
        var index = urlList.indexOf(url);
        if (jsonMap['localBackground'] != null) {
          List localBackground = jsonMap['localBackground'];
          if (localBackground.isNotEmpty && index>=0 && index<localBackground.length) {
            return Future.value(localBackground[index]);
          } else {
            return Future.value('');
          }
        }else {
          return Future.value('');
        }
      }else{
        return Future.value('');
      }
    },parseFontResources: (fontCode,fontFamily,exist) async{
      return ParseFontResource(fontCode: fontCode,fontFamily: fontFamily,hasVipRes: false);
    },needParseElements: needParseElements);
    return templateData;
  }

  /**
   * 预处理旧的excel模版字段（带有externalData的）--转换为datasource
   */
  static Future<Map<String,dynamic>> processOldExcelTemplateField(Map<String,dynamic> jsonMap) async{
    TemplateData _templateData = TemplateData().transformJson(jsonMap);
    ExternalData? oldExcelData = _templateData.externalData;
    if (oldExcelData != null &&
        oldExcelData.externalDataList != null &&
        oldExcelData.externalDataList!.isNotEmpty) {
      List<ExternalListModel> oldDataList = oldExcelData!.externalDataList!;
      List<ExcelData> list = [];
      for (var i = 0; i < oldDataList.length; i++) {
        ExternalListModel oldItem = oldDataList[i];
        ExcelData item = ExcelData(
            // name: oldItem.name,
            name: "Sheet1",
            data: ExcelSheetData(columnHeaders: oldItem.data!.columnHeaders, columns: oldItem.data!.columns));
        list.add(item);
      }
      //转换excel数据
      //externalData和task转换成新的结构
      TemplateExcelData excelData = TemplateExcelData(
          id: int.tryParse(oldExcelData.id ?? "0"), fileName: oldExcelData.fileName ?? "", list: list);

      uploadFile(List<int> fileBytes, String fileName, String contentHash) async {
        if (!Application.isLogin || Application.networkConnected == false) {
          return "";
        }
        return ExcelManager.sharedInstance().uploadOSSFunction(fileName, Uint8List.fromList(fileBytes), contentHash,createCloudFile: false);
      }

      getExcelCloudFileById(num id, String md5) async {
        if (!Application.isLogin || Application.networkConnected == false) {
          return null;
        }
        if ((id == 0) && md5.isEmpty) {
          return null;
        }
        var excelCloudFile = await ExcelManager.sharedInstance().getCloudFileInfo(id.toInt(), md5);
        if (excelCloudFile == null) {
          return null;
        }
        final covertData = await ExcelCovertManager.covertExcel(
            id.toString(), excelCloudFile.name, excelCloudFile.md5, excelCloudFile.downloadUrl);
        excelCloudFile.downloadUrl = covertData.downloadUrl;
        excelCloudFile.md5 = covertData.md5;
        final ret = Future.value(excelCloudFile);
        return ret;
      }

      final dataSource =
          await NiimbotExcelUtils.covertExcelDataToDataSource(excelData, getExcelCloudFileById, uploadFile);
      if(dataSource == null){
        return jsonMap;
      }
      jsonMap['dataSources'] = [dataSource.toJson()];

      tt.TemplateTask? oldTask = _templateData.task;
      if (oldTask != null && oldTask.modifyData != null) {
        TemplateTask newTask = TemplateTask();
        newTask.modifyData = oldTask.modifyData;
        TemplateModify? modify = taskToModify(newTask);
        _templateData.modify = modify;
      }

      int totalPage = _templateData.totalPage;

      List<Range>? ranges = dataSource.range;
      if (ranges != null && ranges.isNotEmpty) {
        List<int> rangeArray = transformRangesToRows(ranges);
        totalPage = rangeArray.length;
      }
      ExcelPageInfo pageInfo =
      ExcelPageInfo(page: _templateData.currentPageIndex + 1, total: totalPage);
      _templateData.bindInfo = pageInfo;
      jsonMap['dataSourceBindInfo'] = pageInfo.toJson();


      if (_templateData.modify != null) {
        jsonMap['dataSourceModifies'] = _templateData.modify?.toJson();
      }

      //去除旧的excel导入相关字段
      jsonMap.remove('externalData');
      jsonMap.remove('task');
      return jsonMap;
    }
    return jsonMap;
  }

  /// Range列表格式转化为离散行号的数组
  /// [range] 已选行的range数组
  static List<int> transformRangesToRows(List<Range> ranges) {
    List<int> array = [];
    for (int i = 0; i < ranges.length; i++) {
      Range item = ranges[i];
      for (int row = item.s; row <= item.e; row++) {
        array.add(row);
      }
    }
    return array;
  }


}
