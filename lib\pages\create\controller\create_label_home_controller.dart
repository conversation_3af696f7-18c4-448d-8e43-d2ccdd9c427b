import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:get/get.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:text/pages/create/model/printer_connect_state.dart';
import 'package:text/pages/industry_template/home/<USER>/label_usage_record.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/utils/ai/layout/layout_util.dart';
import 'package:text/utils/layout_scheme_template_utils.dart';

import '../../../utils/ai/layout_helper.dart';
import '../../industry_template/home/<USER>/hard_ware_serise_model.dart';

/// 新建标签纸的状态
enum CreateLabelStatus {
  /// 存在历史记录
  existHistory,

  /// 不存在历史记录且未连接打印机
  noHistoryAndNoConnect,

  /// 不存在历史记录且连接一台不带识别功能的打印机
  noHistoryAndConnectUnrecognizedMachine,

  /// 不存在历史记录且连接打印机，打印机纸张未识别
  noHistoryAndConnectUnrecognizedLabel,

  /// 过渡状态，当识别到已安装的标签纸时弹出
  transitionToLabelUsing
}

class CreateLabelHomeController extends GetxController {
  /// 当前view状态
  var status = CreateLabelStatus.noHistoryAndNoConnect.obs;

  /// 正在使用的标签纸模型
  var labelUsingModel = Rxn<LabelUsageRecord>();

  /// 上次使用的标签纸记录模型
  var labelUsageModel = Rxn<LabelUsageRecord>();

  /// 选择的标签纸
  var labelSelectedModel = Rxn<LabelUsageRecord>();

  /// 正在连接的硬件设备模型
  var hardwareSeriesModelUsing = Rxn<HardWareSeriesModel>();

  /// 标签纸模型，对外使用，无须知道当前是选择的、上次使用的标签纸模型
  LabelUsageRecord? get labelModel {
    return labelUsingModel.value ?? (labelSelectedModel.value ?? labelUsageModel.value);
  }

  /// 打印机连接状态
  late PrinterConnectState? printerState;

  /// 页面正在消失状态, 选择标签纸/新建标签时页面闪现
  var pageDismissing = false.obs;

  /// 打印机是否连接
  bool get isConnectedDevice {
    return (printerState?.connected ?? 0) == 1;
  }
}

/// 跳转原生
extension GotoNativePage on CreateLabelHomeController {
  /// 跳转原生画板
  gotoCanvasPage({bool isCustomLabel = false, bool isSelected = false, VoidCallback? completion}) {
    Map? toCanvasTemplateData = isSelected ? labelSelectedModel.value?.rawData : labelModel?.rawData;
    if (toCanvasTemplateData != null) {
      LayoutHelper().getLayoutTemplate(toCanvasTemplateData).then((onValue) {
        if (onValue != null) {
          toCanvasTemplateData = onValue;
        }
        toCanvasTemplateData!["vip"] = false;
        toCanvasTemplateData!["hasVIPRes"] = false;
        var labelId = toCanvasTemplateData!["id"];
        toCanvasTemplateData!["id"] = DateTime.now().millisecondsSinceEpoch;

        Map<String, dynamic> args = {
          'labelData': toCanvasTemplateData ?? {},
          'isCustomLabel': isCustomLabel,
          'labelId': labelId,
        };

        // if (Platform.isIOS) {
        pageDismissing.value = true;
        completion?.call();
        CustomNavigation.pop();
        Future.delayed(Duration(milliseconds: 150)).then((value) {
          CustomNavigation.gotoNextPage('toCanvasPage', args).then((value) {
            // completion?.call();
            // CustomNavigation.pop();
          });
        });
      });
    }
  }
}

List createDefaultByLego(List<dynamic>? inputAreas, double labelWidth, double labelHeight) {
  try {
    if (inputAreas?.isEmpty ?? true) {
      return [creatDefaultTextElement(labelWidth, labelHeight)];
    }
    String areaJson = LayoutUtil.initLabelInputAreas(inputAreas, labelWidth, labelHeight);
    final Map<String, dynamic> result =
        LayoutSchemeTemplateUtils.parseAreaToTemplate(areaJson, labelWidth, labelHeight);
    return result['elements'] ?? [creatDefaultTextElement(labelWidth, labelHeight)];
  } catch (e, s) {
    debugPrint('异常信息:\n $e');
    debugPrint('调用栈信息:\n $s');
    return [creatDefaultTextElement(labelWidth, labelHeight)];
  }
}

Map creatDefaultTextElement(double templateWidth, double templateHeight) {
  /// 模板宽度不足小五号字显示换行，导致退出时对比判定错误
  double singleLineWidthWith5 = 24;
  double singleLineHeightWith5 = 26.25;
  double singleLineWidthWith4 = 37.84;
  double width = templateWidth;
  double height = 3.75;
  double x = 0;
  double y = (templateHeight - 3.75) / 2;
  double fontSize = 3.2;

  /// 宽度大于4号字体默认文本宽度时，字号为小四号字
  if (width > singleLineWidthWith4) {
    fontSize = 4.2;
    height = 5.76;
    y = (templateHeight - 5.76) / 2;
  } else if (templateWidth < singleLineWidthWith5) {
    if (templateHeight > singleLineWidthWith5) {
      width = 3.75;
      height = singleLineHeightWith5;
      x = (templateWidth - 3.75) / 2;
      y = (templateHeight - singleLineHeightWith5) / 2;
    } else {
      width = 17.47;
      height = 2.68;
      x = templateWidth - width > 0 ? (templateWidth - width) / 2 : 0;
      y = (templateHeight - 2.68) / 2;
      fontSize = 2.3;
    }
  }
  Map defaultTextElement = {};
  defaultTextElement["id"] = JsonElement.generateId();
  defaultTextElement["x"] = x;
  defaultTextElement["y"] = y;
  defaultTextElement["width"] = width;
  defaultTextElement["height"] = height;
  defaultTextElement["zIndex"] = 0;
  defaultTextElement["type"] = "text";
  defaultTextElement["rotate"] = 0;
  defaultTextElement["isLock"] = 0;
  defaultTextElement["fieldName"] = "";
  defaultTextElement["mirrorId"] = "";
  defaultTextElement["isOpenMirror"] = 0;
  defaultTextElement["mirrorType"] = 0;
  defaultTextElement["isBinding"] = 0;
  defaultTextElement["bindingColumn"] = 0;
  defaultTextElement["value"] = "";
  defaultTextElement["typesettingMode"] = 1;
  defaultTextElement["typesettingParam"] = [0, 180];
  defaultTextElement["textAlignHorizonral"] = 1;
  defaultTextElement["textAlignVertical"] = 0; // 6.0.4版本更换为顶对齐
  defaultTextElement["lineMode"] = 2;
  defaultTextElement["wordSpacing"] = 0.0;
  defaultTextElement["letterSpacing"] = 0.0;
  defaultTextElement["lineSpacing"] = 0.0;
  defaultTextElement["fontStyle"] = [];
  defaultTextElement["fontSize"] = fontSize;
  defaultTextElement["fontColor"] = null;
  defaultTextElement["isTitle"] = false;
  defaultTextElement["contentTitle"] = "";
  defaultTextElement["lineBreakMode"] = 1;
  defaultTextElement["elementColor"] = [255, 0, 0, 0];
  defaultTextElement["boxStyle"] = "auto-width";
  // TextElementBO.defaultBoxStyle().value;
  return defaultTextElement;
}
