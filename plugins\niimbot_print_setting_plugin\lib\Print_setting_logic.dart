import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:nety/models/index.dart';
import 'package:nety/models/niimbot_printer.dart';
import 'package:nety/nety.dart';
import 'package:niimbot_excel/niimbot_data_source_utils.dart';
import 'package:niimbot_excel/niimbot_excel_utils.dart';
import 'package:niimbot_print_setting_plugin/extensions/niimbot_printer.dart';
import 'package:niimbot_print_setting_plugin/interface/print_setting_channel_interface.dart';
import 'package:niimbot_print_setting_plugin/model/c1_print_data.dart';
import 'package:niimbot_print_setting_plugin/model/color.dart';
import 'package:niimbot_print_setting_plugin/model/print_data.dart';
import 'package:niimbot_print_setting_plugin/model/print_page_style.dart';
import 'package:niimbot_print_setting_plugin/model/print_paramter.dart';
import 'package:niimbot_print_setting_plugin/model/update_field_manager.dart';
import 'package:niimbot_print_setting_plugin/print/print_check/check_before_print.dart';
import 'package:niimbot_print_setting_plugin/print/print_manager.dart';
import 'package:niimbot_print_setting_plugin/print_page_manager.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart';
import 'package:niimbot_print_setting_plugin/utils/FToast.dart';
import 'package:niimbot_print_setting_plugin/widget/label_replace_widget.dart';
import 'package:niimbot_print_setting_plugin/widget/print_setting_preview.dart';
import 'package:niimbot_template/models/copy_wrapper.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_data_source_info.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_template/models/template/template_data_source_range.dart';
import 'package:niimbot_template/models/template/template_enum.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/rfid_bind/rfid_info_manager.dart';
import 'package:niimbot_template/template_parse.dart';
import 'package:niimbot_template/utils/display_util.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:nety/models/niimbot_rfid_info.dart';

import 'model/template_external_data.dart';

class PrintSettingLogic extends GetxController {
  PrintTaskType taskType;
  PrintParameter? parameter;
  bool hasExcelFileCache = false;
  PrintData printData = PrintData();
  CheckBeforePrint checkBeforePrint = CheckBeforePrint();
  final StreamController<Map> streamController = StreamController<Map>.broadcast();
  late Stream<Map> stream;

  get extrain {
    return parameter!.templateMoudle?['profile']['extrain'];
  }

  String get meetingId {
    if (parameter == null) {
      return '';
    } else {
      Map uniAppInfo = parameter?.customData?['uniAppInfo'] ?? {};
      return uniAppInfo['meetingId'] ?? '';
    }
  }

  String get machineAlias {
    return channel.getConnectedMachineAlias();
  }

  bool isShowRFID = false;
  PrintPageManager pageManager;
  PrintSettingChannelInterface channel;

  //是否已经初始化过Rfid替换弹窗显示逻辑
  bool hasInitRfidReplaceLogic = false;

  //rfid替换弹窗是否正在显示
  bool isShowingRfidReplaceDialog = false;
  PrintCopiesMode printCopiesMode = PrintCopiesMode.settingCopies;
  int currentCopiesLinkFieldIndex = -1;

  PrintSettingLogic(this.taskType, this.parameter, this.pageManager, this.channel);

  addListenerEventBus(Function(bool) initResult) {
    // 获取 Stream 对象
    stream = streamController.stream;
    channel.addEventBusListener((data) async {
      if (data is Map) {
        if (data.containsKey("action")) {
          if (data['action'] == 'replaceLabelEvent') {
            parameter!.templateMoudle = jsonDecode(data['templateJson']);
            _initTemplateRources(initResult);
            refreshTemplate();
          } else if (data['action'] == 'changePrintDataEvent') {
            parameter!.templateMoudle = jsonDecode(data['templateJson']);
            isMultipleTemplates();
            // streamController.add({"action": "refreshTemplatePreview"});
            _initTemplateRources(initResult, isFromChangePrintData: true);
            // Future.delayed(const Duration(milliseconds: 300), () {
            //   refreshTemplate();
            // });
            // Future.delayed(const Duration(milliseconds: 100), () {
            //   refreshTemplate();
            // });
            // Future.delayed(const Duration(milliseconds: 500), () {
            //   refreshTemplate();
            // });
            // Future.delayed(const Duration(milliseconds: 700), () {
            //   refreshTemplate();
            // });
          } else if (data['action'] == 'setFlutterlabelData') {
            Map? labelDataMap = data['labelData'];
            if (null != labelDataMap && labelDataMap.isNotEmpty) {
              Map<String, dynamic> labelData = jsonDecode(jsonEncode(labelDataMap));
              updateRfidTemplate(pageManager.context, labelData, checkRfid: true);
              PrintManager().rfidTemplate = await TemplateParse.parseFromMap(labelData);
            } else {
              PrintManager().rfidTemplate = null;
            }
          }
          streamController.add(data);
          isShowRFID = await channel.getIsShowRfid();
        } else if (data.containsKey("userInfo")) {
          data["action"] = "loginStateChanged";
          streamController.add(data);
          isShowRFID = await channel.getIsShowRfid();
        } else if (data.containsKey("updateTemplate")) {
          var templateData = data['updateTemplate'];
          if (templateData != null) {
            parameter!.templateMoudleNew = templateData;
          }
        } else if (data.containsKey("networkChanged")) {
          streamController.add(data);
        }
      } else if (data == "modifyMachineAlias") {
        update([UpdateFieldManager.device]);
      }
    });
    TemplateParse.matchLocale(toLocale(channel.getAppLanguageType())).then((_) {});
    if (parameter?.customData?["batchGoodsList"] != null && parameter?.customData?["batchGoodsList"]!.isNotEmpty) {
      parameter!.templateMoudle!['totalPage'] = parameter?.customData?["batchGoodsList"]?.length;
      if (parameter!.templateMoudle!['bindInfo'] != null) {
        parameter!.templateMoudle!['bindInfo']['total'] = parameter!.templateMoudle!['totalPage'];
      }
    }
    _initTemplateRources(initResult);
  }

  List<TemplateDataSource>? _dataSourceRangeToDesktop(
      final List<TemplateDataSource>? dataSource, final TemplateDataSourceInfo? dataSourceInfo, bool isPc) {
    try {
      return dataSource?.map((final e) {
        if ((e.range?.isEmpty == true) && dataSourceInfo != null) {
          return e.copyWith(range: [TemplateDataSourceRange(s: 1, e: dataSourceInfo.total)]);
        } else {
          // if(isPc){
          return e.copyWith(
              range: e.range?.map((final v) {
            return TemplateDataSourceRange(s: v.s - 1, e: v.e - 1);
          }).toList());
          // }else{
          //   return e;
          // }
        }
      }).toList();
    } catch (e, s) {
      return dataSource;
    }
  }

  TemplateDataSourceModifies? _compatibleCommodityModifier(
      bool isCommodityTemplate, TemplateDataSourceModifies? dataSourceModifies) {
    try {
      if (isCommodityTemplate && dataSourceModifies != null) {
        dataSourceModifies.forEach((key, value) {
          value.forEach((rowKey, dataBindModify) {
            dataBindModify.value = null;
          });
        });
      }
    } catch (e, s) {
      return dataSourceModifies;
    }
    return dataSourceModifies;
  }

  _initTemplateRources(Function(bool) initResult, {bool isFromChangePrintData = false}) async {
    var value = await channel.getPrinterLabelData();
    isShowRFID = (await channel.getIsShowRfid());
    if (value != null && value.isNotEmpty) {
      Map<String, dynamic> labelData = jsonDecode(jsonEncode(value));
      PrintManager().rfidTemplate = await TemplateParse.parseFromMap(labelData);
    } else {
      PrintManager().rfidTemplate = null;
    }

    parameter!.templateMoudle!['dataSourceModifies'] = parameter!.templateMoudle!['modify'];
    parameter!.templateMoudle!['dataSourceBindInfo'] = parameter!.templateMoudle!['bindInfo'];
    parameter!.templateMoudle!['dataSources'] = parameter!.templateMoudle!['dataSource'];
    // Map<String, dynamic> copyTemplateModule = {...parameter!.templateMoudle!};
    Map<String, dynamic> copyTemplateModule = jsonDecode(jsonEncode(parameter!.templateMoudle!));
    channel.transformTemplateToStand(copyTemplateModule).then((jsonMap) {
      Map<String, dynamic> templateJsonMap = Map.from(copyTemplateModule);
      templateJsonMap['dataSourceModifies'] = jsonMap['modify'];
      templateJsonMap['elements'] = jsonMap['elements'];
      for (var element in jsonMap['elements']) {
        if (element["type"] == "text") {
          if (((element["value"] ?? "") as String).isEmpty && ((element["dataBind"] ?? []) as List).isEmpty) {
            if (parameter!.taskType == PrintTaskType.miniApp) {
              element["value"] = ' ';
            } else {
              element["value"] = style.defaultTextValue;
            }
          }
        }

        if (element["type"] == "text" || element["type"] == "date" || element["type"] == "serial") {
          // 横向文本模式三下文本竖向对齐需要-1，用于适配老数据
          if (element["typesettingMode"] == 1 &&
              element["boxStyle"] == "fixed-width-height" &&
              element["textAlignVertical"] > 0) {
            element["textAlignVertical"] = (element["textAlignVertical"] ?? 1) - 1;
          }
        }

        if (element["type"] == 'table') {
          if (element['columnWidth'] is List && (element['columnWidth'] as List).isEmpty) {
            element['rowHeight'] = [0];
            element['columnWidth'] = [0];
            element['lineWidth'] = 0;
          }
          if (element['rowHeight'] is List && (element['rowHeight'] as List).isEmpty) {
            element['columnWidth'] = [0];
            element['rowHeight'] = [0];
            element['lineWidth'] = 0;
          }
          if (element['cells'] != null && element['cells'] is List) {
            element['cells'].forEach((v) {
              var cell = v as Map<String, dynamic>;
              if (cell["typesettingMode"] == 1 &&
                  cell["boxStyle"] == "fixed-width-height" &&
                  cell["textAlignVertical"] > 0) {
                cell["textAlignVertical"] = (cell["textAlignVertical"] ?? 1) - 1;
              }
            });
          }

          if (element['combineCells'] != null && element['combineCells'] is List) {
            element['combineCells'].forEach((v) {
              var cell = v as Map<String, dynamic>;
              if (cell["typesettingMode"] == 1 &&
                  cell["boxStyle"] == "fixed-width-height" &&
                  cell["textAlignVertical"] > 0) {
                cell["textAlignVertical"] = (cell["textAlignVertical"] ?? 1) - 1;
              }
            });
          }
        }
      }
      templateJsonMap['dataSources'] = jsonMap['dataSource'];
      List<String> headerList = [];
      TemplateParse.parseFromMap(
        templateJsonMap,
        parseDataSourceResources: (url, hash) async {
          if (templateJsonMap['dataSources'] != null && templateJsonMap['dataSources'].isNotEmpty) {
            Map<String, dynamic> dataSource = templateJsonMap['dataSources'].first;
            if (parameter?.customData?["batchGoodsList"] != null && !isFromChangePrintData) {
              // dataSource['']
              List batchGoodsList = parameter?.customData?["batchGoodsList"];
              // if (dataSource['params'] == null) {
              //   dataSource['params'] = {};
              // }
              if (batchGoodsList != null && batchGoodsList.isNotEmpty) {
                if (dataSource['params'] == null) {
                  dataSource['params'] = {};
                }
                dataSource['params']['ids'] = batchGoodsList.map((element) {
                  return element['id'];
                }).toList();
              }
            }
            List rowDataHeaderList = (await channel.getTemplateRowDataHeaderWith(dataSource)) ?? [];
            if (rowDataHeaderList.isNotEmpty) {
              headerList = List<String>.from(rowDataHeaderList[1]);
              int columnCount = headerList.length;
              for (var i = 0; i < columnCount; i++) {
                if (headerList[i].isEmpty) {
                  String columnLetter = NiimbotExcelUtils.indexToLetters(i + 1);
                  headerList[i] = ("${getI18nString("app100001121", "列")}${columnLetter}");
                }
              }
              List<List<String>> rowData = List<List<String>>.from(rowDataHeaderList[0]);
              if (rowData.isNotEmpty) {
                rowData[0] = headerList;
              } else {
                rowData.add(headerList);
              }
              // parameter!.templateMoudle!["externalData"] =
              //     reconstructExternalData(headerList, rowData, dataSource["name"] ?? "");
              // return List<List<String>>.from(rowDataHeaderList[0]);
              return rowData;
            } else {
              return [];
            }
          } else {
            return [];
          }
        },
        // parseImageResources: (url, {existsSync, materialId}) async {
        //   return ParseImageResource(imageData: '', localImageUrl: '');
        // },
        parseLocalImageResources: (url) {
          final backgroundImage = TemplateParseUtils.parseStringFromJSON(jsonMap['backgroundImage']);
          if (backgroundImage != null) {
            final urlList = backgroundImage.split(',');
            var index = urlList.indexOf(url);
            if (jsonMap['localBackground'] != null) {
              List localBackground = jsonMap['localBackground'];
              if (localBackground.isNotEmpty && index >= 0 && index < localBackground.length) {
                return Future.value(localBackground[index]);
              } else {
                return Future.value('');
              }
            } else {
              return Future.value('');
            }
          } else {
            return Future.value('');
          }
        },
      ).then((value) async {
        debugPrint("打印设置模板数据处理完成" + DateTime.now().millisecondsSinceEpoch.toString());

        TemplateDataSourceModifies? dataSourceModifies;
        dataSourceModifies = _compatibleCommodityModifier(value.commodityTemplate, value.dataSourceModifies);

        List<TemplateDataSource>? dataSources;
        if (value.dataSources != null && value.dataSources!.isNotEmpty) {
          TemplateDataSource dataSource = value.dataSources!.first;
          if (dataSource.type == TemplateDataSourceType.commodity) {
            dataSource = dataSource.copyWith(headers: {DataBindingMode.commodity: headerList});
            if (dataSource.rowData.isNotEmpty && value.totalPage != dataSource.rowData.length - 1) {
              int maxPage = (dataSource.rowData.length - 1) == 0 ? 1 : dataSource.rowData.length - 1;
              style.pageMax = maxPage;
              printData.pageEnd = maxPage;
              value = value.copyWith(totalPage: maxPage);
            }
          } else {
            // dataSource = dataSource.copyWith(headers: {dataSource.headers!.keys.first: headerList});
            String filePath = await NiimbotDataSourceUtils.buildLocalDataSourcePath(dataSource.hash);
            final file = File(filePath);
            hasExcelFileCache = file.existsSync();
          }
          // if (dataSource.rowData.isEmpty) {
          //   ///TODO 数据源数据获取失败时处理
          //   initResult(false);
          // } else {
          //   initResult(true);
          // }
          initResult(true);
          dataSources = [dataSource];
          // dataSources = _dataSourceRangeToDesktop([dataSource], value.dataSourceBindInfo,true);
          // if(value.platformCode == TemplatePlatformCode.CP001PC){
          //
          // }else{
          //   dataSources = _dataSourceRangeToDesktop([dataSource], value.dataSourceBindInfo,false);
          // }
        } else {
          initResult(true);
        }
        parameter?.templateMoudleNew = value.copyWith(
            dataSources: CopyWrapper.value(dataSources), dataSourceModifies: CopyWrapper.value(dataSourceModifies));
        RfidInfoManager().templateData = parameter?.templateMoudleNew;
        if (!hasInitRfidReplaceLogic) {
          hasInitRfidReplaceLogic = true;
          channel.getPrinterLabelData().then((value) {
            if (value is Map) {
              Map<String, dynamic> labelData = jsonDecode(jsonEncode(value));
              updateRfidTemplate(pageManager.context, labelData, checkRfid: true);
            }
          });
        }
        if (taskType == PrintTaskType.printNullUi || taskType == PrintTaskType.printNullUiShowProgress) {
          //无UI打印流程 打印前检查
          int delayMilliseconds = 300;
          printData.uniappId = parameter?.customData?['uniAppInfo']?["uniAppId"] ?? "";
          printData.isShowLoading = parameter?.customData?['uniAppInfo']?["isShowLoading"] ?? true;
          Future.delayed(Duration(milliseconds: delayMilliseconds), () {
            checkBeforePrint.check(this).then((pass) {
              if (pass) {
                PrintManager().readyToPrint(this, const Uuid().v4());
              } else {
                Future.delayed(Duration(milliseconds: delayMilliseconds), () {
                  Navigator.of(pageManager.context).pop();
                  debugPrint("无UI打印进度场景 pop3");
                });
                channel.setUnNeedUIPrintComplete(
                    "0", parameter!.customData!["uniAppInfo"]["uniAppId"], parameter?.customData?["taskId"] ?? '', '');
              }
            });
          });
        } else if (taskType == PrintTaskType.printC1) {
          Map<String, dynamic>? customData = parameter?.customData?['customData'];
          // 设置C1的打印参数
          printData = C1PrintData();
          printData.printCount = customData?['printCount'] ?? 1;
          printData.printDensity = customData?['printDensity'] ?? 0;
          printData.ofsetX = customData?['hOffset'] ?? 0;
          printData.ofsetY = customData?['vOffset'] ?? 0;
          (printData as C1PrintData).cutType = customData?['cutType'];
          (printData as C1PrintData).tubeType = customData?['tubeType'];
          (printData as C1PrintData).tubeSpecs = customData?['tubeSpecs'];
          (printData as C1PrintData).isHalfCut = customData?['isHalfCut'];
          (printData as C1PrintData).cutDepth = customData?['cutDepth'];
          toPrint();
        }
        refreshTemplate();
      });
    });
  }

  Map<String, dynamic> reconstructExternalData(List<String>? headers, List<List<String>>? rowData, String name) {
    List<List<String>>? copyRowData = rowData;
    if (rowData != null && rowData!.length > 1) {
      copyRowData = rowData!.sublist(1);
    }
    Map<String, dynamic> externalJson = parameter!.templateMoudle!["externalData"];
    ExternalData externalData = ExternalData.fromJson(externalJson);

    List<List<String>> columnData = [];
    List<String> customHeaders = [];
    customHeaders = headers ?? [];
    int columnSize = (headers ?? []).length;
    for (int i = 0; i < columnSize; i++) {
      List<String> oneColumnData = [];
      for (int j = 0; j < (copyRowData ?? []).length; j++) {
        List<String> oneRow = copyRowData![j];
        oneColumnData.add(oneRow[i]);
      }
      columnData.add(oneColumnData);
    }
    if (externalData.externalDataList?.isEmpty == true) {
      ExternalListModel model =
          ExternalListModel(name: name, data: ExternalListModelData(columnHeaders: customHeaders, columns: columnData));
      externalData.externalDataList = [model];
    }
    return externalData.toJson();
  }

  toLocale(String language) {
    List<String> parts = language.split('-');
    String languageCode = parts[0];
    String? countryCode;
    if (parts.length > 1) {
      countryCode = parts[1];
    }
    Locale locale = Locale(languageCode);
    return locale;
  }

  initGenerateDesignRatio(BuildContext context) {
    String widthValue = parameter!.templateMoudle!['width'].toString();
    String hightValue = parameter!.templateMoudle!['height'].toString();
    // channel.resetGenerateDesignRatio(context, double.parse(widthValue), double.parse(hightValue));
    //使用niimbot_template插件中倍率计算类
    DisplayUtil.init(context);
    DisplayUtil.generateDesignRatio(double.parse(widthValue), double.parse(hightValue));
  }

  Future<TemplateData> getBatchPrintTemplateWith(int pageIndex) async {
    String templateId = parameter!.batchtIds![pageIndex]["id"];
    Map<String, dynamic> templateInfo = await channel.getBatchPrintTemplate(templateId);
    for (var element in templateInfo['elements']) {
      if (element["type"] == "text") {
        if (((element["value"] ?? "") as String).isEmpty && ((element["dataBind"] ?? []) as List).isEmpty) {
          if (parameter!.taskType == PrintTaskType.miniApp) {
            element["value"] = ' ';
          } else {
            element["value"] = style.defaultTextValue;
          }
        }
        // 横向文本模式三下文本竖向对齐需要-1，用于适配老数据
        if (element["typesettingMode"] == 1 &&
            element["boxStyle"] == "fixed-width-height" &&
            element["textAlignVertical"] > 0) {
          element["textAlignVertical"] = (element["textAlignVertical"] ?? 1) - 1;
        }
      }
      if (element["type"] == 'table') {
        if (element['cells'] != null && element['cells'] is List) {
          element['cells'].forEach((v) {
            var cell = v as Map<String, dynamic>;
            if (cell["typesettingMode"] == 1 &&
                cell["boxStyle"] == "fixed-width-height" &&
                cell["textAlignVertical"] > 0) {
              cell["textAlignVertical"] = (cell["textAlignVertical"] ?? 1) - 1;
            }
          });
        }

        if (element['combineCells'] != null && element['combineCells'] is List) {
          element['combineCells'].forEach((v) {
            var cell = v as Map<String, dynamic>;
            if (cell["typesettingMode"] == 1 &&
                cell["boxStyle"] == "fixed-width-height" &&
                cell["textAlignVertical"] > 0) {
              cell["textAlignVertical"] = (cell["textAlignVertical"] ?? 1) - 1;
            }
          });
        }
      }
    }
    TemplateData templateData = await TemplateParse.parseFromMap(
      templateInfo,
      parseLocalImageResources: (url) {
        var jsonMap = parameter!.templateMoudle!;
        final backgroundImage = TemplateParseUtils.parseStringFromJSON(jsonMap['backgroundImage']);
        if (backgroundImage != null) {
          final urlList = backgroundImage.split(',');
          var index = urlList.indexOf(url);
          if (jsonMap['localBackground'] != null) {
            List localBackground = jsonMap['localBackground'];
            if (localBackground.isNotEmpty && index >= 0 && index < localBackground.length) {
              return Future.value(localBackground[index]);
            } else {
              return Future.value('');
            }
          } else {
            return Future.value('');
          }
        } else {
          return Future.value('');
        }
      },
    );
    return templateData;
  }

  Future<TemplateData> getBatchPrintC1Template(int pageIndex) async {
    String templateId = parameter!.batchtIds![pageIndex]["id"];
    TemplateData? templateData = await channel.getBatchPrintC1Template(pageIndex);
    return templateData ?? TemplateData(usedFonts: {});
  }

  refreshTemplate({bool changPageIndex = false, bool justReplaceLabel = false}) {
    // Timer(Duration(milliseconds: 300), () {
    if (justReplaceLabel) {
      streamController.add({"action": "replaceLabelEvent"});
    } else {
      Map<String, dynamic> refreshTemplateInfo = {"action": "refreshTemplate"};
      refreshTemplateInfo["changPageIndex"] = changPageIndex;
      streamController.add(refreshTemplateInfo);
    }
    // });
  }

  closePrintSettingDialog() {
    Map<String, dynamic> event = {"action": "closePrintSettingDialog"};
    streamController.add(event);
  }

  destroyEventBus() {
    channel.destroyEventBus();
    streamController.close();
  }

  /**
   * 检查是否存在多个模板并设置打印范围
   *
   * 主要用于确定打印任务中是否包含多个模板，以及设置打印数据的起始页和结束页
   * 它会根据商品列表、总页数或批处理任务类型来设置打印范围
   */
  isMultipleTemplates() {
    // 获取商品列表，若参数customData中存在batchGoodsList，则使用之，否则初始化为空列表
    var goodsList = (parameter?.customData?["batchGoodsList"] ?? []) as List;
    // 获取总页数，若templateMoudle中存在total属性，则使用之，否则默认为1
    var totalPage = parameter!.templateMoudle?["totalPage"] ?? 1;
    Map? bindInfo = parameter!.templateMoudle?["bindInfo"];
    var total = bindInfo?["total"] ?? 1;
    if (totalPage < 1) {
      totalPage = 1;
    }
    if (totalPage < total) {
      totalPage = total;
    }
    List dataSource = parameter!.templateMoudle?['dataSource'] ?? []; //
    String externalDataFileName = parameter?.templateMoudle?['externalData']?['fileName'] ?? "";
    // 若商品列表长度大于1，则设置打印范围为商品列表的起始页到结束页
    if (goodsList.length >= 1) {
      style.pageMax = goodsList.length;
      // printData.pageBegin = 1;
      printData.pageEnd = goodsList.length;
      // 若总页数大于1，则设置打印范围为1到总页数
    } else if (totalPage > 1 || dataSource.isNotEmpty) {
      style.pageMax = totalPage;
      //  printData.pageBegin = 1;
      printData.pageEnd = totalPage;
      // 若任务类型为批处理且批处理ID列表非空，则设置打印范围为批处理ID列表的起始页到结束页
    } else if (parameter!.pdfBindInfo != null && parameter!.pdfBindInfo!.pdfBindElementInfos.isNotEmpty) {
      totalPage = parameter!.pdfBindInfo!
          .getPDFEnablePrintMaxNumber(parameter!.templateMoudleNew, templateDaMapInfo: parameter!.templateMoudle!);
      //  printData.pageBegin = 1;
      style.pageMax = totalPage;
      printData.pageEnd = totalPage;
      // 若任务类型为批处理且批处理ID列表非空，则设置打印范围为批处理ID列表的起始页到结束页
    } else if (taskType == PrintTaskType.batch) {
      style.pageMax = parameter!.batchtIds!.length;
      //  printData.pageBegin = 1;
      printData.pageEnd = parameter!.batchtIds!.length;
    }

    // 初始化标志变量，用于指示是否存在序列化元素
    bool containsSerialElement = false;
    // 若模板模块非空，则检查其中是否存在序列化元素
    if (goodsList.length < 1 &&
        dataSource.isEmpty &&
        externalDataFileName.isEmpty &&
        (parameter!.pdfBindInfo == null || parameter!.pdfBindInfo!.pdfBindElementInfos.isEmpty) &&
        parameter!.templateMoudle != null) {
      ///使用结构体解析数据需要等待结构体初始化，会有显示延迟的问题
      //containsSerialElement = parameter!.templateMoudleNew!.elements.any((element) => element is SerialElement);
      containsSerialElement = parameter!.templateMoudle!['elements'].any((element) => element['type'] == 'serial');

      if (containsSerialElement) {
        style.isSerial = true;
        style.pageMax = 5;
        // printData.pageBegin = 1;
        printData.pageEnd = 5;
      }
    }
    if (style.pageIndex + 1 > style.pageMax) {
      style.pageIndex = style.pageMax - 1;
    }
    int currentPage = parameter!.templateMoudle?["currentPage"] ?? 1;
    printData.pageBegin = currentPage;
    style.isDataSource = goodsList.length > 1 || totalPage > 1 || dataSource.isNotEmpty;
    // 根据商品列表长度、总页数或是否存在序列化元素，决定是否显示打印范围
    style.isShowRange = goodsList.length > 1 || totalPage > 1 || containsSerialElement;
  }

  /**
   * 执行打印操作
   *
   * 准备打印数据并触发打印任务。
   * 处理不同的打印任务类型和RFID信息的绑定显示。
   *
   * @param {bool} isSave 是否保存模板，默认为false
   * @param {bool} isJustOne 是否仅打印一个，默认为false
   */
  static bool isAbleToPrint = true;

  toPrint({bool isSave = false, bool isJustOne = false}) async {
    //小程序相关场景不做离线检查
    if (taskType != PrintTaskType.miniApp &&
        taskType != PrintTaskType.printC1 &&
        taskType != PrintTaskType.printNullUi &&
        taskType != PrintTaskType.printNullUiShowProgress) {
      bool canPrint = await channel.checkOfflinePeriod(pageManager.context);
      if (!canPrint) {
        return;
      }
    }

    // 准备打印模板，依据参数对象中的模板模块生成JSON表示
    printData.printTemplate = parameter?.templateMoudleToJson();
    // C1场景默认不做限制
    // if (!isAbleToPrint && taskType != PrintTaskType.printC1) {
    //   return;
    // }
    isAbleToPrint = false;
    Future.delayed(const Duration(seconds: 2), () {
      isAbleToPrint = true;
    });
    if (style.isPrinting || style.isSavePrinting) return;
    // 增加打印浓度，主要因为选择浓度时存在1单位误差
    printData.printDensity;

    printData.isSave = isSave;

    // 设置打印任务类型，使用参数对象中的taskType字段
    printData.taskType = parameter?.taskType!.name ?? '';

    // 设置打印历史ID，如果参数中没有则保持为空字符串
    printData.printHistoryId = parameter?.printHistoryId ?? '';
    http: //127.0.0.1:58056/XM5DpkJ12gY=/

    // 初始化批处理商品列表，从参数对象的自定义数据中获取
    printData.batchGoodsList = (parameter?.customData?["batchGoodsList"] ?? []) as List;

    // 设置是否显示RFID绑定信息，默认为否，根据参数对象中的showRfidBind字段决定
    printData.showRfidBind = parameter?.showRfidBind ?? false;

    // 如果参数中包含RFID信息，则将其编码为JSON字符串
    if (parameter?.rfidInfo != null) {
      printData.rfidInfo = jsonEncode(parameter?.rfidInfo);
    }

    // 如果参数中包含批处理ID，则将其编码为JSON字符串
    if (parameter?.batchtIds != null) {
      printData.batchtIds = jsonEncode(parameter?.batchtIds);
    }

    // 如果参数对象的自定义数据中包含uniappId，则进行赋值
    printData.uniappId = parameter?.customData?['uniAppInfo']?["uniAppId"] ?? printData.uniappId;
    printData.isJustOne = isJustOne;
    // 将准备好的打印数据备份到局部变量data
    // 区分打印数据类型，不同的机型存在不同的打印参数
    PrintData data = PrintData();
    if (printData is C1PrintData) {
      data = C1PrintData();
      (data as C1PrintData).cutType = (printData as C1PrintData).cutType;
      data.tubeType = (printData as C1PrintData).tubeType;
      data.tubeSpecs = (printData as C1PrintData).tubeSpecs;
      data.isHalfCut = (printData as C1PrintData).isHalfCut;
      data.cutDepth = (printData as C1PrintData).cutDepth;
    }
    data.printTemplate = printData.printTemplate;
    data.printCount = printData.printCount;
    data.printDensity = printData.printDensity;
    data.pageBegin = printData.pageBegin;
    data.pageEnd = printData.batchtIds.length;

    data.printPriority = printData.printPriority;
    data.ofsetY = -printData.ofsetY;
    data.ofsetX = printData.ofsetX;
    data.batchtIds = printData.batchtIds;
    data.isSave = printData.isSave;
    data.printHistoryId = printData.printHistoryId;
    data.showRfidBind = printData.showRfidBind;
    data.rfidInfo = printData.rfidInfo;
    data.taskType = printData.taskType;
    data.uniappId = printData.uniappId;
    data.batchGoodsList = printData.batchGoodsList;

    var uuid = Uuid().v4();
    // 如果设置为仅打印一个，则配置打印计数和开始页码，并指定任务类型为打印单个
    List<NiimbotRFIDInfo>? rfidInfos = channel.getDeviceRFIDCacheInfos();
    String ribbonSerial = '';
    String paperSerial = '';
    if (rfidInfos.isNotEmpty) {
      for (var element in rfidInfos) {
        if (element.type != 6) {
          paperSerial = element.uuid;
        } else {
          ribbonSerial = element.uuid;
        }
      }
    }
    if (isJustOne) {
      data.printCount = 1;
      data.pageBegin = style.pageIndex + 1;
      data.pageEnd = style.pageIndex + 1;
      printData.taskType = PrintTaskType.printJustOne.name;
      channel.trackEvent("click", "024_067_095",
          eventData: {'check_version': uuid, 'paperSerial': paperSerial, 'ribbonSerial': ribbonSerial});
    } else if (isSave) {
      channel.trackEvent("click", "024_067_096",
          eventData: {'check_version': uuid, 'paperSerial': paperSerial, 'ribbonSerial': ribbonSerial});
    } else if (parameter!.customData!['uniAppInfo'] != null &&
        (parameter!.customData!['uniAppInfo'] as Map).isNotEmpty) {
    } else {
      channel.trackEvent('click', taskType == PrintTaskType.miniApp ? '055_133' : '024_067_094',
          eventData: {'check_version': uuid, 'paperSerial': paperSerial, 'ribbonSerial': ribbonSerial});
    }
    if (parameter?.customData != null &&
        parameter!.customData!["uniAppInfo"] != null &&
        parameter!.customData!["uniAppInfo"]["uniAppId"] != null) {
      channel.labelRecord(parameter!.templateMoudle!, parameter!.customData!["uniAppInfo"]["uniAppId"]);
    }
    // if (isSave && channel.isLogin() || !isSave || !channel.isConnected()) {
    checkBeforePrint.check(this).then((pass) {
      if (isSave) {
        bool isExcel = parameter?.templateMoudleNew?.isExcel() ?? false;
        bool isCommodity = parameter?.templateMoudleNew?.isCommodity() ?? false;
        if (!channel.isLogin() && (isExcel || isCommodity)) {
          channel.showStageDialog(pageManager.context, false, () {
            update([UpdateFieldManager.printButton]);
            style.isSavePrinting = true;
            PrintManager().readyToPrint(this, uuid);
            channel.toSave(parameter!.templateMoudleNew);
          });
          return;
        } else {
          style.isSavePrinting = true;
          channel.toSave(parameter!.templateMoudleNew);
        }
      }

      if (!pass) {
        // 更新UI以反映正在打印的状态
        printingStatusReset();
      } else {
        style.isPrinting = isSave?false:true;
        update([UpdateFieldManager.printButton]);
        PrintManager().readyToPrint(this, uuid);
      }
    });
    // }

    // if (isSave) {
    //   // style.isSavePrinting = (channel.isLogin() && channel.isConnected());
    //   // update([UpdateFieldManager.printButton]);
    //   ///添加延时防止红屏
    //   // channel.toSave(parameter!.templateMoudleToJson());
    //   if (channel.isLogin()) {
    //     channel.toSave(parameter!.templateMoudleNew);
    //   } else {}

    //   // channel.toSave(parameter!.templateMoudleToJson()).then((value) {
    //   //   if (!channel.isLogin() && (value ?? "").isNotEmpty) {
    //   //     style.isPrinting = true;
    //   //     checkBeforePrint.check(this).then((pass) {
    //   //       if (pass) {
    //   //         // 更新UI以反映正在打印的状态
    //   //         update([UpdateFieldManager.printButton]);
    //   //         PrintManager().readyToPrint(this, uuid);
    //   //       } else {
    //   //         style.isPrinting = false;
    //   //         update([UpdateFieldManager.printButton]);
    //   //       }
    //   //     });
    //   //   }
    //   // });
    // }
  }

  printingStatusReset() {
    style.isPrinting = false;
    style.isSavePrinting = false;
    update([UpdateFieldManager.printButton]);
  }

  openConnectPage() {
    if (Platform.isIOS) {
      // FocusScopeNode currentFocus = FocusScope.of(pageManager.context);
      // if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
      // }
    }
    var uniappId = parameter?.customData?['uniAppInfo']?["uniAppId"] ?? printData.uniappId;
    channel.openConnectPage(fromUnimp: uniappId != "", uniappId: uniappId);
  }

  /// 页数展示
  showBatchCopies(BuildContext context) {
    List<Color?>? contentColors = getPrintColor();
    SharedPreferences.getInstance().then((sp) {
      int crossCount = sp.getInt("batch_cross_axis_count") ?? 2;
      showModalBottomSheet(
          barrierColor: const Color.fromARGB(90, 0, 0, 0),
          isScrollControlled: true,
          context: context,
          backgroundColor: Colors.transparent,
          enableDrag: true,
          builder: (_) {
            return AnimatedPadding(
                padding: EdgeInsets.fromLTRB(0, 40, 0, 0),
                duration: const Duration(milliseconds: 100),
                child: PrintSettingPreview(this, [parameter!.templateMoudleNew!], context, contentColors, crossCount,
                    taskType == PrintTaskType.batch, parameter!.batchtIds, style.pageIndex, (pageIndex) {
                  style.pageIndex = pageIndex;
                  // refreshTemplate(changPageIndex: true);
                  Map<String, dynamic> refreshTemplatePreviewInfo = {"action": "refreshTemplatePreview"};
                  streamController.add(refreshTemplatePreviewInfo);
                  streamController.add({"action": "replaceLabelEvent"});
                }));
          });
    });
  }

  List<Color?>? getPrintColor() {
    Map<String, dynamic> colorInfo = channel.getPaperRibbonColor();
    String paperColor = colorInfo["paperColor"] ?? "";
    String ribbonColor = colorInfo["ribbonColor"] ?? "";
    List<int> rgb = [];
    if (ribbonColor.isNotEmpty && ribbonColor.split(",").length == 2) {
      List<Color?> ribbonDoubleColor = [];
      ribbonColor.split(",").forEach((action) {
        if (action.isNotEmpty && action.split(".").length == 3) {
          List<int> ribbonRgb = action.split(".").map((element) {
            return int.parse(element);
          }).toList();
          ribbonDoubleColor.add(Color.fromARGB(255, ribbonRgb[0], ribbonRgb[1], ribbonRgb[2]));
        }
      });
      return ribbonDoubleColor;
    } else {
      if (ribbonColor.isNotEmpty && ribbonColor.split(".").length == 3) {
        rgb = ribbonColor.split(".").map((element) {
          return int.parse(element);
        }).toList();
      } else if (paperColor.isNotEmpty && paperColor.split(".").length == 3) {
        rgb = paperColor.split(".").map((element) {
          return int.parse(element);
        }).toList();
        if (paperColor == '159.160.160') {
          rgb = [0, 0, 0];
        }
      }
      if (rgb.isNotEmpty) {
        return [Color.fromARGB(255, rgb[0], rgb[1], rgb[2])];
      }
    }
    return null;
  }

  bool isConnected() {
    return channel.isConnected();
  }

  bool isAllTextElement() {
    TemplateData? templateData = parameter?.templateMoudleNew;
    if (templateData == null) {
      return false;
    }
    if (templateData.isExcelBindingTemplate() || templateData.isGoodsLabelTemplate()) {
      return false;
    }
    return templateData.elements.every((element) => element.type == NetalElementType.text);
  }

  bool isExcelTemplate() {
    TemplateData? templateData = parameter?.templateMoudleNew;
    if (templateData == null) {
      return false;
    }
    if (templateData.dataSources != null && templateData.dataSources!.isNotEmpty) {
      return templateData.dataSources![0].type == TemplateDataSourceType.excel;
    }
    return false;
  }

  bool isGoodTemplate() {
    TemplateData? templateData = parameter?.templateMoudleNew;
    if (templateData == null) {
      return false;
    }
    if (templateData.dataSources != null && templateData.dataSources!.isNotEmpty) {
      return templateData.dataSources![0].type == TemplateDataSourceType.commodity;
    }
    return false;
  }

  /// 更新rfid模板
  updateRfidTemplate(BuildContext context, Map<String, dynamic> data, {bool checkRfid = false}) async {
    try {
      // 小程序和打印无UI但是显示进度条不走替换弹窗的逻辑
      if (taskType == PrintTaskType.miniApp || taskType == PrintTaskType.printNullUiShowProgress) {
        return;
      }
      if (isShowingRfidReplaceDialog) {
        return;
      }
      if (data.isNotEmpty &&
          taskType != PrintTaskType.batch &&
          taskType != PrintTaskType.miniApp &&
          taskType != PrintTaskType.printNullUi) {
        Map<String, dynamic> standDataMap = await channel.transformTemplateToStand(data);
        TemplateData targetTemplate = await TemplateParse.parseFromMap(standDataMap, parseLocalImageResources: (url) {
          if (standDataMap!['localBackground'] != null) {
            int index = standDataMap!['multipleBackIndex'];
            List localBackground = standDataMap!['localBackground'];
            return Future.value(localBackground[index]);
          } else {
            return Future.value('');
          }
        });
        TemplateData? sourceTemplate = parameter?.templateMoudleNew;
        if (sourceTemplate == null) {
          return;
        }
        if (checkRfid && !sourceTemplate.isMatchRFID(targetTemplate.profile.barcode ?? "")) {
          if (channel.getLabelReplaceFlag()) {
            TemplateData tempTemplate = await channel.getRfidReplaceTemplate(targetTemplate, sourceTemplate);
            TemplateData mergedTemplate = mergeTemplate(targetTemplate, sourceTemplate).copyWith(
              elements: tempTemplate.elements,
              width: tempTemplate.width,
              height: tempTemplate.height,
              cableDirection: CopyWrapper.value(NetalCableDirection.byValue(tempTemplate.cableDirection!.value)),
              rotate: tempTemplate.rotate,
              canvasRotate: tempTemplate.canvasRotate,
            );
            // parameter?.templateMoudleNew = mergedTemplate;
            parameter?.updateTemplateModule(mergedTemplate);
            Future.delayed(const Duration(milliseconds: 1000), () {
              refreshTemplate(justReplaceLabel: true);
            });
            String msg = "${getI18nString("app01463", "检测到标签纸")}${targetTemplate.name}";
            IconToast.show(context, msg);
            channel.syncPrintSettingRfidReplace();
          } else {
            if (isShowingRfidReplaceDialog) {
              return;
            }
            isShowingRfidReplaceDialog = true;
            Future.delayed(const Duration(seconds: 1), () {
              channel.setFlutterVCCanSideslip(false);
            });

            await showDialog(
                context: context,
                barrierDismissible: false,
                useRootNavigator: false,
                barrierColor: KColor.mainTitle.withOpacity(0.35),
                builder: (BuildContext context) {
                  return LabelReplaceWidget(
                      logic: this,
                      templateData: targetTemplate,
                      callback: (isConfirm, checked) async {
                        isShowingRfidReplaceDialog = false;
                        debugPrint("=======RFID替换弹窗, 选择结果-->isConfirm: $isConfirm, checked: $checked");
                        if (isConfirm) {
                          if (checked) {
                            channel.postEvent({"action": "syncRfidReplaceTag", 'rfidReplaceTag': checked});
                          }
                          //elements = templateData.elements
                          //         ..canvasElements = templateData.canvasElements
                          //         ..elementIds = templateData.elementIds
                          //         ..width = templateData.width
                          //         ..height = templateData.height
                          //         ..cableDirection = cableDirectionTemp
                          //         ..rotate = rotate
                          //         ..canvasRotate = canvasRotate;
                          TemplateData tempTemplate =
                              await channel.getRfidReplaceTemplate(targetTemplate, sourceTemplate);
                          TemplateData mergedTemplate = mergeTemplate(targetTemplate, sourceTemplate).copyWith(
                            elements: tempTemplate.elements,
                            width: tempTemplate.width,
                            height: tempTemplate.height,
                            cableDirection:
                                CopyWrapper.value(NetalCableDirection.byValue(tempTemplate.cableDirection!.value)),
                            rotate: tempTemplate.rotate,
                            canvasRotate: tempTemplate.canvasRotate,
                          );
                          // parameter?.templateMoudleNew = mergedTemplate;
                          parameter?.updateTemplateModule(mergedTemplate);
                          //替换后重置ratio
                          String widthValue = mergedTemplate.width.toString();
                          String hightValue = mergedTemplate.height.toString();
                          try {
                            DisplayUtil.init(context);
                            DisplayUtil.generateDesignRatio(double.parse(widthValue), double.parse(hightValue));
                          } catch (e, s) {
                            debugPrint('异常信息:\n $e');
                            debugPrint('调用栈信息:\n $s');
                          }
                          Future.delayed(const Duration(milliseconds: 1000), () {
                            refreshTemplate(justReplaceLabel: true);
                          });
                          channel.syncPrintSettingRfidReplace();
                        }
                      });
                }).then((value) {
              channel.setFlutterVCCanSideslip(true);
            });
          }
        }
      }
    } catch (e, s) {
      debugPrint('异常信息:\n $e $s');
    }
  }

  saveDeviceOffset(double offsetY, double offsetX) {
    NiimbotPrinter? connectedPrint = NiimbotPrintSDK().store.connectedPrinter;
    if (connectedPrint == null) return;
    String deviceName = channel.getCurrentPrinterMachineId(
        (connectedPrint.code ?? 0).toString(), connectedPrint.name ?? "", connectedPrint.sn ?? "");
    SharedPreferences.getInstance().then((sp) {
      sp.setString("print_offset_$deviceName", jsonEncode({"offsetY": offsetY, "offsetX": offsetX}));
    });
  }

  Future<Map<String, dynamic>> getDeviceOffset() async {
    double offsetY = 0;
    double offsetX = 0;
    NiimbotPrinter? connectedPrint = NiimbotPrintSDK().store.connectedPrinter;
    if (connectedPrint == null) return {"offsetY": offsetY, "offsetX": offsetX};
    String deviceName = channel.getCurrentPrinterMachineId(
        (connectedPrint.code ?? 0).toString(), connectedPrint.name ?? "", connectedPrint.sn ?? "");
    SharedPreferences sp = await SharedPreferences.getInstance();
    String offsetStr = sp.getString("print_offset_$deviceName") ?? "";
    Map<String, dynamic> offsetInfo =
        offsetStr.isEmpty ? {"offsetY": offsetY, "offsetX": offsetX} : jsonDecode(offsetStr);
    return Future.value(offsetInfo);
  }

  /// 替换模板
  TemplateData mergeTemplate(TemplateData target, TemplateData origin) {
    Map<String, String> usedFonts;
    String? id;
    String? originTemplateId;
    NetalCableDirection? cableDirection = NetalCableDirection.byValue((target.cableDirection!.value));
    num canvasRotate = 0;
    num targetWidth = target.width;
    num targetHeight = target.height;
    num targetRotate = target.rotate;
    // target是标签纸所以canvasRotate为0，防止从原生映射时导致的脏数据
    target.profile.extra.createTime = origin.profile.extra.createTime;
    target.profile.extra.userId = origin.profile.extra.userId;
    Map<String, String> mergeUseFont = origin.usedFonts ?? {};
    target.usedFonts?.forEach((key, value) {
      if (mergeUseFont[key] == null) {
        mergeUseFont[key] = value;
      }
    });
    usedFonts = mergeUseFont;
    // target.totalPage = origin.totalPage;
    id = origin.id;
    originTemplateId = origin.originTemplateId;
    target.profile.extra.templateType = origin.profile.extra.templateType;
    target.profile.extra.folderId = origin.profile.extra.folderId;
    // 模版的宽高比(翻转后的)vs标签纸的宽高比
    if (origin.height != null && target.height != null && origin.width != null && target.width != null) {
      double templateAspectRatio = origin.width! / origin.height!;
      double labelAspectRatio = target.width! / target.height!;
      if ((templateAspectRatio > 1 && labelAspectRatio < 1) || (templateAspectRatio < 1 && labelAspectRatio > 1)) {
        canvasRotate = target.canvasRotate + 90;
      }
    }

    if (canvasRotate != 0) {
      num? width = target.width;
      targetWidth = target.height;
      targetHeight = width;
      num rotate = target.rotate + canvasRotate / 90 * 270;
      targetRotate = (rotate % 360).toInt();
      // 线缆尾巴方向调整，一旦旋转，按照目前逻辑是右边翻转90度，所以+1
      if (target.cableDirection != null) {
        cableDirection = NetalCableDirection.byValue((target.cableDirection!.value + 1) % 4);
      }
    }
    TemplateData mergedTemplate = target.copyWith(
        totalPage: origin.totalPage,
        canvasRotate: canvasRotate,
        elements: origin.elements,
        usedFonts: usedFonts,
        dataSources: CopyWrapper.value(origin.dataSources),
        dataSourceModifies: CopyWrapper.value(origin.dataSourceModifies),
        dataSourceBindInfo: CopyWrapper.value(origin.dataSourceBindInfo),
        currentPageIndex: origin.currentPageIndex,
        id: id,
        originTemplateId: originTemplateId,
        name: origin.name,
        cloudTemplateId: CopyWrapper.value(origin.cloudTemplateId),
        width: targetWidth,
        height: targetHeight,
        rotate: targetRotate,
        cableDirection: CopyWrapper.value(cableDirection),
        localThumbnail: "",
        hasVipRes: origin.hasVipRes,
        vip: origin.vip);

    //更新旧数据，用于原生打印,同步标签纸信息
    Map<String, dynamic>? oldMap = parameter?.templateMoudle;
    Map<String, dynamic> newMap = mergedTemplate.toJson();
    if (oldMap != null) {
      parameter?.templateMoudle!["labelNames"] = newMap["labelNames"];
      parameter?.templateMoudle!["thumbnail"] = newMap["thumbnail"];
      parameter?.templateMoudle!["backgroundImage"] = newMap["backgroundImage"];
      parameter?.templateMoudle!["width"] = newMap["width"];
      parameter?.templateMoudle!["height"] = newMap["height"];
      parameter?.templateMoudle!["rotate"] = newMap["rotate"];
      parameter?.templateMoudle!["canvasRotate"] = newMap["canvasRotate"];
      parameter?.templateMoudle!["backgroundImage"] = newMap["backgroundImage"];

      parameter?.templateMoudle!["consumableType"] = newMap["consumableType"];
      parameter?.templateMoudle!["paperType"] = newMap["paperType"];
      parameter?.templateMoudle!["isCable"] = newMap["isCable"];
      parameter?.templateMoudle!["cableLength"] = newMap["cableLength"];
      parameter?.templateMoudle!["cableDirection"] = newMap["cableDirection"];
      parameter?.templateMoudle!["margin"] = newMap["margin"];
      parameter?.templateMoudle!["profile"] = newMap["profile"];
      parameter?.templateMoudle!["paperColor"] = newMap["paperColor"];
      parameter?.templateMoudle!["localBackground"] = newMap["localBackground"];
    }

    return mergedTemplate;
  }

  ///当前场景是否支持16色灰阶
  bool checkCurrentIsSupportGray16(TemplateData data) {
    final elements = data.elements;
    // 是否包含灰阶图片
    // 2025/3/17 Ice_Liu 兼容处理，因16色灰阶文字未合入，若纸+机器支持16阶，预览接口颜色默认传null
    var hasGray16 = true;
    // for (final e in elements) {
    //   if (e is ImageElement) {
    //     if (e.imageProcessingType == NetalImageRenderType.gradient) {
    //       hasGray16 = true;
    //     }
    //   }
    // }
    if (hasGray16 && isDeviceSupportGray16()) {
      return true; //16色灰阶
    } else {
      return false;
    }
  }

  ///耗材及打印机是否支持当前颜色模式
  bool isDeviceSupportGray16() {
    Map<String, dynamic> colorInfo = channel.getPaperRibbonColor();
    String gray16ColorValue = "159.160.160";
    NiimbotPrinter? connectedPrinter = NiimbotPrintSDK().store.connectedPrinter;
    if (connectedPrinter == null) return false;
    String paperColor = colorInfo["paperColor"] ?? "";
    String ribbonColor = colorInfo["ribbonColor"] ?? "";
    return (paperColor == gray16ColorValue || ribbonColor == gray16ColorValue) &&
        (connectedPrinter.colorModeSupport?.contains(NiimbotPrintColorMode.gray16) ?? false);
  }
}

extension PageStyle on PrintSettingLogic {
  PrintPageStyle get style {
    return pageManager.style;
  }
}

extension getLanguage on PrintSettingLogic {
  String getI18nString(String stringCode, String defaultStr, {List<String>? param}) {
    return pageManager.getI18nString!(stringCode, defaultStr, param: param);
  }
}
