import 'dart:io';

import 'package:niimbot_template/models/template_data.dart';
import 'package:text/application.dart';
import 'package:text/database/isar_db_manager.dart';
import 'package:text/pages/my_template/abstract/my_template_database_abstract.dart';
import 'package:text/pages/my_template/my_template_state.dart';
import 'package:text/template/util/template_detail_db_utils.dart';
import 'package:text/template/util/template_misc_utils.dart';
import 'package:text/utils/common_fun.dart';
import 'package:isar/isar.dart';

import '../../../database/template/template_detail_model.dart';


class TemplateDataBaseV2 extends MyTemplateDataBaseAbstract {
  @override
  Future add(List<dynamic> dbModels) async {
    await TemplateDetailDBUtils.batchInsertTemplate(dbModels as List<TemplateData>);
  }

  Future addTemplate(List<TemplateData> dbModels) async {
    await TemplateDetailDBUtils.batchInsertTemplate(dbModels);
  }

  @override
  Future delete({String? id}) async {
    if(id != null && id.isNotEmpty){
      await TemplateDetailDBUtils.deleteTemplateById(id);
    }
  }

  deleteTemplatesInUpdate(List<TemplateData> value, int folderId, List<num> deleteData) async {
    List<TemplateData> templatesList = [];
    templatesList.addAll(value);
    await TemplateDetailDBUtils.batchDeleteTemplateByIds(deleteData);

    await Future.forEach<TemplateData>(value, (element) async {
      var data = await TemplateDetailDBUtils.queryTemplateById(element.id!);
      if (data != null) {
        if ((element.profile.extra.updateTime != data.profile.extra.updateTime ||
                element.profile.extra.folderId != data.profile.extra.folderId) &&
            element.local_type != 2 &&
            element.local_type != 1) {
          await TemplateDetailDBUtils.deleteTemplateById(element.id!);
        } else {
          templatesList.remove(element);
        }
      }
    });
    return templatesList;
  }

  // 批量删除某个文件夹下的所有模板
  Future<void> deleteTemplatesByFolderId(int folderId) async {
    await TemplateDetailDBUtils.deleteTemplatesByFolderId(folderId);
  }

  @override
  Future query({String? id, String? searchKey,int pageIndex = 1, TemplateOperateState state = TemplateOperateState.normal}) async {
    final isar = DBManagerUtil.instance.isar;
    QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel, QAfterFilterCondition> queryCondition ;
    if (state == TemplateOperateState.shareMe) {
      queryCondition = isar.niimBotTemplateCanvasDataModels.filter()
          // .group((q) => q
          //   .templateTypeEqualTo('0')
          //   .or()
          //   .templateTypeEqualTo('2'))
          .not().templateTypeEqualTo("1")
          .and()
          .not().localTypeEqualTo(3);
    }else{
      queryCondition = isar.niimBotTemplateCanvasDataModels.filter()
          // .group((q) => q
          //   .templateTypeEqualTo("0")
          //   .or()
          //   .templateTypeEqualTo("2"))
          .not().templateTypeEqualTo("1")
          .userIdEqualTo(Application.user == null ? "0" : TemplateMiscUtils.getUserId())
          .and()
          .not().localTypeEqualTo(3);
    }
    var folderId = id;
    if (folderId != null && folderId.isNotEmpty) {
      queryCondition = queryCondition.and().folderIdEqualTo(folderId);
    }
    if (searchKey != null && searchKey.isNotEmpty) {
      queryCondition = queryCondition.and().nameContains(searchKey);
    }
    List<TemplateData> templates = await TemplateDetailDBUtils.customQueryTemplates(queryCondition,pageIndex: pageIndex,needParseElements: false);

    var recordList = [];
    templates.forEach((element) {
      recordList.add({"id": element?.id, "thumbnail": element?.thumbnail, "name": element?.name});
    });
    writeLogToFile({"moudle": "myTemplate", "event": "template_lose_native", "data": recordList});
    return templates;
  }


  Future<List<TemplateData>> dbQueryLotBatchTemplateDataWhere(
      {int? batchSize}) async {
    final isar = DBManagerUtil.instance.isar;
    QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel, QAfterFilterCondition> queryCondition ;
    queryCondition =isar.niimBotTemplateCanvasDataModels.filter().userIdEqualTo("").localTypeEqualTo(1);
    List<TemplateData> templates = await TemplateDetailDBUtils.customQueryTemplates(queryCondition,pageSize: batchSize ?? 20);
    return templates;
  }

  Future<List<TemplateData>> dbSearchTemplates(
      {int? batchSize}) async {
    final isar = DBManagerUtil.instance.isar;
    String userId = TemplateMiscUtils.getUserId();
    QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel, QAfterFilterCondition> queryCondition ;
    queryCondition =isar.niimBotTemplateCanvasDataModels.filter()
    .not().templateTypeEqualTo("1")
    .not().localTypeEqualTo(3)
    .and()
    .userIdEqualTo(userId);
    List<TemplateData> templates = await TemplateDetailDBUtils.customQueryTemplates(queryCondition,pageSize: batchSize ?? 20,needParseElements: false);
    return templates;
  }

  Future<List<TemplateData>> dbQueryOffUnLoginTemplates(
      {int? batchSize}) async {
    final isar = DBManagerUtil.instance.isar;
    QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel, QAfterFilterCondition> queryCondition ;
    queryCondition =isar.niimBotTemplateCanvasDataModels.filter().userIdEqualTo("").localTypeEqualTo(1);
    List<TemplateData> templates = await TemplateDetailDBUtils.customQueryTemplates(queryCondition,pageSize: batchSize ?? 20,needParseElements: false);
    return templates;
  }




  @override
  Future update(List<dynamic> dbModels) async {
    await TemplateDetailDBUtils.batchUpdateTemplate(dbModels as List<TemplateData>);
  }

  Future updateTemplate(List<TemplateData> dbModels) async {
    await TemplateDetailDBUtils.batchUpdateTemplate(dbModels);
  }

  Future batchDelete(List<dynamic> dbModels) async {
    await TemplateDetailDBUtils.batchDeleteTemplate(dbModels as List<TemplateData>);
  }

  Future batchUpdate(List<dynamic> dbModels) async {
    await TemplateDetailDBUtils.batchUpdateTemplate(dbModels as List<TemplateData>);
  }
}
