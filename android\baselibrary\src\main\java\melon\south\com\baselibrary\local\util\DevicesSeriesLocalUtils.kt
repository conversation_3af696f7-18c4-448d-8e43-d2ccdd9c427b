package melon.south.com.baselibrary.local.util

import JCApiManager
import WifiConnectionProxyManager
import com.gengcon.print.draw.module.print.DeviceSerialInfo
import com.niimbot.appframework_library.listener.JCTResponseListener
import com.niimbot.appframework_library.utils.NetworkUtils.isConnected
import com.niimbot.bluetooth.BluetoothUtil
import com.niimbot.utiliylibray.util.AssetsUtil
import com.niimbot.utiliylibray.util.PreferencesUtils
import com.niimbot.utiliylibray.util.PreferencesUtils.put
import com.qyx.languagelibrary.utils.TextHookUtil
import com.southcity.watermelon.util.SuperUtils
import com.southcity.watermelon.util.any2Json
import com.southcity.watermelon.util.json2Array
import melon.south.com.baselibrary.moduleEx.DeviceSeriesModuleR
import melon.south.com.baselibrary.util.ResConstant

/**
 * 机型本地工具类
 */
object DevicesSeriesLocalUtils {

    var devicesSeriesMap: MutableMap<String, DeviceSerialInfo> = hashMapOf()

    private var guideListCache: List<DeviceSerialInfo>? = null
    private fun checkCache(listener: (List<DeviceSerialInfo>) -> Unit, netCallback: () -> Unit) {
        if (!guideListCache.isNullOrEmpty()) {
            listener.invoke(guideListCache!!)
        } else {
            netCallback.invoke()
        }
    }

    fun clearCache() {
        guideListCache = null
    }

    fun getModule(
        isNeedRequestHttp: Boolean = isConnected(),
        listener: (List<DeviceSerialInfo>) -> Unit
    ) {
        checkCache(listener) {
            if (isNeedRequestHttp) {
                getHttpModule(listener)
            } else {
                getLocalModule(listener)
            }
        }
    }

    /**
     * 存放设备机型
     */
    fun setModule(devicesModules: List<DeviceSerialInfo?>) {
        put("series_module_${TextHookUtil.getInstance().languageName}", any2Json(devicesModules))
        setModuleMap(devicesModules)
    }

    private fun setModuleMap(devicesModules: List<DeviceSerialInfo?>) {
        devicesSeriesMap.clear()
        for (it in devicesModules) {
            it?.machine_name?.split(",")?.forEach { deviceSeries ->
                if (!deviceSeries.isNullOrBlank()) {
                    devicesSeriesMap[deviceSeries] = it
                }
            }
        }
    }

    /**
     * 从服务器获取之后存入本地
     */
    private fun getHttpModule(listener: (listener: List<DeviceSerialInfo>) -> Unit) {
        JCApiManager.getGuidePage(object : JCTResponseListener<List<DeviceSeriesModuleR>> {
            override fun onSuccess(body: List<DeviceSeriesModuleR>) {
                if (null != body && body.isNotEmpty()) {
                    val list = DeviceSeriesModuleR.convert(body)
                    setModule(list)
                    listener.invoke(list)
                    guideListCache = list
                    ResConstant.updateResMap(list)
                } else {
                    getLocalModule(listener)
                }
            }

            override fun onError(message: String) {
//                ToastInstance.INSTANCE.showToastLimit(message)
                getLocalModule(listener)
            }
        })
    }

    /**
     * 直接从本地数据
     */
    fun getLocalModule(listener: (List<DeviceSerialInfo>) -> Unit) {
        var result = PreferencesUtils.getString(
            "series_module_${TextHookUtil.getInstance().languageName}",
            ""
        )
        if (result.isNullOrEmpty()) {
            result = getSeriesModuleJson()
        }
        val list = json2Array(result, DeviceSerialInfo::class.java)
        listener.invoke(list)
        ResConstant.updateResMap(list)
    }

    fun getLocalModule(): List<DeviceSerialInfo> {
        var result = PreferencesUtils.getString(
            "series_module_${TextHookUtil.getInstance().languageName}",
            ""
        )
        if (result.isNullOrEmpty()) {
            result = getSeriesModuleJson()
        }
        return json2Array(result, DeviceSerialInfo::class.java)
    }

    fun getLocalModuleMap(listener: (MutableMap<String, DeviceSerialInfo>) -> Unit) {
        if (!devicesSeriesMap.isNullOrEmpty()) {
            listener.invoke(devicesSeriesMap)
        } else {
            getLocalModule {
                setModuleMap(it)
                listener.invoke(devicesSeriesMap)
            }
        }
    }

    fun getCurrentSerialInfo(deviceName: String) =
        devicesSeriesMap[BluetoothUtil.getDeviceType(deviceName)]

    fun getCurrentSerialInfoBySerialName(seriesName: String) = devicesSeriesMap[seriesName]

    fun getDeviceSeriesUrl(seriesName: String) = devicesSeriesMap[seriesName]?.guide_image

    fun setCurrentSeriesModuleByLanguage(listener: ((Boolean) -> Unit) = {}) {
        val data = PreferencesUtils.getAny<DeviceSerialInfo>("device_series")
        getModule { list ->
            if (list.isNullOrEmpty()) {
                listener.invoke(true)
                return@getModule
            }
            if (null == data || data.hardware_series_id == "0") {
                WifiConnectionProxyManager.setCurrentDeviceSeriesInfo(any2Json(list[0]))
                listener.invoke(true)
                return@getModule
            }
            list.forEach { module ->
                if (data.hardware_series_id == module.hardware_series_id) {
//                    AppDataUtil.deviceSeries = module
                    WifiConnectionProxyManager.setCurrentDeviceSeriesInfo(any2Json(module))
                    listener.invoke(false)
                    return@getModule
                }
            }
            //如果没有匹配到，则设置为list中的第一个元素
            // AppDataUtil.deviceSeries = list[0]
            WifiConnectionProxyManager.setCurrentDeviceSeriesInfo(any2Json(list[0]))
            listener.invoke(true)
        }
    }

    private fun getSeriesModuleJson(): String {
        return if (TextHookUtil.getInstance().isChina())
            AssetsUtil.getJson("SeriesModule_zh_online.json", SuperUtils.superContext)
        else AssetsUtil.getJson("SeriesModule_en_online.json", SuperUtils.superContext)
    }


}
