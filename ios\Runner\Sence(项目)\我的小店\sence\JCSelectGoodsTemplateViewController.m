//
//  JCSelectGoodsTemplateViewController.m
//  Runner
//
//  Created by huzi_0118 on 2021/9/25.
//  Copyright © 2021 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCSelectGoodsTemplateViewController.h"
#import "JCStoreGoodsLibearyViewController.h"
#import "JCTMDataBindGoodsInfoManager.h"
#import "JCSelectGoodsTemplateView.h"
#import "JCGoodTemplateTypeMode.h"
#import "JCContentTableViewCell.h"
#import "JCSegmentCollectionCell.h"
#import "JCTemplateImageManager.h"
#import "FlutterBoostUtility.h"
#import "JCPreviewInsetView.h"
#import "JCGoodDetailInfo.h"
#import "JCTemplateList.h"
#import "JCFontManager.h"
#import "YLButton.h"
#import "JCGrayManager.h"


@interface JCSelectGoodsTemplateViewController ()<UITableViewDelegate,UITableViewDataSource,UICollectionViewDelegate,UICollectionViewDataSource>
@property(nonatomic,strong) UIButton *printStateBtn;
@property(nonatomic,strong) JCSelectGoodsTemplateView *mainView;
@property(nonatomic,strong) UITableView *templateListView;
@property(nonatomic,strong) NSArray *arrTypeDecrList;
@property(nonatomic,strong) MBProgressHUD *progressHUD;
@property(nonatomic,strong) JCGoodDetailInfo *firstGoodInfoModel;
@property(nonatomic,strong) JCGoodTemplateTypeMode *selectTypeModel;
@property(nonatomic,strong) NSString *searchKeywords;
@property(nonatomic,strong) UIButton *navRightBtn;
@property(nonatomic,strong) NSString *selectTemplateId;
@property(nonatomic,assign) BOOL isSearchType;
@property(nonatomic,strong) NSString *currentType;
@property(nonatomic,strong) NSArray *goodsArr;
@end

@implementation JCSelectGoodsTemplateViewController

- (instancetype)initWithGoodsArr:(NSArray *)goodsArr{
    self = [super init];
    if(self){
        self.searchKeywords = @"";
        self.goodsArr = goodsArr;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(templatechange:) name:TEMPLATE_CHANGED object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(synchTemplateFinish:) name:JCNOTICATION_SYNC_FINISH object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(printerStatusNotification:) name:PrinterStatusNotification object:nil];
    // Do any additional setup after loading the view.
}

- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
}

- (void)initUI{
    XYWeakSelf
    self.title = XY_LANGUAGE_TITLE_NAMED(@"", @"选择商品模板");
    JCSelectGoodsTemplateView *mainView = [[JCSelectGoodsTemplateView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT - (iPhoneX?88:64)) searchType:NO];
    mainView.templateTableView.delegate = self;
    mainView.templateTableView.dataSource = self;
    mainView.segmentCollectionView.delegate = self;
    mainView.segmentCollectionView.dataSource = self;
    mainView.templateTableView.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;
    mainView.segmentCollectionView.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;
    mainView.templateTableView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
        [weakSelf loadGoodsTemplate:NO templateTypeMode:weakSelf.selectTypeModel];
    }];
    mainView.templateTableView.mj_footer = [MJRefreshAutoNormalFooter footerWithRefreshingBlock:^{
        [weakSelf loadMoreGoodsTemplateWithTypeMode:weakSelf.selectTypeModel];
    }];
    mainView.templateTableView.mj_footer.hidden = YES;
    [mainView setCancelBlock:^{
        [weakSelf.view endEditing:YES];
        [weakSelf refreshMainViewSearchState:NO];
        weakSelf.isSearchType = NO;
        weakSelf.searchKeywords = @"";
        for (JCGoodTemplateTypeMode *typeModel in self.arrTypeDecrList) {
            if(typeModel.goodTemplateId.integerValue == weakSelf.currentType.integerValue){
                typeModel.isSelected = @"1";
                typeModel.templateArr = @[].mutableArray;
                self.selectTypeModel = typeModel;
            }else{
                typeModel.isSelected = @"0";
            }
        }
        [weakSelf getTemplateList:NO templateTypeMode:self.selectTypeModel];
        [weakSelf.templateListView reloadData];
    }];
    [mainView setSearchBlock:^(NSString *searchKeywords) {
        weakSelf.searchKeywords = searchKeywords;
        [weakSelf loadGoodsTemplate:YES templateTypeMode:weakSelf.selectTypeModel];
    }];
    [mainView setToSearchBlock:^{
        weakSelf.currentType = weakSelf.selectTypeModel.goodTemplateId;
        for (JCGoodTemplateTypeMode *typeModel in self.arrTypeDecrList) {
            if(typeModel.goodTemplateId.integerValue == 1){
                typeModel.isSelected = @"1";
//                typeModel.templateArr = @[].mutableArray;
                self.selectTypeModel = typeModel;
            }else{
                typeModel.isSelected = @"0";
            }
        }
        [weakSelf refreshMainViewSearchState:YES];
        weakSelf.isSearchType = YES;
        [weakSelf.templateListView reloadData];
    }];
    [mainView setAddTemplateBlock:^{
        [weakSelf toCreateNewGoodsTemplate];
    }];
    [self.view addSubview:mainView];
    self.templateListView = mainView.templateTableView;
    self.mainView = mainView;
}

- (void)refreshMainViewSearchState:(BOOL)isSearchType{
    self.mainView.isSearchType = isSearchType;
}

- (void)initNavigationBar{
    [super initNavigationBar];
    UIButton *navRightBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    navRightBtn.frame = CGRectMake(0, 0, 80, 44);
    [navRightBtn setTitle:XY_LANGUAGE_TITLE_NAMED(@"", @"下一步") forState:UIControlStateNormal];
    [navRightBtn setTitleColor:HEX_RGBA(0xFB4B42, 0.3) forState:UIControlStateNormal];
    [navRightBtn setTitleColor:HEX_RGBA(0xFB4B42, 1) forState:UIControlStateSelected];
    navRightBtn.titleLabel.font = MY_FONT_Bold(16);
    [self showBarButton:NAV_RIGHT button:navRightBtn];
    self.navRightBtn = navRightBtn;
    
    UIButton *leftBtn1 = [UIButton buttonWithType:UIButtonTypeCustom];
    leftBtn1.frame = CGRectMake(0, 0, 30, 44);
    [leftBtn1 setImage:XY_IMAGE_NAMED(@"new_nav_back") forState:UIControlStateNormal];
    [leftBtn1 addTarget:self action:@selector(leftButtonTouch:) forControlEvents:UIControlEventTouchUpInside];
    UIBarButtonItem *leftBtnItem1 = [[UIBarButtonItem alloc] initWithCustomView:leftBtn1];
    
    UIButton *leftBtn2 = [UIButton buttonWithType:UIButtonTypeCustom];
    leftBtn2.frame = CGRectMake(0, 0, 30, 44);
    [leftBtn2 addTarget:self action:@selector(toBluetoothManagerVC) forControlEvents:UIControlEventTouchUpInside];
    if(JC_IS_CONNECTED_PRINTER){
        [leftBtn2 setImage:XY_IMAGE_NAMED(@"goodsPrint_connected") forState:UIControlStateNormal];
    }else{
        [leftBtn2 setImage:XY_IMAGE_NAMED(@"goodsPrint_unconnect") forState:UIControlStateNormal];
    }
    UIBarButtonItem *leftBtnItem2 = [[UIBarButtonItem alloc] initWithCustomView:leftBtn2];
    self.navigationItem.leftBarButtonItems = @[leftBtnItem1,leftBtnItem2];
    self.printStateBtn = leftBtn2;
}

- (void)leftButtonTouch:(UIButton *)sender{
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)toBluetoothManagerVC{
    JC_TrackWithparms(@"click",@"017_051_065",(@{}));
    [[JCBluetoothManager sharedInstance] startBluetoothConnectFrom:(XYNavigationController *)self.navigationController isFromHome:NO];
}

- (void)rightButtonTouch:(UIButton *)sender{
    JCTemplateData *selectModel = nil;
    for(JCTemplateData *model in self.selectTypeModel.templateArr){
        if([self.selectTemplateId isEqualToString:model.idStr]){
            selectModel = model;
        }
    }
    if(selectModel != nil){
      [JCTemplateFunctionHelper getTemplateDetailRequest:selectModel complate:^(JCTemplateData *resultData) {
        [self preareToPrintWithTemplateModel:resultData];
      }];
    }
}

- (void)initNet{
    [super initNet];
    [self loadGoodsTemplate:YES templateTypeMode:self.selectTypeModel];
}

- (NSArray *)arrTypeDecrList{
    if(!_arrTypeDecrList){
        JCGoodTemplateTypeMode *lastTemplateModel = [[JCGoodTemplateTypeMode alloc] init];
        lastTemplateModel.goodTemplateId = @"1";
        lastTemplateModel.isSelected = @"1";
        lastTemplateModel.pageNumber = @"1";
        lastTemplateModel.name = XY_LANGUAGE_TITLE_NAMED(@"", @"最新");
        lastTemplateModel.templateArr = @[].mutableArray;
        
//        JCGoodTemplateTypeMode *historyTemplateModel = [[JCGoodTemplateTypeMode alloc] init];
//        historyTemplateModel.goodTemplateId = @"2";
//        historyTemplateModel.isSelected = @"0";
//        historyTemplateModel.pageNumber = @"1";
//        historyTemplateModel.name = XY_LANGUAGE_TITLE_NAMED(@"", @"历史模板");
//        historyTemplateModel.templateArr = @[].mutableArray;
        _arrTypeDecrList = @[lastTemplateModel];
    }
    return _arrTypeDecrList;
}

- (JCGoodTemplateTypeMode *)selectTypeModel{
    JCGoodTemplateTypeMode *selectTypeModel = nil;
    for (JCGoodTemplateTypeMode *templateTypeModel in self.arrTypeDecrList) {
        if(templateTypeModel.isSelected.integerValue == 1){
            selectTypeModel = templateTypeModel;
            break;
        }
    }
    _selectTypeModel = selectTypeModel;
    return _selectTypeModel;
}

- (void)loadGoodsTemplate:(BOOL)isShowLoading templateTypeMode:(JCGoodTemplateTypeMode *)templateTypeMode{
    templateTypeMode.pageNumber = @"1";
    [self getTemplateList:isShowLoading templateTypeMode:templateTypeMode];
}

- (void)loadMoreGoodsTemplateWithTypeMode:(JCGoodTemplateTypeMode *)templateTypeMode{
    templateTypeMode.pageNumber = StringFromInt(templateTypeMode.pageNumber.integerValue + 1);
    [self getTemplateList:NO templateTypeMode:templateTypeMode];
}

- (void)getTemplateList:(BOOL)isShowLoading templateTypeMode:(JCGoodTemplateTypeMode *)templateTypeMode{
    XYWeakSelf
    if(NETWORK_STATE_ERROR){
        self.templateListView.mj_footer.hidden = NO;
        // 网络错误时，显示空数据
        templateTypeMode.templateArr = @[].mutableCopy;
        [self.templateListView.mj_header endRefreshing];
        [self.templateListView.mj_footer endRefreshing];
        [self.templateListView reloadData];
        [weakSelf showNoDateView:YES];
        return;
    }
    
    // 准备参数调用Flutter方法
    NSNumber *page = @(templateTypeMode.pageNumber.intValue);
    NSNumber *limit = @10;
    NSNumber *commodityTemplate = @(templateTypeMode.goodTemplateId.intValue);
    NSString *searchKey = self.isSearchType ? (self.searchKeywords ?: @"") : @"";
    
    // 显示加载指示器
    MBProgressHUD *hud = nil;
    if (isShowLoading) {
        hud = [MBProgressHUD showHUDAddedTo:hudWindow animated:YES contentColor:hudContentColor backColor:hudBackColor];
    }
    
    // 使用JCTemplateDBManager的nativeGetMyGoodTemplateList方法获取模板列表
    [JCTemplateDBManager nativeGetMyGoodTemplateList:page 
                                             limit:limit 
                                 commodityTemplate:commodityTemplate 
                                         searchKey:searchKey 
                                           success:^(NSArray<JCTemplateData *> *templates, NSDictionary *response) {
        weakSelf.templateListView.mj_footer.hidden = NO;
        [weakSelf.templateListView.mj_footer resetNoMoreData];
        [weakSelf.templateListView.mj_header endRefreshing];
        
        if (hud) {
            [hud hideAnimated:YES];
        }
        
        // 更新模板类型模式中的数据
        if (templateTypeMode.pageNumber.integerValue == 1) {
            templateTypeMode.templateArr = [templates mutableCopy];
        } else {
            NSMutableArray *currentTemplates = [templateTypeMode.templateArr mutableCopy];
            [currentTemplates addObjectsFromArray:templates];
            templateTypeMode.templateArr = currentTemplates;
        }
        
        // 处理加载更多状态
        if (templates.count < limit.integerValue) {
            [weakSelf.templateListView.mj_footer endRefreshingWithNoMoreData];
        } else {
            weakSelf.templateListView.mj_footer.hidden = NO;
        }
        [weakSelf.templateListView reloadData];
    } failed:^(NSString *errorMsg) {
        if (hud) {
            [hud hideAnimated:YES];
        }
        
        [MBProgressHUD showToastWithMessageDarkColor:errorMsg];
        templateTypeMode.templateArr = @[].mutableCopy;
        [weakSelf refreshMainViewSearchState:weakSelf.isSearchType];
        [weakSelf.templateListView.mj_header endRefreshing];
        [weakSelf.templateListView.mj_footer endRefreshing];
        [weakSelf.templateListView reloadData];
        [weakSelf showNoDateView:YES];
    }];
}

- (void)showNoDateView:(BOOL)showNodataView{
    [self.mainView showStoreNoDataView:showNodataView?2:0];
}

- (void)toCreateTemplate{
}

- (void)toCreateNewGoodsTemplate{
    self.firstGoodInfoModel = nil;
    if(NETWORK_STATE_ERROR){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"", @"网络异常")];
        return;
    }
    XYWeakSelf
    [self java_postWithValues:@{@"limit":@"1"} ModelType:[JCGoodDetailInfo class] Path:J_goods_user_goods_list hud:@"" Success:^(__kindof YTKBaseRequest *request,  JCGoodDetailInfo *requestModel) {
        NSMutableArray *requestArr = requestModel.list.mutableCopy;
        if(requestArr.count > 0){
            JCGoodDetailInfo *goodInfoModel = requestArr.firstObject;
            weakSelf.firstGoodInfoModel = goodInfoModel;
            [weakSelf goodLibraryPrintWithCode:goodInfoModel.barcode];
        }else{
            JCGoodDetailInfo *goodInfoModel = [[JCGoodDetailInfo alloc] init];
            goodInfoModel.goodId = [NSString stringWithFormat:@"%@%@",[XYTool getNowTimeTimestamp],StringFromInt((NSInteger)rand()% 10000)];
            goodInfoModel.barcode = @"";
            weakSelf.firstGoodInfoModel = goodInfoModel;
            [weakSelf goodLibraryPrintWithCode:goodInfoModel.barcode];
        }
        
    } failure:^(NSString *msg, id model) {
        JCGoodDetailInfo *goodInfoModel = [[JCGoodDetailInfo alloc] init];
        goodInfoModel.goodId = [NSString stringWithFormat:@"%@%@",[XYTool getNowTimeTimestamp],StringFromInt((NSInteger)rand()% 10000)];
        goodInfoModel.barcode = @"";
        weakSelf.firstGoodInfoModel = goodInfoModel;
        [weakSelf goodLibraryPrintWithCode:goodInfoModel.barcode];
    }];
}

- (void)goodLibraryPrintWithCode:(NSString *)code{
    XYWeakSelf
    JCTemplateData *rfidTemplateModel = [JCPrintManager sharedInstance].rfidTemplateData;
    if(self.progressHUD && !self.progressHUD.hidden){
        [self.progressHUD hideAnimated:NO];
        self.progressHUD = nil;
    }
    self.progressHUD = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
    self.progressHUD.label.text = XY_LANGUAGE_TITLE_NAMED(@"app01115", @"数据加载中");
    NSDictionary *requestDic = @{@"barcode":UN_NIL(code)};
    if(rfidTemplateModel != nil){
        NSString *labelId = STR_IS_NIL(rfidTemplateModel.idStr)?@"-1":rfidTemplateModel.idStr;
        NSString *labelBarcode = STR_IS_NIL(rfidTemplateModel.barcode)?@"-1":rfidTemplateModel.barcode;
        requestDic = @{@"barcode":UN_NIL(code),@"labelId":labelId,@"labelBarcode":labelBarcode};
    }
    [weakSelf java_postWithValues:requestDic ModelType:[JCGoodResultModel class] Path:J_goods_new_scan hud:nil Success:^(__kindof YTKBaseRequest *request, JCGoodResultModel *requestModel) {
        requestModel.goods_info.barcode = code;
        [weakSelf getGoodsResultSuccessParms:requestModel scanCode:code];
    } failure:^(NSString *msg, id model) {
        [JCRecordTool recordWithAction:click_scan_print_callBack_faild];
        [self.progressHUD hideAnimated:NO];
    }];
}

- (void)getGoodsResultSuccessParms:(JCGoodResultModel *)requestModel scanCode:(NSString *)scanCode{
    JCTemplateData *rfidTemplateModel = [JCPrintManager sharedInstance].rfidTemplateData;
    if(!STR_IS_NIL(rfidTemplateModel.idStr)){
        if(!STR_IS_NIL(requestModel.template_info.idStr)){
            requestModel.template_info.labelId = requestModel.template_info.profile.extrain.labelId;
            [self getTemplateDetailWithGoodsResult:requestModel hasRFID:YES];
        }else{
            [self jumpToTemplateEditVCWithRFIDModel:rfidTemplateModel goodInfo:requestModel.goods_info];
            [self.progressHUD hideAnimated:NO];
        }
    }else{
        if(!STR_IS_NIL(requestModel.template_info.idStr)){
            [self getTemplateDetailWithGoodsResult:requestModel hasRFID:NO];
        }else{
            [self.progressHUD hideAnimated:NO];
        }
    }
}


- (void)getTemplateDetailWithGoodsResult:(JCGoodResultModel *)goodResult hasRFID:(BOOL)hasRFID{
    [JCTemplateImageManager downLoadImagesForData:goodResult.template_info options:DownAll complete:^(JCTemplateData *resultData) {
        // Update the template data with the result
        if (resultData) {
            goodResult.template_info = resultData;
        }
        [self.progressHUD hideAnimated:NO];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self jumpToTemplateEditVCWith:goodResult.template_info goodInfo:goodResult.goods_info hasRFID:hasRFID];
        });
    }];
}

- (void)jumpToTemplateEditVCWith:(JCTemplateData *)requestModel goodInfo:(JCGoodDetailInfo *)goodDetailInfo hasRFID:(BOOL)hasRFID{
    requestModel.profile.extrain.userId = @"";
    TemplateSource source = TemplateSource_New;//
    JCTemplateData *model = [JCTMDataBindGoodsInfoManager templateDataWith:requestModel goodInfo:goodDetailInfo];
    model.profile.extrain.goodsCode = goodDetailInfo.barcode;
    model.goodsList = (NSArray<JCGoodDetailInfo> *)@[goodDetailInfo];
    if(STR_IS_NIL(requestModel.profile.extrain.userId)){
        model.name = STR_IS_NIL(goodDetailInfo.name)?XY_LANGUAGE_TITLE_NAMED(@"app01052", @"品名"):goodDetailInfo.name;
    }
    source = TemplateSource_New;
    // 画板灰度
    if([JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:model.templateVersion] == false){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000343", @"您的软件版本过低，请升级")];
        return;
    }
    [JCTemplateFunctionHelper toEdit:model];
}

- (void)jumpToTemplateEditVCWithRFIDModel:(JCTemplateData *)m goodInfo:(JCGoodDetailInfo *)goodDetailInfo{
    JCTemplateData *model = [JCTMDataBindGoodsInfoManager createGoodsTemplateWith:m goodInfo:goodDetailInfo];
    model.profile.extrain.commodityCategoryId = goodDetailInfo.categoryId;
    model.profile.extrain.industryId = goodDetailInfo.industryId;
    model.profile.extrain.goodsCode = goodDetailInfo.barcode;
    model.goodsList = (NSArray<JCGoodDetailInfo> *)@[goodDetailInfo];
    if([JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:model.templateVersion] == false){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000343", @"您的软件版本过低，请升级")];
        return;
    }
    [JCTemplateFunctionHelper toEdit:model];
}

- (void)templatechange:(NSNotification *)notification{
    // 收到模板变更通知后重新加载数据
    [self loadGoodsTemplate:NO templateTypeMode:self.selectTypeModel];
}

- (void)synchTemplateFinish:(NSNotification *)notification{
    NSDictionary *oldNewIdDic = notification.object;
    NSString *oldId = [oldNewIdDic objectForKey:@"oldXYId"];
    NSString *newId = [oldNewIdDic objectForKey:@"newXYId"];
    if(oldId.length != 0 && newId.length != 0){
        // 收到同步完成通知后重新加载数据
        [self loadGoodsTemplate:NO templateTypeMode:self.selectTypeModel];
    }
}

//获取打印机信息
- (void)printerStatusNotification:(NSNotification *)noti {
    NSString *state = noti.object;
    BOOL isBlueOpen = ([state isEqualToString:@"1"] ? YES:NO);
    if(isBlueOpen && JC_IS_CONNECTED_PRINTER){
        [self.printStateBtn setImage:XY_IMAGE_NAMED(@"goodsPrint_connected") forState:UIControlStateNormal];
    }else{
        [self.printStateBtn setImage:XY_IMAGE_NAMED(@"goodsPrint_unconnect") forState:UIControlStateNormal];
    }
}


- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return self.selectTypeModel.templateArr.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return 15;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    CGFloat headerHeight = 15;
    UIView *headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, headerHeight)];
    return headerView;
}


- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    return 15;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section{
    UIView *footerView = [[UIView alloc] init];
    return footerView;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return (SCREEN_WIDTH - 30) * 210/343;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    JCTemplateData *model = [self.selectTypeModel.templateArr safeObjectAtIndex:indexPath.row];
    JCContentTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"JCContentTableViewCell"];
    cell.contentView.backgroundColor = HEX_RGB(0xF5F5F5);
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    model.profile.extrain.templateType = @"2";
    [cell setSelectedBoard:[self.selectTemplateId isEqualToString:model.idStr]];
    [cell setData:model isEdit:NO];
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    JCTemplateData *model = [self.selectTypeModel.templateArr safeObjectAtIndex:indexPath.row];
    if([self.selectTemplateId isEqual:model.idStr]){
        self.selectTemplateId = @"";
        self.navRightBtn.selected = NO;
        self.navRightBtn.enabled = NO;
    }else{
        JC_TrackWithparms(@"click",@"017_053_069",(@{@"temp_id":UN_NIL(model.idStr),@"pos":@(indexPath.row),@"tab_name":UN_NIL(self.selectTypeModel.name),@"is_vip":(model.hasVipRes || model.vip)?@"1":@"0"}));
        self.selectTemplateId = model.idStr;
        self.navRightBtn.selected = YES;
        self.navRightBtn.enabled = YES;
    }
    [tableView reloadData];
}

- (void)preareToPrintWithTemplateModel:(JCTemplateData *)model{
    if(NETWORK_STATE_ERROR){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
        return;
    }
    
    // 检查版本兼容性
    if([JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:model.templateVersion] == false){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000343", @"您的软件版本过低，请升级")];
        return;
    }
    
    // 检查VIP功能
    if([model shouldShowVip]){
        jc_is_support_vip = YES;
        JC_TrackWithparms(@"click",@"017_085_109",(@{}));
        [JCIAPHelper openViewWithAlert:self needOpenTip:YES isUseVipSource:YES success:^{} failure:^(NSString *msg, id model) {
            [MBProgressHUD showToastWithMessageDarkColor:msg];
        } sourceInfo:@{@"sourcePage":@"017"}];
        return;
    }
    
    // 检查曲线文字升级
    if([model shouldCurveTextUpdrage]){
        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01439", @"提醒") message:XY_LANGUAGE_TITLE_NAMED(@"app100000082", @"限时免费体验已结束，为保证您的正常使用，请更新APP到最新版本。") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000081", @"不用了") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00487", @"更新") cancelBlock:nil sureBlock:^{
            [[UIApplication sharedApplication] openURL:XY_URLWithString(app_download_Url) options:@{} completionHandler:^(BOOL success) {}];
        } alertType:3];
        return;
    }
  // 检查字体
    [JCTemplateFunctionHelper checkFontDownload:model complate:^(){
      [self toPrintWithTemplateModel:model];
    }];
}

- (void)toPrintWithTemplateModel:(JCTemplateData *)model{
    // 重新加载模板数据
    [self loadGoodsTemplate:NO templateTypeMode:self.selectTypeModel];
    
    // 跳转到打印页面
    JCStoreGoodsLibearyViewController *vc = [[JCStoreGoodsLibearyViewController alloc] initWithGoodsListState:4 goodsTemplate:model];
    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark -VIP ListCollectionViewDelegate

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView{
    return 1;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    NSInteger itemCount = self.arrTypeDecrList.count;
    return itemCount;
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    JCGoodTemplateTypeMode *templateTypeModel = [self.arrTypeDecrList safeObjectAtIndex:indexPath.row];
    NSString *title = templateTypeModel.name;
    BOOL isSelect = templateTypeModel.isSelected.integerValue == 1;
    JCSegmentCollectionCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"JCSegmentCollectionCell" forIndexPath:indexPath];
    [cell refreshWithTitle:title isSelected:isSelect];
    return cell;
}

- (CGSize)collectionView:(UICollectionView*)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath*)indexPath{
    JCGoodTemplateTypeMode *templateTypeModel = [self.arrTypeDecrList safeObjectAtIndex:indexPath.row];
    NSString *title = templateTypeModel.name;
    UIFont *font = (templateTypeModel.isSelected.integerValue == 1)?MY_FONT_Bold(16):MY_FONT_Bold(14);
    CGSize titleSize = [title jk_sizeWithFont:font constrainedToHeight:1000];
    return CGSizeMake(titleSize.width, 53);
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    JCGoodTemplateTypeMode *selectTypeModel = [self.arrTypeDecrList safeObjectAtIndex:indexPath.row];
    if(selectTypeModel.goodTemplateId.integerValue == self.selectTypeModel.goodTemplateId.integerValue) return;
    for (JCGoodTemplateTypeMode *templateTypeModel in self.arrTypeDecrList) {
        if(selectTypeModel.goodTemplateId.integerValue == templateTypeModel.goodTemplateId.integerValue){
            templateTypeModel.isSelected = @"1";
        }else{
            templateTypeModel.isSelected = @"0";
        }
    }
    
    // 清空当前数据并重新加载
    self.selectTypeModel.templateArr = @[].mutableCopy;
    [self.templateListView reloadData];
    [collectionView reloadData];
    
    // 加载新选择的模板类型数据
    [self loadGoodsTemplate:NO templateTypeMode:self.selectTypeModel];
    
    // 重置选择状态
    self.selectTemplateId = @"";
    self.navRightBtn.selected = NO;
    self.navRightBtn.enabled = NO;
}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
