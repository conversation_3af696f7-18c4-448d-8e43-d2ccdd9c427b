import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'dart:ui' as ui;
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_canvas_plugins_interface/utils/display_util.dart';
import 'package:flutter_canvas_plugins_interface/utils/precision_num.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/model/canvas_element.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/ninepatch/png_chunk_reader.dart';
import 'package:path_provider/path_provider.dart';
import 'package:tuple/tuple.dart';
import 'nine_patch.dart';

class NinePatchImage2 extends StatefulWidget {
  /// 元素ID，截图时比对防止不同的ID导致的截图不一致问题
  final String elementId;

  /// 素材ID，截图时比对防止不同的ID导致的截图不一致问题
  final String materialId;

  /// 点9图路径
  final String imagePath;

  /// 截图保存路径
  final String screenshotsPath;

  /// 画笔颜色
  final Color color;

  /// 四周不可拉伸的范围
  final ValueChanged<Rect?> stretchRectClosure;

  /// 初始化四周不可拉伸系数
  final ValueChanged<Tuple2<double, double>?>? ninePatchInitialCornerScaleClosure;

  /// 点9图首次展示的四周放大比例, 逻辑参数，主要处理复制元素、镜像元素导致的比例错误
  final Tuple2<double, double>? ninePatchInitialCornerScale;

  NinePatchImage2({
    super.key,
    required this.imagePath,
    required this.screenshotsPath,
    required this.color,
    required this.stretchRectClosure,
    this.ninePatchInitialCornerScale,
    this.ninePatchInitialCornerScaleClosure,
    required this.elementId,
    required this.materialId,
  });

  @override
  _NinePatchImageState createState() => _NinePatchImageState();
}

class _NinePatchImageState extends State<NinePatchImage2> {
  // 截图时使用的像素比例
  static const double _SCREENSHOT_PIXEL_RATIO = 8.0;

  // 首次渲染时的延迟时间(毫秒)
  static const int _INITIAL_SCREENSHOT_DELAY = 200;

  // 点9图四角的默认缩放系数，与臣小印保持一致
  static const double _DEFAULT_CORNER_SCALE = 0.35;

  ui.Image? _image;
  Rect? _stretchRect;
  bool _isLoading = true;
  double? _initialCornerScaleX;
  double? _initialCornerScaleY;
  late StreamSubscription _subscription;

  @override
  void initState() {
    super.initState();
    _loadImage();
    // 监听截图请求
    _subscription = CanvasEventBus.getDefault().register(this, (data) async {
      // _logger.log("打印画板EventBus消息：$data");
      if (data is Map && data.containsKey("action")) {
        final action = data['action'];
        switch (action) {
          case "NinePatchImage":
            final elementId = data['elementId'];
            final materialId = data['materialId'];
            if (elementId == widget.elementId && materialId == widget.materialId) {
              File? file = await _takeScreenshot();
              void Function(File? file) saveScreenshotClosure = data['saveScreenshot'];
              saveScreenshotClosure(file);
            }
            break;
        }
      }
    });
    // 赋予默认展示的四周放大比例
    _initialCornerScaleX = widget.ninePatchInitialCornerScale?.item1;
    _initialCornerScaleY = widget.ninePatchInitialCornerScale?.item2;
  }

  @override
  void dispose() {
    super.dispose();
    CanvasEventBus.getDefault().unregister(this);
  }

  // 获取存储目录
  static Future<Directory> get _screenshotsDir async {
    final appDir = await getApplicationDocumentsDirectory();
    final screenshotsDir = Directory('${appDir.path}/JCPrintCache/Image/Template/ninePatch/screenshots');
    if (!await screenshotsDir.exists()) {
      await screenshotsDir.create(recursive: true);
    }
    return screenshotsDir;
  }

  // 保存截图
  Future<File?> saveScreenshot(Uint8List bytes, {String? customName}) async {
    try {
      // final dir = await _screenshotsDir;
      // final timestamp = DateTime.now().millisecondsSinceEpoch;
      // final fileName = customName ?? 'screenshot_$timestamp.png';
      final file = File(widget.screenshotsPath);

      await file.writeAsBytes(bytes);
      return file;
    } catch (e) {
      print('保存截图失败: $e');
      return null;
    }
  }

  Future<File?> _takeScreenshot() async {
    try {
      final boundary = context.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) {
        return null;
      }

      final image = await boundary.toImage(pixelRatio: _SCREENSHOT_PIXEL_RATIO);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final bytes = byteData?.buffer.asUint8List();
      File? file = await saveScreenshot(
        bytes!,
        customName: 'custom_name.png',
      );
      return file;
    } catch (e) {
      print('截图失败: $e');
      return null;
    }
  }

  Future<void> _loadImage() async {
    PngChunkReader.loadPngInfo(widget.imagePath).then((value) async {
      if (value != null) {
        final ninePatch = NinePatchChunk.decode(value.ninePatchChunk!);
        if (ninePatch != null) {
          final file = File(widget.imagePath);
          final bytes = await file.readAsBytes();
          final codec = await ui.instantiateImageCodec(bytes);
          final frame = await codec.getNextFrame();
          final rect = ninePatch.getStretchRegions()!;
          final scale = 1.0; // 计算展示区域大小
          final _width = frame.image.width;
          final _height = frame.image.height;
          setState(() {
            _stretchRect = Rect.fromLTRB(rect.left * scale, rect.top * scale, rect.right * scale, rect.bottom * scale);
            _image = frame.image;
            _isLoading = false;
          });
          // 延时20m存储图片
          Future.delayed(Duration(milliseconds: _INITIAL_SCREENSHOT_DELAY), () async {
            widget.stretchRectClosure
                .call(Rect.fromLTWH(0, 0, _width - (_stretchRect?.width ?? 0), _height - (_stretchRect?.height ?? 0)));
            // 存储首帧图片
            await _takeScreenshot();
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading || _image == null) {
      return Center(child: CircularProgressIndicator());
    }

    return RepaintBoundary(
      child: ColorFiltered(
        colorFilter: ColorFilter.mode(
          widget.color,
          BlendMode.srcIn,
        ),
        child: LayoutBuilder(
          builder: (context, constraints) {
            // 使用transformedSize而不是constraints
            final double imageWidth = _image!.width.toDouble();
            final double imageHeight = _image!.height.toDouble();
            final Rect stretchRect = _stretchRect!;

            // 获取设计图理想的缩放比例，对应的理想mm
            double intrinsicMMX = imageWidth / 35;
            double intrinsicMMY = imageHeight / 35;

            // 定义缩放系数
            double scaleX = (intrinsicMMX * DisplayUtil.dpRatio) / constraints.maxWidth;
            double scaleY = (intrinsicMMY * DisplayUtil.dpRatio) / constraints.maxHeight;

            // 首次渲染时记住四个角的缩放值
            if (_initialCornerScaleX == null) {
              _initialCornerScaleX = _DEFAULT_CORNER_SCALE;
              _initialCornerScaleY = _DEFAULT_CORNER_SCALE;
              // 返回上层存储当前的四角缩小系数
              widget.ninePatchInitialCornerScaleClosure?.call(
                  Tuple2(_initialCornerScaleX ?? _DEFAULT_CORNER_SCALE, _initialCornerScaleY ?? _DEFAULT_CORNER_SCALE));
            }

            // 定义九个区域的矩形区域
            Rect topLeft = Rect.fromLTWH(0, 0, stretchRect.left, stretchRect.top);
            Rect topCenter = Rect.fromLTWH(stretchRect.left, 0, stretchRect.width, stretchRect.top);
            Rect topRight = Rect.fromLTWH(stretchRect.right, 0, imageWidth - stretchRect.right, stretchRect.top);

            Rect middleLeft = Rect.fromLTWH(0, stretchRect.top, stretchRect.left, stretchRect.height);
            Rect middleCenter = Rect.fromLTWH(stretchRect.left, stretchRect.top, stretchRect.width, stretchRect.height);
            Rect middleRight =
                Rect.fromLTWH(stretchRect.right, stretchRect.top, imageWidth - stretchRect.right, stretchRect.height);

            Rect bottomLeft = Rect.fromLTWH(0, stretchRect.bottom, stretchRect.left, imageHeight - stretchRect.bottom);
            Rect bottomCenter = Rect.fromLTWH(
                stretchRect.left, stretchRect.bottom, stretchRect.width, imageHeight - stretchRect.bottom);
            Rect bottomRight = Rect.fromLTWH(stretchRect.right, stretchRect.bottom, imageWidth - stretchRect.right,
                imageHeight - stretchRect.bottom);

            // return Image.asset(widget.imagePath, centerSlice: middleCenter);
            return CustomPaint(
              size: constraints.biggest,
              painter: NinePatchPainter(
                image: _image!,
                topLeft: topLeft,
                topCenter: topCenter,
                topRight: topRight,
                middleLeft: middleLeft,
                middleCenter: middleCenter,
                middleRight: middleRight,
                bottomLeft: bottomLeft,
                bottomCenter: bottomCenter,
                bottomRight: bottomRight,
                cornerScaleX: _initialCornerScaleX!,
                cornerScaleY: _initialCornerScaleY!,
              ),
            );
          },
        ),
      ),
    );
  }
}

class NinePatchPainter extends CustomPainter {
  final ui.Image image;
  final Rect topLeft, topCenter, topRight;
  final Rect middleLeft, middleCenter, middleRight;
  final Rect bottomLeft, bottomCenter, bottomRight;
  final double cornerScaleX, cornerScaleY;

  NinePatchPainter({
    required this.image,
    required this.topLeft,
    required this.topCenter,
    required this.topRight,
    required this.middleLeft,
    required this.middleCenter,
    required this.middleRight,
    required this.bottomLeft,
    required this.bottomCenter,
    required this.bottomRight,
    required this.cornerScaleX,
    required this.cornerScaleY,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // // 四个角落区域 - 当scale < 1时缩小，否则保持原始大小
    // double cornerScaleX = scaleX < 1 ? scaleX : 1;
    // double cornerScaleY = scaleY < 1 ? scaleY : 1;
    // 左上角
    _drawImageRect(
        canvas,
        image,
        topLeft,
        Rect.fromLTWH(
            0, 0, topLeft.width / DisplayUtil.devicePixelRatio, topLeft.height / DisplayUtil.devicePixelRatio));

    // 右上角
    _drawImageRect(
        canvas,
        image,
        topRight,
        Rect.fromLTWH(size.width - topRight.width / DisplayUtil.devicePixelRatio, 0,
            topRight.width / DisplayUtil.devicePixelRatio, topRight.height / DisplayUtil.devicePixelRatio));

    // 左下角
    _drawImageRect(
        canvas,
        image,
        bottomLeft,
        Rect.fromLTWH(0, size.height - bottomLeft.height / DisplayUtil.devicePixelRatio,
            bottomLeft.width / DisplayUtil.devicePixelRatio, bottomLeft.height / DisplayUtil.devicePixelRatio));

    // 右下角
    _drawImageRect(
        canvas,
        image,
        bottomRight,
        Rect.fromLTWH(
            size.width - bottomRight.width / DisplayUtil.devicePixelRatio,
            size.height - bottomRight.height / DisplayUtil.devicePixelRatio,
            bottomRight.width / DisplayUtil.devicePixelRatio,
            bottomRight.height / DisplayUtil.devicePixelRatio));

    // 上边缘 - 水平平铺或拉伸
    double remainingWidth =
        size.width - topLeft.width / DisplayUtil.devicePixelRatio - topRight.width / DisplayUtil.devicePixelRatio;
    if (remainingWidth > 0) {
      if (true) {
        // 平铺模式
        double x = topLeft.width / DisplayUtil.devicePixelRatio;
        while (x < size.width - topRight.width / DisplayUtil.devicePixelRatio) {
          double segmentWidth = min(topCenter.width / DisplayUtil.devicePixelRatio,
              size.width - topRight.width / DisplayUtil.devicePixelRatio - x);
          _drawImageRect(canvas, image, topCenter,
              Rect.fromLTWH(x, 0, segmentWidth, topCenter.height / DisplayUtil.devicePixelRatio));
          x += topCenter.width / DisplayUtil.devicePixelRatio;
        }
      } else {
        // 拉伸模式
        _drawImageRect(
            canvas,
            image,
            topCenter,
            Rect.fromLTWH(topLeft.width / DisplayUtil.devicePixelRatio, 0, remainingWidth,
                topCenter.height / DisplayUtil.devicePixelRatio));
      }
    }

    // 下边缘 - 水平平铺或拉伸
    remainingWidth =
        size.width - bottomLeft.width / DisplayUtil.devicePixelRatio - bottomRight.width / DisplayUtil.devicePixelRatio;
    if (remainingWidth > 0) {
      if (true) {
        // 平铺模式
        double x = bottomLeft.width / DisplayUtil.devicePixelRatio;
        while (x < size.width - bottomRight.width / DisplayUtil.devicePixelRatio) {
          double segmentWidth = min(bottomCenter.width / DisplayUtil.devicePixelRatio,
              size.width - bottomRight.width / DisplayUtil.devicePixelRatio - x);
          _drawImageRect(
              canvas,
              image,
              bottomCenter,
              Rect.fromLTWH(x, size.height - bottomCenter.height / DisplayUtil.devicePixelRatio, segmentWidth,
                  bottomCenter.height / DisplayUtil.devicePixelRatio));
          x += bottomCenter.width / DisplayUtil.devicePixelRatio;
        }
      } else {
        // 拉伸模式
        _drawImageRect(
            canvas,
            image,
            bottomCenter,
            Rect.fromLTWH(
                bottomLeft.width / DisplayUtil.devicePixelRatio,
                size.height - bottomCenter.height / DisplayUtil.devicePixelRatio,
                remainingWidth,
                bottomCenter.height / DisplayUtil.devicePixelRatio));
      }
    }

    // 左边缘 - 垂直平铺或拉伸
    double remainingHeight =
        size.height - topLeft.height / DisplayUtil.devicePixelRatio - bottomLeft.height / DisplayUtil.devicePixelRatio;
    if (remainingHeight > 0) {
      if (true) {
        // 平铺模式
        double y = topLeft.height / DisplayUtil.devicePixelRatio;
        while (y < size.height - bottomLeft.height / DisplayUtil.devicePixelRatio) {
          double segmentHeight = min(middleLeft.height / DisplayUtil.devicePixelRatio,
              size.height - bottomLeft.height / DisplayUtil.devicePixelRatio - y);
          _drawImageRect(canvas, image, middleLeft,
              Rect.fromLTWH(0, y, middleLeft.width / DisplayUtil.devicePixelRatio, segmentHeight));
          y += middleLeft.height / DisplayUtil.devicePixelRatio;
        }
      } else {
        // 拉伸模式
        _drawImageRect(
            canvas,
            image,
            middleLeft,
            Rect.fromLTWH(0, topLeft.height / DisplayUtil.devicePixelRatio,
                middleLeft.width / DisplayUtil.devicePixelRatio, remainingHeight));
      }
    }

    // 右边缘 - 垂直平铺或拉伸
    remainingHeight = size.height -
        topRight.height / DisplayUtil.devicePixelRatio -
        bottomRight.height / DisplayUtil.devicePixelRatio;
    if (remainingHeight > 0) {
      if (true) {
        // 平铺模式
        double y = topRight.height / DisplayUtil.devicePixelRatio;
        while (y < size.height - bottomRight.height / DisplayUtil.devicePixelRatio) {
          double segmentHeight = min(middleRight.height / DisplayUtil.devicePixelRatio,
              size.height - bottomRight.height / DisplayUtil.devicePixelRatio - y);
          _drawImageRect(
              canvas,
              image,
              middleRight,
              Rect.fromLTWH(size.width - middleRight.width / DisplayUtil.devicePixelRatio, y,
                  middleRight.width / DisplayUtil.devicePixelRatio, segmentHeight));
          y += middleRight.height / DisplayUtil.devicePixelRatio;
        }
      } else {
        // 拉伸模式
        _drawImageRect(
            canvas,
            image,
            middleRight,
            Rect.fromLTWH(
                size.width - middleRight.width / DisplayUtil.devicePixelRatio,
                topRight.height / DisplayUtil.devicePixelRatio,
                middleRight.width / DisplayUtil.devicePixelRatio,
                remainingHeight));
      }
    }

    // 中心区域 - 水平垂直平铺或拉伸
    remainingWidth =
        size.width - middleLeft.width / DisplayUtil.devicePixelRatio - middleRight.width / DisplayUtil.devicePixelRatio;
    remainingHeight = size.height -
        topCenter.height / DisplayUtil.devicePixelRatio -
        bottomCenter.height / DisplayUtil.devicePixelRatio;
    if (remainingWidth > 0 && remainingHeight > 0) {
      if (true) {
        // 平铺模式
        double y = topCenter.height / DisplayUtil.devicePixelRatio;
        while (y < size.height - bottomCenter.height / DisplayUtil.devicePixelRatio) {
          double segmentHeight =
              min(middleCenter.height, size.height - bottomCenter.height / DisplayUtil.devicePixelRatio - y);
          double x = middleLeft.width / DisplayUtil.devicePixelRatio;
          while (x < size.width - middleRight.width / DisplayUtil.devicePixelRatio) {
            double segmentWidth =
                min(middleCenter.width, size.width - middleRight.width / DisplayUtil.devicePixelRatio - x);
            _drawImageRect(canvas, image, middleCenter, Rect.fromLTWH(x, y, segmentWidth, segmentHeight));
            x += middleCenter.width;
          }
          y += middleCenter.height;
        }
      } else {
        // 拉伸模式
        _drawImageRect(
            canvas,
            image,
            middleCenter,
            Rect.fromLTWH(middleLeft.width / DisplayUtil.devicePixelRatio,
                topCenter.height / DisplayUtil.devicePixelRatio, remainingWidth, remainingHeight));
      }
    }
  }

  void _drawImageRect(Canvas canvas, ui.Image image, Rect srcRect, Rect dstRect) {
    Paint paint = Paint();
    canvas.drawImageRect(image, srcRect, dstRect, paint);
  }

  @override
  bool shouldRepaint(covariant NinePatchPainter oldDelegate) {
    return oldDelegate.image != image ||
        oldDelegate.topLeft != topLeft ||
        oldDelegate.topCenter != topCenter ||
        oldDelegate.topRight != topRight ||
        oldDelegate.middleLeft != middleLeft ||
        oldDelegate.middleCenter != middleCenter ||
        oldDelegate.middleRight != middleRight ||
        oldDelegate.bottomLeft != bottomLeft ||
        oldDelegate.bottomCenter != bottomCenter ||
        oldDelegate.bottomRight != bottomRight ||
        oldDelegate.cornerScaleX != cornerScaleX ||
        oldDelegate.cornerScaleY != cornerScaleY;
  }
}
