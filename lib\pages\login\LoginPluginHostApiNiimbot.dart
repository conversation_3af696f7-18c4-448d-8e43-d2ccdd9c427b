import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:common_utils/common_utils.dart';
import 'package:dio/src/multipart_file.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:niim_login/login_plugin/handler/international_handler.dart';
import 'package:niim_login/login_plugin/handler/login_result_handler.dart';
import 'package:niim_login/login_plugin/login_plugin_api.dart';
import 'package:niim_login/login_plugin/login_plugin_config.dart';
import 'package:niim_login/login_plugin/login_plugin_host_api.dart';
import 'package:niim_login/login_plugin/pages/login_selection/login_selection_data.dart';
import 'package:niim_login/login_plugin/router/login_navigator_utils.dart';
import 'package:text/business/app/app_helper.dart';
import 'package:text/business/user/user_login_helper.dart';
import 'package:text/macro/constant.dart';
import 'package:text/network/dio_utils.dart';
import 'package:text/pages/login/login_result_handler_niimbot.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/upload_image.dart';

import '../../application.dart';
import '../../international_info_controller.dart';
import '../../log_utils.dart';

class LoginPluginHostApiNiimbot implements LoginPluginHostApi {
  //默认用户头像
  static String defaultUserIcon =
      "https://jc-print.oss-cn-hangzhou.aliyuncs.com/print/cloudprint/merchandise/sku/20220915/6441663227815713.png";

  // 单例公开访问点
  // 静态私有成员，没有初始化
  static late LoginPluginHostApiNiimbot _instance = LoginPluginHostApiNiimbot._internal();

  LoginPluginHostApiNiimbot._internal();

  // 单例公开访问点
  factory LoginPluginHostApiNiimbot() => sharedInstance();

  // 静态私有成员，没有初始化

  // 静态、同步、私有访问点
  static LoginPluginHostApiNiimbot sharedInstance() {
    return _instance;
  }

  String get _oneKeyAuthSecret {
    if (Platform.isAndroid) {
      return Constant.androidOneKey;
    } else if (Platform.isIOS) {
      return Constant.iosOneKey;
    } else {
      return "";
    }
  }

  @override
  Future<Map> appleLoginAuth() {
    // TODO: implement appleLoginAuth
    return Future.value({});
  }

  @override
  bool checkIfSelfAccount(Map niimbotUser) {
    // TODO: implement checkIfSelfAccount
    return true;
  }

  @override
  bool localCheckSelfAccount(Map niimbotUser) {
    String unionId = niimbotUser["uid"] ?? "";
    String cacheUnionId = Application.sp.getString(ConstantKey.User_UnionId) ?? "";
    if (unionId.isNotEmpty && cacheUnionId.isNotEmpty && unionId == cacheUnionId) {
      Log.d("检查本地账号缓存localCheckSelfAccount: true");
      return true;
    } else {
      Log.d("检查本地账号缓存localCheckSelfAccount: false");
      return false;
    }
  }

  @override
  checkSupportNiimbotAccountLogin(Map niimbotUser, Function(bool p1) checkCallback) {
    // TODO: implement checkSupportNiimbotAccountLogin
    // throw UnimplementedError();
    checkCallback(false);
  }

  @override
  Future<Map> facebookLoginAuth() {
    // TODO: implement facebookLoginAuth
    return Future.value({});
  }

  @override
  Future<List> getSupportSocialTypes() {
    // TODO: implement getSupportSocialTypes
    Log.d("获取当前已安装第三方软件");
    return ToNativeMethodChannel().getSupportSocialList();
  }

  @override
  getUserInfo(Map loginData, Function(Map p1) success, Function(String error) fail) {
    String accessToken = loginData["accessToken"];
    String tokenType = loginData["tokenType"];
    Application.saveToken(accessToken);
    String regionCode = loginData["regionCode"] ?? "";
    String unionId = loginData["uid"] ?? "";
    regionCode = regionCode.toLowerCase();
    if (regionCode.isNotEmpty) {
      String userRegion = Application.sp.getString(ConstantKey.User_Chose_Region) ?? "";
      if (userRegion != regionCode) {
        Application.sp.setString(ConstantKey.User_Chose_Region, regionCode);
        Application.syncLanguageCodeToNative(isFirst: false);
        Application.syncLanguageCodeToLoginPlugin();
      }
    }

    ToNativeMethodChannel().getUserInfoFromNative(accessToken: accessToken, tokenType: tokenType).then((value) {
      if (value != null) {
        String? profileUrl = value["avatar"];
        if (null == profileUrl || profileUrl.isEmpty) {
          value["profileUrl"] = defaultUserIcon;
        } else {
          value["profileUrl"] = profileUrl;
        }
        String? nickname = value["nickname"];
        if (null == nickname || nickname.isEmpty) {
          value["uid"] = value["displayUId"];
        }

        Map result = {};
        result['loginData'] = loginData;
        result['loginBusinessData'] = value;
        // ToNativeMethodChannel().reportLoginSuccess(JsonUtil.encodeObj(result));
        success(value);
      } else {
        success({});
      }
    });
    // DioUtils.instance.requestNetwork<Map<String, dynamic>>(
    //   Method.get,
    //   HttpApi.userInfo,
    //   queryParameters: {"region": Application.userRegion},
    //   onSuccess: (value) {
    //     if(value != null && value is Map){
    //       String phone = "";
    //       if(value.containsKey("phone")){
    //         phone = value["phone"];
    //       }
    //       String email = "";
    //       if(value.containsKey("email")){
    //         email = value["email"];
    //       }
    //       // value['uid'] = loginData['displayUId'];
    //       String profileUrl = value["profileUrl"];
    //       if (profileUrl.isEmpty) {
    //         value["profileUrl"] = defaultUserIcon;
    //       }
    //       userInfoCallback(value);
    //     }
    //     else {
    //       userInfoCallback({});
    //     }
    //   },
    //   onNoFilterError: (code, message) {
    //     userInfoCallback({});
    //   },
    // );
  }

  @override
  Future<Map> googleLoginAuth() {
    // TODO: implement googleLoginAuth
    return Future.value({});
  }

  @override
  Future<Map> lineLoginAuth() {
    // TODO: implement lineLoginAuth
    return Future.value({});
  }

  @override
  Future<Map> linkedInLoginAuth() {
    // TODO: implement linkedInLoginAuth
    return Future.value({});
  }

  @override
  ossUploadImage(BuildContext context, String accessToken, String name, MultipartFile data, Function(String p1) success,
      Function fail) {
    Application.saveToken(accessToken);
    UploadImage.uploadImage1(context, data, name, (String imgUrl) {
      if (imgUrl.isNotEmpty) {
        success(imgUrl);
      } else {
        fail();
      }
    }, (errorMsg) {
      fail();
    });
  }

  @override
  Future<Map> phoneOneKeyBinding() {
    // TODO: implement phoneOneKeyBinding
    return Future(() => {"action": "switchPhoneLogin"});
  }

  @override
  Future<Map> phoneOneKeyLogin() {
    // TODO: implement phoneOneKeyLogin
    return ToNativeMethodChannel.sharedInstance().SIMAccountOneKeyLogin(_oneKeyAuthSecret);
  }

  @override
  Future<Map> phoneOneKeyRegister() {
    // TODO: implement phoneOneKeyRegister
    return ToNativeMethodChannel.sharedInstance().SIMAccountOneKeyRegister(_oneKeyAuthSecret);
  }

  @override
  Future<Map> qqLoginAuth() {
    // TODO: implement qqLoginAuth
    return Future.value({});
  }

  @override
  Future<int> requestCameraPermissionIfNecessary() {
    return ToNativeMethodChannel.sharedInstance().authorizationStatus();
  }

  @override
  track(String name, String code, {Map? ext}) {
    // TODO: implement track
    if(ext?.isNotEmpty ?? false) {
      ToNativeMethodChannel().sendTrackingToNative({"track": name, "posCode": code, "ext": ext});
    }
    else{
      ToNativeMethodChannel().sendTrackingToNative({"track": name, "posCode": code});
    }
  }

  @override
  Future<Map> twitterLoginAuth() {
    // TODO: implement twitterLoginAuth
    return Future.value({});
  }

  @override
  Future<Map> wechatLoginAuth() {
    // TODO: implement wechatLoginAuth
    Log.d("微信登录");
    return Future.value({});
  }

  @override
  Future<Map> weiboLoginAuth() {
    // TODO: implement weiboLoginAuth
    return Future.value({});
  }

  @override
  back2Native(Map<String, dynamic> ext) {
    ToNativeMethodChannel().goToNativePage(ext);
  }

  @override
  bool back2LastPage() {
    if (BoostNavigator.instance.pageSize() > 1) {
      BoostNavigator.instance.pop();
      return true;
    }
    return false;
  }

  @override
  callLoginSuccess(String? loginData) {
    if (!Constant.inProduction) {
      Fluttertoast.showToast(
          msg: "ipRegion: ${InternationalHandler.sharedInstance().ipRegion}, "
              "deviceRegion: ${InternationalHandler.sharedInstance().deviceRegion}, "
              "applyRegion: ${InternationalHandler.sharedInstance().applyRegion}");
    }
    ToNativeMethodChannel.sharedInstance().reportLoginSuccess(loginData);
  }

  @override
  Future<void> configInit() async {
    //根据app当前语言获取登录配置文件
    // String content = await rootBundle.loadString('assets/translation/zh-hans.json');
    // String contentLogin = json.encode(Application.loginLanguageData);
    // Map<String, dynamic> intlParam = json.decode(content);
    // Map<String, dynamic> intlLogin = json.decode(contentLogin);
    // Map<String, dynamic> paramData = intlParam["data"];
    // if (paramData.isEmpty) {
    //   paramData = {};
    // }
    // paramData.addAll(intlLogin["data"]);
    // Application.intl = InternationalInfoController(intlParam);
    // String languageType = intlLogin["languageCode"];
    // LoginPluginConfig.sharedInstance()
    //   ..intl = Application.intl
    //   ..httpClient = LoginHttpClient()
    //   ..baseURL = ""
    //   ..loading = JCLoadingUtil()
    //   ..accountFastLoginLocalCheck = true;

    String languageType = Application.loginLanguageType;
    InternationalInfoController loginPluginInterNationalNiim = Application.intl;
    LoginPluginHostApiNiimbot loginPluginHostApiNiimbot = LoginPluginHostApiNiimbot();
    LoginPluginConfig.sharedInstance().socialConfig = [
      LoginPluginSocialConfig(LoginSelectionType.line, key: "**********"),
      LoginPluginSocialConfig(LoginSelectionType.google), //google授权登录的配置信息都在原生了,此处不用配置,声明即可
      LoginPluginSocialConfig(LoginSelectionType.apple,
          androidKey: "services.com.niimbot.cxyprinter", redirectUri: "https://www.niimbot.com"),
      LoginPluginSocialConfig(LoginSelectionType.wechat,
          key: "wxece69872671e1254", redirectUri: "https://www.niimbot.com"),
      LoginPluginSocialConfig(LoginSelectionType.qq, key: "*********", redirectUri: "https://www.niimbot.com")
    ];
    Log.d("登录组件UA：${Application.agent}");
    LoginPluginConfig.sharedInstance().configDio(DioUtils().getDio());
    LoginPluginConfig.sharedInstance().configLogin(loginPluginHostApiNiimbot,
        configMap: {
          "agreementUrl": "https://print.niimbot.com/h5#/agree?type=1&lang=$languageType",
          "privacyUrl": "https://print.niimbot.com/h5#/agree?type=2&lang=$languageType",
          "phoneLoginSupportAllRegion": true,
          "headers": {"niimbot-user-agent": Application.agent, "languageCode": languageType}
        },
        configIntl: loginPluginInterNationalNiim);
    DioUtils.instance.configCustomHeaders({"niimbot-user-agent": Application.agent});
    // 因画板默认使用根导航进行push与pop
    LoginPluginApi.init(customLanguageResource: true, callFromNative: true, isBackOnRootNavigator: true);
    LoginResultHandler.sharedInstance().clearStackLoginResult = (backContext, loginType, loginData, loginBusinessData) {
      Map result = {};
      result['loginData'] = loginData;
      result['loginBusinessData'] = loginBusinessData;
      result['loginType'] = loginType.index;
      if (loginType != NiimbotLoginType.none) {
        LoginResultHandlerNiim.updateUserInfo(loginData, loginBusinessData: loginBusinessData);
        ToNativeMethodChannel.sharedInstance().reportLoginSuccess(JsonUtil.encodeObj(result));
        // 云打印单独处理，返回带参数
        // 先回到登陆页
        LoginNavigatorUtils.goBackUntil(backContext, 'loginHomePage');
        // 再推出登陆页
        // LoginNavigatorUtils.goBack(backContext, name: 'loginHomePage');
        ///登录注册组件的调用改为显示名称调用，加入到FlutterBoost的页面管理
        ///因此此处可使用FlutterBoost的导航回退
        BoostNavigator.instance.pop();
      } else {
        back2Native({'vcName': 'sourcePage', 'needConnectPrinter': false, "popDeriction": "left"});
      }
      Log.e(
          "登录页面回调-->loginData: ${JsonUtil.encodeObj(loginData)}, loginBusinessData: ${JsonUtil.encodeObj(loginBusinessData)}");
    };
    LoginResultHandler.sharedInstance().cancelLogin = () {
      ToNativeMethodChannel.sharedInstance().cancelLogin();
      UserLoginHelper().cancelLoginCallback?.call();
    };
    bool checkSIMCardStatus = await ToNativeMethodChannel.sharedInstance().checkSIMCardInfo();
    LoginPluginApi.setSIMCardInfo(checkSIMCardStatus);
    Application.syncLanguageCodeToLoginPlugin();
    return Future.value();
  }

  /// 请求极验验证
  /// regionCode：国家码，eg：cn
  /// phoneCode：国家区号，eg：86
  /// phone：电话号码
  @override
  Future<Map<String, dynamic>> requestGeetestVerify(String regionCode, String phoneCode, String phone,{bool needGeetestVerify = false}) {
    bool needGeetest = needGeetestVerify;
    if(!needGeetestVerify) {
      needGeetest = AppHelper().needGeetestVerify(phoneCode);
    }else {
      needGeetest = needGeetestVerify;
    }
    Completer<Map<String, dynamic>> completer = Completer<Map<String, dynamic>>();
    return _requestGeetestVerifyInternal(regionCode, phoneCode, phone, needGeetest, completer, (code, message, callback){
      if (code == 50009) {
        _processIpLockWhenLogin(regionCode, phoneCode, phone, completer);
      } else {
        if (code == 50008) {
          AppHelper().requestAppConfig();
        }
        callback['errorMessage'] = message;
        completer.complete(callback);
      }
    });
  }

  ///needGeetestVerify: 是否需要极验验证
  Future<Map<String, dynamic>> _requestGeetestVerifyInternal(
      String regionCode,
      String phoneCode,
      String phone,
      bool needGeetestVerify,
      Completer<Map<String, dynamic>> completer,
      Function(int, String, Map<String, dynamic>)? onError) {
    Map<String, dynamic> callback = {'result': false, 'errorMessage': ''};

    ToNativeMethodChannel.requestGeetestVerify(phoneCode, phone, needGeetestVerify).then((map) {
      bool result = _parseBoolField(map['result']);
      String response = map['response'];

      if (!result) {
        completer.complete(callback);
        return;
      }

        Map<String, dynamic> geetestParams = {};
      if (response.isNotEmpty) {
        Map<String, dynamic> rspMap = json.decode(response);
        // 使用列表推导式简化字段提取逻辑
        ['lot_number', 'captcha_output', 'pass_token', 'gen_time'].forEach((key) {
          if (rspMap.containsKey(key) && rspMap[key]!.isNotEmpty) {
            geetestParams[toCamelCase(key)] = rspMap[key]!;
          }
        });
      }
      Map<String, dynamic> requestParams = {
        'account': phone,
        'areaCode': phoneCode,
        ...geetestParams,
      };
      Map<String, dynamic> urlObjc = {
        'path': '/oauth/sendVerifyCode',
        'needLogin': false,
        'method': 'POST',
      };

      DioUtils.instance.requestNetwork<bool>(Method.post, urlObjc, params: requestParams,
          onSuccess: (_) {
            callback['result'] = true;
            completer.complete(callback);
          },
          onError: (code, message) {
            if (onError != null) {
              onError(code, message, callback);
            }
          }, needEncrypt: true);
    }).catchError((error) {
      // 处理可能的错误情况
      completer.complete(callback);
    });

    return completer.future;
  }

  /// 对于锁ip的用户调用极验验证处理
  void _processIpLockWhenLogin(
      String regionCode,
      String phoneCode,
      String phone,
      Completer<Map<String, dynamic>>
      completer) {
    _requestGeetestVerifyInternal(regionCode, phoneCode, phone, true, completer, (code, message, callback){
      if (code == 50008) {
        AppHelper().requestAppConfig();
      }
      callback['errorMessage'] = message;
      completer.complete(callback);
    });
  }

  bool _parseBoolField(dynamic field) {
  if (field is int) {
    return field == 1 ? true : false;
  }
  return field;
}

  /// 辅助函数：将下划线命名的字符串转换为驼峰命名
  String toCamelCase(String s) {
    return s.replaceAllMapped(RegExp(r'_(.)'), (Match m) => m.group(1)!.toUpperCase());
  }

  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}
