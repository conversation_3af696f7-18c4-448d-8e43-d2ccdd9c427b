//
//
//
//
//  Created by lbxia on 15/10/21.
//  Copyright © 2015年 lbxia. All rights reserved.
//

#import "QQLBXScanViewController.h"
//#import "CreateBarCodeViewController.h"
#import <HMSegmentedControl/HMSegmentedControl.h>
#import "LBXScanVideoZoomView.h"
#import "JCCatCollectionViewCell.h"
#import "JCSanResultViewController.h"
#import "FlutterBoostUtility.h"
#import "NMFAWebViewController.h"
#import "XYTool.h"
#import "JCLabelAppModel.h"

@interface QQLBXScanViewController ()<UICollectionViewDataSource, UICollectionViewDelegate>
@property (nonatomic, strong) LBXScanVideoZoomView *zoomView;
@property (strong, nonatomic) NSArray *titleArr;
@property (nonatomic, strong) UICollectionView *titleCollectionView;
@property (nonatomic, assign) NSInteger selectIndex;
@end

@implementation QQLBXScanViewController


- (void)viewDidLoad
{
    [super viewDidLoad];
    // Do any additional setup after loading the view.

    if ([self respondsToSelector:@selector(setEdgesForExtendedLayout:)]) {
        self.edgesForExtendedLayout = UIRectEdgeNone;
    }
    self.view.backgroundColor = [UIColor blackColor];
    self.isOpenInterestRect = YES;
    //设置扫码后需要扫码图像
    self.isNeedScanImage = YES;
    self.lsl_prefersNavigationBarHidden = YES;
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    [self drawBottomItems];
    [self drawTitle];
    [self.view bringSubviewToFront:_topTitleLabel];
    [self.view bringSubviewToFront:_detailDresprLabel];
}

//绘制扫描区域
- (void)drawTitle
{
    if (!_detailDresprLabel)
    {

        CGSize sizeRetangle = CGSizeMake(SCREEN_WIDTH - 60*2, SCREEN_WIDTH - 60*2);
        CGFloat yMinRetangle = SCREEN_HEIGHT / 2.0 - sizeRetangle.height/2.0 - 60;
        self.detailDresprLabel = [[UILabel alloc]init];
        _detailDresprLabel.bounds = CGRectMake(0, 0, sizeRetangle.width, 100);
        _detailDresprLabel.centerX = CGRectGetWidth(self.view.frame)/2;
        _detailDresprLabel.top = yMinRetangle + sizeRetangle.height + 20;
        _detailDresprLabel.textAlignment = NSTextAlignmentCenter;
        _detailDresprLabel.numberOfLines = 0;

        if(self.scanCreateType == 0){
            _detailDresprLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app01288",@"扫描精臣标签包装盒条码 / 商品条码");
        }else if(self.scanCreateType == 1){
            _detailDresprLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app01288",@"扫描精臣标签包装盒条码 / 商品条码");
        }else if(self.scanCreateType == 3){
            _detailDresprLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app01100",@"扫描一维码即可快速获取商品信息");
        }else if(self.scanCreateType == 4){
            _detailDresprLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app01098",@"扫描精臣标签盒上一维码");
        }else if(self.scanCreateType == 5){
            _detailDresprLabel.text = XY_LANGUAGE_TITLE_NAMED(@"",@"");
        }else if(self.scanCreateType == 6){
            _detailDresprLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app01330",@"扫描标签纸包装盒条码");
        }else if(self.scanCreateType == 7){
          _detailDresprLabel.text = self.isShowScanContent?self.scanContent:@"";
        }
        else{
            _detailDresprLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app00118",@"将取景框对准二维码或条码即可自动扫描");
        }
        if (self.reminder) {
            _detailDresprLabel.text = self.reminder;
            _detailDresprLabel.textAlignment = NSTextAlignmentCenter;
        }
        _detailDresprLabel.textColor = [UIColor whiteColor];
        [self.view addSubview:_detailDresprLabel];
    }
    if (!_topTitleLabel)
    {

        self.topTitleLabel = [[UILabel alloc]init];
        _topTitleLabel.bounds = CGRectMake(0, 0, 200, 100);
        _topTitleLabel.center = CGPointMake(CGRectGetWidth(self.view.frame)/2, 82);
        _topTitleLabel.textAlignment = NSTextAlignmentCenter;
        _topTitleLabel.numberOfLines = 0;

        if(self.scanCreateType == 0){
            _topTitleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app00262",@"扫码取模");
            _topTitleLabel.hidden = YES;
        }else if(self.scanCreateType == 1){
            _topTitleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app00803",@"扫描打印");
            _topTitleLabel.hidden = YES;
        }else if(self.scanCreateType == 3){
            _topTitleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app01097",@"商品库搜索");
        }else if(self.scanCreateType == 4){
            _topTitleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app01099",@"搜索商品");
            _topTitleLabel.hidden = self.isForceHideTopTitle;
        }else if(self.scanCreateType == 5 || self.scanCreateType == 6){
            _topTitleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"",@"");
        }else if(self.scanCreateType == 7){
          _topTitleLabel.text = self.isShowScanTitle?self.scanTitle:@"";
        }else{
            _topTitleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app00826",@"扫描导入");
        }
        _topTitleLabel.textColor = [UIColor whiteColor];
        [self.view addSubview:_topTitleLabel];
    }
}

- (void)cameraInitOver
{
    if (self.isVideoZoom) {
        [self zoomView];
    }
}

- (LBXScanVideoZoomView*)zoomView
{
    if (!_zoomView)
    {

        CGRect frame = self.view.frame;

        int XRetangleLeft = self.style.xScanRetangleOffset;

        CGSize sizeRetangle = CGSizeMake(frame.size.width - XRetangleLeft*2, frame.size.width - XRetangleLeft*2);

        if (self.style.whRatio != 1)
        {
            CGFloat w = sizeRetangle.width;
            CGFloat h = w / self.style.whRatio;

            NSInteger hInt = (NSInteger)h;
            h  = hInt;

            sizeRetangle = CGSizeMake(w, h);
        }

        CGFloat videoMaxScale = [self.scanObj getVideoMaxScale];

        //扫码区域Y轴最小坐标
        CGFloat YMinRetangle = frame.size.height / 2.0 - sizeRetangle.height/2.0 - self.style.centerUpOffset;
        CGFloat YMaxRetangle = YMinRetangle + sizeRetangle.height;

        CGFloat zoomw = sizeRetangle.width + 40;
        _zoomView = [[LBXScanVideoZoomView alloc]initWithFrame:CGRectMake((CGRectGetWidth(self.view.frame)-zoomw)/2, YMaxRetangle + 40, zoomw, 18)];

        [_zoomView setMaximunValue:videoMaxScale/4];


        __weak __typeof(self) weakSelf = self;
        _zoomView.block= ^(float value)
        {
            [weakSelf.scanObj setVideoScale:value];
        };
        [self.view addSubview:_zoomView];

        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(tap)];
        [self.view addGestureRecognizer:tap];
    }

    return _zoomView;

}

- (void)tap
{
    _zoomView.hidden = !_zoomView.hidden;
}

- (void)drawBottomItems
{
    if (_bottomItemsView) {

        return;
    }
    float bottomHeight = iPhoneX?83:52;
    if(self.scanCreateType == 0 || self.scanCreateType == 1){
        self.bottomItemsView = [[UIView alloc]initWithFrame:CGRectMake(0, SCREEN_HEIGHT-bottomHeight,
                                                                          SCREEN_WIDTH, bottomHeight)];
        self.bottomItemsView.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.6];
        [self.view addSubview:self.bottomItemsView];
//        if([XY_JC_LANGUAGE_REAL hasPrefix:@"zh-"] && !self.isFromGoodsScan){
      if(!self.isFromGoodsScan){
            NSArray *titleArr = @[XY_LANGUAGE_TITLE_NAMED(@"app01285", @"自动识别"),XY_LANGUAGE_TITLE_NAMED(@"app01286", @"识标签"),XY_LANGUAGE_TITLE_NAMED(@"app01287", @"扫商品")];
            self.titleArr = titleArr;
            [self initTitleSegment];
        }else{
            self.bottomItemsView.hidden = YES;
            self.selectIndex = 1;
        }
    }

    self.btnClose = [UIButton buttonWithType:UIButtonTypeCustom];//我的二维码->退出
    self.btnClose.frame = CGRectMake(SCREEN_WIDTH - 60, StatusBarHeight, 50, 50);
    [self.btnClose setImage:[UIImage imageNamed:@"scan_close"] forState:UIControlStateNormal];
    [self.btnClose setImage:[UIImage imageNamed:@"scan_close"] forState:UIControlStateHighlighted];
    [self.btnClose addTarget:self action:@selector(clickCancel) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.btnClose];
    self.btnClose.leading = SCREEN_WIDTH - 60;
    if(self.scanCreateType == 7 && self.isShowScanIcon){
      self.btnFlash = [UIButton buttonWithType:UIButtonTypeCustom];//闪光灯->隐藏
      self.btnFlash.frame = CGRectMake((SCREEN_WIDTH - 52)/2, SCREEN_HEIGHT-bottomHeight-23-50, 52, 52);
      self.btnFlash.selected = NO;
      [self.btnFlash setImage:XY_IMAGE_NAMED(@"flash_close") forState:UIControlStateNormal];
      [self.btnFlash setImage:XY_IMAGE_NAMED(@"flash_open") forState:UIControlStateSelected];
      [self.btnFlash addTarget:self action:@selector(openOrCloseFlash) forControlEvents:UIControlEventTouchUpInside];
      [self.view addSubview:self.btnFlash];
      self.btnFlash.leading = 36;
    }else{
      self.btnFlash = [UIButton buttonWithType:UIButtonTypeCustom];//闪光灯->隐藏
      self.btnFlash.frame = CGRectMake(36, SCREEN_HEIGHT-bottomHeight-23-50, 52, 52);
      self.btnFlash.selected = NO;
      [self.btnFlash setImage:XY_IMAGE_NAMED(@"flash_close") forState:UIControlStateNormal];
      [self.btnFlash setImage:XY_IMAGE_NAMED(@"flash_open") forState:UIControlStateSelected];
      [self.btnFlash addTarget:self action:@selector(openOrCloseFlash) forControlEvents:UIControlEventTouchUpInside];
      [self.view addSubview:self.btnFlash];
      self.btnFlash.leading = 36;

      self.btnPhoto = [UIButton buttonWithType:UIButtonTypeCustom];//photo
      self.btnPhoto.frame = CGRectMake(SCREEN_WIDTH - 88, SCREEN_HEIGHT-bottomHeight-23-52, 52, 52);
      [self.btnPhoto setImage:XY_IMAGE_NAMED(@"selectPhoto") forState:UIControlStateNormal];
      [self.btnPhoto setImage:XY_IMAGE_NAMED(@"selectPhoto") forState:UIControlStateHighlighted];
      [self.btnPhoto addTarget:self action:@selector(openPhoto:) forControlEvents:UIControlEventTouchUpInside];
      [self.view addSubview:self.btnPhoto];
      self.btnPhoto.leading = SCREEN_WIDTH - 88;
    }

}

- (void)initTitleSegment{
    CGRect collectionViewFrame = CGRectMake(50, 0, SCREEN_WIDTH - 100, 53);
    UICollectionViewFlowLayout *flowLayout = [[UICollectionViewFlowLayout alloc] init];
    flowLayout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
    flowLayout.minimumLineSpacing = 10;
    flowLayout.minimumInteritemSpacing = 10;

    UICollectionView *collectionView = [[UICollectionView alloc] initWithFrame:collectionViewFrame collectionViewLayout:flowLayout];
    collectionView.backgroundColor = UIColor.clearColor;
    collectionView.showsHorizontalScrollIndicator = NO;
    collectionView.delegate = self;
    collectionView.dataSource = self;
    [collectionView registerNib:@"JCCatCollectionViewCell"];
    [self.bottomItemsView addSubview:collectionView];
    self.selectIndex = 0;
    [collectionView reloadData];

}


- (void)showError:(NSString*)str
{
//    [LBXAlertAction showAlertWithTitle:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") msg:str buttonsStatement:@[@"知道了"] chooseBlock:nil];
}

- (void)scanResultWithArray:(NSArray<LBXScanResult*>*)array
{
    if (array.count < 1)
    {
        [self popAlertMsgWithScanResult:nil];

        return;
    }

    //经测试，可以同时识别2个二维码，不能同时识别二维码和条形码
    for (LBXScanResult *result in array) {

        NSLog(@"scanResult:%@",result.strScanned);
    }

    LBXScanResult *scanResult = array[0];

    NSString*strResult = scanResult.strScanned;

    self.scanImage = scanResult.imgScanned;
    if (!strResult) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self popAlertMsgWithScanResult:nil];
        });
        return;
    }

    //震动提醒
   // [LBXScanWrapper systemVibrate];
    //声音提醒
    //[LBXScanWrapper systemSound];

    [self showNextVCWithScanResult:scanResult];

}

- (void)popAlertMsgWithScanResult:(NSString*)strResult
{
    if (!strResult) {
        [self stopScan];
        [JCAlert showAlertView:@"" message:XY_LANGUAGE_TITLE_NAMED(@"app100000049", @"未识别到条码/二维码") cancelButtonTitle:@"" sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707",@"我知道了") cancelBlock:^{

        } sureBlock:^{
            [self reStartDevice];
        }];
    }
}

- (void)showNextVCWithScanResult:(LBXScanResult*)strResult
{
    NSString *scanedString = strResult.strScanned;
    if(self.scanCreateType == 2 || self.scanCreateType == 5 || self.scanCreateType == 7){
        if (self.scanResultBlock) {
            self.scanResultBlock(strResult);
        }
    }else{
        if([scanedString isPureNumandCharacters]){
            if (self.scanResultBlock) {
                self.scanResultBlock(strResult);
            }
            if (self.scanMergeBlock){
                self.scanMergeBlock(strResult,@(self.selectIndex));
            }
        }else if ([scanedString hasPrefix:@"http://"] || [scanedString hasPrefix:@"https://"]){
            if(NETWORK_STATE_ERROR){
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
                return;
            }

            // 拆分URL
            NSURLComponents *components = [NSURLComponents componentsWithString:scanedString];
            // 扫码登录
            if ([components.path isEqualToString:@"/niimbotScan"]) {
                NSURLQueryItem *scanCodeItem = components.queryItems.firstObject;
                // 扫码接口返回
                void (^scanCode)(NSURLQueryItem *, void(^)(BOOL)) = ^(NSURLQueryItem *scanCodeItem, void(^isScannedCodeBlock)(BOOL)) {
                    if (scanCodeItem.value) {
                        NSString *graphqlStr = @"mutation scannedCode($code: String!) {"
                                                          "scannedCode(code: $code)"
                                                "}";
                        [@{@"code":scanCodeItem.value} jc_graphQLRequestWith:graphqlStr hud:@"" graphQLType:@"query" Success:^(__kindof YTKBaseRequest *request, NSDictionary *requestDic) {
                            NSNumber *isScannedCode = requestDic[@"scannedCode"];
                            isScannedCodeBlock(YES);
                        } failure:^(NSString *msg, id model) {
                            if ([model isEqualToString:@"scan_code_not_found"]) {
                                msg = XY_LANGUAGE_TITLE_NAMED(@"app100001274",@"二维码已失效或不存在");
                            }
                            [MBProgressHUD showToastWithMessageDarkColor:msg];
                            isScannedCodeBlock(NO);
                        }];
                    } else {
                        return isScannedCodeBlock(NO);
                    }
                };

                // 跳入Flutter授权登录页
                void (^gotoScanCodeLogin)(void) = ^void() {
                    scanCode(scanCodeItem, ^(BOOL isScannedCode) {
                        if (isScannedCode) {
                            dispatch_async(dispatch_get_main_queue(), ^{
                                [FlutterBoostUtility gotoFlutterPage:@"scanCodeLogin"
                                                           arguments:@{@"client":@"pc",@"scanCode": scanCodeItem.value}
                                                      onPageFinished: ^(NSDictionary *dic) {
                                    NSNumber *isSuccess = dic[@"isSuccess"];
                                    if ([isSuccess boolValue]) {
                                        [MBProgressHUD showSuccess:XY_LANGUAGE_TITLE_NAMED(@"app100001267",@"登录成功！")];
                                    }
                                }];
                            });
                        }
                    });
                };

                // 是否登录
                if (!xy_isLogin) {
                    [[JCLoginManager sharedInstance] presentLoginViewController:^{

                    } viewController:self loginSuccessBlock:^{
                        [self dismissViewControllerAnimated:YES completion:^{
                            gotoScanCodeLogin();
                        }];
                    }];
                    return;
                } else {
                    [self dismissViewControllerAnimated:YES completion:^{
                        gotoScanCodeLogin();
                    }];
                }
                return;
            } else {
                XYWKWebViewController *vc = [[XYWKWebViewController alloc] initWithUrl:scanedString];
                vc.showWebTitle = YES;
                [self.navigationController pushViewController:vc animated:YES];
                return;
            }
        } else if ([scanedString hasPrefix:@"client="] && [scanedString containsString:@"authCode="]) {
          NSString *processedContent = [[[scanedString
              stringByReplacingOccurrencesOfString:@"client=" withString:@""]
              stringByReplacingOccurrencesOfString:@"authCode=" withString:@""]
              stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
          NSArray *splitArray = [processedContent componentsSeparatedByString:@"&&"];
          if ([splitArray count] != 2){
              return;
          }

          // 跳入Flutter授权登录页
          void (^gotoScanCodeLogin)(NSArray *splitArray) = ^void(NSArray *splitArray) {
            // 从Flutter端获取授权码
            [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"loginScanCode" arguments:@{
              @"client":splitArray.firstObject,
              @"loginCode":splitArray.lastObject
            } result:^(id value) {
                NSString *error = XY_LANGUAGE_TITLE_NAMED(@"app100000328", @"登录失败");

                if ([value isKindOfClass:[NSDictionary class]] && IsNotEmptyDictionary(value)) {
                  NSDictionary *resultDict = (NSDictionary *)value;

                  BOOL isScannedCode = ((NSNumber *)resultDict[@"result"]).boolValue;
                  NSString *serverError = resultDict[@"error"];
                  if (serverError && [serverError isKindOfClass:[NSString class]] && serverError.length > 0) {
                      error = serverError;
                  }
                if (isScannedCode) {
                  dispatch_async(dispatch_get_main_queue(), ^{
                    [FlutterBoostUtility gotoFlutterPage:@"scanCodeLogin"
                                               arguments:@{@"client":splitArray.firstObject,@"scanCode": splitArray.lastObject}
                                          onPageFinished: ^(NSDictionary *dic) {
                      NSNumber *isSuccess = dic[@"isSuccess"];
                      if ([isSuccess boolValue]) {
                          [MBProgressHUD showSuccess:XY_LANGUAGE_TITLE_NAMED(@"app100001267",@"登录成功！")];
                      }
                    }];
                  });
                } else {
                    [MBProgressHUD showSuccess:error];
                }
              }
            }];
          };

          // 是否登录
          if (!xy_isLogin) {
              [[JCLoginManager sharedInstance] presentLoginViewController:^{

              } viewController:self loginSuccessBlock:^{
                  [self dismissViewControllerAnimated:YES completion:^{
                      gotoScanCodeLogin(splitArray);
                  }];
              }];
              return;
          } else {
              [self dismissViewControllerAnimated:YES completion:^{
                  gotoScanCodeLogin(splitArray);
              }];
          }
        } else if ([scanedString hasPrefix:@"gz-asset:assetId="] || [scanedString hasPrefix:@"gz-invite:company="]) {
            // 识别到固资相关信息，打开固资页面
            NSDictionary *guziInfo = [self parseAsset:scanedString];
            if (guziInfo && [guziInfo count] > 0) {
                // 跳入固资页面
                void (^gotoNMFAVC)(void) = ^void() {
                  [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"getAuthCode" arguments:nil result:^(id value) {
                      if (!STR_IS_NIL(value)) {
                          // 构建参数后缀
                          NSMutableString *suffix = [NSMutableString string];

                          // 首先添加type参数（冒号前面的值）
                          NSString *typeValue = [guziInfo objectForKey:@"type"];
                          if (typeValue && [typeValue isKindOfClass:[NSString class]] && typeValue.length > 0) {
                              [suffix appendFormat:@"&type=%@", typeValue];
                          }

                          // 添加其他参数
                          for (NSString *key in guziInfo.allKeys) {
                              if (![key isEqualToString:@"type"] && key.length > 0) { // 已经添加过type了
                                  NSString *paramValue = [guziInfo objectForKey:key];
                                  if (paramValue && [paramValue isKindOfClass:[NSString class]] && paramValue.length > 0) {
                                      [suffix appendFormat:@"&%@=%@", key, paramValue];
                                  }
                              }
                          }

                          // 构建固资页面URL
                          NSArray *appArr = [XYCenter sharedInstance].appArr;
                          JCLabelAppModel *currentAppModel = nil;
                          for (JCLabelAppModel *appModel in appArr) {
                            if ([appModel.name isEqualToString:@"app100001963"]) {
                              currentAppModel = appModel;
                              break;
                            }
                          }
                          if (currentAppModel != nil) {
                            NSString *baseUrl = currentAppModel.router;
                            NSString *loadUrl = [NSString stringWithFormat:@"%@?authCode=%@%@", baseUrl, value, suffix];

    #ifdef DEBUG
                            NSString *assetsHost = [[NSUserDefaults standardUserDefaults] stringForKey:@"LaboratoryTypeAssetsDomain"];
                            if (assetsHost && [assetsHost isKindOfClass:[NSString class]] && assetsHost.length > 0) {
                              loadUrl = [NSString stringWithFormat:@"%@?authCode=%@%@", assetsHost, value, suffix];
                            }
    #endif
                            // 创建并打开固资页面
                            NMFAWebViewController *webViewController = [[NMFAWebViewController alloc] init];
                            webViewController.isSupportShare = NO;
                            [webViewController loadUrl:loadUrl];
                            UIViewController *topVc = [XYTool getCurrentVC];
                            if (topVc && topVc.navigationController) {
                              [topVc.navigationController pushViewController:webViewController animated:YES];
                            } else {
                              // 如果找不到当前视图控制器或没有导航控制器，回退到当前视图控制器
                              UINavigationController *nav = [[UINavigationController alloc] initWithRootViewController:webViewController];
                              [self presentViewController:nav animated:YES completion:nil];
                            }
                          }
                      }
                  }];
                };

              // 是否登录
              if (!xy_isLogin) {
                  [[JCLoginManager sharedInstance] presentLoginViewController:^{

                  } viewController:self loginSuccessBlock:^{
                      [self dismissViewControllerAnimated:YES completion:^{
                          gotoNMFAVC();
                      }];
                  }];
                  return;
              } else {
                  [self dismissViewControllerAnimated:YES completion:^{
                      gotoNMFAVC();
                  }];
              }
            }
        } else {
            JCSanResultViewController *vc = [[JCSanResultViewController alloc] initWithContent:scanedString];
            vc.title = XY_LANGUAGE_TITLE_NAMED(@"app01050", @"扫描结果");
            [self.navigationController pushViewController:vc animated:YES];
            return;
        }
    }
    [self dismissViewControllerAnimated:YES completion:nil];
}

// 解析固资或邀请二维码
- (NSDictionary *)parseAsset:(NSString *)input {
    if (!input || ![input isKindOfClass:[NSString class]] || input.length == 0) {
        return nil;
    }

    @try {
        NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"([a-zA-Z0-9-]+):([a-zA-Z0-9]+)=([a-zA-Z0-9]+)(?:&([a-zA-Z0-9]+)=([a-zA-Z0-9]+))?" options:0 error:nil];
        NSTextCheckingResult *match = [regex firstMatchInString:input options:0 range:NSMakeRange(0, [input length])];

        if (match && match.numberOfRanges >= 4) { // 至少有type, key1, value1
            NSMutableDictionary *result = [NSMutableDictionary dictionary];

            // 安全地提取子字符串，避免越界
            NSRange typeRange = [match rangeAtIndex:1];
            NSRange key1Range = [match rangeAtIndex:2];
            NSRange value1Range = [match rangeAtIndex:3];

            if (typeRange.location != NSNotFound && key1Range.location != NSNotFound && value1Range.location != NSNotFound) {
                NSString *type = [input substringWithRange:typeRange];
                NSString *key1 = [input substringWithRange:key1Range];
                NSString *value1 = [input substringWithRange:value1Range];

                if (type.length > 0 && key1.length > 0) {
                    [result setObject:type forKey:@"type"];
                    [result setObject:value1 forKey:key1];

                    // 检查是否有第二个键值对
                    if (match.numberOfRanges >= 6) {
                        NSRange key2Range = [match rangeAtIndex:4];
                        NSRange value2Range = [match rangeAtIndex:5];

                        if (key2Range.location != NSNotFound && value2Range.location != NSNotFound) {
                            NSString *key2 = [input substringWithRange:key2Range];
                            NSString *value2 = [input substringWithRange:value2Range];

                            if (key2.length > 0) {
                                [result setObject:value2 forKey:key2];
                            }
                        }
                    }

                    return result;
                }
            }
        }

        // 如果正则表达式匹配失败，尝试手动解析
        if ([input hasPrefix:@"gz-asset:"] || [input hasPrefix:@"gz-invite:"]) {
            NSMutableDictionary *result = [NSMutableDictionary dictionary];

            // 提取类型 (gz-asset 或 gz-invite)
            NSRange colonRange = [input rangeOfString:@":"];
            if (colonRange.location != NSNotFound) {
                NSString *type = [input substringToIndex:colonRange.location];
                [result setObject:type forKey:@"type"];

                // 提取参数部分
                NSString *paramsString = [input substringFromIndex:colonRange.location + 1];
                NSArray *params = [paramsString componentsSeparatedByString:@"&"];

                for (NSString *param in params) {
                    NSArray *keyValue = [param componentsSeparatedByString:@"="];
                    if (keyValue.count == 2) {
                        NSString *key = keyValue[0];
                        NSString *value = keyValue[1];
                        if (key.length > 0) {
                            [result setObject:value forKey:key];
                        }
                    }
                }

                if (result.count > 1) { // 至少有type和一个其他参数
                    return result;
                }
            }
        }
    } @catch (NSException *exception) {
        NSLog(@"解析固资码异常: %@", exception);
    }

    return nil;
}

#pragma mark -底部功能项
//打开相册
- (void)openPhoto:(UIButton *)sender
{
    if(!sender.isEnabled){
        return;
    }
    sender.enabled = NO;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        sender.enabled = YES;
    });
    if ([LBXScanPermissions cameraPemission])
        [self openLocalPhoto:NO];
    else
    {
        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app01293",@"请在“设置-隐私-相册” ， 允许精臣云打印访问你的手机相册") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00297",@"设置") cancelBlock:^{

        } sureBlock:^{
            NSURL * url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            if([[UIApplication sharedApplication] canOpenURL:url]) {
                NSURL*url =[NSURL URLWithString:UIApplicationOpenSettingsURLString];
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
            }
        }];
    }
}

//开关闪光灯
- (void)openOrCloseFlash
{
    @try {
        [super openOrCloseFlash];
        if (self.isOpenFlash)
        {
            self.btnFlash.selected = YES;
        }else{
            self.btnFlash.selected = NO;
        }
    } @catch (NSException *exception) {

    } @finally {

    }
}

- (void)clickCancel{
    [self dismissViewControllerAnimated:YES completion:^{
        if(self.scanBlock){
            self.scanBlock(@"");
        }
        if (self.scanResultBlock) {
            self.scanResultBlock(nil);
        }
    }];
}

#pragma mark -底部功能项
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.titleArr.count;
}


- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    NSString *titleName = [self.titleArr safeObjectAtIndex:indexPath.row];
    JCCatCollectionViewCell *cell =  [collectionView dequeueReusableCellWithReuseIdentifier:@"JCCatCollectionViewCell" forIndexPath:indexPath];
    BOOL isSelected = NO;
    if(self.selectIndex == indexPath.row){
        isSelected = YES;
    }
    [cell setTitleString:titleName isSelected:isSelected];
    return cell;
}

- (CGSize)collectionView:(UICollectionView*)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath*)indexPath{
    float width = (SCREEN_WIDTH - 100 - (self.titleArr.count - 1) * 10)/self.titleArr.count;
    return CGSizeMake(width, 50);
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    if(indexPath.row == 2){
        self.selectIndex = indexPath.row;
        self.detailDresprLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app01100",@"扫描一维码即可快速获取商品信息");
        JC_TrackWithparms(@"click",@"045_116",(@{@"b_name":@"扫商品"}));
    }else{
        self.selectIndex = indexPath.row;
        if(self.selectIndex == 0){
            JC_TrackWithparms(@"click",@"045_116",(@{@"b_name":@"自动识别"}));
            self.detailDresprLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app01288",@"扫描精臣标签包装盒条码 / 商品条码");
        } else if(self.selectIndex == 1){
            JC_TrackWithparms(@"click",@"045_116",(@{@"b_name":@"识标签"}));
            self.detailDresprLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app01098",@"扫描标签包装的条码创建");
        } else if(self.selectIndex == 2){
            JC_TrackWithparms(@"click",@"045_116",(@{@"b_name":@"扫商品"}));
            self.detailDresprLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app01100",@"扫描一维码即可快速获取商品信息");
        }
    }
    [collectionView reloadData];
}

- (void)myQRCode
{
//    CreateBarCodeViewController *vc = [CreateBarCodeViewController new];
//    [self.navigationController pushViewController:vc animated:YES];
}




@end
