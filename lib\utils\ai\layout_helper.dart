import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:text/utils/common_fun.dart';

import '../layout_scheme_template_utils.dart';
import 'convert/template_convert_utls.dart';
import 'layout/layout_util.dart';
import 'nlp/nlp_util.dart';

class LayoutHelper {
  static final LayoutHelper _instance = LayoutHelper._internal();

  factory LayoutHelper() {
    return _instance;
  }

  LayoutHelper._internal();

  /// 获取布局模板
  /// [template] 模板数据，包含inputAreas、width、height等字段
  /// 返回处理后的模板数据
  Future<Map<String, dynamic>?> getLayoutTemplate(Map<dynamic, dynamic>? template) async {
    try {
      // 判空处理
      if (template == null) {
        debugPrint('Template cannot be null');
        return null;
      }

      // 1. 从template中取出inputAreas，width，height字段
      final List<dynamic>? inputAreas = LayoutUtil.getInputAreasFromTemplate(template);
      final double width = _parseDoubleValue(template['width']);
      final double height = _parseDoubleValue(template['height']);

      if (width <= 0 || height <= 0) {
        debugPrint('Width and height must be positive values');
        return null;
      }

      // 2. 调用LayoutUtil.initLabelInputAreas获取转换后的json字符串B
      final String jsonStringB = LayoutUtil.initLabelInputAreas(inputAreas, width, height);

      // 3. 调用LayoutSchemeTemplateUtils.parseAreaToTemplate，参数为B、width、height获取模板数据result，并将result['elements']赋值到template['elements']
      final Map<String, dynamic> result = LayoutSchemeTemplateUtils.parseAreaToTemplate(jsonStringB, width, height);

      // 创建模板副本以避免修改原始数据
      final Map<String, dynamic> processedTemplate = Map<String, dynamic>.from(template);
      processedTemplate['elements'] = result['elements'] ?? [];

      // 4. 调用LayoutUtil.parseInputAreaNames获取name属性集合A
      final List<String> nameList = LayoutUtil.parseInputAreaNames(inputAreas);


      // 5. 调用接口获取goodsFields属性集合
      List<String> goodsFields = await getGoodFieldInfoList();
      if (goodsFields.isEmpty) goodsFields = NLPUtils.commodityFields;

      // 5. 调用NLPUtils.getTextSimilarity方法，参数为A、NLPUtils.commodityFields，获取绑定关系C
      final List<TextSimilarityResult> bindingRelations =
          await NLPUtils.getTextSimilarity(nameList, goodsFields);

      // 6. 调用TemplateConvertUtils.convert2DynamicTemplate,参数为template、NLPUtils.commodityFields、C，输出最终模板数据
      final Map<String, dynamic> finalTemplate = await TemplateConvertUtils.convert2DynamicTemplate(
        processedTemplate,
        goodsFields,
        bindingRelations,
      );

      return finalTemplate;
    } catch (e) {
      // 异常处理，返回原始模板或默认模板
      debugPrint('Error in getLayoutTemplate: $e');
      return template != null ? Map<String, dynamic>.from(template) : <String, dynamic>{};
    }
  }

  Future<List<String>> getGoodFieldInfoList() async {
    final goodsImportImpl = CanvasPluginManager().goodsImportImpl;
    Completer<List<String>> completer = Completer<List<String>>();
    goodsImportImpl?.getGoodsField(null, (data) {
      try {
        // 将数据转换为Map列表
        List<Map<String, dynamic>> fieldInfoList = data.map((e) => Map<String, dynamic>.from(e)).toList();

        // 按columnIndex字段正序排序
        fieldInfoList.sort((a, b) {
          int columnIndexA = a['columnIndex'] ?? 0;
          int columnIndexB = b['columnIndex'] ?? 0;
          return columnIndexA.compareTo(columnIndexB);
        });

        // 提取fieldName字段组成数组
        List<String> fieldNames = fieldInfoList
            .map((item) => intlanguage(item['fieldName']?.toString() ?? '', ''))
            .where((fieldName) => fieldName.isNotEmpty)
            .toList();

        completer.complete(fieldNames);
      } catch (e) {
        debugPrint('解析商品字段信息失败: $e');
        completer.complete([]);
      }
    }, (errorCode, errorMsg) {
      debugPrint('获取商品字段信息失败: $errorCode - $errorMsg');
      completer.complete([]);
    });
    return completer.future;
  }

  /// 解析动态值为double类型
  /// [value] 待解析的值
  /// 返回解析后的double值，如果解析失败返回0.0
  double _parseDoubleValue(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }
}
