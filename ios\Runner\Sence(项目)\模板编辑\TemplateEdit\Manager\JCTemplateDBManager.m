//
//  JCTemplateDBManager.m
//  Runner
//
//  Created by xingling xu on 2020/6/2.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCTemplateDBManager.h"
#import "JCTemplateData4DBManager.h"
#import "JCTemplateSaveManager.h"
#import "JCTemplateList.h"
#import "JCTemplateListModel.h"
#import "JCLabelInfoMangerHelper.h"

@implementation JCTemplateDBManager

/**
 * 根据模板ID查询模板详情
 * @param idStr 模板ID
 * @param success 成功回调，返回模板数据
 * @param failed 失败回调
 */
+ (void)db_queryTemplateDataById:(NSString *)idStr success:(void(^)(JCTemplateData *))success failed:(XYBlock)failed {
    [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"nativeGetTemplateDetail" arguments:@{
      @"templateId":idStr,@"fromLocal":@YES
    } result:^(NSString *value) {
      if ([value isKindOfClass:[NSString class]] && !STR_IS_NIL(value)) {
        NSError *error;
        JCTemplateData *templateData = [[JCTemplateData alloc] initWithString:value error:&error];
        if (templateData) {
          success(templateData);
        } else {
          failed(nil);
        }
      } else {
        failed(nil);
      }
    }];
}

/**
 * 保存或更新标签模板数据
 * @param data 要保存的模板数据
 */
+ (void)db_insertOrUpdateTemplateData:(JCTemplateData *)data {
    if(data.profile.extrain.templateType.integerValue == 1 && data.idStr.length > 14){
        data.profile.extrain.templateType = @"0";
    }
    // Call Flutter insertOrUpdateTemplate method
    NSString *templateJsonStr = [data.toDictionary xy_toJsonString];
    [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"insertOrUpdateTemplate" arguments:@{
        @"template": templateJsonStr
    } result:^(id result) {
        NSLog(@"Template inserted successfully");
    }];
}

/**
 * 保存或更新标签模板数据
 * @param data 要保存的模板数据
 */
+ (void)db_insertOrUpdateTemplateData:(JCTemplateData *)data complate:(XYNormalBlock)complate{
    if(data.profile.extrain.templateType.integerValue == 1 && data.idStr.length > 14){
        data.profile.extrain.templateType = @"0";
    }
    // Call Flutter insertOrUpdateTemplate method
    NSString *templateJsonStr = [data.toDictionary xy_toJsonString];
    [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"insertOrUpdateTemplate" arguments:@{
        @"template": templateJsonStr
    } result:^(id result) {
        NSLog(@"Template inserted successfully");
        complate();
    }];
}

/**
 * 根据模板获取其标签纸信息
 * @param currentTemplate 当前模板数据
 * @param result 结果回调，返回标签信息
 */
+ (void)db_getLabelInfoWithTemplate:(JCTemplateData *)currentTemplate result:(void(^)(JCTemplateData *labelInfo))result {
    NSString *labelId = currentTemplate.profile.extrain.labelId;
    NSString *barCode = currentTemplate.profile.barcode;

    if(STR_IS_NIL(barCode)){
        for (NSString *code in currentTemplate.profile.extrain.barcodeCategoryMap.allValues) {
            if(!STR_IS_NIL(code)){
                barCode = code;
            }
        }
    }

    [JCLabelInfoMangerHelper getLocalLabelInfoWith:labelId barCode:barCode result:^(id resultData) {
        if ([resultData isKindOfClass:[JCTemplateData class]]) {
            result(resultData);
        } else {
            result(nil);
        }
    }];
}

/**
 * 获取商品模板列表
 * @param page 页码
 * @param limit 每页数量
 * @param commodityTemplate 商品模板类型
 * @param searchKey 搜索关键词
 * @param success 成功回调，返回模板列表和响应数据
 * @param failed 失败回调
 */
+ (void)nativeGetMyGoodTemplateList:(NSNumber *)page limit:(NSNumber *)limit commodityTemplate:(NSNumber *)commodityTemplate searchKey:(NSString *)searchKey success:(void(^)(NSArray<JCTemplateData *> *templates, NSDictionary *response))success failed:(void(^)(NSString *errorMsg))failed {
    // 调用Flutter方法获取模板列表
    [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"nativeGetMyGoodTemplateList"
                                                          arguments:@{
                                                              @"page": page,
                                                              @"limit": limit,
                                                              @"commodityTemplate": commodityTemplate,
                                                              @"searchKey": searchKey ?: @""
                                                          }
                                                             result:^(id result) {
        if (!result || ![result isKindOfClass:[NSString class]]) {
            if (failed) {
                failed(@"获取模板数据失败");
            }
            return;
        }

        NSData *jsonData = [result dataUsingEncoding:NSUTF8StringEncoding];
        NSError *error;
        NSDictionary *responseDict = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];

        if (error || !responseDict) {
            NSLog(@"解析模板数据错误: %@", error);
            if (failed) {
                failed(error.localizedDescription);
            }
            return;
        }

        // 从返回的JSON数据中提取模板列表
        NSArray *templateJsonList = responseDict[@"list"];
        NSMutableArray *templateDataArray = [NSMutableArray array];

        // 转换模板数据对象
        for (NSDictionary *templateDict in templateJsonList) {
            JCTemplateData *templateData = [[JCTemplateData alloc] initWithDictionary:templateDict error:nil];
            if (templateData) {
                [templateDataArray addObject:templateData];
            }
        }

        if (success) {
            success(templateDataArray, responseDict);
        }
    }];
}

/**
 * 从Flutter层批量获取模板
 * @param pageNumber 页码
 * @param pageSize 每页数量
 * @param isCloudTemplate 是否云模板
 * @param isNeedRequest 是否需要请求网络
 * @param success 成功回调，返回模板列表
 * @param failed 失败回调
 */
+ (void)db_queryTemplateListWithPage:(NSInteger)pageNumber
                                   limit:(NSInteger)pageSize
                       cloudTemplate:(BOOL)isCloudTemplate
                             needRequest:(BOOL)isNeedRequest
                                 success:(void(^)(NSArray *))success
                                  failed:(XYBlock)failed{
  dispatch_group_t group = dispatch_group_create();
  dispatch_group_enter(group);
  NSLog(@"查询本地数据库");
  //先获取本地数据库模板
  [self nativeGetMyTemplateListWithMethodName:@"nativeGetMyTemplateList"
                                   pageNumber:pageNumber
                                        limit:pageSize
                                    fromLocal:YES
                                      success:^(NSArray *templateDatas){
    success(templateDatas);
    dispatch_group_leave(group);
  } failed:^(id x) {
    dispatch_group_leave(group);
    failed(x);
  }];
  if (!isNeedRequest) {
    return;
  }
  dispatch_group_notify(group, dispatch_get_main_queue(), ^{
    //再获取服务端模板，仅当登录或为云模板时请求服务端
    [self nativeGetMyTemplateListWithMethodName:@"nativeGetMyTemplateList"
                                     pageNumber:pageNumber
                                          limit:pageSize
                                      fromLocal:NO
                                        success:success
                                         failed:failed];
  });
}

/**
 * 原生从Flutter层获取模板列表
 * @param methodName 调用的Flutter方法名
 * @param pageNumber 页码
 * @param pageSize 每页数量
 * @param fromLocal 是否从本地获取数据
 * @param success 成功回调，返回模板列表
 * @param failed 失败回调
 */
+ (void)nativeGetMyTemplateListWithMethodName:(NSString *)methodName
                                   pageNumber:(NSInteger)pageNumber
                                        limit:(NSInteger)pageSize
                                    fromLocal:(BOOL)fromLocal
                                      success:(void(^)(NSArray *))success
                                       failed:(XYBlock)failed {
  // 参数校验
  if (STR_IS_NIL(methodName)) {
    if (failed) {
      failed(@"方法名不能为空");
    }
    return;
  }

  // 构建参数字典
  NSDictionary *params = @{
    @"page": @(pageNumber),
    @"limit": @(pageSize),
    @"fromLocal": @(fromLocal)
  };

  [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:methodName arguments:params result:^(NSString *value) {
    if ([value isKindOfClass:[NSString class]] && !STR_IS_NIL(value)) {
      NSArray<JCTemplateData*> *templates = [self getTemplateDatasFromString:value];
      if (success) {
        success(templates);
      }
    } else {
      if (failed) {
        failed(@"获取模板数据失败");
      }
    }
  }];
}

/**
 * JSON字符串序列化为模板model列表
 * @param templateStr JSON字符串
 * @return 模板数据数组
 */
+ (NSArray<JCTemplateData*> *) getTemplateDatasFromString:(NSString *)templateStr{
  NSData *jsonData = [templateStr dataUsingEncoding:NSUTF8StringEncoding];
  id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options: NSJSONReadingMutableContainers error:nil];
  if([jsonObject isKindOfClass:[NSDictionary class]]){
    NSError *error = nil;
    JCTemplateListModel *templateListModel = [[JCTemplateListModel alloc] initWithDictionary:jsonObject error:&error];

    if (error) {
      NSLog(@"解析TemplateListModel失败: %@", error);
      return @[];
    }

    // 计算hasMore属性
    templateListModel.hasMore = (templateListModel.page * templateListModel.limit) < templateListModel.total;

    return templateListModel.list ?: @[];
  } else{
    return @[];
  }
}

/**
 * 删除标签使用信息
 */
+ (void)db_deletLabelUseInfo{
    [[JCFMDB shareDatabase:DB_NAME] jc_deleteAllDataFromTable:TABLE_LABEL_USE_INFO];
}

+ (NSArray<JCTemplateData *> *)getAllDBTemplateData {
    NSArray *temp = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_TEMPLATE_NEW dicOrModel:[JCTemplateData4DB class] whereFormat:nil];
    NSMutableArray<JCTemplateData *> *resultArray = [NSMutableArray array];
    if (temp.count > 0) {
        [temp enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            JCTemplateData *data = [JCTemplateData4DBManager templateDataWith:obj];
            if (data) {
                [resultArray addObject:data];
            }
        }];
    }
    return resultArray;
}

/**
 * 批量插入模板数据
 * 从188267开始自增插入1000条数据
 */
+ (void)batchInsertTemplateData {
    NSInteger baseIdStr = 188267;

    // 先查询ID为188267的模板
    NSArray *temp = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_TEMPLATE_NEW dicOrModel:[JCTemplateData4DB class] whereFormat:@"WHERE idStr='188267'"];

    if (temp.count == 0) {
        NSLog(@"未找到ID为188267的模板数据");
        return;
    }

    // 获取源模板数据
    JCTemplateData4DB *sourceData4DB = temp.firstObject;
    JCTemplateData *sourceTemplate = [JCTemplateData4DBManager templateDataWith:sourceData4DB];

    if (!sourceTemplate) {
        NSLog(@"模板数据转换失败");
        return;
    }

    // 循环复制1000次，ID从188267开始自增
    for (NSInteger i = 0; i < 1000; i++) {
        NSString *idStr = [NSString stringWithFormat:@"%ld", (long)(baseIdStr + i)];

        // 复制模板数据并修改ID
        JCTemplateData *templateData = [sourceTemplate copy];
        templateData.idStr = idStr;

        // 转换为DB模型并保存
        JCTemplateData4DB *data4DB = [JCTemplateData4DBManager dbDataWith:templateData];
        [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:TABLE_TEMPLATE_NEW dicOrModel:data4DB];
    }
}

@end

