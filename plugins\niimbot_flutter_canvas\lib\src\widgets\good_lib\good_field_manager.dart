import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/localization/localization_public.dart';
import 'package:niimbot_flutter_canvas/src/model/good_lib_field_info.dart';
import 'package:niimbot_flutter_canvas/src/widgets/good_lib/good_field_repository.dart';
import 'package:niimbot_flutter_canvas/src/widgets/good_lib/good_lib_field_utils.dart';

/// 商品库字段管理
class GoodFieldManager {
  static final GoodFieldManager _instance = GoodFieldManager._internal();

  factory GoodFieldManager() {
    return _instance;
  }

  GoodFieldManager._internal();

  List<GoodLibFieldInfo> goodFieldList = [];
  GoodFieldRepository fieldRepository = GoodFieldRepository();

  Future<List<GoodLibFieldInfo>> getGoodFields() async {
    if (goodFieldList == null || goodFieldList.isEmpty) {
      goodFieldList = await updateGoodFields();
    }
    return goodFieldList;
  }

  // Future<List<GoodLibFieldInfo>> updateGoodFields() async {
  //   return await fieldRepository.getGoodFieldInfoList();
  // }

  Future<List<GoodLibFieldInfo>> updateGoodFields() async {
    List<GoodLibFieldInfo> temp = await fieldRepository.getGoodFieldInfoList();
    if(temp.isEmpty){
      return goodFieldList;
    }else{
      goodFieldList = temp;
      return temp;
    }
  }

 //根据错误码获取错误信息
  String getErrorMessage(int errorCode){
    Map<int,String> data = {401000:intlanguage('app100001546',''),401001:intlanguage('app100001547',''),401002:	intlanguage('app100001548',''),402000:intlanguage('app100001549',''),402001:intlanguage('app100001550', ''),402002:intlanguage('app100001551', ''),402003:intlanguage('app100001552', '')};
    return data[errorCode] ?? "";
  }

  void updateGoodFieldsWithData(List<GoodLibFieldInfo> data) async {
    goodFieldList = data;
  }

  ///获取商品库数据源列头信息
  List<String> goodsInfoFieldDescName() {
    return GoodLibFieldUtils.goodsInfoFieldDescName(goodFieldList);
  }

  ///获取商品库数据源商品内容信息
  List<String> goodsInfoFieldName() {
    return GoodLibFieldUtils.goodsInfoFieldName(goodFieldList);
  }

  ///获取排序后的商品库维护字段信息
  List<GoodLibFieldInfo> sortGoodLibFields({List<GoodLibFieldInfo>? datas}) {
    return GoodLibFieldUtils.sortGoodLibFields(datas ?? goodFieldList);
  }

  //根据header组装商品信息
  List<String> getGoodsInfoListWithGoods(Map<String, dynamic>? goodsInfo,{List<String>? ids}){
     List<String> goodsFieldValus = [];

     for (var fieldName in TemplateData.goodsInfnFieldName()) {
        if (fieldName == "id" && ids != null) {
          ids.add(goodsInfo?[fieldName]);
        }
        if (goodsInfo?[fieldName] is String) {
          goodsFieldValus.add(goodsInfo?[fieldName]);
        } else if (goodsInfo?[fieldName] is int) {
          goodsFieldValus.add(goodsInfo![fieldName].toString());
        } else {
          goodsFieldValus.add("");
        }
      }
      return goodsFieldValus;
  }

  //过滤删除的字段和空串
  correctionDataWith(Map<String, dynamic> goodsData){
     //已经删除的商品库字段更新数据的时候不做提交
    List<String> deleteFields = GoodFieldManager().goodFieldList.where((element) => element.isDeleted == true).map((e) => e.fieldKey).toList();
    for (var element in deleteFields) {
      if(goodsData.containsKey(element)){
         goodsData[element] = null;
      }
    }
    //将空串赋值为null 服务端认为空串有值 影响商品库字段的删除
    for (var entry in goodsData.entries) {
      if(entry.value != null && entry.value is String && entry.value == ''){
        goodsData[entry.key] = null;
      }
    }
  }

  ///获取商品库维护字段信息map
  Map<String,int> getFieldsSortInfoMap() {
    Map<String,int> map = {};
    for (int i = 0;i<goodFieldList.length;i++) {
      GoodLibFieldInfo fieldInfo = goodFieldList[i];
      String fieldKey = fieldInfo.fieldKey;
      int sortIndex = fieldInfo.sortIndex ?? 0;
      map[fieldKey] = sortIndex;
    }
    return map;
  }
}
