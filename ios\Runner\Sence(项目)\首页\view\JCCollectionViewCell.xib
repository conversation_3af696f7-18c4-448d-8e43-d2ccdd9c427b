<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="JCCollectionViewCell">
            <rect key="frame" x="0.0" y="0.0" width="623" height="548"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="623" height="548"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Xip-Ah-ZAp">
                        <rect key="frame" x="0.0" y="0.0" width="623" height="546"/>
                        <subviews>
                            <imageView userInteractionEnabled="NO" contentMode="scaleToFill" semanticContentAttribute="spatial" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="F3O-zG-1wu" userLabel="templateTypeView">
                                <rect key="frame" x="29" y="496" width="14" height="14"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="14" id="08j-6v-5xN"/>
                                    <constraint firstAttribute="height" constant="14" id="q1a-fJ-SC4"/>
                                </constraints>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AFq-fe-Nc3">
                                <rect key="frame" x="0.0" y="0.0" width="623" height="484"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="fontVipYellow" translatesAutoresizingMaskIntoConstraints="NO" id="kfw-G6-URP">
                                        <rect key="frame" x="4" y="4" width="22" height="14"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="22" id="DOJ-PR-PmZ"/>
                                            <constraint firstAttribute="height" constant="14" id="Zy8-my-B4c"/>
                                        </constraints>
                                    </imageView>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="goodsState" highlightedImage="goodsState" translatesAutoresizingMaskIntoConstraints="NO" id="bv1-8b-sDH">
                                        <rect key="frame" x="25" y="469" width="15" height="15"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="15" id="AuC-MW-aEZ"/>
                                            <constraint firstAttribute="width" constant="15" id="Xu0-m4-MRN"/>
                                        </constraints>
                                    </imageView>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ogh-Zb-cQa">
                                        <rect key="frame" x="563" y="0.0" width="60" height="20"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="R08-7Y-w8I">
                                                <rect key="frame" x="13" y="1" width="34" height="18.5"/>
                                                <fontDescription key="fontDescription" name="PingFangSC-Semibold" family="PingFang SC" pointSize="13"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemRedColor"/>
                                        <constraints>
                                            <constraint firstItem="R08-7Y-w8I" firstAttribute="centerY" secondItem="Ogh-Zb-cQa" secondAttribute="centerY" id="4Od-8U-EcH"/>
                                            <constraint firstItem="R08-7Y-w8I" firstAttribute="centerX" secondItem="Ogh-Zb-cQa" secondAttribute="centerX" id="7xi-u0-56T"/>
                                            <constraint firstAttribute="width" constant="60" id="Bkg-KU-CfD"/>
                                            <constraint firstAttribute="height" constant="20" id="jbQ-IY-hXj"/>
                                        </constraints>
                                    </view>
                                    <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Y7J-KG-XZ6">
                                        <rect key="frame" x="15" y="17" width="593" height="450"/>
                                        <color key="backgroundColor" red="0.94901960784313721" green="0.94901960784313721" blue="0.94901960784313721" alpha="1" colorSpace="calibratedRGB"/>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" white="0.92000000000000004" alpha="1" colorSpace="calibratedWhite"/>
                                <constraints>
                                    <constraint firstItem="Ogh-Zb-cQa" firstAttribute="top" secondItem="AFq-fe-Nc3" secondAttribute="top" id="3Q7-xH-TVg"/>
                                    <constraint firstItem="bv1-8b-sDH" firstAttribute="leading" secondItem="AFq-fe-Nc3" secondAttribute="leading" constant="25" id="6L3-9x-BN2"/>
                                    <constraint firstItem="Ogh-Zb-cQa" firstAttribute="trailing" secondItem="AFq-fe-Nc3" secondAttribute="trailing" id="GTN-Bo-nTy"/>
                                    <constraint firstAttribute="top" secondItem="kfw-G6-URP" secondAttribute="top" constant="-4" id="Hjp-lO-NQ5"/>
                                    <constraint firstItem="Y7J-KG-XZ6" firstAttribute="leading" secondItem="AFq-fe-Nc3" secondAttribute="leading" constant="15" id="LOB-zN-Vhe"/>
                                    <constraint firstAttribute="trailing" secondItem="Y7J-KG-XZ6" secondAttribute="trailing" constant="15" id="LYI-fu-gij"/>
                                    <constraint firstAttribute="bottom" secondItem="Y7J-KG-XZ6" secondAttribute="bottom" constant="17" id="W6a-3J-TWZ"/>
                                    <constraint firstAttribute="bottom" secondItem="bv1-8b-sDH" secondAttribute="bottom" id="Zrq-ju-bOw"/>
                                    <constraint firstItem="Y7J-KG-XZ6" firstAttribute="top" secondItem="AFq-fe-Nc3" secondAttribute="top" constant="17" id="nW1-ni-tXZ"/>
                                    <constraint firstItem="kfw-G6-URP" firstAttribute="leading" secondItem="AFq-fe-Nc3" secondAttribute="leading" constant="4" id="nlm-Kd-rhX"/>
                                </constraints>
                            </view>
                            <imageView userInteractionEnabled="NO" contentMode="scaleToFill" semanticContentAttribute="spatial" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="ezX-KM-c1N">
                                <rect key="frame" x="15" y="496" width="14" height="14"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="14" id="1ZY-eW-2jE"/>
                                    <constraint firstAttribute="width" constant="14" id="Bzw-eG-HjY"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="L6f-ed-OdM">
                                <rect key="frame" x="15" y="519" width="31" height="15"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="15" id="Zcp-ff-uxt"/>
                                </constraints>
                                <fontDescription key="fontDescription" name="PingFangSC-Regular" family="PingFang SC" pointSize="12"/>
                                <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="o7W-X5-jIK">
                                <rect key="frame" x="45" y="493" width="563" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="BiI-Ym-iyZ"/>
                                </constraints>
                                <fontDescription key="fontDescription" name="PingFangSC-Semibold" family="PingFang SC" pointSize="13"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3a2-Vb-cbl">
                                <rect key="frame" x="581.5" y="511.5" width="41.5" height="30.5"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="41.5" id="fLC-6c-XVM"/>
                                    <constraint firstAttribute="height" constant="30.5" id="hrG-iN-mpy"/>
                                </constraints>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" image="print_icon" title=""/>
                                <connections>
                                    <action selector="printEvent:" destination="gTV-IL-0wX" eventType="touchUpInside" id="nH8-3S-0M6"/>
                                </connections>
                            </button>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="AFq-fe-Nc3" firstAttribute="top" secondItem="Xip-Ah-ZAp" secondAttribute="top" id="Cdz-Oz-N9O"/>
                            <constraint firstItem="L6f-ed-OdM" firstAttribute="top" secondItem="o7W-X5-jIK" secondAttribute="bottom" constant="6" id="HeR-YQ-i6k"/>
                            <constraint firstItem="ezX-KM-c1N" firstAttribute="leading" secondItem="Xip-Ah-ZAp" secondAttribute="leading" constant="15" id="Hmm-PH-5Aq"/>
                            <constraint firstItem="L6f-ed-OdM" firstAttribute="leading" secondItem="Xip-Ah-ZAp" secondAttribute="leading" constant="15" id="JzL-RR-iH2"/>
                            <constraint firstItem="AFq-fe-Nc3" firstAttribute="leading" secondItem="Xip-Ah-ZAp" secondAttribute="leading" id="NoD-vJ-2ei"/>
                            <constraint firstItem="ezX-KM-c1N" firstAttribute="centerY" secondItem="o7W-X5-jIK" secondAttribute="centerY" id="PxV-jc-I2g"/>
                            <constraint firstItem="o7W-X5-jIK" firstAttribute="top" secondItem="AFq-fe-Nc3" secondAttribute="bottom" constant="9" id="QhB-T5-Mzc"/>
                            <constraint firstItem="F3O-zG-1wu" firstAttribute="centerY" secondItem="ezX-KM-c1N" secondAttribute="centerY" id="RQr-fF-6wM"/>
                            <constraint firstAttribute="bottom" secondItem="L6f-ed-OdM" secondAttribute="bottom" constant="12" id="eBS-QS-f2B"/>
                            <constraint firstItem="F3O-zG-1wu" firstAttribute="leading" secondItem="ezX-KM-c1N" secondAttribute="trailing" id="hBJ-pM-aJC"/>
                            <constraint firstAttribute="trailing" secondItem="AFq-fe-Nc3" secondAttribute="trailing" id="hN1-Kp-RWb"/>
                            <constraint firstItem="3a2-Vb-cbl" firstAttribute="centerY" secondItem="L6f-ed-OdM" secondAttribute="centerY" id="lGo-MF-xc4"/>
                            <constraint firstAttribute="trailing" secondItem="3a2-Vb-cbl" secondAttribute="trailing" id="pLj-uq-o7o"/>
                            <constraint firstItem="o7W-X5-jIK" firstAttribute="leading" secondItem="F3O-zG-1wu" secondAttribute="trailing" constant="2" id="vfL-Uk-j0h"/>
                            <constraint firstAttribute="trailing" secondItem="o7W-X5-jIK" secondAttribute="trailing" constant="15" id="wg2-JU-eNr"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="Xip-Ah-ZAp" secondAttribute="trailing" id="RF8-ls-0J9"/>
                <constraint firstAttribute="bottom" secondItem="Xip-Ah-ZAp" secondAttribute="bottom" constant="2" id="Ylr-j0-tf2"/>
                <constraint firstItem="Xip-Ah-ZAp" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="grd-Ib-WlG"/>
                <constraint firstItem="Xip-Ah-ZAp" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="vge-eC-nA3"/>
            </constraints>
            <size key="customSize" width="623" height="548"/>
            <connections>
                <outlet property="bgV" destination="Xip-Ah-ZAp" id="KPm-BQ-0mD"/>
                <outlet property="goodsStateImageLeftSpace" destination="6L3-9x-BN2" id="cYS-YY-yjb"/>
                <outlet property="goodsStateImageView" destination="bv1-8b-sDH" id="gME-4S-RGx"/>
                <outlet property="printButton" destination="3a2-Vb-cbl" id="Nra-ha-Qsa"/>
                <outlet property="sizeLabel" destination="L6f-ed-OdM" id="UxD-R1-3OV"/>
                <outlet property="stateImage" destination="ezX-KM-c1N" id="3Pi-Gg-bAh"/>
                <outlet property="stateImageW" destination="Bzw-eG-HjY" id="Hfb-wp-Tzq"/>
                <outlet property="templateImageV" destination="Y7J-KG-XZ6" id="LPC-Ps-vf0"/>
                <outlet property="templateStateLabel" destination="R08-7Y-w8I" id="df2-i5-1eu"/>
                <outlet property="templateStateView" destination="Ogh-Zb-cQa" id="RQd-FI-v5n"/>
                <outlet property="templateTypeView" destination="F3O-zG-1wu" id="qgb-nh-mgN"/>
                <outlet property="templateTypeViewWidth" destination="08j-6v-5xN" id="5tF-Lv-xge"/>
                <outlet property="titleLabel" destination="o7W-X5-jIK" id="ky7-JG-iMw"/>
                <outlet property="titleLeading" destination="vfL-Uk-j0h" id="f9a-iZ-aXJ"/>
                <outlet property="vipStateImage" destination="kfw-G6-URP" id="PI9-Cb-Rwy"/>
                <outlet property="wholeBgView" destination="AFq-fe-Nc3" id="zqg-aB-DHf"/>
            </connections>
            <point key="canvasLocation" x="-266.39999999999998" y="-32.383808095952027"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="fontVipYellow" width="23" height="15"/>
        <image name="goodsState" width="15" height="15"/>
        <image name="print_icon" width="20.5" height="20"/>
        <systemColor name="systemRedColor">
            <color red="1" green="0.23137254901960785" blue="0.18823529411764706" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
