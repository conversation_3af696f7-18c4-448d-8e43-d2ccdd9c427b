//
//  JCHomeView.m
//  Runner
//
//  Created by huzi_0118 on 2019/3/23.
//  Copyright © 2019年 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCHomeView.h"
#import "JCHomeHeadView.h"
#import "JCUnOriginalRFIDView.h"
#import "HQFlowView.h"
#import "HQImagePageControl.h"
#define contentHeight 8000
#import "JCRequestErrView.h"
#import "UIButton+HALRTL.h"
#import "JCShopNormalVC.h"
#define ServerStateViewHeight  SCREEN_WIDTH * 30 /330

// MARK: -- VIP Banner --
@interface JCVipTextBanner : UIView

/// 按钮事件
@property (nonatomic, copy) XYNormalBlock clickActionBlock;

/// 关闭事件
@property (nonatomic, copy) XYNormalBlock clickCloseBlock;

@end

@interface JCVipTextBanner ()

/// VIP文本横幅
@property (nonatomic, strong) UIButton *vipTextButton;

/// VIP关闭
@property (nonatomic, strong) UIImageView *closeImageView;

@end

@implementation JCVipTextBanner

- (UIView *)vipTextButton {
    if (!_vipTextButton) {
        XYWeakSelf
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        button.backgroundColor = [UIColor clearColor];
        button.titleLabel.font = MY_FONT_Regular(13);
        button.titleLabel.lineBreakMode = NSLineBreakByClipping;
        button.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeading;
        [button setTitleColor:HEX_RGB(0x595959) forState:UIControlStateNormal];
        [button setImage:XY_IMAGE_NAMED(@"vip_banner_trumpet") forState:UIControlStateNormal];
        [button setTitle:@"VIP会员首购特惠，买一送一啦！VIP会员首购最长" forState:UIControlStateNormal];
        [button jk_setImagePosition:LXMImagePositionLeft spacing:5];
        [button jk_addActionHandler:^(NSInteger tag) {
            weakSelf.clickActionBlock();
        }];
        _vipTextButton = button;
    }
    return _vipTextButton;
}

- (UIImageView *)closeImageView {
    if (!_closeImageView) {
        UIImageView *imageView = [[UIImageView alloc] initWithImage:XY_IMAGE_NAMED(@"vip_banner_close")];
        imageView.userInteractionEnabled = YES;
        UITapGestureRecognizer *tapGestureRecognizer = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(closeView)];
        [imageView addGestureRecognizer:tapGestureRecognizer];
        _closeImageView = imageView;
    }
    return _closeImageView;
}


- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setUp];
    }
    return self;
}

- (void)setUp {
    [self addSubview:self.vipTextButton];
    [self addSubview:self.closeImageView];
    [self.vipTextButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.bottom.equalTo(self);
        make.trailing.equalTo(self.closeImageView.mas_leading).inset(7);
    }];
    [self.closeImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self);
        make.centerY.equalTo(self.vipTextButton);
        make.size.mas_equalTo(CGSizeMake(16, 16));
    }];
}

- (void)closeView {
    XYWeakSelf
    weakSelf.clickCloseBlock();
}

@end

@interface JCHomeView()<HQFlowViewDelegate,HQFlowViewDataSource,UIScrollViewDelegate>
@property (nonatomic, strong) UIButton *rightButton;
@property (nonatomic, strong) JCHomeHeadView *heardView;
@property (nonatomic, strong) NSArray *templateArr;
@property (nonatomic, strong) UIButton *myLabelButton;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIButton *printHistoryButton;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UILabel *noMoreDataLabel;
@property (nonatomic, strong) NSMutableArray *advArray;
@property (nonatomic,assign) CGFloat heardViewY;
@property (nonatomic,assign) CGFloat contentViewY;
@property (nonatomic,strong) UIView *thirdView;
@property (nonatomic,strong) UIView *customNaviBar;
@property (nonatomic,strong) UIView *customNaviBar2;
@property (nonatomic, strong) UIButton *rightButton2;
@property (nonatomic,strong) UIView *topHeadImageView;
@property (nonatomic,strong) UIImageView *headImageView;
@property (nonatomic, strong) UIButton *moreButton2;
@property (nonatomic, strong) HQFlowView *pageFlowView;
@property (nonatomic, strong) UIImageView *themeBackImageView;
@property (nonatomic, strong) HQImagePageControl *pageC;
@property (nonatomic,assign) CGFloat interval; //contentView距离上面的透明度间距,固定为12

@property (nonatomic,assign) CGFloat pageFlowViewSpace;
@property (nonatomic,strong) UIButton *courseBtn;
@property (nonatomic,strong) UIButton *courseBtn2;
@property (nonatomic,assign) NSInteger cellCount;
@property (nonatomic,assign) BOOL isAnimalRFID;
@property (nonatomic,assign) BOOL isRFID;
@property (nonatomic,strong) JCRequestErrView *requestErrView;
@property(nonatomic,retain)UIButton * scanSmallBtn;
@property(nonatomic,retain)UIButton * templateSmallBtn;

/// VIP文本横幅
@property (nonatomic, strong) JCVipTextBanner *vipTextBanner;

/// VIP展示模型
@property (nonatomic, strong) JCVIPTextBannerModel *vipTextBannerModel;

/// 标记是否正在用户手动滑动，避免自动调整contentOffset导致回弹
@property (nonatomic, assign) BOOL isUserScrolling;

@end

@implementation JCHomeView

- (id)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if(self){
        self.heardViewY = 0;
        self.contentViewY = 0;
        self.interval = 12;
        self.pageFlowViewSpace = 12;
        [self initBackImageView];
        [self initBackHeadView];
        [self initMainView];
        [self initCustomNavBar];
        [self initCustomNavBar2];
        [self initHeaderView];
        [self initShopGoView];
        [self initVIPBanner];
        [self initContentView];
        [self initShopButton];
        [self refreshLangChangedView];
        [self bringSubviewToFront:self.customNaviBar];
        [self bringSubviewToFront:self.customNaviBar2];
        [self bringSubviewToFront:self.moreButton2];
        [self.mainCollectionView addObserver:self forKeyPath:@"contentOffset" options:NSKeyValueObservingOptionNew context:nil];
        [self.mainCollectionView addObserver:self forKeyPath:@"contentInset" options:NSKeyValueObservingOptionNew context:nil];
          JCNCAddOb(self, @selector(batteryPowerChangeNotifation:), PrinterBatteryPowerNotification, nil);
    }
    return self;
}

-(void)refreshCustomNaviBar{
    [self.moreButton2 setTitle:XY_LANGUAGE_TITLE_NAMED(@"app01083", @"更多标签") forState:UIControlStateNormal];
    [self.requestErrView refreshServerState:m_currentServerState serverStateDetail:m_currentServerStateDetail];
}

-(void)batteryPowerChangeNotifation:(NSNotification *)noti
{
    [self refreshRightButton];
}

-(void)dealloc{
    [self.mainCollectionView removeObserver:self forKeyPath:@"contentOffset"];
    [self.mainCollectionView removeObserver:self forKeyPath:@"contentInset"];
    self.pageFlowView.delegate = nil;
    self.pageFlowView.dataSource = nil;
    [self.pageFlowView stopTimer];
}
//
- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context {
    if ([keyPath isEqualToString:@"contentOffset"]) {
        // 如果用户正在手动滑动，减少自动调整以避免回弹
        if (self.isUserScrolling) {
            // 仅更新必要的UI元素位置，不调整contentOffset
            CGPoint p = self.mainCollectionView.contentOffset;
            CGFloat contentHight = self.mainCollectionView.contentSize.height;
            self.moreButton2.hidden = contentHight <= 320 || self.cellCount < 10;
            self.moreButton2.frame = CGRectMake((SCREEN_WIDTH- 160)/2, contentHight - p.y , 160, 30);
            self.moreButton2.leading = (SCREEN_WIDTH- 160)/2;
            self.scanSmallBtn.leading = 10;
            self.templateSmallBtn.leading = self.scanSmallBtn.trailing + 20;
            
            // 更新导航栏透明度等UI效果
            p.y += self.mainCollectionView.contentInset.top;
            NSInteger offsetY = (NSInteger)p.y;
            if(offsetY > 1){
                if(!self.isAnimalRFID){
                    float navBarHeight = iPhoneX?88:64;
                    float headViewOffset = self.heardViewY + self.heardView.height - navBarHeight;
                    if(p.y <= headViewOffset){
                        float aplha = p.y  / headViewOffset;
                        self.customNaviBar2.alpha = aplha > 0 ? aplha : 0;
                        self.customNaviBar.alpha = (1 - aplha) > 1 ? 1 : (1 - aplha);
                        self.heardView.alpha = 1 - self.customNaviBar2.alpha;
                        self.topHeadImageView.alpha = self.customNaviBar.alpha;
                    }else{
                        self.customNaviBar2.alpha = 1;
                        self.customNaviBar.alpha = 0;
                        self.heardView.alpha = 0;
                        self.topHeadImageView.alpha = self.customNaviBar.alpha;
                    }
                }
                self.contentView.top = self.contentViewY - p.y;
                self.contentView.height = contentHeight;
                self.mainCollectionView.mj_header.hidden = YES;
            }
            return;
        }
        
        CGPoint p = self.mainCollectionView.contentOffset;
        CGFloat contentHight = self.mainCollectionView.contentSize.height;
        NSLog(@"position2,contentOffset1 %.f contentSizeHeight %.f ",p.y,contentHight);
        self.moreButton2.hidden = contentHight <= 320 || self.cellCount < 10;
        self.moreButton2.frame = CGRectMake((SCREEN_WIDTH- 160)/2, contentHight - p.y , 160, 30);
        NSLog(@"position2 moreButton2 %.f contentSizeHeight %.f ",self.moreButton2.frame.origin.y,contentHight);
        self.moreButton2.leading = (SCREEN_WIDTH- 160)/2;
        self.scanSmallBtn.leading = 10;
        self.templateSmallBtn.leading = self.scanSmallBtn.trailing + 20;
        NSLog(@"contentInset Top %.f",self.mainCollectionView.contentInset.top);
        p.y += self.mainCollectionView.contentInset.top;
        NSLog(@"contentOffset2 Top %.f",p.y);
        NSInteger offsetY = (NSInteger)p.y;
        if(offsetY > 1){
            if(!self.isAnimalRFID){
                float navBarHeight = iPhoneX?88:64;
                float headViewOffset = self.heardViewY + self.heardView.height - navBarHeight;
                if(p.y <= headViewOffset){
                    float aplha = p.y  / headViewOffset;
                    self.customNaviBar2.alpha = aplha > 0 ? aplha : 0;
                    self.customNaviBar.alpha = (1 - aplha) > 1 ? 1 : (1 - aplha);
                    self.heardView.alpha = 1 - self.customNaviBar2.alpha;
                    self.topHeadImageView.alpha = self.customNaviBar.alpha;
                }else{
                    self.customNaviBar2.alpha = 1;
                    self.customNaviBar.alpha = 0;
                    self.heardView.alpha = 0;
                    self.topHeadImageView.alpha = self.customNaviBar.alpha;
                }
            }
            self.contentView.top = self.contentViewY - p.y;
            self.contentView.height = contentHeight;
            self.mainCollectionView.mj_header.hidden = YES;
        }else{
            UIEdgeInsets insets = self.mainCollectionView.contentInset;
            self.heardView.top = self.heardViewY - insets.top + p.y;
            self.requestErrView.top = self.heardView.bottom + self.interval;
            self.pageFlowView.top = self.requestErrView.hidden?self.heardView.bottom + self.pageFlowViewSpace:self.requestErrView.bottom + self.pageFlowViewSpace;
            self.homeNewRFIDView.top = self.pageFlowView.hidden ? (self.requestErrView.hidden?self.heardView.bottom + self.interval:self.requestErrView.bottom + self.interval):self.pageFlowView.bottom + self.interval;
            CGFloat offsetY = self.pageFlowView.hidden ? (self.requestErrView.hidden?self.heardView.bottom:self.requestErrView.bottom): self.pageFlowView.bottom;
            if(!self.isAnimalRFID){

            }
            // VIP的文本Banner是否展示
            if (self.vipTextBanner.isHidden) {
                self.thirdView.top = !self.isRFID ? (offsetY + self.interval) : self.homeNewRFIDView.bottom + self.interval;
            } else {
                self.vipTextBanner.top = !self.isRFID ? (offsetY + self.interval) : self.homeNewRFIDView.bottom + self.interval;
                self.thirdView.top = self.vipTextBanner.bottom + self.interval;
            }
            self.contentView.top = self.contentViewY;
            self.contentView.height = contentHeight;
            self.mainCollectionView.mj_header.hidden = NO;
            self.customNaviBar2.alpha = 0;
            self.customNaviBar.alpha = 1;
            self.heardView.alpha = 1;
            self.topHeadImageView.alpha = self.customNaviBar.alpha;
        }
    }else if ([keyPath isEqualToString:@"contentInset"]) {
        if(self.isAnimalRFID){
//            return;
        }
        UIEdgeInsets insets = self.mainCollectionView.contentInset;
        self.heardView.top = self.heardViewY - insets.top;
        CGFloat offsetY = self.heardView.bottom;
        if(!self.requestErrView.hidden){
            self.requestErrView.top = (offsetY + self.interval);
            offsetY = self.requestErrView.bottom;
        }
        if(!self.pageFlowView.hidden){
            self.pageFlowView.top = (offsetY + self.pageFlowViewSpace);
            offsetY = self.pageFlowView.bottom;
        }
        offsetY += self.interval;
      
        if(!self.homeNewRFIDView.hidden && self.isRFID){
            self.homeNewRFIDView.top = offsetY;
            offsetY = self.homeNewRFIDView.bottom + self.interval;;
        }
        if (self.vipTextBanner.isHidden) {
            self.thirdView.top = offsetY;
        } else {
            self.vipTextBanner.top = offsetY;
            self.thirdView.top = self.vipTextBanner.bottom + self.interval;
        }
    }
}

- (void)refreshServiceState:(ServerState)serverState serverStateDetail:(NSString *)serverStateDetail{
    self.requestErrView.frame = CGRectMake(15, self.heardView.bottom + self.interval, SCREEN_WIDTH - 30, ServerStateViewHeight);
    [self.requestErrView refreshServerState:serverState serverStateDetail:serverStateDetail];
    [self refreshBannerWithBannerArr:self.advArray];
}

-(void)refreshWithAnimation{
    // 注释掉自动调整滑动位置的逻辑，避免滑动回弹问题
    // 如果需要特定的滑动行为，应该通过用户手势或明确的业务逻辑触发
    /*
    CGPoint p = self.mainCollectionView.contentOffset;
    p.y += self.mainCollectionView.contentInset.top;
    if(p.y <= 0) return;
    if(self.heardView.alpha == 0) return;
    UIEdgeInsets insets = self.mainCollectionView.contentInset;
    if(insets.top + self.mainCollectionView.contentSize.height + insets.bottom <= self.mainCollectionView.height) return;
    if(self.heardView.alpha < 0.5){
        if(insets.top + self.mainCollectionView.contentSize.height + insets.bottom <= self.mainCollectionView.height + self.heardViewY + self.heardView.height){
            [self.mainCollectionView setContentOffset:CGPointMake(0, - self.mainCollectionView.contentInset.top) animated:NO];
            return;
        }
        float navBarHeight = iPhoneX?88:64;
        [self.mainCollectionView setContentOffset:CGPointMake(0, self.heardViewY + self.heardView.height - self.mainCollectionView.contentInset.top - navBarHeight) animated:NO];
    }else{
        [self.mainCollectionView setContentOffset:CGPointMake(0, - self.mainCollectionView.contentInset.top) animated:NO];
    }
    */
}

- (void)initBackImageView{
    self.backgroundColor = HEX_RGB(0xF5F5F5);
}

- (void)initBackHeadView{
  AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
  XYTabBarController *currentTabbarVC = (XYTabBarController *)appDelegate.mainVC;
  NSArray *themeImageArr = [currentTabbarVC getThemeImageArr];
  NSString * topImageUrl = [self getTopBackImageUrl];
  UIImageView *backHeadImageView = [[UIImageView alloc] initWithFrame:CGRectZero];
  backHeadImageView.image = XY_IMAGE_NAMED(@"head_bg");
  [self addSubview:backHeadImageView];
  [backHeadImageView mas_makeConstraints:^(MASConstraintMaker *make) {
      make.top.leading.trailing.equalTo(self).offset(0);
      make.height.mas_equalTo((iPhoneX?170:147));
  }];
  self.topHeadImageView = backHeadImageView;
  self.headImageView = backHeadImageView;
  if(themeImageArr.count > 0) {
    NSString *imagePath = [NSString stringWithFormat:@"%@/%@_head_bg_image.png",RESOURCE_IMAGE_HOME_PATH,XY_JC_LANGUAGE];
    if ([[NSFileManager defaultManager] fileExistsAtPath:imagePath]) {
        UIImage *image = [UIImage imageWithContentsOfFile:imagePath];
        dispatch_async(dispatch_get_main_queue(), ^{
          backHeadImageView.image = image;
        });
    } else {
      if(topImageUrl.length > 0){
          //性能不好的手机首次调用sd_setImageWithURL 解码期间会出现白图
          //[backHeadImageView sd_setImageWithURL:XY_URLWithString(topImageUrl)];
          [self loadTopImageWithUrl:topImageUrl topImageView:backHeadImageView];
      }
    }
  }else {
    if(topImageUrl.length > 0){
        //性能不好的手机首次调用sd_setImageWithURL 解码期间会出现白图
        //[backHeadImageView sd_setImageWithURL:XY_URLWithString(topImageUrl)];
        [self loadTopImageWithUrl:topImageUrl topImageView:backHeadImageView];
    }
  }

}

-(void)loadTopImageWithUrl:(NSString *)url topImageView:(UIImageView *)topImageView{
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSData * data = [[NSData alloc]initWithContentsOfURL:[NSURL URLWithString:url]];
        UIImage *image = [[UIImage alloc]initWithData:data];
        NSFileManager* fm=[NSFileManager defaultManager];
        NSString *swiperImagePath = RESOURCE_IMAGE_HOME_PATH;
        if(![fm fileExistsAtPath:swiperImagePath]){
            [fm createDirectoryAtPath:swiperImagePath withIntermediateDirectories:YES attributes:nil error:nil];
        }
        NSString *imagePath = [NSString stringWithFormat:@"%@/%@_head_bg_image.png",RESOURCE_IMAGE_HOME_PATH,XY_JC_LANGUAGE];
        // 将图片数据写入文件
        [@{} xy_downLoadFileWithUrlString:url savePath:imagePath tag:@"" Success:^(__kindof YTKBaseRequest *request, id model) {
           
        } failure:^(NSString *msg, id model) {
          NSLog(@"%@",msg);
        } downloadBlock:nil];
        if (data != nil) {
            dispatch_async(dispatch_get_main_queue(), ^{
                topImageView.image = image;
            });
        }
    });
}

-(NSString *)getTopBackImageUrl
{
    NSString * imageUrl = XY_LANGUAGE_TITLE_NAMED(@"app100001257", @"");
    BOOL isURL = [imageUrl hasPrefix:@"http://"] || [imageUrl hasPrefix:@"https://"];
    return isURL ? imageUrl : @"";
}

-(void)initMainView{
    XYWeakSelf
    JCWaterfallCollectionLayout* collectionLayout = [[JCWaterfallCollectionLayout alloc]init];
    self.collectionLayout = collectionLayout;
    UICollectionView *mainCollectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:collectionLayout];
    mainCollectionView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    mainCollectionView.showsVerticalScrollIndicator = NO;
    mainCollectionView.backgroundColor = [UIColor clearColor];
    // 设置delegate以监听滑动状态
    mainCollectionView.delegate = (id<UICollectionViewDelegate>)self;
    [self addSubview:mainCollectionView];
    self.mainCollectionView = mainCollectionView;
    [mainCollectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(weakSelf).offset(0);
        make.leading.equalTo(weakSelf).offset(0);
        make.trailing.equalTo(weakSelf).offset(0);
        //      iPhoneX ? -83 : -54
        make.bottom.equalTo(weakSelf).offset(0);
    }];
}


/** 初始化头部  **/
- (void)initHeaderView{
    float navBarHeight = iPhoneX?88:64;
    JCHomeHeadView *heardView = [[JCHomeHeadView alloc] initWithFrame:CGRectMake(13, navBarHeight + 10, SCREEN_WIDTH - 26, (SCREEN_WIDTH-26)* 97/337)];
    [heardView refreshHomeOperateWithTitle1:XY_LANGUAGE_TITLE_NAMED(@"app00975", @"行业模板") title2:XY_LANGUAGE_TITLE_NAMED(@"app01284", @"扫一扫")];
    [heardView setOperateBlock:^(id x) {
        if(self.homeOperateBlock){
            self.homeOperateBlock(x);
        }
    }];
    [self.mainCollectionView addSubview:heardView];
    self.heardView = heardView;
    self.heardViewY = heardView.top;
}

/** 初始化商城按钮  **/
- (void)initShopButton{
    UIButton *shopButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [shopButton setImage:XY_IMAGE_NAMED(@"homeShopBtn") forState:UIControlStateNormal];
    [shopButton addTarget:self action:@selector(toShopEvent) forControlEvents:UIControlEventTouchUpInside];
    shopButton.hidden = [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] || !xy_isNorthAmerica;
    shopButton.frame = CGRectMake(SCREEN_WIDTH - 78 - 15, self.height - 15 - 81 - (iPhoneX?83:49), 78, 81);
    [self addSubview:shopButton];
    self.toShopButton = shopButton;
}

- (void)initVIPBanner {
    XYWeakSelf
    JCVipTextBanner *vipBanner = [[JCVipTextBanner alloc] initWithFrame:CGRectMake(21, 0, SCREEN_WIDTH - 42, 18)];
    // 点击标题事件
    vipBanner.clickActionBlock = ^{
        if((NETWORK_STATE_ERROR) || STR_IS_NIL(weakSelf.vipTextBannerModel.redirectUrl)){
            return;
        }
        AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
        __block XYTabBarController *currentTabbarVC = appDelegate.mainVC;
        if(weakSelf.vipTextBannerModel.typeCode.integerValue == 3) {
            if(!xy_isLogin){
                [[JCLoginManager sharedInstance] checkLogin:^{

                } viewController:[XYTool getCurrentVC] loginSuccessBlock:^{
                    XYNavigationController *navVC = (XYNavigationController *)currentTabbarVC.selectedViewController;
                    JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopAppointUrl:weakSelf.vipTextBannerModel.redirectUrl];
                    vc.jumpSource = @"y_page_main_notice";
                    [navVC pushViewController:vc animated:YES];
                }];
            } else {
                XYNavigationController *navVC = (XYNavigationController *)currentTabbarVC.selectedViewController;
                JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopAppointUrl:weakSelf.vipTextBannerModel.redirectUrl];
                vc.jumpSource = @"y_page_main_notice";
                [navVC pushViewController:vc animated:YES];
            }
        } else if(weakSelf.vipTextBannerModel.typeCode.integerValue == 2) {
            NSURL *url = [NSURL URLWithString:weakSelf.vipTextBannerModel.redirectUrl];
            if([[UIApplication sharedApplication] canOpenURL:url]) {
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
            }else{
                NSURL *URL = [NSURL URLWithString:weakSelf.vipTextBannerModel.redirectUrl];
                [[UIApplication sharedApplication] openURL:URL options:@{} completionHandler:^(BOOL success) {
                    //  回调
                }];
            }
        } else {
            if([weakSelf.vipTextBannerModel.redirectUrl hasPrefix:@"niimbot://app"]){
                [JCToNativeRouteHelp toNativePageWith:weakSelf.vipTextBannerModel.redirectUrl fromType:JC_ADPop eventTitle:@""];
            }else{
                XYWKWebViewController *c = [[XYWKWebViewController alloc] initWithUrl:weakSelf.vipTextBannerModel.redirectUrl];
                c.title = weakSelf.vipTextBannerModel.title;
                [currentTabbarVC.selectedViewController pushViewController:c animated:YES];
            }
        }
        // 点击埋点
      JC_TrackWithparms(@"click",@"003_286",(@{@"b_name":STR_IS_NIL(weakSelf.vipTextBannerModel.content) ? @"" : weakSelf.vipTextBannerModel.content, @"banner_id":weakSelf.vipTextBannerModel.xyid, @"a_name":weakSelf.vipTextBannerModel.activityName}));
    };
    vipBanner.clickCloseBlock = ^{
        XYWeakSelf
        weakSelf.vipTextBannerModel.closeTime = [NSNumber numberWithDouble:[[NSDate date] timeIntervalSince1970]];
        NSString *where = [NSString stringWithFormat:@"where xyid = '%@'",weakSelf.vipTextBannerModel.xyid];
        NSArray *modelArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_VIP_TEXT_BANNER dicOrModel:[JCVIPTextBannerModel class] whereFormat:where];
        if (modelArr.count > 0) {
            [[JCFMDB shareDatabase:DB_NAME] jc_updateTable:TABLE_VIP_TEXT_BANNER dicOrModel:weakSelf.vipTextBannerModel whereFormat:where];
        } else {
            [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:TABLE_VIP_TEXT_BANNER dicOrModel:weakSelf.vipTextBannerModel];
        }
        [weakSelf refreshVIPBanner:weakSelf.vipTextBannerModel];
    };
    self.vipTextBanner = vipBanner;
    // 默认不展示
    [self.vipTextBanner setHidden:YES];
    [self.mainCollectionView addSubview:self.vipTextBanner];
}

/** 初始化内容区域 **/
- (void)initContentView{
    UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(15, self.heardView.bottom + self.interval, SCREEN_WIDTH - 30, contentHeight)];
    contentView.backgroundColor = UIColor.whiteColor;
    [contentView setJCShadowWithRadius:12 cloolorHex:0x000000 alpha:0.05 shadowRadius:7.5];
    [self addSubview:contentView];
    [self insertSubview:contentView belowSubview:self.mainCollectionView];
    contentView.clipsToBounds = YES;
    self.contentView = contentView;
    self.contentViewY = contentView.frame.origin.y;

    UIView *thirdV = [[UIView alloc] initWithFrame:CGRectMake(15, 0, SCREEN_WIDTH - 30, 40)];
    thirdV.backgroundColor = UIColor.whiteColor;
    thirdV.layer.cornerRadius = 12;
    [self.mainCollectionView addSubview:thirdV];
    self.thirdView = thirdV;
    [self.thirdView addSubview:self.themeBackImageView];
    [self.themeBackImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.thirdView);
    }];
    // 我的模板
    UIButton *myLabelButton = [UIButton buttonWithType:UIButtonTypeCustom];
    myLabelButton.titleLabel.font = MY_FONT_Bold(16);
    [myLabelButton setImage:[XY_IMAGE_NAMED(@"gengduo") imageFlippedForRightToLeftLayoutDirection] forState:UIControlStateNormal];
    [myLabelButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00716", @"我的标签") forState:UIControlStateNormal];
    [myLabelButton setTitleColor:HEX_RGB(0x262626) forState:UIControlStateNormal];
    [myLabelButton jk_setImagePosition:LXMImagePositionRight spacing:2];
    myLabelButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    [myLabelButton addTarget:self action:@selector(moreMyTemplate:) forControlEvents:UIControlEventTouchUpInside];
    [thirdV addSubview:myLabelButton];
    [myLabelButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(thirdV).offset(20);
        make.top.equalTo(thirdV).offset(15);
        make.height.mas_equalTo(20);
    }];
    self.myLabelButton = myLabelButton;

    [self.mainCollectionView addSubview:self.homeNewRFIDView];

    // 打印记录
    UIButton *printHistoryButton = [UIButton buttonWithType:UIButtonTypeCustom];
    printHistoryButton.titleLabel.font = MY_FONT_Regular(14);
    [printHistoryButton setImage:XY_IMAGE_NAMED(@"home_print_history") forState:UIControlStateNormal];
    [printHistoryButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000396", @"打印记录") forState:UIControlStateNormal];
    [printHistoryButton jk_setImagePosition:LXMImagePositionLeft spacing:4];
    [printHistoryButton setTitleColor:HEX_RGB(0x6E6E6E) forState:UIControlStateNormal];
    printHistoryButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentTrailing;
    [printHistoryButton addTarget:self action:@selector(onPrintHistoryClicked:) forControlEvents:UIControlEventTouchUpInside];

    [thirdV addSubview:printHistoryButton];
    [printHistoryButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(myLabelButton);
        make.trailing.equalTo(thirdV).offset(-16);
        make.height.mas_equalTo(35);
        make.width.mas_greaterThanOrEqualTo((SCREEN_WIDTH - 34)/2);
    }];
    self.printHistoryButton = printHistoryButton;

    // 我的模板底部 '更多' 按钮
    UIButton *moreBtn1 = [UIButton buttonWithType:UIButtonTypeCustom];
    [moreBtn1 setTitle:XY_LANGUAGE_TITLE_NAMED(@"app01083", @"更多标签") forState:UIControlStateNormal];
    moreBtn1.titleLabel.font = [UIFont fontWithName:@"PingFang-SC-Medium" size:13];
    [moreBtn1 setTitleColor:HEX_RGB(0x537FB7) forState:UIControlStateNormal];
    [moreBtn1 addTarget:self action:@selector(moreMyTemplate:) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:moreBtn1];
    self.moreButton2 = moreBtn1;

    self.mainCollectionView.contentInset = UIEdgeInsetsMake(200, 0, 50, 0);
}

- (UIImageView *)themeBackImageView{
    if(!_themeBackImageView){
        _themeBackImageView = [[UIImageView alloc] init];
        _themeBackImageView.image = [UIImage imageWithColor:COLOR_WHITE];
        _themeBackImageView.backgroundColor = COLOR_CLEAR;
        _themeBackImageView.layer.cornerRadius = 10;
        _themeBackImageView.clipsToBounds = YES;
    }
    return _themeBackImageView;
}

- (void)initCustomNavBar2{
    //to do
    float navBarHeight = iPhoneX?88:64;
    UIView *customNavBar = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, navBarHeight)];//[UIColor jk_colorWithHexString:themeConfig[@"color_main_tab_normal"]];
    customNavBar.backgroundColor = [self getTopBackImageUrl].length > 0 ? [UIColor jk_colorWithHexString:XY_LANGUAGE_TITLE_NAMED(@"app100001261", @"0xFB4B42")] : COLOR_NEW_THEME;
    UIButton *btn1 = [UIButton buttonWithType:UIButtonTypeCustom];
    btn1.frame = CGRectMake(10, navBarHeight - 44, 35, 44);
    [btn1 setImage:XY_IMAGE_NAMED(@"行业模板2") forState:UIControlStateNormal];
    [btn1 addTarget:self action:@selector(customNaviBar2TopClicked:) forControlEvents:UIControlEventTouchUpInside];
    btn1.tag = 3;
    [customNavBar addSubview:btn1];
    btn1.leading = 10;
    self.scanSmallBtn = btn1;

    UIButton *btn2 = [UIButton buttonWithType:UIButtonTypeCustom];
    btn2.frame = CGRectMake(btn1.right + 20, btn1.top, 35, 44);
    [btn2 setImage:XY_IMAGE_NAMED(@"扫一扫2") forState:UIControlStateNormal];
    [btn2 addTarget:self action:@selector(customNaviBar2TopClicked:) forControlEvents:UIControlEventTouchUpInside];
    btn2.tag = 5;
    [customNavBar addSubview:btn2];
    btn2.leading = btn1.trailing + 20;
    self.templateSmallBtn = btn2;

    UIButton *courseBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    courseBtn.contentMode = UIViewContentModeScaleAspectFit;
    courseBtn.backgroundColor = [UIColor clearColor];
    [courseBtn jk_setImagePosition:LXMImagePositionLeft spacing:3];
    [courseBtn addTarget:self action:@selector(courseBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [customNavBar addSubview:courseBtn];
    courseBtn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentTrailing;
    self.courseBtn2 = courseBtn;


    UIButton *rightButton = [UIButton buttonWithType:UIButtonTypeCustom];
    rightButton.contentMode = UIViewContentModeScaleAspectFit;
    rightButton.backgroundColor = [UIColor clearColor];
    [rightButton jk_setImagePosition:LXMImagePositionLeft spacing:3];
    [rightButton addTarget:self action:@selector(rightButtonTouch:) forControlEvents:UIControlEventTouchUpInside];
    rightButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentTrailing;
    [customNavBar addSubview:rightButton];
    self.rightButton2 = rightButton;
    [self addSubview:customNavBar];
    customNavBar.alpha = 0.f;
    self.customNaviBar2 = customNavBar;
    [self refreshCustomNaviBar];
    [self refreshRightButton];
}

-(void)customNaviBar2TopClicked:(UIButton *)sender{
    if(self.homeOperateBlock){
        self.homeOperateBlock(@(sender.tag));
    }
}

- (void)initShopGoView{
    [self.mainCollectionView addSubview:self.requestErrView];
    [self.mainCollectionView addSubview:self.pageFlowView];
    [self.pageFlowView addSubview:self.pageC];
    [self.pageFlowView reloadData];
}

- (JCRequestErrView *)requestErrView{
    if(!_requestErrView){
        _requestErrView = [[JCRequestErrView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 30, 0)];
        _requestErrView.hidden = YES;
    }
    return _requestErrView;
}

- (void)initCustomNavBar{
    float navBarHeight = iPhoneX?88:64;
    UIView *customNavBar = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, navBarHeight)];
    customNavBar.backgroundColor = UIColor.clearColor;

    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero];
    titleLabel.font = MY_FONT_Bold(19);
    titleLabel.textColor = HEX_RGB(0xFFFFFF);
    titleLabel.textAlignment = NSTextAlignmentLeft;
    [customNavBar addSubview:titleLabel];
    self.titleLabel = titleLabel;


    UIButton *rightButton = [UIButton buttonWithType:UIButtonTypeCustom];
    rightButton.contentMode = UIViewContentModeScaleAspectFit;
    rightButton.backgroundColor = [UIColor clearColor];
    [rightButton jk_setImagePosition:LXMImagePositionLeft spacing:3];
    rightButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentTrailing;
    [rightButton addTarget:self action:@selector(rightButtonTouch:) forControlEvents:UIControlEventTouchUpInside];
    [customNavBar addSubview:rightButton];
    self.rightButton = rightButton;
    [self addSubview:customNavBar];
    self.customNaviBar = customNavBar;

    UIButton *courseBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    courseBtn.contentMode = UIViewContentModeScaleAspectFit;
    courseBtn.backgroundColor = [UIColor clearColor];
    courseBtn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentTrailing;
    [courseBtn jk_setImagePosition:LXMImagePositionLeft spacing:3];
    [courseBtn addTarget:self action:@selector(courseBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [customNavBar addSubview:courseBtn];
    self.courseBtn = courseBtn;

    [self refreshRightButton];
}

-(void)courseBtnClick:(UIButton *)sender{
    if(self.homeOperateBlock){
        self.homeOperateBlock(@103);
    }
}

- (void)toShopEvent{
    if(self.toShopBlock){
        self.toShopBlock();
    }
}

-(void)refreshRFIDStatus:(BOOL)flag{

}

-(void)refreshUnRFIDStatus:(BOOL)flag type:(NSInteger)showType withTip:(NSString *)tipString surplusLevel:(NSInteger)surplusLevel{
}

- (void)refreshRightButton{
    float navBarHeight = iPhoneX?88:64;
    NSString *rightTitleStr = @"";
    NSString *fontName = @"";
    __block NSString *imageName = @"";
    UIColor *color;
    if(JC_IS_CONNECTED_PRINTER && !STR_IS_NIL(JC_CURRENT_CONNECTED_PRINTER)){
        NSString *printerName = JC_CURRENT_CONNECTED_PRINTER;
        rightTitleStr = [[JCBluetoothManager sharedInstance] printerTypeFromPrinterName:printerName isNeedShowChildType:NO];
        fontName = @"PingFang-SC-Medium";
        color = HEX_RGB(0xFFFFFF);
        imageName = @"已连接";
    }else{
        fontName = @"PingFang-SC-Regular";
        rightTitleStr = XY_LANGUAGE_TITLE_NAMED(@"app00190", @"未连接");
        color = HEX_RGB(0xFFFFFF);
        imageName = @"未连接";

    }
    self.titleLabel.textAlignment = NSTextAlignmentLeft;
    self.rightButton.titleLabel.font = [UIFont fontWithName:fontName size:14];
    [self.rightButton setTitleColor:color forState:UIControlStateNormal];
    [self.rightButton setImage:XY_IMAGE_NAMED(imageName) forState:UIControlStateNormal];
    [self.rightButton setTitle:rightTitleStr forState:UIControlStateNormal];
    self.rightButton2.titleLabel.font = [UIFont fontWithName:fontName size:14];
    [self.rightButton2 setTitleColor:color forState:UIControlStateNormal];
    [self.rightButton2 setImage:XY_IMAGE_NAMED(imageName) forState:UIControlStateNormal];
    [self.rightButton2 setTitle:rightTitleStr forState:UIControlStateNormal];
    self.courseBtn.titleLabel.font = MY_FONT_Bold(14);
    [self.courseBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    [self.courseBtn setImage:XY_IMAGE_NAMED(@"教程-白") forState:UIControlStateNormal];
    [self.courseBtn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00874", @"教程") forState:UIControlStateNormal];
    self.courseBtn2.titleLabel.font = MY_FONT_Bold(14);
    [self.courseBtn2 setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    [self.courseBtn2 setImage:XY_IMAGE_NAMED(@"教程-白") forState:UIControlStateNormal];
    [self.courseBtn2 setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00874", @"教程") forState:UIControlStateNormal];
    CGSize titleSize = [rightTitleStr boundingRectWithSize:CGSizeMake(999999.0f, 44) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont fontWithName:fontName size:14]} context:nil].size;
    self.rightButton.frame = CGRectMake(SCREEN_WIDTH - (titleSize.width + XY_IMAGE_NAMED(imageName).size.width + 7)-15, navBarHeight - 44, titleSize.width + XY_IMAGE_NAMED(imageName).size.width + 7, 44);
    self.rightButton.leading = SCREEN_WIDTH - (titleSize.width + XY_IMAGE_NAMED(imageName).size.width + 7)-15;
    self.rightButton2.frame = CGRectMake(SCREEN_WIDTH - (titleSize.width + XY_IMAGE_NAMED(imageName).size.width + 7)-15, navBarHeight - 44, titleSize.width + XY_IMAGE_NAMED(imageName).size.width + 7, 44);
    self.rightButton2.leading = SCREEN_WIDTH - (titleSize.width + XY_IMAGE_NAMED(imageName).size.width + 7)-15;
    self.titleLabel.frame = CGRectMake(25, navBarHeight - 44, 150, 44);
    self.titleLabel.leading = 25;
    self.titleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app00520", @"精臣云打印");
    CGSize titleSize1 = [XY_LANGUAGE_TITLE_NAMED(@"app00874", @"教程") boundingRectWithSize:CGSizeMake(999999.0f, 44) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:MY_FONT_Bold(14)} context:nil].size;
    self.courseBtn.frame = CGRectMake(self.rightButton.left - (XY_IMAGE_NAMED(@"教程-白").size.width + 15 + titleSize1.width), self.rightButton.top, XY_IMAGE_NAMED(@"教程-白").size.width + 7 + titleSize1.width, self.rightButton.height);
    self.courseBtn.leading = self.rightButton.leading - (XY_IMAGE_NAMED(@"教程-白").size.width + 15 + titleSize1.width);
    self.courseBtn2.frame = CGRectMake(self.rightButton2.left - (XY_IMAGE_NAMED(@"教程-白").size.width + 15 + titleSize1.width), self.rightButton2.top, XY_IMAGE_NAMED(@"教程-白").size.width + 7 + titleSize1.width, self.rightButton2.height);
    self.courseBtn2.leading = self.rightButton2.leading - (XY_IMAGE_NAMED(@"教程-白").size.width + 15 + titleSize1.width);

}


- (void)refreshContentView:(NSInteger)cellCount{
    [self.mainCollectionView reloadData];
    self.mainCollectionView.bounces = !(cellCount == 0 && !xy_isLogin);
    self.cellCount = cellCount;
    self.moreButton2.hidden = cellCount < 10;
    CGPoint p = self.mainCollectionView.contentOffset;
    p.y += self.mainCollectionView.contentInset.top;
    CGFloat homeRealyRFIDHeight = self.homeNewRFIDView.height;
    if(self.isAnimalRFID){
        homeRealyRFIDHeight = 0;
    }else{
        if(self.homeNewRFIDView.hidden){
            homeRealyRFIDHeight = 0;
        }else{
            homeRealyRFIDHeight = self.homeNewRFIDView.height;
        }
    }
    self.contentView.frame = CGRectMake(15, self.contentViewY - p.y, SCREEN_WIDTH - 30, contentHeight);
    [self.contentView setJCShadowWithRadius:12 cloolorHex:0x000000 alpha:0.05 shadowRadius:7.5];
    CGPoint contentOffset = self.mainCollectionView.contentOffset;
    CGPoint contentOffsetNew = CGPointMake(p.x, p.y - self.mainCollectionView.contentInset.top);
//    if(contentOffset.x == contentOffsetNew.x && fabs(contentOffset.y - contentOffsetNew.y) < 1){
//        contentOffsetNew.y = contentOffsetNew.y+1;
//    }
    [self.mainCollectionView setContentOffset:contentOffsetNew animated:NO];
    [self refreshBannerWithBannerArr:self.advArray];
    CGPoint p1 = self.mainCollectionView.contentOffset;
    CGFloat contentHight = self.mainCollectionView.contentSize.height;
    NSLog(@"position1 contentOffset1 %.f contentSizeHeight %.f ",p1.y,contentHight);
    self.moreButton2.hidden = contentHight <= 320 || self.cellCount < 10;
    self.moreButton2.frame = CGRectMake((SCREEN_WIDTH- 160)/2, contentHight - p1.y , 160, 30);
    NSLog(@"position1 moreButton2 %.f contentSizeHeight %.f ",self.moreButton2.frame.origin.y,contentHight);
}

- (void)refreshLangChangedView{
    [self refreshCustomNaviBar];
    [self refreshRightButton];
    [self.heardView refreshHomeOperateWithTitle1:XY_LANGUAGE_TITLE_NAMED(@"app00975", @"行业模板") title2:XY_LANGUAGE_TITLE_NAMED(@"app01284", @"扫一扫")];

    [self.myLabelButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00716", @"我的标签") forState:UIControlStateNormal];
  
    // 获取当前视图的布局方向
    UISemanticContentAttribute semanticContentAttribute = UIView.appearance.semanticContentAttribute;
    BOOL isRightToLeft = semanticContentAttribute == UISemanticContentAttributeForceRightToLeft;
    // 如果是 RTL 布局方向，翻转图片
    UIImage *originalImage =  XY_IMAGE_NAMED(@"gengduo");
    UIImage *flippedImage = isRightToLeft ? [originalImage imageFlippedForRightToLeftLayoutDirection] : originalImage;

    [self.myLabelButton setImage:flippedImage forState:UIControlStateNormal];
    [self.myLabelButton setTitleColor:HEX_RGB(0x262626) forState:UIControlStateNormal];
    [self.myLabelButton jk_setImagePosition:LXMImagePositionRight spacing:2];

    [self.printHistoryButton setImage:XY_IMAGE_NAMED(@"home_print_history") forState:UIControlStateNormal];
    [self.printHistoryButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000396", @"打印记录") forState:UIControlStateNormal];
    [self.printHistoryButton jk_setImagePosition:LXMImagePositionLeft spacing:4];
    //[self.printHistoryButton jk_setImagePosition:LXMImagePositionLeft spacing:4];
    self.printHistoryButton.imageEdgeInsets = UIEdgeInsetsMake(2,0,0,0);
    [self.courseBtn jk_setImagePosition:LXMImagePositionLeft spacing:3];
    [self.courseBtn2 jk_setImagePosition:LXMImagePositionLeft spacing:3];
    [self.rightButton jk_setImagePosition:LXMImagePositionLeft spacing:3];
    [self.rightButton2 jk_setImagePosition:LXMImagePositionLeft spacing:3];


    self.toShopButton.hidden = [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] || !xy_isNorthAmerica;
    AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
    
    XYTabBarController *currentTabbarVC = (XYTabBarController *)appDelegate.mainVC;
    NSArray *themeImageArr = [currentTabbarVC getThemeImageArr];
    NSString * topImageUrl = [self getTopBackImageUrl];
    if(themeImageArr.count > 0){
        NSString *backImageFilePath = [NSString stringWithFormat:@"%@/%@/source",RESOURCE_APP_THEME_SOURCE_PATH,XY_JC_LANGUAGE_REAL];
        NSString *backImagePath = [NSString stringWithFormat:@"%@/%@",backImageFilePath,themeImageArr[0][7]];
        UIImage *backImage = [UIImage imageWithData:[NSData dataWithContentsOfFile:backImagePath]];
        CGImageRef backImageCG = CGImageCreateWithImageInRect(backImage.CGImage, CGRectMake(0, 0, backImage.size.width, 120));
        backImage = [UIImage imageWithCGImage:backImageCG];
        self.thirdView.backgroundColor = COLOR_CLEAR;
        if(backImage != nil){
           self.themeBackImageView.image = backImage;
        }
    }else{
        self.thirdView.backgroundColor = COLOR_WHITE;
        self.themeBackImageView.image = [UIImage imageWithColor:COLOR_WHITE];
    }
    
    //切换语言，顶部背景图改变逻辑
    if(themeImageArr.count > 0) {
      NSString *imagePath = [NSString stringWithFormat:@"%@/%@_head_bg_image.png",RESOURCE_IMAGE_HOME_PATH,XY_JC_LANGUAGE];
      if ([[NSFileManager defaultManager] fileExistsAtPath:imagePath]) {
          UIImage *image = [UIImage imageWithContentsOfFile:imagePath];
          dispatch_async(dispatch_get_main_queue(), ^{
            self.headImageView.image = image;
          });
      } else {
        if(topImageUrl.length > 0){
            //性能不好的手机首次调用sd_setImageWithURL 解码期间会出现白图
            //[backHeadImageView sd_setImageWithURL:XY_URLWithString(topImageUrl)];
            [self loadTopImageWithUrl:topImageUrl topImageView:self.headImageView];
        }else {
            self.headImageView.image = XY_IMAGE_NAMED(@"head_bg");
        }
      }
    }else {
      if(topImageUrl.length > 0){
          //性能不好的手机首次调用sd_setImageWithURL 解码期间会出现白图
          //[backHeadImageView sd_setImageWithURL:XY_URLWithString(topImageUrl)];
          [self loadTopImageWithUrl:topImageUrl topImageView:self.headImageView];
      }else {
          self.headImageView.image = XY_IMAGE_NAMED(@"head_bg");
      }
    }
  
    self.customNaviBar2.backgroundColor = [self getTopBackImageUrl].length > 0 ? [UIColor jk_colorWithHexString:XY_LANGUAGE_TITLE_NAMED(@"app100001261", @"0xFB4B42")] : COLOR_NEW_THEME;
    
}

- (void)rightButtonTouch:(UIButton *)sender{
    if(self.homeOperateBlock){
        self.homeOperateBlock(@2);
    }
}

- (void)leftButtonTouch:(UIButton *)sender{
    if(self.homeOperateBlock){
        self.homeOperateBlock(@1);
    }
}

- (void)moreMyTemplate:(UIButton *)sender{
    if(self.homeOperateBlock){
        self.homeOperateBlock(@6);
    }
}

- (void)clickShopEvent:(UIButton *)sender{
    if(self.homeOperateBlock){
        self.homeOperateBlock(@9);
    }
}

- (void)onPrintHistoryClicked:(UIButton *)sender{
    if(self.homeOperateBlock){
        self.homeOperateBlock(@10);
    }
}

- (void)refreshHomeHeadView{

}

-(void)refreshBannerWithBannerArr:(NSArray *)arr{
    CGPoint p = self.mainCollectionView.contentOffset;
    p.y += self.mainCollectionView.contentInset.top;
    self.advArray = arr.mutableCopy;
    if(self.advArray.count == 1){
        self.pageFlowView.isOpenAutoScroll = NO;
        self.pageC.hidden = YES;
    }else{
        self.pageFlowView.isOpenAutoScroll = YES;
        self.pageC.hidden = NO;
    }
    float contentViewY = 0;
    if(self.advArray.count > 0){
        self.pageFlowView.hidden = NO;
        contentViewY = self.heardViewY + self.heardView.height + (self.pageFlowView.hidden?0:self.pageFlowView.height + self.pageFlowViewSpace) + (self.homeNewRFIDView.hidden? 0 : (self.homeNewRFIDView.height + self.interval)) + (self.requestErrView.hidden?0:(ServerStateViewHeight + self.interval)) + self.interval;
        contentViewY += !self.vipTextBanner.isHidden ? self.vipTextBanner.height + self.interval : 0;
        self.contentViewY = contentViewY;
        self.contentView.frame = CGRectMake(15, self.contentViewY - p.y, SCREEN_WIDTH - 30, contentHeight);
        [self.pageFlowView reloadData];
    }else{
        self.pageFlowView.hidden = YES;
        contentViewY = self.heardViewY + self.heardView.height + + (self.pageFlowView.hidden ? 0 : self.pageFlowView.height + self.pageFlowViewSpace) + (!self.isRFID? 0 : (self.homeNewRFIDView.height + self.interval)) + (self.requestErrView.hidden?0:(ServerStateViewHeight + self.interval)) + self.interval;
        contentViewY += !self.vipTextBanner.isHidden ? self.vipTextBanner.height + self.interval : 0;
        self.contentViewY = contentViewY;
        self.contentView.frame = CGRectMake(15, self.contentViewY - p.y, SCREEN_WIDTH - 30, contentHeight);
    }
    [self changeMainCollectionViewContenInset];
    [self.contentView setJCShadowWithRadius:12 cloolorHex:0x000000 alpha:0.05 shadowRadius:7.5];
    NSLog(@"contentOffset0 p.y:%.f contentInsetTop:%.f set Top %.f",p.y,self.mainCollectionView.contentInset.top,p.y - self.mainCollectionView.contentInset.top);
    [self.mainCollectionView setContentOffset:CGPointMake(p.x, p.y - self.mainCollectionView.contentInset.top) animated:NO];
}

- (void)refreshVIPBanner:(JCVIPTextBannerModel *)model {
    self.vipTextBannerModel = model;
    // 根据模型是否展示
    [self.vipTextBanner setHidden:![model isShow]];
    // 设置文案
    [self.vipTextBanner.vipTextButton setTitle:model.content forState:UIControlStateNormal];
    UISemanticContentAttribute semanticContentAttribute = UIView.appearance.semanticContentAttribute;
    BOOL isRightToLeft = semanticContentAttribute == UISemanticContentAttributeForceRightToLeft;
    UIImage *originalImage =  XY_IMAGE_NAMED(@"vip_banner_trumpet");
    UIImage *flippedImage = isRightToLeft ? [originalImage imageFlippedForRightToLeftLayoutDirection] : originalImage;
    [self.vipTextBanner.vipTextButton setImage:flippedImage forState:UIControlStateNormal];
    [self.vipTextBanner.vipTextButton jk_setImagePosition:LXMImagePositionLeft spacing:5];
    // 根据是否显示调整偏移量
    CGFloat offsetY = self.pageFlowView.hidden ? (self.requestErrView.hidden ? self.heardView.bottom : self.requestErrView.bottom): self.pageFlowView.bottom;
    self.thirdView.top = !self.isRFID ? (offsetY + (!self.vipTextBanner.isHidden ? self.vipTextBanner.height : 0) + self.interval) : (!self.vipTextBanner.isHidden ? self.vipTextBanner.bottom : self.homeNewRFIDView.bottom) + self.interval;
    [self refreshBannerWithBannerArr:self.advArray];
}

-(void)changeMainCollectionViewContenInset{
    UIEdgeInsets inset = self.mainCollectionView.contentInset;
    float hearderHeight = self.heardViewY + self.heardView.height;
    float pageFlowerHeight = self.pageFlowView.hidden ? 0 : self.pageFlowView.height + self.pageFlowViewSpace;
    float thirdViewHeight = self.thirdView.height;
    float newRFIDViewHeight = self.homeNewRFIDView.hidden?0:self.homeNewRFIDView.height +  self.interval;
    float requestErrViewHeight = self.requestErrView.hidden? 0 :(ServerStateViewHeight + self.interval);
    float vipTextBannerHeight = !self.vipTextBanner.isHidden ? (self.vipTextBanner.height + 9) : 0;
    inset.top = hearderHeight + pageFlowerHeight + thirdViewHeight + newRFIDViewHeight + requestErrViewHeight + vipTextBannerHeight + self.interval;
    self.mainCollectionView.contentInset = inset;
}

- (void)resetOffset{
    CGPoint p = self.mainCollectionView.contentOffset;
    p.y += self.mainCollectionView.contentInset.top;
    [self changeMainCollectionViewContenInset];
    [self.contentView setJCShadowWithRadius:12 cloolorHex:0x000000 alpha:0.05 shadowRadius:7.5];
    [self.mainCollectionView setContentOffset:CGPointMake(p.x, - self.mainCollectionView.contentInset.top) animated:NO];
}

- (void)refreshNewRFIDViewState:(BOOL)flag rfidViewData:(JCTemplateData *)rfidViewData
                       showType:(NSInteger)showType surplusLevel:(NSInteger)surplusLevel isAnimal:(BOOL)isAnimal{
//    if(self.isAnimalRFID){
//        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//            [self refreshNewRFIDViewState:flag rfidViewData:rfidViewData showType:showType surplusLevel:surplusLevel isAnimal:isAnimal];
//        });
//    }
    self.rfidViewDataModel = rfidViewData;
    CGFloat homeRFIDViewHeight = (showType >= 2)?114:217;
    CGFloat centerY = 0;
    CGFloat contentViewY = 0;
    [self.homeNewRFIDView  setShowType:showType surplusLevel:surplusLevel];
    self.homeNewRFIDView.size = CGSizeMake(SCREEN_WIDTH - 30, homeRFIDViewHeight);
    UIEdgeInsets inset = self.mainCollectionView.contentInset;
    CGPoint p = self.mainCollectionView.contentOffset;
    p.y += self.mainCollectionView.contentInset.top;
    if(self.advArray.count > 0){
        centerY = self.pageFlowView.bottom + self.interval + homeRFIDViewHeight/2;
        contentViewY = self.heardViewY + self.heardView.height + (self.pageFlowView.hidden?0:self.pageFlowView.height + self.pageFlowViewSpace) + self.interval + (self.requestErrView.hidden? 0 :(ServerStateViewHeight + self.interval));
        contentViewY += !self.vipTextBanner.isHidden ? self.vipTextBanner.height + self.interval : 0;
    }else{
        centerY = self.pageFlowView.top + self.interval + homeRFIDViewHeight/2;
        contentViewY = self.heardViewY + self.heardView.height + self.interval + (self.requestErrView.hidden? 0 :(ServerStateViewHeight + self.interval));
        contentViewY += !self.vipTextBanner.isHidden ? self.vipTextBanner.height + self.interval : 0;
    }
    inset.top = self.heardViewY + self.heardView.height + self.interval + (self.pageFlowView.hidden ? 0 : (self.pageFlowViewSpace + self.pageFlowView.height)) + self.thirdView.height + (self.requestErrView.hidden? 0 :(ServerStateViewHeight + self.interval));
    // 添加VIPTextBanner高度
    inset.top += !self.vipTextBanner.isHidden ? (self.vipTextBanner.height + 9) : 0;
    self.homeNewRFIDView.center = CGPointMake(SCREEN_WIDTH/2, centerY);
    [self.homeNewRFIDView layoutIfNeeded];
    self.isAnimalRFID = YES;
    if(rfidViewData != nil){
        [self.homeNewRFIDView setModelRFID:rfidViewData];
    }
    if(flag){
        self.isRFID = YES;
        self.homeNewRFIDView.alpha = 0.0;
        self.homeNewRFIDView.hidden = NO;
        self.homeNewRFIDView.transform = CGAffineTransformMakeScale(0.5, 0.5);
        inset.top = inset.top  + homeRFIDViewHeight + self.interval;
        [UIView animateWithDuration:isAnimal?1:0 animations:^{
            self.homeNewRFIDView.transform = CGAffineTransformMakeScale(1, 1);
            self.homeNewRFIDView.alpha = 1.0;
            self.mainCollectionView.contentInset = inset;
            [self.mainCollectionView setContentOffset:CGPointMake(p.x, p.y - self.mainCollectionView.contentInset.top) animated:NO];
            CGPoint p = self.mainCollectionView.contentOffset;
            p.y += self.mainCollectionView.contentInset.top;
            self.contentView.top = contentViewY + self.interval + homeRFIDViewHeight;
            self.contentView.top += !self.vipTextBanner.isHidden ? self.vipTextBanner.height : 0;
            self.contentViewY = contentViewY + self.interval + homeRFIDViewHeight;
        } completion:^(BOOL finished) {
            self.isAnimalRFID = NO;

        }];

    }else{
        self.isRFID = NO;
        UIEdgeInsets inset = self.mainCollectionView.contentInset;
        inset.top = self.heardViewY + self.heardView.height + self.interval + (self.pageFlowView.hidden ? 0 : (self.pageFlowView.height + self.pageFlowViewSpace))  + self.thirdView.height + (self.requestErrView.hidden? 0 :(ServerStateViewHeight + self.interval));
        // 添加VIPTextBanner高度
        inset.top += !self.vipTextBanner.isHidden ? (self.vipTextBanner.height + 9) : 0;
        if(showType == -1){
            self.contentViewY = contentViewY - p.y;
            self.mainCollectionView.contentInset = inset;
            [self.mainCollectionView setContentOffset:CGPointMake(p.x, p.y - self.mainCollectionView.contentInset.top) animated:NO];
            self.homeNewRFIDView.hidden = YES;
            self.isAnimalRFID = NO;
            return;
        }
        [UIView animateWithDuration:isAnimal?1:0 animations:^{
            self.homeNewRFIDView.transform = CGAffineTransformMakeScale(0.5, 0.5);
            self.homeNewRFIDView.alpha = 0;
            self.contentView.top = contentViewY - p.y;
            self.contentViewY = contentViewY - p.y;
            self.mainCollectionView.contentInset = inset;
            [self.mainCollectionView setContentOffset:CGPointMake(p.x, p.y - self.mainCollectionView.contentInset.top) animated:NO];
            } completion:^(BOOL finished) {
                self.contentViewY = contentViewY;
                self.homeNewRFIDView.hidden = YES;
                self.homeNewRFIDView.transform = CGAffineTransformMakeScale(1, 1);
                self.isAnimalRFID = NO;
            }];
    }
}

- (void)refreshShopStatus:(JCLabelBuyInfoModel *)buyInfoModel {
    [self.homeNewRFIDView refreshShopStatus:buyInfoModel];
}

- (NSMutableArray *)advArray
{
    if (!_advArray) {
        _advArray = [NSMutableArray array];
    }
    return _advArray;
}

#pragma mark -- 轮播图

- (HQFlowView *)pageFlowView
{
    if (!_pageFlowView) {

        _pageFlowView = [[HQFlowView alloc] initWithFrame:CGRectMake(15, self.heardView.bottom + 6, SCREEN_WIDTH - 30, (SCREEN_WIDTH - 30) * 66/330)];
        _pageFlowView.delegate = self;
        _pageFlowView.dataSource = self;
        _pageFlowView.minimumPageAlpha = 0;
        _pageFlowView.leftRightMargin = 0;
        _pageFlowView.topBottomMargin = 0;
        _pageFlowView.orginPageCount = _advArray.count;
        _pageFlowView.isOpenAutoScroll = YES;
        _pageFlowView.autoTime = 4.0;
        _pageFlowView.orientation = HQFlowViewOrientationHorizontal;

    }
    return _pageFlowView;
}

- (JCHomeNewRFIDView*)homeNewRFIDView{
    XYWeakSelf
    if(!_homeNewRFIDView){
        _homeNewRFIDView = [[JCHomeNewRFIDView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 30, 0)];
        _homeNewRFIDView.hidden = YES;
        [_homeNewRFIDView setClickAction:^(id x) {
            if(weakSelf.homeOperateBlock){
                weakSelf.homeOperateBlock(x);
            }
        }];
        [_homeNewRFIDView setBuyLabelAction:^(id x){
            if(weakSelf.buyCurrentLabelBlock){
                weakSelf.buyCurrentLabelBlock(x);
            }
        }];
        [_homeNewRFIDView setWifiCodeMakeAction:^{
            if(weakSelf.wifiCodeMakeBlock){
                weakSelf.wifiCodeMakeBlock();
            }
        }];
    }
    return _homeNewRFIDView;
}

- (HQImagePageControl *)pageC
{
    if (!_pageC) {

        //初始化pageControl
        if (!_pageC) {
            _pageC = [[HQImagePageControl alloc]initWithFrame:CGRectMake(0, self.pageFlowView.height - 16, self.pageFlowView.width, 8) currentImage:[UIImage imageWithColor:COLOR_NEW_THEME] andDefaultImage:[UIImage imageWithColor:HEX_RGBA(0x000000, 0.15)] size:CGSizeMake(5, 5)];
            _pageC.pageIndicatorTintColor = HEX_RGBA(0x000000, 0.15);
            _pageC.currentPageIndicatorTintColor = COLOR_NEW_THEME;
            _pageC.hidesForSinglePage = YES;
        }
        [self.pageFlowView.pageControl setCurrentPage:0];
        self.pageFlowView.pageControl = _pageC;

    }
    return _pageC;
}

#pragma mark JQFlowViewDelegate
- (CGSize)sizeForPageInFlowView:(HQFlowView *)flowView
{
    return CGSizeMake(SCREEN_WIDTH-30, self.pageFlowView.frame.size.height);
}

- (void)didSelectCell:(UIView *)subView withSubViewIndex:(NSInteger)subIndex
{
    NSLog(@"点击第%ld个广告",(long)subIndex);
    if(self.bannerOperateBlock){
        self.bannerOperateBlock(@(subIndex));
    }
}
#pragma mark JQFlowViewDatasource
- (NSInteger)numberOfPagesInFlowView:(HQFlowView *)flowView
{
    return self.advArray.count;
}
- (HQIndexBannerSubview *)flowView:(HQFlowView *)flowView cellForPageAtIndex:(NSInteger)index
{
    HQIndexBannerSubview *bannerView = (HQIndexBannerSubview *)[flowView dequeueReusableCell];
    if (!bannerView) {
        bannerView = [[HQIndexBannerSubview alloc] initWithFrame:CGRectMake(0, 0, self.pageFlowView.frame.size.width, self.pageFlowView.frame.size.height)];
        bannerView.layer.cornerRadius = 10;
        bannerView.layer.masksToBounds = YES;
        bannerView.coverView.backgroundColor = [UIColor darkGrayColor];
    }
    //在这里下载网络图片
    NSData *data = [NSData dataWithContentsOfFile:[self.advArray safeObjectAtIndex:index]];
    UIImage *image = [UIImage imageWithData:data];
    if(image != nil){
        [bannerView.mainImageView setImage:image];
    }else{
        [bannerView.mainImageView setImage:[UIImage new]];
    }
    bannerView.mainImageView.layer.cornerRadius = 10;
    //    [bannerView.mainImageView sd_setImageWithURL:[NSURL URLWithString:self.advArray[index]] placeholderImage:nil];
    return bannerView;
}

- (void)didScrollToPage:(NSInteger)pageNumber inFlowView:(HQFlowView *)flowView
{
    if(self.bannerShowBlock != nil){
        self.bannerShowBlock(@(pageNumber));
    }
    [self.pageFlowView.pageControl setCurrentPage:pageNumber];
}
#pragma mark --旋转屏幕改变JQFlowView大小之后实现该方法
- (void)viewWillTransitionToSize:(CGSize)size withTransitionCoordinator:(id)coordinator
{
    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad){
        [coordinator animateAlongsideTransition:^(id context) { [self.pageFlowView reloadData];
        } completion:NULL];
    }
}

- (BOOL)shouldAutorotateToInterfaceOrientation:(UIInterfaceOrientation)interfaceOrientation
{
    return (interfaceOrientation != UIInterfaceOrientationPortraitUpsideDown);
}


/** 刷新内容区域 **/
- (void)refreshMainViewWithBannerArr:(NSArray *)bannerArr {

}

- (void)refreshContentViewWithTemplateArr:(NSArray *)templateArr{

}

#pragma mark - UIScrollViewDelegate

// 开始拖拽时设置用户滑动标记
- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    if (scrollView == self.mainCollectionView) {
        self.isUserScrolling = YES;
    }
}

// 结束拖拽时清除用户滑动标记
- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
    if (scrollView == self.mainCollectionView) {
        if (!decelerate) {
            self.isUserScrolling = NO;
        }
    }
}

// 减速结束时清除用户滑动标记
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    if (scrollView == self.mainCollectionView) {
        self.isUserScrolling = NO;
    }
}

@end

