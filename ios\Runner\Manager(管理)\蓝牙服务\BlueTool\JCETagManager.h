//
//  JCETagManager.h
//  Runner
//
//  Created by aiden on 2023/7/31.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef enum : NSUInteger {
    JCETagConnectSuccess = 0,  //已连接
    JCETagConnectFaild, // 连接失败
    JCETagDisConnect, // 未连接
    JCETagConnecting // 连接中
} JCETagConnectStatus;

@interface JCETagManager : NSObject

/**
 电子价签小程序状态
 @param YES 进入电子价签    NO 退出电子价签
 */
+ (void)etagFlutterAppStatus:(BOOL)etagAppStatus;

/**
 断开电子价签
 @param YES 手动断开  NO 非主动断开，可触发自动回连
 */
+ (void)disconnectETag:(BOOL)isByhand;

/**
 通过EventChannel返回设备状态以刷新电子价签连接状态
 */
+ (void)refreshETagConnectStatus;

/**
 发送电子价签状态至flutter层刷新
 @param connectStatus 电子价签设备状态
 @param deviceName  电子价签设备名称
 */
+ (void)sendEtagStatusToFlutterWith:(JCETagConnectStatus)connectStatus connectedName:(NSString *)deviceName;

/**
 通过future层返回模版及商品数据 混合后返回预览图
 @param templateDataStr 组装商品数据后的模板数组字符串
 @param complate  返回预览图字节流数组
 */
+ (void)getET10GoodsMixImagesData:(NSString *)templateDataStr isBatchPreview:(BOOL)isBatchPreview complate:(XYBlock)complate;

/**
 通过eventChannel同步SDK心跳返回的屏幕信息至flutter层
 @param etagHeartInfosModel SDK返回的屏幕心跳信息
 */
+ (void)refershETagScreenStatus:(JCAPIEtagHeartInfosModel *)etagHeartInfosModel;

/**
 调用SDK连接电子价签
 @param model 设备信息model
 @param connectBlock  YES 连接成功  NO 连接失败/断开连接
 */
+ (void)connectEtagWith:(JCBluetoothModel *)model connectBlock:(XYBlock)connectBlock;

/**
 电子价签信息写入屏幕
 @param index 屏幕索引
 @param templateInfo  写入信息
 */
+ (void)writeDataToEtagScreenWith:(NSInteger)index data:(JCTemplateData *)templateModel complate:(XYBlock)complate;

/**
 终止电子价签信息写入屏幕
 */
+ (void)cancelWriteDataToEtagScreen;

/**
 电子价签固件升级
 @param crcValue  crc值
 @param path 固件文件路径
 @param hVersion 硬件版本号
 @param sVersion  固件版本号
 @param withComplete 发送完成/报错的回调
 0:升级成功
 200:取消成功
 100- 忙碌
 101-断链
 102-超时
 103-数据错误
 @param withOnProgress  发送进度
 */
+ (void)updatePrinterWithsVersion:(NSString *)sVersion path:(NSString *)path
                         crcValue:(NSString *)crcValue hVersion:(NSString *)hVersion
                     withComplete:(void(^)(int))withComplete
                   withOnProgress:(void(^)(float))withOnProgress;

/**
 设置电子价签声音开关
 @param voiceValue 声音开关
 - voiceValue: 0-全部关闭  1-打开开机的蜂鸣器 2-打开蓝牙连接的蜂鸣器 3-同时打开开机/蓝牙的蜂鸣器
 @param complete: 回调结果
 */
+ (void)setVoice:(NSInteger)voiceValue withComplete:(void(^)(int))complete;

/**
 获取电子价签设备信息
 @param complete 获取电子价签设备详情回调
 */
+ (void)getEtagDeviceDetail:(void(^)(NSDictionary *))complete;

/**
 设置电子价签自动关机时间
 @param closeTime关机时间档位
 @param complete 设置电子价签关机时间回调
 */
+ (void)setCloseTime:(NSInteger)closeTime withComplete:(void(^)(int))complete;

/**
 重置电子价签屏幕信息
 @param complete 重置电子价签回调
 */
+(void)resetTagStatusComplete:(void(^)(NSInteger))complete;
@end

NS_ASSUME_NONNULL_END
