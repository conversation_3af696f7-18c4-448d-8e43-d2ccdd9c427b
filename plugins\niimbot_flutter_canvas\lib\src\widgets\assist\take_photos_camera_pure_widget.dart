import 'dart:io';

import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart' as nLog;
import 'package:image/image.dart' as img;
import 'package:image_picker/image_picker.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/utils/loading_mix.dart';
import 'package:niimbot_flutter_canvas/src/utils/ocr_util.dart';
import 'package:niimbot_flutter_canvas/src/utils/permission_utils.dart';
import 'package:niimbot_flutter_canvas/src/widgets/assist/ocr/ocr_photo_edit_correct_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/assist/ocr/ocr_photo_edit_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/common_components.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/custom_dialog.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/svg_icon.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import '../../localization/localization_public.dart';
import '../../provider/floating_bar_visible_notifier.dart';

final nLog.Logger _logger = nLog.Logger("_TakePhotosCameraWidgetState", on: kDebugMode);

List<CameraDescription>? _cameras;

/// TakePhotosCameraWidget is the Main Application.
class TakePhotosCameraPureWidget extends StatefulWidget {
  final double maxWidth;
  final double maxHeight;
  final int imageQuality;

  /// Default Constructor
  TakePhotosCameraPureWidget({Key? key, this.maxWidth = 1000, this.maxHeight = 1000, this.imageQuality = 80})
      : super(key: key);

  @override
  State<TakePhotosCameraPureWidget> createState() => _TakePhotosCameraPureWidgetState();
}

class _TakePhotosCameraPureWidgetState extends State<TakePhotosCameraPureWidget> {
  CameraController? controller;
  double _cameraPreviewOpacity = 1.0;

  @override
  void reassemble() {
    super.reassemble();
    // controller?.resumePreview();
  }

  @override
  void initState() {
    super.initState();
    FloatingBarVisibleNotifier().forceDismiss();
    availableCameras().then((value) {
      _cameras = value;
      controller = CameraController(_cameras![0], ResolutionPreset.high, enableAudio: false);
      controller!.initialize().then((_) {
        if (!mounted) {
          return;
        }
        setState(() {});
      }).catchError((Object e) {
        if (e is CameraException) {
          switch (e.code) {
            case 'CameraAccessDenied':
              _logger.log('User denied camera access.');
              break;
            default:
              _logger.log('Handle other errors.');
              break;
          }
        }
      });
    });

    Future.delayed(Duration(milliseconds: 300), () {
      CommonComponents.setStatusBarIconBrightness(Brightness.light);
    });
  }

  Future<void> initializeCameraController(CameraDescription cameraDescription) async {
    final CameraController cameraController = CameraController(
      cameraDescription,
      ResolutionPreset.high,
      enableAudio: false,
      imageFormatGroup: ImageFormatGroup.jpeg,
    );

    controller = cameraController;

    // If the controller is updated then update the UI.
    cameraController.initialize().then((_) {
      if (!mounted) {
        return;
      }
      setState(() {});
    }).catchError((Object e) {
      if (e is CameraException) {
        switch (e.code) {
          case 'CameraAccessDenied':
            _logger.log('User denied camera access.');
            break;
          default:
            _logger.log('Handle other errors.');
            break;
        }
      }
    });
  }

  @override
  void dispose() {
    CommonComponents.setStatusBarIconBrightness(Brightness.dark);

    ///2023/12/6 Ice_Liu 释放相机资源在模态显示时，会造成界面抖动，原因未知o(╥﹏╥)o。 //iOS没有这个问题 故ios先正常处理
    if (Platform.isIOS) {
      Future.delayed(Duration(milliseconds: 100), () => controller?.dispose());
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    EdgeInsetsGeometry margin = EdgeInsets.only(top: MediaQuery.sizeOf(context).height * .06);
    if (controller == null || !controller!.value.isInitialized) {
      return Container(
        margin: margin,
        color: Colors.black,
      );
    }
    BorderRadius borderRadius = BorderRadius.all(Radius.circular(24));

    return Container(
      margin: margin,
      child: Stack(
        children: [
          Positioned.fill(
              child: Container(
            color: Colors.black,
          )),
          Positioned(
              left: (MediaQuery.sizeOf(context).width -
                      MediaQuery.sizeOf(context).height / controller!.value.aspectRatio) /
                  2.0,
              child: Opacity(
                  opacity: _cameraPreviewOpacity,
                  child: SizedBox(
                    height: MediaQuery.sizeOf(context).height,
                    // width: MediaQuery.sizeOf(context).width,
                    width: MediaQuery.sizeOf(context).height / controller!.value.aspectRatio,
                    child: ClipRRect(
                        borderRadius: BorderRadius.all(Radius.circular(12)), child: CameraPreview(controller!)),
                  ))),
          Align(
              alignment: Alignment.topCenter,
              child: Container(
                height: MediaQuery.paddingOf(context).top + 49,
                width: MediaQuery.sizeOf(context).width,
                color: Colors.transparent,
                // color: widget.fromSmartTypesetting ? Colors.transparent: Color(0xB2000000),
                padding: EdgeInsets.only(top: MediaQuery.paddingOf(context).top + 23, right: 8),
                child: Row(
                  children: [
                    InkWell(
                      onTap: () async {
                        //CanvasPluginManager
                        await controller?.pausePreview();
                        Navigator.of(context).pop();
                      },
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(26, 0, 0, 17),
                        child: Icon(
                          Icons.close_outlined,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    Spacer(),
                  ],
                ),
              )),
          Align(
            alignment: Alignment.bottomCenter,
            child: Padding(
              padding: const EdgeInsets.only(bottom: 77.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Row(
                    children: [
                      Spacer(
                        flex: 2,
                      ),
                      Material(
                          borderRadius: borderRadius,
                          color: controller?.value.flashMode == FlashMode.torch ? Color(0x66FFFFFF) : Color(0x66000000),
                          child: InkWell(
                              borderRadius: borderRadius,
                              onTap: () async {
                                try {
                                  await controller?.setFlashMode(
                                      controller?.value.flashMode == FlashMode.torch ? FlashMode.off : FlashMode.torch);
                                  setState(() {});
                                } catch (_) {}
                              },
                              child: Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  borderRadius: borderRadius,
                                ),
                                child: Center(
                                  child: Padding(
                                    padding: const EdgeInsets.all(6.0),
                                    child: SvgIcon(
                                      'assets/common/camera_flashlight.svg',
                                      useDefaultColor: false,
                                    ),
                                  ),
                                ),
                              ))),
                      Spacer(
                        flex: 3,
                      ),
                      InkWell(
                          borderRadius: borderRadius,
                          onTap: () async {
                            /// 模拟快门闪动
                            setState(() {
                              _cameraPreviewOpacity = 0.0;
                            });
                            Future.delayed(Duration(milliseconds: 150), () {
                              setState(() {
                                _cameraPreviewOpacity = 1.0;
                              });
                            });
                            XFile? imageFile = await controller?.takePicture();
                            await _turnOffFlash();
                            controller?.pausePreview();
                            Navigator.pop(context, imageFile);
                          },
                          child: SvgIcon(
                            'assets/common/camera_shutter.svg',
                            useDefaultColor: false,
                          )),
                      Spacer(
                        flex: 3,
                      ),
                      Material(
                        borderRadius: borderRadius,
                        color: Color(0x66000000),
                        child: InkWell(
                          borderRadius: borderRadius,
                          onTap: _pickFromPhotoAlbum,
                          child: Container(
                            width: 48,
                            height: 48,
                            decoration: BoxDecoration(
                              borderRadius: borderRadius,
                            ),
                            child: Center(
                              child: Padding(
                                padding: const EdgeInsets.all(6.0),
                                child: SvgIcon(
                                  'assets/common/photo_album.svg',
                                  useDefaultColor: false,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Spacer(
                        flex: 2,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _turnOffFlash() async {
    try {
      if (controller?.value.flashMode != FlashMode.off) {
        await controller?.setFlashMode(FlashMode.off);
        await Future.delayed(Duration(milliseconds: 100));
        setState(() {});
      }
    } catch (_) {}
  }

  _pickFromPhotoAlbum() async {
    //图片
    if (Platform.isAndroid) {
      bool psState = await PermissionDialog.requestImage(context);
      if (psState) {
        await _turnOffFlash();
        controller?.pausePreview();

        XFile? imageFile = await _pickImage();
        if (imageFile == null) {
          controller?.resumePreview();
          return;
        }
        Navigator.pop(context, imageFile);
      }
    } else {
      /// 请求相册权限
      if (await PermissionUtils.checkPhotoAlbumGranted() != true) {
        // 展示alert
        showCupertinoAlert(context,
            title: intlanguage("app100000869", '照片权限未开启'),
            content: intlanguage("app01293", '请在"设置-精臣云打印-相册" ， 允许精臣云打印访问你的手机相册'),
            cancelDes: intlanguage("app100000866", '暂不'),
            confirmDes: intlanguage("app01308", '去设置'),
            cancelAction: () {}, confirmAction: () async {
          // 跳转设置页
          await PhotoManager.openSetting();
        });
        return;
      }

      await _turnOffFlash();
      controller?.pausePreview();

      XFile? imageFile = await _pickImage();
      if (imageFile == null) {
        controller?.resumePreview();
        return;
      }
      Navigator.pop(context, imageFile);
    }
  }

  Future<XFile?> _pickImage() async {
    final ImagePicker _picker = ImagePicker();
    // Pick an image
    final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: widget.maxWidth,
        maxHeight: widget.maxHeight,
        imageQuality: widget.imageQuality);
    return image;
  }
}
