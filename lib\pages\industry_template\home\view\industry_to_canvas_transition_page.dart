import 'dart:async';


import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:niim_login/login_plugin/macro/color.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_logic.dart';
import 'package:text/pages/industry_template/home/<USER>/label_usage_record.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_state.dart';
import 'package:text/pages/industry_template/select_label/create_label_page.dart';
import 'package:text/pages/industry_template/select_label/label_selector_page.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/log_utils.dart';
import 'package:text/utils/svg_icon.dart';

import '../../../../utils/cachedImageUtil.dart';

class IndustryToCanvasTransitionPage extends StatefulWidget {
  const IndustryToCanvasTransitionPage({Key? key}) : super(key: key);

  @override
  State<IndustryToCanvasTransitionPage> createState() => _IndustryToCanvasTransitionPageState();
}

class _IndustryToCanvasTransitionPageState extends State<IndustryToCanvasTransitionPage>
    with SingleTickerProviderStateMixin {
  final IndustryTemplateState state = Get.find<IndustryTemplateLogic>().state;

  late Animation<double> positioned;

  late Animation<double> opacity;

  late AnimationController _animationController;
  late Function selectSuccessFun;

  /// 是否正在展示
  bool isShow = false;

  @override
  void initState() {
    /// 设置动画
    _animationController = AnimationController(duration: const Duration(milliseconds: 250), vsync: this);
    _animationController.addStatusListener((status) {
      Log.d("动画状态：$status");
      if (status == AnimationStatus.dismissed) {
        CustomNavigation.pop(result: {'isAnimated': false});
        state.showTransitionPageState = 0;
      } else if (status == AnimationStatus.completed) {
        state.showTransitionPageState = 1;
      } else {
        state.showTransitionPageState = 2;
      }
    });
    selectSuccessFun = () {
      if (state.showTransitionPageState == 1) {
        _animationController.reverse();
      }
    };

    /// 卡片Postioned动画
    positioned = Tween(begin: -800.0, end: 0.0)
        .animate(CurvedAnimation(parent: _animationController, curve: Curves.linearToEaseOut));

    /// 背景不透明动画
    opacity = Tween(begin: 0.0, end: 0.35)
        .animate(CurvedAnimation(parent: _animationController, curve: Curves.linearToEaseOut));

    /// FlutterBoost返回问题处理
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _animationController.forward();

      BoostContainer.of(context)?.backPressedHandler = () {
        if (!mounted) {
          CustomNavigation.pop();
        } else {
          if (Navigator.of(context).canPop()) {
            Navigator.of(context).pop();
          } else {
            _animationController.reverse();
          }
          state.showTransitionPageState = 0;
        }
      };
    });

    super.initState();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  _showStatus(IndustryToCanvasTransitionState transitionState) {
    switch (transitionState) {
      case IndustryToCanvasTransitionState.existHistory:
        return _ExistHistory(
          model: state.labelCanvasModel,
          selectSuccessFun: selectSuccessFun,
        );
      case IndustryToCanvasTransitionState.transitionToLabelUsing:
      case IndustryToCanvasTransitionState.connectAndRecognizedLabel:
        return _TransitionStatus(
          selectSuccessFun: selectSuccessFun,
        );
      default:
        return _TransitionPage(
          selectSuccessFun: selectSuccessFun,
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Stack(
        children: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              if (state.showTransitionPageState == 1) {
                _animationController.reverse();
              }
            },
            child: AnimatedBuilder(
              animation: opacity,
              builder: (BuildContext context, Widget? child) {
                return Container(
                  color: Colors.black.withOpacity(opacity.value),
                  child: child,
                );
              },
              child: const SizedBox.expand(),
            ),
          ),
          AnimatedBuilder(
            animation: positioned,
            builder: (context, child) {
              return Positioned(
                left: 0,
                right: 0,
                bottom: positioned.value,
                child: child ?? const SizedBox.shrink(),
              );
            },
            child: Column(
              children: [
                // const Spacer(),
                Container(
                  margin: EdgeInsets.only(left: 16, right: 16, bottom: 16 + MediaQuery.paddingOf(context).bottom),
                  decoration:
                      const BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(26)), color: Colors.white),
                  child: Column(
                    children: [
                      Stack(
                        children: [
                          // 新建标签
                          Row(
                            children: [
                              Expanded(
                                child: Center(
                                  child: Padding(
                                    padding: const EdgeInsets.only(top: 18, bottom: 5, left: 45, right: 45),
                                    child: Obx(() => Text(
                                          state.transitionState.value ==
                                                  IndustryToCanvasTransitionState.transitionToLabelUsing
                                              ? intlanguage('app100000581', '识别到已安装标签纸')
                                              : intlanguage('app100000636', '请确定标签纸'),
                                          textAlign: TextAlign.center,
                                          style: const TextStyle(
                                              fontSize: 18, fontWeight: FontWeight.w600, color: Colors.black),
                                        )),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Positioned(
                            top: 12,
                            right: 15,
                            child: GestureDetector(
                              onTap: () => _animationController.reverse(),
                              child: const SvgIcon('assets/images/label_create/close_page.svg'),
                            ),
                          )
                        ],
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Obx(() {
                        return _showStatus(state.transitionState.value);
                      }),
                    ],
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}

/// 更换标签纸，打开选择标签纸
_searchLabelAndNew(BuildContext context, IndustryTemplateLogic logic, Function selectSuccessFun) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
    builder: (BuildContext context) {
      return LabelSelectorPage(
        isNeedPop: true,
        onChanged: (value) {
          selectSuccessFun();
          Timer(const Duration(milliseconds: 500), () {
            logic.state.labelCanvasModel = LabelUsageRecord.fromJson(value);
            logic.gotoCanvasPage(isSelected: true, completion: () => Navigator.of(context).pop());
          });
        },
      );
    },
  ).then((value) {
    if (value != null) {
      if (value is Map && value.isEmpty) return;
      selectSuccessFun();
      Timer(const Duration(milliseconds: 500), () {
        logic.state.labelCanvasModel = LabelUsageRecord.fromJson(value);
        logic.gotoCanvasPage(isSelected: true);
      });
    }
  });
}

/// 自定义空白标签纸
_customLabel(BuildContext context, IndustryTemplateLogic logic, Function selectSuccessFun) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
    builder: (BuildContext context) {
      return const CreateLabelPage(scene: CreateLabelScene.industryTemplate);
    },
  ).then((value) {
    if (value != null) {
      selectSuccessFun();
      Timer(const Duration(milliseconds: 500), () {
        Log.d("自定义json值1:$value");
        logic.state.labelCanvasModel = LabelUsageRecord.fromJson(value);
        logic.gotoCanvasPage(isCustomLabel: true);
      });
    }
  });
}

class _ExistHistory extends StatefulWidget {
  final LabelUsageRecord? model;
  final Function selectSuccessFun;

  const _ExistHistory({Key? key, required this.model, required this.selectSuccessFun}) : super(key: key);

  @override
  State<_ExistHistory> createState() => _ExistHistoryState();
}

class _ExistHistoryState extends State<_ExistHistory> {
  final IndustryTemplateLogic logic = Get.find<IndustryTemplateLogic>();

  final IndustryTemplateState state = Get.find<IndustryTemplateLogic>().state;

  /// 新建标签纸，默认使用上次使用的
  _newLabel() {
    widget.selectSuccessFun();
    Timer(const Duration(milliseconds: 500), () {
      logic.gotoCanvasPage();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: _newLabel,
          child: CacheImageUtil().netCacheImage(
            imageUrl: widget.model?.backgroundImage ?? '',
            imageBuilder: (BuildContext context, ImageProvider imageProvider) {
              return Container(
                decoration: BoxDecoration(boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.07),
                    offset: const Offset(0, 4),
                    blurRadius: 40,
                  )
                ]),
                child: Image(
                  image: imageProvider,
                  width: 226,
                  height: 140,
                ),
              );
            },
            errorWidget: const SvgIcon('assets/images/label_create/label_placeholder.svg'),
          ),
        ),
        const SizedBox(height: 16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                child: Text(
                  widget.model?.name ?? '',
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w400,
                    color: KColor.title,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 3,
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(
                width: 6,
              ),
              Container(
                decoration: BoxDecoration(color: KColor.content_background, borderRadius: BorderRadius.circular(4)),
                padding: const EdgeInsets.symmetric(vertical: 1, horizontal: 5),
                child: Text(
                  intlanguage('app100000567', '上次使用'),
                  style: const TextStyle(fontSize: 10, fontWeight: FontWeight.w400, color: KColor.COLOR_999999),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 6,
        ),
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () => _searchLabelAndNew(context, logic, widget.selectSuccessFun),
          child: Column(
            children: [
              const SizedBox(
                height: 6,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    intlanguage('app01589', '更换'),
                    style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w600, color: KColor.RED),
                  ),
                  const SizedBox(
                    width: 4,
                  ),
                  SvgIcon(Directionality.of(context) == TextDirection.rtl
                      ? 'assets/images/label_create/left_arrow_red.svg'
                      : 'assets/images/label_create/right_arrow_red.svg')
                ],
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 14,
        ),
        GestureDetector(
          onTap: _newLabel,
          child: Row(
            children: [
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 24),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  decoration: BoxDecoration(color: KColor.RED, borderRadius: BorderRadius.circular(12)),
                  child: Text(
                    intlanguage('app100000667', '以此标签使用模版'),
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: KColor.WHITE),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 12,
        ),
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () => _customLabel(context, logic, widget.selectSuccessFun),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SvgIcon('assets/images/label_create/add_black.svg'),
                const SizedBox(
                  width: 3,
                ),
                Flexible(
                  child: Text(
                    intlanguage('app100000669', '自定义空白标签使用模版'),
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: KColor.title),
                  ),
                )
              ],
            ),
          ),
        ),
        const SizedBox(
          height: 20,
        ),
      ],
    );
  }
}

class _TransitionPage extends StatefulWidget {
  final Function selectSuccessFun;

  const _TransitionPage({Key? key, required this.selectSuccessFun}) : super(key: key);

  @override
  State<_TransitionPage> createState() => _TransitionPageState();
}

class _TransitionPageState extends State<_TransitionPage> {
  final IndustryTemplateLogic logic = Get.find<IndustryTemplateLogic>();

  final IndustryTemplateState state = Get.find<IndustryTemplateLogic>().state;

  /// 连接打印机
  _recognizedLabelAndNew() {
    CustomNavigation.gotoNextPage('DeviceConnectPage', {}).then((value) {});
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          children: [
            const SizedBox(height: 32),
            state.transitionState.value == IndustryToCanvasTransitionState.noConnect
                ? const SvgIcon('assets/images/label_create/printer_gray.svg')
                : const SvgIcon('assets/images/label_create/printer_label_error.svg'),
            const SizedBox(height: 12),
            Text(
              state.transitionState.value == IndustryToCanvasTransitionState.noConnect
                  ? intlanguage('app100000568', '打印机未连接，未识别到标签纸')
                  : (state.transitionState.value == IndustryToCanvasTransitionState.connectUnrecognizedMachine
                      ? intlanguage('app100000588', '当前打印机不具有标签纸识别功能')
                      : intlanguage('app100000506', '未识别到标签纸')),
              textAlign: TextAlign.center,
              style: state.transitionState.value == IndustryToCanvasTransitionState.noConnect
                  ? const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: KColor.COLOR_999999)
                  : const TextStyle(fontSize: 13, fontWeight: FontWeight.w600, color: Colors.black),
            ),
            Visibility(
                visible: state.transitionState.value != IndustryToCanvasTransitionState.noConnect,
                child: Column(
                  children: [
                    const SizedBox(
                      height: 8,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 33),
                      child: Text(
                        state.transitionState.value == IndustryToCanvasTransitionState.connectUnrecognizedMachine
                            ? intlanguage('app100000671', '当前打印机不具有自动识别功能，请用以下方式。')
                            : intlanguage('app100000641', '请检查标签纸是否安装正确、且为精臣可识别标签纸，请尝试以下方式。'),
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: KColor.COLOR_999999),
                      ),
                    ),
                    const SizedBox(
                      height: 6,
                    ),
                  ],
                )),
            const SizedBox(
              height: 24,
            ),
            GestureDetector(
              onTap: () => state.transitionState.value == IndustryToCanvasTransitionState.noConnect
                  ? _recognizedLabelAndNew()
                  : _searchLabelAndNew(context, logic, widget.selectSuccessFun),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
                      decoration: BoxDecoration(color: KColor.RED, borderRadius: BorderRadius.circular(12)),
                      child: Text(
                        state.transitionState.value == IndustryToCanvasTransitionState.noConnect
                            ? intlanguage('app01096', '连接打印机')
                            : intlanguage('app100000668', '查找标签纸并使用模版'),
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: KColor.WHITE),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Visibility(
                visible: state.transitionState.value == IndustryToCanvasTransitionState.noConnect,
                child: Column(
                  children: [
                    const SizedBox(
                      height: 25,
                    ),
                    GestureDetector(
                      onTap: () => _searchLabelAndNew(context, logic, widget.selectSuccessFun),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const SvgIcon('assets/images/label_create/search_new.svg'),
                          const SizedBox(
                            width: 6,
                          ),
                          Flexible(
                            child: Text(
                              intlanguage('app100000668', '查找标签纸并使用模版'),
                              textAlign: TextAlign.center,
                              style: const TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: KColor.title),
                            ),
                          )
                        ],
                      ),
                    ),
                  ],
                )),
            SizedBox(
              height: state.transitionState.value == IndustryToCanvasTransitionState.noConnect ? 25 : 18,
            ),
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () => _customLabel(context, logic, widget.selectSuccessFun),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SvgIcon('assets/images/label_create/add_black.svg'),
                  const SizedBox(
                    width: 6,
                  ),
                  Flexible(
                    child: Text(
                      intlanguage('app100000669', '自定义空白标签使用模版'),
                      style: const TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: KColor.title),
                      textAlign: TextAlign.center,
                    ),
                  )
                ],
              ),
            ),
            SizedBox(
              height: state.transitionState.value == IndustryToCanvasTransitionState.noConnect ? 33 : 43,
            ),
          ],
        ),
      );
    });
  }
}

class _TransitionStatus extends StatefulWidget {
  final Function selectSuccessFun;

  const _TransitionStatus({Key? key, required this.selectSuccessFun}) : super(key: key);

  @override
  State<_TransitionStatus> createState() => _TransitionStatusState();
}

class _TransitionStatusState extends State<_TransitionStatus> {
  final IndustryTemplateLogic logic = Get.find<IndustryTemplateLogic>();

  final IndustryTemplateState state = Get.find<IndustryTemplateLogic>().state;

  /// 新建标签纸，使用已连接的打印内标签纸
  _newLabel() {
    widget.selectSuccessFun();
    Timer(const Duration(milliseconds: 500), () {
      logic.gotoCanvasPage();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: _newLabel,
          child: Container(
            child: CacheImageUtil().netCacheImage(
                imageUrl: state.labelCanvasModel?.backgroundImage ?? '',
                errorWidget: const SvgIcon('assets/images/label_create/label_placeholder.svg'),
                width: 244,
                height: 150),
            decoration: BoxDecoration(boxShadow: [
              BoxShadow(color: Colors.black.withOpacity(0.07), offset: const Offset(0, 4), blurRadius: 30)
            ]),
          ),
        ),
        const SizedBox(
          height: 12,
        ),
        Text(
          state.labelCanvasModel?.name ?? '',
          style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: KColor.COLOR_999999),
        ),
        const SizedBox(
          height: 35,
        ),
        GestureDetector(
          onTap: _newLabel,
          child: Row(
            children: [
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 24),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  decoration: BoxDecoration(color: KColor.RED, borderRadius: BorderRadius.circular(12)),
                  child: Text(
                    intlanguage('app100000642', '以此标签纸编辑模板'),
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: KColor.WHITE),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 24,
        ),
      ],
    );
  }
}
