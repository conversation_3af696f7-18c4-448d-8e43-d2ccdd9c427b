//
//  JCNPSWebviewAlert.m
//  Runner
//
//  Created by ch<PERSON><PERSON> on 2024/3/6.
//

#import "JCNPSWebviewAlert.h"
#import "JCWebviewPreloadManager.h"
#import "JCNPSWebView.h"

@interface JCNPSWebviewAlert()
@property(nonatomic,copy)NSString * webKey;
@property(nonatomic,copy)NSString * sku;
@property(copy,nonatomic) NSString *source;
@end

@implementation JCNPSWebviewAlert

-(instancetype)initWithFrame:(CGRect)frame webKey:(NSString *)webKey sku:(NSString *)sku source:(NSString *)source
{
    if(self = [super initWithFrame:frame]){
        self.source = source;
        self.webKey = webKey;
        //self.isNeedClearBg = true;
        self.sku = sku;
        self.frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
        [self prepareUI];
    }
    return self;
}

-(void)prepareUI{
    //self.type = MMPopupTypeSheet;
    self.backgroundColor = UIColor.clearColor;
    JCWebviewPreloadManager * manager = [JCWebviewPreloadManager defalutManager];
    JCNPSWebView * web = (JCNPSWebView *)[manager getWebView:self.webKey];
    web.source = self.source;
    web.frame = CGRectMake(0, 0,SCREEN_WIDTH, SCREEN_HEIGHT);
    [self addSubview:web];
    self.layer.masksToBounds = YES;
    self.clipsToBounds = YES;
    XYWeakSelf
    [web setCloseClick:^(NSNumber * isRemove) {
        if([isRemove boolValue] == TRUE){
            [manager removeWebWithWebKey:weakSelf.webKey];
            [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"closeNps" arguments:nil result:nil];
            if(weakSelf.closeClick != nil){
                weakSelf.closeClick();
            }else{
                [[NSNotificationCenter defaultCenter] postNotificationName:JCCloseNps object:nil];
            }
        }
        if([isRemove boolValue] == false){
            [weakSelf hide];
        }
    }];
}

- (void)setUniAppId:(NSString *)uniAppId {
  if (_uniAppId != uniAppId) {
    _uniAppId = uniAppId;
  }
  JCWebviewPreloadManager * manager = [JCWebviewPreloadManager defalutManager];
  JCNPSWebView * web = (JCNPSWebView *)[manager getWebView:self.webKey];
  web.uniAppId = uniAppId;
}

-(void)show{
    
    // 网络错误
    if (NETWORK_STATE_ERROR) {
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
        return;
    }
    JCWebviewPreloadManager * manager = [JCWebviewPreloadManager defalutManager];
    JCNPSWebView * web = (JCNPSWebView *)[manager getWebView:self.webKey];
    web.sku = self.sku;
    BOOL isFronPrintSetting = NO;
    UIViewController *viewController = [XYTool getCurrentVC];
    if([viewController isKindOfClass:[FBFlutterViewContainer class]] && [((FBFlutterViewContainer *)viewController).name isEqualToString:@"printSetting"]){
      isFronPrintSetting = YES;
    }
    if((web == nil || web.loadSuccess == false)){
        if(!isFronPrintSetting){
          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000661", @"加载失败")];
          if(web.loadUrl != nil && web.loadUrl.length > 0){
              web.loadSuccess = false;
              [web loadUrl:web.loadUrl];
          }
        }
        return;
    }
    [[UIApplication sharedApplication].keyWindow addSubview:self];
}

-(void)hide{
    [self removeFromSuperview];
}

-(void)dealloc{
    
}

@end
