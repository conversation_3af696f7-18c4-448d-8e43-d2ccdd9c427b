//
//  JCTemplateData.h
//  Runner
//
//  Created by xingling xu on 2020/3/11.
//  Copyright © 2020 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <Foundation/Foundation.h>
#import "JCElementModel.h"
#import "JCPrintRecordModel.h"
#import "JCGoodDetailInfo.h"
#import <YYModel/YYModel.h>
static NSString *const background_image_component = @",";

#define  YYModel_Encode_And_Copy_Method_Implementation \
- (void)encodeWithCoder:(NSCoder *)aCoder { [self yy_modelEncodeWithCoder:aCoder]; }\
- (id)initWithCoder:(NSCoder *)aDecoder { self = [super init]; return [self yy_modelInitWithCoder:aDecoder]; }\
- (NSUInteger)hash { return [self yy_modelHash]; }\
- (BOOL)isEqual:(id)object { return [self yy_modelIsEqual:object]; }
#define instant_time_version  @"1.7.0.1"
@class JCProfile,JCProfileExtrain;


@interface JCTemplateData : XYBaseModel <NSCoding, NSCopying>
@property (nonatomic, copy)     NSString *idStr;                        //  模版id
@property (nonatomic, copy)     NSString *name;                         //  模块名称
@property (nonatomic, copy)     NSString *thumbnail;                    //  缩略图
@property (nonatomic, copy)     NSString<Optional> *localThumbnail;               //  本地缩略图
@property (nonatomic, copy)     NSString *contentThumbnail;             //  前景图
@property (nonatomic, copy)     NSString *backgroundImage;              //  背景图url路径（传SDK时使用base64）
@property (nonatomic, copy)     NSArray *localBackground;               //  本地背景图地址
@property (nonatomic, copy)     NSString *previewImage;                 //  预览图url路径
@property (nonatomic, copy)     NSString *des;                          //  模版描述 可用于全文检索
@property (nonatomic, assign)   NSInteger rotate;                       //  模板旋转角度（模板整体旋转,中心点为锚点，范围0、90、180、270）
@property (nonatomic, assign)   CGFloat cableLength;                    //  线缆纸张 尾巴长度,
@property (nonatomic, assign)   NSInteger cableDirection;               //  线缆尾巴方向:0上,  1右,  2下,  3左,
@property (nonatomic, assign)   NSInteger paperType;                    //  纸张类型: 1:间隙纸 2:黑标纸 3:连续纸 4:定孔纸 5:透明纸 6 标牌 10 黑标间隙纸
@property (nonatomic, assign)   NSInteger consumableType;               //  耗材类型 1：标签 2：标牌
@property (nonatomic, copy)     NSString *isEdited;                     //  特供版行业模版状态：1 待编辑 2 已编辑
@property (nonatomic, assign)   BOOL commodityTemplate;                 //商品模版
@property (nonatomic, assign)   BOOL isCable;                           //  是否线缆标签
@property (nonatomic, assign)   CGFloat width;                          //  模板宽度
@property (nonatomic, assign)   CGFloat height;                         //  模板高度
@property (nonatomic, copy)     NSArray *margin;                        //  边距 double （不打印区域）（上右下左）(单位毫米)。
@property (nonatomic, copy)     NSArray *paperColor;                    //[[0,0,0], [230.0.18]]
@property (nonatomic, copy)     NSArray *names;                         //名称
@property (nonatomic, copy)     NSArray *labelNames;                    //原始标签名称
@property (nonatomic, copy)     NSDictionary *usedFonts;                //  使用到的字体集合
@property (nonatomic, copy)     NSString *unit;                         //  度量单位 mm px
@property (nonatomic, copy)     NSString *platformCode;                 //  最后更新平台Code
@property (nonatomic, copy)     NSDictionary *externalData;             //  excel导入转换为相应格式
@property (nonatomic, copy)     NSDictionary *task;                     //  目前保存的是针对externalData 修改过的字段
@property (nonatomic, assign)   NSInteger currentPage;                  //  如果有导入excel记录当前的页数，从1开始
@property (nonatomic, assign)   NSInteger totalPage;                    //  如果有导入excel记录最大的页数，从1开始
@property (nonatomic, copy)     NSArray<JCElementModel> *elements;      //  画板上的元素
@property (nonatomic, strong)   JCProfile *profile;                     //  模板配置信息
@property (nonatomic, assign)   BOOL isMutableBackground;               //  是否多背景
@property (nonatomic, assign)   NSInteger multipleBackIndex;             //  多背景当前选择的index
@property (nonatomic, assign)   BOOL   isSelected;                      //  是否被选中
@property (nonatomic, assign)   BOOL   isPrintHistory;                  //  是否是打印记录
@property (nonatomic, assign)   JCLocalType localType;                  //  本地保存逻辑 (本地字段)
@property (nonatomic, assign)   NSInteger fromOldVersion;               //  是否来自数据迁移的老版本：4.0之前的版本
@property (nonatomic, assign)   NSInteger paccuracy;                    //  模板绘制精度
@property (nonatomic, assign)   NSInteger paccuracyName;                //  模板绘制精度
@property(nonatomic,copy) NSString *printMethodCode;                    //打印模式（1 热敏 2 碳带）
@property(nonatomic,copy) NSString *useTime;                            //使用时间
@property(nonatomic,copy) NSArray<JCPrintRecordModel> *printedProcessList;      //打印记录
@property(nonatomic,copy) NSArray<JCGoodDetailInfo> *goodsList;         //商品列表
@property(nonatomic,copy) NSString *updateTime;
@property(nonatomic,copy) NSString *createTime;
@property(nonatomic,copy) NSString *barcode;                            //商城 条码
@property(nonatomic,copy) NSString *labelId;                            //模版来源 标签ID
@property(nonatomic,copy) NSString *version;                            //模版版本
@property (nonatomic, assign)  BOOL hasVipRes;                         //是否存有Vip资源
@property (nonatomic, assign)  BOOL vip;                               //是否是Vip模版
@property (nonatomic, assign)  BOOL needLoadRotateImage;               //是否是Vip模版
@property (nonatomic, assign)  NSInteger canvasRotate;                   //画板旋转角度
@property(nonatomic,copy) NSString *originTemplateId;                //原始模版ID
@property(nonatomic,copy)   NSString *sourceOfIndustryTemplateId;         //原始行业模版ID
@property(nonatomic,copy)   NSString *cloudTemplateId;         //原始行业模版ID
@property (nonatomic, copy)  NSDictionary *commodityInfo;                // 商品信息
@property (nonatomic, copy)  NSString *recentUserId;                //最近使用Userid
@property (nonatomic, copy)  NSString *temporaryId;                 //模版临时Id 用于用户模版替换背景时使用
@property (nonatomic, copy)  NSString *buyBarCode;

@property (nonatomic, copy)  NSArray *supportedEditors;             //支持的排版场景
//服务端接收字段
@property (nonatomic,copy)NSArray<NSDictionary *> *dataSources; // 新版本数据绑定对齐新加字段
@property(nonatomic,copy)NSDictionary * dataSourceModifies;   // 新版本数据绑定对齐新加字段
@property(nonatomic,copy)NSDictionary * dataSourceBindInfo;     // 新版本数据绑定对齐新加字段
//和flutter交互用的字段
@property (nonatomic,strong)NSArray<NSDictionary *> *dataSource; // 新版本数据绑定对齐新加字段
@property (nonatomic,strong)NSArray<NSDictionary *> *inputAreas; // 会议小程序区域
@property(nonatomic,copy)NSDictionary * modify;     // 新版本数据绑定对齐新加字段
@property(nonatomic,copy)NSDictionary * bindInfo;   // 新版本数据绑定对齐新加字段
@property(nonatomic,assign)NSInteger printPage;   // 模板批量打印份数
//json版本号
@property(nonatomic,copy)NSString * templateVersion;  //json版本号
//标签材质说明
@property(nonatomic,copy)NSString * consumableTypeTextId;

//线缆schema
@property(nonatomic,copy)NSString * layoutSchema;

//线缆schema
@property(nonatomic,assign)BOOL isCableLabel;

//危废schema
@property(nonatomic,assign)BOOL isDangerLabel;

@property(nonatomic,assign)BOOL hasLabelInfo;

@property(nonatomic,copy)NSString *requestBarCode;

+ (void)checkVipResourceVersion:(JCTemplateData *)tData;
/** 更新本地化的字段 *///
- (void)updateLocalProperty;

//是否云模板
- (BOOL)isCloudTemplate;

//是否商品模板
- (BOOL)isBusinessTemplate;

//重置画板元素层级
- (void)resetElementIndex;

// 创建默认文本元素
- (void)createDefaultTextModel;

// 获取默认字体
- (NSDictionary *)getCurrentFonts;

// 获取需要的所有字体
- (NSDictionary *)getNeedFonts;

// 清除新加的json对齐字段
-(void)clearTemplateNewJsonData;

//是否是pc新保存的模版数据
-(BOOL)isFromPcExcel;

//获取商品库模板默认绑定信息
- (NSDictionary *)getDefaultDataBindInfo;

//获取商品库模板元素默认修改信息
- (NSDictionary *)getDefaultDataSourceModifies;

//是否是新商品模板
-(BOOL)isNewCommodityTemplate;

//重制元素默认值
- (void)resetElementValue;

//获取当前模板支持版本
- (NSString *)getCurrentTemplateVersion;

//模版是否包含自定义商品库字段
-(BOOL)isHaveCustomField;

//是否包含商品库自定义字段
-(BOOL)isHaveTextElement;

//是否只有文本元素
-(BOOL)isOnlyHaveTextElement;

//设置模板本地路径等数据
-(void)resetTemplateLocalPath;

//是否是商品库模版 新结构或者老结构的商品库模版
-(BOOL)isNewOrOldCommodityTemplate;

//通过对比更新时间判断模板是否更新过
-(BOOL)templateHasUpdateWith:(JCTemplateData *)newTemplateDat;

//替换rfid相关模版信息
-(JCTemplateData *)refreshWithRfidData:(JCTemplateData *)data;


//模版图片元素是否灰阶16
-(BOOL)isHaveGray16Element;

//模版是否存在单色灰阶
-(BOOL)isHaveSingleColorGrayElement;

//重置元素中图片为非16色灰阶
-(void)resetUnGray16Element;

//重置元素图本地地址
- (void)resetElementLocalPath;
@end

/* JCProfile : 模板扩展属性
 */
@interface JCProfile : XYBaseModel <NSCoding, NSCopying>
@property (nonatomic, copy)     NSString *barcode;                      //  "一维码",可用于查询
@property (nonatomic, copy)     NSString *keyword;                      //  "关键词",//可用于全文检索
@property (nonatomic, copy)     NSString *hardwareSeriesId;             //  "硬件系列ID",//(SDK技术分类)
@property (nonatomic, copy)     NSString *hardwareSeriesName;           //  "硬件系列",//(SDK分类描述，如D11、U11都属于D11系列)
@property (nonatomic, copy)     NSString *machineId;                    //  ""90,91,16""
@property (nonatomic, copy)     NSString *machineName;                  //  "适配机型（B11、B3S、B50）"
@property (nonatomic, strong)   JCProfileExtrain *extrain;              //  自定义扩展属性
@end

@interface JCProfileExtrain : XYBaseModel <NSCoding, NSCopying>
@property (nonatomic, assign)   BOOL isCustom;                          //  是否订制标签模板 (固定资产)
@property (nonatomic, copy)     NSString *folderId;                     //  所属文件夹Id,
@property (nonatomic, copy)     NSString *adaptPlatformCode;              //  适配平台Id
@property (nonatomic, copy)     NSString *adaptPlatformName;            //  适配平台名称
@property (nonatomic, copy)     NSString *userId;                       //  创建的用户id
@property (nonatomic, copy)     NSString *industryId;                   //  行业分类id
@property (nonatomic, copy)     NSString *commodityCategoryId;          //  商品分类id
@property (nonatomic, copy)     NSString *downloadCount;                //  下载次数
@property (nonatomic, assign)   BOOL isDelete;                             //  是否删除
@property (nonatomic, copy)     NSString *createTime;                   //  创建时间,
@property (nonatomic, copy)     NSString *updateTime;                   //  最后更新时间
@property (nonatomic, assign)   BOOL isHot;                             //  是否是热门模板
@property (nonatomic, copy)     NSString *sortDependency;               //  服务端返回用于排序的序号
@property (nonatomic, copy)     NSString *clickNum;                     //  模板点击量、访问量
@property (nonatomic, copy)     NSString *templateType;                 //  模板类型：0_普通用户模板, 1_云模板,2_用户模板样例 (商品模板),4_云模板样例
@property (nonatomic, copy)     NSString * templateClass;                 //  0 标签    1 模版
@property (nonatomic, assign)   BOOL isPrivate;                         //  模板是否私有 ：0_公开, 1_私有,
@property (nonatomic, copy)     NSString *amazonCodeBeijing;            //  亚马逊条码（北京）
@property (nonatomic, copy)     NSString *amazonCodeWuhan;              //  亚马逊条码（武汉）
@property (nonatomic, copy)     NSString *sparedCode;                   //  备用条码
@property (nonatomic, copy)     NSString *virtualBarCode;               //  备用条码2（厂内虚拟旧条码）
@property (nonatomic, assign)   BOOL isNewPath;                         //  是否是新版路径
@property (nonatomic, assign)   BOOL isMobileTemplete;                  //  是否移动端模板
@property (nonatomic, copy)     NSString *resourceVersion;                  //  素材版本
@property (nonatomic, copy)     NSString *phone;                        //  模板关联的用户手机号
@property (nonatomic, copy)     NSString *goodsCode;                    //  商品条码
@property (nonatomic, copy)     NSString *sourceId;                     //  来源 云模板id
@property (nonatomic, copy)     NSString *labelId;                     //  来源 云标签id
@property (nonatomic, copy)     NSDictionary *barcodeCategoryMap;       //  条码集合
@property (nonatomic,copy)      NSString * materialModelSn;
@end

