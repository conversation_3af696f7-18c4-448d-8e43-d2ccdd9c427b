import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:niim_login/login_plugin/utils/graphql_utils.dart';
import 'package:text/application.dart';
import 'package:text/macro/constant.dart';
import 'package:text/network/dio_utils.dart';
import 'package:text/network/http_api.dart';
import 'package:text/pages/create/model/printer_connect_state.dart';
import 'package:text/pages/industry_template/home/<USER>/hard_ware_serise_model.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_category_list_model.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_model.dart';
import 'package:text/pages/industry_template/home/<USER>/label_usage_record.dart';
import 'package:text/pages/industry_template/home/<USER>/local_industry_template_model.dart';
import 'package:text/pages/industry_template/home/<USER>/template_size_model.dart';
import 'package:text/pages/industry_template/select_label/hardware_list_model.dart';
import 'package:text/pages/industry_template/select_label/label_category_list_model.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/tools/gray_config_manager.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/hardware_manager.dart';
import 'package:text/utils/log_utils.dart';
import 'package:text/utils/toast_util.dart';

import '../state/industry_template_state.dart';
import 'package:niimbot_flutter_canvas/src/utils/ocr_util.dart';
import 'package:niimbot_flutter_canvas/src/model/template_data.dart' as CanvasTemplateData;
import '../model/advertisement_model.dart';
import '../service/advertisement_db_utils.dart';
import '../service/advertisement_manager.dart';

class IndustryTemplateLogic extends GetxController {
  final IndustryTemplateState state = IndustryTemplateState();

  IndustryTemplateLogic() {
    _initEventBus();
  }

  void _initEventBus() {
    // 监听广告刷新事件
    NiimbotEventBus.getDefault().register(Object(), (data) {
      if (data == Application.EVENT_REFRESH_ADVERTISEMENTS) {
        _loadAdvertisementData();
      }
    });
  }

  /// 加载广告数据
  Future<void> _loadAdvertisementData() async {
    try {
      state.isAdvertisementLoading.value = true;

      // 从数据库加载广告数据
      List<AdvertisementModel> advertisements = await AdvertisementDbUtils.getValidAdvertisements();

      if (advertisements.isNotEmpty) {
        await getAdvertisementData();
        state.lastAdvertisementUpdateTime = DateTime.now().millisecondsSinceEpoch;
        update(['advertisement']);
      } else {
        state.currentAdvertisement.value = null;
      }
      this.checkAdvertisements();
      this.advertisementShowPoint();
    } catch (e) {
      Log.d('加载广告数据失败: $e');
      state.currentAdvertisement.value = null;
      this.checkAdvertisements();
    } finally {
      state.isAdvertisementLoading.value = false;
    }
  }

  // 检查是否显示广告
  Future<void> checkAdvertisements() async {
    AdvertisementModel? ad = state.currentAdvertisement.value;
    var needShow = false;
    if (ad != null) {
      needShow = await AdvertisementManager().shouldShowAd(ad);
    }
    state.showAdvert.value = needShow;
  }

  /// 刷新广告数据
  Future<void> refreshAdvertisements() async {
    // 先刷新数据
    // await AdvertisementManager().refreshAdvertisements();
    // 然后加载并更新UI
    await _loadAdvertisementData();
  }

  // 广告显示打点
  void advertisementShowPoint() {
    AdvertisementModel? advertisement = this.state.currentAdvertisement.value;
    if (advertisement != null && advertisement.content != null) {
      ToNativeMethodChannel().sendTrackingToNative({
        "track": "show",
        "posCode": "011_448",
        "ext": {
          'a_name': advertisement.activityName ?? "",
          'b_name': advertisement.title ?? "",
          'banner_id': advertisement.id ?? "",
        }
      });
    }
  }

  @override
  void onInit() {
    // 监听机型尺寸切换，更新title显示
    _listenObs();
    // 获取当前打印机选择列表且更新当前打印机模型, 同时根据当前的打印机模型获取对应的标签纸
    //getPrinterAndLabelInfo();
    // 监听打印机连接断开、标签纸信息
    _hardWareWithLabelListener();
    _industrySceneInit();
    _setFirstSortTemplates();
    getTemplateSize();
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    // 离开页面时，更新当前广告缓存
    final ad = state.currentAdvertisement.value;
    if (ad != null) {
      AdvertisementManager().markAdAsShown(ad);
    }
    NiimbotEventBus.getDefault().unregister(this);
    super.onClose();
  }
}

/// 监听obs属性变化
extension ListenObs on IndustryTemplateLogic {
  _listenObs() {
    everAll([state.hardWareSeriesModel, state.size], (callback) {
      String size;
      if (state.size() == Size.zero) {
        size = intlanguage('app100000604', '全部尺寸');
      } else {
        double widthFraction = state.size().width - state.size().width.truncate();
        double heightFraction = state.size().height - state.size().height.truncate();
        size = widthFraction == 0 ? state.size().width.truncate().toString() : state.size().width.toString();
        size += 'x';
        size += heightFraction == 0 ? state.size().height.truncate().toString() : state.size().height.toString();
      }
      // state.hardWareWithSizeName.value = (state.hardWareSeriesModel()?.name ?? '') + '/' + size;
      // 2023/8/30 Ice_Liu 应产品要求删除机型系列名称
      state.hardWareWithSizeName.value = size;
      if (state.hardWareWithSizeName.value.length > 12) {
        state.hardWareWithSizeName.value = state.hardWareWithSizeName.value.substring(0, 12) + '...';
      }
    });

    // 监听尺寸选中状态
    ever(state.sizeSelectedState, (callback) {
      // 非标签纸尺寸且当前选中使用标签纸为背景
      if (state.sizeSelectedState.value != "labelSize" && state.isUsedLabelBackGround == true) {
        // 取消使用当前标签纸为背景
        state.isUsedLabelBackGround = false;
        update(['labelSize']);
      }
    });
  }
}

/// 标签纸&设备管理
extension HardwareAndLabelManager on IndustryTemplateLogic {
  /// 获取打印机机型列表以及当前打印机模型
  getPrinterAndLabelInfo() {
    // 先获取当前打印机模型
    HardWareManager.instance().getPrinterSeriesInfo().then((List<HardWareSeriesModel>? hardwareSeriseList) {
      state.hardwareSeriesList = hardwareSeriseList;
      // state.hardWareSeriesModel.value = HardWareManager.instance().seriesModel;
      // 当前机型是正在连接的机型
      if (HardWareManager.instance().hardwareModelState == HardwareModelState.using) {
        state.hardwareSeriesModelUsing.value = HardWareManager.instance().seriesModel;
        state.hardWareSeriesModel.value = HardWareManager.instance().seriesModel;
      } else {
        String? hardwareModel = Application.sp.getString(ConstantKey.latestHardwareName);
        if ((hardwareModel ?? '').isEmpty) {
          hardwareModel = "B21";
        }
        hardwareSeriseList!.forEach((element) {
          element.hardwareNameStr?.split(",").forEach((t) {
            if (hardwareModel == t) {
              state.hardWareSeriesModel.value = element;
            }
          });
        });
      }
      // 再获取当前的标签纸信息
      _getLabelData();
    });
  }

  /// 获取标签纸信息
  _getLabelData() {
    ToNativeMethodChannel().getPrinterLabelData().then((value) {
      if (value is Map) {
        if (value.isNotEmpty) {
          // 当前已连接机器且存在标签纸
          final _dataMap = Map<String, dynamic>.from(value);
          state.labelConnectingModel = LabelUsageRecord.fromJson(_dataMap);
          state.labelUseModel = state.labelConnectingModel;
          // 默认选中此标签纸尺寸,更新尺寸信息

          if (state.sizeSelectedState.value == "labelSize" || state.sizeSelectedState.value == "all") {
            state.isLabelSizeSelected = true;
            state.size.value = Size(state.labelModel?.width.toDouble() ?? 0, state.labelModel?.height.toDouble() ?? 0);
            state.sizeSelectedState.value = "labelSize";
          }
          // 当前过渡状态更新为当前已连接机器且存在标签纸
          state.transitionState.value = IndustryToCanvasTransitionState.connectAndRecognizedLabel;
          // 选中当前的标签纸作为画板标签纸
          state.labelCanvasModel = state.labelConnectingModel;
          // 检查标签纸是否符合当前的机型
          checkLabelMatchHardWare(
              labelSupportDeviceSet: {...?(state.labelCanvasModel?.profile.machineId.split(',').toSet())},
              isNeedShowAlert: true);
          // 刷新行业场景
          getTemplateSortList();
          if (state.lastHeight != state.interfaceSize.height || state.lastWidth != state.interfaceSize.width) {
            getIndustryList();
          }

          // 获取打印机连接状态
          ToNativeMethodChannel().getPrinterConnectState().then((value) {
            try {
              state.printerState = PrinterConnectState.fromJson(Map<String, dynamic>.from(value ?? {}));
            } catch (error) {
              Log.d(error.toString());
            }
          });

          // 记录埋点
          ToNativeMethodChannel().sendTrackingToNative({
            "track": "view",
            "posCode": "011",
            "ext": {
              'type': '2',
              'state': getPageStateForTrack(),
              'type1': state.hardWareWithSizeName.value,
              'type2': state.sortName.value,
              'source': 1
            }
          });
        } else {
          // 根据系列下的首个机型筛选符合机型的标签纸历史记录
          ToNativeMethodChannel.sharedInstance()
              .getUseRecentLabel(machineIds: state.hardWareSeriesModel()?.hardwareIdList)
              .then((usedList) {
            try {
              state.labelUseModel = usedList?.first;

              if (state.sizeSelectedState.value == "labelSize") {
                state.isLabelSizeSelected = true;
                state.size.value =
                    Size(state.labelModel?.width.toDouble() ?? 0, state.labelModel?.height.toDouble() ?? 0);
                state.sizeSelectedState.value = "labelSize";
              }
            } catch (error) {
              Log.d(error.toString());
            }

            // 刷新行业场景
            getTemplateSortList();

            //判断当选择常用尺寸后从画板返回后是否需要刷新状态为全选
            var isSelectAll = false;
            for (var element in state.templateSizes) {
              if (state.seriesFirstDevice?.seriesId == element.id?.toString()) {
                for (var element in element.sizes!) {
                  if (element.width!.toInt() == state.interfaceSize.width.toInt() &&
                      element.height!.toInt() == state.interfaceSize.height.toInt()) {
                    isSelectAll = true;
                  }
                }
              }
            }
            if (!isSelectAll &&
                (state.sizeSelectedState.value != "all" &&
                    state.sizeSelectedState.value != "labelSize" &&
                    state.sizeSelectedState.value != "customSize")) {
              state.sizeSelectedState.value = "all";
              state.size.value = Size.zero;
            }

            if (state.lastHeight != state.interfaceSize.height || state.lastWidth != state.interfaceSize.width) {
              getIndustryList();
            }
          });

          // 默认选中全部
          //  state.sizeSelectedState.value = "all";

          // 获取打印机连接状态 不存在历史记录,
          ToNativeMethodChannel().getPrinterConnectState().then((value) {
            try {
              state.printerState = PrinterConnectState.fromJson(Map<String, dynamic>.from(value ?? {}));
              if ((state.printerState?.connected ?? 0) == 1) {
                // 已连接设备
                if ((state.printerState?.rfidStatus ?? 0) == 1) {
                  // 打印机纸张未识别
                  state.transitionState.value = IndustryToCanvasTransitionState.connectUnrecognizedLabel;
                } else {
                  // 当前打印机不具有自动识别功能
                  state.transitionState.value = IndustryToCanvasTransitionState.connectUnrecognizedMachine;
                }
              } else {
                // 未连接设备
                state.transitionState.value = IndustryToCanvasTransitionState.noConnect;
              }
              // 记录埋点
              ToNativeMethodChannel().sendTrackingToNative({
                "track": "view",
                "posCode": "011",
                "ext": {
                  'type': '2',
                  'state': getPageStateForTrack(),
                  'type1': state.hardWareWithSizeName.value,
                  'type2': state.sortName.value,
                  'source': 1
                }
              });
            } catch (error) {
              Log.d(error.toString());
            }
          });
        }
      }
    });
  }

  /// 打印机、标签纸、网络状态监听
  _hardWareWithLabelListener() {
    NiimbotEventBus.getDefault().register(this, (data) {
      Log.d("===============行业模板页面EventBus： $data");
      if (data is Map && data.containsKey('labelData') && data['labelData'] is Map) {
        // 标签纸状态更新
        if ((data['labelData'] as Map).isNotEmpty) {
          final _dataMap = Map<String, dynamic>.from(data['labelData']);
          var labelModel = LabelUsageRecord.fromJson(_dataMap);
          // 更新当前连接的标签纸
          state.labelConnectingModel = labelModel;
          // 更新当前的标签纸作为画板标签纸
          state.labelCanvasModel = labelModel;
          // 如果是正在展示中间过渡页，直接跳入画板
          if (state.showTransitionPageState == 1) {
            if (state.labelModel?.id != labelModel.id) {
              // 筛选时的纸和识别到的纸不匹配
              state.transitionState.value = IndustryToCanvasTransitionState.transitionToLabelUsing;
            } else {
              // 连接打印机且已识别
              state.transitionState.value = IndustryToCanvasTransitionState.connectAndRecognizedLabel;
            }
          } else {
            // 连接打印机且已识别
            state.transitionState.value = IndustryToCanvasTransitionState.connectAndRecognizedLabel;
          }
          // 当前机型和选择的机型匹配
          // 更新标签纸模型
          state.labelUseModel = labelModel;
          // if (state.sizeSelectedState.value == "labelSize" || state.sizeSelectedState.value == "all") {
          //   state.isLabelSizeSelected = true;
          // }
          state.isLabelSizeSelected = true;
          state.sizeSelectedState.value = "labelSize";
          if (state.isLabelSizeSelected) {
            // 当前标签纸尺寸被选择，则更新尺寸
            state.size.value = Size(state.labelModel?.width.toDouble() ?? 0, state.labelModel?.height.toDouble() ?? 0);
          }
          // 更新标签纸匹配状态
          checkLabelMatchHardWare(
              labelSupportDeviceSet: {...?(state.labelUseModel?.profile.machineId.split(',').toSet())},
              isNeedShowAlert: true);
          // 刷新标签纸尺寸页
          update(['labelSize']);
          if (state.lastHeight != state.interfaceSize.height || state.lastWidth != state.interfaceSize.width) {
            getIndustryList();
          }
        } else {
          // 未识别到纸，取出纸
          state.labelConnectingModel = null;
          // 当前系列和选择的系列匹配
          // 更新标签纸模型, 从历史记录筛选符合系列的标签纸
          // 筛选符合系列的历史记录标签纸
          ToNativeMethodChannel.sharedInstance()
              .getUseRecentLabel(machineIds: state.hardWareSeriesModel()?.hardwareIdList)
              .then((labelList) {
            try {
              if (labelList?.isNotEmpty ?? false) {
                // 存在符合机型的标签纸
                state.labelUseModel = labelList?.first;
                if (state.sizeSelectedState.value == "labelSize" || state.sizeSelectedState.value == "all") {
                  state.isLabelSizeSelected = true;
                }
                if (state.isLabelSizeSelected) {
                  state.size.value =
                      Size(state.labelUseModel?.width.toDouble() ?? 0, state.labelUseModel?.height.toDouble() ?? 0);
                }
              } else {
                // 不存在符合机型的标签纸，默认选中全部, 尺寸、标签纸置空
                state.isLabelSizeSelected = false;
                state.isUsedLabelBackGround = false;
                state.labelUseModel = null;
                state.sizeSelectedState.value = "all";
                state.size.value = Size.zero;
              }
              // 刷新标签纸尺寸页
              update(['labelSize']);
              if (state.lastHeight != state.interfaceSize.height || state.lastWidth != state.interfaceSize.width) {
                getIndustryList();
              }
            } catch (error) {}
          });
          // 画板标签纸置空
          state.labelCanvasModel = null;
          // 获取打印机连接状态 不存在历史记录,
          ToNativeMethodChannel().getPrinterConnectState().then((value) {
            try {
              state.printerState = PrinterConnectState.fromJson(Map<String, dynamic>.from(value ?? {}));
              if ((state.printerState?.connected ?? 0) == 1) {
                /// 已连接设备
                if ((state.printerState?.rfidStatus ?? 0) == 1) {
                  // 打印机纸张未识别
                  state.transitionState.value = IndustryToCanvasTransitionState.connectUnrecognizedLabel;
                } else {
                  // 当前打印机不具有自动识别功能
                  state.transitionState.value = IndustryToCanvasTransitionState.connectUnrecognizedMachine;
                }
              } else {
                /// 未连接设备
                state.transitionState.value = IndustryToCanvasTransitionState.noConnect;
              }
            } catch (error) {
              Log.d(error.toString());
            }
          });
        }
      } else if (data is Map && data.containsKey('printerConnectState')) {
        Map printerConnectInfo = data['printerConnectState'];
        var printerState = PrinterConnectState.fromJson(Map<String, dynamic>.from(data['printerConnectState']));
        state.printerState = printerState;
        // 确认当前处于连接状态
        if ((printerState.connected ?? 0) == 1) {
          // 用机器 id 查找
          String? machineId = printerState.machineId;
          HardwareModel? model = HardWareManager.instance().findHardwareModel(machineId: machineId ?? '');
          // 通过机型构建系列模型
          state.hardwareSeriesModelUsing.value = HardWareSeriesModel(
              id: printerConnectInfo["seriesId"],
              name: printerState.seriesName ?? model?.seriesName,
              hardwareIdStr: model?.id,
              hardwareNameStr: model?.name);
          state.hardWareSeriesModel.value = HardWareSeriesModel(
              id: printerConnectInfo["seriesId"],
              name: printerState.seriesName ?? model?.seriesName,
              hardwareIdStr: model?.id,
              hardwareNameStr: model?.name);
          if ((printerState.rfidStatus ?? 0) == 1) {
            // 打印机纸张未识别
            state.transitionState.value = IndustryToCanvasTransitionState.connectUnrecognizedLabel;
          } else {
            // 当前打印机不具有自动识别功能
            state.transitionState.value = IndustryToCanvasTransitionState.connectUnrecognizedMachine;
          }
          // if (PrinterInfo.instance().hardwareModelState == HardwareModelState.using) {
          //   state.hardwareSeriesModelUsing.value = PrinterInfo.instance().seriseModel;
          //   state.hardWareSeriesModel.value = PrinterInfo.instance().seriseModel;
          // }

          //   state.hardwareSeriesModelUsing.value = HardWareManager.instance().seriesModel;

          // 刷新列表
          if (state.lastHeight != state.interfaceSize.height || state.lastWidth != state.interfaceSize.width) {
            getIndustryList();
          }
        } else {
          state.labelConnectingModel = null;
          state.hasSelectLabelByUser = false;
          // state.labelUseModel = null;
          // 当前系列和选择的系列匹配
          if (state.hardWareSeriesModel()?.id == state.hardwareSeriesModelUsing()?.id) {
            // 更新标签纸模型, 从历史记录筛选符合系列的标签纸
            // 筛选符合系列的历史记录标签纸
            ToNativeMethodChannel.sharedInstance()
                .getUseRecentLabel(machineIds: state.hardWareSeriesModel()?.hardwareIdList)
                .then((labelList) {
              try {
                if (labelList?.isNotEmpty ?? false) {
                  // 存在符合机型的标签纸
                  state.labelUseModel = labelList?.first;
                  state.isLabelMatch.value = true;
                  //  如果打印机断联，模板尺寸保持原来就可以了
                  if (state.isLabelSizeSelected) {
                    state.size.value =
                        Size(state.labelUseModel?.width.toDouble() ?? 0, state.labelUseModel?.height.toDouble() ?? 0);
                  }
                } else {
                  // 不存在符合机型的标签纸，默认选中全部, 尺寸、标签纸置空
                  state.isLabelSizeSelected = false;
                  state.isUsedLabelBackGround = false;
                  state.labelUseModel = null;
                  state.sizeSelectedState.value = "all";
                  state.size.value = Size.zero;
                }
                // 刷新标签纸尺寸页
                update(['labelSize']);
                if (state.lastHeight != state.interfaceSize.height || state.lastWidth != state.interfaceSize.width) {
                  getIndustryList();
                }
              } catch (error) {}
            });
          }
          // 未连接设备
          state.printerState = null;
          state.hardwareSeriesModelUsing.value = null;
          state.transitionState.value = IndustryToCanvasTransitionState.noConnect;
          // 刷新标签纸尺寸页
          update(['labelSize']);
        }
      } else if (data is Map && data.containsKey('networkChanged')) {
        if (state.connectState.value != data['networkChanged']) {
          state.connectState.value = data['networkChanged'];
          if (state.connectState.value == true) {
            getTemplateSortList();
            getIndustryList();
          }
        }
      }
    });
  }
}

/// 状态刷新
extension StateChange on IndustryTemplateLogic {
  /// 顶部弹窗点击事件监听
  topDialogClickListener(IndustrySelectedState value) {
    if (state.sceneSelect.value == value) {
      state.panelController.isPanelOpen ? state.panelController.close() : state.panelController.open();
    } else {
      state.panelController.open();
    }
    state.sceneSelect.value = value;
    update();
  }

  /// 模型尺寸确认
  confirmAction() {
    // 是否是自定义尺寸,且宽高均有效
    if ((state.sizeSelectedState.value == "customSize") && (state.customWidth != 0 && state.customHeight != 0)) {
      state.size.value = Size(state.customWidth, state.customHeight);
      state.isLabelSizeSelected = false;
    } else if (state.sizeSelectedState.value == "customSize") {
      // 自定义尺寸，自定义尺寸失效，不符合范围
      if (state.isLabelSizeSelected) {
        // 标签纸选中则使用标签纸尺寸
        state.sizeSelectedState.value = "labelSize";
        state.size.value = Size(state.labelModel?.width.toDouble() ?? 0, state.labelModel?.height.toDouble() ?? 0);
      } else {
        // 默认选中全部
        state.sizeSelectedState.value = "all";
        state.size.value = Size.zero;
      }
      // 状态置空
      state.customWidth = 0;
      state.customHeight = 0;
    }
    // 收起面板
    packUpPanel();
    // 刷新数据
    getIndustryList();
  }

  /// 收起面板
  packUpPanel() {
    state.panelController.close();
  }

  /// 检查标签纸状态,是否连接机器或者已识别到标签纸
  goToCanvasPage(TemplateData model, {bool backFromSearch = false}) {
    state.backFromSearchPage = backFromSearch;

    ///更新加载圈状态
    state.homeState = IndustryHomeState.showContainer;
    update();
    // 更新模版数据
    state.templateData = model;
    //未识别到纸的情况下，用当前筛选的标签纸作为画板背景；识别到纸的情况下，则用识别到的纸作为画布背景
    if (state.labelConnectingModel == null) {
      if (state.labelUseModel != null && state.isLabelSizeSelected) {
        // 如果是选中当前标签纸为尺寸进行筛选，则使用当前作为尺寸筛选的labelModel当作画板标签纸
        state.labelCanvasModel = state.labelUseModel;
      } else {
        state.labelCanvasModel = null;
      }
    }
    gotoCanvasPage();
    return;
  }
}

/// 场景刷新
extension SceneFresh on IndustryTemplateLogic {
  /// 获取行业分类数据
  getTemplateSortList() {
    Map<String, dynamic> params = {
      'height': state.interfaceSize.height,
      'width': state.interfaceSize.width,
    };
    DioUtils.instance.requestNetwork<IndustryTemplateModel>(Method.post, IndustryTemplateApi.industryTemplateCategories,
        params: params, isList: true, onSuccessList: (list) {
      _setFirstSortTemplates();
      state.industrytemplates.addAll(list);

      if (state.localIndustrytemplate.value.id != -1) {
        for (var i = 0; i < state.industrytemplates.length; i++) {
          if (state.industrytemplates[i].id == state.localIndustrytemplate.value.id) {
            state.industrySelect.value = i;
          }
        }
      } else {
        state.industrySelect.value = 0;
      }
      update();
    });
  }

  /// 获取场景列表
  getSortTemplates() {
    return state.industrytemplates[state.industrySelect.value].childrens;
  }

  /// 设置全部行业场景
  _setFirstSortTemplates() {
    state.industrytemplates.clear();
    state.industrytemplates.add(state.nativeIndustryTemplate);
  }

  /// 行业场景初始化
  _industrySceneInit() {
    String record = Application.sp.getString(
          ConstantKey.local_industry_template,
        ) ??
        '';
    if (record.isNotEmpty) {
      state.localIndustrytemplate.value = LocalIndustryTemplateModel.fromJson(json.decode(record));
    } else {
      state.localIndustrytemplate.value.toLocal(state.nativeIndustryTemplate, 0);
    }
    if (state.localIndustrytemplate.value.id == -1) {
      state.sortName.value = intlanguage('app100001591', '行业场景');
    } else {
      if (state.localIndustrytemplate.value.childrens!.id == -1) {
        state.sortName.value = state.localIndustrytemplate.value.name!;
      } else {
        state.sortName.value = state.localIndustrytemplate.value.childrens!.name!;
      }
    }
  }

  /// 获取当前选择场景id
  num getSelectSceneId() {
    num sceneId = -1;
    if (state.localIndustrytemplate.value.id != -1) {
      if (state.localIndustrytemplate.value.childrens!.id == -1) {
        state.sortName.value = state.localIndustrytemplate.value.name!;
        sceneId = state.localIndustrytemplate.value.id ?? -1;
      } else {
        sceneId = state.localIndustrytemplate.value.childrens?.id ?? -1;
      }
    }
    return sceneId;
  }

  /// 获取当前纸张状态（埋点使用）
  int getPaperStateForTrack() {
    int paperState = 2;
    if (state.hardwareSeriesModelUsing() != null && state.labelConnectingModel != null) {
      paperState = 1;
    } else if (state.hasSelectLabelByUser) {
      paperState = 3;
    }
    return paperState;
  }

  /// 获取当前页面状态（埋点使用）
  int getPageStateForTrack() {
    int pageState = 3;
    if (state.hardwareSeriesModelUsing() != null && state.labelConnectingModel != null) {
      pageState = 1;
    } else if (state.localIndustrytemplate.value.id == -1) {
      pageState = 2;
    }
    return pageState;
  }

  initIndustrySelect() {
    if (state.localIndustrytemplate.value.id != -1) {
      for (var i = 0; i < state.industrytemplates.length; i++) {
        if (state.industrytemplates[i].id == state.localIndustrytemplate.value.id) {
          state.industrySelect.value = i;
        }
      }
    } else {
      state.industrySelect.value = 0;
    }
  }

  /// 行业场景模块选择定位
  sortSelect(int index) {
    var id = getSortTemplates()[index].id;
    return state.industrytemplates[state.industrySelect.value].id == state.localIndustrytemplate.value.id &&
        id == state.localIndustrytemplate.value.childrens!.id;
  }

  /// 行业场景改变事件
  changeIndustry(int index) {
    var industrySelect = state.industrytemplates[state.industrySelect.value];
    if (industrySelect.id == -1) {
      state.sortName.value = intlanguage('app100001591', '行业场景');
    } else {
      if (getSortTemplates()[index].id == -1) {
        state.sortName.value = industrySelect.name!;
      } else {
        state.sortName.value = getSortTemplates()[index].name;
      }
    }
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "011_191_194",
      "ext": {'type2': state.sortName.value, 'source': 1}
    });
    //模板刷新优化最新需求改动，只要发生点击就应该刷新模板
    // if (state.localIndustrytemplate.value.isSortRepeat(industrySelect, index)) {
    //   state.panelController.close();
    //   return;
    // }
    // ;

    /// 转化为本地数据存储
    state.localIndustrytemplate.value.toLocal(industrySelect, index);
    var local = state.localIndustrytemplate.value.toJson();
    Application.sp.setString(ConstantKey.local_industry_template, json.encode(local));
    state.panelController.close();
    getIndustryList();
  }

  ///上报场景数据为空
  reportSceneInfo(Function() success) {
    var industry = state.localIndustrytemplate.value;
    var categoryId = '';
    var isParentScene = false;
    if (industry.id != -1) {
      if (industry.childrens!.id == -1) {
        isParentScene = true;
        categoryId = industry.id.toString();
      } else {
        isParentScene = false;
        categoryId = industry.childrens!.id.toString();
      }
    }
    String gql = '''
        mutation reportSceneInfo(\$input: ReportSceneInfo!){
          reportSceneInfo(input:\$input)
        }
        ''';
    Map arg = {
      'machineId': state.hardWareSeriesModel.value?.id.toString(),
      'machineName': state.hardWareSeriesModel.value?.name,
      'labelId': state.isLabelSizeSelected ? state.labelUseModel?.id.toString() : '',
      'labelName': state.isLabelSizeSelected ? state.labelUseModel?.name : '',
      'width': state.interfaceSize.width.toString(),
      'height': state.interfaceSize.height.toString(),
      'isParentScene': isParentScene,
      'sceneId': categoryId.toString(),
      'sceneName': state.sortName.value
    };
    GraphQLUtils.sharedInstance()
        .mutate(gql, variables: {"input": arg}, authorization: true, headers: {"niimbot-user-agent": Application.agent})
        .then((QueryResultWrapper wrapper) {
      var result = wrapper.queryResult;
      if (!result.hasException && result.data!["reportSceneInfo"] == true) {
        success.call();
      } else {
        var error = intlanguage('app01139', '网络异常');
        showToast(msg: error);
      }
    });
  }

  onRefresh() {
    Log.d('下拉刷新');
    state.page = 1;
    state.refreshController.resetNoData();
    getIndustryList(result: (isSuccess, isError) {
      isSuccess ? state.refreshController.refreshCompleted() : state.refreshController.refreshFailed();
    });
  }

  onLoading() {
    var errorPage = 0;
    state.page++;
    errorPage = state.page;
    getIndustryList(
        isClear: false,
        result: (isSuccess, isError) {
          if (!isError) {
            isSuccess ? state.refreshController.loadComplete() : state.refreshController.loadNoData();
          } else {
            state.page = errorPage - 1;
            state.refreshController.loadFailed();
          }
        });
  }

  ///获取模板分类数据
  getIndustryList({bool isClear = true, Function(bool, bool)? result}) {
    var industry = state.localIndustrytemplate.value;
    List<num> categoryIds = [];

    if (industry.id != -1) {
      final child = industry.childrens;
      if (child == null) {
        // childrens 为 null，什么都不加
      } else if (child.id == -1) {
        categoryIds = industry.childrenIds ?? [];
      } else {
        if (child.id != null) {
          categoryIds.add(child.id!);
        }
      }
    }

    state.lastWidth = state.interfaceSize.width;
    state.lastHeight = state.interfaceSize.height;

    if (result == null) {
      state.page = 1;
    }

    Map<String, dynamic> params = {
      'page': state.page,
      'height': state.interfaceSize.height,
      'width': state.interfaceSize.width,
      'isCustomSize': state.isCustomSize,
      'labelId': state.isLabelSizeSelected ? state.labelUseModel?.id ?? '' : '',
      'categoryIds': categoryIds,
      'limit': 20
    };

    state.notifyState = false;
    state.homeState = IndustryHomeState.loading;

    if (result == null) {
      state.refreshController.resetNoData();
      update();
    }
    refreshAdvertisements();
    DioUtils.instance.requestNetwork<IndustryTemplateCategoryListModel>(
      Method.post,
      IndustryTemplateApi.industryTemplateListUnderCategory,
      params: params,
      isList: false,
      onSuccess: (value) {
        if (isClear) {
          state.categoryListModel.clear();
        }

        if (value?.list.isNotEmpty ?? false) {
          for (var element in value!.list) {
            if (state.isUsedLabelBackGround) {
              element.backgroundImage = state.labelUseModel?.backgroundImage ?? "";
            }
            state.categoryListModel.add(element);
          }
          state.homeState = IndustryHomeState.showContainer;
        } else {
          if (result == null) {
            state.homeState = IndustryHomeState.empty;
          } else {
            state.homeState = IndustryHomeState.showContainer;
            result(false, false);
            return;
          }
        }

        result?.call(true, false);
        update();
      },
      onError: (int code, String msg) {
        state.homeState = IndustryHomeState.error;
        if (result != null) {
          state.homeState = IndustryHomeState.showContainer;
          result(false, true);
        }
        update();
      },
    );
  }

  /// 查找标签纸
  void searchByLabelBarcode(
      {String keywords = '',
      int page = 1,
      int limit = 10,
      bool isMoreData = false,
      Function(LabelCategoryListModel?)? resultClosure}) async {
    Map<String, dynamic> parameters = {
      "cloudTemplateTypes": "LABEL_TEMPLATE",
      "from": "1",
      "keywords": keywords,
      "includeVip": '1',
      "limit": limit,
      "page": page,
    };

    DioUtils.instance.requestNetwork<LabelCategoryListModel>(Method.post, LabelTemplateApi.labelTemplatePage,
        params: parameters, isList: false, onSuccess: (model) {
      if (model!.list is List) {
        if (model.list!.isEmpty) {
          // showToast(msg: intlanguage('app00321', '请使用正品耗材'));
          state.homeState = IndustryHomeState.empty;
          update();
        } else {
          resultClosure?.call(model);
          _checkMatchPager(model, state.hardWareSeriesModel()?.id);
        }
      }
    }, onError: (code, message) {
      showToast(msg: intlanguage('app01160', '数据请求失败'));
    });
  }

  /// 标签纸是否符合当前系列
  _checkMatchPager(LabelCategoryListModel model, String? deviceId) {
    final Set<String> labelSupportDeviceSet = {...?(model.list?.first.machineIdList)};
    final Set<String> deviceSet = {...?(state.hardWareSeriesModel()?.hardwareIdList)};
    // 标签纸支持的设备类型与系列下的设备类型存在交集，代表当前标签纸可用于当前系列
    if (labelSupportDeviceSet.intersection(deviceSet).isNotEmpty) {
      state.isLabelMatch.value = true;
      getIndustryList();
    } else {
      state.isLabelMatch.value = false;
    }
  }

  /// 标签纸是否符合当前系列
  bool checkLabelMatchHardWare({required Set<String> labelSupportDeviceSet, isNeedShowAlert = true}) {
    final Set<String> deviceSet = {...?(state.hardWareSeriesModel()?.hardwareIdList)};
    // 标签纸支持的设备类型与系列下的设备类型存在交集，代表当前标签纸可用于当前系列
    if (labelSupportDeviceSet.intersection(deviceSet).isNotEmpty || labelSupportDeviceSet.isEmpty) {
      if (isNeedShowAlert) {
        state.isLabelMatch.value = true;
      }
      return true;
    } else {
      if (isNeedShowAlert) {
        state.isLabelMatch.value = false;
      }
      return false;
    }
  }

  getTemplateSize() {
    DioUtils.instance.requestNetwork<TemplateSizeModel>(Method.get, IndustryTemplateApi.getTemplateSize, isList: true,
        onSuccessList: (list) {
      state.templateSizes = list;
    }, onError: (int code, String msg) {});
  }

  /// 获取广告数据
  Future<void> getAdvertisementData() async {
    try {
      // 获取当前分类信息
      final parentCategoryId =
          state.localIndustrytemplate.value.id == -1 ? null : state.localIndustrytemplate.value.id?.toInt();

      var industry = state.localIndustrytemplate.value;
      List<num> categoryIds = [];

      if (industry.id != -1) {
        final child = industry.childrens;
        if (child == null) {
          categoryIds = [];
          // 全部即为空
        } else if (child.id == -1) {
          categoryIds = [];
        } else {
          if (child.id != null) {
            categoryIds.add(child.id!);
          }
        }
      }
      // 从数据库获取匹配的广告
      final ads = await AdvertisementDbUtils.getAdvertisementsByIndustryTemplate(
        selectedCategoryIds: categoryIds,
        parentCategoryId: parentCategoryId,
      );
      final advertisement = ads.first;
      state.currentAdvertisement.value = advertisement;
    } catch (e) {
      state.currentAdvertisement.value = null;
      state.previousAdvertisementId = null;
      Log.d('获取广告数据失败: $e');
    }
  }

  /// 从接口获取广告数据
  Future<void> _fetchAdvertisementFromApi(String cacheKey, int? categoryId) async {
    Map<String, dynamic> params = {};

    // 根据新的接口，只传递分类ID
    if (categoryId != null) {
      params['categoryId'] = categoryId;
    }

    // 使用统一的API管理 - 直接请求广告数组
    DioUtils.instance.requestNetwork<Map<String, dynamic>>(
        Method.get, // 根据用户提供的接口，应该是GET请求
        IndustryTemplateApi.getAdvertisementList,
        params: params,
        isList: false, onSuccess: (data) async {
      List<AdvertisementModel> advertisements = (data?[AdFieldKeys.industryTemplate] as List)
          .map((e) => AdvertisementModel.fromJson(e as Map<String, dynamic>))
          .toList();
      // effectiveEndTime
      if (advertisements.isNotEmpty) {
        // 保存到数据库
        await AdvertisementDbUtils.refreshAdvertisementData(advertisements);

        Log.d('成功保存 ${advertisements.length} 个广告到数据库');
      }
    }, onError: (code, message) {
      Log.d('获取广告数据失败: $code - $message');
    });
  }

  /// 广告点击事件
  void onAdvertisementClick(AdvertisementModel advertisement) async {
    // 点击模板埋点
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "011_449",
      "ext": {
        'a_name': advertisement.activityName,
        'b_name': advertisement.title,
        'banner_id': advertisement.id,
      }
    });

    // 检查网络状态
    if (state.connectState.value == false) {
      showToast(msg: intlanguage('app01139', '网络异常'));
      return;
    }

    // 根据typeCode处理不同跳转类型
    // switch (advertisement.typeCode) {
    //   case 3: // 商城页面
    //     break;
    //   case 2: // 外部链接
    //     break;
    //   default:
    //     break;
    // }

    ToNativeMethodChannel().pushToRoute({
      "typeCode": advertisement.typeCode.toString(),
      "url": advertisement.redirectUrl.toString(),
      "title": advertisement.title
    });
    // 新增：点击广告后，更新缓存
    await AdvertisementManager().markAdAsShown(advertisement);
  }
}

/// 跳转原生
extension GotoNativePage on IndustryTemplateLogic {
  /// 跳转原生画板
  gotoCanvasPage({bool isCustomLabel = false, bool isSelected = false, VoidCallback? completion}) {
    // 非自定义标签纸 && 匹配当前设备系列,作为历史记录最新的一条，替换尺寸筛选项
    if (!isCustomLabel &&
        checkLabelMatchHardWare(
            labelSupportDeviceSet: {...?(state.labelCanvasModel?.profile.machineId.split(',').toSet())},
            isNeedShowAlert: false)) {
      // 选中且当前选中的标签纸 && 进入画板的标签纸不是一个，替换尺寸筛选项，作为最新的一条历史记录
      if (state.isLabelSizeSelected && state.labelUseModel?.id != state.labelCanvasModel?.id) {
        // 标签纸选中，刷新size与状态
        state.size.value =
            Size(state.labelCanvasModel?.width.toDouble() ?? 0, state.labelCanvasModel?.height.toDouble() ?? 0);
        // 选中标签纸尺寸
        state.sizeSelectedState.value = "labelSize";
        // 刷新匹配状态
        state.isLabelMatch.value = true;
        getIndustryList();
      }
      // 更新标签纸模型为画板标签纸
      state.labelUseModel = state.labelCanvasModel;
      // 刷新状态
      update(['labelSize']);
    }

    if (GrayConfigManager().grayModule(module: GrayModule.newDrawBoard) == GrayBranch.branchB &&
        state.templateData!.commodityTemplate) {
      Map<String, dynamic> parameters = {
        'isNewDrawingBoard': 1,
      };
      DioUtils.instance.requestNetwork<TemplateData>(
          Method.get,
          params: parameters,
          {
            'method': 'GET',
            'path': '/industryTemplate/template/' + state.templateData!.id.toString(),
            'needLogin': false,
          },
          isList: false, onSuccess: (model) {
        Map<String, dynamic> args = {
          'templateData': model?.rawData,
          'labelData': state.labelCanvasModel?.rawData,
          'isCustomLabel': isCustomLabel,
        };
        _toCanvasPage(args, isCustomLabel);
      }, onError: (code, message) {
        showToast(msg: message);
      });
    } else {
      var dataSources;
      if (state.templateData?.rawData["dataSources"] != null) {
        dataSources = state.templateData?.rawData["dataSources"] as List;
      } else {
        dataSources = [];
      }
      if (state.templateData!.commodityTemplate && dataSources.isNotEmpty) {
        showToast(msg: intlanguage("app100000343", "您的软件版本过低，请升级"));
        return;
      } else {
        Map<String, dynamic> args = {
          'templateData': state.templateData?.rawData,
          'labelData': state.labelCanvasModel?.rawData,
          'isCustomLabel': isCustomLabel,
        };
        _toCanvasPage(args, isCustomLabel);
      }
    }
  }

  /// 执行画板页面跳转并处理返回逻辑
  /// @param args 包含模板数据和标签数据的参数
  /// @param isCustomLabel 是否为自定义标签
  _toCanvasPage(Map<String, dynamic> args, bool isCustomLabel) {
    try {
      // 如果没有标签数据，直接进入画板页面
      if (args['labelData'] == null) {
        _toCanvasPageR(args, isCustomLabel);
        return;
      }

      // 解析模板数据和标签数据
      final CanvasTemplateData.TemplateData source = CanvasTemplateData.TemplateData.fromJson(args['templateData']);
      final CanvasTemplateData.TemplateData target = CanvasTemplateData.TemplateData.fromJson(args['labelData']);

      // 检查模板尺寸与标签尺寸是否不匹配
      final bool needsResize = source.width != target.width || source.height != target.height;

      if (needsResize) {
        // 尺寸不匹配时，调用OCR工具生成适配新尺寸的模板
        OcrUtils.generateIndustryReplaceTemplate(source, target).then((onValue) {
          onValue.name = source.name;
          args['templateData'] = onValue.toJson();
          _toCanvasPageR(args, isCustomLabel);
        }).catchError((e, s) {
          debugPrint('模板适配失败: $e');
          debugPrint('调用栈: $s');
          // 即使适配失败也尝试打开原模板
          _toCanvasPageR(args, isCustomLabel);
        });
      } else {
        // 尺寸匹配，无需处理，直接进入画板
        _toCanvasPageR(args, isCustomLabel);
      }
    } catch (e, s) {
      debugPrint('解析模板数据异常:\n $e');
      debugPrint('调用栈信息:\n $s');
      // 出现异常仍尝试打开画板
      _toCanvasPageR(args, isCustomLabel);
    }
  }

  _toCanvasPageR(Map<String, dynamic> args, bool isCustomLabel) {
    CustomNavigation.gotoNextPage('CanvasPage', args).then((value) {
      // completion?.call();
      // 查找模版的标签纸，被存储为最近的历史记录
      // 筛选符合机型的历史记录标签纸
      ToNativeMethodChannel.sharedInstance()
          .getUseRecentLabel(machineIds: state.hardWareSeriesModel()?.hardwareIdList)
          .then((labeList) {
        try {
          if (labeList?.isNotEmpty ?? false) {
            // 标签纸作为画板标签纸
            state.labelCanvasModel = labeList?.first;
            // 非自定义标签纸 && 匹配当前设备系列,作为历史记录最新的一条，替换尺寸筛选项
            if (!isCustomLabel) {
              // 选中且当前选中的标签纸 && 进入画板的标签纸不是一个，替换尺寸筛选项，作为最新的一条历史记录
              if (state.isLabelSizeSelected && state.labelUseModel?.id != state.labelCanvasModel?.id) {
                // 标签纸选中，刷新size与状态
                state.size.value =
                    Size(state.labelCanvasModel?.width.toDouble() ?? 0, state.labelCanvasModel?.height.toDouble() ?? 0);
                // 选中标签纸尺寸
                state.sizeSelectedState.value = "labelSize";
                // 刷新匹配状态
                state.isLabelMatch.value = true;
                // 更新标签纸模型为画板标签纸
                state.labelUseModel = state.labelCanvasModel;
                getIndustryList();
              }
              // 更新标签纸模型为画板标签纸
              state.labelUseModel = state.labelCanvasModel;
              // 刷新状态
              update(['labelSize']);
            }
          } else {
            // 不存在历史
            // 画板标签纸置空
            state.labelCanvasModel = null;
          }
        } catch (error) {}
      });
    });
  }
}
