import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:niim_login/login_plugin/utils/image_utils.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:text/application.dart';
import 'package:text/network/entity/user.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_state.dart';
import 'package:text/pages/my_template/model/folder_model.dart';
import 'package:text/pages/my_template/my_template_state.dart';
import 'package:text/pages/my_template/page/personal_template/controller/personal_template_logic.dart';
import 'package:text/pages/my_template/page/personal_template/personal_template_selected_page.dart';
import 'package:text/pages/my_template/widget/folder_move_widget.dart';
import 'package:text/pages/my_template/widget/folders_item_widget.dart';
import 'package:text/pages/my_template/widget/my_template_item_widget.dart';
import 'package:text/pages/my_template/widget/search_widget.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/plane_button.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';

class PersonalTemplatePage extends StatefulWidget {
  const PersonalTemplatePage({Key? key}) : super(key: key);

  @override
  _PersonalTemplatePageState createState() => _PersonalTemplatePageState();
}

class _PersonalTemplatePageState extends State<PersonalTemplatePage>
    with PageVisibilityObserver, TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  final PersonalTemplateLogic personalLogic = Get.find<PersonalTemplateLogic>();
  final personalState = Get.find<PersonalTemplateLogic>().state;
  // 添加变量记录用户滚动位置
  double? _userScrollPosition;

  @override
  void initState() {
    super.initState();
    // 监听滚动事件
    personalState.folderScrollController.addListener(_onScroll);
  }

  void _onScroll() {
    _userScrollPosition = personalState.folderScrollController.position.pixels;
  }

  @override
  void onPageShow() {
    super.onPageShow();
    Future.delayed(Duration(milliseconds: 200), () {
      if (Application.user != null) {
        personalLogic.checkVipToast();
      }
    });
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "show",
      "posCode": "007_447",
    });
  }

  void onPageHide() {
    super.onPageHide();
    if (personalLogic.personalTemplateState.smartIsPop) {
      personalLogic.personalTemplateState.smartIsPop = false;
      Navigator.of(personalLogic.personalTemplateState.batchPrintKey.currentContext!, rootNavigator: true).pop();
    }
    // 页面Hide时，需要处理的弹窗Dismiss
    personalLogic.onPageHideAction(context);
  }

  @override
  void dispose() {
    personalState.folderScrollController.removeListener(_onScroll);
    PageVisibilityBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    PageVisibilityBinding.instance.addObserver(this, ModalRoute.of(context) as Route);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    // 在build时保持滚动位置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (personalState.folderScrollController.hasClients) {
        if (_userScrollPosition != null) {
          // 如果用户有滚动位置，优先使用用户滚动位置
          personalState.folderScrollController.jumpTo(_userScrollPosition!);
        }
      }
    });

    return Container(
      color: ThemeColor.background,
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: SearchWidget(() {
                  personalLogic.toSearchPersonalTemplate();
                }),
              ),
              personalState.operateType == TemplateOperateState.normal ? _batchPrint() : Container(),
            ],
          ),
          Divider(
            height: 1,
            color: ThemeColor.border,
          ),
          Expanded(
            child: Row(
              children: [
                GetBuilder<PersonalTemplateLogic>(
                    id: RefreshManager.folderList,
                    builder: (logic) {
                      return Container(
                        width: 130,
                        child: Column(
                          children: [
                            Expanded(
                              child: Stack(
                                children: [
                                  Container(
                                    color: ThemeColor.listBackground,
                                    child: NotificationListener<ScrollNotification>(
                                      onNotification: (notification) {
                                        return false;
                                      },
                                      child: MediaQuery.removePadding(
                                        removeTop: true,
                                        context: context,
                                        child: ListView.builder(
                                          physics: ClampingScrollPhysics(),
                                          itemCount: personalState.folderList.length,
                                          controller: personalState.folderScrollController,
                                          itemBuilder: (context, index) {
                                            return FoldersItemWidget(
                                                personalLogic,
                                                personalState.folderIndex,
                                                index,
                                                personalState.folderList[index],
                                                personalState.operateType == TemplateOperateState.batchPrint);
                                          },
                                        ),
                                      ),
                                    ),
                                  ),
                                  Container(
                                    color: Colors.transparent,
                                    child: MediaQuery.removePadding(
                                        removeTop: true,
                                        context: context,
                                        child: ListView.builder(
                                            itemCount: personalState.folderCoverList.length,
                                            shrinkWrap: true,
                                            controller: personalState.folderCoverScrollController,
                                            itemBuilder: (context, index) {
                                              return FoldersItemWidget(
                                                  personalLogic,
                                                  personalState.folderIndex,
                                                  index,
                                                  personalState.folderCoverList[index],
                                                  personalState.operateType == TemplateOperateState.batchPrint);
                                            })),
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      );
                    }),
                SizedBox(
                  width: 16,
                ),
                Expanded(
                  child: Container(
                      padding: EdgeInsetsDirectional.only(end: 16),
                      // width: 10,
                      child: GetBuilder<PersonalTemplateLogic>(
                          id: RefreshManager.templateList,
                          builder: (logic) {
                            return _templatePage(context);
                          })),
                ),
              ],
            ),
          ),
          GetBuilder<PersonalTemplateLogic>(
              id: RefreshManager.sizeErrorToast,
              builder: (logic) {
                return personalLogic.personalTemplateState.showToast != "" &&
                        personalState.operateType == TemplateOperateState.batchPrint
                    ? _buildSizeErrorToast(context)
                    : Container();
              }),
          GetBuilder<PersonalTemplateLogic>(
              id: RefreshManager.templateSelect,
              builder: (logic) {
                return personalState.operateType == TemplateOperateState.managerState ||
                        personalState.operateType == TemplateOperateState.batchPrint
                    ? _buildBottomBar(context)
                    : Container();
              }),
        ],
      ),
    );
  }

  _batchPrint() {
    return GestureDetector(
        onTap: () {
          ToNativeMethodChannel().sendTrackingToNative({
            "track": "click",
            "posCode": "007_256",
          });
          personalLogic.batchPrint(context);
        },
        child: Container(
          margin: EdgeInsetsDirectional.fromSTEB(2, 0, 12, 0),
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(25)),
          child: Container(
            child: Stack(
              children: [
                Container(
                  height: 32,
                  margin: EdgeInsets.symmetric(vertical: 3, horizontal: 2),
                  decoration: BoxDecoration(
                    color: ThemeColor.COLOR_FFFFFBF2,
                    border: Border.all(color: ThemeColor.COLOR_0D000000, width: 1),
                    borderRadius: BorderRadius.circular(19),
                  ),
                  child: Container(
                    key: personalLogic.personalTemplateState.batchPrintKey,
                    margin: EdgeInsets.symmetric(vertical: 3, horizontal: 15),
                    alignment: Alignment.center,
                    child: Text(intlanguage('app100001238', '快捷打印'),
                        style: TextStyle(
                          color: ThemeColor.COLOR_FFB2780E,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        )),
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  _batchPrintText() {
    String text = "intlanguage('app100001238', '快捷打印')"; // 获取文本内容
    final int maxLength = 10; // 设置最大长度

// 如果文本长度超过最大长度，进行换行处理
    if (text.length > 10) {
      List<String> textParts = [];

      // 根据最大长度将文本分割成多个部分
      while (text.length > maxLength) {
        textParts.add(text.substring(0, maxLength));
        text = text.substring(maxLength);
      }

      // 将剩余的文本部分添加到最后一个部分中
      textParts.add(text);

      // 在需要换行的位置插入换行符`\n`
      text = textParts.join('\n');
    }

    return Text(
      text,
      style: TextStyle(
        color: ThemeColor.COLOR_FFB2780E,
        fontSize: 14,
        fontWeight: FontWeight.w400,
      ),
      maxLines: null, // 设置为null可以自动根据文本内容换行
      overflow: TextOverflow.visible, // 显示全部文本内容
    );
  }

  _templatePage(BuildContext context) {
    switch (personalState.templatePageState) {
      case IndustryHomeState.empty:
        return Container(
            padding: const EdgeInsets.only(bottom: 155),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  ImageUtils.getImgPath('no_data'),
                  fit: BoxFit.contain,
                ),
                Padding(
                  padding: EdgeInsetsDirectional.only(top: 30, start: 30, end: 30),
                  child: Text(
                    intlanguage('app100001187', '该文件夹暂无模板'),
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
                  ),
                ),
              ],
            ));
      case IndustryHomeState.loading:
        return Container(
            child: Stack(
          children: [
            // MediaQuery.removePadding(
            //   child: ListView.builder(
            //       itemCount: personalState.myTemplateList.length,
            //       itemBuilder: (context, index) {
            //         return MyTemplateItemWidget(
            //   personalState.myTemplateList[index],
            //   () {},
            //   operateType: personalState.operateType,
            // );
            //       }),
            //   context: context,
            //   removeTop: true,
            // ),
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.only(bottom: 80),
              child: const CupertinoActivityIndicator(
                radius: 20,
                animating: true,
              ),
            ),
          ],
        ));
      case IndustryHomeState.showContainer:
        return Container(
            color: ThemeColor.background,
            child: MediaQuery.removePadding(
              context: context,
              removeTop: true,
              child: SmartRefresher(
                controller: personalState.templateRefreshController,
                enablePullUp: true,
                onRefresh: () {
                  if (mounted) {
                    personalLogic.onRefreshTemplate();
                  }
                },
                onLoading: () {
                  if (mounted) {
                    personalLogic.onLoadTemplate();
                  }
                },
                child: ListView.builder(
                    itemCount: personalState.myTemplateList.length,
                    controller: personalState.templateScrollController,
                    itemBuilder: (context, index) {
                      // 添加简单的边界检查
                      if (index >= personalState.myTemplateList.length) {
                        return Container();
                      }
                      return MyTemplateItemWidget(
                        personalLogic,
                        personalState.myTemplateList[index],
                        (TemplateEventType eventType) {
                          if (eventType == TemplateEventType.select) {
                            personalLogic.refereshSelectTemplate(index);
                          }
                        },
                        operateType: personalState.operateType,
                        index: index,
                        morefunction: (value) {
                          personalLogic.onRefreshTemplate(isNeedRequestRefresh: value);
                        },
                      );
                    }),
              ),
            ));
      default:
        return Container();
    }
  }

  Widget _buildBottomBar(BuildContext context) {
    List<Widget> allSelectWidgets = [
      GestureDetector(
        onTap: () {
          personalLogic.selectFolderAllTemplate(context);
        },
        child: Container(
          color: ThemeColor.background,
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.only(left: 16, right: 5),
                child: personalState.selectAllState
                    ? const SvgIcon(
                        'assets/images/goods_lib/icon_good_check.svg',
                        width: 20,
                        height: 20,
                      )
                    : const SvgIcon(
                        'assets/images/goods_lib/icon_good_nocheck.svg',
                        width: 20,
                        height: 20,
                      ),
              ),
              Container(
                constraints: BoxConstraints(
                  maxWidth: 50,
                ),
                child: Text(intlanguage('app00506', '全选'),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: ThemeColor.subtitle,
                    )),
              )
            ],
          ),
        ),
      ),
      const SizedBox(
        width: 13,
      ),
      GestureDetector(
          onTap: () {
            if (personalState.operateType == TemplateOperateState.managerState) return; //暂时屏蔽预览功能
            if (personalState.myTemplateSelectList.length <= 0) {
              return;
            }
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              enableDrag: false,
              shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
              builder: (BuildContext context) {
                return PersonalTemplateSelectedPage();
              },
            );
          },
          child: Container(
            // width: 90,
            child: Row(
              children: [
                Container(
                  constraints: BoxConstraints(
                    maxWidth: 75,
                  ),
                  child: Text(
                      intlanguage('app100001052', '已选\$个',
                          param: [personalState.myTemplateSelectList.length.toString()]),
                      maxLines: 4,
                      style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w600, color: ThemeColor.brand)),
                ),
                (personalState.myTemplateSelectList.length <= 0 ||
                        personalState.operateType != TemplateOperateState.batchPrint)
                    ? Container()
                    : SvgIcon(
                        'assets/images/goods_lib/icon_arrow_up.svg',
                        width: 18,
                        height: 18,
                      ),
              ],
            ),
          )),
      const Spacer(),
    ];
    List<Widget> managerWidgets = [
      PlaneButton(
        width: 70,
        height: 36,
        enabled: personalState.myTemplateSelectList.length > 0,
        backgroundColor: Color(0xAAF5F5F5),
        borderRadius: const BorderRadius.all(Radius.circular(18)),
        child: Container(
          width: 70,
          alignment: Alignment.center,
          padding: EdgeInsets.symmetric(horizontal: 10),
          child: Text(
            intlanguage('app00657', '移动'),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(color: ThemeColor.brand, fontSize: 13, fontWeight: FontWeight.w600),
          ),
        ),
        onTap: () {
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
            builder: (BuildContext context) {
              personalState.selectFolderId = personalState.folderList[personalState.folderIndex].id.toString();
              var folderList = personalState.folderList.sublist(1);
              return GetBuilder<PersonalTemplateLogic>(
                  id: RefreshManager.folderSelect,
                  builder: (logic) {
                    return FolderMoveWidget((FolderModel selectModel, Function(bool result) resultCallBack) {
                      personalLogic.templateBatchManagerEvent(
                        context,
                        TemplateEventStatus.batchMove,
                        personalState.myTemplateSelectList,
                        model: selectModel,
                        resultCallBack: (operateResult) {
                          resultCallBack(operateResult);
                        },
                      );
                    }, (int selectIndex) {
                      personalLogic.refereshSelectFolder(selectIndex + 1);
                    }, folderList, personalState.selectFolderId);
                  });
            },
          );
        },
      ),
      const SizedBox(
        width: 10,
      ),
      // Container(
      //   margin: EdgeInsets.only(right: 20),
      //   child:
      // ),
      PlaneButton(
        width: 70,
        height: 36,
        enabled: personalState.myTemplateSelectList.length > 0,
        backgroundColor: Color(0xAAF5F5F5),
        borderRadius: const BorderRadius.all(Radius.circular(18)),
        child: Container(
          width: 84,
          alignment: Alignment.center,
          padding: EdgeInsets.symmetric(horizontal: 10),
          child: Text(
            intlanguage('app00063', '删除'),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(color: ThemeColor.brand, fontSize: 13, fontWeight: FontWeight.w600),
          ),
        ),
        onTap: () {
          personalLogic.templateBatchManagerEvent(
              context, TemplateEventStatus.batchDelete, personalState.myTemplateSelectList);
        },
      ),
      SizedBox(
        width: 16,
      )
    ];
    List<Widget> batchPrintWidget = [
      Container(
          width: 70,
          height: 36,
          child: Stack(
            children: [
              PlaneButton(
                width: 70,
                height: 36,
                enabled: personalState.myTemplateSelectList.length > 0,
                backgroundColor: ThemeColor.brand,
                borderRadius: const BorderRadius.all(Radius.circular(18)),
                child: Container(
                  width: 84,
                  alignment: Alignment.center,
                  padding: EdgeInsets.symmetric(horizontal: 10),
                  child: Text(
                    intlanguage('app00016', '打印'),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(color: ThemeColor.background, fontSize: 13, fontWeight: FontWeight.w600),
                  ),
                ),
                onTap: () {
                  personalLogic.batchPrintTemplates(context);
                },
              ),
              Positioned(
                top: -2,
                right: 0,
                child: Container(
                  child: Image.asset(
                    "assets/images/my_template/vip_right_top.png",
                    width: 23,
                    height: 15,
                  ),
                ),
              ),
            ],
          )),
      const SizedBox(
        width: 10,
      )
    ];
    List<Widget> operateWidgets = allSelectWidgets;
    personalState.operateType == TemplateOperateState.managerState
        ? operateWidgets.addAll(managerWidgets)
        : operateWidgets.addAll(batchPrintWidget);
    return Align(
      alignment: Alignment.bottomCenter,
      child: GetBuilder<PersonalTemplateLogic>(builder: (logic) {
        return Container(
            padding: const EdgeInsets.only(right: 0),
            margin: EdgeInsets.only(bottom: 34),
            // height: 56,
            width: context.width - 10,
            alignment: Alignment.centerLeft,
            color: Colors.white,
            child: Column(
              children: [
                Container(
                  height: 0.5,
                  color: Color(0xAAEBEBEB),
                ),
                SizedBox(
                  height: 13,
                ),
                Row(children: operateWidgets),
              ],
            ));
      }),
    );
  }

  // 构建尺寸错误提示框
  _buildSizeErrorToast(BuildContext context) {
    return Align(
        alignment: Alignment.bottomCenter,
        child: Container(
            width: context.width,
            alignment: Alignment.centerLeft,
            color: ThemeColor.COLOR_FFF5E5,
            child: Row(
              children: [
                Padding(
                  padding: EdgeInsetsDirectional.only(start: 12),
                  child: SvgIcon(
                    'assets/images/my_template/warning.svg',
                  ),
                ),
                Container(
                  width: context.width - 35,
                  child: Padding(
                    padding: EdgeInsetsDirectional.only(start: 4, top: 10, bottom: 10),
                    child: Text(
                      personalLogic.personalTemplateState.showToast,
                      // overflow: TextOverflow.ellipsis,
                      style: const TextStyle(color: ThemeColor.COLOR_FFAF37, fontSize: 13, fontWeight: FontWeight.w500),
                    ),
                  ),
                ),
              ],
            )));
  }

  // 构建尺寸错误提示框
  _buildTopVipErrorToast(BuildContext context) {
    return Padding(
        padding: EdgeInsetsDirectional.only(top: 8),
        child: GestureDetector(
          onTap: () {
            personalLogic.vipInvalidGuide(context, VIPUseScence.batchPrint, () {});
          },
          child: Container(
              alignment: Alignment.centerLeft,
              color: ThemeColor.COLOR_FFFFF6E8,
              child: Row(
                children: [
                  Padding(
                    padding: EdgeInsetsDirectional.only(start: 12),
                    child: Image.asset(
                      "assets/images/my_template/king.png",
                      width: 20,
                      height: 20,
                    ),
                  ),
                  Container(
                    width: context.width - 60,
                    child: Padding(
                      padding: EdgeInsetsDirectional.only(start: 4, top: 10, bottom: 10),
                      child: Text(
                        personalLogic.personalTemplateState.showTopToast,
                        // overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                            color: ThemeColor.COLOR_FF9D6C33, fontSize: 13, fontWeight: FontWeight.w500),
                      ),
                    ),
                  ),
                  Expanded(child: Container()),
                  Padding(
                    padding: EdgeInsetsDirectional.only(end: 12),
                    child: SvgIcon(
                      'assets/images/my_template/back.svg',
                      matchTextDirection: true,
                    ),
                  ),
                ],
              )),
        ));
  }

  @override
  bool get wantKeepAlive => false;
}
