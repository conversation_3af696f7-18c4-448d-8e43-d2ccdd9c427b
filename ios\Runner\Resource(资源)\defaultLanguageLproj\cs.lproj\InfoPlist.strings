/* 
  InfoPlist.strings
  XYFrameWork

  Created by zhaohu on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "Tato aplikace vyžaduje přístup k vašemu fotoaparátu pro skenování čárových kódů, rozpoznávání textu a pořizování fotografií. Povolíte přístup k fotoaparátu?";
NSBluetoothPeripheralUsageDescription = "Tato aplikace vyžaduje přístup k Bluetooth pro připojení k tiskárně. Povolíte přístup k Bluetooth?";
NSBluetoothAlwaysUsageDescription = "Tato aplikace vyžaduje přístup k Bluetooth pro připojení k tiskárně. Povolíte přístup k Bluetooth?";
NSContactsUsageDescription = "Tato aplikace vyžaduje přístup k vašim kontaktům pro čtení. Povolíte přístup k adresáři?";
NSMicrophoneUsageDescription = "Tato aplikace vyžaduje přístup k vašemu mikrofonu pro rozpoznávání řeči. Povolíte přístup k mikrofonu?";
NSPhotoLibraryUsageDescription = "Toto oprávnění se používá pro tisk obrázků, rozpoznávání čárových kódů, QR kódů, rozpoznávání textu, nastavení vlastních avatarů atd. Prosím, „povolte přístup ke všem fotografiím“, aby bylo možné správně přistupovat k galerii v NIIMBOT. Pokud zvolíte „Vybrat fotky...“, všechny nevybrané a budoucí nové fotky nebudou v NIIMBOT dostupné.";
NSLocationWhenInUseUsageDescription = "Pro snadné připojení k blízkým Wi-Fi sítím aplikace NIIMBOT žádá o povolení polohy.";
NSLocationAlwaysUsageDescription = "Pro snadné připojení k blízkým Wi-Fi sítím aplikace NIIMBOT žádá o povolení polohy.";
NSLocationAlwaysAndWhenInUseUsageDescription = "Pro snadné připojení k blízkým Wi-Fi sítím aplikace NIIMBOT žádá o povolení polohy.";
NSSpeechRecognitionUsageDescription = "Tato aplikace vyžaduje váš souhlas pro přístup k rozpoznávání řeči. Povolíte přístup k rozpoznávání řeči?";
"UILaunchStoryboardName" = "LaunchScreen";
