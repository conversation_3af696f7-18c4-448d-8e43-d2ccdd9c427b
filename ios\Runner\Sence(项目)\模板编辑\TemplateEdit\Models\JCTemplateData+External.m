//
//  JCTemplateData+Images.m
//  Runner
//
//  Created by xingling xu on 2020/6/3.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCTemplateData+External.h"
#import "JCElementModel+Transfer.h"
#import "JCFontManager.h"

@implementation JCTemplateData (External)

- (void)downLoadAllImages:(void(^)(void))block {
    // 下载缩略图
    // 下载背景图
    // 下载元素图片
}

- (void)downLoadAllFonts:(void(^)(void))block {
    NSArray *fonts = [[JCFMDB shareDatabase:DB_DEFAULT]  jc_lookupTable:TABLE_FONTINFO dicOrModel:[JCFontModel class] whereFormat:nil];
    NSArray *existFontCodes = [JCFontModel getDownLoadFontCodeArr];
    NSMutableArray *notExistFontCodes = [NSMutableArray array];
    [self.elements enumerateObjectsUsingBlock:^(JCElementModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        NSArray *modelFonts = [obj getAllFontCodes];
        [modelFonts enumerateObjectsUsingBlock:^(NSString *fontCode, NSUInteger idx, BOOL * _Nonnull stop) {
            if (!STR_IS_NIL(fontCode) && ![existFontCodes containsObject:fontCode]) {
                [notExistFontCodes addObject:fontCode];
            }
        }];
    }];
    NSMutableArray *notExistFontModels = [NSMutableArray arrayWithCapacity:notExistFontCodes.count];
    [fonts enumerateObjectsUsingBlock:^(JCFontModel *fontModel, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([notExistFontCodes containsObject:fontModel.fontCode]) {
            [notExistFontModels addObject:fontModel];
        }
    }];
    // download
    if (notExistFontModels.count == 0 ) {
        if (block) block();
        return;
    }
    dispatch_group_t group = dispatch_group_create();
    for (NSInteger i = 0; i < notExistFontModels.count; i ++) {
        JCFontModel *model = [notExistFontModels safeObjectAtIndex:i];
        if (model) {
            dispatch_group_enter(group);
            [[JCFontManager sharedManager] downLoadFontRequestWithModel:model finishBlock:^(BOOL isscuess) {
                [JCFontModel saveTime:[XYTool getNowTimeTimestamp] fontCode:model.fontCode];
                dispatch_group_leave(group);
            }];
        }
    }
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        NSLog(@"请求完成");
        if (block) block();
    });
}

- (BOOL)checkHasUnTrailVipRes{
    BOOL hasVipRes = NO;
    NSArray *elements = self.elements;
    if(elements.count > 0){
        for (JCElementModel *elementModel in elements) {
            if(!STR_IS_NIL(elementModel.fontCode)){
                NSString *fontCode = elementModel.fontCode;
                NSArray *fonts = [[JCFMDB shareDatabase:DB_DEFAULT]  jc_lookupTable:TABLE_VIP_FONTINFO dicOrModel:[JCFontModel class] whereFormat:[NSString stringWithFormat:@"where fontCode = '%@'",fontCode]];
                if(fonts.count > 0){
                    JCFontModel *fontModel = fonts.firstObject;
                    if(fontModel.isVip.integerValue == 1){
                        hasVipRes = YES;
                        break;
                    }
                }else{
                    NSArray *fonts = [[JCFMDB shareDatabase:DB_DEFAULT]  jc_lookupTable:TABLE_USER_FONTINFO dicOrModel:[JCFontModel class] whereFormat:[NSString stringWithFormat:@"where fontCode = '%@'",fontCode]];
                    if(fonts.count > 0){
                        JCFontModel *fontModel = fonts.firstObject;
                        if(fontModel.isVip.integerValue == 1){
                            hasVipRes = YES;
                            break;
                        }
                    }
                }
            }else if(!STR_IS_NIL(elementModel.materialId)){
                NSString *materialId = elementModel.materialId;
                if([jc_vip_logo_ids containsObject:materialId]){
                    hasVipRes = YES;
                    break;
                }
                if([jc_vip_boder_ids containsObject:materialId]){
                    hasVipRes = YES;
                    break;
                }
            }
            else if([elementModel.type isEqualToString:@"table"]){
                NSArray *cellArr1 = elementModel.cells;
                NSArray *cellArr2 = elementModel.combineCells;
                for (JCElementModel *elementModel in cellArr1) {
                    if(!STR_IS_NIL(elementModel.fontCode)){
                        NSString *fontCode = elementModel.fontCode;
                        NSArray *fonts = [[JCFMDB shareDatabase:DB_DEFAULT]  jc_lookupTable:TABLE_VIP_FONTINFO dicOrModel:[JCFontModel class] whereFormat:[NSString stringWithFormat:@"where fontCode = '%@'",fontCode]];
                        if(fonts.count > 0){
                            JCFontModel *fontModel = fonts.firstObject;
                            if(fontModel.isVip.integerValue == 1){
                                hasVipRes = YES;
                                break;
                            }
                        }else{
                            NSArray *fonts = [[JCFMDB shareDatabase:DB_DEFAULT]  jc_lookupTable:TABLE_USER_FONTINFO dicOrModel:[JCFontModel class] whereFormat:[NSString stringWithFormat:@"where fontCode = '%@'",fontCode]];
                            if(fonts.count > 0){
                                JCFontModel *fontModel = fonts.firstObject;
                                if(fontModel.isVip.integerValue == 1){
                                    hasVipRes = YES;
                                    break;
                                }
                            }
                        }
                    }
                }
                for (JCElementModel *elementModel in cellArr2) {
                    if(!STR_IS_NIL(elementModel.fontCode)){
                        NSString *fontCode = elementModel.fontCode;
                        NSArray *fonts = [[JCFMDB shareDatabase:DB_DEFAULT]  jc_lookupTable:TABLE_VIP_FONTINFO dicOrModel:[JCFontModel class] whereFormat:[NSString stringWithFormat:@"where fontCode = '%@'",fontCode]];
                        if(fonts.count > 0){
                            JCFontModel *fontModel = fonts.firstObject;
                            if(fontModel.isVip.integerValue == 1){
                                hasVipRes = YES;
                                break;
                            }
                        }else{
                            NSArray *fonts = [[JCFMDB shareDatabase:DB_DEFAULT]  jc_lookupTable:TABLE_USER_FONTINFO dicOrModel:[JCFontModel class] whereFormat:[NSString stringWithFormat:@"where fontCode = '%@'",fontCode]];
                            if(fonts.count > 0){
                                JCFontModel *fontModel = fonts.firstObject;
                                if(fontModel.isVip.integerValue == 1){
                                    hasVipRes = YES;
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    self.hasVipRes = hasVipRes;
    return hasVipRes;
}


- (BOOL)useMultipleColor{
    BOOL hasMultipleColor = NO;
    NSArray *elements = self.elements;
    if(elements.count > 0){
        for (JCElementModel *elementModel in elements) {
            if([elementModel.type isEqualToString:@"table"]){
                if(![self array:elementModel.lineColor isEqualTo:@[@255,@0,@0,@0]]){
                    hasMultipleColor = YES;
                }
                if(![self array:elementModel.contentColor isEqualTo:@[@255,@0,@0,@0]]){
                    hasMultipleColor = YES;
                }
            }else{
                if(![self array:elementModel.elementColor isEqualTo:@[@255,@0,@0,@0]] && elementModel.paperColorIndex != 0){
                    hasMultipleColor = YES;
                }
            }
        }
    }
    return hasMultipleColor;
}

/// 是否元素中包含反白
- (BOOL)isContainerColorReverse {
    BOOL hasColorReverse = NO;
    NSArray *elements = self.elements;
    if(elements.count > 0){
        for (JCElementModel *elementModel in elements) {
            if(elementModel.colorReverse == 1){
                hasColorReverse = YES;
                break;
            }
        }
    }
    return hasColorReverse;
}


- (BOOL)array:(NSArray *)array1 isEqualTo:(NSArray *)array2 {
    if (array1.count != array2.count) {
        return NO;
    }
    for (NSString *str in array1) {
        if (![array2 containsObject:str]) {
            return NO;
        }
    }
    for (NSString *str in array2) {
        if (![array1 containsObject:str]) {
            return NO;
        }
    }
    return YES;
    
}

- (void)setVipProperty{
    if([self checkHasUnTrailVipRes] || [self checkHasVipTraiTime]){
        self.hasVipRes = YES;
    }else{
        self.hasVipRes = NO;
    }
}

- (BOOL)checkHasVipTraiTime{
    BOOL hasVipTraiTime = NO;
    NSArray *elements = self.elements;
    if(elements.count > 0){
        for (JCElementModel *elementModel in elements) {
            if([elementModel.type isEqualToString:@"date"]){
                if(elementModel.dateIsRefresh == 1){
                    hasVipTraiTime = YES;
                    break;
                }
            }
        }
    }
    return hasVipTraiTime;
}

- (BOOL)checkHasCurveText{
    BOOL hasCurveText = NO;
    NSArray *elements = self.elements;
    if(elements.count > 0){
        for (JCElementModel *elementModel in elements) {
            if(elementModel.typesettingMode == 3){
                hasCurveText = YES;
                break;
            }
        }
    }
    return hasCurveText;
}

- (void)resetUnDownloadVipFontResource {
    NSArray *elements = self.elements;
    if(elements.count > 0){//
        for (JCElementModel *elementModel in elements) {
            if(!STR_IS_NIL(elementModel.fontCode)){
                NSString *fontCode = elementModel.fontCode;
                if(![JCFontModel fontHasDownLoad:fontCode]){
                    elementModel.fontCode = text_default_font_code;
                }
            }
            if([elementModel.type isEqualToString:@"table"]){
                NSArray *cellArr1 = elementModel.cells;
                NSArray *cellArr2 = elementModel.combineCells;
                for (JCElementModel *elementModel in cellArr1) {
                    if(!STR_IS_NIL(elementModel.fontCode)){
                        NSString *fontCode = elementModel.fontCode;
                        if(![JCFontModel fontHasDownLoad:fontCode]){
                            elementModel.fontCode = text_default_font_code;
                        }
                    }
                }
                for (JCElementModel *elementModel in cellArr2) {
                    if(!STR_IS_NIL(elementModel.fontCode)){
                        NSString *fontCode = elementModel.fontCode;
                        if(![JCFontModel fontHasDownLoad:fontCode]){
                            elementModel.fontCode = text_default_font_code;
                        }
                    }
                }
            }
        }
    }
}

- (void)resetVipFontToUnuserResource{
    NSArray *elements = self.elements;
    if(elements.count > 0){
        for (JCElementModel *elementModel in elements) {
            if(!STR_IS_NIL(elementModel.fontCode)){
                NSString *fontCode = elementModel.fontCode;
                NSArray *fonts = [[JCFMDB shareDatabase:DB_DEFAULT]  jc_lookupTable:TABLE_VIP_FONTINFO dicOrModel:[JCFontModel class] whereFormat:[NSString stringWithFormat:@"where fontCode = '%@'",fontCode]];
                if(fonts.count > 0){
                    JCFontModel *fontModel = fonts.firstObject;
                    if(fontModel.isVip.integerValue == 1){
                        elementModel.fontCode = text_default_font_code;
                    }
                }
            }
            if([elementModel.type isEqualToString:@"table"]){
                NSArray *cellArr1 = elementModel.cells;
                NSArray *cellArr2 = elementModel.combineCells;
                for (JCElementModel *elementModel in cellArr1) {
                    if(!STR_IS_NIL(elementModel.fontCode)){
                        NSString *fontCode = elementModel.fontCode;
                        NSArray *fonts = [[JCFMDB shareDatabase:DB_DEFAULT]  jc_lookupTable:TABLE_VIP_FONTINFO dicOrModel:[JCFontModel class] whereFormat:[NSString stringWithFormat:@"where fontCode = '%@'",fontCode]];
                        if(fonts.count > 0){
                            JCFontModel *fontModel = fonts.firstObject;
                            if(fontModel.isVip.integerValue == 1){
                                elementModel.fontCode = text_default_font_code;
                            }
                        }
                    }
                }
                for (JCElementModel *elementModel in cellArr2) {
                    if(!STR_IS_NIL(elementModel.fontCode)){
                        NSString *fontCode = elementModel.fontCode;
                        NSArray *fonts = [[JCFMDB shareDatabase:DB_DEFAULT]  jc_lookupTable:TABLE_VIP_FONTINFO dicOrModel:[JCFontModel class] whereFormat:[NSString stringWithFormat:@"where fontCode = '%@'",fontCode]];
                        if(fonts.count > 0){
                            JCFontModel *fontModel = fonts.firstObject;
                            if(fontModel.isVip.integerValue == 1){
                                elementModel.fontCode = text_default_font_code;
                            }
                        }
                    }
                }
            }
        }
    }
}

- (void)resetVIPRealyTimeToUnuse{
    NSArray *elements = self.elements;
    if(elements.count > 0){
        for (JCElementModel *elementModel in elements) {
            if([elementModel.type isEqualToString:@"date"]){
                elementModel.dateIsRefresh = 0;
            }
        }
    }
}

//检查是否有VIP        YES  含有 NO 不含有
+ (BOOL)checkCanSaveOrPrintWith:(JCTemplateData *)templateData cancelBlock:(void(^)(void))cancelBlock sureBlock:(void(^)(void))sureBlock{
    BOOL hasVipRes = ([templateData shouldShowVip]);
    if(hasVipRes){
        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032", @"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app01520", @"您正在使用VIP会员专属资源，请先开通VIP会员即可享受。") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707", @"知道了") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01521", @"开通VIP") cancelBlock:cancelBlock sureBlock:sureBlock alertType:3];
        return NO;
    }else{
        return YES;
    }
}

- (BOOL)shouldShowVip{
    BOOL hasVipRes = ([self checkHasUnTrailVipRes] && !m_user_vip) || (self.vip && !m_user_vip) || ([self checkHasVipTraiTime] && !m_trialTimeIsValite);
    return hasVipRes;
}

- (BOOL)shouldCurveTextUpdrage{
    BOOL showCurveTextUpdrage = NO;
//    if([self checkHasCurveText] && !m_curveTextProbationEndTimeIsValite){
//        showCurveTextUpdrage = NO;
//    }
    return showCurveTextUpdrage;
}
@end
