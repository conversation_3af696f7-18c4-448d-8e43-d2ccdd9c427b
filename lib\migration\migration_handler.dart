import 'dart:convert';

import 'package:common_utils/common_utils.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:text/template/util/template_detail_db_utils.dart';
import 'package:text/template/util/template_transform_utils.dart';
import 'package:text/utils/niimbot_template_db_manager.dart';
import 'package:niimbot_template/models/template_data.dart' as NiimbotTemplateData;

abstract class MigrationHandler<T> {
  // 新增唯一迁移标识符（格式：表名_版本号）
  String get migrationKey;

  Future<int> get sourceCount;

  Future<List<T>> getBatch(int offset, int limit);

  Future<void> migrateItem(T item);

  Future<bool> get isMigrated async {
    final sp = await SharedPreferences.getInstance();
    return sp.getBool(migrationKey) ?? false;
  }

  Future<void> markAsMigrated() async {
    final sp = await SharedPreferences.getInstance();
    await sp.setBool(migrationKey, true);
  }
}

class TemplateMigrationHandler extends MigrationHandler<NiimbotTemplateData.TemplateData> {
  @override
  Future<int> get sourceCount async {
    int count = await NiimbotTemplateDbManager.dbQueryTemplateCount();
    LogUtil.d('MigrationManager模板总数${count}');
    return count;
  }

  @override
  Future<List<NiimbotTemplateData.TemplateData>> getBatch(int offset, int limit) async {
    List<TemplateData> canvasTemplates =
        await NiimbotTemplateDbManager.dbQueryTemplateDataWhere(limit: limit, offset: offset);
    List<NiimbotTemplateData.TemplateData> niimbotTemplates = [];
    for (int i = 0; i < canvasTemplates.length; i++) {
      TemplateData canvasT = canvasTemplates[i];
      String canvasJson = jsonEncode(canvasT);
      NiimbotTemplateData.TemplateData niimbotT = await TemplateTransformUtils.canvasJsonToNiimbotTemplate(canvasJson,needParseElements: false);
      niimbotTemplates.add(niimbotT);
    }
    return niimbotTemplates;
  }

  @override
  Future<void> migrateItem(NiimbotTemplateData.TemplateData item) async {
    try {
      await TemplateDetailDBUtils.migrationTemplateData(item);
      ///迁移成功后删除原始表中对应的模版
      await NiimbotTemplateDbManager.dbDeleteTemplateDataById(item.id!);
    } catch (e) {
      LogUtil.d('MigrationManager模板${item.id}迁移失败 error=${e.toString()}');
    }
  }

  @override
  String get migrationKey => "template_v1";
}

class TemplateCloudMigrationHandler extends MigrationHandler<NiimbotTemplateData.TemplateData> {
  @override
  Future<int> get sourceCount async {
    int count = await NiimbotTemplateDbManager.dbQueryTemplateCount(isCloudTemplate: true);
    LogUtil.d('MigrationManager云模板总数${count}');
    return count;
  }

  @override
  Future<List<NiimbotTemplateData.TemplateData>> getBatch(int offset, int limit) async {
    List<TemplateData> canvasTemplates =
    await NiimbotTemplateDbManager.dbQueryTemplateDataWhere(limit: limit, offset: offset);
    List<NiimbotTemplateData.TemplateData> niimbotTemplates = [];
    for (int i = 0; i < canvasTemplates.length; i++) {
      TemplateData canvasT = canvasTemplates[i];
      String canvasJson = jsonEncode(canvasT);
      NiimbotTemplateData.TemplateData niimbotT = await TemplateTransformUtils.canvasJsonToNiimbotTemplate(canvasJson);
      niimbotTemplates.add(niimbotT);
    }
    return niimbotTemplates;
  }

  @override
  Future<void> migrateItem(NiimbotTemplateData.TemplateData item) async {
    try {
      await TemplateDetailDBUtils.migrationTemplateData(item);
    } catch (e) {
      LogUtil.d('MigrationManager云模板${item.id}迁移失败 error=${e.toString()}');
    }
  }

  @override
  String get migrationKey => "template_v1";
}
