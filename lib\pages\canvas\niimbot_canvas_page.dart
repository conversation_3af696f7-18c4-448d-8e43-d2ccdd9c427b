import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:camera/camera.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart' as flutter_boost;
import 'package:flutter_boost/flutter_boost.dart';
import 'package:flutter_canvas_plugins_interface/config/template_config.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_config_interface.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/shared/canvas_font_data.dart';
import 'package:flutter_canvas_plugins_interface/shared/local_string_key.dart';
import 'package:flutter_canvas_plugins_interface/shared/localization_config.dart';
import 'package:flutter_canvas_plugins_interface/user_center/canvas_user_center.dart';
import 'package:flutter_canvas_plugins_interface/user_center/model/user_model.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/excel_transform_manager.dart';
import 'package:niimbot_flutter_canvas/src/provider/template_changed_notifier.dart';
import 'package:niimbot_flutter_canvas/src/utils/canvas_helper.dart';
import 'package:niimbot_flutter_canvas/src/utils/debounce_util.dart' as CanvasDebounceUtil;
import 'package:niimbot_flutter_canvas/src/utils/print_channel.dart';
import 'package:niimbot_flutter_canvas/src/utils/template_utils.dart';
import 'package:niimbot_flutter_canvas/src/widgets/assist/ocr/take_photos_camera_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/font_file_manager.dart';
import 'package:niimbot_flutter_canvas/src/widgets/good_lib/good_field_manager.dart';
import 'package:niimbot_template/models/copy_wrapper.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_data_source_info.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_template/models/template/template_data_source_range.dart';
import 'package:niimbot_template/models/template/template_enum.dart';
import 'package:niimbot_template/models/template_data.dart' as NiimbotTemplateData;
import 'package:niimbot_template/template_parse.dart';
import 'package:niimbot_template/utils/display_util.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:text/application.dart';
import 'package:text/business/user/user_login_helper.dart';
import 'package:text/cache/cache_helper.dart';
import 'package:text/pages/canvas/controller/niimbot_canvas_controller.dart';
import 'package:text/pages/canvas/impl/advance_qr_code_impl.dart';
import 'package:text/pages/canvas/impl/canvas_default_config_impl.dart';
import 'package:text/pages/canvas/impl/canvas_etag_config_impl.dart';
import 'package:text/pages/canvas/impl/canvas_industry_template_impl.dart';
import 'package:text/pages/canvas/impl/excel_import_impl.dart';
import 'package:text/pages/canvas/impl/font_panel_impl.dart';
import 'package:text/pages/canvas/impl/goods_import_impl.dart';
import 'package:text/pages/canvas/impl/label_import_impl.dart';
import 'package:text/pages/canvas/impl/loading_toast_impl.dart';
import 'package:text/pages/canvas/impl/material_selector_impl.dart';
import 'package:text/pages/canvas/impl/native_method_impl.dart';
import 'package:text/pages/canvas/impl/user_center/canvas_user_impl.dart';
import 'package:text/pages/canvas/impl/vip_trial_impl.dart';
import 'package:text/pages/canvas/impl/web_page_impl.dart';
import 'package:text/pages/my_template/model/template_data_extension.dart';
import 'package:text/template/model/template_operation_status.dart';
import 'package:text/template/template_manager.dart';
import 'package:text/template/util/template_transform_utils.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/DebounceUtil.dart';
import 'package:text/utils/custom_modal_route.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/toast_util.dart';

import '../../log_utils.dart';
import '../../routers/custom_navigation.dart';
import '../../utils/common_fun.dart';
import '../../vipTrial/vip_trial_manager.dart';

//ignore: must_be_immutable
class NiimbotCanvasPage extends StatefulWidget {
  String? uniqueId;

  String jsonData;
  String printerJsonData;
  String fontCategories;
  String fontPath;
  String fontDefaultFile;
  int needRfidTemplate;
  bool needDownloadFonts;
  Locale locale;
  String language;
  String source;
  int isNewTemplate;
  String defaultSelectType;

  NiimbotCanvasPage(
      {Key? key,
      required uniqueId,
      this.jsonData = "",
      this.printerJsonData = "",
      this.fontCategories = "",
      this.fontPath = "",
      this.fontDefaultFile = "",
      this.needRfidTemplate = 0,
      this.needDownloadFonts = true,
      this.language = 'zh_cn',
      this.locale = const Locale('zh'),
      this.source = '',
      this.defaultSelectType = '',
      this.isNewTemplate = 0})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return NiimbotCanvasPageState();
  }
}

class NiimbotCanvasPageState extends State<NiimbotCanvasPage> with flutter_boost.PageVisibilityObserver {
  final NiimbotCanvasController _controller = NiimbotCanvasController();
  Map? _singleColorPrintInfo;

  Timer? _updateRefreshDateTimer;
  late int enterTime;
  late int leaveTime;

  // 添加保存状态跟踪
  bool _templateHasBeenSaved = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    PageVisibilityBinding.instance.addObserver(this, CustomModalRoute.of(context) as Route);
  }

  int getDayNow() {
    var nowTime = DateTime.now();
    return nowTime.millisecondsSinceEpoch;
  }

  @override
  void initState() {
    Log.d("=============执行NiimbotCanvasPage initState");
    PdfBindInfoManager.instance.currentPageIndex = 0;
    enterTime = getDayNow();
    // 处理分享码打开NiimbotCanvasController被初始化后部分值异常的情况
    if (Get.isRegistered<NiimbotCanvasController>()) {
      _controller.state.initModel = Get.find<NiimbotCanvasController>().state.initModel;
    }
    Get.put(_controller);
    _controller
      ..context = context
      ..jsonData = widget.jsonData
      ..needRfidTemplate = widget.needRfidTemplate
      ..source = widget.source;
    _controller.syncRfidReplaceTag(init: true);
    // 初始化用户中心
    CanvasUserCenter(creator: () => CanvasUserImpl());
    // 重新赋予用户中心值
    // 后期模型可能不一致或者解码部分属性
    CanvasUserCenter().userModel = UserModel.fromJson(Application.user != null ? Application.user?.rawData ?? {} : {});

    // 监听用户信息变更
    NiimbotEventBus.getDefault().register(this, (data) {
      // 用户信息变更
      if (data is Map && data.containsKey("userInfo")) {
        // 后期模型可能不一致或者解码部分属性
        CanvasUserCenter().userModel = UserModel.fromJson(Application.user?.rawData ?? {});
        // NotifyListener
        CanvasUserCenter().notifyListeners();
      } else if (data is Map && data.containsKey("updateGoodsField")) {
        canvasKey.currentState?.updateGoodsField();
      } else if (data is Map && data.containsKey("updateCanvasPageJson")) {
        try {
          var target = TemplateData.fromJson(data['targetTemplate'] as Map<String, dynamic>);
          _controller.jsonData = json.encode(target);
          // 模板内容变更时重置保存状态
          _templateHasBeenSaved = false;
        } catch (e, s) {
          debugPrint('调用栈信息:\n $s');
        }
      } else if (data is Map && data.containsKey("saveSuccessData")) {
        // 监听保存成功事件
        _templateHasBeenSaved = true;
      } else if (data is Map && data.containsKey("myTemplateRefresh")) {
        // 监听模板刷新事件，通常在保存成功后触发
        _templateHasBeenSaved = true;
      }
    });

    // 插件接口实现
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // parserJson();
      CanvasPluginManager().excelImportImpl = ExcelImportImpl();
      CanvasPluginManager().goodsImportImpl = GoodsImportImpl();
      CanvasPluginManager().advanceQRCodeImpl = AdvanceQRCodeInterImpl();
      CanvasPluginManager().loadingToastImpl = LoadingToastImpl();
      CanvasPluginManager().fontPanelImpl = FontPanelImpl();
      CanvasPluginManager().webPageImpl = WebPageImpl();
      CanvasPluginManager().labelImpl = LabelImportImpl();
      CanvasPluginManager().vipMethodImpl = VipTrialImpl();
      CanvasPluginManager().canvasIndustryTemplateInterface = CanvasIndustryTemplateImpl();
      CanvasPluginManager().canvasConfigImpl =
          widget.source == "etag" ? CanvasEtagConfigImpl() : CanvasDefaultConfigImpl();

      ///素材列表已支持全球显示vip分类
      CanvasPluginManager().materialSelectorImpl = MaterialSelectorImpl()..isShowVip = true;
      CanvasPluginManager().nativeMethodImpl = NativeMethodImpl();
      parserJson().then((value) {
        CanvasPluginManager().loadingToastImpl?.dismissLoading();
        if (value != null) {
          Future.delayed(Duration(milliseconds: 200), () {
            CanvasEventBus.getDefault().post(value);
          });
        }
      }).catchError((_) {
        CanvasPluginManager().loadingToastImpl?.dismissLoading();
        CanvasPluginManager().loadingToastImpl?.showToast(intlanguage("app100001167", "加载数据源失败"));
        Future.delayed(Duration(milliseconds: 600), () {
          flutter_boost.BoostNavigator.instance.pop();
        });
      });
    });

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      /// 定时刷新实时时间
      _updateRefreshDateTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        canvasKey.currentState?.updateAllRefreshDates();
      });
      // Future.delayed(const Duration(seconds: 1)).then((value) {
      //   String newJsonData = TemplateChangedNotifier().templateDataJsonData ?? "";
      //   _controller.jsonData = newJsonData;
      // });

      // 延时500mil执行，保证非白屏下显示
      Future.delayed(const Duration(milliseconds: 500), () {
        // 当source来源时行业模版且jsonData的labelId和连接的labelId是一个，弹出提示表明行业模版的标签纸被替换为识别到的标签纸
        if (widget.source == 'industryTemplate') {
          // 入参模版数据
          TemplateData templateData = TemplateData.fromJson(jsonDecode(widget.jsonData));
          ToNativeMethodChannel().getPrinterLabelData().then((value) {
            if (value is Map) {
              // 当前连接打印机内标签纸数据
              TemplateData labelData = TemplateData.fromJson(Map<String, dynamic>.from(value));
              // 标签纸名称，根据local显示
              String name = templateData.labelNames.isEmpty
                  ? ''
                  : templateData.labelNames
                          .singleWhere(
                              (element) =>
                                  element.languageCode.toLowerCase() ==
                                  CanvasPluginManager().hostMethodImpl?.getCurrentLanguageType(),
                              orElse: () => templateData.labelNames.first)
                          .name ??
                      '';
              if (!TextUtil.isEmpty(labelData.id) && templateData.profile.extrain.labelId == labelData.id) {
                String msg = "${intlanguage("app01463", "检测到标签纸")}${name}";
                showToast(msg: msg);
              }
            }
          });
        }
      });
    });

    super.initState();
    // ToNativeMethodChannel().sendTrackingToNative({"track": "view", "posCode": "108", "ext": {}});
    CacheHelper.getInstance().cacheActionWhenToCanvasPage();
    if (Application.networkConnected) {
      GoodFieldManager().getGoodFields();
    }
  }

  @override
  void onPageShow() {
    super.onPageShow();
    _controller.pageShow.value = true;
    Log.d('===========niimbotCanvasPage---->onPageShow');
    enterTime = getDayNow();
    if (mounted) {
      VipTrialManager().context = context;
    }
  }

  @override
  void onPageHide() {
    super.onPageHide();
    _controller.pageShow.value = false;
    Log.d('===========niimbotCanvasPage---->onPageHide');
    leaveTime = getDayNow();
    int pageStayTime = leaveTime - enterTime;
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "pagequit",
      "posCode": "108",
      "ext": {"duration": pageStayTime}
    });
  }

  @override
  void onBackground() {
    Log.d('===========niimbotCanvasPage---->onBackground');
    super.onBackground();
    DebounceUtil.lastClickTime = null;
    CanvasDebounceUtil.DebounceUtil.lastClickTime = null;
    // // camera处理
    // if (cameraGlobalKey.currentState != null) {
    //   cameraGlobalKey.currentState?.controller.dispose();
    // }
    // leaveTime = getDayNow();
    // int pageStayTime = leaveTime - enterTime;
    // ToNativeMethodChannel().sendTrackingToNative({
    //   "track": "pagequit",
    //   "posCode": "108",
    //   "ext": {"duration": pageStayTime}
    // });
  }

  @override
  void onForeground() {
    super.onForeground();
    enterTime = getDayNow();
    if (cameraGlobalKey.currentState != null && cameraGlobalKey.currentState?.controller != null) {
      final CameraController? cameraController = cameraGlobalKey.currentState?.controller;
      if (cameraController == null || !cameraController.value.isInitialized) {
        return;
      }
      // 重新初始化新的Controller
      cameraGlobalKey.currentState?.initializeCameraController(cameraController.description);
    }
  }

  @override
  void dispose() {
    PageVisibilityBinding.instance.removeObserver(this);
    TemplateChangedNotifier().templateDataJsonData = null;
    // 取消事件监听，防止内存泄漏
    NiimbotEventBus.getDefault().unregister(this);
    Get.delete<NiimbotCanvasController>();
    _controller.dispose();
    if (_updateRefreshDateTimer?.isActive == true) {
      _updateRefreshDateTimer?.cancel();
    }
    super.dispose();
  }

  String appDocDir = "";
  String jsonData = "";
  String printerJsonData = "";
  final List<CanvasFontData> _canvasFontData = [];

  Future parserJson() async {
    appDocDir = widget.fontPath.isEmpty ? "" : widget.fontPath;
    String jsonDataOrigin = widget.jsonData.isEmpty
        ? await DefaultAssetBundle.of(context).loadString("assets/print_json_40_30.txt")
        : widget.jsonData;
    log("=======进入画板json：$jsonDataOrigin");
    Directory baseExcelFileDir = await getApplicationDocumentsDirectory();
    TemplateUtils.documentPath = baseExcelFileDir.path;
    ExcelTransformManager.sharedInstance().documentPath = baseExcelFileDir.path;
    ExcelManager.sharedInstance().dataSourceWrapper = null;
    jsonDataOrigin = await TemplateUtils.transformTemplateJsonData(widget.jsonData);
    jsonData = await TemplateData.loadImageData(jsonDataOrigin);
    Map<String, dynamic> oldJsonMap = jsonDecode(jsonData);
    await AdvanceQRCodeManager().initAdvanceQRCodeCache(TemplateData.fromJson(oldJsonMap));
    List<String> currentPaperColors = [];
    dynamic supportColors = await ToNativeMethodChannel().getCurrentPaperSupportColors();
    if (supportColors != null) {
      currentPaperColors = List<String>.from(supportColors);
    }
    Map? ribbonDoubleColor = null;
    Map? singleColorPrintInfo = (await ToNativeMethodChannel().getCurrentSingleColorInfo() as Map?) ?? {};
    if (singleColorPrintInfo.containsKey("consumablesType") && singleColorPrintInfo["consumablesType"] == 2) {
      String ribbonColor = singleColorPrintInfo["currentPrintColor"];
      if (ribbonColor.contains(",")) {
        _singleColorPrintInfo = {};
        ribbonDoubleColor = singleColorPrintInfo;
        ribbonDoubleColor["action"] = "currentPrintColor";
      } else {
        _singleColorPrintInfo = singleColorPrintInfo;
      }
    } else {
      _singleColorPrintInfo = singleColorPrintInfo;
    }
    if (currentPaperColors.isNotEmpty) {
      oldJsonMap["paperColor"] = currentPaperColors;
    } else if (oldJsonMap["paperColor"] == null) {
      oldJsonMap["paperColor"] = ["0.0.0", "52.199.89", "230.0.18"];
    }
    jsonData = initTemplateElementColor(oldJsonMap);
    printerJsonData = widget.printerJsonData.isEmpty
        ? await DefaultAssetBundle.of(context).loadString("assets/printer_json_40_30.txt")
        : widget.printerJsonData;
    String fontJsonData = widget.fontCategories.isEmpty
        ? await DefaultAssetBundle.of(context).loadString("assets/fontlib_zh-cn.json")
        : widget.fontCategories;
    var jsonMap = jsonDecode(fontJsonData);
    for (var fontDataJson in jsonMap) {
      _canvasFontData.add(CanvasFontData(
          code: fontDataJson['code'] as String,
          isVip: fontDataJson['isVip'] as bool,
          thumbnailUrl: fontDataJson['thumbnailUrl'] as String,
          id: fontDataJson['id'] as int,
          url: fontDataJson['path'] as String,
          name: fontDataJson['name'] as String));
    }
    if (Platform.isIOS) {
      appDocDir = '${(await getApplicationDocumentsDirectory()).path}/font/';
    } else if (Platform.isAndroid) {
      appDocDir = appDocDir.isEmpty
          ? '/data/user/0/com.gengcon.android.jccloudprinter/files/font_manager/custom_and_vip/'
          : appDocDir;
    }
    if (kDebugMode) {
      Log.d('------appDocDir $appDocDir');
    }

    TemplateData template = TemplateData.fromJson(jsonDecode(jsonData));
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "view",
      "posCode": "108",
      "ext": {"temp_id": template.id.length > 10 ? "" : template.id, "industry_temp_id": template.cloudTemplateId ?? ""}
    });
    _controller.getSelectPrinter(template.profile.machineId,
        isCustomTemplate: template.profile.extrain.labelId?.isNotEmpty != true);

    FontFileManager().fontPath = appDocDir;
    setState(() {
      _controller.jsonData = jsonData;
      // 新模板加载时重置保存状态
      _templateHasBeenSaved = false;
    });
    if (_controller.hasPopShowRfidDialog == false) {
      _controller.hasPopShowRfidDialog = true;
      // _controller.getRfidTemplate();
    }

    return Future.value(ribbonDoubleColor);
  }

  String initTemplateElementColor(Map<String, dynamic> templateMap) {
    String templateData = "";
    List<String> paperColor = List<String>.from(templateMap["paperColor"]);
    if (paperColor.isNotEmpty) {
      for (Map element in templateMap["elements"] as List) {
        if (element["type"] == "table") {
          int lineColorChannel = element["lineColorChannel"] < 0 ? 0 : element["lineColorChannel"] ?? 0;
          if (lineColorChannel >= paperColor.length) lineColorChannel = 0;
          String lineColorStr = "255." + paperColor[lineColorChannel];
          element["lineColor"] = lineColorStr.split(".").map((e) => int.parse(e)).toList();
          int contentColorChannel = element["contentColorChannel"] ?? 0;
          if (contentColorChannel >= paperColor.length) contentColorChannel = 0;
          String contentColorStr = "255." + paperColor[contentColorChannel];
          element["contentColor"] = contentColorStr.split(".").map((e) => int.parse(e)).toList();
        } else {
          int paperColorIndex = element["paperColorIndex"] ?? 0;
          if (paperColorIndex >= paperColor.length) paperColorIndex = 0;
          String elementColorStr = "255." + paperColor[paperColorIndex];
          element["elementColor"] = elementColorStr.split(".").map((e) => int.parse(e)).toList();
        }
      }
    }
    templateData = jsonEncode(templateMap);
    return templateData;
  }

  @override
  Widget build(BuildContext context) {
    // Log.d("=============执行NiimbotCanvasPage build--->jsonData: $jsonData");

    return WillPopScope(
        onWillPop: () async {
          return true;
        },
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          body: jsonData.isNotEmpty
              ? CanvasThemeWidget(
                  defaultSelectType: widget.defaultSelectType,
                  onNavigatorPop:
                      ((buildContext, canvasData, showOtherSave, isEtagSave, resetElementFocusStateFun) async {
                    TemplateData template = TemplateData.fromJson(jsonDecode(canvasData ?? ""));
                    ToNativeMethodChannel().sendTrackingToNative({
                      "track": "click",
                      "posCode": "108_073_102",
                      "ext": {"temp_id": template.id ?? "", "industry_temp_id": template.cloudTemplateId ?? ""}
                    });
                    String newCanvasJson = TemplateUtils.transformContentTitle(canvasData ?? "");
                    NiimbotTemplateData.TemplateData templateData = await _canvasJsonToNiimbotTemplate(newCanvasJson);
                    //传过来的showOtherSave参数废弃，重新赋值
                    showOtherSave = await TemplateManager().checkContainFile(templateData.id, isEtag: isEtagSave);
                    bool needSaveAsk = !exitDrawboard(newCanvasJson);
                    //电子价签过来的模版无网的状态下不支持保存
                    if (needAskSaveAlert(needSaveAsk, isEtagSave)) {
                      List<ExitSheetData> exitSheetDataList = _getExitSheetDataListV2(
                          templateData, showOtherSave, context, resetElementFocusStateFun, newCanvasJson);
                      if (ModalRoute.of(context)?.isCurrent == false) {
                        return false;
                      }
                      showCustomListDialog(context, intlanguage('app00110', '是否保存后退出'), '', exitSheetDataList, (index) {
                        if (index == -1) {
                          _exitSheetClickTrack("取消", templateData);
                        } else {
                          exitSheetDataList[index].sheetClickAction.call();
                        }
                      });
                    } else {
                      ExcelTransformManager().clearData();
                    }
                    return needAskSaveAlert(needSaveAsk, isEtagSave);
                  }),
                  key: Key(jsonData),
                  jsonData: jsonData,
                  needDownloadFonts: widget.needDownloadFonts,
                  fontConfig: CanvasFontConfig(
                      fontCategories: [
                        CanvasFontCategoryData(id: "1", name: "分类 1", fonts: _canvasFontData),
                        CanvasFontCategoryData(id: "2", name: "分类 2", fonts: _canvasFontData),
                      ],
                      // fontPath: Platform.isIOS ? appDocDir : '/data/user/0/com.gengcon.android.jccloudprinter/files/',
                      fontPath: appDocDir,
                      fontDefaultFile: widget.fontDefaultFile.isEmpty ? 'ZT001.ttf' : widget.fontDefaultFile),
                  toolkitButtons: [
                    // ToolkitButton(
                    //     title: '动作触发',
                    //     icon: 'assets/element_icon_speech.svg',
                    //     type: ToolkitButtonType.action,
                    //     index: 100,
                    //     actionEvent: (BuildContext buildContext, ToolkitButton toolkitButton) {}),
                  ],
                  onPrint: _onPrintData,
                  onSave: (canvasJson, config, context, templateName, {String? templateId, bool isOtherSave = true}) {
                    Completer<TemplateData> completer = Completer<TemplateData>();
                    _onSavedDataV2(
                      canvasJson ?? "",
                      context,
                      (value) {
                        return completer.complete(value);
                      },
                    );
                    return completer.future;
                  },
                  onCheckSmartTypesettingOpen: _ifShowSmartTypesetting,
                  onSmartTypesetting: _onSmartTypesetting,
                  onSmartTypesettingFresh: _onSmartTypesettingFresh,
                  canvasInitData: _canvasInitData,
                  onConfig: _controller.labelSetting,
                  getI18nString: _controller.getI18nString,
                  onLabelChange: _controller.onLabelInfoClick,
                  localizationConfig: LocalizationConfig(widget.locale,
                      customLocalizationResource: {LocalStringKey.element: '添加元素'},
                      direction: Application.textDirection),
                  singleColorPrintInfo: _singleColorPrintInfo,
                  language: widget.language,
                  initModel: _controller.state.initModel,
                  onIMClick: (String imLink) {
                    Map<String, dynamic> map = {"imLink": imLink};
                    CustomNavigation.gotoNextPage('DrawboardIMPage', map);
                  },
                )
              : Container(
                  color: Colors.white,
                ),
        ));
  }

  ///未登录情况下，保存显示暂存本地弹窗
  showStageDialog(bool isCanSavaLocal, Function()? loginSucceed) {
    String title = isCanSavaLocal
        ? intlanguage("app100001914", "已暂存本地，登录后可永久保存")
        : intlanguage('app100000125', '您需要登录账号，用来保存您当前的数据。');
    String leftBtn = intlanguage('app00030', '取消');
    String rightBtn = intlanguage('app01191', '立即登录');
    showNimmbotDialog(context, title: title, cancelDes: leftBtn, confirmDes: rightBtn, confirmAction: () {
      UserLoginHelper().gotoLogin(context, loginFailed: () {}, loginSucceed: loginSucceed);
    }, cancelAction: () {});
  }

  //点击返回是否需要弹出保存的弹框
  bool needAskSaveAlert(bool needSaveAsk, bool isEtagSave) {
    CanvasItemConfigInterface? canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
    bool networkConnected = CanvasPluginManager().fontPanelImpl!.isNetReachable();
    bool isEtag = canvasConfigIml?.currentConfigMode() == CanvasCurrentConfigMode.etag;
    //电子价签模式下
    if (isEtag) {
      //点击的底部保存
      if (isEtagSave) {
        return true;
      } else {
        //点击返回 有网有更改的前提下才弹框
        if (networkConnected && needSaveAsk) {
          return true;
        } else {
          return false;
        }
      }
      //常规模式下
    } else {
      return needSaveAsk;
    }
  }

  //保存确认弹窗Sheet列表
  List<ExitSheetData> getSaveSheetDataList(String canvasData, bool showOtherSave, BuildContext context) {
    List<ExitSheetData> exitSheetDataList = [];

    ///保存
    ExitSheetData saveSheet =
        ExitSheetData(title: intlanguage('app00017', '保存'), saveType: SaveType.save, sheetClickAction: () {});
    exitSheetDataList.add(saveSheet);

    ///另存为
    if (showOtherSave) {
      ExitSheetData otherSaveSheet =
          ExitSheetData(title: intlanguage('app00039', '另存为'), saveType: SaveType.otherSave, sheetClickAction: () {});
      exitSheetDataList.add(otherSaveSheet);
    }
    return exitSheetDataList;
  }

  ///退出确认弹窗Sheet列表
  List<ExitSheetData> _getExitSheetDataListV2(NiimbotTemplateData.TemplateData templateData, bool showOtherSave,
      BuildContext context, Function resetElementFocusStateFun, String canvasData) {
    List<ExitSheetData> exitSheetDataList = [];

    ///保存
    ExitSheetData saveSheet = ExitSheetData(
        title: intlanguage('app00017', '保存'),
        sheetClickAction: () {
          _checkSaveTemplateContainerPdf(canvasData, () {
            _onSavedDataWithBack(templateData, null, context, isExistTemplate: showOtherSave, isOtherSave: false)
                .then((value) {
              if (value == null) {
                return;
              }
              ExcelTransformManager().clearData();
              _exitCanvasPage();
            });
            _exitSheetClickTrack("保存", templateData);
          });
        });
    exitSheetDataList.add(saveSheet);

    ///另存为
    if (showOtherSave) {
      ExitSheetData otherSaveSheet = ExitSheetData(
          title: intlanguage('app00039', '另存为'),
          sheetClickAction: () {
            //todo
            _checkSaveTemplateContainerPdf(canvasData, () {
              _onSavedDataWithBack(templateData, null, context, isExistTemplate: showOtherSave, isOtherSave: true)
                  .then((value) {
                if (value == null) {
                  return;
                }
                ExcelTransformManager().clearData();
                _exitCanvasPage();
              });
              _exitSheetClickTrack("另存为", templateData);
            });
          });
      exitSheetDataList.add(otherSaveSheet);
    }

    ///不保存
    ExitSheetData notSaveSheet = ExitSheetData(
        title: intlanguage('app00111', '不保存'),
        saveType: SaveType.notSave,
        sheetClickAction: () {
          resetElementFocusStateFun.call();
          ExcelTransformManager().clearData();
          flutter_boost.BoostNavigator.instance.pop();
          _exitSheetClickTrack("不保存", templateData);
        });
    exitSheetDataList.add(notSaveSheet);
    return exitSheetDataList;
  }

  _exitCanvasPage() {
    if (_updateRefreshDateTimer?.isActive == true) {
      _updateRefreshDateTimer?.cancel();
    }
    //toast显示时间需要1s toast消失后再关闭页面
    Duration duration = Duration(seconds: 1);
    // showSuccessToast(context, duration: duration);
    Future.delayed(duration, () {
      ExcelTransformManager().clearData();
      flutter_boost.BoostNavigator.instance.pop();
    });
  }

  ///退出画板确认弹窗sheet点击埋点
  _exitSheetClickTrack(String sheetName, NiimbotTemplateData.TemplateData? templateData) {
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "108_213_200",
      "ext": {
        "b_name": sheetName,
        "temp_id": templateData?.id ?? "",
        "industry_temp_id": templateData?.cloudTemplateId ?? "",
      }
    });
  }

  bool exitDrawboard(String canvasJson) {
    /// 解析模板数据
    Map<String, dynamic> canvasJsonMap = jsonDecode(canvasJson);
    TemplateData canvasData = TemplateData.fromJson(canvasJsonMap);
    Map<String, dynamic> oldJsonMap = jsonDecode(_controller.jsonData ?? "");
    TemplateData oldData = TemplateData.fromJson(oldJsonMap);
    bool isSameData = true;

    // Log.e("old模板：${_controller.jsonData}");
    // Log.e("new模板：${canvasJson}");
    ///对比模板基础信息
    if (!compairBaseInfo(oldData, canvasData)) {
      isSameData = false;
    }

    ///对比模板中元素信息
    if (!compairElementInfo(oldData.elements, canvasData.elements)) {
      isSameData = false;
    }

    ///对比模板中的modify信息
    if (!TemplateUtils.compairModifyInfo(oldData, canvasData)) {
      isSameData = false;
    }
    // 获取画板中数据是否已做保存
    // if (canvasTemplateInfoHasSave(canvasData)) {
    //   isSameData = true;
    // }

    return isSameData;
  }

  bool canvasTemplateInfoHasSave(TemplateData canvasData) {
    bool hasSave = _templateHasBeenSaved;
    return hasSave;
  }

  bool compairBaseInfo(TemplateData oldData, TemplateData canvasData) {
    bool isSameBaseInfo = true;
    // oldData.totalPage = 1;
    bool hasOnlyPdfSource = false;
    if ((canvasData.dataSource?.isEmpty ?? true) &&
        (canvasData.externalData?.externalDataList?.isEmpty ?? true) &&
        PdfBindInfoManager.instance.templateIsBindPdf(canvasData)) {
      hasOnlyPdfSource = true;
    }
    if (oldData.name != canvasData.name ||
        oldData.cableLength != canvasData.cableLength ||
        (oldData.currentPageIndex != canvasData.currentPageIndex && !hasOnlyPdfSource) ||
        oldData.cableDirection != canvasData.cableDirection ||
        oldData.consumableType != canvasData.consumableType ||
        oldData.width != canvasData.width ||
        oldData.height != canvasData.height ||
        oldData.multipleBackIndex != canvasData.multipleBackIndex ||
        (oldData.totalPage != canvasData.totalPage && canvasData.totalPage > 1) ||
        oldData.canvasRotate != canvasData.canvasRotate ||
        oldData.rotate != canvasData.rotate ||
        !compairBindDataSourceInfo(oldData, canvasData)) {
      isSameBaseInfo = false;
    }
    return isSameBaseInfo;
  }

  bool compairBindDataSourceInfo(TemplateData oldData, TemplateData canvasData) {
    bool isSameDataSource = true;
    // oldData.totalPage = 1;
    if ((oldData.dataSource == null && canvasData.dataSource == null) ||
        ((oldData.dataSource ?? []).isEmpty && (canvasData.dataSource ?? []).isEmpty)) {
      isSameDataSource = true;
    } else if ((oldData.dataSource == null && canvasData.dataSource != null) ||
        (oldData.dataSource != null && canvasData.dataSource == null)) {
      isSameDataSource = false;
    } else {
      if ((oldData.dataSource ?? []).isEmpty && (canvasData.dataSource ?? []).isEmpty) {
        isSameDataSource = true;
      } else {
        Map? oldDataSource = oldData.dataSource?.first.toJson();
        Map? newDataSource = canvasData.dataSource?.first.toJson();
        isSameDataSource = paramsMapEquals(oldDataSource, newDataSource);
      }
    }
    return isSameDataSource;
  }

  bool paramsMapEquals<T, U>(Map<T, U>? a, Map<T, U>? b) {
    if (a == null) {
      return b == null;
    }
    if (b == null || a.length != b.length) {
      return false;
    }
    if (identical(a, b)) {
      return true;
    }
    for (final T key in a.keys) {
      if (!b.containsKey(key)) {
        return false;
      } else {
        if (b[key] is! Map) {
          if (b[key] is List) {
            if (key == "range") {
              if ((b[key] as List).length != (a[key] as List).length) {
                return false;
              } else {
                for (int index = 0; index < (b[key] as List).length; index++) {
                  if (!paramsMapEquals((b[key] as List)[index] as Map<T, U>, (a[key] as List)[index] as Map<T, U>)) {
                    return false;
                  }
                }
              }
            } else if (key == "ids") {
              if ((b[key] as List).join(",") != (a[key] as List).join(",")) {
                return false;
              }
            }
          } else if (b[key] != a[key]) {
            return false;
          }
        } else {
          if (!paramsMapEquals(b[key] as Map<T, U>, a[key] as Map<T, U>)) {
            return false;
          }
        }
      }
    }
    return true;
  }

  bool compairElementInfo(List oldElements, List canvasElements) {
    bool isSameElementsInfo = true;
    if (oldElements.length != canvasElements.length) {
      isSameElementsInfo = false;
    }
    int sameCount = 0;
    for (JsonElement oldElement in oldElements) {
      for (JsonElement canvasElement in canvasElements) {
        if (oldElement.id != canvasElement.id) {
          continue;
        } else {
          if (!isSameELement(oldElement, canvasElement)) {
            isSameElementsInfo = false;
            break;
          } else {
            sameCount++;
          }
        }
      }
    }
    return isSameElementsInfo && sameCount == canvasElements.length;
  }

  bool isSameELement(JsonElement oldElemnt, canvasElement) {
    if (oldElemnt.value == intlanguage('app00364', '双击文本框编辑')) {
      oldElemnt.value = "";
    }
    if (canvasElement.value == intlanguage('app00364', '双击文本框编辑')) {
      canvasElement.value = "";
    }
    bool isSameElementInfo = oldElemnt.isSameELement(canvasElement);
    return isSameElementInfo;
  }

  ///检查一键排版开关
  Future<List<bool>> _ifShowSmartTypesetting(BuildContext context, String? canvasJson) {
    if (widget.isNewTemplate != 1) {
      return Future.value([false, false]);
    }
    return _controller.ifShowSmartTypesetting(context, canvasJson);
  }

  ///点击一键排版
  Future<int> _onSmartTypesetting(
      String? canvasJson, String? dataBindingMode, int? currentPage, TemplateConfig? config, BuildContext context) {
    return _controller.openSmartTypesetting(context);
  }

  ///获取一键排版推荐模板
  Future<TemplateData?> _onSmartTypesettingFresh(
      String canvasJson, String dataBindingMode, int currentPage, TemplateConfig? config, BuildContext context) {
    return _controller.getSmartTemplate(context, canvasJson);
  }

  /// 打印入口
  void _onPrintData(String? canvasJson, String? dataBindingMode, int? currentPage, TemplateConfig? config,
      List<Map<String, dynamic>>? pdfBindInfos, BuildContext context) {
    try {
      canvasJson = TemplateUtils.transformToCompactTemplateJsonData(canvasJson ?? "", dateSourceChange: true);

      /// 实例化模板数据结构
      // TemplateData templateData = TemplateData.fromJson(json.decode(canvasJson));

      // /// 生成可打印的转义数据
      // String printJson = templateData.generatePrintJson(pageIndex: currentPage - 1);
      // Log.d('----  canvasData ----\n${templateData.generateCanvasJson()}');
      // Log.d('----  printData ----\n$printJson');

      String rfidInfoJson = "";
      RfidInfo? rfidInfo = RfidRepository().getRfidInfo();
      if (rfidInfo != null) {
        rfidInfoJson = jsonEncode(rfidInfo.toJson());
      }
      VipTrialManager().context = null;
      CustomNavigation.gotoNextPage('ToPrintSettingPage', {
        'content': canvasJson,
        "pdfBindInfo": pdfBindInfos,
        'needRfidTemplate': widget.needRfidTemplate == 1 ? true : false,
        // 增加是否从画板弹出，避免打印历史记录下载资源的影响，导致断网的情况下无法打印
        'isFromCanvas': true,
        'showRfid': RfidBindCheck.isSupportRecordRfid(),
        'rfidInfo': rfidInfoJson,
        // 'isAiLayout': CanvasHelper.isAiLayout,
        'printChannelCode': CanvasHelper.printChannelCode?.featureCode,
        'needDownloadRes': false,
      }) /*.then((value) {
        if (value != null && value is Map && value["updateLabelData"] == true) {
          _controller.setCanvasLabelData(json.decode(value['labelData']));
        }
      })*/
          ;
    } catch (e) {
      Log.d(e.toString());
    }
  }

  Future<TemplateData?> _onSavedDataWithBack(
      NiimbotTemplateData.TemplateData templateData, TemplateConfig? config, BuildContext context,
      {bool isExistTemplate = false, bool isOtherSave = false}) async {
    //处理离线或者断网 datasource url如果为空的时候，上传oss以及添加到云文件的逻辑
    bool canSave = await TemplateUtils.processDataSourceUrl();
    if (!canSave) {
      return null;
    }
    CanvasItemConfigInterface? canvasConfig = CanvasPluginManager().canvasConfigImpl;
    //是否为电子价签画板
    bool isEtag = false;
    if (canvasConfig?.currentConfigMode() == CanvasCurrentConfigMode.etag) {
      isEtag = true;
    }
    Completer<TemplateData?> completer = Completer<TemplateData?>();
    TemplateData? resultTemplate;
    if (isEtag) {
      //电子价签模版不管是保存或者另存都要弹出重命名弹窗，特殊处理
      showEditTextDialog(
        context,
        intlanguage('app100001281', '命名为'),
        "${templateData.name}",
        100,
        (value) async {
          NiimbotTemplateData.TemplateData copyT = await templateData.copyWith(name: value);
          resultTemplate = await _processSaveTemplateLogic(copyT, isExistTemplate, isOtherSave, isEtag: isEtag);
          completer.complete(resultTemplate);
        },
        editTextHint: intlanguage('app00290', '请输入模板名称'),
        editTextEmptyToast: intlanguage('app00290', '请输入模板名称'),
      );
    } else {
      if (isOtherSave) {
        //点击另存为按钮
        showEditTextDialog(
          context,
          intlanguage('app00040', '新建名称'),
          "${templateData.name}-1",
          100,
          (value) async {
            NiimbotTemplateData.TemplateData copyT = await templateData.copyWith(name: value);
            resultTemplate = await _processSaveTemplateLogic(copyT, isExistTemplate, true, isEtag: false);
            completer.complete(resultTemplate);
          },
          editTextHint: intlanguage('app00290', '请输入模板名称'),
          editTextEmptyToast: intlanguage('app00290', '请输入模板名称'),
        );
      } else {
        //点击保存按钮
        resultTemplate = await _processSaveTemplateLogic(templateData, isExistTemplate, false);
        completer.complete(resultTemplate);
      }
    }
    return completer.future;
  }

  Future<TemplateData> _processSaveTemplateLogic(
      NiimbotTemplateData.TemplateData templateData, bool isExistTemplate, bool isOtherSave,
      {bool isEtag = false}) async {
    if (isOtherSave) {
      if (!Application.isLogin && !isEtag && !templateData.isExcel() && !templateData.isCommodity()) {
        // showStageDialog(true, () {});
      } else if (!Application.isLogin && !isEtag && (templateData.isExcel() || templateData.isCommodity())) {
        Completer<TemplateData> completer = Completer<TemplateData>();
        showStageDialog(false, () {
          _onSavedDataV2(
            jsonEncode(templateData),
            context,
            (value) {
              return completer.complete(value);
            },
          );
        });
        return completer.future;
      }
      //点击另存为按钮
      NiimbotTemplateData.TemplateData template =
          await TemplateManager().createTemplate(templateData, isEtag: isEtag, callback: (status, nt) {
        switch (status) {
          case TemplateOperationStatus.localSuccess:
            showSuccessToast(context, isSaveWithBack: true);
            //刷新flutter我的模版列表
            NiimbotEventBus.getDefault().post({"myTemplateRefresh": ""});
            ToNativeMethodChannel.refreshNativeTemplateList();
            //刷新原生相关的模版列表
            _templateHasBeenSaved = true;
            break;
          case TemplateOperationStatus.serverSuccess:
            NiimbotEventBus.getDefault().post({"myTemplateRefresh": ""});
            ToNativeMethodChannel.refreshNativeTemplateList();
            _templateHasBeenSaved = true;
            break;
        }
      },isFromCanvas: true);
      TemplateData canvasTemplate = await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(template);
      _controller.jsonData = jsonEncode(canvasTemplate.toJson());
      if (isEtag) {
        NiimbotEventBus.getDefault().post({"saveSuccessData": _controller.jsonData});
      }
      return canvasTemplate;
    } else {
      if (!Application.isLogin && !isEtag && !templateData.isExcel() && !templateData.isCommodity()) {
        // showStageDialog(true, () {});
      } else if (!Application.isLogin && !isEtag && (templateData.isExcel() || templateData.isCommodity())) {
        Completer<TemplateData> completer = Completer<TemplateData>();
        showStageDialog(false, () {
          _onSavedDataV2(
            jsonEncode(templateData),
            context,
            (value) {
              return completer.complete(value);
            },
          );
        });
        return completer.future;
      }
      //点击保存按钮
      if (isExistTemplate) {
        NiimbotTemplateData.TemplateData template =
            await TemplateManager().updateTemplate(templateData, isEtag: isEtag, callback: (status, nt) {
          switch (status) {
            case TemplateOperationStatus.localSuccess:
              showSuccessToast(context, isSaveWithBack: true);
              NiimbotEventBus.getDefault().post({"myTemplateRefresh": ""});
              ToNativeMethodChannel.refreshNativeTemplateList();
              _templateHasBeenSaved = true;
              break;
            case TemplateOperationStatus.serverSuccess:
              NiimbotEventBus.getDefault().post({"myTemplateRefresh": ""});
              ToNativeMethodChannel.refreshNativeTemplateList();
              _templateHasBeenSaved = true;
              break;
          }
        }, isFromCanvas: true);
        TemplateData canvasTemplate = await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(template);
        _controller.jsonData = jsonEncode(canvasTemplate.toJson());
        if (isEtag) {
          NiimbotEventBus.getDefault().post({"saveSuccessData": _controller.jsonData});
        }
        return canvasTemplate;
      } else {
        if (!Application.isLogin && !isEtag && !templateData.isExcel() && !templateData.isCommodity()) {
          // showStageDialog(true, () {});
        } else if (!Application.isLogin && !isEtag && (templateData.isExcel() || templateData.isCommodity())) {
          Completer<TemplateData> completer = Completer<TemplateData>();
          showStageDialog(false, () {
            _onSavedDataV2(
              jsonEncode(templateData),
              context,
              (value) {
                return completer.complete(value);
              },
            );
          });
          return completer.future;
        }
        NiimbotTemplateData.TemplateData template =
            await TemplateManager().createTemplate(templateData, isEtag: isEtag, callback: (status, nt) {
          switch (status) {
            case TemplateOperationStatus.localSuccess:
              showSuccessToast(context, isSaveWithBack: true);
              NiimbotEventBus.getDefault().post({"myTemplateRefresh": ""});
              ToNativeMethodChannel.refreshNativeTemplateList();
              _templateHasBeenSaved = true;
              break;
            case TemplateOperationStatus.serverSuccess:
              NiimbotEventBus.getDefault().post({"myTemplateRefresh": ""});
              ToNativeMethodChannel.refreshNativeTemplateList();
              _templateHasBeenSaved = true;
              break;
          }
        }, isFromCanvas: true);
        TemplateData canvasTemplate = await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(template);
        _controller.jsonData = jsonEncode(canvasTemplate.toJson());
        if (isEtag) {
          NiimbotEventBus.getDefault().post({"saveSuccessData": _controller.jsonData});
        }
        return canvasTemplate;
      }
    }
  }

  _onSavedDataV2(String canvasJson, BuildContext context, ValueChanged? valueCall) async {
    CanvasItemConfigInterface? canvasConfig = CanvasPluginManager().canvasConfigImpl;
    //是否为电子价签画板
    bool isEtag = false;
    if (canvasConfig?.currentConfigMode() == CanvasCurrentConfigMode.etag) {
      isEtag = true;
    }
    bool canSave = await TemplateUtils.processDataSourceUrl();
    if (!canSave) {
      return null;
    }
    canvasJson = TemplateUtils.transformContentTitle(canvasJson);
    NiimbotTemplateData.TemplateData templateData = await _canvasJsonToNiimbotTemplate(canvasJson);
    bool isTemplateExist = await TemplateManager().checkContainFile(templateData.id, isEtag: isEtag);
    if (isTemplateExist) {
      List<ExitSheetData> exitSheetDataList = getSaveSheetDataList(canvasJson, true, context);
      showCustomListDialog(context, intlanguage('app00110', '是否保存后退出'), '', exitSheetDataList, (index) async {
        if (index != -1) {
          _checkSaveTemplateContainerPdf(canvasJson, () async {
            bool isClickOtherSave = exitSheetDataList[index].saveType == SaveType.otherSave ? true : false;
            if (isClickOtherSave) {
              LogUtil.d(
                  "other save niimotT totalPage=${templateData.totalPage} currentPageIndex=${templateData.currentPageIndex}");
              showEditTextDialog(
                context,
                intlanguage('app00040', '新建名称'),
                "${templateData.name}-1",
                100,
                (value) async {
                  if (!Application.isLogin && !isEtag && !templateData.isExcel() && !templateData.isCommodity()) {
                    showStageDialog(true, () {});
                  } else if (!Application.isLogin &&
                      !isEtag &&
                      (templateData.isExcel() || templateData.isCommodity())) {
                    showStageDialog(false, () {
                      _onSavedDataV2(canvasJson, context, valueCall);
                    });
                    return;
                  }
                  NiimbotTemplateData.TemplateData copyT = await templateData.copyWith(name: value);
                  NiimbotTemplateData.TemplateData template =
                      await TemplateManager().createTemplate(copyT, isEtag: isEtag, callback: (status, nt) {
                    switch (status) {
                      case TemplateOperationStatus.localSuccess:
                        showSuccessToast(context);
                        NiimbotEventBus.getDefault().post({"myTemplateRefresh": ""});
                        ToNativeMethodChannel.refreshNativeTemplateList();
                        _templateHasBeenSaved = true;
                        break;
                      case TemplateOperationStatus.serverSuccess:
                        NiimbotEventBus.getDefault().post({"myTemplateRefresh": ""});
                        ToNativeMethodChannel.refreshNativeTemplateList();
                        _templateHasBeenSaved = true;
                        break;
                    }
                  }, isFromCanvas: true);
                  TemplateData canvasTemplate = await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(template);
                  _controller.jsonData = jsonEncode(canvasTemplate.toJson());
                  valueCall?.call(canvasTemplate);
                },
                editTextHint: intlanguage('app00290', '请输入模板名称'),
                editTextEmptyToast: intlanguage('app00290', '请输入模板名称'),
              );
            } else {
              if (!Application.isLogin && !isEtag && !templateData.isExcel() && !templateData.isCommodity()) {
                showStageDialog(true, () {});
              } else if (!Application.isLogin && !isEtag && (templateData.isExcel() || templateData.isCommodity())) {
                showStageDialog(false, () {
                  _onSavedDataV2(canvasJson, context, valueCall);
                });
                return;
              }
              NiimbotTemplateData.TemplateData template =
                  await TemplateManager().updateTemplate(templateData, isEtag: isEtag, callback: (status, nt) {
                switch (status) {
                  case TemplateOperationStatus.localSuccess:
                    showSuccessToast(context);
                    NiimbotEventBus.getDefault().post({"myTemplateRefresh": ""});
                    ToNativeMethodChannel.refreshNativeTemplateList();
                    _templateHasBeenSaved = true;
                    break;
                  case TemplateOperationStatus.serverSuccess:
                    NiimbotEventBus.getDefault().post({"myTemplateRefresh": ""});
                    ToNativeMethodChannel.refreshNativeTemplateList();
                    _templateHasBeenSaved = true;
                    break;
                }
              }, isFromCanvas: true);
              TemplateData canvasTemplate = await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(template);
              _controller.jsonData = jsonEncode(canvasTemplate.toJson());
              valueCall?.call(canvasTemplate);
            }
          });
        }
      });
    } else {
      _checkSaveTemplateContainerPdf(canvasJson, () async {
        if (!Application.isLogin && !isEtag && !templateData.isExcel() && !templateData.isCommodity()) {
          showStageDialog(true, () {});
        } else if (!Application.isLogin && !isEtag && (templateData.isExcel() || templateData.isCommodity())) {
          showStageDialog(false, () {
            _onSavedDataV2(canvasJson, context, valueCall);
          });
          return;
        }
        NiimbotTemplateData.TemplateData template =
            await TemplateManager().createTemplate(templateData, isEtag: isEtag, callback: (status, nt) {
          switch (status) {
            case TemplateOperationStatus.localSuccess:
              showSuccessToast(context);
              NiimbotEventBus.getDefault().post({"myTemplateRefresh": ""});
              ToNativeMethodChannel.refreshNativeTemplateList();
              _templateHasBeenSaved = true;
              break;
            case TemplateOperationStatus.serverSuccess:
              NiimbotEventBus.getDefault().post({"myTemplateRefresh": ""});
              ToNativeMethodChannel.refreshNativeTemplateList();
              _templateHasBeenSaved = true;
              break;
          }
        }, isFromCanvas: true);
        TemplateData canvasTemplate = await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(template);
        _controller.jsonData = jsonEncode(canvasTemplate.toJson());
        valueCall?.call(canvasTemplate);
      });
    }
  }

  _checkSaveTemplateContainerPdf(String canvasJson, VoidCallback checkCallBack) async {
    SharedPreferences sp = await SharedPreferences.getInstance();
    bool? hasShowPdfSaveTip = sp.getBool("has_show_pdf_save_tip");
    TemplateData data = TemplateData.fromJson(json.decode(canvasJson));
    if (hasShowPdfSaveTip == null && PdfBindInfoManager.instance.templateIsBindPdf(data)) {
      sp.setBool("has_show_pdf_save_tip", true);
      ToNativeMethodChannel().sendTrackingToNative({"track": "show", "posCode": "108_395_358", "ext": {}});
      String title = intlanguage('app100001861', '仅保存当前页面的PDF内容，下次打印时请重新选择PDF文件。');
      Future.delayed(Duration(milliseconds: 200), () {
        showCustomDialog(context, title, "",
            justSureButton: true,
            dismissOutSideTouch: false,
            rightFunStr: intlanguage('app00707', '我知道了'), rightFunCall: () {
          checkCallBack.call();
        }, rightTextColor: Colors.black);
      });
    } else {
      checkCallBack.call();
    }
  }

  Future<NiimbotTemplateData.TemplateData> _canvasJsonToNiimbotTemplate(String canvasJson) async {
    Map<String, dynamic> jsonMap = jsonDecode(canvasJson);
    //转换成插件中的templateData结构
    jsonMap['dataSourceModifies'] = jsonMap['modify'];
    jsonMap['dataSourceBindInfo'] = jsonMap['bindInfo'];
    jsonMap['dataSources'] = jsonMap['dataSource'];
    int currentPageIndex = 0;
    if (jsonMap['currentPage'] is num && jsonMap['currentPage'] > 0) {
      currentPageIndex = jsonMap['currentPage']-1;
    }
    jsonMap['currentPageIndex'] = currentPageIndex;
    initGenerateDesignRatio(context, jsonMap);
    List<String> headerData = [];
    NiimbotTemplateData.TemplateData templateData =
        await TemplateParse.parseFromMap(jsonMap, parseDataSourceResources: (url, hash) async {
      headerData = ExcelTransformManager().headers ?? [];
      List<List<String>> rowData = ExcelTransformManager().rowData ?? [];
      List<List<String>> resultData = [...rowData];
      resultData.insert(0, headerData);
      return resultData;
    }, parseLocalImageResources: (url) {
      final backgroundImage = TemplateParseUtils.parseStringFromJSON(jsonMap['backgroundImage']);
      if (backgroundImage != null) {
        final urlList = backgroundImage.split(',');
        var index = urlList.indexOf(url);
        if (jsonMap['localBackground'] != null) {
          List localBackground = jsonMap['localBackground'];
          if (localBackground.isNotEmpty && index >= 0 && index < localBackground.length) {
            return Future.value(localBackground[index]);
          } else {
            return Future.value('');
          }
        } else {
          return Future.value('');
        }
      } else {
        return Future.value('');
      }
    });
    LogUtil.d("niimotT totalPage=${templateData.totalPage} currentPageIndex=${templateData.currentPageIndex}");
    List<TemplateDataSource>? dataSources;
    if (templateData.dataSources != null && templateData.dataSources!.isNotEmpty) {
      TemplateDataSource dataSource = templateData.dataSources!.first;
      if (dataSource.type == TemplateDataSourceType.commodity) {
        dataSource.headers = {DataBindingMode.commodity: headerData};
      } else {
        // dataSource.headers = {dataSource.headers!.keys.first: headerData};
      }
      dataSources = templateData.dataSources;
      // dataSources = _dataSourceRangeToDesktop(templateData.dataSources, templateData.dataSourceBindInfo);
    }
    templateData = templateData.copyWith(dataSources: CopyWrapper.value(dataSources));
    return templateData;
  }

  List<TemplateDataSource>? _dataSourceRangeToDesktop(
      final List<TemplateDataSource>? dataSource, final TemplateDataSourceInfo? dataSourceInfo) {
    return dataSource?.map((final e) {
      if ((e.range?.isEmpty == true) && dataSourceInfo != null) {
        e.range = [TemplateDataSourceRange(s: 1, e: dataSourceInfo.total)];
      } else {
        e.range = e.range?.map((final v) {
          return TemplateDataSourceRange(s: v.s - 1, e: v.e - 1);
        }).toList();
      }
      return e;
    }).toList();
  }

  initGenerateDesignRatio(BuildContext context, Map<String, dynamic> jsonMap) {
    String widthValue = jsonMap['width'].toString();
    String hightValue = jsonMap['height'].toString();
    //使用niimbot_template插件中倍率计算类
    DisplayUtil.init(context);
    DisplayUtil.generateDesignRatio(double.parse(widthValue), double.parse(hightValue));
  }

  void showSuccessToast(BuildContext context,
      {Duration duration = const Duration(seconds: 1), bool isSaveWithBack = false}) {
    //未登录情况已经提示了暂存弹窗,返回时保存没有暂存弹窗继续走原先的逻辑
    if (!Application.isLogin && !isSaveWithBack) {
      return;
    }
    FToast().init(context).showToast(
        child: showIconToast(),
        toastDuration: duration,
        positionedToastBuilder: (context, child, gravity) {
          return Container(
            child: child,
          );
        });
  }

  showIconToast() {
    String saveSuccessTip =
        Application.user == null ? intlanguage('app100001913', '已保存到本地') + "!" : intlanguage('app00349', '保存成功') + "!";
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0),
        color: Colors.black.withOpacity(0.5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                height: 18,
                width: 18,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.0),
                ),
              ),
              Image.asset(
                'assets/images/vipNpsSuccess.png',
                height: 20,
                width: 20,
              ),
            ],
          ),
          SizedBox(
            width: 7.0,
          ),
          Text(
            saveSuccessTip,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w400, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Future<bool> _canvasInitData(String? canvasJson) {
    _controller.jsonData = canvasJson;
    // 画板数据初始化时重置保存状态
    _templateHasBeenSaved = false;
    return Future(() {
      return true;
    });
  }
}

enum SaveType {
  /// 保存
  save,

  /// 另存为
  otherSave,
  //不保存
  notSave
}

class ExitSheetData {
  String title;
  Function sheetClickAction;
  SaveType? saveType;

  ExitSheetData({required this.title, required this.sheetClickAction, this.saveType});
}

extension TemplateDataSourceModifiesExtension on TemplateDataSourceModifies {
  Map<String, dynamic> toJson() {
    Map<String, dynamic> json = {};
    forEach((key, map) {
      Map<String, dynamic> innerJson = {};
      map.forEach((innerKey, innerValue) {
        innerJson[innerKey.toString()] = innerValue.toJson();
      });
      json[key] = innerJson;
    });
    return json;
  }

  static TemplateDataSourceModifies fromJson(Map<String, dynamic> json) {
    TemplateDataSourceModifies templateModify = {};
    json.forEach((key, value) {
      Map<String, TemplateDataSourceModify> innerMap = {};
      value.forEach((innerKey, innerValue) {
        innerMap[innerKey] = TemplateDataSourceModify.fromJson(innerValue);
      });
      templateModify[key] = innerMap;
    });
    return templateModify;
  }
}
