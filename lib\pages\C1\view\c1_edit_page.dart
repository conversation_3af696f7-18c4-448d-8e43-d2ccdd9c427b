import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_quill/quill_delta.dart';
import 'package:get/get.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:niimbot_template/models/copy_wrapper.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/font_size_config.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/models/values/composite_value_type.dart';
import 'package:niimbot_template/models/values/serial_value_type.dart';
import 'package:niimbot_template/models/values/template_value_type.dart';
import 'package:niimbot_template/models/values/text_value_type.dart';
import 'package:popover/popover.dart';
import 'package:text/application.dart';
import 'package:text/pages/C1/common/dotted_line.dart';
import 'package:text/pages/C1/common/group_step_widget.dart';
import 'package:text/pages/C1/common/items_popmenu_widget.dart';
import 'package:text/pages/C1/common/num_step_widget.dart';
import 'package:text/pages/C1/controller/c1_edit_logic.dart';
import 'package:text/pages/C1/controller/c1_home_logic.dart';
import 'package:text/pages/C1/model/c1_print_manager.dart';
import 'package:text/pages/C1/model/define_model.dart';
import 'package:text/pages/C1/model/template_data_transform.dart';
import 'package:text/pages/C1/quill/serial_embed.dart';
import 'package:text/pages/C1/quill/serial_embed_builder.dart';
import 'package:text/pages/C1/quill/symbol_embed.dart';
import 'package:text/pages/C1/quill/symbol_ember_builder.dart';
import 'package:text/pages/C1/view/symbol/symbol_selector.dart';
import 'package:text/tools/rfid_manager.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/cachedImageUtil.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';
import 'package:tuple/tuple.dart';

class C1EditPage extends StatefulWidget {
  /// 模版数据
  final TemplateData templateData;

  /// 标题
  final String consumableInfo;

  final String lengthInfo;

  final int tubeType;

  final double tubeSpecs;

  final TextFormat textFormat;

  const C1EditPage(
      {super.key,
      required this.templateData,
      required this.consumableInfo,
      required this.lengthInfo,
      required this.tubeType,
      required this.tubeSpecs,
      required this.textFormat});

  @override
  State<C1EditPage> createState() => _C1EditPageState();
}

class _C1EditPageState extends State<C1EditPage> with TickerProviderStateMixin {
  /// 逻辑控制器
  late C1EditLogic logic;

  /// 编辑焦点
  final FocusNode editNode = FocusNode();

  /// 编辑控制器
  final TextEditingController textEditingController = TextEditingController();

  /// quill富文本编辑器
  final QuillController quillController = QuillController.basic();

  /// 富文本编辑器滑动管理
  final ScrollController quillEditorScrollController = ScrollController();

  final ScrollController paragraphListScrollController = ScrollController();

  /// 键盘订阅
  late StreamSubscription<bool> keyboardSubscription;

  /// 动画控制器
  late AnimationController animationController;

  /// 动画
  late Animation<double> animation;

  /// 上次未溢出的文本长度
  late int lastNotOverflowDocumentLength;

  /// 当前输入是否溢出
  late bool inputIsOverFlow = false;

  /// 当前是否正在更新富文本编辑器的值
  late bool isUpdatingQuillController = false;

  StreamSubscription<DocChange>? quillChangeSubscription;

  double keyboardHeight = 0;

  bool popoverShowing = false;

  double get itemHeight => 36 + widget.templateData.height * TemplateDataTransform.dpRatio + 20 + 12;

  final GlobalKey settingKey = GlobalKey();

  bool importGuideShowing = false;

  @override
  void initState() {
    super.initState();

    // 逻辑控制器
    logic = C1EditLogic(
        template: widget.templateData,
        tubeType: widget.tubeType,
        tubeSpecs: widget.tubeSpecs,
        textFormat: widget.textFormat,
        consumableInfo: widget.consumableInfo,
        lengthInfo: widget.lengthInfo);
    logic.importExcelGuide = () {
      int userId = Application.user?.userId ?? 0;
      bool guideShowed = _isUserShowImportGuide(userId);
      if (guideShowed) {
        return;
      }
      if (!mounted) {
        return;
      }
      BuildContext? settingContext = settingKey.currentContext;
      if (settingContext == null) {
        return;
      }
      showPopover(
          context: settingContext,
          rootNavigator: false,
          transitionDuration: Duration(milliseconds: 1),
          bodyBuilder: (context) {
            return Container(
              width: 134,
              height: 44,
              padding: EdgeInsetsDirectional.symmetric(horizontal: 6),
              child: Center(
                child: Text(
                  intlanguage('app100001927', '从这里批量导入'),
                  style: TextStyle(color: Colors.black, fontSize: 14, fontWeight: FontWeight.w400),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            );
          },
          shadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.15),
              blurRadius: 55,
              spreadRadius: 0.1,
            ),
          ],
          radius: 12,
          direction: PopoverDirection.bottom,
          width: 134,
          arrowHeight: 10,
          arrowWidth: 18,
          barrierColor: Colors.transparent,
          routeSettings: RouteSettings(name: "c1ImportExcelPopover"),
          onPop: () {
            importGuideShowing = false;
          });
      importGuideShowing = true;
      _recordUserShowImportGuide(userId);
    };
    // 插入logic
    Get.put<C1EditLogic>(logic);

    // 动画控制器初始化
    animationController = AnimationController(duration: Duration(milliseconds: 150), vsync: this);

    // 动画初始化
    animation = Tween(begin: -10.0, end: 10.0).animate(CurvedAnimation(
      parent: animationController,
      curve: Curves.easeInOut,
    ));

    // 键盘监听
    _setKeyboardListener();

    // 富文本编辑器监听
    _setQuillListener();

    // 取消全局手势
    Application.isEnableGesture = false;

    RfidManager.instance.updateC1RibbonColorListener = (ribbonColor) {
      TemplateDataTransform.ribbonColor = ribbonColor;
      List<BaseElement> elements = logic.templateData().elements;
      for (int i = 0; i < elements.length; i++) {
        elements[i] = elements[i].copyWith(imageCache: CopyWrapper<NetalImageResult?>.value(null));
      }
      setState(() {});
    };

    TemplateDataTransform.ribbonColor = RfidManager.getC1RibbonColor();
  }

  bool _isUserShowImportGuide(int userId) {
    return Application.sp.getBool("C1ImportGuideShowed_$userId") ?? false;
  }

  _recordUserShowImportGuide(int userId) {
    Application.sp.setBool("C1ImportGuideShowed_$userId", true);
  }

  _setKeyboardListener() {
    // 键盘监听
    var keyboardVisibilityController = KeyboardVisibilityController();
    // Subscribe
    keyboardSubscription = keyboardVisibilityController.onChange.listen((bool visible) {
      switch (logic.paragraphStatus()) {
        case ParagraphStatus.isEditing:
          // 当前是由双击导致的键盘show，默认为编辑态，当键盘Hide，但键盘事件是符号或者序号则代表仍旧处于编辑态，
          // 只是不展示键盘，否则重置为段落状态为none
          logic.paragraphStatus.value = visible
              ? ParagraphStatus.isEditing
              : logic.keyBoardActionStatus() == KeyBoardActionStatus.normal
                  ? ParagraphStatus.none
                  : ParagraphStatus.isEditing;
          if (popoverShowing) {
            Navigator.of(context).popUntil((route) => route.settings.name == "c1_edit_page");
            popoverShowing = false;
          }
          break;
        case ParagraphStatus.isEditingSelected:
          // 当前是由双击导致的键盘show，默认为编辑态，当键盘Hide，但键盘事件是符号或者序号则代表仍旧处于编辑态，
          // 只是不展示键盘，否则重置为段落状态为none
          logic.paragraphStatus.value = visible
              ? ParagraphStatus.isEditingSelected
              : logic.keyBoardActionStatus() == KeyBoardActionStatus.none
                  ? ParagraphStatus.none
                  : ParagraphStatus.isEditingSelected;
          break;
        case ParagraphStatus.none:
          break;
      }
      if (logic.paragraphStatus() == ParagraphStatus.none) {
        // 编辑器清空
        quillController.readOnly = false;
        quillChangeSubscription?.cancel();
        quillChangeSubscription = null;
      }
    });
  }

  _setQuillListener() {
    quillController.addListener(() {
      // 正在更新值时不进行监听，等待值更新完毕再处理
      if (isUpdatingQuillController) {
        return;
      }
      // document长度
      final documentLength = quillController.document.length;
      // 超出提示
      ValueChanged<Tuple2<bool, double>> overFlowClosure = (pair) {
        bool isOverFlow = pair.item1;
        double dx = pair.item2;
        inputIsOverFlow = isOverFlow;
        lastNotOverflowDocumentLength = quillController.document.length;
        if (isOverFlow) {
          logic.refreshCurrentParagraph();
          // Vibration.vibrate(duration: 500, amplitude: 128);
          animationController.repeat(reverse: true, period: Duration(milliseconds: 150));
          Future.delayed(const Duration(milliseconds: 600), () {
            animationController.animateTo(0.5, curve: Curves.easeInOut, duration: Duration(milliseconds: 75));
          });
        } else {
          if (logic.templateData().tubeFileSetting?.autoWidth == true) {
            _autoWidthScroll(dx);
          }
        }
      };
      // 上次输入溢出且仍旧在输入，禁止输入
      if (inputIsOverFlow && documentLength > lastNotOverflowDocumentLength) {
        final latestIndex = lastNotOverflowDocumentLength - 1;
        quillController.replaceText(
          latestIndex,
          documentLength - lastNotOverflowDocumentLength,
          '',
          TextSelection.collapsed(offset: latestIndex),
          shouldNotifyListeners: false,
        );
        return;
      }
      // 获取富文本编辑器文本值
      String textValue =
          quillController.document.toPlainText([SymbolEmbedBuilder(), SerialEmbedBuilder()]).replaceAll('\n', '');
      // 根据序列号分隔符分割前后部分
      List<String> splitValues = textValue.split('\$/〇-〇/').where((e) => e.isNotEmpty).toList();

      // 解析序列号和文本值
      List<ValueTypeProtocol> valueObjects = [];
      int index = 0;
      for (String val in splitValues) {
        try {
          // 尝试解析为JSON，如果成功则为序列号
          Map<String, dynamic> serialMap = jsonDecode(val);
          SerialValueType serialValue = SerialValueType.fromJson(serialMap);
          valueObjects.add(serialValue);
        } catch (e) {
          // 解析失败，作为文本处理
          TextValueType textValue = TextValueType(
            id: '${logic.selectedParagraphElement?.id}_$index',
            elementId: logic.selectedParagraphElement?.id,
            type: TemplateValueType.text,
            value: val,
          );
          valueObjects.add(textValue);
        }
        index++;
      }

      // 如果没有内容，至少添加一个空文本
      if (valueObjects.isEmpty) {
        valueObjects.add(TextValueType(
          id: '${logic.selectedParagraphElement?.id}_0',
          elementId: logic.selectedParagraphElement?.id,
          type: TemplateValueType.text,
          value: '',
        ));
      }

      // 直接使用值对象列表更新段落结构
      logic.updateValueObjectsFromList(valueObjects: valueObjects, overFlowClosure: overFlowClosure);

      if (popoverShowing) {
        Navigator.of(context).popUntil((route) => route.settings.name == "c1_edit_page");
        popoverShowing = false;
      }
    });
  }

  resetParagraphStatus() {
    if (logic.paragraphStatus == ParagraphStatus.none) {
      return;
    }
    // 状态置空
    logic.paragraphStatus.value = ParagraphStatus.none;
    quillController.readOnly = false;
    quillChangeSubscription?.cancel();
    quillChangeSubscription = null;
  }

  @override
  Widget build(BuildContext context) {
    // 防止进入画板后context未刷新的情况，此处和画板内对齐
    TemplateDataTransform.init(context);
    TemplateDataTransform.generateDesignRatio(
        logic.templateData().width.toDouble(), logic.templateData().height.toDouble());
    return PopScope(
        canPop: false,
        onPopInvoked: (bool didPop) async {
          if (!didPop) {
            if (importGuideShowing) {
              Navigator.of(settingKey.currentContext!).pop();
              return;
            }
            if (logic.paragraphStatus != ParagraphStatus.none) {
              resetParagraphStatus();
            } else {
              logic.handleExitPage(context);
            }
          }
        },
        child: KeyboardVisibilityBuilder(builder: (_, bool isKeyboardVisible) {
          if (isKeyboardVisible) {
            keyboardHeight = MediaQuery.viewInsetsOf(context).bottom;
          }
          return Material(
            child: ColoredBox(
              color: ThemeColor.COLOR_F0F0F5,
              child: Stack(
                children: [
                  Column(
                    children: [
                      barWidget(),
                      Divider(color: ThemeColor.COLOR_F0F0F5, height: 1),
                      Expanded(child: fileListWidget()),
                      buildBottomBar(),
                    ],
                  ),
                  Obx(() {
                    switch (logic.paragraphStatus()) {
                      case ParagraphStatus.isEditing:
                      case ParagraphStatus.isEditingSelected:
                        return Positioned.fill(child: keyboardAction());
                      case ParagraphStatus.none:
                        return SizedBox.shrink();
                    }
                  }),
                ],
              ),
            ),
          );
        }));
  }

  void handleParagraphEditAction(int index, ValueTypeProtocol value, int columnIndex) {
    String oldSelectParagraphId = logic.selectedParagraph()?.item1.id ?? '';
    // 获取当前选中
    String newSelectParagraphId = value.id ?? '';
    // 更新选中段落
    logic.selectedParagraph.value = Tuple3<ValueTypeProtocol, int, BuildContext>(value, columnIndex, context);
    TextElement? selectText = logic.selectedElement;
    logic.selectTextFontSize = selectText?.fontSize;
    logic.selectTextBold = selectText?.fontStyle.contains(NetalTextFontStyle.bold);
    logic.selectTextUnderline = selectText?.fontStyle.contains(NetalTextFontStyle.underline);
    logic.selectTextAlignH = selectText?.textAlignHorizontal == NetalTextAlign.start;
    logic.selectTextVerticalStyle = selectText?.typesettingMode == NetalTypesettingMode.vertical;
    //如果编辑段落已经有序号，则不允许再次插入序号
    CompositeValueType columnValue = (value as CompositeValueType).valueObjects![columnIndex] as CompositeValueType;
    logic.allowInsertSerial.value = !C1PrintManager.instance.checkColumnContainSerial(columnValue);
    // 状态变更为编辑中
    logic.paragraphStatus.value = ParagraphStatus.isEditing;
    // 刷新键盘
    logic.update([logic.selectedParagraphElement?.id ?? '']);
    // 更新富文本编辑器值
    updateQuillController();
    // 刷新段落
    logic.update([oldSelectParagraphId, newSelectParagraphId]);
    Future.delayed(const Duration(milliseconds: 400), () {
      if (oldSelectParagraphId != newSelectParagraphId) {
        scrollParagraphListToPosition(index);
      }
      if (logic.templateData().tubeFileSetting?.autoWidth == true) {
        double dx = 0;
        ScrollController scrollController = logic.getParagraphScrollController(newSelectParagraphId);
        if (logic.templateData().tubeFileSetting?.line == 1) {
          dx = scrollController.position.maxScrollExtent;
        } else {
          int columnIndexOther;
          if (columnIndex == 0) {
            columnIndexOther = 1;
          } else {
            columnIndexOther = 0;
          }
          Tuple2<NetalImageResult?, BaseElement?> result =
              logic.generateElementImage(parentValue: value, columnIndex: columnIndex);
          Tuple2<NetalImageResult?, BaseElement?> resultOther =
              logic.generateElementImage(parentValue: value, columnIndex: columnIndexOther);
          int? width;
          if (result.item2 != null && (result.item2 as TextElement).typesettingMode == NetalTypesettingMode.vertical) {
            width = result.item1?.height;
          } else {
            width = result.item1?.width;
          }
          int? widthOther;
          if (resultOther.item2 != null &&
              (resultOther.item2 as TextElement).typesettingMode == NetalTypesettingMode.vertical) {
            widthOther = resultOther.item1?.height;
          } else {
            widthOther = resultOther.item1?.width;
          }
          if (width != null && widthOther != null) {
            if (width >= widthOther) {
              dx = scrollController.position.maxScrollExtent;
            } else {
              dx =
                  max(scrollController.position.maxScrollExtent - (TemplateDataTransform.px2dp(widthOther - width)), 0);
            }
          }
        }
        scrollController.animateTo(
          dx,
          duration: Duration(milliseconds: 200),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void scrollParagraphListToBottom() {
    paragraphListScrollController.animateTo(
      paragraphListScrollController.position.maxScrollExtent,
      duration: Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );
  }

  void scrollParagraphListToPosition(int position) {
    paragraphListScrollController.animateTo(
      itemHeight * position,
      duration: Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );
  }

  @override
  void dispose() {
    super.dispose();
    animationController.dispose();
    paragraphListScrollController.dispose();
    Get.delete<C1EditLogic>();
    // 启用全局手势
    Application.isEnableGesture = true;
    RfidManager.instance.updateC1RibbonColorListener = null;
  }
}

extension Bar on _C1EditPageState {
  // 头部
  barWidget() {
    return Theme(
      data: Theme.of(context).copyWith(splashColor: Colors.transparent, highlightColor: Colors.transparent),
      child: AppBar(
        centerTitle: true,
        backgroundColor: ThemeColor.background,
        elevation: 0,
        leading: IconButton(
            onPressed: () {
              logic.handleExitPage(context);
            },
            icon: const SvgIcon(
              'assets/images/industry_template/home/<USER>',
              width: 10,
              height: 16,
              matchTextDirection: true,
            )),
        title: Text(
          logic.title,
          style: TextStyle(
            fontSize: 17,
            color: ThemeColor.title,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () async {
              resetParagraphStatus();
              await logic.toFileSettingPage(context, paragraphListScrollController);
              setState(() {});
            },
            icon: SvgIcon(
              'assets/images/C1/gear.svg',
              width: 21,
              height: 21,
              key: settingKey,
            ),
          ),
        ],
      ),
    );
  }
}

extension Edit on _C1EditPageState {
  Widget fileListWidget() {
    double paddingBottom;
    if (logic.paragraphStatus != ParagraphStatus.none && keyboardHeight != 0) {
      paddingBottom = keyboardHeight + 40 + 48 + 12 - 50;
    } else {
      paddingBottom = 20;
    }
    return Container(
      child: NotificationListener<ScrollNotification>(
        onNotification: (notification) {
          if (notification is ScrollStartNotification && notification.dragDetails != null) {
            resetParagraphStatus();
          }
          return true;
        },
        child: ScrollConfiguration(
          behavior: ScrollConfiguration.of(context).copyWith(
              // 设置为ClampingScrollPhysics去掉回弹效果
              physics: ClampingScrollPhysics(),
              scrollbars: false),
          child: ListView.separated(
            padding: EdgeInsetsDirectional.fromSTEB(16, 20, 16, paddingBottom),
            controller: paragraphListScrollController,
            itemCount: (logic.templateData().values?.length ?? 0) + 1,
            itemBuilder: (BuildContext context, int index) {
              if ((logic.templateData().values?.length ?? 0) == index) {
                return newParagraphWidget();
              }
              return editItemWidget(index: index, valueTypeProtocol: logic.templateData().values?[index]);
            },
            separatorBuilder: (BuildContext context, int index) {
              return SizedBox(
                height: 12,
              );
            },
          ),
        ),
      ),
    );
  }

  Widget editItemWidget({required int index, required ValueTypeProtocol? valueTypeProtocol}) {
    return GetBuilder<C1EditLogic>(
        id: valueTypeProtocol?.id,
        builder: (logic) {
          CompositeValueType value = logic.templateData().values![index] as CompositeValueType;
          int repeatCount = value.repeatCount!;
          bool containSerial;
          int serialCount;
          if (value.valueObjects?.length == 2) {
            containSerial =
                C1PrintManager.instance.checkColumnContainSerial(value.valueObjects![0] as CompositeValueType) ||
                    C1PrintManager.instance.checkColumnContainSerial(value.valueObjects![1] as CompositeValueType);
            serialCount = max(
                C1PrintManager.instance.getColumnSerialCount(value.valueObjects![0] as CompositeValueType),
                C1PrintManager.instance.getColumnSerialCount(value.valueObjects![1] as CompositeValueType));
          } else {
            containSerial =
                C1PrintManager.instance.checkColumnContainSerial(value.valueObjects![0] as CompositeValueType);
            serialCount = C1PrintManager.instance.getColumnSerialCount(value.valueObjects![0] as CompositeValueType);
          }
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              DefaultTextStyle(
                style: TextStyle(color: ThemeColor.subtitle),
                child: Container(
                  height: 36,
                  child: Row(
                    children: [
                      Text(
                        (index + 1).toString(),
                        style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                      ),
                      if (containSerial)
                        GestureDetector(
                          onTap: () => logic.previewParagraphSerial(context, index),
                          behavior: HitTestBehavior.opaque,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(
                                width: 8,
                              ),
                              Container(
                                decoration: BoxDecoration(
                                    color: ThemeColor.COLOR_2D9AFF, borderRadius: BorderRadius.all(Radius.circular(4))),
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.symmetric(horizontal: 2.0, vertical: 4.0),
                                  child: Text(intlanguage("app100001701", "含序号"),
                                      style: const TextStyle(
                                          fontSize: 10, fontWeight: FontWeight.w400, color: Colors.white)),
                                ),
                              ),
                              Container(
                                  margin: const EdgeInsetsDirectional.symmetric(horizontal: 6.0),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(intlanguage("app100001664", "共\$段", param: [serialCount.toString()]),
                                          style: const TextStyle(
                                              fontSize: 13,
                                              fontWeight: FontWeight.w400,
                                              color: ThemeColor.COLOR_2D9AFF)),
                                      Container(
                                          margin: EdgeInsetsDirectional.only(start: 2.0),
                                          child: SvgIcon('assets/images/C1/arrow_right_blue.svg'))
                                    ],
                                  ))
                            ],
                          ),
                        ),
                      const Spacer(),
                      DefaultTextStyle.merge(
                        style: TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
                        child: GestureDetector(
                          onTap: () {
                            // 重置状态
                            // 状态变更为空
                            logic.paragraphStatus.value = ParagraphStatus.none;
                            // 键盘收起
                            editNode.unfocus();
                            logic.setParagraphRepeatCount(context, index);
                          },
                          behavior: HitTestBehavior.opaque,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                intlanguage('app100001475', '重复') + ': ',
                              ),
                              Text(
                                'x' + repeatCount.toString(),
                              ),
                              const SizedBox(
                                width: 4,
                              ),
                              const SvgIcon(
                                'assets/images/C1/unfold.svg',
                              )
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(
                        width: 3,
                      ),
                      ItemsPopUpWidget(
                        items: ParagraphOperate.values,
                        itemsSelectedChanged: (ItemsProtocol selected) {
                          if (selected == ParagraphOperate.copy) {
                            bool lastItem = false;
                            if (index == logic.templateData().values!.length - 1) {
                              lastItem = true;
                            }
                            logic.copyParagraph(index);
                            setState(() {});
                            if (lastItem) {
                              Future.delayed(const Duration(milliseconds: 100), () {
                                scrollParagraphListToBottom();
                              });
                            }
                          } else if (selected == ParagraphOperate.insert) {
                            bool lastItem = false;
                            if (index == logic.templateData().values!.length - 1) {
                              lastItem = true;
                            }
                            logic.insertParagraph(index);
                            setState(() {});
                            // if (lastItem) {
                            //   Future.delayed(const Duration(milliseconds: 100), () {
                            //     scrollParagraphListToBottom();
                            //   });
                            // }
                          } else {
                            logic.deleteParagraph(context, index, () {
                              setState(() {});
                            });
                          }
                        },
                        dropDown: true,
                        checkKeyboardStatus: true,
                        initKeyboardStatus: logic.paragraphStatus != ParagraphStatus.none,
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(12.0, 6.0, 0, 6.0),
                          child: const SvgIcon(
                            'assets/images/C1/ellipsis.svg',
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              netalWidget(index: index, value: value),
            ],
          );
        });
  }

  Widget netalWidget({required int index, required ValueTypeProtocol? value}) {
    String paragraphId = (value as CompositeValueType).id!;
    // 展示的上下左右盲区
    List<double> margin =
        logic.templateData().margin.map((e) => (e >= 0 ? e : 0) * TemplateDataTransform.dpRatio.toDouble()).toList();
    // 耗材"真实"宽高，宽 = 设置宽度，高 = 盲区高度 + 可打印高度
    double canvasWidth = logic.templateData().width * TemplateDataTransform.dpRatio;
    double canvasHeight = logic.templateData().height * TemplateDataTransform.dpRatio;
    // 获取段落单行元素信息
    ParagraphElementItem elementItem1 = getParagraphElementItem(index: index, value: value, columnIndex: 0);
    // 如果是双行，获取段落双行元素信息
    ParagraphElementItem? elementItem2 = logic.textFormat == TextFormat.doubleLine
        ? getParagraphElementItem(index: index, value: value, columnIndex: 1)
        : null;
    // 如果是自动长度，则宽 = 元素宽度中最大的 + 左右盲区
    if (logic.templateData().tubeFileSetting?.autoWidth == true) {
      canvasWidth = max(elementItem1.templateAdaptWidth, elementItem2?.templateAdaptWidth ?? 0.toDouble()) +
          margin[1] +
          margin[3];
      if (canvasWidth > TemplateDataTransform.adaptMaxWidthDp) {
        canvasWidth = TemplateDataTransform.adaptMaxWidthDp;
      }
    }
    // 是否溢出
    bool isOverFlow = elementItem1.overflow || elementItem2?.overflow == true;
    // if (isOverFlow) {
    //   itemHeightMap[index] = 36 + canvasHeight + 14 + 20;
    // } else {
    //   itemHeightMap[index] = 36 + canvasHeight + 20;
    // }
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          controller: logic.getParagraphScrollController(paragraphId),
          child: Container(
            child: GestureDetector(
              onTapUp: (TapUpDetails details) {
                var select = logic.selectedParagraph();
                if (select == null) {
                  return;
                }
                String selectParagraphId = select.item1.id!;
                if (selectParagraphId == value.id &&
                    logic.templateData().tubeFileSetting?.autoWidth == true &&
                    logic.templateData().tubeFileSetting?.line == 2) {
                  int selectColumnIndex = select.item2;
                  double dy = details.localPosition.dy;
                  int columnIndex;
                  if (dy > 0.5 * canvasHeight) {
                    columnIndex = 1;
                  } else {
                    columnIndex = 0;
                  }
                  if (columnIndex != selectColumnIndex) {
                    // 变更为编辑态
                    logic.paragraphStatus.value = ParagraphStatus.isEditing;
                    handleParagraphEditAction(index, value, columnIndex);
                    return;
                  }
                }
                // 状态变更为空
                logic.paragraphStatus.value = ParagraphStatus.none;
                // 键盘收起
                editNode.unfocus();
              },
              child: Stack(
                children: [
                  // 画布
                  Container(
                      width: canvasWidth,
                      height: canvasHeight,
                      color: logic.templateData().backgroundImage.isEmpty ? ThemeColor.background : null,
                      child: logic.templateData().backgroundImage.isEmpty
                          ? SizedBox.expand()
                          : CacheImageUtil().netCacheImage(
                              fit: BoxFit.fill,
                              imageUrl: logic.templateData().backgroundImage,
                              filterQuality: FilterQuality.high,
                              useOldImageOnUrlChange: true,
                              errorWidget: Container(color: ThemeColor.background))),
                  // 盲区
                  /*Positioned.fill(
                    child: Container(
                      padding: EdgeInsetsDirectional.fromSTEB(margin[3], /*margin[0]*/ 0, margin[1], /*margin[2]*/ 0),
                      child: DottedBorder(
                        strokeWidth: 1.5,
                        color: ThemeColor.COLOR_FF3B30,
                        child: SizedBox.expand(),
                      ),
                    ),
                  ),*/
                  // 单行
                  elementItem1.widget,
                  // 双行
                  if (elementItem2 != null) ...[
                    elementItem2.widget,
                    PositionedDirectional(
                      start: margin[3],
                      // top: margin[0] + 0.5 * (canvasHeight - (margin[0] + margin[2] + 1)),
                      top: 0.5 * canvasHeight + 1,
                      end: margin[1],
                      child: DottedLine(
                        color: ThemeColor.divider,
                      ),
                    )
                  ]
                ],
              ),
            ),
          ),
        ),
        isOverFlow
            ? Container(
                height: 20,
                padding: EdgeInsetsDirectional.only(start: 12, top: 4),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SvgIcon(
                      'assets/images/C1/c1_overFlow_warn.svg',
                    ),
                    const SizedBox(
                      width: 2,
                    ),
                    Text(
                      intlanguage('app100001534', '文字超出管长'),
                      style: TextStyle(color: ThemeColor.COLOR_F8473E, fontSize: 11, fontWeight: FontWeight.w400),
                    )
                  ],
                ),
              )
            : SizedBox(height: 20),
      ],
    );
  }

  ParagraphElementItem getParagraphElementItem(
      {required int index, required ValueTypeProtocol? value, required int columnIndex}) {
    // 获取渲染结果
    Tuple2<NetalImageResult?, BaseElement?> result =
        logic.generateElementImage(parentValue: value, columnIndex: columnIndex);
    // 图片信息
    NetalImageResult? imageResult = result.item1;
    // 元素样式信息
    BaseElement? element = result.item2;
    // 获取元素旋转信息
    num angle = element?.rotate ?? 0;
    // 获取元素的文本方向信息
    NetalTypesettingMode typesettingMode = (element as TextElement).typesettingMode;
    // 获取元素位置信息
    double x = element.x.toDouble() * TemplateDataTransform.dpRatio;
    double y = element.y.toDouble() * TemplateDataTransform.dpRatio;
    // px转换为dp宽高
    double width = TemplateDataTransform.px2dp(imageResult?.width ?? 0);
    double height = TemplateDataTransform.px2dp(imageResult?.height ?? 0);
    // 自动宽度时的宽度，区分元素的横排竖排
    double templateAdaptWidth = width;
    // 查看是否当前宽度是否溢出模版的安全宽度,区分元素的横排竖排
    bool isOverFlow = false;
    if (element.typesettingMode == NetalTypesettingMode.horizontal) {
      isOverFlow = element.value.isNotEmpty && TemplateDataTransform.px2mm(imageResult?.width ?? 0) > logic.safeWidth;
    } else if (element.typesettingMode == NetalTypesettingMode.vertical) {
      isOverFlow = element.value.isNotEmpty && TemplateDataTransform.px2mm(imageResult?.height ?? 0) > logic.safeWidth;
      templateAdaptWidth = height;
    }
    // 获取当前columnIndex下的element以及value
    CompositeValueType? compositeValueType = (value != null && value is CompositeValueType) ? value : null;
    ValueTypeProtocol? _value = compositeValueType?.valueObjects?[columnIndex];
    // 根据当前是否编辑态更新状态
    if (logic.paragraphStatus() == ParagraphStatus.isEditing && logic.selectedParagraphElement?.id == _value?.id) {
      inputIsOverFlow = isOverFlow;
    }
    // 选中元素事件
    ValueChanged<BuildContext> selectedAction = (BuildContext context) {
      // 获取当前logic中的选中
      String oldSelectParagraphId = logic.selectedParagraph()?.item1.id ?? '';
      // 获取当前选中
      String newSelectParagraphId = value?.id ?? '';
      // 状态变更为编辑选中态
      logic.paragraphStatus.value = ParagraphStatus.isEditingSelected;
      // 更新选中段落
      logic.selectedParagraph.value = Tuple3<ValueTypeProtocol, int, BuildContext>(value!, columnIndex, context);
      // 刷新段落
      logic.update([oldSelectParagraphId, newSelectParagraphId]);
      // 根据旋转的角度计算弹出的X轴偏移量
      double arrowDxOffset = angle == 0 ? 0 : 0;
      // 根据旋转的角度计算弹出的Y轴偏移量
      double arrowDyOffset = angle == 0 ? 0 : -width;
      // 展示字体操作属性
      showFontAttributes(
          context: context,
          typesettingMode: typesettingMode,
          arrowDxOffset: arrowDxOffset,
          arrowDyOffset: arrowDyOffset);
    };

    // 恢复编辑态
    ValueChanged<BuildContext> isEditingSelectedAction = (BuildContext context) {
      // 变更为编辑态
      logic.paragraphStatus.value = ParagraphStatus.isEditing;
    };

    // 双击手势事件
    ValueChanged<BuildContext> onDoubleTapAction = (BuildContext context) {
      handleParagraphEditAction(index, value!, columnIndex);
    };

    // 单击手势事件
    ValueChanged<BuildContext> onTapAction = (BuildContext context) {
      {
        if (logic.selectedParagraph() != null) {
          ValueTypeProtocol editValueTypeProtocol = logic.selectedParagraph()!.item1;
          int editColumnIndex = logic.selectedParagraph()!.item2;
          if (editValueTypeProtocol.id == value!.id && editColumnIndex == columnIndex) {
            // if (logic.paragraphStatus() == ParagraphStatus.isEditing) {
            //   // 展示悬浮事件
            //   selectedAction(context);
            // } else {
            //   // 恢复默认编辑态
            //   isEditingSelectedAction(context);
            // }
          } else {
            if (logic.paragraphStatus() == ParagraphStatus.isEditingSelected) {
              // 恢复默认编辑态
              isEditingSelectedAction(context);
            }
            Future.delayed(const Duration(milliseconds: 100), () {
              // 展示键盘编辑
              onDoubleTapAction(context);
            });
          }
        } else {
          // 展示键盘编辑
          onDoubleTapAction(context);
        }
      }
    };

    Widget contentWidget = StatefulBuilder(builder: (context, setState) {
      ValueTypeProtocol? selectValue = logic.selectedParagraph.value?.item1;
      int? selectColumnIndex = logic.selectedParagraph.value?.item2;
      Color borderColor;
      if (selectValue == value && selectColumnIndex == columnIndex) {
        if (isOverFlow) {
          borderColor = ThemeColor.COLOR_FF4C4C;
        } else {
          borderColor = ThemeColor.COLOR_2DCAFF;
        }
      } else {
        borderColor = Colors.transparent;
      }
      return AnimatedBuilder(
        animation: animation,
        builder: (BuildContext context, Widget? child) {
          return Transform.translate(offset: Offset(isOverFlow ? animation.value : 0, 0), child: child);
        },
        child: Transform.rotate(
          alignment: Alignment.center,
          angle: pi * angle / 180,
          child: Builder(builder: (context) {
            return GestureDetector(
              behavior: HitTestBehavior.opaque,
              onDoubleTap: () {
                onDoubleTapAction(context);
              },
              onTap: () {
                onTapAction(context);
              },
              child: DottedBorder(
                strokeWidth: 1.5,
                color: borderColor,
                dashPattern: [4, 4],
                padding: const EdgeInsets.all(0),
                child: Image.memory(
                  alignment: AlignmentDirectional.centerStart,
                  imageResult?.pixels ?? Uint8List(0),
                  width: width,
                  height: height,
                  scale: TemplateDataTransform.devicePixelRatio,
                  fit: BoxFit.none,
                  gaplessPlayback: true,
                ),
              ),
            );
          }),
        ),
      );
    });
    //四周的虚线框，所以start和top要减1.5
    Widget elementWidget = PositionedDirectional(start: x - 1.5, top: y - 1.5, child: contentWidget);
    return ParagraphElementItem(widget: elementWidget, overflow: isOverFlow, templateAdaptWidth: templateAdaptWidth);
  }
}

extension newParagraph on _C1EditPageState {
  Widget newParagraphWidget() {
    return GestureDetector(
      onTap: () {
        setState(() {
          logic.createParagraph();
        });
        Future.delayed(const Duration(milliseconds: 100), () {
          scrollParagraphListToBottom();
          if (logic.paragraphStatus != ParagraphStatus.none) {
            List<ValueTypeProtocol> values = logic.templateData().values!;
            int index = values.length - 1;
            ValueTypeProtocol value = values[index];
            handleParagraphEditAction(index, value, 0);
          }
        });
      },
      child: DottedBorder(
        borderType: BorderType.RRect,
        radius: const Radius.circular(9),
        padding: EdgeInsets.symmetric(vertical: 16),
        dashPattern: [4, 4],
        color: ThemeColor.COLOR_bfbfbf,
        child: Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SvgIcon('assets/images/C1/c1_new_paragraph.svg'),
              const SizedBox(
                width: 6,
              ),
              Text(
                intlanguage('app100001483', '新段落'),
                style: TextStyle(color: ThemeColor.brand, fontSize: 16, fontWeight: FontWeight.w600),
              )
            ],
          ),
        ),
      ),
    );
  }
}

extension bottomBar on _C1EditPageState {
  Widget buildBottomBar() {
    return Container(
        padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewPadding.bottom),
        decoration: BoxDecoration(
          color: ThemeColor.background, // 底色
          boxShadow: [
            BoxShadow(
              blurRadius: 5, //阴影范围
              spreadRadius: 0.1, //阴影浓度
              offset: Offset(0, -2.0),
              color: Colors.grey.withOpacity(0.15), //阴影颜色
            ),
          ],
        ),
        child: DefaultTextStyle(
          style: TextStyle(color: ThemeColor.title, fontSize: 15, fontWeight: FontWeight.w500),
          child: Row(mainAxisAlignment: MainAxisAlignment.spaceAround, children: [
            Expanded(
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () => logic.saveTemplate(context),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgIcon('assets/images/C1/bottom_bar_save.svg'),
                      SizedBox(
                        width: 8,
                      ),
                      Text(intlanguage("app00017", "保存"))
                    ],
                  ),
                ),
              ),
            ),
            Container(
              color: ThemeColor.border,
              width: 1,
              height: 17,
            ),
            Expanded(
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () async => logic.handlePrint(context),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgIcon('assets/images/C1/bottom_bar_print.svg'),
                      SizedBox(
                        width: 8,
                      ),
                      Text(
                        intlanguage("app00016", "打印"),
                      )
                    ],
                  ),
                ),
              ),
            ),
          ]),
        ));
  }
}

/// 键盘事件
extension KeyboardAction on _C1EditPageState {
  Widget quillEditor() {
    return QuillEditor(
      scrollController: quillEditorScrollController,
      focusNode: editNode,
      configurations: QuillEditorConfigurations(
          controller: quillController,
          elementOptions: const QuillEditorElementOptions(
            codeBlock: QuillEditorCodeBlockElementOptions(
              enableLineNumbers: true,
            ),
            orderedList: QuillEditorOrderedListElementOptions(),
            unorderedList: QuillEditorUnOrderedListElementOptions(
              useTextColorForDot: true,
            ),
          ),
          customStyles: const DefaultStyles(
            h1: DefaultTextBlockStyle(
              TextStyle(
                fontSize: 32,
                height: 1.15,
                fontWeight: FontWeight.w300,
              ),
              VerticalSpacing(16, 0),
              VerticalSpacing(0, 0),
              null,
            ),
            placeHolder: DefaultTextBlockStyle(
                TextStyle(color: ThemeColor.hint, fontSize: 16, fontWeight: FontWeight.w400),
                VerticalSpacing(0, 0),
                VerticalSpacing(0, 0),
                null),
            sizeSmall: TextStyle(fontSize: 9),
            subscript: TextStyle(
              fontFeatures: [FontFeature.subscripts()],
            ),
            superscript: TextStyle(
              fontFeatures: [FontFeature.superscripts()],
            ),
          ),
          autoFocus: true,
          scrollable: true,
          maxHeight: 40,
          placeholder: intlanguage('app100000972', '请输入'),
          textSelectionThemeData: Theme.of(context).textSelectionTheme.copyWith(cursorColor: Colors.red),
          embedBuilders: [
            SerialEmbedBuilder(serialBlockClick: (serialValue) async {
              var selected = logic.selectedParagraph();
              String paragraphId = selected!.item1.id!;
              int columnIndex = selected.item2;
              SerialValueType? serialValueType = await logic.editSerial(context, serialValue);
              if (serialValueType != null) {
                editSerialBlockEmbedWithValue(serialValueType);
              }
              if (logic.paragraphStatus == ParagraphStatus.none) {
                Future.delayed(const Duration(milliseconds: 200), () {
                  int index = logic.templateData().values!.indexWhere((e) => e.id == paragraphId);
                  ValueTypeProtocol value = logic.templateData().values![index];
                  handleParagraphEditAction(index, value, columnIndex);
                });
              }
            }),
            SymbolEmbedBuilder(),
          ],
          builder: (
            BuildContext context,
            QuillRawEditor rawEditor,
          ) {
            return Container(
              padding: EdgeInsets.symmetric(horizontal: 14, vertical: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(10)),
                color: ThemeColor.listBackground,
              ),
              child: rawEditor,
            );
          }),
    );
  }

  /// 自动长度编辑文本时scrollview自动滑动到可见视窗
  _autoWidthScroll(double dx) {
    String selectedParagraphId = (logic.selectedParagraph()!.item1 as CompositeValueType).id!;
    ScrollController scrollController = logic.getParagraphScrollController(selectedParagraphId);
    scrollController.jumpTo(scrollController.offset + dx);
  }

  // 符号
  Widget symbol() {
    return GestureDetector(
      onTap: () {
        // 光标不收起
        setState(() {
          quillController.readOnly = true;
          // 更新键盘活动展示状态
          logic.keyBoardActionStatus.value = KeyBoardActionStatus.symbol;
          // 焦点focus
          editNode.requestFocus();
        });
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgIcon('assets/images/C1/symbol.svg'),
          const SizedBox(
            height: 2,
          ),
          Text(
            intlanguage('app100001508', '符号'),
            style: TextStyle(color: ThemeColor.mainTitle, fontSize: 10, fontWeight: FontWeight.w400),
          )
        ],
      ),
    );
  }

  // 序号
  Widget serialNumber() {
    return Obx(() {
      bool allowInsertSerial = logic.allowInsertSerial();
      return GestureDetector(
        onTap: () async {
          if (allowInsertSerial) {
            logic.keyBoardActionStatus.value = KeyBoardActionStatus.serial;
            quillController.readOnly = false;
            var selected = logic.selectedParagraph();
            String paragraphId = selected!.item1.id!;
            int columnIndex = selected.item2;

            // 获取用户配置的序列号
            SerialValueType? serialValue = await logic.insertSerial(context);

            // 如果用户成功配置了序列号，将其添加到编辑器
            if (serialValue != null) {
              addSerialBlockEmbedWithValue(serialValue);
            }

            // 处理状态恢复
            if (logic.paragraphStatus == ParagraphStatus.none) {
              Future.delayed(const Duration(milliseconds: 200), () {
                int index = logic.templateData().values!.indexWhere((e) => e.id == paragraphId);
                ValueTypeProtocol value = logic.templateData().values![index];
                handleParagraphEditAction(index, value, columnIndex);
              });
            }
          }
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            allowInsertSerial
                ? SvgIcon('assets/images/C1/serial_number.svg')
                : SvgIcon('assets/images/C1/serial_number_disable.svg'),
            const SizedBox(
              height: 2,
            ),
            Text(
              intlanguage('app100001509', '序号'),
              style: TextStyle(
                  color: allowInsertSerial ? ThemeColor.mainTitle : ThemeColor.COLOR_D9D9D9,
                  fontSize: 10,
                  fontWeight: FontWeight.w400),
            )
          ],
        ),
      );
    });
  }

  // 符号
  Widget keyboard() {
    return GestureDetector(
      onTap: () {
        // 光标不收起
        setState(() {
          quillController.readOnly = false;
          // 更新键盘活动展示状态
          logic.keyBoardActionStatus.value = KeyBoardActionStatus.normal;
          // 焦点focus
          editNode.requestFocus();
        });
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgIcon('assets/images/C1/keyboard.svg'),
          const SizedBox(
            height: 2,
          ),
          Text(
            intlanguage('app100001706', '键盘'),
            style: TextStyle(color: ThemeColor.mainTitle, fontSize: 10, fontWeight: FontWeight.w400),
          )
        ],
      ),
    );
  }

  // 完成
  Widget done() {
    return GestureDetector(
      onTap: () {
        // 开启输入
        quillController.readOnly = false;
        // 状态置空
        logic.paragraphStatus.value = ParagraphStatus.none;
      },
      child: Text(
        intlanguage('app01031', '完成'),
        style: TextStyle(color: ThemeColor.title, fontSize: 15, fontWeight: FontWeight.w600),
      ),
    );
  }

  Widget keyboardAction() {
    return Column(
      children: [
        Obx(() {
          switch (logic.keyBoardActionStatus()) {
            case KeyBoardActionStatus.none:
            case KeyBoardActionStatus.normal:
              return const Spacer();
            case KeyBoardActionStatus.symbol:
              return Expanded(
                child: GestureDetector(
                  onTap: resetParagraphStatus,
                  child: SizedBox.expand(),
                  behavior: HitTestBehavior.translucent,
                ),
              );
            case KeyBoardActionStatus.serial:
              return const Spacer();
          }
        }),
        GetBuilder<C1EditLogic>(
            id: logic.selectedParagraphElement?.id,
            builder: (logic) {
              double fontSize = logic.selectTextFontSize?.toDouble() ?? logic.fontSizeConfigs.last.fontSize;
              FontSizeConfig fontSizeConfig = logic.getFontSizeConfig(fontSize);
              String fontTitle = (fontSizeConfig.title ?? '') +
                  (Application.isChineseLanguage ? intlanguage('app100001515', '号') : '');
              bool textBold = logic.selectTextBold ?? false;
              bool textUnderline = logic.selectTextUnderline ?? false;
              // 竖排文本不支持调整对齐方式，默认左对齐
              if ((logic.templateData().tubeFileSetting?.autoWidth ?? false) == true) {
                logic.selectTextAlignH = null;
              }
              NetalTextAlign? textAlignHorizontal = (logic.templateData().tubeFileSetting?.autoWidth ?? false) == true
                  ? NetalTextAlign.start
                  : logic.selectTextAlignH == true
                      ? NetalTextAlign.start
                      : NetalTextAlign.center;
              NetalTypesettingMode typesettingMode = logic.selectTextVerticalStyle == true
                  ? NetalTypesettingMode.vertical
                  : NetalTypesettingMode.horizontal;
              double itemWidth = 64;
              return Container(
                color: ThemeColor.background,
                height: 40,
                child: Row(
                  children: [
                    Flexible(
                      child: Builder(builder: (context) {
                        return GestureDetector(
                            onTap: () {
                              popoverShowing = true;
                              showPopover(
                                  context: context,
                                  bodyBuilder: (_) {
                                    return fontSizeWidget();
                                  },
                                  onTapDown: (TapDownDetails details) {
                                    popoverShowing = false;
                                  },
                                  arrowHeight: 10,
                                  arrowWidth: 16,
                                  arrowDxOffset: 0,
                                  arrowDyOffset: 0,
                                  backgroundColor: ThemeColor.title,
                                  barrierColor: Colors.transparent,
                                  radius: 10,
                                  transitionDuration: const Duration(milliseconds: 100),
                                  direction: PopoverDirection.top,
                                  transition: PopoverTransition.other,
                                  barrierDismissible: true,
                                  rootNavigator: false,
                                  onPop: () {
                                    popoverShowing = false;
                                  },
                                  popoverTransitionBuilder: (
                                    Animation<double> animation,
                                    Widget child,
                                  ) {
                                    bool isPopup = animation.status == AnimationStatus.forward;
                                    if (isPopup) {
                                      // 保持键盘的聚焦
                                      FocusScope.of(context).requestFocus(editNode);
                                    }
                                    return FadeTransition(
                                      opacity: CurvedAnimation(
                                        parent: animation,
                                        curve: Curves.easeOut,
                                      ),
                                      child: child,
                                    );
                                  });
                              ToNativeMethodChannel().sendTrackingToNative({
                                "track": "click",
                                "posCode": "129_437_425",
                                "ext": {"type": 1}
                              });
                            },
                            behavior: HitTestBehavior.opaque,
                            child: Container(
                                child: Center(
                                    child: Text(fontTitle,
                                        style:
                                            TextStyle(fontSize: 15, fontWeight: FontWeight.w400, color: Colors.black),
                                        textAlign: TextAlign.center))));
                      }),
                    ),
                    Padding(
                        padding: EdgeInsetsDirectional.symmetric(vertical: 12),
                        child: Container(
                          color: Color(0x173C3C43),
                          width: 0.5,
                        )),
                    Flexible(
                      child: GestureDetector(
                          onTap: () {
                            logic.updateAllParagraphTextAttribute(textBold: !textBold);
                            ToNativeMethodChannel().sendTrackingToNative({
                              "track": "click",
                              "posCode": "129_437_425",
                              "ext": {"type": 2}
                            });
                          },
                          behavior: HitTestBehavior.opaque,
                          child: Container(
                            child: Center(
                              child: textBold
                                  ? SvgIcon('assets/images/C1/c1_text_bold_selected.svg')
                                  : SvgIcon('assets/images/C1/c1_text_bold_normal.svg'),
                            ),
                          )),
                    ),
                    Padding(
                        padding: EdgeInsetsDirectional.symmetric(vertical: 12),
                        child: Container(
                          color: Color(0x173C3C43),
                          width: 0.5,
                        )),
                    Flexible(
                      child: GestureDetector(
                          onTap: () {
                            logic.updateAllParagraphTextAttribute(textUnderline: !textUnderline);
                            ToNativeMethodChannel().sendTrackingToNative({
                              "track": "click",
                              "posCode": "129_437_425",
                              "ext": {"type": 3}
                            });
                          },
                          behavior: HitTestBehavior.opaque,
                          child: Container(
                            child: Center(
                              child: textUnderline
                                  ? SvgIcon('assets/images/C1/c1_text_underline_selected.svg')
                                  : SvgIcon('assets/images/C1/c1_text_underline_normal.svg'),
                            ),
                          )),
                    ),
                    Padding(
                        padding: EdgeInsetsDirectional.symmetric(vertical: 12),
                        child: Container(
                          color: Color(0x173C3C43),
                          width: 0.5,
                        )),
                    Flexible(
                      child: GestureDetector(
                          onTap: () {
                            // 竖排文本默认不可点击，不处理
                            if (logic.selectTextAlignH == null) return;
                            if (textAlignHorizontal == NetalTextAlign.start) {
                              logic.updateAllParagraphTextAttribute(textAlignHorizontal: NetalTextAlign.center);
                              logic.fixLengthTextAlignHorizontal = NetalTextAlign.center;
                            } else {
                              logic.updateAllParagraphTextAttribute(textAlignHorizontal: NetalTextAlign.start);
                              logic.fixLengthTextAlignHorizontal = NetalTextAlign.start;
                            }
                            ToNativeMethodChannel().sendTrackingToNative({
                              "track": "click",
                              "posCode": "129_437_425",
                              "ext": {"type": 4}
                            });
                          },
                          behavior: HitTestBehavior.opaque,
                          child: Container(
                            child: Center(
                              child: logic.selectTextAlignH == null
                                  ? SvgIcon('assets/images/C1/c1_text_align_unavailable.svg')
                                  : logic.selectTextAlignH == true
                                      ? SvgIcon('assets/images/C1/c1_text_align_selected.svg')
                                      : SvgIcon('assets/images/C1/c1_text_align_normal.svg'),
                            ),
                          )),
                    ),
                    Padding(
                        padding: EdgeInsetsDirectional.symmetric(vertical: 12),
                        child: Container(
                          color: Color(0x173C3C43),
                          width: 0.5,
                        )),
                    Flexible(
                      child: GestureDetector(
                          onTap: () {
                            if (typesettingMode == NetalTypesettingMode.horizontal) {
                              logic.updateAllParagraphTextAttribute(typesettingMode: NetalTypesettingMode.vertical);
                            } else {
                              logic.updateAllParagraphTextAttribute(typesettingMode: NetalTypesettingMode.horizontal);
                            }
                            ToNativeMethodChannel().sendTrackingToNative({
                              "track": "click",
                              "posCode": "129_437_425",
                              "ext": {"type": 5}
                            });
                          },
                          behavior: HitTestBehavior.opaque,
                          child: Container(
                            child: Center(
                              child: typesettingMode != NetalTypesettingMode.horizontal
                                  ? SvgIcon('assets/images/C1/c1_text_vertical_style_selected.svg')
                                  : SvgIcon('assets/images/C1/c1_text_vertical_style_normal.svg'),
                            ),
                          )),
                    ),
                    Padding(
                        padding: EdgeInsetsDirectional.symmetric(vertical: 12),
                        child: Container(
                          color: Color(0x173C3C43),
                          width: 0.5,
                        )),
                    Flexible(
                      child: Builder(builder: (context) {
                        return GestureDetector(
                            onTap: () {
                              popoverShowing = true;
                              showPopover(
                                  context: context,
                                  bodyBuilder: (_) {
                                    return wordSpacing();
                                  },
                                  onTapDown: (TapDownDetails details) {
                                    popoverShowing = false;
                                  },
                                  arrowHeight: 10,
                                  arrowWidth: 16,
                                  arrowDxOffset: 0,
                                  arrowDyOffset: 0,
                                  backgroundColor: ThemeColor.title,
                                  barrierColor: Colors.transparent,
                                  transitionDuration: const Duration(milliseconds: 200),
                                  direction: PopoverDirection.top,
                                  transition: PopoverTransition.other,
                                  barrierDismissible: true,
                                  rootNavigator: false,
                                  onPop: () {
                                    popoverShowing = false;
                                  },
                                  popoverTransitionBuilder: (
                                    Animation<double> animation,
                                    Widget child,
                                  ) {
                                    bool isPopup = animation.status == AnimationStatus.forward;
                                    if (isPopup) {
                                      // 保持键盘的聚焦
                                      FocusScope.of(context).requestFocus(editNode);
                                    }
                                    return FadeTransition(
                                      opacity: CurvedAnimation(
                                        parent: animation,
                                        curve: Curves.easeOut,
                                      ),
                                      child: child,
                                    );
                                  });
                              ToNativeMethodChannel().sendTrackingToNative({
                                "track": "click",
                                "posCode": "129_437_425",
                                "ext": {"type": 6}
                              });
                            },
                            behavior: HitTestBehavior.opaque,
                            child: Container(
                                child: Center(
                                    child: Application.isChineseLanguage
                                        ? Text(intlanguage('app01011', '字距'),
                                            style: TextStyle(
                                                fontSize: 15, fontWeight: FontWeight.w400, color: Colors.black),
                                            textAlign: TextAlign.center)
                                        : SvgIcon('assets/images/C1/c1_text_word_space.svg'))));
                      }),
                    )
                  ],
                ),
              );
            }),
        Container(
          padding: EdgeInsetsDirectional.only(start: 12.0, end: 12.0, bottom: 4.0),
          color: ThemeColor.background,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(child: quillEditor()),
              // Expanded(child: inputTextField()),
              const SizedBox(
                width: 6,
              ),
              (logic.keyBoardActionStatus() == KeyBoardActionStatus.normal ||
                      logic.keyBoardActionStatus() == KeyBoardActionStatus.serial)
                  ? symbol()
                  : keyboard(),
              const SizedBox(
                width: 14,
              ),
              serialNumber(),
              const SizedBox(
                width: 12,
              ),
              done()
            ],
          ),
        ),
        Obx(() {
          switch (logic.keyBoardActionStatus()) {
            case KeyBoardActionStatus.none:
              return SizedBox.shrink();
            case KeyBoardActionStatus.normal:
              return SizedBox(
                height: MediaQuery.of(context).viewInsets.bottom,
              );
            case KeyBoardActionStatus.symbol:
              return SymbolSelector(
                onChooseSymbol: (item) {
                  String symbolCode = item?.unicode.toString() ?? '';
                  // 富文本编辑器添加符号节点
                  addSymbolNode(symbolCode: symbolCode);
                },
                deleteAction: () {
                  final index = quillController.selection.baseOffset;
                  quillController.replaceText(index - 1, 1, '', null, shouldNotifyListeners: false);
                  quillController.updateSelection(
                    TextSelection.collapsed(
                      offset: quillController.selection.extentOffset - 1,
                    ),
                    ChangeSource.local,
                  );
                },
              );
            case KeyBoardActionStatus.serial:
              return SizedBox(
                height: MediaQuery.of(context).viewInsets.bottom,
              );
          }
        }),
      ],
    );
  }
}

/// 字体调整
extension font on _C1EditPageState {
  /// 展示文本调整属性popover
  showFontAttributes(
      {required BuildContext context,
      required NetalTypesettingMode typesettingMode,
      double arrowDxOffset = -50,
      required double arrowDyOffset}) {
    showPopover(
        context: context,
        bodyBuilder: (_) {
          return Obx(() {
            // 段落状态为空时，未选中任意一个
            if (logic.paragraphStatus() == ParagraphStatus.none) {
              return SizedBox.shrink();
            }
            switch (logic.textAttributeStatus()) {
              case TextAttributeStatus.none:
                return fontAttribute(typesettingMode: typesettingMode);
              case TextAttributeStatus.fontSize:
                return fontSizeWidget();
              case TextAttributeStatus.fontSpacing:
                return wordSpacing();
              case TextAttributeStatus.verticalText:
                return SizedBox.shrink();
              case TextAttributeStatus.horizontalText:
                return SizedBox.shrink();
            }
          });
        },
        onTapDown: (TapDownDetails details) {
          // Get the RenderBox of the context
          final RenderBox renderBox = context.findRenderObject() as RenderBox;
          // Get the size of the RenderBox
          final Size size = renderBox.size;
          // Get the position of the tap relative to the RenderBox
          final Offset localPosition = renderBox.globalToLocal(details.globalPosition);

          // Check if the tap is within the bounds of the RenderBox
          if (localPosition.dx >= 0 &&
              localPosition.dx <= size.width &&
              localPosition.dy >= 0 &&
              localPosition.dy <= size.height) {
            // The tap is within the context's clickable area
            print('Tap is within the context\'s bounds');
            // 状态变更为编辑中
            logic.paragraphStatus.value = ParagraphStatus.isEditing;
            // 更新富文本编辑器值
            updateQuillController();
          } else {
            // 状态变更为空
            logic.paragraphStatus.value = ParagraphStatus.none;
            // 键盘收起
            editNode.unfocus();
          }
        },
        arrowHeight: 10,
        arrowWidth: 16,
        arrowDxOffset: arrowDxOffset,
        arrowDyOffset: arrowDyOffset,
        backgroundColor: ThemeColor.title,
        barrierColor: Colors.transparent,
        transitionDuration: const Duration(milliseconds: 200),
        direction: PopoverDirection.top,
        transition: PopoverTransition.other,
        barrierDismissible: false,
        rootNavigator: false,
        popoverTransitionBuilder: (
          Animation<double> animation,
          Widget child,
        ) {
          bool isPopup = animation.status == AnimationStatus.forward;
          if (isPopup) {
            // 保持键盘的聚焦
            FocusScope.of(context).requestFocus(editNode);
          }
          return FadeTransition(
            opacity: CurvedAnimation(
              parent: animation,
              curve: Curves.easeOut,
            ),
            child: child,
          );
        });
  }

  Widget fontAttribute({required NetalTypesettingMode typesettingMode}) {
    Widget item(String text, TextAttributeStatus textAttribute) {
      return GestureDetector(
        onTap: () {
          // 优先处理竖排文字｜｜横排文字，这两者无下一级UI展示
          if (textAttribute == TextAttributeStatus.verticalText ||
              textAttribute == TextAttributeStatus.horizontalText) {
            NetalTypesettingMode _typesettingMode;
            switch (textAttribute) {
              case TextAttributeStatus.verticalText:
                _typesettingMode = NetalTypesettingMode.vertical;
                break;
              case TextAttributeStatus.horizontalText:
                _typesettingMode = NetalTypesettingMode.horizontal;
                break;
              default:
                _typesettingMode = NetalTypesettingMode.horizontal;
                break;
            }
            // 更新文本为竖排||横排
            logic.updateCurrentParagraphTextAttribute(typesettingMode: _typesettingMode);
            // 退出文本属性设置
            Navigator.of(context).pop();
            // 更新段落状态
            // 状态变更为编辑中
            logic.paragraphStatus.value = ParagraphStatus.isEditing;
          } else {
            logic.textAttributeStatus.value = textAttribute;
          }
        },
        child: Padding(
          padding: const EdgeInsetsDirectional.symmetric(horizontal: 18, vertical: 8),
          child: Text(
            text,
          ),
        ),
      );
    }

    Widget typesettingModeWidget() {
      switch (typesettingMode) {
        case NetalTypesettingMode.horizontal:
          return item(intlanguage('app100001514', '竖排文字'), TextAttributeStatus.verticalText);
        case NetalTypesettingMode.vertical:
          return item(intlanguage('app100001533', '横排文字'), TextAttributeStatus.horizontalText);
        case NetalTypesettingMode.arc:
          return SizedBox.shrink();
      }
    }

    return Container(
      color: ThemeColor.title,
      child: DefaultTextStyle(
        style: TextStyle(color: ThemeColor.background, fontSize: 13, fontWeight: FontWeight.w400),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            item(Application.isChineseLanguage ? intlanguage('app01009', '字号') : '', TextAttributeStatus.fontSize),
            Container(
              width: 1,
              height: 40,
              color: ThemeColor.COLOR_616161,
            ),
            item(intlanguage('app01011', '字距'), TextAttributeStatus.fontSpacing),
            Container(
              width: 1,
              height: 40,
              color: ThemeColor.COLOR_616161,
            ),
            // 文本方向
            typesettingModeWidget(),
          ],
        ),
      ),
    );
  }

  Widget fontSizeWidget() {
    return GroupStepWidget<FontSizeConfig>(
      initValue: logic.selectedParagraphFontSizeConfig,
      values: logic.fontSizeConfigs,
      throttleSubtractCallback: (fontSizeConfig) {
        logic.updateAllParagraphTextAttribute(fontSize: fontSizeConfig);
      },
      throttleAddCallback: (fontSizeConfig) {
        logic.updateAllParagraphTextAttribute(fontSize: fontSizeConfig);
      },
      widgetBuilder: (fontSizeConfig) {
        return Text(
          (fontSizeConfig?.title ?? '') + (Application.isChineseLanguage ? intlanguage('app100001515', '号') : ''),
          style: TextStyle(color: ThemeColor.background, fontSize: 13, fontWeight: FontWeight.w400),
        );
      },
    );
  }

  Widget wordSpacing() {
    return NumStepWidget(
      max: 5.0,
      min: -5.0,
      stepValue: 0.1,
      value: logic.selectedElement?.letterSpacing.toDouble() ?? 0,
      valueChanged: (value) {
        logic.updateAllParagraphTextAttribute(letterSpacing: value);
      },
    );
  }

  List<String> _getAllVisibleParagraphIds() {
    double startScroll = paragraphListScrollController.position.pixels;
    double endScroll = paragraphListScrollController.position.viewportDimension;
    List<String> ids = [];
    List<ValueTypeProtocol> values = logic.templateData().values!;
    for (int i = 0; i < values.length; i++) {
      double itemStart = itemHeight * i;
      double itemEnd = itemStart + itemHeight - 12;
      if (itemStart < endScroll && itemEnd > startScroll) {
        ids.add(values[i].id!);
      }
    }
    return ids;
  }
}

extension Serial on _C1EditPageState {
  /// 添加序列号BlockEmbed，使用指定的序列号值
  addSerialBlockEmbedWithValue(SerialValueType serialValue) {
    final Map<String, dynamic> serialJson = serialValue.toJson();
    final block = BlockEmbed(SerialBlockEmbed.serialType, SerialBlockEmbed(jsonEncode(serialJson)).toJsonString());
    final index = quillController.selection.baseOffset;
    final length = quillController.selection.extentOffset - index;
    quillController.replaceText(index, length, block, null, shouldNotifyListeners: false);
    quillController.updateSelection(
      TextSelection.collapsed(
        offset: quillController.selection.extentOffset + 1,
      ),
      ChangeSource.local,
    );
  }

  /// 编辑序列号Block嵌入
  /// 根据传入的序列号值(serialValue)找到对应的序列号嵌入并替换为新值
  /// @param serialValue 新的序列号值对象
  editSerialBlockEmbedWithValue(SerialValueType serialValue) {
    // 创建包含更新后序列号数据的Block嵌入
    final Map<String, dynamic> serialJson = serialValue.toJson();
    final newBlock = BlockEmbed(SerialBlockEmbed.serialType, SerialBlockEmbed(jsonEncode(serialJson)).toJsonString());

    // 获取当前文档的Delta结构
    final document = quillController.document;
    final delta = document.toDelta();

    // 在Delta操作列表中查找匹配ID的序列号嵌入
    int offset = 0;
    int? targetOffset;

    for (final operation in delta.operations) {
      // 只检查插入操作中的Map类型数据(embed对象)
      if (operation.isInsert && operation.data is Map) {
        final Map data = operation.data as Map;
        // 检查是否为序列号类型的嵌入
        if (data.containsKey(SerialBlockEmbed.serialType)) {
          try {
            // 解析嵌入数据的多层结构
            String embedData = data[SerialBlockEmbed.serialType];
            Map<String, dynamic> serialEmbedData = jsonDecode(embedData);
            Map<String, dynamic> serialData = jsonDecode(serialEmbedData[SerialBlockEmbed.serialType]);
            SerialValueType existingSerial = SerialValueType.fromJson(serialData);

            // 通过ID匹配确定是否为目标序列号
            if (existingSerial.id == serialValue.id) {
              targetOffset = offset;
              break;
            }
          } catch (e) {
            // 解析错误时跳过当前操作
            print('序列号解析错误: $e');
          }
        }
      }

      // 累加操作长度计算下一个操作的偏移量
      offset += operation.length ?? 0;
    }

    // 如果找到目标序列号嵌入，则执行替换
    if (targetOffset != null) {
      // // 修改序列号嵌入替换逻辑：先插入后删除
      // // 1. 先在目标位置之后插入新的序列号嵌入
      // quillController.replaceText(
      //     targetOffset + 1, // 在目标位置后插入
      //     0, // 在目标位置后插入，长度为0
      //     newBlock,
      //     null, // 更新后将光标定位在新嵌入后
      //     shouldNotifyListeners: false // 不通知UI更新，避免闪烁
      //     );
      //
      // // 2. 删除原有的序列号嵌入
      // quillController.replaceText(
      //     targetOffset,
      //     1, // 删除1个字符长度的嵌入
      //     '', // 用空字符串替换，相当于删除
      //     null,
      //     shouldNotifyListeners: false // 不通知UI更新
      //     );

      // quillController.document.delete(targetOffset, 1);
      Delta delta = Delta()
        ..retain(targetOffset)
        ..delete(1);
      quillController.document.compose(delta, ChangeSource.local);
      quillController.replaceText(targetOffset, 0, newBlock, null, shouldNotifyListeners: false);
      quillController.updateSelection(
        TextSelection.collapsed(
          offset: targetOffset + 1,
        ),
        ChangeSource.local,
      );
      print('已更新序列号嵌入: ID=${serialValue.id}, 位置=$targetOffset');
    } else {
      // 未找到目标时的备选方案：使用当前光标位置
      final currentIndex = quillController.selection.baseOffset;
      print('未找到目标序列号嵌入(ID=${serialValue.id})，使用当前光标位置: $currentIndex');

      // 在当前位置插入新的序列号嵌入
      quillController.replaceText(
          currentIndex,
          1, // 假设当前位置有内容需要替换
          newBlock,
          TextSelection.collapsed(offset: currentIndex + 1),
          shouldNotifyListeners: false);
    }
  }
}

extension Symobl on _C1EditPageState {
  /// 添加序列号BlockEmbed
  addSymbolNode({required String symbolCode, bool shouldNotifyListeners = true}) {
    final block = BlockEmbed(SymbolBlockEmbed.symbolType, SymbolBlockEmbed(symbolCode).toJsonString());
    final index = quillController.selection.baseOffset;
    final length = quillController.selection.extentOffset - index;
    quillController.replaceText(index, length, block, null, shouldNotifyListeners: shouldNotifyListeners);
    quillController.updateSelection(
      TextSelection.collapsed(
        offset: quillController.selection.extentOffset + 1,
      ),
      ChangeSource.local,
    );
  }
}

extension quill on _C1EditPageState {
  /// 更新QuillController文本值
  updateQuillController() {
    // 设置标识符正在更新值，开始更新
    isUpdatingQuillController = true;
    // 更新文本框控制器输入值
    // 先清空
    quillController.replaceText(
        0, quillController.plainTextEditingValue.text.length - 1, '', const TextSelection.collapsed(offset: 0),
        shouldNotifyListeners: false);

    // 获取段落数据
    CompositeValueType valueTypeParagraph = logic.selectedParagraph()!.item1 as CompositeValueType;
    int columnIndex = logic.selectedParagraph()!.item2;
    CompositeValueType valueTypeColumn = valueTypeParagraph.valueObjects![columnIndex] as CompositeValueType;
    List<ValueTypeProtocol> values = valueTypeColumn.valueObjects!;

    // 富文本编辑器添加文本元素
    for (var valueType in values) {
      if (valueType is TextValueType) {
        String textValue = valueType.value ?? "";
        if (textValue.isNotEmpty) {
          // 处理特殊字符
          if (textValue.length == 1 && textValue.codeUnitAt(0) >= 57344) {
            addSymbolNode(symbolCode: textValue.codeUnitAt(0).toString(), shouldNotifyListeners: false);
          } else {
            // 处理普通文本
            var unicodeValues = textValue.runes.toList();
            for (var entry in unicodeValues.asMap().entries) {
              var unicode = entry.value;
              String unicodeString = String.fromCharCode(unicode);
              if (unicode >= 57344) {
                // 自定义emoji字符
                addSymbolNode(symbolCode: unicode.toString(), shouldNotifyListeners: false);
              } else {
                // 添加文本到富文本编辑器中
                if (unicodeString.isNotEmpty) {
                  final index = quillController.selection.baseOffset;
                  final length = quillController.selection.extentOffset - index;
                  quillController.replaceText(index, length, unicodeString,
                      TextSelection.collapsed(offset: quillController.selection.extentOffset + 1),
                      shouldNotifyListeners: false);
                }
              }
            }
          }
        }
      } else if (valueType is SerialValueType) {
        // 序列号处理 - 使用新的方法传递序列号值
        addSerialBlockEmbedWithValue(valueType);
      }
    }

    // 默认未溢出
    inputIsOverFlow = false;
    // 设置标识符结束更新值,结束更新
    isUpdatingQuillController = false;
    // 更新光标发送通知
    quillController.updateSelection(quillController.selection, ChangeSource.local);
  }
}
