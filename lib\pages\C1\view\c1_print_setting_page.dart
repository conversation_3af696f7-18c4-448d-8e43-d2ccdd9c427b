import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:text/pages/C1/common/add_subtract_text_group.dart';
import 'package:text/pages/C1/common/attr_icon_button.dart';
import 'package:text/pages/C1/common/items_selector_popmenu_widget.dart';
import 'package:text/pages/C1/controller/c1_print_setting_logic.dart';
import 'package:text/pages/C1/model/define_model.dart';
import 'package:text/pages/C1/view/group_style_widget.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';

class C1PrintSettingPage extends StatefulWidget {
  final TemplateData templateData;

  //管材类型：1-线管，2-热缩管
  final int tubeType;

  //管材规格：线管-截面积，热缩管-直径
  final double tubeSpecs;
  final TextFormat textFormat;

  const C1PrintSettingPage(this.templateData, this.tubeType, this.tubeSpecs, this.textFormat, {super.key});

  @override
  State<StatefulWidget> createState() => _C1PrintSettingPageState();
}

class _C1PrintSettingPageState extends State<C1PrintSettingPage> {
  late BuildContext Function() getBuildContext;
  late C1PrintSettingLogic logic = C1PrintSettingLogic(
      templateData: widget.templateData,
      tubeType: widget.tubeType,
      tubeSpecs: widget.tubeSpecs,
      textFormat: widget.textFormat,
      getBuildContext: getBuildContext);
  final FocusNode focusNodeParagraphStart = FocusNode();
  final FocusNode focusNodeParagraphEnd = FocusNode();
  late TextEditingController textControllerParagraphStart =
      TextEditingController(text: logic.paragraphStart().toString());
  late TextEditingController textControllerParagraphEnd = TextEditingController(text: logic.paragraphEnd().toString());
  late TextEditingController textControllerPrintCount = TextEditingController(text: logic.printCount().toString());
  late ParagraphStartInputFormatter paragraphStartInputFormatter = ParagraphStartInputFormatter(logic);
  late ParagraphEndInputFormatter paragraphEndInputFormatter = ParagraphEndInputFormatter(logic);
  PrintCountInputFormatter printCountInputFormatter = PrintCountInputFormatter();

  @override
  void initState() {
    super.initState();
    getBuildContext = () {
      return context;
    };
    Get.put<C1PrintSettingLogic>(logic);
    focusNodeParagraphStart.addListener(() {
      if (!focusNodeParagraphStart.hasFocus) {
        if (textControllerParagraphStart.text.isEmpty) {
          textControllerParagraphStart.text = "1";
        }
      }
    });
    focusNodeParagraphEnd.addListener(() {
      if (!focusNodeParagraphEnd.hasFocus) {
        if (textControllerParagraphEnd.text.isEmpty) {
          textControllerParagraphEnd.text = logic.paragraphCount.toString();
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    double height = MediaQuery.of(context).size.height -
        MediaQuery.of(context).viewPadding.top -
        MediaQuery.of(context).viewPadding.bottom -
        52;
    return Container(
      height: height,
      decoration: const BoxDecoration(
        color: ThemeColor.COLOR_F5F5F5,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [barWidget(), deviceType(), printActionSetting(), cutSetting(), c1Print()],
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    textControllerPrintCount.dispose();
    Get.delete<C1PrintSettingLogic>();
  }
}

extension Bar on _C1PrintSettingPageState {
  Widget barWidget() {
    return Container(
      color: ThemeColor.WHITE,
      height: 46,
      child: Row(
        children: [
          Container(
            padding: EdgeInsetsDirectional.only(start: 16),
            width: 80,
            child: Align(
              alignment: AlignmentDirectional.centerStart,
              child: GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Text(intlanguage('app01584', '关闭'),
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: ThemeColor.COLOR_262626)),
              ),
            ),
          ),
          Expanded(
              child: Center(
            child: Text(intlanguage('app00019', '打印设置'),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
                style: TextStyle(fontSize: 17, fontWeight: FontWeight.w600, color: ThemeColor.COLOR_262626)),
          )),
          SizedBox(
            width: 80,
          )
        ],
      ),
    );
  }
}

extension C1Device on _C1PrintSettingPageState {
  // 设备型号
  Widget deviceType() {
    return GroupStyleWidget(
        margin: EdgeInsetsDirectional.only(start: 16, top: 12, end: 16),
        padding: EdgeInsetsDirectional.symmetric(horizontal: 16),
        child: Container(
          height: 72,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                  children: [
                    Container(
                      child: Text(
                        intlanguage('app00272', '设备型号'),
                        style: const TextStyle(color: ThemeColor.title, fontSize: 15, fontWeight: FontWeight.w400),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Spacer(),
                    GestureDetector(
                        onTap: logic.toDeviceConnectPage,
                        behavior: HitTestBehavior.opaque,
                        child: Obx(() {
                          return Container(
                            height: 32,
                            child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SvgIcon(
                                    logic.deviceConnected.value
                                        ? 'assets/images/C1/bluetooth_connected.svg'
                                        : 'assets/images/C1/bluetooth_unconnected.svg',
                                    side: 16,
                                  ),
                                  Text(
                                    logic.deviceConnected.value ? 'C1' : intlanguage("app00190", "未连接"),
                                    style: const TextStyle(
                                        color: ThemeColor.subtitle, fontSize: 15, fontWeight: FontWeight.w400),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  SizedBox(
                                    width: 4,
                                  ),
                                  const SvgIcon(
                                    'assets/images/canvas/arrow_right_gray.svg',
                                    side: 16,
                                    matchTextDirection: true,
                                  )
                                ],
                              ),
                          );
                        }))
                  ]
                ),
              SizedBox(height: 4),
              GestureDetector(
                onTap: () async => logic.toOffsetCalibrationPage(context),
                behavior: HitTestBehavior.opaque,
                child: Container(
                  padding: EdgeInsetsDirectional.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(color: ThemeColor.COLOR_F7F7F7, borderRadius: BorderRadius.circular(6)),
                  child: Text(
                      intlanguage('app00977', '偏移校准'),
                      style:
                      const TextStyle(color: Color(0x993C3C43), fontSize: 11, fontWeight: FontWeight.w500),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                ),
              )
            ],
          )
        ));
  }
}

extension C1PrintActionSetting on _C1PrintSettingPageState {
  //打印行为相关设置
  Widget printActionSetting() {
    return GroupStyleWidget(
        margin: EdgeInsetsDirectional.only(start: 16, top: 12, end: 16),
        padding: EdgeInsetsDirectional.symmetric(horizontal: 16, vertical: 0),
        child: Obx(() {
          List<Widget> children;
          if (logic.expandMore.value) {
            children = [
              _printParagraph(),
              const Divider(height: 0.5),
              _printCount(),
              const Divider(height: 0.5),
              _printDensity(),
              const Divider(height: 0.5),
              _paragraphCompensation(),
              const Divider(height: 0.5),
              _repeatType()
            ];
          } else {
            children = [
              _printParagraph(),
              const Divider(height: 0.5),
              _printCount(),
              const Divider(height: 0.5),
              _printActionMore()
            ];
          }
          return Column(
            children: children,
          );
        }));
  }

  //打印段落
  Widget _printParagraph() {
    return Container(
      height: 44,
      child: Center(
        child: Row(
          children: [
            Expanded(
              child: Text(
                intlanguage('app100001485', '打印段落'),
                style: const TextStyle(color: ThemeColor.title, fontSize: 15, fontWeight: FontWeight.w400),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            SizedBox(width: 6),
            Container(
                width: 53,
                height: 32,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.05), borderRadius: BorderRadius.all(Radius.circular(8.0))),
                child: TextField(
                  style: const TextStyle(color: ThemeColor.title, fontSize: 14, fontWeight: FontWeight.w400),
                  textAlign: TextAlign.center,
                  cursorColor: const Color(0xFFFB4B42),
                  decoration: null,
                  controller: textControllerParagraphStart,
                  focusNode: focusNodeParagraphStart,
                  inputFormatters: [paragraphStartInputFormatter],
                  keyboardType: TextInputType.number,
                  onChanged: (text) {
                    try {
                      int num = int.parse(text);
                      if (num <= logic.paragraphEnd.value) {
                        logic.paragraphStart.value = num;
                      }
                    } catch (e) {}
                  },
                  onTap: () {
                    int length = textControllerParagraphStart.text.length;
                    if (length > 0) {
                      textControllerParagraphStart.selection = TextSelection(baseOffset: 0, extentOffset: length);
                    }
                  },
                )),
            Container(
              margin: EdgeInsetsDirectional.symmetric(horizontal: 6),
              width: 12,
              height: 1,
              color: ThemeColor.COLOR_FFBFBFBF,
            ),
            Container(
                width: 53,
                height: 32,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.05), borderRadius: BorderRadius.all(Radius.circular(8.0))),
                child: TextField(
                  style: const TextStyle(color: ThemeColor.title, fontSize: 14, fontWeight: FontWeight.w400),
                  textAlign: TextAlign.center,
                  cursorColor: const Color(0xFFFB4B42),
                  decoration: null,
                  controller: textControllerParagraphEnd,
                  focusNode: focusNodeParagraphEnd,
                  inputFormatters: [paragraphEndInputFormatter],
                  keyboardType: TextInputType.number,
                  onChanged: (text) {
                    try {
                      int num = int.parse(text);
                      if (num >= logic.paragraphStart.value && num <= logic.paragraphCount) {
                        logic.paragraphEnd.value = num;
                      }
                    } catch (e) {}
                  },
                  onTap: () {
                    int length = textControllerParagraphEnd.text.length;
                    if (length > 0) {
                      textControllerParagraphEnd.selection = TextSelection(baseOffset: 0, extentOffset: length);
                    }
                  },
                )),
          ],
        ),
      ),
    );
  }

  //打印份数
  Widget _printCount() {
    return Container(
      height: 44,
      child: Center(
        child: Row(
          children: [
            Expanded(
              child: Text(
                intlanguage('app01103', '打印份数'),
                style: const TextStyle(color: ThemeColor.title, fontSize: 15, fontWeight: FontWeight.w400),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            SizedBox(width: 6),
            AddSubTractTextGroup(
              style: const TextStyle(color: ThemeColor.title, fontSize: 14, fontWeight: FontWeight.w400),
              maxLength: 4,
              minValue: 1,
              maxValue: 3000,
              defaultValue: 1,
              textEditingController: textControllerPrintCount,
              inputFormatters: [printCountInputFormatter],
              onChanged: (num) {
                logic.printCount.value = num.toInt();
              },
            )
          ],
        ),
      ),
    );
  }

  //更多打印设置
  Widget _printActionMore() {
    String moreText;
    if (logic.repeatType.value == RepeatType.repeatPriority) {
      moreText = intlanguage('app100001487', '浓度3，段落补偿0mm，重复优先',
          param: [logic.printDensity.value.toString(), logic.paragraphCompensation.value.toString()]);
    } else {
      moreText = intlanguage('app100001488', '浓度3，段落补偿0mm，序号优先',
          param: [logic.printDensity.value.toString(), logic.paragraphCompensation.value.toString()]);
    }
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => logic.expandMore.value = true,
      child: Container(
        height: 60,
        child: Center(
          child: Row(
            children: [
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      intlanguage('app100001486', '更多打印设置'),
                      style: const TextStyle(color: ThemeColor.title, fontSize: 15, fontWeight: FontWeight.w400),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      moreText,
                      style: const TextStyle(color: ThemeColor.COLOR_999999, fontSize: 13, fontWeight: FontWeight.w400),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    )
                  ],
                ),
              ),
              SvgIcon(
                'assets/images/C1/arrow_down_gray.svg',
              )
            ],
          ),
        ),
      ),
    );
  }

  //打印浓度
  Widget _printDensity() {
    return Obx(() {
      return Container(
        height: 44,
        child: Center(
          child: Row(
            children: [
              Expanded(
                child: Text(
                  intlanguage('app00488', '打印浓度'),
                  style: const TextStyle(color: ThemeColor.title, fontSize: 15, fontWeight: FontWeight.w400),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(width: 6),
              logic.printDensity > logic.printDensityMin
                  ? AttrIconButton(
                      SvgIcon('assets/images/C1/text_adjust_minus.svg'),
                      width: 36,
                      height: 32,
                      onTap: logic.subtractDensity,
                    )
                  : AttrIconButton(
                      SvgIcon('assets/images/C1/text_adjust_minus_disable.svg'),
                      width: 36,
                      height: 32,
                    ),
              Container(
                width: 61,
                child: Center(
                  child: Text(
                    logic.printDensity.value.toString(),
                    style: const TextStyle(color: ThemeColor.title, fontSize: 14, fontWeight: FontWeight.w400),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              logic.printDensity < logic.printDensityMax
                  ? AttrIconButton(
                      SvgIcon('assets/images/C1/text_adjust_add.svg'),
                      width: 36,
                      height: 32,
                      onTap: logic.addDensity,
                    )
                  : AttrIconButton(
                      SvgIcon('assets/images/C1/text_adjust_add_disable.svg'),
                      width: 36,
                      height: 32,
                    )
            ],
          ),
        ),
      );
    });
  }

  //段落补偿
  Widget _paragraphCompensation() {
    return Obx(() {
      return Container(
        height: 44,
        child: Center(
          child: Row(
            children: [
              Expanded(
                child: Text(
                  intlanguage('app100001489', '段落补偿(mm)'),
                  style: const TextStyle(color: ThemeColor.title, fontSize: 15, fontWeight: FontWeight.w400),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(width: 6),
              logic.paragraphCompensation > 0
                  ? AttrIconButton(
                      SvgIcon('assets/images/C1/text_adjust_minus.svg'),
                      width: 36,
                      height: 32,
                      onTap: logic.subtractParagraphCompensation,
                    )
                  : AttrIconButton(
                      SvgIcon('assets/images/C1/text_adjust_minus_disable.svg'),
                      width: 36,
                      height: 32,
                    ),
              Container(
                width: 61,
                child: Center(
                  child: Text(
                    logic.paragraphCompensation.value.toString(),
                    style: const TextStyle(color: ThemeColor.title, fontSize: 14, fontWeight: FontWeight.w400),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              logic.paragraphCompensation < 10
                  ? AttrIconButton(
                      SvgIcon('assets/images/C1/text_adjust_add.svg'),
                      width: 36,
                      height: 32,
                      onTap: logic.addParagraphCompensation,
                    )
                  : AttrIconButton(
                      SvgIcon('assets/images/C1/text_adjust_add_disable.svg'),
                      width: 36,
                      height: 32,
                    )
            ],
          ),
        ),
      );
    });
  }

  //打印次序：重复优先、序号优先
  Widget _repeatType() {
    return Container(
      height: 44,
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: Text(
                intlanguage('app100001490', '打印次序'),
                style: const TextStyle(color: ThemeColor.title, fontSize: 15, fontWeight: FontWeight.w400),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Flexible(
              child: ItemsSelectorPopUpWidget(
                  popHeight: 100,
                  anchorEdgeInsets: EdgeInsetsDirectional.zero,
                  items: RepeatType.values.map((e) => e.showText).toList(),
                  initializeIndex: RepeatType.values.indexOf(logic.repeatType.value),
                  itemsSelectedChanged: (int selectedIndex) {
                    logic.repeatType.value = RepeatType.values[selectedIndex];
                  },
                ),
            ),
          ],
        ),
      ),
    );
  }
}

extension C1CutSetting on _C1PrintSettingPageState {
  Widget cutSetting() {
    return GroupStyleWidget(
        margin: EdgeInsetsDirectional.only(start: 16, top: 12, end: 16),
        padding: EdgeInsetsDirectional.symmetric(horizontal: 16, vertical: 0),
        child: Obx(() {
          List<Widget> children;
          if (logic.cutType.value == CutType.halfCut) {
            children = [_cutType(), const Divider(height: 0.5), _cutLength()];
          } else {
            children = [_cutType(), const Divider(height: 0.5), _printDivider()];
          }
          return Column(
            children: children,
          );
        }));
  }

  //切割方式
  Widget _cutType() {
    return Container(
      height: 44,
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: Text(
                intlanguage('app100001493', '切割方式'),
                style: const TextStyle(color: ThemeColor.title, fontSize: 15, fontWeight: FontWeight.w400),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Flexible(
              child: ItemsSelectorPopUpWidget(
                popHeight: 100,
                anchorEdgeInsets: EdgeInsetsDirectional.zero,
                items: CutType.values.map((e) => e.showText).toList(),
                initializeIndex: CutType.values.indexOf(logic.cutType.value),
                itemsSelectedChanged: (int selectedIndex) {
                  logic.cutType.value = CutType.values[selectedIndex];
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  //切割深度
  Widget _cutLength() {
    return Container(
      height: 88,
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              intlanguage('app100001496', '切割深度'),
              style: const TextStyle(color: ThemeColor.title, fontSize: 15, fontWeight: FontWeight.w400),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            Row(
              children: [
                Container(
                    child: Text(
                      intlanguage('app100000775', '浅'),
                      style: const TextStyle(color: ThemeColor.COLOR_595959, fontSize: 12, fontWeight: FontWeight.w300),
                    )),
                Expanded(
                    child: Slider(
                  value: logic.cutDepth.value.toDouble(),
                  label: logic.cutDepth.value.toString(),
                  onChanged: (progress) {
                    logic.cutDepth.value = progress.toInt();
                  },
                  min: -3,
                  max: 3,
                  divisions: 6,
                  inactiveColor: Colors.black.withOpacity(0.05),
                  activeColor: ThemeColor.brand,
                )),
                Container(
                    child: Text(
                      intlanguage('app100000776', '深'),
                      style: const TextStyle(color: ThemeColor.COLOR_595959, fontSize: 12, fontWeight: FontWeight.w300),
                    )),
              ],
            )
          ],
        ),
      ),
    );
  }

  //是否打印分割线
  Widget _printDivider() {
    return Container(
      height: 44,
      child: Center(
        child: Row(
          children: [
            Text(
              intlanguage('app100001497', '是否打印分割线'),
              style: const TextStyle(color: ThemeColor.title, fontSize: 15, fontWeight: FontWeight.w400),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            Spacer(),
            CupertinoSwitch(
                value: logic.printDivider.value,
                activeColor: ThemeColor.brand,
                onChanged: (bool on) {
                  logic.printDivider.value = on;
                })
          ],
        ),
      ),
    );
  }
}

extension C1Print on _C1PrintSettingPageState {
  //打印
  Widget c1Print() {
    return Container(
      margin: EdgeInsetsDirectional.only(start: 16, top: 24, end: 16),
      child: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            VoidCallback clickBuried = (){
              ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "129_369_338", "ext": {}});
            };
            // 取消目前的Focus状态，防止键盘不收起
            FocusManager.instance.primaryFocus?.unfocus();
            //校验线号机是否连接状态
            if (!logic.checkConnectStatus()) {
              clickBuried();
              return;
            }
            if (textControllerPrintCount.text.isEmpty || textControllerPrintCount.text == "0") {
              textControllerPrintCount.text = "1";
              logic.printCount.value = 1;
            }
            else{
              int value = int.parse(textControllerPrintCount.text);
              logic.printCount.value = value;
            }
            if (textControllerParagraphStart.text.isEmpty) {
              textControllerParagraphStart.text = "1";
              logic.paragraphStart.value = 1;
            }
            else {
              int value = int.parse(textControllerParagraphStart.text);
              logic.paragraphStart.value = value;
            }
            if (textControllerParagraphEnd.text.isEmpty) {
              textControllerParagraphEnd.text = logic.paragraphCount.toString();
              logic.paragraphEnd.value = logic.paragraphCount;
            } else {
              int value = int.parse(textControllerParagraphEnd.text);
              if (value < logic.paragraphStart.value) {
                textControllerParagraphEnd.text = logic.paragraphCount.toString();
                logic.paragraphEnd.value = logic.paragraphCount;
              }
              else {
                logic.paragraphEnd.value = value;
              }
            }
            logic.handlePrint();
            clickBuried();
          },
          child: Container(
            height: 50,
            decoration: BoxDecoration(color: ThemeColor.brand, borderRadius: BorderRadius.circular(12)),
            child: Center(
              child: Text(
                intlanguage('app00016', '打印'),
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: ThemeColor.WHITE),
              ),
            ),
          )),
    );
  }
}

class ParagraphStartInputFormatter extends TextInputFormatter {
  final C1PrintSettingLogic logic;

  ParagraphStartInputFormatter(this.logic);

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    String text = newValue.text;
    if (text.length > 0) {
      try {
        int num = int.parse(text);
        if (num >= 1 && num <= logic.paragraphEnd.value) {
          return newValue;
        }
      } catch (e) {}
      return oldValue;
    }
    return newValue;
  }
}

class ParagraphEndInputFormatter extends TextInputFormatter {
  final C1PrintSettingLogic logic;

  ParagraphEndInputFormatter(this.logic);

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    String text = newValue.text;
    if (text.length > 0) {
      try {
        int num = int.parse(text);
        if (num >= 1 && num <= logic.paragraphCount) {
          return newValue;
        } else if (num > logic.paragraphCount) {
          EasyLoading.showToast(intlanguage("app100001782", "当前最多打印\$段", param: [logic.paragraphCount.toString()]));
        }
      } catch (e) {}
      return oldValue;
    }
    return newValue;
  }
}

class PrintCountInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    String text = newValue.text;
    if (text.length > 0) {
      try {
        int num = int.parse(text);
        if (num >= 1 && num <= 3000) {
          return newValue;
        }
      } catch (e) {}
      return oldValue;
    }
    return newValue;
  }
}
