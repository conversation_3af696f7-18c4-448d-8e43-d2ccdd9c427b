import 'dart:io';

import 'package:isar/isar.dart';
import 'package:path_provider/path_provider.dart';
import 'package:text/database/C1/niimbot_template_model.g.dart';
import 'package:text/database/ad/ad_model.g.dart';
import 'package:text/database/banner/banner_model.g.dart';
import 'package:text/database/folder/folder_model.g.dart';
import 'package:text/database/message/message_promotion_model.g.dart';
import 'package:text/database/printHistory/print_history_model.g.dart';
import 'package:text/database/printLog/print_content_log_model.g.dart';
import 'package:text/database/printLog/print_data_log_model.g.dart';
import 'package:text/pages/industry_template/home/<USER>/advertisement_db_model.dart';

import 'template/template_detail_model.dart';

class DBManagerUtil {
  DBManagerUtil._() {
    init();
  }

  static final DBManagerUtil _instance = DBManagerUtil._();

  factory DBManagerUtil() => _instance;

  static DBManagerUtil get instance => _instance;

  String dbFolderName = 'Niimbot';
  String dbName = 'niimbot_db';

  late String dirDBPath;

  late Isar isar;

  Future<void> init() async {
    dirDBPath = await getDatabaseDirPath();
    isar = await openDB(schemas: [
      NiimBotTemplateCanvasDataModelSchema,
      AdModelSchema,
      BannerModelSchema,
      PrintDataLogModelSchema,
      PrintContentLogModelSchema,
      PrintHistoryModelSchema,
      MessagePromotionModelSchema,
      NiimbotTemplateModelSchema,
      FolderModelSchema,
      AdvertisementDbModelSchema,
      AdvertisementCacheInfoSchema,
    ]);
  }

  Future<String> getDatabaseDirPath() async {
    final dir = await getApplicationDocumentsDirectory();
    return '${dir.path}${Platform.pathSeparator}$dbFolderName';
  }

  Future<Isar> openDB({required List<CollectionSchema<dynamic>> schemas}) async {
    print('-----dir-----${dirDBPath}');
    var nmbotDir = Directory(dirDBPath);
    var isExist = nmbotDir.existsSync();
    if (isExist) {
      final result = await Isar.open(schemas, directory: dirDBPath, inspector: true, name: dbName);
      return result;
    } else {
      await nmbotDir.create();
      final result = await Isar.open(schemas, directory: dirDBPath, inspector: true, name: dbName);
      return result;
    }
  }

  ///读
  getData() async {
    // print('2222222222222');
    // final templateData = isar.collection<MyTemplateModel>();
    // List<MyTemplateModel> list = await templateData.where().findAll();
    // print('2222222222222${list}');
    //
    // list.forEach((element) {
    //   print(
    //       '-------------${element.id}------------${element.name}----------${element.isUpdate}');
    // });
  }

  ///写
  addData() {
    isar.writeTxn(() async {
      //  print( '------${DateTime.now()}--------');
      //  final a = await isar.myTemplateModels.putAll(list);
      // print( '------${DateTime.now()}--------${a}');
      // list.forEach((element) async {
      //   await isar.myTemplateModels.put(element);
      // });
    });
  }

  ///更新
  updateData() {}

  ///删除
  delData() async {
    isar.writeTxn(() async {
      // int success  = await isar.myTemplateModels.where().deleteAll();
      // print('------------${success}');
    });
  }
}
