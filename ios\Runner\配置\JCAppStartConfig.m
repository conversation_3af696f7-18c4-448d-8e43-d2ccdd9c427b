//
//  JCAppStartConfig.m
//  Runner
//
//  Created by Echo Ma on 2022/10/18.
//

#import "JCPrintContentModel.h"
#import "JCActivityCodeModel.h"
#import "JCTemplateData4DB.h"
#import "JCAppStartConfig.h"
#import "JCMyFolderModel.h"
#import "JCLabelUseModel.h"
#import "JCAdModel.h"
#import "JCPrintHistoryModel.h"
#import "LaboratoryModel.h"
#import "JCVIPTextBannerModel.h"

@implementation JCAppStartConfig

+ (void)initLocalPathAndResource{
    [self creatDBWithLang:@""];
    [self creatDBWithLang:@"zh-cn"];
    [self creatDBWithLang:@"en"];
    [self initLocalUA];
    [self initLocalPath];
    [self initUserDefaultData];
    [self checkLocalLanguageResource];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self saveDefalutDeviceToDB];
        [self copyLocalDefaultFontToSandBox];
        [self saveDefalutPrinterListToDB];
    });
}

+ (void)initLocalUA {
    NSString *appId = [NSString stringWithFormat:@"AppId/%@",[[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleIdentifier"]];
    NSString *os = [NSString stringWithFormat:@"OS/%@",@"ios"];
    NSString *appVersionName = [NSString stringWithFormat:@"AppVersionName/%@",[[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"]];
    NSString *deviceName = [XYTool deviceName];
    NSString *model = [NSString stringWithFormat:@"Model/%@",[deviceName stringByReplacingOccurrencesOfString:@" " withString:@"-"]];
    NSString *systemVersion = [NSString stringWithFormat:@"SystemVersion/%@",[UIDevice currentDevice].systemVersion];
    NSString *deviceId = [NSString stringWithFormat:@"DeviceId/%@",[JCKeychainTool getDeviceIDInKeychain]];
//    NSString *boundleId = [NSString stringWithFormat:@"boundleId/%@",[[NSBundle mainBundle] bundleIdentifier]];
    NSString *referer = @"referer/CP001Mobile";
    NSString *newUA =[NSString stringWithFormat:@"%@ %@ %@ %@ %@ %@ %@",appId,os,appVersionName,model,systemVersion,deviceId,referer];
    [[NSUserDefaults standardUserDefaults] setObject:newUA forKey:@"niimbot-user-agent"];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

+ (void)initUserDefaultData{
    if([[NSUserDefaults standardUserDefaults] objectForKey:@"overseasTrialTime"] == nil){
        [[NSUserDefaults standardUserDefaults] setObject:@"2021-03-01T00:00:00.000+0000" forKey:@"overseasTrialTime"];
    }
    if([[NSUserDefaults standardUserDefaults] objectForKey:@"storeTrialTime"] == nil){
        [[NSUserDefaults standardUserDefaults] setObject:@"2023-08-31T00:00:00.000+0000" forKey:@"storeTrialTime"];
    }
    if([[NSUserDefaults standardUserDefaults] objectForKey:@"unCNSupportActivityCode"] == nil){
        [[NSUserDefaults standardUserDefaults] setObject:@0 forKey:@"unCNSupportActivityCode"];
    }
    NSString *timeStamp = [XYTool getNowTimeTimestamp];
    [[NSUserDefaults standardUserDefaults] setObject:@(timeStamp.longLongValue) forKey:@"requestVersion"];
}

+ (void)initLocalPath{
    if(![[NSFileManager defaultManager] fileExistsAtPath:RESOURCE_IMAGE_THUMB_BACK_PATH]){
        [[NSFileManager defaultManager] createDirectoryAtPath:RESOURCE_IMAGE_THUMB_BACK_PATH withIntermediateDirectories:YES attributes:nil error:nil];
    }
    if(![[NSFileManager defaultManager] fileExistsAtPath:RESOURCE_IMAGE_CLOILD_THUMB_BACK_PATH]){
        [[NSFileManager defaultManager] createDirectoryAtPath:RESOURCE_IMAGE_CLOILD_THUMB_BACK_PATH withIntermediateDirectories:YES attributes:nil error:nil];
    }
    if(![[NSFileManager defaultManager] fileExistsAtPath:RESOURCE_PDF_PATH]){
        [[NSFileManager defaultManager] createDirectoryAtPath:RESOURCE_PDF_PATH withIntermediateDirectories:YES attributes:nil error:nil];
    }
    if(![[NSFileManager defaultManager] fileExistsAtPath:RESOURCE_UNIAPP_PATH]){
        [[NSFileManager defaultManager] createDirectoryAtPath:RESOURCE_UNIAPP_PATH withIntermediateDirectories:YES attributes:nil error:nil];
    }
    if(![[NSFileManager defaultManager] fileExistsAtPath:RESOURCE_UNIAPP_PATH]){
        [[NSFileManager defaultManager] createDirectoryAtPath:RESOURCE_UNIAPP_PATH withIntermediateDirectories:YES attributes:nil error:nil];
    }
    if(![[NSFileManager defaultManager] fileExistsAtPath:RESOURCE_ACTIVITY_CODE_IMAGE_PATH]){
        [[NSFileManager defaultManager] createDirectoryAtPath:RESOURCE_ACTIVITY_CODE_IMAGE_PATH withIntermediateDirectories:YES attributes:nil error:nil];
    }
    if(![[NSFileManager defaultManager] fileExistsAtPath:RESOURCE_EVENTS_IMAGE_PATH]){
        [[NSFileManager defaultManager] createDirectoryAtPath:RESOURCE_EVENTS_IMAGE_PATH withIntermediateDirectories:YES attributes:nil error:nil];
    }
    if(![[NSFileManager defaultManager] fileExistsAtPath:RESOURCE_ELEMENT_PATH]){
        [[NSFileManager defaultManager] createDirectoryAtPath:RESOURCE_ELEMENT_PATH withIntermediateDirectories:YES attributes:nil error:nil];
    }
    if(![[NSFileManager defaultManager] fileExistsAtPath:RESOURCE_CLOILD_ELEMENT_PATH]){
        [[NSFileManager defaultManager] createDirectoryAtPath:RESOURCE_CLOILD_ELEMENT_PATH withIntermediateDirectories:YES attributes:nil error:nil];
    }
}

+ (void)creatDBWithLang:(NSString *)langType{
    NSString *dbPath = RESOURCE_DB_PATH;
    NSFileManager* fm=[NSFileManager defaultManager];
    if(![fm fileExistsAtPath:dbPath]){
        [fm createDirectoryAtPath:dbPath withIntermediateDirectories:YES attributes:nil error:nil];
    }
    if(STR_IS_NIL(langType)){
        JCFMDB *dbEn = [JCFMDB shareDatabase:@"JCPrint.sqlite" path:dbPath];
        [self creatDBTableWith:dbEn];
    }else{
        JCFMDB *dbEn = [JCFMDB shareDatabase:[NSString stringWithFormat:@"JCPrint_%@.sqlite",langType] path:dbPath];
        [self creatDBTableWith:dbEn];
    }

}

/**
 检查本地语言资源 如果第一次启动复制到沙盒
 */

+ (void)checkLocalLanguageResource{
    NSFileManager *fileManager=[NSFileManager defaultManager];
    if(![fileManager fileExistsAtPath:RESOURCE_LANGUAGE_PATH]){
        [fileManager createDirectoryAtPath:RESOURCE_LANGUAGE_PATH withIntermediateDirectories:YES attributes:nil error:nil];
    }
    if(![fileManager fileExistsAtPath:RESOURCE_LoginPlugin_LANGUAGE_PATH]){
        [fileManager createDirectoryAtPath:RESOURCE_LoginPlugin_LANGUAGE_PATH withIntermediateDirectories:YES attributes:nil error:nil];
    }
    NSArray *langFileArr = [fileManager contentsOfDirectoryAtPath:RESOURCE_LANGUAGE_PATH error:nil];
    if(langFileArr.count == 0){
        NSString *langBundlePath = [[NSBundle mainBundle] pathForResource:@"LanguageBundle" ofType:@"bundle"];
        NSArray *langFileArr = [fileManager contentsOfDirectoryAtPath:langBundlePath error:nil];
        for (NSString *langFile in langFileArr) {
            if(![langFile hasPrefix:@"login"]){
                NSString *langFileBundlePath = [NSString stringWithFormat:@"%@/%@",langBundlePath,langFile];
                NSString *langFileLocalPath =[RESOURCE_LANGUAGE_PATH stringByAppendingPathComponent:langFile];
                [fileManager copyItemAtPath:langFileBundlePath toPath:langFileLocalPath error:nil];
            }
        }
        [[NSUserDefaults standardUserDefaults] setObject:@"" forKey:@"langVersion"];
        [[NSUserDefaults standardUserDefaults] synchronize];
    }
    NSArray *loginLangFileArr = [fileManager contentsOfDirectoryAtPath:RESOURCE_LoginPlugin_LANGUAGE_PATH error:nil];
    if(loginLangFileArr.count == 0){
        NSString *langBundlePath = [[NSBundle mainBundle] pathForResource:@"LanguageBundle" ofType:@"bundle"];
        NSArray *loginLangFileArr = [fileManager contentsOfDirectoryAtPath:langBundlePath error:nil];
        for (NSString *langFile in loginLangFileArr) {
            if([langFile hasPrefix:@"login"]){
                NSString *langFileBundlePath = [NSString stringWithFormat:@"%@/%@",langBundlePath,langFile];
                NSString *langFileLocalPath =[RESOURCE_LoginPlugin_LANGUAGE_PATH stringByAppendingPathComponent:langFile];
                [fileManager copyItemAtPath:langFileBundlePath toPath:langFileLocalPath error:nil];
            }
        }
    }
}

/**
 创建数据库表
 */

+ (void)creatDBTableWith:(JCFMDB *)db{
    //
    if (![db jc_isExistTable:TABLE_TEMPLATE_NEW]) {
        [db jc_createTable:TABLE_TEMPLATE_NEW dicOrModel:[JCTemplateData4DB class] primaryKey:@"idStr"];
    }else{
        [db jc_alterTable:TABLE_TEMPLATE_NEW dicOrModel:[JCTemplateData4DB class]];
//        [db updatePrimaryKey:TABLE_TEMPLATE_NEW primaryKey:@"idStr"];
    }
    if (![db jc_isExistTable:TABLE_BATCH_PRINT_TEMPLATE]) {
        [db jc_createTable:TABLE_BATCH_PRINT_TEMPLATE dicOrModel:[JCTemplateData4DB class] primaryKey:@"idStr"];
    }else{
        [db jc_alterTable:TABLE_BATCH_PRINT_TEMPLATE dicOrModel:[JCTemplateData4DB class]];
//        [db updatePrimaryKey:TABLE_TEMPLATE_NEW primaryKey:@"idStr"];
    }

    if (![db jc_isExistTable:TABLE_CLOUD_TEMPLATE_NEW]) {
        [db jc_createTable:TABLE_CLOUD_TEMPLATE_NEW dicOrModel:[JCTemplateData4DB class]];
    }else{
        [db jc_alterTable:TABLE_CLOUD_TEMPLATE_NEW dicOrModel:[JCTemplateData4DB class]];
    }
    if (![db jc_isExistTable:TABLE_EXCELINFO]) {
        [db jc_createTable:TABLE_EXCELINFO dicOrModel:[JCExcelInfoModel class]];
    }
    if (![db jc_isExistTable:TABLE_CATINFO]) {
        [db jc_createTable:TABLE_CATINFO dicOrModel:[JCCatModel class]];
    }else{
        [db jc_alterTable:TABLE_CATINFO dicOrModel:[JCCatModel class]];
    }

    if (![db jc_isExistTable:TABLE_CATDETAILINFO]) {
        [db jc_createTable:TABLE_CATDETAILINFO dicOrModel:[JCCatChildrenModel class]];
    }else{
        [db jc_alterTable:TABLE_CATDETAILINFO dicOrModel:[JCCatChildrenModel class]];
    }
    if (![db jc_isExistTable:TABLE_GOODS_CATINFO]) {
        [db jc_createTable:TABLE_GOODS_CATINFO dicOrModel:[JCCatModel class]];
    }else{
        [db jc_alterTable:TABLE_GOODS_CATINFO dicOrModel:[JCCatModel class]];
    }

    if (![db jc_isExistTable:TABLE_GOODS_CATDETAILINFO]) {
        [db jc_createTable:TABLE_GOODS_CATDETAILINFO dicOrModel:[JCCatChildrenModel class]];
    }else{
        [db jc_alterTable:TABLE_GOODS_CATDETAILINFO dicOrModel:[JCCatChildrenModel class]];
    }

    if (![db jc_isExistTable:TABLE_LOGO_CATINFO]) {
        [db jc_createTable:TABLE_LOGO_CATINFO dicOrModel:[JCCatModel class]];
    }else{
        [db jc_alterTable:TABLE_LOGO_CATINFO dicOrModel:[JCCatModel class]];
    }

    if (![db jc_isExistTable:TABLE_LOGO_CATDETAILINFO]) {
        [db jc_createTable:TABLE_LOGO_CATDETAILINFO dicOrModel:[JCCatChildrenModel class]];
    }else{
        [db jc_alterTable:TABLE_LOGO_CATDETAILINFO dicOrModel:[JCCatChildrenModel class]];
    }

    if (![db jc_isExistTable:TABLE_PRINTERINFO]) {
        [db jc_createTable:TABLE_PRINTERINFO dicOrModel:[JCPrinterModel class]];
    }else{
        BOOL flag = [db jc_alterTable:TABLE_PRINTERINFO dicOrModel:[JCPrinterModel class]];
        [db jc_alterTable:TABLE_PRINTERINFO dicOrModel:[JCPrinterModel class]];
        if(flag){
            [db jc_deleteAllDataFromTable:TABLE_PRINTERINFO];
        }
    }

    if (![db jc_isExistTable:TABLE_LOGINFO]) {
        [db jc_createTable:TABLE_LOGINFO dicOrModel:[JCTemplateLogoModel class]];
    }else{
        [db jc_alterTable:TABLE_LOGINFO dicOrModel:[JCTemplateLogoModel class]];
    }

    if (![db jc_isExistTable:TABLE_LOGINFO_Save]) {
        [db jc_createTable:TABLE_LOGINFO_Save dicOrModel:[JCTemplateLogoModel class]];
    }else{
        [db jc_alterTable:TABLE_LOGINFO_Save dicOrModel:[JCTemplateLogoModel class]];
    }

    if (![db jc_isExistTable:TABLE_FONTINFO]) {
        [db jc_createTable:TABLE_FONTINFO dicOrModel:[JCFontModel class]];
    }else{
        [db jc_alterTable:TABLE_FONTINFO dicOrModel:[JCFontModel class]];
    }
    if (![db jc_isExistTable:TABLE_USER_FONTINFO]) {
        [db jc_createTable:TABLE_USER_FONTINFO dicOrModel:[JCFontModel class] primaryKey:@"dbId"];
    }else{
        [db jc_alterTable:TABLE_USER_FONTINFO dicOrModel:[JCFontModel class]];
    }

    if (![db jc_isExistTable:TABLE_VIP_FONTINFO]) {
        [db jc_createTable:TABLE_VIP_FONTINFO dicOrModel:[JCFontModel class]];
    }else{
        [db jc_alterTable:TABLE_VIP_FONTINFO dicOrModel:[JCFontModel class]];
    }

    if (![db jc_isExistTable:TABLE_FONT_CATEGORY_INFO]) {
        [db jc_createTable:TABLE_FONT_CATEGORY_INFO dicOrModel:[JCFontCategoryModel class]];
    }else{
        [db jc_alterTable:TABLE_FONT_CATEGORY_INFO dicOrModel:[JCFontCategoryModel class]];
    }

    if (![db jc_isExistTable:TABLE_VIP_LOGOINFO]) {
        [db jc_createTable:TABLE_VIP_LOGOINFO dicOrModel:[JCTemplateLogoModel class]];
    }else{
        [db jc_alterTable:TABLE_VIP_LOGOINFO dicOrModel:[JCTemplateLogoModel class]];
    }

    if (![db jc_isExistTable:TABLE_LANGINFO]) {
        [db jc_createTable:TABLE_LANGINFO dicOrModel:[JCLanguageModel class]];
    }else{
        [db jc_alterTable:TABLE_LANGINFO dicOrModel:[JCLanguageModel class]];
    }

    if (![db jc_isExistTable:TABLE_MYDIRINFO]) {
        [db jc_createTable:TABLE_MYDIRINFO dicOrModel:[JCMyFolderModel class]];
    }else{
        [db jc_alterTable:TABLE_MYDIRINFO dicOrModel:[JCMyFolderModel class]];
    }

    if (![db jc_isExistTable:TABLE_MYDEVICES]) {
        [db jc_createTable:TABLE_MYDEVICES dicOrModel:[JCSelectDeviceModel class]];
    }else{
        [db jc_alterTable:TABLE_MYDEVICES dicOrModel:[JCSelectDeviceModel class]];
    }

    if (![db jc_isExistTable:TABLE_RFIDRULL]) {
        [db jc_createTable:TABLE_RFIDRULL dicOrModel:[JCRuleModel class]];
    }else{
        BOOL flag = [db jc_alterTable:TABLE_RFIDRULL dicOrModel:[JCRuleModel class]];
        if(flag){
            [db jc_deleteAllDataFromTable:TABLE_RFIDRULL];
        }
    }
    if (![db jc_isExistTable:TABLE_DEVICE_OFFSET]) {
        [db jc_createTable:TABLE_DEVICE_OFFSET dicOrModel:[JCDeviceOffSetInfoModel class]];
    }else{
        [db jc_alterTable:TABLE_DEVICE_OFFSET dicOrModel:[JCDeviceOffSetInfoModel class]];
    }

    if (![db jc_isExistTable:TABLE_RFIDTABLE]) {
        [db jc_createTable:TABLE_RFIDTABLE dicOrModel:[JCRFIDModel class]];
    }else{
        [db jc_alterTable:TABLE_RFIDTABLE dicOrModel:[JCRFIDModel class]];
    }

    if (![db jc_isExistTable:TABLE_RFIDTABLE_NEW]) {
        [db jc_createTable:TABLE_RFIDTABLE_NEW dicOrModel:[JCRFIDModel class]];
    }else{
        [db jc_alterTable:TABLE_RFIDTABLE_NEW dicOrModel:[JCRFIDModel class]];
    }

    if (![db jc_isExistTable:TABLE_LABEL_USE_INFO]) {
        [db jc_createTable:TABLE_LABEL_USE_INFO dicOrModel:[JCLabelUseModel class]];
    }else{
        [db jc_alterTable:TABLE_LABEL_USE_INFO dicOrModel:[JCLabelUseModel class]];
    }

    if (![db jc_isExistTable:TABLE_LABEL_ACTIVITY_CODE_INFO]) {
        [db jc_createTable:TABLE_LABEL_ACTIVITY_CODE_INFO dicOrModel:[JCActivityCodeModel class]];
    }else{
        [db jc_alterTable:TABLE_LABEL_ACTIVITY_CODE_INFO dicOrModel:[JCActivityCodeModel class]];
    }

    if (![db jc_isExistTable:TABLE_LABEL_FORM_CODE_INFO]) {
        [db jc_createTable:TABLE_LABEL_FORM_CODE_INFO dicOrModel:[JCActivityCodeModel class]];
    }else{
        [db jc_alterTable:TABLE_LABEL_FORM_CODE_INFO dicOrModel:[JCActivityCodeModel class]];
    }

    if (![db jc_isExistTable:TABLE_LABEL_LAST_USE_LOGO_INFO]) {
        [db jc_createTable:TABLE_LABEL_LAST_USE_LOGO_INFO dicOrModel:[JCTemplateLogoModel class]];
    }else{
        [db jc_alterTable:TABLE_LABEL_LAST_USE_LOGO_INFO dicOrModel:[JCTemplateLogoModel class]];
    }

    // 实验室功能
    if (![db jc_isExistTable:TABLE_LABORATORY]) {
        [db jc_createTable:TABLE_LABORATORY dicOrModel:[LaboratoryModel class]];
    }else{
        [db jc_alterTable:TABLE_LABORATORY dicOrModel:[LaboratoryModel class]];
    }

    // VIP文本横幅展示
    if (![db jc_isExistTable:TABLE_VIP_TEXT_BANNER]) {
        [db jc_createTable:TABLE_VIP_TEXT_BANNER dicOrModel:[JCVIPTextBannerModel class]];
    }else{
        [db jc_alterTable:TABLE_VIP_TEXT_BANNER dicOrModel:[JCVIPTextBannerModel class]];
    }
}

/**
 保存默认系列数据缓存到数据库
 */

 + (void)saveDefalutDeviceToDB{
    NSString *dbName1 = @"JCPrint_zh-cn.sqlite";
     NSString *dbName2 = @"JCPrint_en.sqlite";
    NSArray *printerInfoArr1 = [[JCFMDB shareDatabase:dbName1] jc_lookupTable:TABLE_MYDEVICES dicOrModel:[JCSelectDeviceModel class] whereFormat:nil];
    if(printerInfoArr1.count == 0){
        NSBundle *bundle = [NSBundle mainBundle];
        NSString *printerInfoFile = @"";
        #ifdef Pro
        printerInfoFile =[NSString stringWithFormat:@"devicesList_%@",@"zh-cn"];
        #elif ReleaseAppStore
        printerInfoFile =[NSString stringWithFormat:@"devicesList_%@",@"zh-cn"];
        #elif JCTest
        printerInfoFile =[NSString stringWithFormat:@"devicesList_%@_test",@"zh-cn"];
        #elif Develop
        printerInfoFile =[NSString stringWithFormat:@"devicesList_%@",@"zh-cn"];
        #endif
        NSString *file = [bundle pathForResource:printerInfoFile ofType:@"text"];
        NSString *jsonString = [NSString stringWithContentsOfFile:file encoding:NSUTF8StringEncoding error:nil];
        NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
        if(jsonData){
            [[JCFMDB shareDatabase] jc_inTransaction:^(BOOL *rollback) {
                if(*rollback){
                    NSLog(@"更新失败");
                }else{
                    NSArray *arr = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingAllowFragments error:nil];
                    NSMutableArray *array = [NSMutableArray array];
                    for(NSDictionary *dic in arr){
                        JCSelectDeviceModel *m = [[JCSelectDeviceModel alloc] initWithDictionary:dic error:nil];
                        if(m == nil) continue;
                        [array addObject:m];
                    }
                    NSArray *aaa = [[JCFMDB shareDatabase:dbName1] jc_insertTable:TABLE_MYDEVICES dicOrModelArray:array];
                    NSLog(@"=====------=======%@",aaa);
                }
            }];
        }

    }
     NSArray *printerInfoArr2 = [[JCFMDB shareDatabase:dbName2] jc_lookupTable:TABLE_MYDEVICES dicOrModel:[JCSelectDeviceModel class] whereFormat:nil];
     if(printerInfoArr2.count == 0){
         NSBundle *bundle = [NSBundle mainBundle];
         NSString *printerInfoFile = @"";
        #ifdef Pro
         printerInfoFile =[NSString stringWithFormat:@"devicesList_%@",@"en"];
        #elif ReleaseAppStore
         printerInfoFile =[NSString stringWithFormat:@"devicesList_%@",@"en"];
        #elif JCTest
         printerInfoFile =[NSString stringWithFormat:@"devicesList_%@_test",@"en"];
        #elif Develop
         printerInfoFile =[NSString stringWithFormat:@"devicesList_%@",@"en"];
        #endif
         NSString *file = [bundle pathForResource:printerInfoFile ofType:@"text"];
         NSString *jsonString = [NSString stringWithContentsOfFile:file encoding:NSUTF8StringEncoding error:nil];
         NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
         if(jsonData){
             [[JCFMDB shareDatabase] jc_inTransaction:^(BOOL *rollback) {
                 if(*rollback){
                     NSLog(@"更新失败");
                 }else{
                     NSArray *arr = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingAllowFragments error:nil];
                     NSMutableArray *array = [NSMutableArray array];
                     for(NSDictionary *dic in arr){
                         JCSelectDeviceModel *m = [[JCSelectDeviceModel alloc] initWithDictionary:dic error:nil];
                         if(m == nil) continue;
                         [array addObject:m];
                     }
                     NSArray *aaa = [[JCFMDB shareDatabase:dbName2] jc_insertTable:TABLE_MYDEVICES dicOrModelArray:array];
                     NSLog(@"=====------=======%@",aaa);
                 }
             }];
         }

     }
}

/**
 保存默认字体到数据库
 */

+ (void)copyLocalDefaultFontToSandBox{
    NSFileManager* fileManager = [NSFileManager defaultManager];
    NSString *fontPath = [NSString stringWithFormat:@"%@/font",DocumentsFontPath];
    if(![fileManager fileExistsAtPath:fontPath]){
        [fileManager createDirectoryAtPath:fontPath withIntermediateDirectories:YES attributes:nil error:nil];
    }

    // 临时处理字体
    [@[@"ZT063.ttf", @"ZT825.ttf", @"Thai.ttf", @"emoji.ttf",@"Fallback", @"fontConfig.json"] enumerateObjectsUsingBlock:^(id  _Nonnull name, NSUInteger idx, BOOL * _Nonnull stop) {
        NSString *cnDefaultFile = [fontPath stringByAppendingPathComponent: [NSString stringWithFormat:@"%@", name]];
        if (![fileManager fileExistsAtPath:cnDefaultFile]) {
            NSString *resourcePath = [[NSBundle mainBundle] pathForResource:name ofType:@""];
            [fileManager copyItemAtPath:resourcePath toPath:cnDefaultFile error:nil];
        } else if ([name isEqualToString:@"emoji.ttf"] && [fileManager fileExistsAtPath:cnDefaultFile]) {
          // 强制覆盖emoji.ttf，防止后续版本更新字体后未覆盖原字体的情况
          NSError *removeError = nil;
          // 删除目标路径的文件
          if (![fileManager removeItemAtPath:cnDefaultFile error:&removeError]) {
            NSLog(@"删除文件失败: %@", removeError.localizedDescription);
            return;
          }
          NSError *copyError = nil;
          NSString *resourcePath = [[NSBundle mainBundle] pathForResource:name ofType:@""];
          // 复制文件到目标路径
          if (![fileManager copyItemAtPath:resourcePath toPath:cnDefaultFile error:&copyError]) {
            NSLog(@"复制文件失败: %@", copyError.localizedDescription);
            return;
          }
          NSLog(@"文件覆盖成功");
        }
    }];

//    [@[@"Korean"] enumerateObjectsUsingBlock:^(id  _Nonnull name, NSUInteger idx, BOOL * _Nonnull stop) {
//        NSString *cnDefaultFile = [fontPath stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.otf", name]];
//        if (![fileManager fileExistsAtPath:cnDefaultFile]) {
//            NSString *resourcePath = [[NSBundle mainBundle] pathForResource:name ofType:@"otf"];
//            [fileManager copyItemAtPath:resourcePath toPath:cnDefaultFile error:nil];
//        }
//    }];
}

/**
 保存默认的打印机列表到数据库
 */
+ (void)saveDefalutPrinterListToDB{
    NSString *whereString = nil;
    NSArray *printerInfoArr = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_PRINTERINFO dicOrModel:[JCPrinterModel class] whereFormat:whereString];
    if(printerInfoArr.count == 0){
        NSBundle *bundle = [NSBundle mainBundle];
        NSString *printerInfoFile = @"";
        #ifdef Pro
        printerInfoFile = @"printerList";
        #elif ReleaseAppStore
        printerInfoFile = @"printerList";
        #elif JCTest
        printerInfoFile = @"printerList_test";
        #elif Develop
        printerInfoFile = @"printerList";
        #endif
        NSString *file = [bundle pathForResource:printerInfoFile ofType:@"text"];
        NSString *jsonString = [NSString stringWithContentsOfFile:file encoding:NSUTF8StringEncoding error:nil];
        if(jsonString){
            JCPrinterModel *model = [[JCPrinterModel alloc] initWithString:jsonString usingEncoding:NSUTF8StringEncoding error:nil];
            printerInfoArr = model.list.mutableCopy;
            NSMutableArray *printerNameArr = [NSMutableArray array];
            for (JCPrinterModel *model in printerInfoArr) {
                [printerNameArr addObject:model.name];
            }
            [[JCFMDB shareDatabase] jc_inTransaction:^(BOOL *rollback) {
                if(*rollback){
                    NSLog(@"更新失败");
                }else{
                    [[JCFMDB shareDatabase:DB_DEFAULT] jc_insertTable:TABLE_PRINTERINFO dicOrModelArray:printerInfoArr];
                }
            }];
        }
    }
}
@end
