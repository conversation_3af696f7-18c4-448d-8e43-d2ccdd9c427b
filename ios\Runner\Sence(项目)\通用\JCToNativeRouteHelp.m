//
//  JCToNativeRouteHelp.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2022/4/1.
//  Copyright © 2022 Jingchen Technology Co.Ltd  . All rights reserved.
//
#import "JCShopNormalVC.h"
#import "JCQAViewController.h"
#import "JCToNativeRouteHelp.h"
#import "JCMyStoreViewController.h"
#import "JCExcelListViewController.h"
#import "JCVIPDetailViewController.h"
#import "JCRedeemCodeViewController.h"
#import "JCLabelCustomizeViewController.h"
#import "JCGoodsTemplateViewController.h"
#import "JCStoreGoodsLibearyViewController.h"
#import "JCActivityCodeEditViewController.h"
#import "FlutterBoostUtility.h"
#import "JCFlutter2NativeHandler.h"
#import "JCLabelUseModel.h"
#import "JCGrayConfigModel.h"
#import "JCOtherAppAlert.h"
#import "JCLabelAppModel.h"
#import "NMFAWebViewController.h"

@implementation JCToNativeRouteHelp

+ (void)toNativePageWith:(NSString *)nativeRoute fromType:(RouteType)fromType eventTitle:(NSString *)eventTitle origRout:(NSString *)origRout{
    if(STR_IS_NIL(nativeRoute)) return;
    XYViewController *current = (XYViewController *)[XYTool getCurrentVC];
    UITabBarController *currentTabbarVC = current.tabBarController;
    UINavigationController *currentNavVC = current.navigationController;
    if(currentTabbarVC == nil){
        AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
        currentTabbarVC = appDelegate.mainVC;
    }
    if(currentNavVC == nil){
        currentNavVC = currentTabbarVC.selectedViewController;
    }
    if([origRout hasPrefix:JC_VIP_Detail]){
        NSDictionary *vipRoutParms = [XYTool dictionaryWithUrlString:origRout];
        NSNumber *isNeedLogin = vipRoutParms[@"needLogin"];
        NSNumber *showType = vipRoutParms[@"showType"];
        XYNormalBlock toVipDetailBlock = ^(){
            if(showType.integerValue == 1){
                NSMutableDictionary *sourceInfo = @{}.mutableCopy;
                if(fromType == JC_Home_Banner){
                    sourceInfo = @{@"sourcePage":@"003",@"act_name":UN_NIL(eventTitle)}.mutableCopy;;
                }else if(fromType == JC_Store_Banner){
                    JC_TrackWithparms(@"click",@"004_006_015",@{});
                    sourceInfo = @{@"sourcePage":@"004",@"act_name":UN_NIL(eventTitle)}.mutableCopy;;
                }else if(fromType == JC_H5Web){
                    sourceInfo = @{@"sourcePage":@"028",@"act_name":UN_NIL(eventTitle)}.mutableCopy;;
                }else if(fromType == JC_ADPop){
                    sourceInfo = @{@"sourcePage":@"028",@"act_name":UN_NIL(eventTitle)}.mutableCopy;;
                }
                NSString *anchorSubscribeId = [vipRoutParms objectForKey:@"anchorSubscribeId"];
                if(anchorSubscribeId != nil){
                    [sourceInfo setObject:anchorSubscribeId forKey:@"anchorSubscribeId"];
                }
                UIViewController *currentVC = [XYTool getCurrentVC];
                [JCIAPHelper openViewWithAlert:currentVC needOpenTip:NO isUseVipSource:NO success:^{
                    
                } failure:^(NSString *msg, id model) {
                    
                } sourceInfo:sourceInfo];
            }else{
                [JCToNativeRouteHelp toNativePageWith:nativeRoute fromType:fromType eventTitle:eventTitle];
            }
        };
        if(isNeedLogin != nil && isNeedLogin.integerValue == 1){
            [[JCLoginManager sharedInstance] checkLogin:^{
                
            } viewController:current loginSuccessBlock:^{
                toVipDetailBlock();
            }];
        }else{
            toVipDetailBlock();
        }
    }else if([origRout hasPrefix:JC_New_Canvas]){
        // 获取路有信息
        NSDictionary *vipRoutParms = [XYTool dictionaryWithUrlString:origRout];
        [[JCFlutter2NativeHandler sharedInstance] loadCanvasWithAppRouter:vipRoutParms];
    }else if([nativeRoute hasPrefix:JC_Cable_Canvas]){
        NSString *fontPath = [NSString stringWithFormat:@"%@/font", DocumentsFontPath];
        [self getCableTemplata:^(id x) {
          JCTemplateData *cableTemplate = x;
          NSMutableDictionary *arguments = @{@"jsonData": UN_NIL(cableTemplate.toJSONString),@"isPresent": @NO,@"fontPath": fontPath,
                                             @"isEnablePopGesture": @NO}.mutableCopy;
          if(cableTemplate != nil){
              arguments[@"jsonData"] = UN_NIL(cableTemplate.toJSONString);
          }
          [FlutterBoostUtility gotoFlutterPage:@"cableCanvas" arguments:arguments onPageFinished:^(NSDictionary *dic) {
                                                
          }];
        }];
    }
    else{
        [JCToNativeRouteHelp toNativePageWith:nativeRoute fromType:fromType eventTitle:eventTitle];
    }
}


+ (void)toNativePageWith:(NSString *)nativeRoute fromType:(RouteType)fromType eventTitle:(NSString *)eventTitle{
    if(STR_IS_NIL(nativeRoute)) return;
    XYViewController *current = (XYViewController *)[XYTool getCurrentVC];
    UITabBarController *currentTabbarVC = current.tabBarController;
    UINavigationController *currentNavVC = current.navigationController;
    if(currentTabbarVC == nil){
        AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
        currentTabbarVC = appDelegate.mainVC;
    }
    if(currentNavVC == nil){
        currentNavVC = currentTabbarVC.selectedViewController;
    }
    if([nativeRoute hasPrefix:JC_VIP_Detail]){
        NSDictionary *vipRoutParms = [XYTool dictionaryWithUrlString:nativeRoute];
        NSNumber *isNeedLogin = vipRoutParms[@"needLogin"];
        XYNormalBlock toVipDetailBlock = ^(){
            __block BOOL containVIPVC = NO;
            [currentNavVC.viewControllers enumerateObjectsUsingBlock:^(__kindof UIViewController * _Nonnull obj, NSUInteger idx, BOOL *stop) {
                if([obj isKindOfClass:[JCVIPDetailViewController class]]){
                    containVIPVC = YES;
                    JCVIPDetailViewController *vipVC = obj;
                    vipVC.sourcePage = @"028";
                    vipVC.eventTitle = eventTitle;
                    [currentNavVC popToViewController:vipVC animated:YES];
                    *stop = YES;
                }
            }];
            if(!containVIPVC){
                JCVIPDetailViewController *vc = [[JCVIPDetailViewController alloc] init];
                NSString *anchorSubscribeId = [vipRoutParms objectForKey:@"anchorSubscribeId"];
                if(anchorSubscribeId != nil){
                    vc.anchorSubscribeId = anchorSubscribeId;
                }
                if(fromType == JC_Home_Banner){
                    vc.sourcePage = @"003";
                    vc.eventTitle = eventTitle;
                    JC_TrackWithparms(@"click",@"003_081_001",@{});
                }else if(fromType == JC_Store_Banner){
                    JC_TrackWithparms(@"click",@"004_006_015",@{});
                    vc.sourcePage = @"004";
                    vc.eventTitle = eventTitle;
                }else if(fromType == JC_H5Web){
                    JC_TrackWithparms(@"click",@"028_078",@{});
                    vc.sourcePage = @"028";
                    vc.eventTitle = eventTitle;
                }else if(fromType == JC_ADPop){
                    JC_TrackWithparms(@"click",@"028_078",@{});
                    vc.sourcePage = @"028";
                    vc.eventTitle = eventTitle;
                }
                [currentNavVC pushViewController:vc animated:YES];
            }
        };
        if(isNeedLogin != nil && isNeedLogin.integerValue == 1){
            [[JCLoginManager sharedInstance] checkLogin:^{
                
            } viewController:current loginSuccessBlock:^{
                toVipDetailBlock();
            }];
        }else{
            toVipDetailBlock();
        }
    }else if([nativeRoute isEqualToString:JC_PersonalCenter]){
      if(current.presentingViewController != nil){
          [current dismissViewControllerAnimated:YES completion:^{
              [self toNativePageWith:nativeRoute fromType:fromType eventTitle:eventTitle];
          }];
          return;
      }else{
          [currentNavVC popToRootViewControllerAnimated:YES];
          dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
              currentTabbarVC.selectedIndex = currentTabbarVC.viewControllers.count -1;
          });
      }
    }else if([nativeRoute isEqualToString:JC_Feedback]){
        JCQAViewController *vc = [[JCQAViewController alloc] init];
        [currentNavVC pushViewController:vc animated:YES];
    }else if([nativeRoute isEqualToString:JC_HelpCenter]){
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        NSString *positionId = @"";
        if([nativeRoute rangeOfString:@"?"].location != NSNotFound){
            NSDictionary *parmsDic = [XYTool dictionaryWithUrlString:nativeRoute];
            positionId = parmsDic[@"positionId"];
        }
        [JCGrayJumpHelper jumpHelpCenterWithGray];
    }else if([nativeRoute isEqualToString:JC_LiveCode]){
        [[JCLoginManager sharedInstance] checkLogin:^{
            
        } viewController:current loginSuccessBlock:^{
            if(NETWORK_STATE_ERROR){
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
                return;
            }
            JCActivityCodeEditViewController *vc = [[JCActivityCodeEditViewController alloc] initWithUrl:LiveCodeUrl];
            [currentNavVC pushViewController:vc animated:YES];
        }];
    }else if([nativeRoute isEqualToString:JC_Silkbag]){
        if(![XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]){
            return;
        }
    }else if([nativeRoute hasPrefix:JC_Login_DirectTo]){
        NSDictionary *parmsDic = [XYTool dictionaryWithUrlString:nativeRoute];
        NSString *newRoute = @"";
        if([parmsDic objectForKey:@"directTo"] != nil){
            newRoute = [parmsDic objectForKey:@"directTo"];
        }
        if(xy_isLogin){
            if([newRoute hasPrefix:@"niimbot://app"]){
                [JCToNativeRouteHelp toNativePageWith:newRoute fromType:fromType eventTitle:eventTitle];
            }else if([newRoute hasPrefix:@"http"]){
                if([current isKindOfClass:[XYWKWebViewController class]]){
                    XYWKWebViewController *vc = (XYWKWebViewController *)current;
                    [vc loadUrl:newRoute];
                }else{
                    XYWKWebViewController *vc = [[XYWKWebViewController alloc] initWithUrl:newRoute];
                    [current.navigationController pushViewController:vc animated:YES];
                }
            }
        }else{
            [[JCLoginManager sharedInstance] checkLogin:^{
                
            } viewController:current loginSuccessBlock:^{
                if([newRoute hasPrefix:@"niimbot://app"]){
                    [JCToNativeRouteHelp toNativePageWith:newRoute fromType:fromType eventTitle:eventTitle];
                }else if([newRoute hasPrefix:@"http"]){
                    if([current isKindOfClass:[XYWKWebViewController class]]){
                        XYWKWebViewController *vc = (XYWKWebViewController *)current;
                        [vc loadUrl:newRoute];
                    }else{
                        XYWKWebViewController *vc = [[XYWKWebViewController alloc] initWithUrl:newRoute];
                        [current.navigationController pushViewController:vc animated:YES];
                    }
                }
            }];
        }
    }else if([nativeRoute hasPrefix:JC_New_UserGuide]){
        NSDictionary *arguments = @{
            @"isPresent": @NO,
            @"isFromAppStart": @0
        };
        [FlutterBoostUtility gotoFlutterPage:@"selectDeviceSeries"
                                   arguments:arguments
                              onPageFinished:^(NSDictionary *_) {
            // 页面结束回传数据
        }];
        
    }else if([nativeRoute hasPrefix:JC_Batch_Print]){
        NSDictionary *arguments = @{@"token": m_userModel.token, @"isPresent": @NO,@"isEnablePopGesture": @NO};
        [FlutterBoostUtility gotoFlutterPage:@"myTemplate"
                                   arguments:arguments
                              onPageFinished:^(NSDictionary *_) {
            // 页面结束回传数据
        }];
    }
    else if([nativeRoute hasPrefix:JC_New_GoodsTemplate]){
        [JCRecordTool recordWithAction:silkbag_template_click withContent:@"silkbag_template_click" isClickEvent:YES parmeters:@{}];
        JCGoodsTemplateViewController *vc = [JCGoodsTemplateViewController new];
        [currentNavVC pushViewController:vc animated:YES];
    }else if([nativeRoute hasPrefix:JC_New_GoodsRepo]){
        [JCRecordTool recordWithAction:silkbag_commodityLibrary_click withContent:@"silkbag_commodityLibrary_click" isClickEvent:YES parmeters:@{}];
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"", @"网络异常")];
            return;
        }
        if(!m_storeTrialTimeIsValite){
            [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01439", @"提醒") message:XY_LANGUAGE_TITLE_NAMED(@"app100000082", @"限时免费体验已结束，为保证您的正常使用，请更新APP到最新版本。") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000081", @"不用了") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00487", @"更新") cancelBlock:nil sureBlock:^{
                [[UIApplication sharedApplication] openURL:XY_URLWithString(app_download_Url) options:@{} completionHandler:^(BOOL success) {}];
            } alertType:3];
            return;
        }
        JCStoreGoodsLibearyViewController *vc = [[JCStoreGoodsLibearyViewController alloc] initWithGoodsListState:0];
        [currentNavVC pushViewController:vc animated:YES];
    }
    else if([nativeRoute hasPrefix:JC_New_MyExcel]){
        [JCRecordTool recordWithAction:silkbag_excel_File_click withContent:@"silkbag_excel_File_click" isClickEvent:YES parmeters:@{}];
        if(!m_storeTrialTimeIsValite){
            [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01439", @"提醒") message:XY_LANGUAGE_TITLE_NAMED(@"app100000082", @"限时免费体验已结束，为保证您的正常使用，请更新APP到最新版本。") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000081", @"不用了") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00487", @"更新") cancelBlock:nil sureBlock:^{
                [[UIApplication sharedApplication] openURL:XY_URLWithString(app_download_Url) options:@{} completionHandler:^(BOOL success) {}];
            } alertType:3];
            return;
        }
        JCExcelListViewController *vc = [[JCExcelListViewController alloc] init];
        vc.improtFrom = 4;
        [currentNavVC pushViewController:vc animated:YES];
    }else if([nativeRoute hasPrefix:JC_New_CustomLabel]){
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"", @"网络异常")];
            return;
        }
        JCLabelCustomizeViewController *vc = [[JCLabelCustomizeViewController alloc] init];
        vc.jumpSource = @"y_page_inner_app";
        [currentNavVC pushViewController:vc animated:YES];
    }else if([nativeRoute isEqualToString:JC_VIPRedeemCode]){
        [[JCLoginManager sharedInstance] checkLogin:^{
            
        } viewController:current loginSuccessBlock:^{
            JCRedeemCodeViewController *vc = [[JCRedeemCodeViewController alloc] init];
            [currentNavVC pushViewController:vc animated:YES];
        }];
    }else if([nativeRoute isEqualToString:JC_NPS_VIP]){
        [[JCLoginManager sharedInstance] checkLogin:^{
            
        } viewController:current loginSuccessBlock:^{
            [FlutterBoostUtility gotoTransparentFlutterPage:@"vipNPSGrade"
                                                  arguments:@{
                @"token": m_userModel.token,
                @"isPresent": @YES,
                @"isAnimated": @NO,
            }
                                             onPageFinished:^(NSDictionary *_) {
                // 页面结束回传数据
            }];
        }];
    }else if([nativeRoute isEqualToString:JC_ETAG]){
        NSDictionary *arguments = @{
            @"isPresent": @NO,
            @"isEnablePopGesture": @NO,
            @"token": UN_NIL(m_userModel.token),
        };
        [FlutterBoostUtility gotoFlutterPage:@"eTag"
                                   arguments:arguments
                              onPageFinished:^(NSDictionary *_) {
            // 页面结束回传数据
            [JCBluetoothManager sharedInstance].deviceType = JCBluetoothNormal;
        }];
    } else if([nativeRoute isEqualToString:JC_New_IndustryTemplate]){
        // 获取标签纸信息
        JCTemplateData *data = [JCPrintManager sharedInstance].rfidTemplateData;
        // 替换为Flutter界面模版入口
        [FlutterBoostUtility gotoFlutterPage:@"industryTemplate"
                                   arguments:@{
            @"labelData": data == nil ? @{} : data.toDictionary,
            @"token": m_userModel.token == nil ? @"" : m_userModel.token,
        }
                              onPageFinished:^(NSDictionary *_) {
            // 页面结束回传数据
        }];
    } else if ([nativeRoute isEqualToString:JC_C1]) {
        NSString *fontPath = [NSString stringWithFormat:@"%@/font", DocumentsFontPath];
        NSDictionary *arguments = @{
            @"fontPath": fontPath,
            @"isEnablePopGesture": @NO
        };
        [FlutterBoostUtility gotoFlutterPage:@"C1"
                                   arguments:arguments
                              onPageFinished:^(NSDictionary *_) {
            // 页面结束回传数据
        }];
    }else if([nativeRoute hasPrefix:JC_New_Canvas]){
        // 获取路有信息
        NSDictionary *vipRoutParms = [XYTool dictionaryWithUrlString:nativeRoute];
        [[JCFlutter2NativeHandler sharedInstance] loadCanvasWithAppRouter:vipRoutParms];
    }else if([nativeRoute hasPrefix:JC_Cable_Canvas]){
        NSString *fontPath = [NSString stringWithFormat:@"%@/font", DocumentsFontPath];
        [self getCableTemplata:^(id x) {
          JCTemplateData *cableTemplate = x;
          NSMutableDictionary *arguments = @{@"jsonData": UN_NIL(cableTemplate.toJSONString),@"isPresent": @NO,@"fontPath": fontPath,
                                             @"isEnablePopGesture": @NO}.mutableCopy;
          if(cableTemplate != nil){
              arguments[@"jsonData"] = UN_NIL(cableTemplate.toJSONString);
          }
          [FlutterBoostUtility gotoFlutterPage:@"cableCanvas" arguments:arguments onPageFinished:^(NSDictionary *dic) {
                                                
          }];
        }];
        
    } else if ([nativeRoute hasPrefix:JC_Mall_Activity]) {
        NSDictionary *routParms = [JCToNativeRouteHelp getUrlParameterWithUrl:nativeRoute];
        UIViewController *topVc = [XYTool getCurrentVC];
        if (NETWORK_STATE_ERROR) {
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        if (!STR_IS_NIL(routParms[@"url"])) {
            JCShopNormalVC *shopVc = [[JCShopNormalVC alloc] initWithShopAppointUrl:routParms[@"url"]];
            shopVc.entrance_type_id = @"3";
            shopVc.jumpSource = @"out_station_active_page";
            shopVc.isNeedBackApp = YES;
            if(topVc.navigationController == nil){
                XYNavigationController *nav = [[XYNavigationController alloc] initWithRootViewController:shopVc];
                [topVc presentViewController:nav animated:YES completion:^{
                    
                }];
            }else{
                [topVc.navigationController pushViewController:shopVc animated:YES];
            }
        }
    }else {
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100001824", @"暂不支持此消息")];
    }
}

+ (void)getRecentTemplateData:(XYBlock)recentCableBlock{
    __block JCTemplateData *recentCableTemplate = nil;
    NSString *whereUseModel = [NSString stringWithFormat:@"where userId = '%@'",m_userModel.userid];
    NSArray *labelUseModelArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_LABEL_USE_INFO dicOrModel:[JCLabelUseModel class] whereFormat:whereUseModel];
    NSSortDescriptor*sorter=[[NSSortDescriptor alloc]initWithKey:@"useTime" ascending:NO];
    NSMutableArray *sortDescriptors=[[NSMutableArray alloc]initWithObjects:&sorter count:1];
    NSArray *sortlabelUseModelArr = [labelUseModelArr sortedArrayUsingDescriptors:sortDescriptors].mutableCopy;
    if(sortlabelUseModelArr.count > 0){
      JCLabelUseModel *firstLabelUseModel = sortlabelUseModelArr.firstObject;
      [JCTemplateFunctionHelper getTemplateDetailRequestById:firstLabelUseModel.templateId complate:^(JCTemplateData *templateData) {
        recentCableBlock(templateData);
      } needLoading:NO];
    }else{
      recentCableBlock(nil);
    }
}

+ (void)getCableTemplata:(XYBlock)cableBlock{
  JCTemplateData *cableTemplata = nil;
  JCTemplateData *rfidCableTemplata = [JCPrintManager sharedInstance].rfidTemplateData;
  [self getRecentTemplateData:^(id cableTemplata) {
    
    if(rfidCableTemplata != nil && rfidCableTemplata.isCableLabel){
        cableTemplata = rfidCableTemplata;
    }
    cableBlock(cableTemplata);
  }];
}

+ (NSDictionary *)getUrlParameterWithUrl:(NSString *)url {
    NSMutableDictionary *parm = [[NSMutableDictionary alloc]init];
    //传入url创建url组件类
    NSURLComponents *urlComponents = [[NSURLComponents alloc] initWithString:url];
    //回调遍历所有参数，添加入字典
    [urlComponents.queryItems enumerateObjectsUsingBlock:^(NSURLQueryItem * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        [parm setObject:obj.value forKey:obj.name];
    }];
    return parm;
}

+ (void)bannberJumpWith:(NSString *)typeCode
            routeString:(NSString *)routString
                  title:(NSString *)title
               sourceVC:(UIViewController *)sourceVc{
    if([typeCode isEqualToString:@"3"]){
        JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopAppointUrl:routString];
        vc.entrance_type_id = @"3";
        vc.jumpSource = @"y_page_user_vip_detail";
        [sourceVc.navigationController pushViewController:vc animated:YES];
    }else if([typeCode isEqualToString:@"2"]){
        NSURL *url = [NSURL URLWithString:routString];
        if([[UIApplication sharedApplication] canOpenURL:url]) {
            [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
        }else{
            NSURL *URL = [NSURL URLWithString:routString];
            [[UIApplication sharedApplication] openURL:URL options:@{} completionHandler:^(BOOL success) {
                //  回调
            }];
        }
    }else if([routString hasPrefix:@"niimbot://app"]){
        [JCToNativeRouteHelp toNativePageWith:routString fromType:JC_Store_Banner eventTitle:title];
    }else{
        XYWKWebViewController *vc = [[XYWKWebViewController alloc] initWithUrl:routString];
        vc.title = title;
        if(STR_IS_NIL(title)){
            vc.showWebTitle = YES;
        }
        XYNavigationController *nav = [[XYNavigationController alloc] initWithRootViewController:vc];
        if([[NBCAPMiniAppManager sharedInstance] hasUniAppActivity]){
            [sourceVc presentViewController:nav animated:YES completion:^{
                
            }];
        }else{
            [sourceVc.navigationController pushViewController:vc animated:YES];
        }
    }
}

+ (void)jumpToCYAppWith:(NSString *)schemePath appstore:(NSString*)appStoreUrl action:(NSString*)actName{
    JCOtherAppModel *appInfo = [[JCOtherAppModel alloc] init];
    appInfo.title = XY_LANGUAGE_TITLE_NAMED(@"", @"您将前往【臣小印】App");
    appInfo.iconName = @"jc_cy_app_icon";
    appInfo.descrp = XY_LANGUAGE_TITLE_NAMED(@"", @"臣小印，一款面向家用市场推出的集标签打印、社区分享、实物收纳于一体的产品。上万款标签模板、海量素材、创意功能让标签编辑变得更简单。来社区一起共创有温度、更好玩的标记新世界。");
    appInfo.schemePath = schemePath;
    appInfo.appStoreUrl = appStoreUrl;
    appInfo.confirmText = @"前往";
    appInfo.isFromAct = YES;
    appInfo.actName = actName;
    JCOtherAppAlert *otherAppAlert = [[JCOtherAppAlert alloc] initWithAppInfo:appInfo];
    [otherAppAlert showContentAlert];
}

+ (void)jumpToOtherAppWithAppModel:(JCLabelAppModel*)appModel{
    NSString *schemePath = @"";
    NSString *appStoreUrl = @"";
    NSString *downLoadUrl = @"";
    NSString *titleCN = @"";
    NSString *confirmText = XY_LANGUAGE_TITLE_NAMED(@"app100000709", @"前往");
    NSString *title = XY_LANGUAGE_TITLE_NAMED(appModel.dialogTitle, @"");
    NSString *descrp = XY_LANGUAGE_TITLE_NAMED(appModel.dialogMessage, @"");
    NSString *iconUrl = [appModel.icon containsString:@"http"]?appModel.icon:XY_LANGUAGE_TITLE_NAMED(appModel.icon, @"");
    if([appModel.packageName containsString:@"jxc"]){
        schemePath = JC_JXC_SCHEME;
        appStoreUrl = JC_JXC_APP_STORE;
    }else if([appModel.name isEqualToString:@"app100001963"]){
      schemePath = JC_GZ_SCHEME;
      appStoreUrl = JC_GZ_APP_STORE;
      titleCN = @"精臣云资产";
      // 从Flutter端获取授权码
      // url为空不加载授权码
      if (STR_IS_NIL(appModel.router)) {
        return;
      }
      [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"getAuthCode" arguments:nil result:^(id value) {
        if (!STR_IS_NIL(value)) {
          NSString *loadUrl = [NSString stringWithFormat:@"%@?authCode=%@",appModel.router,value];
#ifdef DEBUG
          NSString *assetsHost = [[NSUserDefaults standardUserDefaults] stringForKey:@"LaboratoryTypeAssetsDomain"];
          if (assetsHost) {
            loadUrl = [NSString stringWithFormat:@"%@?authCode=%@",assetsHost,value];
          }
#endif
          NMFAWebViewController *webViewController = [[NMFAWebViewController alloc] init];
          webViewController.isSupportShare = NO;
          [webViewController loadUrl:loadUrl];
          UIViewController *topVc = [XYTool getCurrentVC];
          [topVc.navigationController pushViewController:webViewController animated:YES];
        }
      }];
      return;
    }else if([appModel.packageName containsString:@"com.niimbot.cxprinter"]){
        schemePath = JC_CXY_SCHEME;
        appStoreUrl = JC_CXY_APP_STORE;
        titleCN = @"臣小印";
    }
    else if([appModel.packageName containsString:@"com.niimbot.pc"]){
        downLoadUrl = XY_LANGUAGE_TITLE_NAMED(@"app100001762", @"www.niimbot.com/cnweb/pc.html");
        confirmText = XY_LANGUAGE_TITLE_NAMED(@"app100001763", @"复制下载链接");
        titleCN = @"云打印电脑端";
        if(STR_IS_NIL(title)) title = @"【精臣云打印】电脑端";
        if(STR_IS_NIL(descrp)) descrp = @"精臣云打印电脑端支持精臣全系列商用智能标签打印机，目前仅支持Windows系统安装使用。";
        if(STR_IS_NIL(iconUrl)) iconUrl = @"https://oss-print.niimbot.com/public_resources/material/dcdde35f15dc61e0bf2b6ba24040056c.png";
    }
    JC_TrackWithparms(@"show",@"004_308",(@{@"b_name":titleCN}));
    JCOtherAppModel *appInfo = [[JCOtherAppModel alloc] init];
    appInfo.title = title;
    appInfo.titleCN = titleCN;
    appInfo.iconName = iconUrl;
    appInfo.descrp = descrp;
    appInfo.schemePath = schemePath;
    appInfo.appStoreUrl = appStoreUrl;
    appInfo.downloadUrl = downLoadUrl;
    appInfo.confirmText = confirmText;
    JCOtherAppAlert *otherAppAlert = [[JCOtherAppAlert alloc] initWithAppInfo:appInfo];
    [otherAppAlert showContentAlert];
}
@end
