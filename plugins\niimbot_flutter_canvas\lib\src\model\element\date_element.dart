import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:intl/intl.dart';
import 'package:niimbot_flutter_canvas/src/model/date_element_helper.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element_style.dart';

import '../../localization/localization_public.dart';
import 'package:intl/intl.dart' as intl;

class DateElement extends TextElement {
  String? dateFormat;
  String? timeFormat;
  int? time;
  int dateIsRefresh;
  @override
  String? value;
  // String? contentTitle;
  int timeOffset = 0;
  int associateType = 0;
  bool associated = false;
  String associateId = "";
  int validityPeriod;
  String validityPeriodUnit;
  String timeUnit = "";
  int get validityPeriodNew {
    int validityPeriodNewValue = validityPeriod;
    if (validityPeriodUnit == DateElementHelper.Associated_Day) {
      validityPeriodNewValue = validityPeriod - 1;
    }
    return validityPeriodNewValue;
  }

  set validityPeriodNew(int value) {
    validityPeriod = value;
    if (validityPeriodUnit == DateElementHelper.Associated_Day) {
      validityPeriod = value + 1;
    }
  }

  DateElement(
      {required super.id,
      required super.x,
      required super.y,
      required super.width,
      required super.height,
      super.zIndex,
      required this.value,
      super.rotate,
      super.isLock,
      super.mirrorId,
      super.isOpenMirror,
      super.mirrorType,
      super.typesettingMode,
      super.typesettingParam,
      super.textAlignHorizontal,
      super.textAlignVertical,
      required super.lineMode,
      required super.wordSpacing,
      required super.letterSpacing,
      required super.lineSpacing,
      super.fontFamily,
      super.fontCode,
      required super.fontStyle,
      required super.fontSize,
      super.fontColor,
      super.fieldName,
      super.isTitle,
      // this.contentTitle,
      this.dateFormat,
      this.timeFormat,
      this.associateType = 0,
      required this.time,
      required this.dateIsRefresh,
      this.timeOffset = 0,
      this.associated = false,
      this.associateId = "",
      this.validityPeriod = 24,
      this.validityPeriodUnit = DateElementHelper.Associated_Hour,
      this.timeUnit = "",
      super.contentTitle = "",
      super.isEditing = false,
      super.boxStyle,
      super.textStyle})
      : super(type: ElementItemType.date, value: '');

  DateElement.fromJson(Map<String, dynamic> json)
      : dateIsRefresh = json['dateIsRefresh'],
        value = _parseValue(json['value']),
        time = json['time'] == null || json['time'] == '' ? 0 : ((json['time'] is int) ? json['time'] : 0),
        dateFormat = json['dateFormat']?.toString().trim().replaceAll("无", "").replaceAll("NO", ""),
        timeFormat = json['timeFormat']?.toString().trim().replaceAll("无", "").replaceAll("NO", ""),
        associated = json['associated'] == null
            ? false
            : ((json['associated'] is int) ? json['associated'] == 1 : json['associated']),
        timeOffset = json['timeOffset'] ?? 0,
        associateId = json['associateId'] ?? "",
        validityPeriod = json['validityPeriod'] ?? 24,
        validityPeriodUnit = json['validityPeriodUnit'] ?? DateElementHelper.Associated_Hour,
        timeUnit = json['timeUnit'] ?? "",
        associateType = json['associateType'] ?? 0,
        super.fromJson(json) {
    if (time == 0) {
      try {
        time = int.parse(value!.replaceAll("#", ""));
      } catch (e) {
        time = DateTime.now().millisecondsSinceEpoch;
      }
    }
  }

  @override
  Map<String, dynamic> toJson() {
    /// 按指定格式处理时间
    List<String> formats = [];
    var showDate = '';
    var isSave = false;
    if (this.value!.contains("#")) {
      isSave = true;
    } else {
      isSave = false;
    }
    this.value = this.time.toString();
    showDate = this.value ?? '';
    // 判断日期是否开启，如果开启，则将日期格式添加到formats列表中
    if (isDateOpen()) {
      formats.add(this.dateFormat!);
    }
    // 判断时间是否开启，如果开启，则将时间格式添加到formats列表中
    if (isTimeOpen()) {
      formats.add(this.timeFormat!);
    }
    String? languageType = CanvasPluginManager().hostMethodImpl?.getCurrentLanguageType();
    if (languageType == "zh-cn") {
      languageType = "zh_CN";
    } else if (languageType == "zh-cn-t") {
      languageType = "zh_TW";
    } else {
      languageType = "en_US";
    }

    /// * `"20120227T132700"`
    // String standardValue = '${value.substring(0, 8)}T${value.substring(8, 14)}';
    // DateTime dateTime = DateTime.tryParse(standardValue) ?? DateTime.now();

    /**
     * 根据是否启用刷新日期的标志，更新当前对象的时间属性为当前时间。
     * 这个代码块没有参数。
     * 没有直接的返回值，但可能会修改当前对象的【time】属性。
     */
    if (isRefreshDateEnable()) {
      this.time = DateTime.now().millisecondsSinceEpoch;
    }

    // 根据关联状态和时间偏移计算最终的日期时间。
    DateTime dateTime;
    if (this.associateId.isEmpty && !this.associated || this.associateId.isNotEmpty && this.associated) {
      // 当关联ID为空且未关联，或关联ID不为空且已关联时，直接根据时间戳和时间偏移计算日期时间。
      dateTime = DateTime.fromMillisecondsSinceEpoch((this.time ?? 0) + (this.timeOffset ?? 0) * 60 * 1000);
    } else {
      // 当关联ID和关联状态不满足上述条件时，需要根据有效期单位来进一步计算日期时间。
      DateTime validityDateTime;
      validityDateTime = DateTime.fromMillisecondsSinceEpoch((this.time ?? 0) + (this.timeOffset ?? 0) * 60 * 1000);
      // 根据有效期单位分别计算新的日期时间。
      if (this.validityPeriodUnit == DateElementHelper.Associated_Hour) {
        // 如果有效期单位为小时，则仅增加小时数。
        validityDateTime = DateTime(
            validityDateTime.year,
            validityDateTime.month,
            validityDateTime.day,
            validityDateTime.hour + this.validityPeriodNew,
            validityDateTime.minute,
            validityDateTime.second,
            validityDateTime.millisecond,
            validityDateTime.microsecond);
      } else if (this.validityPeriodUnit == DateElementHelper.Associated_Day) {
        // 如果有效期单位为天，则增加天数。
        validityDateTime = DateTime(
            validityDateTime.year,
            validityDateTime.month,
            validityDateTime.day + this.validityPeriodNew,
            validityDateTime.hour,
            validityDateTime.minute,
            validityDateTime.second,
            validityDateTime.millisecond,
            validityDateTime.microsecond);
      } else if (this.validityPeriodUnit == DateElementHelper.Associated_month) {
        // 如果有效期单位为月，则增加月数，需要处理跨月问题。
        int day = daysInMonth(validityDateTime.year, validityDateTime.month + this.validityPeriodNew);
        if (validityDateTime.day <= day) {
          day = validityDateTime.day;
        }
        validityDateTime = DateTime(
            validityDateTime.year,
            validityDateTime.month + this.validityPeriodNew,
            day,
            validityDateTime.hour,
            validityDateTime.minute,
            validityDateTime.second,
            validityDateTime.millisecond,
            validityDateTime.microsecond);
      } else if (this.validityPeriodUnit == DateElementHelper.Associated_Year) {
        // 如果有效期单位为年，则增加年数，需要处理跨年问题。
        int day = daysInMonth(validityDateTime.year + this.validityPeriodNew, validityDateTime.month);
        if (validityDateTime.day <= day) {
          day = validityDateTime.day;
        }
        validityDateTime = DateTime(
            validityDateTime.year + this.validityPeriodNew,
            validityDateTime.month,
            day,
            validityDateTime.hour,
            validityDateTime.minute,
            validityDateTime.second,
            validityDateTime.millisecond,
            validityDateTime.microsecond);
      }
      // 计算有效期的毫秒数，进而计算最终的日期时间。
      var validityPeriodNew = validityDateTime.millisecondsSinceEpoch - (this.time ?? 0);
      dateTime = DateTime.fromMillisecondsSinceEpoch((this.time ?? 0) + validityPeriodNew);
    }

    // 当不采用紧凑旧模板时，使用指定的格式列表对日期时间进行格式化
    if (!compactOldTemplate) {
      showDate =
          DateFormat(formats.join(' '), formats.join(' ').contains("EEE") ? languageType : "en_US").format(dateTime);
    }

    // 判断并设置时间单位为"morning"或"afternoon"，并根据时间单位调整dateTime对象的小时值
    if (this.timeUnit.isNotEmpty && isTimeOpen()) {
      // 判断是上午还是下午
      if (dateTime.hour > 11) {
        this.timeUnit = "afternoon";
      } else {
        this.timeUnit = "morning";
      }

      // 如果是下午且时间为12点，将时间调整为下午12点之后的第一个小时
      if (this.timeUnit == "afternoon" && dateTime.hour == 0) {
        dateTime = DateTime(dateTime.year, dateTime.month, dateTime.day, dateTime.hour + 12, dateTime.minute,
            dateTime.second, dateTime.millisecond, dateTime.microsecond);
      }

      // 处理下午的时间格式化
      if (dateTime.hour > 11 && this.timeUnit == "afternoon") {
        // 如果当前小时不是12点，则将小时值减去12，调整为下午的时间
        if (dateTime.hour != 12) {
          dateTime = DateTime(dateTime.year, dateTime.month, dateTime.day, dateTime.hour - 12, dateTime.minute,
              dateTime.second, dateTime.millisecond, dateTime.microsecond);
        }

        // 根据本地化字符串是否包含中文，选择不同的格式化方法
        if (formats.length > 1) {
          var time =
              DateFormat(timeFormat, (timeFormat ?? "").contains("EEE") ? languageType : "en_US").format(dateTime);
          if ((timeFormat?.startsWith('H')??false) && (time.startsWith("0") && time.length > 1)) {
            time = time.replaceFirst("0", "");
          }
          showDate =
              DateFormat(dateFormat, (dateFormat ?? "").contains("EEE") ? languageType : "en_US").format(dateTime) +
                  " " +
                  intlanguage('app100001460', '下午\$', param: [time]);
        } else {
          showDate =
              DateFormat(formats.join(''), formats.join(' ').contains("EEE") ? languageType : "en_US").format(dateTime);
          showDate = intlanguage('app100001460', '下午\$', param: [showDate]);
        }
        // 处理上午的时间格式化
      } else {
        // 如果时间为0点，调整为上午12点之后的第一个小时
        if (dateTime.hour == 0) {
          dateTime = DateTime(dateTime.year, dateTime.month, dateTime.day, dateTime.hour + 12, dateTime.minute,
              dateTime.second, dateTime.millisecond, dateTime.microsecond);
        }
        if (formats.length > 1) {
          var time =
              DateFormat(timeFormat, (timeFormat ?? "").contains("EEE") ? languageType : "en_US").format(dateTime);
          if ((timeFormat?.startsWith('H') ?? false) && (time.startsWith("0") && time.length > 1)) {
            time = time.replaceFirst("0", "");
          }
          showDate =
              DateFormat(dateFormat, (dateFormat ?? "").contains("EEE") ? languageType : "en_US").format(dateTime) +
                  " " +
                  intlanguage('app100001461', '上午\$', param: [time]);
        } else {
          showDate =
              DateFormat(formats.join(''), formats.join(' ').contains("EEE") ? languageType : "en_US").format(dateTime);
          showDate = intlanguage('app100001461', '上午\$', param: [showDate]);
        }
      }
    }

    // 判断contentTitle是否非空且不包含"-save-"，如果满足条件，则在当前value前添加contentTitle
    if (contentTitle != null && contentTitle?.isNotEmpty == true && !contentTitle!.contains("-save-")) {
      showDate = contentTitle! + intlanguage('app100001507', ':') + showDate;
    }
    // 如果contentTitle非空且包含"-save-"，则将"-save-"替换为空字符串
    if (contentTitle != null && contentTitle?.isNotEmpty == true && contentTitle!.contains("-save-")) {
      this.contentTitle = this.contentTitle?.replaceAll("-save-", "");
    }

    final Map<String, dynamic> data = new Map<String, dynamic>();
    data.addAll(super.toJson());
    data['associateType'] = this.associateType;
    data['value'] = isSave ? this.value : showDate;
    data['dateFormat'] = this.dateFormat;
    data['timeFormat'] = this.timeFormat;
    data['time'] = this.time;
    data['dateIsRefresh'] = this.dateIsRefresh;
    data['contentTitle'] = this.contentTitle ?? "";
    data['associated'] = this.associated ?? false;
    data['timeOffset'] = this.timeOffset ?? 0;
    data['associateId'] = this.associateId ?? "";
    data['validityPeriod'] = this.validityPeriod ?? 24; // 时间单位分钟
    data['validityPeriodNew'] = this.validityPeriodNew ?? 24; // 新版有效期
    data['validityPeriodUnit'] = this.validityPeriodUnit ?? DateElementHelper.Associated_Hour;
    data['timeUnit'] = this.timeUnit ?? "";
    completionFontDefault(data);

    /// 文本样式
    textStyleToJson(data);
    return data;
  }

  int daysInMonth(int year, int month) {
    // 构造指定年份和月份的DateTime对象
    DateTime firstDayOfMonth = DateTime(year, month);
    DateTime lastDayOfMonth = DateTime(year, month + 1, 0);

    // 计算天数
    int days = lastDayOfMonth.day;

    return days;
  }

  @override
  Map<String, dynamic> antiEscapeValueToJson() {
    Map<String, dynamic> data = toJson();
    data['value'] = this.time.toString();
    return data;
  }

  String getSelectorDateFormat() {
    if (dateFormat?.isEmpty ?? true) {
      return DateElementHelper.getDateFormat();
    }
    return dateFormat!;
  }

  String getSelectorTimeFormat() {
    if (timeFormat?.isEmpty ?? true) {
      String format = DateElementHelper.getTimeFormat();
      if (format.isEmpty) {
        return "HH:mm";
      }
      return format;
    }
    return timeFormat!;
  }

  @override
  bool hasVipSource() {
    if (super.hasVipSource() || isRefreshDateEnable()) {
      return true;
    }
    return false;
  }

  @override
  bool isBindingExcel() {
    return false;
  }

  /// 是否展示实时时间，需要同时满足：
  /// 1、当前为vip用户
  /// 2、实时时间开关打开
  bool isRefreshDateEnable() {
    if (dateIsRefresh != 1) {
      return false;
    }
    return true;
  }

  bool isDateOpen() {
    return (this.dateFormat ?? '').length > 0;
  }

  bool isTimeOpen() {
    return (this.timeFormat ?? '').length > 0;
  }
}

int _parseTime(dynamic field, dynamic value) {
  var time = field ?? 0;
  if (time == 0) {
    try {
      time = int.parse(value);
    } catch (e) {
      time = DateTime.now().millisecondsSinceEpoch;
    }
  }
  return time;
}

String _parseValue(dynamic field) {
  if (field is int) {
    field = field.toString();
  }

  /// 字段兼容处理
  if ((field ?? '').contains('T')) {
    try {
      return intl.DateFormat('yyyyMMddHHmmss').format(DateTime.fromMillisecondsSinceEpoch(int.parse(field)));
    } catch (_) {
      return field;
    }
  }
  return field;
}
