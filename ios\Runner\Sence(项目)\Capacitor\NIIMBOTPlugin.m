//
//  NIIMBOTPlugin.m
//  Runner
//
//  Created by 杨瑞 on 2022/12/21.
//

#import <Foundation/Foundation.h>
#import <objc/runtime.h>
#import <Capacitor/Capacitor.h>
#import <Capacitor/Capacitor-Swift.h>
#import <Capacitor/CAPBridgedPlugin.h>
#import <Capacitor/CAPBridgedJSTypes.h>

#import "QQLBXScanViewController.h"
#import "JCTemplateImageManager.h"
#import "JCElementModel+Transfer.h"
#import "JCActivityCodeModel.h"
#import "JCFontManager.h"
#import "StyleDIY.h"
#import "Global.h"
#import "JCLabelAppModel.h"
#import "JCOtherAppAlert.h"
#import "ImageLibraryBridge.h"
#import "ImageTool.h"
#import "JCDeviceSeriesHelp.h"
#import "JCVipTipView.h"
#import "JCShareTool.h"
#import <MobileCoreServices/MobileCoreServices.h>

#import "NIIMBOTPlugin.h"
#import "NBCAPMiniAppEngine.h"
#import "NBCAPMiniAppManager.h"
#import "JCActivityCodeHelp.h"
#import "JCActivityCodeModel.h"
#import "XYWKWebViewController.h"
#import "XYTool.h"
#import "WXApi.h"
#import "JCShopNormalVC.h"
#import "CapacitorPrintHelper.h"
#import "JCBluetoothManager.h"
#import "JCTMDataBindGoodsInfoManager.h"
#import "JCTemplateImageManager.h"
#import "NotificationMacro.h"

/*
 * 实现 Capacitor 原生能力支持
 * 插件定义参见
 * https://whjc.yuque.com/fq7i4s/wdsc38/wrck8ghf860ro57m
 */

#define PENDING_CLIPBOARD_KEY @"pendingClipboardDataForMiniApp" // Use the same key as in AppDelegate

static NSString * _Nonnull meeting_ionic_id = @"__CAP__SPR889G";

@interface NIIMBOTPlugin() <UIDocumentPickerDelegate>

@property (nonatomic, strong) NSString *appId;

@property (nonatomic, strong) NSString *currentUploadItemId;
@property (nonatomic, assign) NSInteger channelType;              // 文件上传渠道
@property (nonatomic, strong) NSString *uploadEventId;            // 上传文件事件Id

@property (nonatomic, copy) XYBlock saveFileToSystemFolder;      // 保存文件到系统的结果回调函数

@property(nonatomic,strong)CAPPluginCall * toDoCall; //由于某些条件中断的任务 存储起来 目前的业务只涉及到单次任务

@property(nonatomic,strong)NSNumber *taskId;

@property(nonatomic,strong) NSString *currentUniAppId;

@property(nonatomic,assign)BOOL changeRfidEnable;

@property(nonatomic,strong) MBProgressHUD  *progressHUD;

@property(nonatomic,strong) NSDictionary *wifiCodeInfo;

@property(nonatomic,strong) JCTemplateData *wifiTemplateData;

@property(nonatomic,strong) JCTemplateData *currentPreviewTemplateData;

@property(nonatomic,assign) NSInteger notSaveToHippo;

@property(nonatomic,assign)BOOL isUploadLimit;
/// 是否正在加载危废打印页
@property(nonatomic,assign) BOOL isLoadingDangerCapPrintPage;
/// 是否正在处理批量打印
@property(nonatomic,assign) BOOL isProcessingBatchPrint;

/// 上传额外字段
@property(nonatomic,strong) NSDictionary *uploadExtraParams;

@end

@implementation NIIMBOTPlugin
static BOOL isResiveNotification = NO;
- (id)initWithBridge:(id<CAPBridgeProtocol>)bridge pluginId:(NSString *)pluginId pluginName:(NSString *)pluginName {
  self = [super initWithBridge:bridge pluginId:pluginId pluginName:pluginName];
  if (self) {
    self.isUploadLimit = YES;
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshVipOperateStatus:)
                                                 name:JCNOTICATION_VIP_OPERATE_SUCCESS
                                               object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(upLoadPressNotification:)
                                                 name:JCNOTICATION_UPLOADPRESS object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(fileNotification:)
                                                 name:JCNOTICATION_EXCEL_UPLOAD object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(miniAppBroadcast:)
                                                 name:JCNOTICATION_MINI_APP_BROADCAST object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(cleanNotification:)
                                                 name:JCNOTICATION_CLEAN object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(printerStatusNotification:) name:PrinterStatusNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(rfidGotLabelInfoNotification:) name:RfidGotLabelInfoNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(rfidPrinterCoverOpenNotification:) name:PrinterCoverOpenNotification object:nil];
    // 半屏弹窗预览图刷新通知
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(JCPrintSettingAlertPreviewImageRefresh:) name:@"JCPrintSettingAlertPreviewImageRefresh" object:nil];
    // 查看台账通知
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(checkLedgerNotification:) name:@"checkLedgerNotification" object:nil];

    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(nativeGestureEvent) name:@"NBCAPScreenEdgePanNotification" object:nil];
    // Add observer for clipboard notifications
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(clipboardNotification:) name:@"JCNOTICATION_MINI_APP_CLIPBOARD" object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(rfidColorRefreshPreview:) name:JCNOTICATION_ELEMENT_REFRESH object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReciveSynchNotification) name:JCNOTICATION_SYNCH_CHANGE object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(machineAliasChangeNotification:) name:JCMachineAliasChangeNotification object:nil];
  }
  return self;
}

- (void)dealloc {
  [[NSNotificationCenter defaultCenter] removeObserver:self];
}

/**
 * 获取启动参数
 * */
- (void)getLaunchConfiguration:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];

  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }

  self.appId = appId;

  // Check for pending clipboard data if it's the target app
  if ([appId isEqualToString:meeting_ionic_id]) {
    NSString *pendingCode = [[NSUserDefaults standardUserDefaults] stringForKey:PENDING_CLIPBOARD_KEY];
    if (pendingCode != nil && pendingCode.length > 0) {
      NSLog(@"[Capacitor Plugin] Found pending clipboard code for %@: %@", appId, pendingCode);
      // Remove the data from UserDefaults before sending event to prevent duplicate sends
      [[NSUserDefaults standardUserDefaults] removeObjectForKey:PENDING_CLIPBOARD_KEY];
      // Send the event
      [self notifyListeners:@"clipboard-update" data:@{@"data": pendingCode}];
    }
  }

  // 关闭原生加载动画
  dispatch_async(dispatch_get_main_queue(), ^{
    [[NBCAPMiniAppEngine sharedInstance] dismissLaunchLoading:appId];
  });

  NSDictionary *extra = [[NBCAPMiniAppEngine sharedInstance] getAppLaunchExtra:appId];

  // 刷新 token
  NSMutableDictionary *extraMutable = [[NSMutableDictionary alloc] initWithDictionary:extra ?: @{}];
  NSString *token = [NSString stringWithFormat:@"bearer %@", m_userModel.token];
  [extraMutable setValue:UN_NIL(token) forKey:@"token"];
  [extraMutable setValue:UN_NIL(UN_NIL(JC_CURRENT_CONNECTED_PRINTER)) forKey:@"machineName"];
  [extraMutable setValue:UN_NIL([JCBluetoothManager sharedInstance].connectedModel.alias) forKey:@"machineAlias"];
  [call resolve:extraMutable];
}

/**
 * 退出小程序
 * @param options.type kill:杀掉小程序 hide:隐藏小程序(保活)
 * */
- (void)quit:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  NSString *type = [call getString:@"type" defaultValue:@""];

  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }

  NBCAPAlive aliveMode = NBCAPAliveNone;
  if ([[type lowercaseString] isEqualToString:@"kill"]) {
    aliveMode = NBCAPAliveWeak;
    [[NSNotificationCenter defaultCenter] removeObserver:self];
  } else if ([[type lowercaseString] isEqualToString:@"hide"]) {
    aliveMode = NBCAPAliveStrong;
  }
  //JCUONICAPP_QUIT
  [[NSNotificationCenter defaultCenter] postNotificationName:JCUONICAPP_QUIT
                                                      object:nil
                                                    userInfo:nil];
  dispatch_async(dispatch_get_main_queue(), ^{
    [[NBCAPMiniAppEngine sharedInstance] quitMiniApp:appId
                                           aliveMode:aliveMode
                                            animated:YES
                                            complete:^(BOOL result) {
      self.currentUniAppId = @"";
      [call resolve];
    }];
  });
}

-(void)removeAppListener{
  [[NSNotificationCenter defaultCenter] removeObserver:self];
}

/**
 * 下载文件
 * @param options.url 下载地址
 * @param options.name 指定下载文件名(带文件类型尾缀), 指定即使用当前名存储
 * @param options.eventId 事件id
 * @param options.type 类型 update:更新包 album:照片/图片 file:文件
 * */
- (void)downloadFile:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  NSString *mUrl = [call getString:@"url" defaultValue:@""];
  NSString *name = [call getString:@"name" defaultValue:@""];
  NSString *eventId = [call getString:@"eventId" defaultValue:@""];
  NSString *type = [call getString:@"type" defaultValue:@""];


  if ([appId length] == 0 ||
      [mUrl length] == 0 ||
      [eventId length] == 0 ||
      [type length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }

  NSString *url = mUrl;
  NSData *originalData = nil;
  if([mUrl hasPrefix:@"http"] || [mUrl hasPrefix:@"https"]){

  }else if([mUrl hasPrefix:@"data:"] && [mUrl length] > [@"data:" length]){
    //这里主要是为了解析类似 【data:image/png;base64, (base64内容)] 格式的数据流格式字符串
    NSString *nonHeaderString = [mUrl substringFromIndex:[@"data:" length]];
    if([nonHeaderString hasPrefix:@"image/"]){
      NSRange flagRange = [mUrl rangeOfString:@","];
      NSUInteger sepIndex = flagRange.location + flagRange.length;
      NSString *base64Str = [mUrl substringFromIndex:sepIndex];
      originalData = [base64Str jk_base64DecodedData];
    }
  }else{
    if([mUrl isBase64Data]){
      url = [mUrl base64Decode];
    }
  }

  // 更新包下载
  NSString *fileName;
  NSString *filePath;

  XYWeakSelf
  if ([type isEqualToString:@"update"]) {
    // 更新包下载
    fileName = [NSString stringWithFormat:@"%@@%@.wgt",
                appId,
                [@([[NSDate date] timeIntervalSince1970]) description]];
    filePath = [NSString stringWithFormat:@"%@/%@",
                RESOURCE_CAPAPP_RESOURCE_PATH,
                fileName];

  } else if ([type isEqualToString:@"album"]) {
    // 图片保存到相册
    fileName = [NSString stringWithFormat:@"%@@%@.png",
                appId,
                [@([[NSDate date] timeIntervalSince1970]) description]];
    filePath = [NSString stringWithFormat:@"%@/%@",
                RESOURCE_CAPAPP_RESOURCE_PATH,
                fileName];
  } else if ([type isEqualToString:@"file"]) {
    // 文件保存到手机系统
    if ([name length] > 0) {
      fileName = name;
    } else {
      fileName = [[NSURL URLWithString:url] lastPathComponent];
    }

    filePath = [NSString stringWithFormat:@"%@/%@",
                RESOURCE_CAPAPP_RESOURCE_PATH,
                fileName];
  } else {
    [call reject:@"不识别的事件 type" :nil :nil :@{}];
    return;
  }

  if(originalData){
    [weakSelf saveFileData:originalData
                  filePath:filePath
                  fileName:fileName
                      call:call];
    return;
  }

  // 执行下载
  [@{} xy_downLoadFileWithUrlString:url
                           savePath:filePath
                            success:^(__kindof YTKBaseRequest *request, id model) {
    NSData *fileData = [NSData dataWithContentsOfFile:filePath];
    NSString *fileMd5 = fileData.MD5String;

    NSLog(@"[Capacitor] 文件下载成功: %@", [@{
      @"appId": appId,
      @"eventId": eventId,
      @"size": @([fileData length]),
      @"status": @"success",
      @"progress": @(100),
      @"path": filePath,
      @"key": fileMd5,
      @"name": fileName
    } xy_toJsonString]);

    [weakSelf saveFileData:fileData
                  filePath:filePath
                  fileName:fileName
                      call:call];

  } failure:^(NSString *msg, id model) {
    // 下载失败
    [weakSelf notifyListeners:@"download-file"
                         data:@{
      @"appId": appId,
      @"eventId": eventId,
      @"size": @0,
      @"status": @"fail",
      @"progress": @(0),
      @"path": filePath,
      @"key": @"",
      @"name": fileName
    }];
  } progressBlock:^(NSProgress * _Nonnull progress) {
    // 下载中
    float percent = progress.fractionCompleted;
    [weakSelf notifyListeners:@"download-file"
                         data:@{
      @"appId": appId,
      @"eventId": eventId,
      @"size": @([progress totalUnitCount]),
      @"status": @"downloading",
      @"progress": @((int)(percent * 100)),
      @"path": filePath,
      @"key": @"",
      @"name": fileName
    }];
  } downloadBlock:^(__kindof YTKBaseRequest *request) {
  }];
}

- (void)updatePrintTemplate:(CAPPluginCall *)call{
  dispatch_async(dispatch_get_main_queue(), ^{
    NSDictionary *templateDataStr = [call getObject:@"template" defaultValue:@{}];
    JCTemplateData *currentData = nil;
    if(templateDataStr != nil){
      currentData = [[JCTemplateData alloc] initWithDictionary:templateDataStr error:nil];
    }
    [DrawBoardInfo updateInfoWith:currentData];
    NSLog(@"%ld",currentData.rotate);
    NSString *fontPath = [NSString stringWithFormat:@"%@/font",DocumentsFontPath];// 字体存储的路径
    NSLog(@"打印设置：设置默认字体路径");
    [DrawBoardInfo updateInfoWith:currentData];
    if (DrawBoardInfo.template_width_mm == 0) {
      DrawBoardInfo.template_width_mm = currentData.width;
    }
    if (DrawBoardInfo.boardWidth == 0) {
      DrawBoardInfo.boardWidth = SCREEN_WIDTH-45;
    }
    [ImageLibraryBridge initImageProcessing:fontPath error:nil];
    // NSInteger pageMax = goodsArr.count > 1?goodsArr.count:DrawBoardInfo.excelPageNumMax;
    if([currentData shouldCurveTextUpdrage]){
      [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01439", @"提醒") message:XY_LANGUAGE_TITLE_NAMED(@"app100000082", @"限时免费体验已结束，为保证您的正常使用，请更新APP到最新版本。") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000081", @"不用了") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00487", @"更新") cancelBlock:nil sureBlock:^{
        [[UIApplication sharedApplication] openURL:XY_URLWithString(app_download_Url) options:@{} completionHandler:^(BOOL success) {

        }];
      } alertType:3];
      return;
    }
    if([currentData shouldShowVip]){
      jc_is_support_vip = YES;
      NSString *sourcePage = @"";
      JC_TrackWithparms(@"click",@"012_082_106",(@{}));
      sourcePage = @"012";
      XYWeakSelf
      UIViewController *uniVC = [XYTool getCurrentVC];
      [JCIAPHelper openViewWithAlert:uniVC needOpenTip:YES isUseVipSource:YES success:^{
        [weakSelf refreshVipOperateStatus:nil];
      } failure:^(NSString *msg, id model) {
        [MBProgressHUD showToastWithMessageDarkColor:msg];
      } sourceInfo:@{@"sourcePage":sourcePage}];
      return;
    }
    [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_UNIAPP_REFRESH_TEMPLATE object:currentData];
  });

}

/**
 * 保存下载文件
 * @param data下载地址
 * */
- (void)saveFileData:(NSData *)data filePath:(NSString *)filePath fileName:(NSString *)fileName call:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  NSString *mUrl = [call getString:@"url" defaultValue:@""];
  NSString *name = [call getString:@"name" defaultValue:@""];
  NSString *eventId = [call getString:@"eventId" defaultValue:@""];
  NSString *type = [call getString:@"type" defaultValue:@""];

  NSData *fileData = data;
  NSString *fileMd5 = fileData.MD5String;

  // 通知下载成功
  [self notifyListeners:@"download-file"
                   data:@{
    @"appId": appId,
    @"eventId": eventId,
    @"size": @([fileData length]),
    @"status": @"success",
    @"progress": @(100),
    @"path": filePath,
    @"key": fileMd5,
    @"name": fileName
  }];

  if ([type isEqualToString:@"album"]) {
    // 保存到相册
    UIImage *image = [UIImage imageWithData:fileData];
    [JCActivityCodeHelp saveImageFinished:image withProgress:nil];

    // 移除图片缓存
    [[NSFileManager defaultManager] removeItemAtPath:filePath
                                               error:nil];
  } else if ([type isEqualToString:@"file"]) {
    [self saveToFileSystem:filePath
                  callback:^(NSNumber *result) {
      if ([result boolValue]) {
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100001282", @"文件已保存到我的文件中")];//XY_LANGUAGE_TITLE_NAMED(@"app00349", @"保存成功")
      } else {
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01410", @"保存失败")];
      }

      // 移除文件缓存
      [[NSFileManager defaultManager] removeItemAtPath:filePath
                                                 error:nil];
    }];
  }
}

/**
 * 移除下载文件
 * @param options.path 下载地址
 * */
- (void)removeFile:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  NSString *path = [call getString:@"path" defaultValue:@""];

  if ([appId length] == 0 ||
      [path length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }

  NSError *error;
  BOOL result = [[NSFileManager defaultManager] removeItemAtPath:path
                                                           error:&error];

  [call resolve];
}

/**
 * 安装更新
 * @param options.path 下载地址
 * */
- (void)install:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  NSString *path = [call getString:@"path" defaultValue:@""];
  NSString *version = [call getString:@"version" defaultValue:@""];

  if ([appId length] == 0 ||
      [path length] == 0 ||
      [version length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }

  dispatch_async(dispatch_get_main_queue(), ^{
    [[NBCAPMiniAppEngine sharedInstance] install:appId
                                         version:version
                                     wgtFilePath:path
                                         success:^(id x) {
      [call resolve:@{@"result": @1}];
    }
                                         failure:^(id x) {
      [call resolve:@{@"result": @0}];
    }];
  });
}

/**
 * 重启应用
 * */
- (void)restart:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];

  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }

  dispatch_async(dispatch_get_main_queue(), ^{
    // 插件生命周期随小程序关闭会释放, 这里交由 NBCAPMiniAppEngine 完成重启工作
    [[NBCAPMiniAppEngine sharedInstance] restart:appId];
  });
  [call resolve];
}

/**
 * 原生Http请求
 * */
- (void)request:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  NSString *urlString = [call getString:@"url" defaultValue:@""];
  NSString *method = [call getString:@"method" defaultValue:@""];
  NSDictionary *params = [call getObject:@"params" defaultValue:@{}];
  NSDictionary *headers = [call getObject:@"headers" defaultValue:@{}];
  NSNumber *timeout = [call getNumber:@"timeout" defaultValue:@15];

  if ([appId length] == 0 ||
      [urlString length] == 0 ||
      [method length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }

  if ([[method lowercaseString] isEqualToString:@"get"]) {
    // 获得NSURLSession对象
    NSURLSession *session = [NSURLSession sharedSession];
    if ([params.allKeys count] > 0) {
      NSMutableArray *keyValueParams = [[NSMutableArray alloc] init];
      for (NSString *key in params.allKeys) {
        [keyValueParams addObject:[NSString stringWithFormat:@"%@=%@", [key urlencode], [[[params valueForKey:key] description] urlencode]]];
      }
      NSString *paramsString = [keyValueParams componentsJoinedByString:@"&"];
      urlString = [NSString stringWithFormat:@"%@%@%@", urlString, [urlString containsString:@"?"] ? @"&":@"?", paramsString];
    }

    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:urlString]];
    request.HTTPMethod = @"GET";   // 请求方法
    // 请求头
    for (NSString *key in headers.allKeys) {
      [request setValue:[headers valueForKey:key] forHTTPHeaderField:key];
    }
    [request setTimeoutInterval:timeout.integerValue];

    // 创建任务
    NSURLSessionDataTask *task = [session dataTaskWithRequest:request
                                            completionHandler:^(NSData *data,
                                                                NSURLResponse *response,
                                                                NSError *error) {
      if (error == nil && [data isKindOfClass:[NSData class]]) {
        NSString *stringResponse = [[NSString alloc] initWithData:data
                                                         encoding:NSUTF8StringEncoding];

        NSLog(@"[Capacitor] GET: %@", [@{
          @"data": stringResponse,
          @"status": @(200),
          @"headers": headers,
          @"url": urlString
        } xy_toJsonString]);

        [call resolve:@{
          @"data": stringResponse,
          @"status": @(200),
          @"headers": headers,
          @"url": urlString
        }];
      } else {
        [call resolve:@{
          @"data": @{},
          @"status": @(500),
          @"headers": headers,
          @"url": urlString
        }];
      }
    }];

    [task resume];

  } else if ([[method lowercaseString] isEqualToString:@"post"]) {
    // 获得NSURLSession对象
    NSURLSession *session = [NSURLSession sharedSession];
    NSData *requestData = [NSJSONSerialization dataWithJSONObject:params options:0 error:nil];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:urlString]];
    request.HTTPMethod = @"POST";   // 请求方法
    request.HTTPBody = requestData; // 请求体
    // 请求头
    for (NSString *key in headers.allKeys) {
      [request setValue:[headers valueForKey:key] forHTTPHeaderField:key];
    }
    [request setTimeoutInterval:timeout.integerValue];

    // 创建任务
    NSURLSessionDataTask *task = [session dataTaskWithRequest:request
                                            completionHandler:^(NSData *data,
                                                                NSURLResponse *response,
                                                                NSError *error) {
      if (error == nil && [data isKindOfClass:[NSData class]]) {
        NSString *stringResponse = [[NSString alloc] initWithData:data
                                                         encoding:NSUTF8StringEncoding];
        [call resolve:@{
          @"data": stringResponse,
          @"status": @(200),
          @"headers": headers,
          @"url": urlString
        }];
      } else {
        [call resolve:@{
          @"data": @{},
          @"status": @(500),
          @"headers": headers,
          @"url": urlString
        }];
      }
    }];

    [task resume];

  } else {
    [call reject:@"未支持的请求类型" :nil :nil :@{}];
  }
}

/**
 * 跳转到其它小程序
 * @param options.appId 小程序Id
 * @param options.path 打开页面路径
 * @param options.replace 是否替换当前小程序
 * */
- (void)navigateToMiniProgram:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  NSString *targetAppId = [call getString:@"targetAppId" defaultValue:@""];
  NSString *path = [call getString:@"path" defaultValue:@""];
  BOOL replace = [call getBool:@"replace" defaultValue:NO];

  if ([appId length] == 0 ||
      [targetAppId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }

  dispatch_async(dispatch_get_main_queue(), ^{
    // 插件生命周期随小程序关闭会释放, 这里交由 NBCAPMiniAppManager 完成关闭当前程序打开新程序的工作
    XYBlock dataReceived = [[NBCAPMiniAppEngine sharedInstance] getAppDataReceived:appId];
    [[NBCAPMiniAppManager sharedInstance] checkThenOpenMiniApp:targetAppId
                                                     keepAlive:NO
                                                       subpath:path
                                                         extra:nil
                                                 replacedAppId:replace ? appId : nil
                                                  dataReceived:dataReceived];
  });
}

/**
 * 上传埋点数据
 * */
- (void)eventTrack:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  NSString *eventCode = [call getString:@"eventCode" defaultValue:@""];
  NSString *posCode = [call getString:@"posCode" defaultValue:@""];
  NSDictionary *params = [call getObject:@"params" defaultValue:@{}];

  if ([appId length] == 0 ||
      [eventCode length] == 0 ||
      [posCode length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }

  if ([eventCode isEqualToString:@"click"] ||
      [eventCode isEqualToString:@"show"] ||
      [eventCode isEqualToString:@"view"]) {
    JC_TrackWithparms(eventCode, posCode, params);
  } else {
    [call reject:@"未支持的埋点类型" :nil :nil :@{}];
  }
}

/**
 * 唤起购买vip界面
 * */
- (void)openBuyVIP:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];

  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }

  XYWeakSelf
  dispatch_async(dispatch_get_main_queue(), ^{
    [weakSelf openVipPage];
  });

  [call resolve];
}
/**
 * 调用图相库生成预览图
 * */
-(void)generateTemplatePreview:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }
  UIViewController *currentVC = [XYTool getCurrentVC];
  NSDictionary * templateDataDic = [call getObject:@"templateData" defaultValue:@{}];
  NSError *error;
  JCTemplateData *templateData = [[JCTemplateData alloc] initWithDictionary:templateDataDic error:&error];
  // 高效逆序字符串
  NSMutableString *reversedStr = [NSMutableString stringWithCapacity:[templateData.idStr length]];
  [templateData.idStr enumerateSubstringsInRange:NSMakeRange(0,[templateData.idStr length])
                                         options:(NSStringEnumerationReverse | NSStringEnumerationByComposedCharacterSequences)
                                      usingBlock:^(NSString *substring, NSRange substringRange, NSRange enclosingRange, BOOL *stop) {
        [reversedStr appendString:substring];
    }];
    templateData.idStr = reversedStr;
    templateData.profile.extrain.templateType = @"0";
    templateData.profile.extrain.templateClass = @"1";
    [self configDrawBoardInfo:templateData];
    NBCAPMiniAppManager * manager = [NBCAPMiniAppManager sharedInstance];
    dispatch_async(dispatch_get_main_queue(), ^{
        // 下载标签纸数据
//        [JCLabelInfoMangerHelper getServerLabelInfoWithTemplate:templateData success:^(JCTemplateData *requestModel) {
//
//        } field:^(id msg) {
//
//        }];
        // 下载模版数据
      [manager downLoadImageImageAndFontsForData:@"4" templateData:templateData complete:^(JCTemplateData *complateData){
            NSString * base64Image = [manager prewDataWith:complateData index:0];
            NSDictionary * data = @{@"appId":appId,@"templatePreviewBase64":base64Image};
            [self notifyListeners:@"generate-template-preview" data:data];
        }];
    });
    [call resolve];
}

/**
 * 调用图相库生成wifi码预览图
 * */
-(void)generateWifiCodePreview:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }
  self.currentUniAppId = appId;
  NSString *labelId = [call getString:@"labelId" defaultValue:@""];
  NSString *templateId = [call getString:@"templateId" defaultValue:@""];
  NSString *wifiCodeDetail = [call getString:@"appletCodeId" defaultValue:@""];
  NSString *title = [call getString:@"title" defaultValue:@""];
  NSDictionary *data = @{@"appletCodeId":wifiCodeDetail,@"title":title};
  NSString *contentId = @"";
  if(!STR_IS_NIL(templateId)){
    contentId = templateId;
  }else{
    contentId = labelId;
  }
  dispatch_async(dispatch_get_main_queue(), ^{
    [JCTemplateFunctionHelper getTemplateDetailRequestById:contentId complate:^(JCTemplateData *tagDetailModel) {
        BOOL is_ableEdit_Template = [tagDetailModel checkTemplateDetailByBackImage:YES containFont:NO];
        if(is_ableEdit_Template){
          [self openNativePrintImageWith:tagDetailModel source:@"3" dataInfo:data];
          return;
        }
        if(self.progressHUD == nil){
          self.progressHUD = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
        }
        self.progressHUD.label.text = XY_LANGUAGE_TITLE_NAMED(@"app01115", @"数据加载中");
        NSString *postUrl = [NSString stringWithFormat:@"%@/%@",J_myTemplate_Custom,contentId];
        [@{} java_postWithModelType:[JCTemplateData class] Path:postUrl hud:nil Success:^(__kindof YTKBaseRequest *request, JCTemplateData *model) {
          [self openNativePrintImageWith:model source:@"3" dataInfo:data];
        } failure:^(NSString *msg, id model) {
          [self.progressHUD hideAnimated:NO];
          [MBProgressHUD showToastWithMessageDarkColor:msg];
        }];
    } needLoading:YES];
  });
  [call resolve];
}

//调用原生 图像库绘制打印图功能
- (void)openNativePrintImageWith:(JCTemplateData *)currentData source:(NSString *)source dataInfo:(NSDictionary *)dataInfo{
    XYWeakSelf
    NSString *fontPath = [NSString stringWithFormat:@"%@/font",DocumentsFontPath];// 字体存储的路径
    NSArray *goodsInfoDicArr = dataInfo[@"commodityInfos"];
    NSLog(@"打印设置：设置默认字体路径");
    [DrawBoardInfo updateInfoWith:currentData];
    if (DrawBoardInfo.template_width_mm == 0) {
        DrawBoardInfo.template_width_mm = currentData.width;
    }
    if (DrawBoardInfo.boardWidth == 0) {
        DrawBoardInfo.boardWidth = SCREEN_WIDTH-45;
    }
    [ImageLibraryBridge initImageProcessing:fontPath error:nil];
    currentData.idStr = [NSString stringWithFormat:@"%@%@",currentData.idStr,currentData.idStr];
    currentData.profile.extrain.templateType = @"0";
    currentData.profile.extrain.templateClass = @"1";
    [self configDrawBoardInfo:currentData];
    NBCAPMiniAppManager * manager = [NBCAPMiniAppManager sharedInstance];
    [manager downLoadImageImageAndFontsForData:@"4" templateData:currentData complete:^(JCTemplateData *complateData){
        NSString *wifiCodeDetail = dataInfo[@"appletCodeId"];
        NSString *title = dataInfo[@"title"];
        NSString *uniAppName = [manager getUniAppNameWithAppId:self.currentUniAppId];
        XYWeakSelf
        if([self.wifiCodeInfo.allValues containsObject:wifiCodeDetail]){
            NSString *imageData = weakSelf.wifiCodeInfo[@"imageData"];
            JCTemplateData *jcData = [weakSelf getWifiCodeTemplateWith:complateData wifiTipTitle:title wifiCodeDetail:imageData];
            weakSelf.wifiTemplateData = jcData;
            [weakSelf refreshWifiCodeWith:jcData complate:^(NSString *imageUrl) {
                [weakSelf.progressHUD hideAnimated:NO];
                if(!STR_IS_NIL(imageUrl)){
                    NSDictionary *callbackDataDic = @{@"templatePreviewBase64":@{@"data":imageUrl,@"template":jcData.toJSONString}};
                    [self notifyListeners:@"generate-wifi-code-preview" data:callbackDataDic];
                }
            }];
        }else{
            [self getWifiCodeImageDataWith:wifiCodeDetail templateWidth:complateData.width complate:^(NSString *imageData) {
                if(!STR_IS_NIL(imageData)){
                    weakSelf.wifiCodeInfo = @{@"scene":wifiCodeDetail,@"imageData":imageData};
                    JCTemplateData *jcData = [weakSelf getWifiCodeTemplateWith:complateData wifiTipTitle:title wifiCodeDetail:imageData];
                    weakSelf.wifiTemplateData = jcData;
                    [weakSelf refreshWifiCodeWith:jcData complate:^(NSString *imageUrl) {
                        [weakSelf.progressHUD hideAnimated:NO];
                        if(!STR_IS_NIL(imageUrl)){
                            NSDictionary *callbackDataDic = @{@"templatePreviewBase64":@{@"data":imageUrl,@"template":jcData.toJSONString}};
                            [self notifyListeners:@"generate-wifi-code-preview" data:callbackDataDic];
                        }
                    }];
                }else{
                    weakSelf.wifiCodeInfo = nil;
                    [weakSelf.progressHUD hideAnimated:NO];
                }
            }];
        }
    }];
}

- (void)refreshWifiCodeWith:(JCTemplateData *)currentData complate:(XYBlock)complate{
  NSInteger screenScale = [UIScreen mainScreen].scale;
  JCTemplateData *jcData = currentData.toSdk(YES).configureBase64Background(YES).setCurrentPageIndex(0);
  NSString *jsonStr = [[jcData dataDict] xy_toJsonString];
  float displayMultiple = DrawBoardInfo.mm2pxScale*screenScale;
  float printMultiple = DrawBoardInfo.dpiScale;
  UIImage *previewImage = [ImageLibraryBridge generatePrintPreviewImage:jsonStr
                                                        displayMultiple:displayMultiple
                                                          printMultiple:printMultiple
                                                  printPreviewImageType:0
                                                             themeColor:JC_CURRENT_PRINT_COLOR
                                                                  error:nil];
  previewImage = [ImageTool decodedThumbImageWithImage:previewImage limitBytes:500 * 1024];
  NSData *imageData = UIImagePNGRepresentation(previewImage);
  NSString *fileName = [NSString stringWithFormat:@"%@%@.png",jcData.idStr,[XYTool getNowTimeTimestamp]];
  [[JCOSSManager sharedManager] oss_uploadFileWithParmsDic:@{@"module":@"USER_IMG"} fullFileName:fileName fileData:imageData Success:^(NSString * _Nonnull key, NSString * _Nonnull url) {
    dispatch_async(dispatch_get_main_queue(), ^{
      if (url && url.length > 0) {
        NSLog(@"上传成功刷新 URL地址：%@",url);
        complate(url);
      }else{
        complate(@"");
      }
    });
  } failure:^(NSString * _Nonnull key, NSString * _Nonnull errMsg) {
    dispatch_async(dispatch_get_main_queue(), ^{
      complate(@"");
    });
  }];
}

- (void)getWifiCodeImageDataWith:(NSString *)wifiCodeId templateWidth:(float)width complate:(XYBlock)complate{
  NSString *graphqlStr = @"query generateWifiCodeBase64 ($input: GetAppletCodeInut!) { generateWifiCodeBase64 (input: $input)}";
  [@{@"input":@{@"scene": UN_NIL(wifiCodeId)}} jc_graphQLRequestWith:graphqlStr hud:nil graphQLType:@"query" Success:^(__kindof YTKBaseRequest *request, NSDictionary *dataInfo) {
    NSString *dataStr = dataInfo[@"generateWifiCodeBase64"];
    dataStr = [dataStr stringByReplacingOccurrencesOfString:@"data:image/png;base64," withString:@""];
    complate(UN_NIL(dataStr));
  } failure:^(NSString *msg, id model) {
    complate(@"");
  }];
}

/**
 * 跳转商城
 * */
-(void)navigateToWebPage:(CAPPluginCall *)call{
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }
  NSString *url = [call getString:@"url" defaultValue:@""];
  NSString *urlType = [call getString:@"type" defaultValue:@""];
  dispatch_async(dispatch_get_main_queue(), ^{
    //拦截了
    if([urlType isEqualToString:@"normalH5"]){
      //      XYWKWebViewController *c = [[XYWKWebViewController alloc] initWithUrl:url];
      //      c.isShowNav = NO;
      //      c.showWebTitle = YES;
      //      c.isFullScreen = YES;

      NSString * urlStr = [NSString stringWithFormat:@"%@&fullSceen=1",url];
      XYWKWebViewController *c = [[XYWKWebViewController alloc] initWithUrl:urlStr];
      c.progressView.progressTintColor = HEX_RGB(0xF8473E);
      c.webView.scrollView.bounces = NO;
      //      [[XYTool getCurrentVC].navigationController pushViewController:c animated:YES];
      XYNavigationController *nav = [[XYNavigationController alloc] initWithRootViewController:c];
      UIViewController *uniVC = [XYTool getCurrentVC];
      [uniVC presentViewController:nav animated:YES completion:^{

      }];
    }else{
      JCShopNormalVC * shopVc = [[JCShopNormalVC alloc]initWithShopAppointUrl:url];
      shopVc.entrance_type_id = @"3";
      UIViewController *uniVC = [XYTool getCurrentVC];
      [uniVC presentViewController:shopVc animated:YES completion:^{

      }];
    }

  });
  [call resolve];
}

/**
 * 开始搜索打印机
 * */
-(void)startSearchPrinter:(CAPPluginCall *)call{
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }
  dispatch_async(dispatch_get_main_queue(), ^{
    [[JCBluetoothManager sharedInstance] startBluetoothConnectFrom:nil isFromHome:NO];
  });
  [call resolve];
}

/**
 * 打印机状态变更
 * */
-(void)printerStatusNotification:(NSNotification *)notification
{
  NSMutableDictionary *printerStatusInfo = [NSMutableDictionary dictionary];
  if(JC_IS_CONNECTED_PRINTER){
    JCPrinterModel *currentPrintModel = JC_CURRENT_CONNECTED_PRINTER_MODEL;
    [printerStatusInfo setValue:@(1) forKey:@"status"];
    [printerStatusInfo setValue:UN_NIL(currentPrintModel.xyid) forKey:@"machineId"];
    [printerStatusInfo setValue:UN_NIL(UN_NIL(JC_CURRENT_CONNECTED_PRINTER)) forKey:@"sn"];
    [printerStatusInfo setValue:UN_NIL([JCBluetoothManager sharedInstance].connectedModel.alias) forKey:@"machineAlias"];
    //打印机连接上后的容错处理
    [self restorePrintConfig];
  }else{
    [printerStatusInfo setValue:@(0) forKey:@"connected"];
    [printerStatusInfo setValue:@"" forKey:@"machineId"];
    [printerStatusInfo setValue:@"" forKey:@"sn"];
    [printerStatusInfo setValue:@"" forKey:@"machineAlias"];
  }
  NSString *printerStatusInfoStr = printerStatusInfo.xy_toJsonString;
  NSLog(@"打印机状态变化printer-connect 发送小程序数据:%@",printerStatusInfoStr);
  [self notifyListeners:@"printer-connection-change" data:printerStatusInfo];
}

//批量生成带版式的预览图
-(void)batchGeneratePreviewWithLayout:(CAPPluginCall *)call{
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }
  NSMutableDictionary *previewInfo = call.options.mutableCopy;
  NSArray *rowDataIds = previewInfo[@"rowDataIds"];

  // Special handling for catering mini-app
  if ([appId isEqualToString:meeting_ionic_id]) {
    // For catering app, parse column names as objects with type, name, alias properties
    NSArray *colNames = previewInfo[@"colNames"];
    NSMutableArray *cateringColumnNames = [NSMutableArray array];

    for (NSDictionary *item in colNames) {
      NSMutableDictionary *columnInfo = [NSMutableDictionary dictionary];
      [columnInfo setObject:item[@"type"] forKey:@"type"];
      [columnInfo setObject:item[@"name"] forKey:@"name"];
      [columnInfo setObject:item[@"alias"] forKey:@"alias"];
      [cateringColumnNames addObject:columnInfo];
    }

    previewInfo[@"cateringColumnNames"] = cateringColumnNames;
    previewInfo[@"isCateringCap"] = @YES;
  }

  [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"generateLayoutSchemeDataSourceTemplate" arguments:@{
    @"meetingParams":previewInfo.xy_toJsonString,
  } result:^(id value) {
    if(!STR_IS_NIL(value) && [value isKindOfClass:[NSString class]]){
      NSMutableArray *previewImages = [NSMutableArray array];
      dispatch_queue_t queue = dispatch_queue_create("CC_queue",DISPATCH_QUEUE_CONCURRENT);
      dispatch_async(queue, ^{
        // 3.创建一个数目为1的信号量，用于"卡"for循环，等上次循环结束在执行下一次的for循环
        dispatch_semaphore_t sema = dispatch_semaphore_create(0);
        NSMutableArray *previewImages = [NSMutableArray array];
        for (NSInteger index = 0; index < rowDataIds.count; index++) {
          dispatch_async(queue, ^{
            JCTemplateData *jcData = [[JCTemplateData alloc] initWithString:value error:nil];
            UIImage *previewImage = [JCTemplateImageManager getPreviewImageWith:jcData currentPage:index];
            // base64编码
            NSData *imageData = UIImagePNGRepresentation(previewImage);
            NSString *encodedImageStr = (previewImage == nil) ? @"":[imageData base64EncodedStringWithOptions:NSDataBase64Encoding64CharacterLineLength];
            encodedImageStr = [encodedImageStr stringByReplacingOccurrencesOfString:@"\n" withString:@""];
            encodedImageStr = [encodedImageStr stringByReplacingOccurrencesOfString:@"\r" withString:@""];
            encodedImageStr = [encodedImageStr stringByReplacingOccurrencesOfString:@"\t" withString:@""];
            [previewImages addObject:@{@"base64":encodedImageStr,@"id":rowDataIds[index]}];
            dispatch_semaphore_signal(sema);
          });
          dispatch_semaphore_wait(sema, DISPATCH_TIME_FOREVER);
        }
        NSDictionary *ionicPreviewImages = @{
          @"appId":appId,
          @"previewImages":previewImages,
        };
        [call resolve:ionicPreviewImages];
      });
    }else{
      [call reject:@"模板解析异常" :nil :nil :@{}];
    }
  }];
}

//批量生成多个不同版式的预览图
-(void)batchGeneratePreviewWithLayouts:(CAPPluginCall *)call{
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }
  NSMutableDictionary *previewInfo = call.options.mutableCopy;
  NSArray *layoutJsonList = previewInfo[@"layoutJsonList"];
  NSMutableArray *layoutJsonListNew = [NSMutableArray array];
  for (NSDictionary *layoutInfoDic in layoutJsonList) {
    NSMutableDictionary *layoutInfoNew = layoutInfoDic.mutableCopy;
    layoutInfoNew[@"json"] = [(NSString *)layoutInfoDic[@"json"] toDictionary];
    [layoutJsonListNew addObject:layoutInfoNew];
  }
  previewInfo[@"layoutJsonList"] = layoutJsonListNew;

  // Special handling for catering mini-app
  if ([appId isEqualToString:meeting_ionic_id]) {
    // For catering app, parse column names as objects with type, name, alias properties
    NSArray *colNames = previewInfo[@"colNames"];
    NSMutableArray *cateringColumnNames = [NSMutableArray array];

    for (NSDictionary *item in colNames) {
      NSMutableDictionary *columnInfo = [NSMutableDictionary dictionary];
      [columnInfo setObject:item[@"type"] forKey:@"type"];
      [columnInfo setObject:item[@"name"] forKey:@"name"];
      [columnInfo setObject:item[@"alias"] forKey:@"alias"];
      [cateringColumnNames addObject:columnInfo];
    }

    previewInfo[@"cateringColumnNames"] = cateringColumnNames;
    previewInfo[@"isCateringCap"] = @YES;
  } else {
    previewInfo[@"cateringColumnNames"] = @{};
    previewInfo[@"isCateringCap"] = @NO;
  }

  [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"generatePreviewTemplateWithLayouts" arguments:@{
    @"params":previewInfo.xy_toJsonString,
  } result:^(id value) {
    if(value != nil && [value isKindOfClass:[NSArray class]]){
      NSArray *templateStrs = value;
      NSMutableArray *previewImages = [NSMutableArray array];
      for (NSInteger index = 0; index < templateStrs.count; index++) {
        NSDictionary *layoutInfo = layoutJsonList[index];
        JCTemplateData *templateData = [[JCTemplateData alloc] initWithString:templateStrs[index] error:nil];
        UIImage *previewImage = [JCTemplateImageManager getPreviewImageWith:templateData currentPage:0];
        // base64编码
        NSData *imageData = UIImagePNGRepresentation(previewImage);
        NSString *encodedImageStr = (previewImage == nil) ? @"":[imageData base64EncodedStringWithOptions:NSDataBase64Encoding64CharacterLineLength];
        encodedImageStr = [encodedImageStr stringByReplacingOccurrencesOfString:@"\n" withString:@""];
        encodedImageStr = [encodedImageStr stringByReplacingOccurrencesOfString:@"\r" withString:@""];
        encodedImageStr = [encodedImageStr stringByReplacingOccurrencesOfString:@"\t" withString:@""];
        [previewImages addObject:@{@"base64":encodedImageStr,@"id":layoutInfo[@"id"]}];
      }
      [call resolve:@{
        @"appId":appId,
        @"previewImages":previewImages
      }];
    }else{
      [call reject:@"模板解析异常" :nil :nil :@{}];
    }
  }];
}

- (NSArray *)getArrDataFromJsonString:(NSString*)jsonStr{
  NSData *jsonData = [jsonStr dataUsingEncoding:NSUTF8StringEncoding];
  id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options: NSJSONReadingMutableContainers error:nil];
  if([jsonObject isKindOfClass:[NSArray class]]){
    return jsonObject;
  }else{
    return @[];
  }
}

//批量打印
-(void)startBatchPrint:(CAPPluginCall *)call{
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }

  // 如果已经在处理批量打印，直接返回
  if (self.isProcessingBatchPrint) {
    NSLog(@"[Batch Print] Already processing a batch print request, ignoring duplicate");
    return;
  }

  // 设置标识为正在处理
  self.isProcessingBatchPrint = YES;

  __block MBProgressHUD *hub;
  NSMutableDictionary *previewInfo = call.options.mutableCopy;
  NSString *theme = previewInfo[@"theme"];
  NSString *meetingId = previewInfo[@"codeId"];

  // Special handling for catering mini-app
  if ([appId isEqualToString:@"__CAP__SPR889G"]) {
    __block MBProgressHUD *hub;
    dispatch_async(dispatch_get_main_queue(), ^{
      hub = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
    });
    // For catering app, parse column names as objects with type, name, alias properties
    NSArray *colNames = previewInfo[@"colNames"];
    NSMutableArray *cateringColumnNames = [NSMutableArray array];

    for (NSDictionary *item in colNames) {
      NSMutableDictionary *columnInfo = [NSMutableDictionary dictionary];
      [columnInfo setObject:item[@"type"] forKey:@"type"];
      [columnInfo setObject:item[@"name"] forKey:@"name"];
      [columnInfo setObject:item[@"alias"] forKey:@"alias"];
      [cateringColumnNames addObject:columnInfo];
    }

    previewInfo[@"cateringColumnNames"] = cateringColumnNames;
    previewInfo[@"isCateringCap"] = @YES;
  }
  JCPrintScene scene = ((NSNumber *)previewInfo[@"isDirectPrint"]).boolValue ? JCPrintNullUiShowProgress : JCPrintSceneUniApp;
  [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"generateLayoutSchemeDataSourceTemplate" arguments:@{
    @"meetingParams":previewInfo.xy_toJsonString,
  } result:^(id value) {
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      // 请求完成，重置标识
      self.isProcessingBatchPrint = NO;
    });

    dispatch_async(dispatch_get_main_queue(), ^{
      if (hub) {
        [hub hideAnimated:YES];
      }
      if (STR_IS_NIL(value)) {
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000346", @"数据不存在")];
      }
    });
    if(!STR_IS_NIL(value)){
      NSDictionary *templateDic = ((NSString *)value).xy_toDictionary;
      [self toFlutterPrintPage:theme appId:appId meetingId:meetingId taskId:@"" templateInfoDic:templateDic scene:scene];
    }
  }];
}

//基于版式生成模板
-(void)generateTemplateWithLayout:(CAPPluginCall *)call{
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }
  NSDictionary *previewInfo = call.options;
  [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"generateTemplateWithLayout" arguments:@{
    @"params":previewInfo.xy_toJsonString,
  } result:^(id value) {
    if(value != nil){
      [call resolve:@{
        @"result":@1,
        @"template":value
      }];
    }else{
      [call reject:@"参数异常" :nil :nil :@{}];
      return;
    }
  }];
}
//唤起打印界面
-(void)openPrintPage:(CAPPluginCall *)call{
  NSString *appId = [call getString:@"appId" defaultValue:@""];

  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }
  self.currentUniAppId = appId;
  NSDictionary *templateDic = [call getObject:@"templateData" defaultValue:@{}];
  NSString *contentTheme = [call getString:@"theme" defaultValue:@""];//dataDic[@"theme"];
  NSString *taskId = [call getString:@"taskId" defaultValue:@""];
  [self toFlutterPrintPage:contentTheme appId:appId meetingId:@"" taskId:taskId templateInfoDic:templateDic scene:JCPrintSceneUniApp];
}

- (void)toFlutterPrintPage:(NSString *)contentTheme appId:(NSString *)appId meetingId:(NSString *)meetingId
                    taskId:(NSString *)taskId templateInfoDic:(NSDictionary *)templateDic scene:(JCPrintScene)scene {
  if(!STR_IS_NIL(contentTheme)){
    if([contentTheme isEqualToString:@"red"]){
      [JCPrintManager sharedInstance].printThemeColor = @"#FB4B42";
    }else if([contentTheme isEqualToString:@"green"]){
      [JCPrintManager sharedInstance].printThemeColor = @"#3E8C80";
    }else{
      [JCPrintManager sharedInstance].printThemeColor = contentTheme;
    }
  }else{
    [JCPrintManager sharedInstance].printThemeColor = @"#FB4B42";
  }
  [JCPrintManager sharedInstance].isDangerCapRePrint = NO;
  JCTemplateData *currentData = nil;
  if(templateDic != nil){
    currentData = [[JCTemplateData alloc] initWithDictionary:templateDic error:nil];
  }
  [DrawBoardInfo updateInfoWith:currentData];
  NSString *fontPath = [NSString stringWithFormat:@"%@/font",DocumentsFontPath];// 字体存储的路径
  NSLog(@"打印设置：设置默认字体路径");
  [DrawBoardInfo updateInfoWith:currentData];
  if (DrawBoardInfo.template_width_mm == 0) {
    DrawBoardInfo.template_width_mm = currentData.width;
  }
  if (DrawBoardInfo.boardWidth == 0) {
    DrawBoardInfo.boardWidth = SCREEN_WIDTH-45;
  }
  [ImageLibraryBridge initImageProcessing:fontPath error:nil];

  dispatch_async(dispatch_get_main_queue(), ^{
    if([currentData shouldCurveTextUpdrage]){
      [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01439", @"提醒") message:XY_LANGUAGE_TITLE_NAMED(@"app100000082", @"限时免费体验已结束，为保证您的正常使用，请更新APP到最新版本。") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000081", @"不用了") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00487", @"更新") cancelBlock:nil sureBlock:^{
        [[UIApplication sharedApplication] openURL:XY_URLWithString(app_download_Url) options:@{} completionHandler:^(BOOL success) {

        }];
      } alertType:3];
      return;
    }
    if([currentData shouldShowVip]){
      XYWeakSelf
      jc_is_support_vip = YES;
      NSString *sourcePage = @"";
      JC_TrackWithparms(@"click",@"012_082_106",(@{}));
      sourcePage = @"012";
      UIViewController *uniVC = [XYTool getCurrentVC];
      [JCIAPHelper openViewWithAlert:uniVC needOpenTip:YES isUseVipSource:YES success:^{
        [weakSelf refreshVipOperateStatus:nil];
      } failure:^(NSString *msg, id model) {
        [MBProgressHUD showToastWithMessageDarkColor:msg];
      } sourceInfo:@{@"sourcePage":sourcePage}];
      return;
    }
    saveRecentlyUsedRecord(currentData.profile.extrain.labelId);
    if(currentData == nil){
      [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app00629", @"数据错误")];
      XYBlock1 block = ^(id x,id y){

      };
      uploadLogInfo(templateDic.xy_toJsonString, @"", block, J_get_sls_Log_Token);
      return;
    }
    [JCPrintManager sharedInstance].meetingId = meetingId;
    BOOL hasImageElement = NO;
    for (JCElementModel *element in currentData.elements) {
      if([element.type isEqualToString:@"image"]){
        hasImageElement = YES;
        break;
      }
    }
    if(!hasImageElement){
      [[JCPrintManager sharedInstance] doPrintWithTemplateModel:@[currentData]
                                                       uniAppId:appId
                                                     printScene:scene
                                                      historyId:nil
                                                  printComplate:^(NSNumber *complateCount) {
        // 记录历史记录
        if(complateCount.integerValue > 0){
          NSDictionary * dic = @{@"appId":appId,@"taskId":taskId,@"success":@(1)};
          [self notifyListeners:@"print-complete" data:dic];
        }
      }];
    }else{
      [JCTemplateImageManager downLoadImagesForData:currentData options:DownAll complete:^(JCTemplateData *resultData) {
        [[JCPrintManager sharedInstance] doPrintWithTemplateModel:@[resultData]
                                                         uniAppId:appId
                                                       printScene:scene
                                                        historyId:nil
                                                    printComplate:^(NSNumber *complateCount) {
          // 记录历史记录
          if(complateCount.integerValue > 0){
            NSDictionary * dic = @{@"appId":appId,@"taskId":taskId,@"success":@(1)};
            [self notifyListeners:@"print-complete" data:dic];
          }
        }];
      }];
    }
  });
}

//系统全屏页面侧滑事件
-(void)nativeGestureEvent{
  NSDictionary * dic = @{@"appId":self.appId,@"source":@"gesture"};
  [self notifyListeners:@"system-back" data:dic];
}
//断开打印机连接
-(void)disconnectPrinter:(CAPPluginCall *)call{
  dispatch_async(dispatch_get_main_queue(), ^{
    [[JCBluetoothManager sharedInstance] closeConnectedNIIMBOTPlugin:YES deviceType:JCBluetoothNormal];
  });
}

//打印前容错
-(void)restorePrintConfig{

}
/**
 * 开始打印
 * */
-(void)startPrint:(CAPPluginCall *)call{

  //开启打印前容错处理
  [self restorePrintConfig];
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }
  if([JCBluetoothManager sharedInstance].openWhenPrinting == YES || [JCBluetoothManager sharedInstance].isCoverOpen == YES){
    if([JCBluetoothManager sharedInstance].isCoverOpen == YES && [JCBluetoothManager sharedInstance].openWhenPrinting == false){
      dispatch_async(dispatch_get_main_queue(), ^{
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01426", @"记得盖上打印机盖子哦")];
      });
    }
    self.toDoCall = call;
    return;
  }
  [JCPrintManager sharedInstance].printScene = JCPrintNullUi;
  [self startPrintWithCall:call isNeedMixLableData:false backBlock:nil];
  [call resolve];
}

//开启打印任务
-(void)startPrintWithCall:(CAPPluginCall *)call isNeedMixLableData:(BOOL)isNeedMixLableData backBlock:(XYBlock)backBlock{
  NSString *appId = call.options[@"appId"];
  NSString *taskId = @"0";
  if([call.options[@"taskId"] isKindOfClass:[NSString class]]){
    taskId = call.options[@"taskId"];
  }else if([call.options[@"taskId"] isKindOfClass:[NSNumber class]]){
    taskId = [(NSNumber *)call.options[@"taskId"] stringValue];
  }
  NSString * hippoId = call.options[@"hippoId"];
  NSError *error;
  JCTemplateData *templateData = nil;
  if([call.options[@"templateData"] isKindOfClass:[NSString class]]){
    templateData = [[JCTemplateData alloc] initWithString:call.options[@"templateData"] error:&error];
  }else if ([call.options[@"templateData"] isKindOfClass:[NSDictionary class]]){
    templateData = [[JCTemplateData alloc] initWithDictionary:call.options[@"templateData"] error:&error];
  }
  if (templateData == nil) {
    [call reject:@"模版解析失败" :nil :nil :@{}];
    return;
  }
  NSInteger currentPrintState = [JCPrintManager sharedInstance].currentPrintState;
  if(currentPrintState != 0 || ([JCBluetoothManager sharedInstance].isSwitchMode && !JC_IS_CONNECTED_PRINTER)){
    NSDictionary * dic = @{@"appId":appId,@"taskId":taskId,@"success":@0};
    [self notifyListeners:@"print-complete" data:dic];
    return;
  }
  [self configDrawBoardInfo:templateData];
  __weak typeof(self) weakSelf = self;
  [self preparePrintData:templateData complete:^{
    if([NSThread isMainThread]){
      [weakSelf toPrint:templateData hippoId:hippoId appId:appId taskId:taskId backBlock:backBlock];
    }else{
      dispatch_async(dispatch_get_main_queue(), ^{
        [weakSelf toPrint:templateData hippoId:hippoId appId:appId taskId:taskId backBlock:backBlock];
      });
    }
  }];
}

-(void)toPrint:(JCTemplateData *)templateData hippoId:(NSString *)hippoId appId:(NSString *)appId taskId:(NSString *)taskId backBlock:(XYBlock)backBlock
{
  CapacitorPrintHelper * printer = [[CapacitorPrintHelper alloc]init];
  JCPrintManager * manager = [JCPrintManager sharedInstance];
  [printer startPrintWithData:templateData printBlock:^(id x) {
    JCTemplateData *data = x;
    [manager printWithTemplateModel:data excel:data.totalPage meetingId:hippoId callBack:^(NSNumber *success) {
      [JCPrintManager sharedInstance].printScene = JCPrintSceneOthers;
      NSDictionary * dic = @{@"appId":appId,@"taskId":taskId,@"success":success};
      [self notifyListeners:@"print-complete" data:dic];
      if(backBlock != nil){
        backBlock(nil);
      }
    }];
  }];
}


/**
 * 唤起扫一扫页面
 * */
-(void)openScanPage:(CAPPluginCall *)call{
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }
  NSMutableDictionary * data = [[NSMutableDictionary alloc]init];
  NSString * type = [call getString:@"type" defaultValue:@""];
  NSString * title = [call getString:@"title" defaultValue:@""];
  // 根据type区分扫描类型
  NSNumber * searchType = @(0);
  if ([type isEqualToString:@"commodity"]) {
    searchType = @(0);
  } else if ([type isEqualToString:@"label"]) {
    searchType = @(1);
  } else if ([type isEqualToString:@"scanResult"]) {
    searchType = @(2);
  }
  [data addEntriesFromDictionary:@{@"type":searchType,@"title":title}];
  dispatch_async(dispatch_get_main_queue(), ^{
    [[NBCAPMiniAppManager sharedInstance] openScanWithData:data callBack:^(NSDictionary * value) {
      if(value == nil || value.allKeys.count == 0){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000049", @"未识别到条码/二维码")];
      }else{
        NSMutableDictionary * result = [NSMutableDictionary dictionary];
        [result addEntriesFromDictionary:@{@"type":type}];
        [result addEntriesFromDictionary:value];
        [self notifyListeners:@"scan-complete" data:result];
      }

    }];
  });
}


-(void)rfidGotLabelInfoNotification:(NSNotification *)notification{
  NSString *getLabelSuccess = notification.object;
  if(getLabelSuccess.integerValue == 1){
    //小程序重复监听 会导致事件多次响应 导致和小程序的交互错乱
    //根本解决办法需要在合适的时机移除监听 在对应的小程序活跃的时候加载监听
    if([self.appId isEqualToString:[NBCAPMiniAppManager sharedInstance].currentAppId]){
      [self refreshRfid];
    }
    [self checkTodoPrint];
  }else{

  }
}

-(void)checkTodoPrint
{
  if(self.toDoCall != nil){
    NSNumber * callTask = [self.toDoCall getNumber:@"taskId" defaultValue:@(0)];
    NSInteger currentPrintState = [JCPrintManager sharedInstance].currentPrintState;
    if((callTask != nil && self.taskId && [callTask integerValue] == [self.taskId integerValue]) || currentPrintState != 0){
      return;
    }
    self.taskId = [self.toDoCall getNumber:@"taskId" defaultValue:@(0)];
    [self startPrintWithCall:self.toDoCall isNeedMixLableData:true backBlock:^(id x) {
      self.toDoCall = nil;
    }];
  }

}

//打印机开盖时通知处理
-(void)rfidPrinterCoverOpenNotification:(NSNotification *)noti{
  NSString *statusString = noti.object;
  if([statusString isEqualToString:@"0"]){
    NSDictionary *rfidChangedInfoDic = @{@"labelId":@"",@"labelName":@""};
    NSString *rfidChangedInfoStr = rfidChangedInfoDic.xy_toJsonString;
    NSLog(@"RFID识别label-change 发送小程序数据:%@",rfidChangedInfoStr);
    [self notifyListeners:@"printer-label-change" data:rfidChangedInfoDic];
  }
}

-(void)refreshRfid{
  JCTemplateData *rfidTemplateData = [JCPrintManager sharedInstance].rfidTemplateData;
  if(rfidTemplateData != nil){
    NSDictionary *rfidChangedInfoDic = @{@"labelId":UN_NIL(rfidTemplateData.idStr),@"labelName":UN_NIL(rfidTemplateData.name)};
    NSString *rfidChangedInfoStr = rfidChangedInfoDic.xy_toJsonString;
    NSLog(@"RFID识别label-change 发送小程序数据:%@",rfidChangedInfoStr);
    [self notifyListeners:@"printer-label-change" data:rfidChangedInfoDic];
  }
}

//下载字体和图片资源
-(void)preparePrintData:(JCTemplateData *)templateData complete:(void(^)(void))completeBlock{
  __block JCTemplateData *blockModel = templateData;
  [JCTemplateImageManager downLoadImagesForData:templateData options:DownAll complete:^(JCTemplateData *resultData) {
    // Update the template data with the result
    if (resultData) {
      blockModel = resultData;
    }
    [[JCFontManager sharedManager] downLoadFontRequestWithTemplateData:templateData finishBlock:^(BOOL loaded) {
      completeBlock();
    }];
  }];
}

//检查模板元素数据完整性
- (BOOL)checkTemplateElementsSourceComplate:(JCTemplateData *)templateData{
    BOOL sourceIsComplete = YES;
    for (JCElementModel *element in templateData.elements) {
        if([element.type isEqualToString:@"image"]){//检查图片元素是否已下载
            NSString *imageLocalPath = element.localUrl;
            if(![[NSFileManager defaultManager] fileExistsAtPath:imageLocalPath]){
                NSLog(@"批量打印-----元素不存在");
                sourceIsComplete = NO;
            }
        }
    }
    return sourceIsComplete;
}

-(void)configDrawBoardInfo:(JCTemplateData *)templateData{
  [DrawBoardInfo updateInfoWith:templateData];
  //设置打印精度信息
  if (DrawBoardInfo.template_width_mm == 0) {
    DrawBoardInfo.template_width_mm = templateData.width;
  }
  if (DrawBoardInfo.boardWidth == 0) {
    DrawBoardInfo.boardWidth = SCREEN_WIDTH-45;
  }
  //初始化打印字体路径
  NSString *fontPath = [NSString stringWithFormat:@"%@/font",DocumentsFontPath];
  [ImageLibraryBridge initImageProcessing:fontPath error:nil];
}

/**
 * 二维码转换为高级二维码
 * */
- (void)QRCodeToLiveCode:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];

  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }

  dispatch_async(dispatch_get_main_queue(), ^{
    JCActivityCodeModel *activityCodeMode = [self handleQRCodeToLiveCode:call.dictionaryRepresentation];
    if (activityCodeMode != nil) {
      [[NBCAPMiniAppEngine sharedInstance] quitMiniApp:appId
                                             aliveMode:NBCAPAliveWeak
                                              animated:YES
                                              complete:^(BOOL complete) {
        XYBlock dataReceived = [[NBCAPMiniAppEngine sharedInstance] getAppDataReceived:appId];
        if (dataReceived != nil) {
          dataReceived(@{
            @"type": @"select",
            @"data": activityCodeMode
          });
        }
        [[NSNotificationCenter defaultCenter] removeObserver:self];
      }];
    }
  });

  [call resolve];
}

/**
 * 调用原生文件上传
 * @param options.channelType 类型 0:客户端本地 2:微信 3:qq 4:钉钉
 * @param options.eventId 事件id
 * */
- (void)clientUploadFile:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  NSNumber *channelType = [call getNumber:@"channelType" defaultValue:@0];
  NSString *eventId = [call getString:@"eventId" defaultValue:@""];


  NSArray<NSString *> *fileType = nil;
  id fileTypeRaw = [[call dictionaryRepresentation] objectForKey:@"fileType"];
  if ([fileTypeRaw isKindOfClass:[NSArray class]]) {
      fileType = fileTypeRaw;
  } else if ([fileTypeRaw isKindOfClass:[NSString class]]) {
      // 若传的是单个字符串也兼容一下
      fileType = @[fileTypeRaw];
  } else {
      fileType = @[]; // 默认空数组
  }

  NSArray<NSString *> *allowedExtensions = nil;
  id extRaw = [[call dictionaryRepresentation] objectForKey:@"allowedExtensions"];
  if ([extRaw isKindOfClass:[NSArray class]]) {
      allowedExtensions = extRaw;
  } else if ([extRaw isKindOfClass:[NSString class]]) {
      allowedExtensions = @[extRaw];
  } else {
      allowedExtensions = @[];
  }

  NSNumber *maxSize = [call getNumber:@"maxSize" defaultValue:@0];

  self.uploadExtraParams = nil;

  if ([appId length] == 0 ||
      [eventId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }
  if(NETWORK_STATE_ERROR){
    dispatch_async(dispatch_get_main_queue(), ^{
      [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app00268", @"网络异常")];
    });
    return;
  }
  NSNumber *notSaveToHippoNumber = [call getNumber:@"notSaveToHippo" defaultValue:@0];
  NSNumber *isUploadLimitNumber = [call getNumber:@"isLimitSize" defaultValue:@YES];
  self.notSaveToHippo = [notSaveToHippoNumber integerValue];
  self.isUploadLimit = isUploadLimitNumber.boolValue;
  XYWeakSelf
  dispatch_async(dispatch_get_main_queue(), ^{
    if(fileType != nil && fileType.count > 0 && allowedExtensions.count > 0) {
      [weakSelf uploadFileFrom:channelType.integerValue
                        itemId:eventId extraParams:@{@"fileType":fileType,@"maxSize":maxSize,@"allowedExtensions":allowedExtensions}];
    } else{
      [weakSelf uploadFileFrom:channelType.integerValue
                        itemId:eventId];
    }
  });
  [call resolve];
}

/**
 * 高级二维码操作事件 用于埋点等其它相关操作
 * @param {String} options.operate 操作编码 1:新建 2:删除 3:保存 4:使用
 * */
- (void)liveCodeOperateEvent:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  NSString *operate = [call getString:@"operate" defaultValue:@""];

  if ([appId length] == 0 ||
      [operate length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }

  // TODO: 待办

  [call resolve];
}

/**
 * 高级二维码信息发生改变
 * */
- (void)liveCodeInfoChanged:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }

  dispatch_async(dispatch_get_main_queue(), ^{
    JCActivityCodeModel *activityCodeMode = [self handleLiveCodeInfoChanged:call.dictionaryRepresentation type:1];
    if (activityCodeMode != nil) {
      XYBlock dataReceived = [[NBCAPMiniAppEngine sharedInstance] getAppDataReceived:appId];
      if (dataReceived != nil) {
        dataReceived(@{
          @"type": @"changed",
          @"data": activityCodeMode
        });
      }
    }
  });

  [call resolve];
}

/**
 * 表单信息发生改变
 * */
- (void)sheetInfoChanged:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];
  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }

  dispatch_async(dispatch_get_main_queue(), ^{
    JCActivityCodeModel *activityCodeMode = [self handleLiveCodeInfoChanged:call.dictionaryRepresentation type:2];
    activityCodeMode.isForm = @"1";
    if (activityCodeMode != nil) {
      XYBlock dataReceived = [[NBCAPMiniAppEngine sharedInstance] getAppDataReceived:appId];
      if (dataReceived != nil) {
        dataReceived(@{
          @"type": @"changed",
          @"data": activityCodeMode
        });
      }
    }
  });

  [call resolve];
}

/**
 * 分享第三方
 * */
- (void)share:(CAPPluginCall *)call {
  NSString *url = [call getString:@"url" defaultValue:@""];
  NSString *title = [call getString:@"title" defaultValue:@""];
  NSString *description = [call getString:@"description" defaultValue:@""];
  NSString *iconUrl = [call getString:@"iconUrl" defaultValue:@""];
  // 类型 0::微信 1:QQ 2:钉钉
  //目前文件分享只支持微信
  NSNumber *channelType = [call getNumber:@"channelType" defaultValue:@0];
  //是分享文件还是链接
  NSString *shareType = [call getString:@"shareType" defaultValue:@""];
  //文件后缀 shareType 为 file时，需要该字段
  NSString *fileExtension = [call getString:@"fileExtension" defaultValue:@""];
  if ([url length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }
  if([shareType isEqualToString:@"file"]){
    if([[channelType stringValue] isEqualToString:@"0"]){
      if(![WXApi isWXAppInstalled]){
        dispatch_async(dispatch_get_main_queue(), ^{
          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01193",@"您没有安装手机微信")];
          return;
        });
        return;;
      }
      NSDictionary *shareInfo = @{@"title":UN_NIL(title),@"description":UN_NIL(description),@"shareUrl":UN_NIL(url),@"icon":UN_NIL(iconUrl),@"fileExtension":fileExtension};
      NSMutableDictionary * dic = [NSMutableDictionary dictionaryWithDictionary:shareInfo];
      dispatch_async(dispatch_get_global_queue(0, 0), ^{
        if(UN_NIL(iconUrl).length == 0){
          NSData * imageData = UIImageJPEGRepresentation(XY_IMAGE_NAMED(@"excel列表_文档"), 1.0);
          [dic addEntriesFromDictionary:@{@"iconData":imageData}];
        }
        dispatch_async(dispatch_get_main_queue(), ^{
          [JCShareTool shareContentToThirdAppWithShareInfo:dic needCheckLogin:NO thirdAppType:channelType.integerValue];
        });

      });
    }
  }else if([shareType isEqualToString:@"link"]){
    NSDictionary *shareInfo = @{@"title":UN_NIL(title),@"description":UN_NIL(description),@"shareUrl":UN_NIL(url),@"icon":UN_NIL(iconUrl)};
    dispatch_async(dispatch_get_main_queue(), ^{
      [JCShareTool shareContentToThirdAppWithShareInfo:shareInfo needCheckLogin:NO thirdAppType:channelType.integerValue];
    });
  }else{
    NSDictionary *shareInfo = @{@"title":UN_NIL(title),@"description":UN_NIL(description),@"shareUrl":UN_NIL(url),@"icon":UN_NIL(iconUrl)};
    dispatch_async(dispatch_get_main_queue(), ^{
      [JCShareTool shareContentToThirdAppWithShareInfo:shareInfo needCheckLogin:NO thirdAppType:channelType.integerValue];
    });
  }

  [call resolve];
}

-(void)testWithUrl:(NSString *)url
{
  NSString * patch = [self writeToCacheWithUrl:url];
  NSURL *urlToShare = [NSURL URLWithString:url];
  NSData * data = [[NSData alloc]initWithContentsOfURL:urlToShare];
  NSURL * fileUrl = [[NSURL alloc]initFileURLWithPath:patch];
  dispatch_async(dispatch_get_main_queue(), ^{

    UIActivityViewController * a = [[UIActivityViewController alloc]initWithActivityItems:@[data,fileUrl] applicationActivities:nil];
    a.modalPresentationStyle = UIModalPresentationPopover;
    UIViewController *uniVC = [XYTool getCurrentVC];
    UIActivityViewControllerCompletionHandler myBlock = ^(NSString *activityType,BOOL completed)
    {
      NSLog(@"activityType :%@", activityType);
      if (completed)
      {
        NSLog(@"completed");
      }
      else
      {
        NSLog(@"cancel");
      }

      //放回上一级界面
      //[[XYTool getCurrentVC]         :YES];

    };

    //                a.completionHandler = myBlock;
    [uniVC presentViewController:a animated:YES completion:nil];
  });

}

#pragma mark 写入缓存
- (NSString *)writeToCacheWithUrl:(NSString *)urlStr
{
  NSData *data = [NSData dataWithContentsOfURL:[NSURL URLWithString:[urlStr stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]]];
  //创建文件管理器
  NSFileManager *fileManager = [NSFileManager defaultManager];
  //获取document路径
  NSString *cachesPath = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory,NSUserDomainMask, YES) objectAtIndex:0];
  [fileManager createDirectoryAtPath:[cachesPath stringByAppendingString:@"/Caches"] withIntermediateDirectories:YES attributes:nil error:nil];
  //写入路径
  NSString *path = [cachesPath stringByAppendingString:[NSString stringWithFormat:@"/%lu.%@",(unsigned long)[urlStr hash],urlStr.pathExtension]];

  [data writeToFile:path atomically:YES];
  return path;
}


/**
 * 选择使用高级二维码
 * */
- (void)selectLiveCode:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];

  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }

  dispatch_async(dispatch_get_main_queue(), ^{
    JCActivityCodeModel *activityCodeMode = [self handleSelectLiveCode:call.dictionaryRepresentation type:1];
    if (activityCodeMode != nil) {
      [[NBCAPMiniAppEngine sharedInstance] quitMiniApp:appId
                                             aliveMode:NBCAPAliveWeak
                                              animated:YES
                                              complete:^(BOOL complete) {
        XYBlock dataReceived = [[NBCAPMiniAppEngine sharedInstance] getAppDataReceived:appId];
        if (dataReceived != nil) {
          dataReceived(@{
            @"type": @"select",
            @"data": activityCodeMode
          });
        }
        [[NSNotificationCenter defaultCenter] removeObserver:self];
      }];
    }
  });

  [call resolve];
}

/**
 * 选择使用表单
 * */
- (void)selectSheet:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];

  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }

  dispatch_async(dispatch_get_main_queue(), ^{
    JCActivityCodeModel *activityCodeMode = [self handleSelectLiveCode:call.dictionaryRepresentation type:2];
    if (activityCodeMode != nil) {
      [[NBCAPMiniAppEngine sharedInstance] quitMiniApp:appId
                                             aliveMode:NBCAPAliveWeak
                                              animated:YES
                                              complete:^(BOOL complete) {
        XYBlock dataReceived = [[NBCAPMiniAppEngine sharedInstance] getAppDataReceived:appId];
        if (dataReceived != nil) {
          dataReceived(@{
            @"type": @"select",
            @"data": activityCodeMode
          });
        }
        [[NSNotificationCenter defaultCenter] removeObserver:self];
      }];
    }
  });

  [call resolve];
}

/**
 * 其他小程序发送消息
 * */
- (void)postMessage:(CAPPluginCall *)call {
  NSString *appId = [call getString:@"appId" defaultValue:@""];

  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }

  dispatch_async(dispatch_get_main_queue(), ^{
    [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_MINI_APP_BROADCAST
                                                        object:nil
                                                      userInfo:call.dictionaryRepresentation];
  });

  [call resolve];
}

#pragma mark - Private

// 选择使用高级二维码 type: 1 活码 2 表单
- (JCActivityCodeModel *)handleSelectLiveCode:(NSDictionary *)params type:(NSInteger)type{
  NSDictionary *messageDic = params;
  JCActivityCodeModel *codeModel = [[JCActivityCodeModel alloc] initWithDictionary:messageDic error:nil];
  codeModel.isForm = type == 2?@"1":@"";
  if(codeModel != nil){
    NSString *tableName = type == 2?TABLE_LABEL_FORM_CODE_INFO:TABLE_LABEL_ACTIVITY_CODE_INFO;
    NSArray *codeModelArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:tableName dicOrModel:[JCActivityCodeModel class] whereFormat:[NSString stringWithFormat:@"where codeId = '%@'",codeModel.codeId]];
    if(codeModelArr.count > 0){
      [[JCFMDB shareDatabase:DB_NAME] jc_updateTable:tableName dicOrModel:codeModel whereFormat:[NSString stringWithFormat:@"where codeId = '%@'",codeModel.codeId]];
    }else{
      [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:tableName dicOrModel:codeModel];
    }
  }
  return codeModel;
}

// 高级二维码信息发生改变
- (JCActivityCodeModel *)handleLiveCodeInfoChanged:(NSDictionary *)params type:(NSInteger)type{
  NSDictionary *messageDic = params;
  JCActivityCodeModel *codeModel = [[JCActivityCodeModel alloc] initWithDictionary:messageDic error:nil];
  codeModel.isForm = type == 2?@"1":@"";
  if(codeModel != nil){
    NSString *tableName = type == 2?TABLE_LABEL_FORM_CODE_INFO:TABLE_LABEL_ACTIVITY_CODE_INFO;
    NSString *where = [NSString stringWithFormat:@"where codeId = '%@'",codeModel.codeId];
    NSArray *codeModelArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:tableName dicOrModel:[JCActivityCodeModel class] whereFormat:where];
    if(codeModelArr.count > 0){
      [[JCFMDB shareDatabase:DB_NAME] jc_updateTable:tableName dicOrModel:codeModel whereFormat:where];
    }
  }
  return codeModel;
}

// 二维码转换为高级二维码
- (JCActivityCodeModel *)handleQRCodeToLiveCode:(NSDictionary *)params {
  NSDictionary *messageDic = params;
  JCActivityCodeModel *codeModel = [[JCActivityCodeModel alloc] initWithDictionary:messageDic error:nil];
  if (codeModel != nil) {
    NSArray *codeModelArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_LABEL_ACTIVITY_CODE_INFO dicOrModel:[JCActivityCodeModel class] whereFormat:[NSString stringWithFormat:@"where codeId = '%@'",codeModel.codeId]];
    if (codeModelArr.count > 0) {
      [[JCFMDB shareDatabase:DB_NAME] jc_updateTable:TABLE_LABEL_ACTIVITY_CODE_INFO dicOrModel:codeModel whereFormat:[NSString stringWithFormat:@"where codeId = '%@'",codeModel.codeId]];
    } else {
      [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:TABLE_LABEL_ACTIVITY_CODE_INFO dicOrModel:codeModel];
    }
  }
  return codeModel;
}

// 保存文件到 iOS 文件系统
#pragma mark - 下载文件
- (void)saveToFileSystem:(NSString *)filePathString
                callback:(XYBlock)callback{
  if (filePathString == nil || [filePathString length] == 0) {
    callback(@0);
    return;
  }

  self.saveFileToSystemFolder = callback;

  UIDocumentPickerViewController *documentPickerVC = [[UIDocumentPickerViewController alloc] initWithURL:[NSURL fileURLWithPath:filePathString]
                                                                                                  inMode:UIDocumentPickerModeExportToService];
  // 设置代理
  documentPickerVC.delegate = self;
  // 设置模态弹出方式
  documentPickerVC.modalPresentationStyle = UIModalPresentationFullScreen;

  [[XYTool getCurrentVC] presentViewController:documentPickerVC
                                      animated:YES
                                    completion:nil];
}

// 发起购买 VIP
- (void)openVipPage {
  [JCIAPHelper openViewWithAlert:[XYTool getCurrentVC] needOpenTip:NO isUseVipSource:YES success:^{

  } failure:^(NSString *msg, id model) {
    [MBProgressHUD showToastWithMessageDarkColor:msg];
  } sourceInfo:@{@"sourcePage":@"012"}];
}

- (BOOL)isCanResiveNotificaiton{
  if(!isResiveNotification){
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      isResiveNotification = NO;
    });
    isResiveNotification = YES;
    return YES;
  }else{
    return NO;
  }
}

- (void)cleanNotification:(NSNotification *)notification{
  [[NSNotificationCenter defaultCenter] removeObserver:self];
}
- (void)refreshVipOperateStatus:(NSNotification *)notification{
//  if(![self isCanResiveNotificaiton]) return;
  NSMutableDictionary *userInfoDic = [NSMutableDictionary dictionary];
  [userInfoDic setValue:UN_NIL(m_userModel.userid) forKey:@"userId"];
  NSString *uniAppName = [[NBCAPMiniAppManager sharedInstance] getUniAppNameWithAppId:self.appId];
  [userInfoDic setValue:UN_NIL(uniAppName) forKey:@"appletName"];
  [userInfoDic setValue:UN_NIL(self.appId) forKey:@"appId"];

  // 购买 vip 前绑定主账号的逻辑会引起 token 刷新, 这里刷新给小程序
  NSString *token = [NSString stringWithFormat:@"bearer %@",m_userModel.token];
  [userInfoDic setValue:UN_NIL(token) forKey:@"token"];

  NSMutableDictionary *vipInfoDic = [NSMutableDictionary dictionary];
  if(m_userModel.vipInfo != nil){
    UserVipInfo *vipInfo = m_userModel.vipInfo;
    [vipInfoDic setValue:@(vipInfo.valid) forKey:@"valid"];
    [vipInfoDic setValue:(vipInfo.autoRenewal.integerValue == 1)?@(true):@(false) forKey:@"autoRenewal"];
    [vipInfoDic setValue:vipInfo.membershipName forKey:@"membershipName"];
    [vipInfoDic setValue:@(vipInfo.startTime) forKey:@"startTime"];
    [vipInfoDic setValue:@(vipInfo.expireAt) forKey:@"expireAt"];
    [vipInfoDic setValue:@(vipInfo.vipInterestsExpireTime) forKey:@"vipInterestsExpireTime"];
    [vipInfoDic setValue:@[] forKey:@"privileges"];
  }
  [userInfoDic setValue:vipInfoDic forKey:@"vipInfo"];
  NSString *vipInfoChangeStr = userInfoDic.xy_toJsonString;
  NSLog(@"VIP购买buy-vip-complete 发送小程序数据:%@",vipInfoChangeStr);
  [self notifyListeners:@"buy-vip-complete" data:userInfoDic];
}

/**
 选中文件上传进度监听
 */
- (void)upLoadPressNotification:(NSNotification *)notification{
  //    if(![self isCanResiveNotificaiton]) return;
  NSDictionary *fileUploadStateDic = notification.object;
  if(fileUploadStateDic.allKeys.count == 0) return;
  if(![[NBCAPMiniAppEngine sharedInstance].topMiniApp isEqualToString:self.appId]) return;
  [self uploadUpdata:fileUploadStateDic];
}

- (void)uploadUpdata:(NSDictionary *)fileUploadStateDic{
  XYWeakSelf
  dispatch_async(dispatch_get_main_queue(), ^{
    if(fileUploadStateDic.allKeys.count > 0){
      NSMutableDictionary *uploadFileEventData = [[NSMutableDictionary alloc] init];
      [uploadFileEventData setValue:weakSelf.appId forKey:@"appId"];
      [uploadFileEventData addEntriesFromDictionary:fileUploadStateDic];
      NSLog(@"文件上传状态同步到小程序：%@", [uploadFileEventData xy_toJsonString]);
      [weakSelf notifyListeners:@"upload-file" data:uploadFileEventData];
    }
  });
}

// 文件上传相关操作
- (void)uploadFileFrom:(NSInteger)channelType itemId:(NSString *)itemId {
  self.currentUploadItemId = itemId;
  self.channelType = channelType;
  if(channelType == 2){ //WEIXIN
    NSURL *thirdUrl = [NSURL URLWithString:[NSString stringWithFormat:@"%@://",@"weixin"]];
    if( [[UIApplication sharedApplication] canOpenURL:thirdUrl]){
      [[UIApplication sharedApplication] openURL:thirdUrl options:@{} completionHandler:^(BOOL success) {
        //  回调
      }];
    }else{
      [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000503", @"当前暂未安装该软件，请检查后重试")];
    }
  }else if(channelType == 3){//QQ
    NSURL *thirdUrl = [NSURL URLWithString:[NSString stringWithFormat:@"%@://",@"mqq"]];
    if( [[UIApplication sharedApplication] canOpenURL:thirdUrl]){
      [[UIApplication sharedApplication] openURL:thirdUrl options:@{} completionHandler:^(BOOL success) {
        //  回调
      }];
    }else{
      [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000503", @"当前暂未安装该软件，请检查后重试")];
    }
  }else if(channelType == 4){ //DINGTALK
    NSURL *thirdUrl = [NSURL URLWithString:@"dingtalk://dingtalkclient/page/link?url="];
    if( [[UIApplication sharedApplication] canOpenURL:thirdUrl]){
      [[UIApplication sharedApplication] openURL:thirdUrl options:@{} completionHandler:^(BOOL success) {
        //  回调
      }];
    }else{
      [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000503", @"当前暂未安装该软件，请检查后重试")];
    }
  }else if(channelType == 1){//History FILE

  }else if(channelType == 0){ //LOCALFILE
    [self openLocalFile];
  }
}

- (void)uploadFileFrom:(NSInteger)channelType itemId:(NSString *)itemId extraParams:(NSDictionary *)extraParams {
  self.currentUploadItemId = itemId;
  self.channelType = channelType;
  if(channelType == 2){ //WEIXIN
    NSURL *thirdUrl = [NSURL URLWithString:[NSString stringWithFormat:@"%@://",@"weixin"]];
    if( [[UIApplication sharedApplication] canOpenURL:thirdUrl]){
      [[UIApplication sharedApplication] openURL:thirdUrl options:@{} completionHandler:^(BOOL success) {
        //  回调
      }];
    }else{
      [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000503", @"当前暂未安装该软件，请检查后重试")];
    }
  }else if(channelType == 3){//QQ
    NSURL *thirdUrl = [NSURL URLWithString:[NSString stringWithFormat:@"%@://",@"mqq"]];
    if( [[UIApplication sharedApplication] canOpenURL:thirdUrl]){
      [[UIApplication sharedApplication] openURL:thirdUrl options:@{} completionHandler:^(BOOL success) {
        //  回调
      }];
    }else{
      [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000503", @"当前暂未安装该软件，请检查后重试")];
    }
  }else if(channelType == 4){ //DINGTALK
    NSURL *thirdUrl = [NSURL URLWithString:@"dingtalk://dingtalkclient/page/link?url="];
    if( [[UIApplication sharedApplication] canOpenURL:thirdUrl]){
      [[UIApplication sharedApplication] openURL:thirdUrl options:@{} completionHandler:^(BOOL success) {
        //  回调
      }];
    }else{
      [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000503", @"当前暂未安装该软件，请检查后重试")];
    }
  }else if(channelType == 1){//History FILE

  }else if(channelType == 0){ //LOCALFILE
    self.uploadExtraParams = extraParams;
    [self openLocalFileWithFileTypes:extraParams[@"fileType"] maxSize:[extraParams[@"maxSize"] intValue] allowedExtensions:extraParams[@"allowedExtensions"]];
  }
}

/**
 调起本地系统文件浏览器选取指定类型的文件
 */
- (void)openLocalFile{
  UIViewController *currentVC = [XYTool getCurrentVC];
  NSArray *documentTypes = @[@"public.content",@"public.image", @"com.adobe.pdf", @"com.microsoft.word.doc", @"com.microsoft.excel.xls", @"com.microsoft.powerpoint.ppt"];
  UIDocumentPickerViewController *documentPickerViewController = [[UIDocumentPickerViewController alloc] initWithDocumentTypes:documentTypes inMode:UIDocumentPickerModeOpen];
  documentPickerViewController.delegate = self;
  documentPickerViewController.modalPresentationStyle = UIModalPresentationFullScreen;
  [currentVC presentViewController:documentPickerViewController animated:YES completion:^{
  }];
}

+ (NSDictionary<NSString *, NSArray<NSString *> *> *)fileTypeUTIMapping {
    return @{
        @"image": @[(__bridge NSString *)kUTTypeImage],
        @"video": @[(__bridge NSString *)kUTTypeMovie],
        @"audio": @[(__bridge NSString *)kUTTypeAudio],
        @"pdf": @[(__bridge NSString *)kUTTypePDF],
        @"doc": @[
            (__bridge NSString *)kUTTypeText,
            @"com.microsoft.word.doc",
            @"application/vnd.ms-works" // 支持 .wps, .wks, .wdb
        ],
        @"docx": @[@"org.openxmlformats.wordprocessingml.document"],
        @"xls": @[@"com.microsoft.excel.xls"],
        @"xlsx": @[@"org.openxmlformats.spreadsheetml.sheet"],
        @"ppt": @[@"com.microsoft.powerpoint.ppt"],
        @"pptx": @[@"org.openxmlformats.presentationml.presentation"],
        @"binary": @[(__bridge NSString *)kUTTypeData]
    };
}


- (NSArray<NSString *> *)documentTypesForFileTypes:(NSArray<NSString *> *)fileTypes
                                         extensions:(NSArray<NSString *> *)extensions {
    NSDictionary<NSString *, NSArray<NSString *> *> *typeMap = [NIIMBOTPlugin fileTypeUTIMapping];
    NSMutableSet<NSString *> *result = [NSMutableSet set];

    BOOL hasFileType = (fileTypes.count > 0);
    BOOL hasExtensions = (extensions.count > 0);

    if (hasFileType) {
        for (NSString *type in fileTypes) {
            if (type.length == 0) continue;

            if ([type hasPrefix:@"."]) {
                // 扩展名 -> UTI
                NSString *ext = [type substringFromIndex:1];
                NSString *uti = (__bridge_transfer NSString *)UTTypeCreatePreferredIdentifierForTag(
                    kUTTagClassFilenameExtension,
                    (__bridge CFStringRef)ext,
                    NULL
                );
                if (uti) {
                    [result addObject:uti];
                }
            } else {
                // 类型映射 -> UTI
                NSArray *mapped = typeMap[type];
                if ([mapped isKindOfClass:[NSArray class]]) {
                    [result addObjectsFromArray:mapped];
                }
            }
        }
    } else if (hasExtensions) {
        for (NSString *ext in extensions) {
            if (ext.length == 0) continue;
            NSString *uti = (__bridge_transfer NSString *)UTTypeCreatePreferredIdentifierForTag(
                kUTTagClassFilenameExtension,
                (__bridge CFStringRef)ext,
                NULL
            );
            if (uti) {
                [result addObject:uti];
            }
        }
    }

    if (result.count == 0) {
        [result addObject:(__bridge NSString *)kUTTypeItem];
    }

    return result.allObjects;
}


- (void)openLocalFileWithFileTypes:(NSArray<NSString *> *)fileTypes
                          maxSize:(NSInteger)maxSize
               allowedExtensions:(NSArray<NSString *> *)allowedExtensions {

    UIViewController *currentVC = [XYTool getCurrentVC];
    NSArray<NSString *> *documentTypes = [self documentTypesForFileTypes:fileTypes extensions:allowedExtensions];

    UIDocumentPickerViewController *documentPickerViewController = [[UIDocumentPickerViewController alloc] initWithDocumentTypes:documentTypes inMode:UIDocumentPickerModeOpen];
    documentPickerViewController.delegate = self;
    documentPickerViewController.modalPresentationStyle = UIModalPresentationFullScreen;
    [currentVC presentViewController:documentPickerViewController animated:YES completion:nil];
}

// 上传失败统一处理
- (void)sendUploadFailedWithPath:(NSString *)path name:(NSString *)name size:(unsigned long long)size reason:(NSString *)reason {
    NSData *fileData = [NSData dataWithContentsOfFile:path];
    NSString *fileMD5 = fileData.MD5String ?: @"";
    NSString *extension = path.pathExtension ?: @"";
    NSMutableDictionary *info = [@{
        @"appId": UN_NIL(self.appId),
        @"key": fileMD5,
        @"name": UN_NIL(name),
        @"progress": @0,
        @"size": @(size),
        @"status": @"fail",
        @"type": extension,
        @"eventId": UN_NIL(self.currentUploadItemId),
        @"channelType": @(self.channelType),
        @"reason": UN_NIL(reason)
    } mutableCopy];
    [self notifyListeners:@"upload-file" data:info];
}


/**
 调用本地ios文件夹回调
 */

#pragma mark - UIDocumentPickerDelegate
- (void)documentPicker:(UIDocumentPickerViewController *)controller didPickDocumentsAtURLs:(NSArray<NSURL *> *)urls {
  if (![urls isKindOfClass:[NSArray class]] || urls.count == 0) {
    return;
  }

  if (controller.documentPickerMode == UIDocumentPickerModeOpen) {
    // 选取文件用于上传
    if (![urls isKindOfClass:[NSArray class]] || urls.count == 0) return;

        NSURL *fileURL = urls.firstObject;
        BOOL authorized = [fileURL startAccessingSecurityScopedResource];
        if (!authorized) return;

        NSString *fileName = fileURL.lastPathComponent;
        NSString *path = [[fileURL.absoluteString stringByRemovingPercentEncoding] stringByReplacingOccurrencesOfString:@"file://" withString:@""];

        NSFileManager *manager = [NSFileManager defaultManager];
        NSError *error = nil;
        NSDictionary *attrs = [manager attributesOfItemAtPath:path error:&error];
        unsigned long long fileSize = [attrs fileSize];

        NSMutableDictionary *dict = [@{
            @"fileName": UN_NIL(fileName),
            @"filePath": UN_NIL(path)
        } mutableCopy];

        // 有 uploadExtraParams 走限制逻辑
        if ([self.uploadExtraParams isKindOfClass:[NSDictionary class]] && self.uploadExtraParams.count > 0) {
            [dict addEntriesFromDictionary:self.uploadExtraParams];

            NSArray *allowedExtensions = self.uploadExtraParams[@"allowedExtensions"];
            NSNumber *maxSize = self.uploadExtraParams[@"maxSize"];
            NSArray<NSString *> *fileTypes = self.uploadExtraParams[@"fileType"];

            // 获取扩展名
            NSString *extension = [[fileName pathExtension] lowercaseString];


            BOOL extAllowed = YES;
            if ([allowedExtensions isKindOfClass:[NSArray class]] && allowedExtensions.count > 0) {
                extAllowed = [allowedExtensions containsObject:extension];
            }

            BOOL sizeAllowed = YES;
            if ([maxSize isKindOfClass:[NSNumber class]] && maxSize.unsignedLongLongValue > 0) {
                sizeAllowed = fileSize < maxSize.unsignedLongLongValue;
            }

            if (!extAllowed || !sizeAllowed) {
                NSString *reason = !extAllowed ? @"后缀不允许" : @"文件超出大小限制";
                [self sendUploadFailedWithPath:path name:fileName size:fileSize reason:reason];
                return;
            }

            [self uploadExcelFileWithPath:path uploadTag:[XYTool getNowTimeTimestamp]];
            return;
        }

        // 默认逻辑
        if ([path hasSuffix:@".HEIC11"]) {
            [ImageTool convertHEICToPNG:path completion:^(NSString *convertedPath) {
                NSMutableDictionary *convertedDict = [@{
                    @"fileName": UN_NIL(fileName),
                    @"filePath": UN_NIL(convertedPath)
                } mutableCopy];
                [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_EXCEL_UPLOAD object:nil userInfo:convertedDict];
            }];
        } else if ([manager fileExistsAtPath:path]) {
            [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_EXCEL_UPLOAD object:nil userInfo:dict];
        }
  } else if (controller.documentPickerMode == UIDocumentPickerModeExportToService) {
    // 选取路径用于导出
    // 获取授权
    BOOL fileUrlAuthozied = [urls.firstObject startAccessingSecurityScopedResource];
    if (fileUrlAuthozied) {
      // 通过文件协调工具来得到新的文件地址，以此得到文件保护功能
      NSFileCoordinator *fileCoordinator = [[NSFileCoordinator alloc] init];
      NSError *error;

      [fileCoordinator coordinateReadingItemAtURL:urls.firstObject
                                          options:0
                                            error:&error
                                       byAccessor:^(NSURL *newURL) {
        // 读取文件
        NSString *fileName = [newURL lastPathComponent];
        NSLog(@"[UIDocumentPickerViewController] save fileName : %@", fileName);
        if (self.saveFileToSystemFolder) {
          self.saveFileToSystemFolder(@([fileName isKindOfClass:[NSString class]] || [fileName length] > 0));
        }
      }];

      [urls.firstObject stopAccessingSecurityScopedResource];

      if (error != nil) {
        if (self.saveFileToSystemFolder) {
          self.saveFileToSystemFolder(@0);
        }
      }
    } else {
      // 授权失败
      if (self.saveFileToSystemFolder) {
        self.saveFileToSystemFolder(@0);
      }
    }
  }
}

#pragma clang diagnostic pop

/**
 获取ios本地文件类型
 */
- (NSString *)preferredUTIForExtension:(NSString *)ext
{
  // Request the UTI via the file extension
  NSString *theUTI = (__bridge_transfer NSString *)
  UTTypeCreatePreferredIdentifierForTag(
                                        kUTTagClassFilenameExtension,
                                        (__bridge CFStringRef) ext, NULL);
  return theUTI;
}

/**
 获取ios本地文件类型
 */
- (BOOL)pathPointsToLikelyUTIMatch:(NSString *)path uti:(CFStringRef)theUTI
{
  NSString *extension = path.pathExtension;
  NSString *preferredUTI = [self preferredUTIForExtension:extension];
  return (UTTypeConformsTo(
                           (__bridge CFStringRef) preferredUTI, theUTI));
}

/**
 根据本地文件地址判断是否支持上传
 */
- (BOOL)pathPointsToLikelySupporFile:(NSString *)path
{
  BOOL isSupportFile = NO;
  if([self pathPointsToLikelyUTIMatch:path uti:CFSTR("public.image")]){
    isSupportFile = YES;
  }else if ([self pathPointsToLikelyUTIMatch:path uti:CFSTR("com.adobe.pdf")]){
    isSupportFile = YES;
  }else if ([self pathPointsToLikelyUTIMatch:path uti:CFSTR("com.microsoft.word.doc")]){
    isSupportFile = YES;
  }else if ([self pathPointsToLikelyUTIMatch:path uti:CFSTR("org.openxmlformats.wordprocessingml.document")]){
    isSupportFile = YES;
  }else if ([self pathPointsToLikelyUTIMatch:path uti:CFSTR("com.microsoft.excel.xls")]){
    isSupportFile = YES;
  }else if ([self pathPointsToLikelyUTIMatch:path uti:CFSTR("org.openxmlformats.spreadsheetml.sheet")]){
    isSupportFile = YES;
  }else if ([self pathPointsToLikelyUTIMatch:path uti:CFSTR("com.microsoft.powerpoint.ppt")]){
    isSupportFile = YES;
  }else if ([self pathPointsToLikelyUTIMatch:path uti:CFSTR("org.openxmlformats.presentationml.presentation")]){
    isSupportFile = YES;
  }else if ([path hasSuffix:@".wps"] || [path hasSuffix:@".et"] || [path hasSuffix:@".ett"] || [path hasSuffix:@".dps"] || [path hasSuffix:@".dpt"]
            || [path hasSuffix:@".pot"]  || [path hasSuffix:@".dot"]  || [path hasSuffix:@".xlt"]){
    isSupportFile = YES;
  }
  return isSupportFile;
}

/**
 第三方软件选中文件使用云打印监听
 */
- (void)fileNotification:(NSNotification *)notifcation {
  XYWeakSelf
//  if(![self isCanResiveNotificaiton]) return;
  if(![[NBCAPMiniAppManager sharedInstance].currentAppId isEqualToString:self.appId]) return;
  XYViewController *vc = (XYViewController *)[XYTool getCurrentVC];
  NSDictionary *info = notifcation.userInfo;
  NSString *filePath = [info objectForKey:@"filePath"];
  if(![self pathPointsToLikelySupporFile:filePath]){
    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000208",@"不支持的文件格式")];
    return;
  }
  long long fileSize = [self fileSizeAtPath:filePath];
  if(fileSize > 100 * 1024 * 1024){
    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000211", @"文件大小不能超过100M")];
  }else{
    if(!self.isUploadLimit){
      if(NETWORK_STATE_ERROR){
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app00268", @"网络异常")];
        });
      }else{
        [weakSelf uploadExcelFileWithPath:filePath uploadTag:[XYTool getNowTimeTimestamp]];
      }
    }else if(fileSize > 5 * 1024 * 1024 && !m_user_vip){
      JC_TrackWithparms(@"show",@"108_247_228",@{});
      BOOL isNeedRenew = m_userModel.vipInfo != nil && !m_userModel.vipInfo.valid;
      NSString *headImage = isNeedRenew?@"vipRenewTip":@"fileSizeTip";
      NSString *title1 = isNeedRenew?XY_LANGUAGE_TITLE_NAMED(@"app100000199", @"您的会员已到期"):XY_LANGUAGE_TITLE_NAMED(@"app100000218", @"开通会员解锁100MB文件上传特权");
      NSString *title2 = isNeedRenew?XY_LANGUAGE_TITLE_NAMED(@"app100000207", @"该高级二维码中有文件超过5MB，如需保存请开通会员以保证超大文件的使用"):XY_LANGUAGE_TITLE_NAMED(@"app100000215", @"非会员仅可上传5MB文件");
      JCVipTipView *tipView = [[JCVipTipView alloc] initWithHeadImageName:headImage detail1:title1 detail2:title2 isRenewVip:isNeedRenew];
      [tipView setButtonEventBlock:^(NSNumber *tagNumber) {
        if(tagNumber.integerValue == 1){
          JC_TrackWithparms(@"click",@"108_247_230",@{});
        }else{
          JC_TrackWithparms(@"click",@"108_247_229",@{});
          [JCIAPHelper openViewWithAlert:vc needOpenTip:NO isUseVipSource:YES success:^{

          } failure:^(NSString *msg, id model) {
            [MBProgressHUD showToastWithMessageDarkColor:msg];
          } sourceInfo:@{@"sourcePage":@"025"}];
        }
      }];
      [tipView show];
    }else{
      if(NETWORK_STATE_ERROR){
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app00268", @"网络异常")];
        });
      }else{
        [weakSelf uploadExcelFileWithPath:filePath uploadTag:[XYTool getNowTimeTimestamp]];
      }
    }
  }
}

/**
 上传选中文件
 */

- (void)uploadExcelFileWithPath:(NSString *)filePath uploadTag:(NSString *)uploadTag{
  NSData *fileData = [NSData dataWithContentsOfFile:filePath];
  long long fileSize = [self fileSizeAtPath:filePath];
  NSString *fileMD5 = fileData.MD5String;
  NSString *fileType = filePath.pathExtension;
  NSString *fileName = filePath.lastPathComponent;
  NSDictionary *fileInfo = @{@"key":UN_NIL(fileMD5),@"name":UN_NIL(fileName),
                             @"progress":@0,@"size":@(fileSize),@"status":@"uploading",
                             @"type":UN_NIL(fileType),@"eventId":UN_NIL(self.currentUploadItemId),
                             @"channelType":@(self.channelType)};
  if(fileData == nil) {
    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"", @"无效文件")];
    return;
  }
  NSString *module = @"USER_HIPPO";
  if(self.notSaveToHippo == 1){
    module = @"USER_UPLOAD";
  }
  [self uploadUpdata:fileInfo];
  [[JCOSSManager sharedManager] oss_uploadFileWithParmsDic:@{@"module":module} fullFileName:filePath.lastPathComponent fileData:fileData fileInfo:fileInfo Success:^(NSString * _Nonnull key, NSString * _Nonnull url) {
    if (url && url.length > 0) {
      NSLog(@"上传成功刷新 URL地址：%@",url);
    }
  } failure:^(NSString * _Nonnull key, NSString * _Nonnull errMsg) {

  } uploadPress:^(NSDictionary *uploadInfoDic) {

  }];
  self.currentUploadItemId = @"";
}

/**
 获取文件大小
 */
- (long long)fileSizeAtPath:(NSString*) filePath{
  NSFileManager* manager = [NSFileManager defaultManager];
  if ([manager fileExistsAtPath:filePath]){
    return [[manager attributesOfItemAtPath:filePath error:nil] fileSize];
  }
  return 0;
}

/**
 获取指定url地址下内容
 */
- (void)getHtmlContentWith:(NSString *)htmlUrl complate:(XYBlock1)complateBlock{
  dispatch_queue_t serial_queue= dispatch_queue_create("com.liveCode.SerialQueue", NULL);
  dispatch_async(serial_queue, ^{
    NSURL *requestURL = [NSURL URLWithString:htmlUrl];
    NSURLRequest *request = [NSURLRequest requestWithURL:requestURL];
    NSURLSession *session = [NSURLSession sharedSession];

    NSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
      NSHTTPURLResponse *urlResponse = (NSHTTPURLResponse *)response;
      NSDictionary *headerDic = urlResponse.allHeaderFields;
      NSString *contentType = headerDic[@"Content-Type"];
      if([contentType containsString:@"text"]){
        NSString *contentStr = [NSString stringWithContentsOfURL:XY_URLWithString(htmlUrl) encoding:NSUTF8StringEncoding error:nil];
        dispatch_async(dispatch_get_main_queue(), ^{
          complateBlock(contentStr,contentType);
        });
      }else{
        NSString *contentStr = nil;
        dispatch_async(dispatch_get_main_queue(), ^{
          complateBlock(contentStr,contentType);
        });
      }
      NSLog(@"%@",headerDic);
    }];
    [dataTask resume];
  });
}

/**
 判断广播目标是自己则通知小程序层
 */
- (void)miniAppBroadcast:(NSNotification *)notifcation {
//  if(![self isCanResiveNotificaiton]) return;
  NSDictionary *info = notifcation.userInfo;
  if ([info[@"targetAppId"] isKindOfClass:[NSArray class]] &&
      [((NSArray *)info[@"targetAppId"]) containsObject:self.appId]) {
    [self notifyListeners:@"post-message" data:info];
  }
}


//
- (JCTemplateData *)getWifiCodeTemplateWith:(JCTemplateData *)oldTemplateData wifiTipTitle:(NSString *)title wifiCodeDetail:(NSString *)imageDataStr{
  JCTemplateData *newTemplate = oldTemplateData.copy;
  if(newTemplate.elements.count == 2){
    for (JCElementModel *elementModel in newTemplate.elements) {
      if([elementModel.type isEqualToString:@"text"]){
        elementModel.value = UN_NIL(title);
      }else if([elementModel.type isEqualToString:@"image"]){
        elementModel.imageData = imageDataStr;
        NSData *imageData = [imageDataStr jk_base64DecodedData];
        //    UIImage *image = [UIImage imageWithData:imageData];
        // TODO: Image Migration
//        NSString *imageLocalPath = ElementLocalPath(elementModel.elementId, NO);
//        [imageData writeToFile:imageLocalPath atomically:YES];
      }
    }
  }else if(newTemplate.elements.count == 0){
    JCElementModel *titleElement = [self createTextElementWith:title];
    JCElementModel *wifiCodeElement = [self createImageElementWith:imageDataStr];
    wifiCodeElement.imageProcessingValue = @[@250];
    float templateWidth = oldTemplateData.width;
    float templateHeight = oldTemplateData.height;
    float space = templateHeight * 0.05;
    float elementWidth = 0;
    float textElementWidth = 0;
    float wifiCodeheight = 0;
    if(templateWidth > templateHeight){
      elementWidth = templateHeight * 0.65;
    }else{
      elementWidth = templateWidth * 0.75;
    }
    textElementWidth = templateWidth * 0.9;
    wifiCodeheight = elementWidth;
    float textHeight = [self textHeightWith:titleElement width:textElementWidth];
    float textElementY = (templateHeight - wifiCodeheight - textHeight - space)/2;
    titleElement.x = (templateWidth - textElementWidth)/2;
    titleElement.y = textElementY;
    titleElement.width = textElementWidth;
    titleElement.height = textHeight;
    wifiCodeElement.x = (templateWidth - elementWidth)/2;
    wifiCodeElement.y = textElementY + textHeight + space;
    wifiCodeElement.width = elementWidth;
    wifiCodeElement.height = wifiCodeheight;
    NSArray *elements = @[titleElement,wifiCodeElement];
    newTemplate.elements = (NSArray<JCElementModel> *)elements;
  }
  return newTemplate;
}

- (float)textHeightWith:(JCElementModel *)model width:(float)elementWidth{
  model.rotate = 0;
  model.width = elementWidth;
  NSMutableDictionary *dict = [model.toSdk(YES).setCurrentPageIndex(0).showPlaceHolder(YES) elementDict].mutableCopy;
  // 由于此处整合序列号和时间，需要强制替换为文本格式
  [dict setObject:@"text" forKey:@"type"];
  NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:2];
  [params setObject:[XYTool fonts4SDK] forKey:@"usedFonts"];
  [params setObject:@[dict] forKey:@"elements"];
  NSString *json = [params xy_toJsonString];
  NSString *fontPath = [NSString stringWithFormat:@"%@/font",DocumentsFontPath];
  [ImageLibraryBridge initImageProcessing:fontPath error:nil];
  NSInteger screenScale = [UIScreen mainScreen].scale;
  float x;
  float y;//
  float width;
  float height;
  NSError *err;
  [ImageLibraryBridge generateTextPreviewImage:json
                               displayMultiple:DrawBoardInfo.mm2pxScale*screenScale
                                 printMultiple:DrawBoardInfo.dpiScale
                                             x:&x
                                             y:&y
                                         width:&width
                                        height:&height
                                    themeColor:JC_CURRENT_PRINT_COLOR
                                         error:&err];

  return 26 * height/width;
}

- (JCElementModel *)createTextElementWith:(NSString *)value
{
  JCElementModel *model = [JCElementModel new];
  model.type = @"text";
  model.value = value;
  model.fontFamily = XY_LANGUAGE_TITLE_NAMED(text_default_font_name, @"思源黑体");
  model.fontCode = text_default_font_code;
  model.fontSize = 3.2;
  model.textAlignHorizonral = 1;// 水平左对齐
  model.textAlignVertical = 1;//上下居中对齐
  model.lineSpacing = 0;
  model.wordSpacing = 0;
  model.lineBreakMode = 0;
  model.elementVersion = @"";
  model.letterSpacing = 0;
  model.lineMode = JCTextLineModeWidthFixed;
  model.typesettingMode = 1;
  model.typesettingParam = @[@0,@180];
  model.elementId = [NSString jk_UUID];
  return model;
}

- (JCElementModel *)createImageElementWith:(NSString *)baseData
{
  JCElementModel *model = [JCElementModel new];
  model.imageData = baseData;
  model.type = @"image";
  model.elementId = [NSString jk_UUID];
  model.localUrl = ElementLocalPath(model.elementId, NO);
  model.imageProcessingValue = @[@127];
  NSData *imageData = [baseData jk_base64DecodedData];
  NSString *imageLocalPath = ElementLocalPath(model.elementId, NO);
  if(![[NSFileManager defaultManager] fileExistsAtPath:RESOURCE_ELEMENT_PATH]){
      [[NSFileManager defaultManager] createDirectoryAtPath:RESOURCE_ELEMENT_PATH withIntermediateDirectories:YES attributes:nil error:nil];
  }
  NSError * error;
  BOOL isSuccess = [imageData writeToFile:imageLocalPath options:NSDataWritingAtomic error:&error];
  NSLog(@"JCEtagFileSaveManager write error:%@",error);
  return model;
}

// MARK: -- 危废事件 --
/// 跳转画板
/// - Parameter call: 回调
- (void)jumpToDrawingBoard:(CAPPluginCall *)call {
  // 样式
  NSString *layoutScheme = [call getString:@"layoutSchema" defaultValue:@""];
  // 数据
  NSString *formData = [call getString:@"formData" defaultValue:@""];
  // 获取lableID
  NSString *labelId = [call getString:@"labelId" defaultValue:@""];
  dispatch_async(dispatch_get_main_queue(), ^{
    // hud
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
    [self generateLayoutSchemeTemplate:layoutScheme formData:formData isPreview:NO result:^(JCTemplateData *templateData) {
      if (templateData) {
        // 下载标签纸数据
        [JCLabelInfoMangerHelper getServerLabelInfoWithLabelId:labelId barCode:@"" success:^(JCTemplateData *requestModel) {
          [hud hideAnimated:NO];
          // 跳入画板
          __block JCTemplateData *newData = nil;
          [JCTemplateDBManager db_queryTemplateDataById:labelId success:^(JCTemplateData *newLabelData) {
            if (newLabelData) {
              newData = [JCTMDataBindGoodsInfoManager templateOldData:templateData replaceWithLabelData:newLabelData];
              newData.name = templateData.name;
              newData.usedFonts = templateData.getNeedFonts;
              // 跳转画板
              dispatch_async(dispatch_get_main_queue(), ^{
                [JCFlutter2NativeHandler toFlutterCanvasWith:newData type:0 isPresent:YES];
              });
            }
          } failed:^(NSError *error) {
            [MBProgressHUD showToastWithMessageDarkColor:@"获取模板数据失败"];
          }];
        } field:^(id x) {
          [MBProgressHUD showToastWithMessageDarkColor:x];
        }];
      } else {
        [hud hideAnimated:NO];
      }
    }];
  });
}

/// 生成预览图
/// - Parameter call: 回调
- (void)generateTemplatePreviewWithLayout:(CAPPluginCall *)call __attribute__((deprecated("此方法用于危废调用生成预览图，业务原因暂不使用",""))) {
  // 样式
  NSString *layoutScheme = [call getString:@"layoutSchema" defaultValue:@""];
  // 数据
  NSString *formData = [call getString:@"formData" defaultValue:@""];
  // 生成TemplateData
  [self generateLayoutSchemeTemplate:layoutScheme formData:formData isPreview:YES result:^(JCTemplateData *templateData) {
    if (templateData) {
      // 生成预览图
      UIImage *previewImage = [JCTemplateImageManager getThumbNailImageWith:templateData];
      // base64编码
      NSData *imageData = UIImagePNGRepresentation(previewImage);
      NSString *encodedImageStr = (previewImage == nil) ? @"":[imageData base64EncodedStringWithOptions:NSDataBase64Encoding64CharacterLineLength];
      encodedImageStr = [encodedImageStr stringByReplacingOccurrencesOfString:@"\n" withString:@""];
      encodedImageStr = [encodedImageStr stringByReplacingOccurrencesOfString:@"\r" withString:@""];
      encodedImageStr = [encodedImageStr stringByReplacingOccurrencesOfString:@"\t" withString:@""];
      // 传递数据给capacitor小程序
      NSMutableDictionary *result = [NSMutableDictionary dictionary];
      [result addEntriesFromDictionary:@{@"imageData":encodedImageStr}];
      [self notifyListeners:@"generate-template-preview-with-layout" data:result];
    }
  }];
}

/// 半屏弹窗预览图刷新
/// - Parameter notification: 通知
- (void)JCPrintSettingAlertPreviewImageRefresh:(NSNotification *)notification {
  NSDictionary *notificationInfo = notification.object;
  NSString *base64Image = notificationInfo[@"imageBase64"];
  self.currentPreviewTemplateData = nil;
  if (!STR_IS_NIL(base64Image)) {
    [self generateTemplatePreviewWithTemplateData:base64Image];
  }
}

/// - Parameter notification: 通知
- (void)rfidColorRefreshPreview:(NSNotification *)notification {
//  if(self.currentPreviewTemplateData != nil){
    //    NBCAPMiniAppManager * manager = [NBCAPMiniAppManager sharedInstance];
    //    NSString * base64Image = [manager prewDataWith:self.currentPreviewTemplateData index:0];
    //    NSDictionary * data = @{@"appId":self.currentUniAppId,@"templatePreviewBase64":base64Image};
    //    [self notifyListeners:@"generate-template-preview" data:data];
    [self notifyListeners:@"rfid-color-change" data:@{@"appId":self.currentUniAppId,@"paperColor":UN_NIL([JCBluetoothManager sharedInstance].seviceLabelRFIDModel.paperColor),@"ribbonColor":UN_NIL([JCBluetoothManager sharedInstance].sevicecarbonRFIDModel.carbonColor)}];
//  }
}

//网络变化监听
- (void)didReciveSynchNotification{
  if(NETWORK_STATE_ERROR){
      return;
  }
//  dispatch_async(dispatch_get_main_queue(), ^{
//    // 插件生命周期随小程序关闭会释放, 这里交由 NBCAPMiniAppEngine 完成重启工作
//    [[NBCAPMiniAppEngine sharedInstance] restart:[NBCAPMiniAppManager sharedInstance].currentAppId];
//  });
}

/// 生成危废预览图
- (void)generateTemplatePreviewWithTemplateData:(NSString *)base64Image {
  NSDictionary *data = @{@"appId":self.currentUniAppId, @"templatePreviewBase64":base64Image};
  [self notifyListeners:@"generate-template-preview" data:data];
}

///移除事件监听
- (void)removeListeners:(CAPPluginCall *)call {
  NSDictionary *options = call.options;
  NSString *appId = options[@"appId"];
  NSArray *eventNames = options[@"events"];
  for (NSString *eventName in eventNames) {
    [self removeEventListener:eventName listener:call];
    NSLog(@"移除监听事件%@",eventName);
  }
}
/// 传递样式和数据跳转打印设置页面
/// - Parameter call: 回调
- (void)openPrintPageWithLayout:(CAPPluginCall *)call {
  // 如果正在加载危废打印页，则不处理
  if (self.isLoadingDangerCapPrintPage) {
    return;
  }
  // 标记为正在加载
  self.isLoadingDangerCapPrintPage = YES;
  // 获取appId
  NSString *appId = [call getString:@"appId" defaultValue:@""];

  if ([appId length] == 0) {
    [call reject:@"参数异常" :nil :nil :@{}];
    return;
  }
  self.currentUniAppId = appId;
  // 样式
  NSString *layoutScheme = [call getString:@"layoutSchema" defaultValue:@""];
  // 数据
  NSString *formData = [call getString:@"formData" defaultValue:@""];
  // 主题
  NSString *contentTheme = [call getString:@"theme" defaultValue:@""];
  // 是否补打
  BOOL isRePrint = [call getBool:@"isRePrint" defaultValue:NO];
  // 获取lableID
  NSString *labelId = [call getString:@"labelId" defaultValue:@""];
  // 设置补打参数
  [JCPrintManager sharedInstance].isDangerCapRePrint = isRePrint;
  // 设置主题色
  if (!STR_IS_NIL(contentTheme)) {
//    UIColor *connectSecheColor = [UIColor colorWithString:contentTheme];
//    [JCBluetoothManager sharedInstance].connectSecheColor = connectSecheColor;
    [JCPrintManager sharedInstance].printThemeColor = contentTheme;
  }else{
    [JCPrintManager sharedInstance].printThemeColor = @"#FB4B42";
  }
  // 生成模版数据
  dispatch_async(dispatch_get_main_queue(), ^{
    // hud
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
    [self generateLayoutSchemeTemplate:layoutScheme formData:formData isPreview:YES result:^(JCTemplateData *templateData) {
      if (templateData) {
        // 下载标签纸数据
        [JCLabelInfoMangerHelper getServerLabelInfoWithLabelId:labelId barCode:@"" success:^(JCTemplateData *requestModel) {
          [hud hideAnimated:NO];
          // 跳入画板
          __block JCTemplateData *newData = nil;
          [JCTemplateDBManager db_queryTemplateDataById:labelId success:^(JCTemplateData *newLabelData) {
            if (newLabelData) {
              newData = [JCTMDataBindGoodsInfoManager templateOldData:templateData replaceWithLabelData:newLabelData];
              newData.name = templateData.name;
              newData.usedFonts = templateData.getNeedFonts;
              // 跳转打印半屏
              [[JCPrintManager sharedInstance] doPrintWithTemplateModel:@[newData]
                                                               uniAppId:[NBCAPMiniAppManager sharedInstance].currentAppId
                                                             printScene:JCPrintSceneUniApp
                                                              historyId:nil
                                                          printComplate:^(NSNumber *complateCount) {
                // 记录历史记录
                if(complateCount.integerValue > 0){
                  NSDictionary * dic = @{@"appId":appId,@"success":@(1)};
                  [self notifyListeners:@"print-complete" data:dic];
                }
              }];
            }
            // 标记为结束加载
            self.isLoadingDangerCapPrintPage = NO;
          } failed:^(NSError *error) {
            [MBProgressHUD showToastWithMessageDarkColor:@"获取模板数据失败"];
            // 标记为结束加载
            self.isLoadingDangerCapPrintPage = NO;
          }];
        } field:^(id x) {
            [hud hideAnimated:NO];
            // 标记为结束加载
            self.isLoadingDangerCapPrintPage = NO;
        }];
      } else {
        [hud hideAnimated:NO];
        // 标记为结束加载
        self.isLoadingDangerCapPrintPage = NO;
      }
    }];
  });
}

/// 生成TemplateData
/// - Parameters:
///   - layoutScheme: 样式
///   - formData: 数据
///   - result: 返回的模版
- (void)generateLayoutSchemeTemplate:(NSString *)layoutScheme formData:(NSString *)formData isPreview:(BOOL)isPreview result:(void(^)(JCTemplateData *templateData))result {
    // 在Flutter端进行解析，拼接样式和数据
    [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"generateLayoutSchemeTemplate" arguments:@{
      @"layoutScheme": layoutScheme,
      @"formData": formData,
      @"isPreview": @(isPreview)
    } result:^(id value) {
      if ([value isKindOfClass:[NSString class]]) {
        __block JCTemplateData *templateData = [[JCTemplateData alloc] initWithString:value error:nil];
        // 下载模版数据
        [JCTemplateImageManager downLoadImagesForData:templateData options:DownAll complete:^(JCTemplateData *resultData) {
          // Update the template data with the result
          if (resultData) {
            templateData = resultData;
          }
          // 如果同步完毕，直接跳转画板
          BOOL is_ableEdit_Template = [resultData checkTemplateDetailByBackImage:YES containFont:NO];
          if(is_ableEdit_Template) {
            result(templateData);
          } else {
            result(nil);
          }
        }];
      } else {
        result(nil);
      }
    }];
}

- (void)checkLedgerNotification:(NSNotification *)notification {
  // 发送台账通知
  [self notifyListeners:@"jump-to-record" data:nil];
}//

// 关闭打印页面
- (void)closePrintPage:(CAPPluginCall *)call {
  [[JCAppEventChannel shareInstance] eventData: @{
      @"action": @"closePrintSettingDialog"
  }];
}

/**
 * 剪贴板通知处理
 */
- (void)clipboardNotification:(NSNotification *)notification {
  // Only process if this is the SPR889G app
//  if (![[NBCAPMiniAppEngine sharedInstance].topMiniApp isEqualToString:@"__CAP__SPR889G"]) {
//    return;
//  }

  NSDictionary *userInfo = notification.userInfo;
  if (userInfo && userInfo[@"data"]) {
    NSString *data = userInfo[@"data"];
    NSLog(@"[Capacitor Plugin] Received clipboard notification with data: %@", data);
    // Remove any stored key from UserDefaults to prevent duplicate sends
    [[NSUserDefaults standardUserDefaults] removeObjectForKey:PENDING_CLIPBOARD_KEY];
    [self notifyListeners:@"clipboard-update" data:@{@"data": data}];
  }
}

// H5获取原生沙盒路径
- (void)getIOSBasePath:(CAPPluginCall *)call {
  NSDictionary *getIOSBasePathInfo = @{@"basePath": NSHomeDirectory()};
  [call resolve:getIOSBasePathInfo];
}

/**
 * 机器别名修改通知处理
 */
- (void)machineAliasChangeNotification:(NSNotification *)notification {
  NSDictionary *userInfo = notification.userInfo;
  if (userInfo) {
    NSString *appId = STR_IS_NIL(self.currentUniAppId) ? @"" : self.currentUniAppId;
    NSString *machineAlias = STR_IS_NIL(userInfo[@"machineAlias"]) ? @"" : userInfo[@"machineAlias"];

    NSDictionary *eventData = @{
      @"appId": appId,
      @"machineAlias": machineAlias
    };

    [self notifyListeners:@"machine-alias-change" data:eventData];
  }
}


// 取消上传任务
- (void)cancelUploadTask:(CAPPluginCall *)call {
//  NSString *eventId = [call getString:@"eventId" defaultValue:@""];
  if (![[JCOSSManager sharedManager].currentPut isCancelled]) {
    [[JCOSSManager sharedManager].currentPut cancel];
    [JCOSSManager sharedManager].currentPut = nil;
  }
}

@end
