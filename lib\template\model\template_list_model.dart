import 'dart:async';

import 'package:niimbot_template/models/parse/parse_resource.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart' as CanvasTemplate;
import 'package:niimbot_template/template_parse.dart';

import '../util/template_transform_utils.dart';
///模版列表model
class TemplateListModel {
  num? total;
  num? page;
  num? limit;
  List<TemplateData>? list;
  List<num>? deletedTemplates;
  bool hasMore = false;

  static Future<TemplateListModel> fromJson(dynamic json) async {
    List? list = null;
    if (json['list'] != null) {
      list = json['list'];
    }
    List<num>? deletedList = null;
    if (json['deletedTemplates'] != null) {
      deletedList = [];
      json['deletedTemplates']?.forEach((value) {
        deletedList?.add(value);
      });
    }
    List<TemplateData> templateList = [];
    if (list?.isNotEmpty == true) {
      await Future.forEach(list!, (v) async {
        TemplateData templateData = await TemplateTransformUtils.parseServiceTemplateJsonToNiimbotTemplate(v as Map<String,dynamic>,needParseElements: false);
        templateList.add(templateData);
      });
    }
    return TemplateListModel()
      ..total = json['total']
      ..page = json['page']
      ..limit = json['limit']
      ..list = templateList
      ..deletedTemplates = deletedList;
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['total'] = total;
    map['page'] = page;
    map['limit'] = limit;
    if (list != null) {
      map['list'] = list?.map((v) => v.toJson()).toList();
    }
    map['deletedTemplates'] = deletedTemplates;
    return map;
  }


  Future<Map<String, dynamic>> toJsonMap() async{
    final map = <String, dynamic>{};
    map['total'] = total;
    map['page'] = page;
    map['limit'] = limit;
    map['hasMore'] = hasMore;
    if (list != null) {
      List<Map<String,dynamic>> resultList = [];
      for(int i = 0;i<list!.length;i++){
        TemplateData v = list![i];
        CanvasTemplate.TemplateData canvasTemplate = await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(v);
        Map<String, dynamic> json = canvasTemplate.toJson(handleTemplateType: false);
        resultList.add(json);
      }
      map['list'] = resultList;
    }
    map['deletedTemplates'] = deletedTemplates;
    return map;
  }
}
