import 'dart:io';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/interface/print_setting_channel_interface.dart';
import 'package:niimbot_print_setting_plugin/model/print_paramter.dart';
import 'package:niimbot_print_setting_plugin/print_page_manager.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart';

class PrintSlowPage extends StatefulWidget {
  PrintTaskType taskType;
  PrintParameter? parameter;
  PrintPageManager pageManager;
  PrintSettingChannelInterface channel;

  PrintSlowPage(this.taskType, this.parameter, this.pageManager, this.channel, {Key? key}) : super(key: key);

  @override
  _PrintSlowPageState createState() => _PrintSlowPageState();
}

class _PrintSlowPageState extends State<PrintSlowPage> {
  late PrintSettingLogic logic;
  String? capId;
  bool isClosing = false;
  @override
  void initState() {
    super.initState();
    logic = PrintSettingLogic(widget.taskType, widget.parameter, widget.pageManager, widget.channel);
    logic.addListenerEventBus((result) {
      if (!result) {
        if (!Platform.isWindows) {
          Fluttertoast.showToast(
              msg: logic.pageManager.style.bindDataError,
              gravity: ToastGravity.CENTER,
              backgroundColor: const Color(0xCC333333),
              textColor: Colors.white);
        }
        Future.delayed(const Duration(seconds: 2), () {
          Navigator.pop(context);
        });
      }
    });
    Get.put(logic);
    if (widget.taskType == PrintTaskType.miniApp) {
      logic.channel.trackEvent("view", "55",
          eventData: {'mpname': logic.parameter!.customData?['uniAppInfo']?['uniAppName'] ?? ""});
    } else {
      logic.channel.trackEvent("view", "024");
    }
    logic.channel.trackEvent("view", "134");
    logic.stream.listen((data) {
      if (data["action"] == "closePrintSettingDialog") {
        debugPrint("closePrintSettingDialog:关闭半模态弹窗");
        if (isClosing) return;
        isClosing = true;
        Navigator.pop(context);
      }
    });
    Map<String, dynamic>? customData = logic.parameter!.customData;
    Map<dynamic, dynamic>? uniAppInfo = customData?["uniAppInfo"];
    capId = uniAppInfo?["uniAppId"];
  }

  @override
  void dispose() {
    super.dispose();
    logic.destroyEventBus();
    Get.delete<PrintSettingLogic>();
  }

  @override
  Widget build(BuildContext context) {
    logic.initGenerateDesignRatio(context);
    double ratio = 0.6;
    if (capId == "__CAP__SPR666G") {
      ratio = 0.8;
    }
    return widget.taskType == PrintTaskType.printNullUi ||
            widget.taskType == PrintTaskType.printC1 ||
            widget.taskType == PrintTaskType.printNullUiShowProgress
        ? const SizedBox.shrink()
        : GestureDetector(
            onTap: () {
              if (Platform.isIOS) {
                FocusScopeNode currentFocus = FocusScope.of(context);
                if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
                  FocusManager.instance.primaryFocus?.unfocus();
                }
              }
              debugPrint("拦截点击空白位置发生的页面重绘");
            },
            child: PopScope(
              canPop: ModalRoute.of(context)?.isCurrent == true,
              child: Container(
                  height: MediaQuery.of(context).size.height * ratio + MediaQuery.of(context).viewInsets.bottom / 2,
                  // padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
                  decoration: BoxDecoration(
                    color: Color(0xFFFAFAFA),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(12.0),
                      topRight: Radius.circular(12.0),
                    ),
                  ),
                  child: logic.pageManager.printPageItems(logic)),
            ),
          );
  }
}
