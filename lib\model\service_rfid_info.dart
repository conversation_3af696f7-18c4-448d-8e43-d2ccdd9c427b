class ServiceRfidInfo {
  //色值（双色碳带时包含2个色值，逗号分割）
  var rfidColor = "";
  var rfidPaperColor = "";
  var rfidRibbonColor = "";

  //Map<String, RFIDLabelInfo>? rfidLabelInfos;

  isDoubleColorRibbon() => rfidRibbonColor.split(',').length > 1;

  List<int>? getPreviewColor(bool isSupportRFID) {
    if (isSupportRFID) {

      // 检查rfidPaperColor是否符合要求
      if (rfidPaperColor.isNotEmpty && rfidPaperColor.split('.').length == 3) {
        try {
          return rfidPaperColor.split('.').map((e) => int.parse(e)).toList();
        } catch (e) {
          // 如果转换失败，继续检查rfidRibbonColor
        }
      }
    } else {
      // 检查rfidRibbonColor是否符合要求
      if (rfidRibbonColor.isNotEmpty && rfidRibbonColor.split('.').length == 3) {
        try {
          return rfidRibbonColor.split('.').map((e) => int.parse(e)).toList();
        } catch (e) {
          // 如果转换失败，返回null
        }
      }
    }

    return null;
  }
}
