//
//  JCGoodsTemplateSearchController.m
//  Runner
//
//  Created by huzi_0118 on 2021/9/25.
//  Copyright © 2021 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCGoodsTemplateSearchController.h"
#import "JCGoodTemplateTypeMode.h"
#import "JCSegmentCollectionCell.h"
#import "JCTemplateImageManager.h"
#import "JCCollectionViewCell.h"
#import "JCGoodsTemplateView.h"
#import "JCTemplateList.h"
#import "YLButton.h"
#import "JCGrayManager.h"


@interface JCGoodsTemplateSearchController ()<UICollectionViewDelegate,UICollectionViewDataSource>

@property(nonatomic,strong) JCGoodsTemplateView *mainView;
@property(nonatomic,strong) UICollectionView *templateListView;
@property(nonatomic,strong) NSArray *arrTypeDecrList;
@property(nonatomic,strong) JCGoodTemplateTypeMode *selectTypeModel;
@property(nonatomic,strong) NSString *searchKeywords;
@end

@implementation JCGoodsTemplateSearchController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.lsl_prefersNavigationBarHidden = YES;
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(templatechange:) name:TEMPLATE_CHANGED object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(synchTemplateFinish:) name:JCNOTICATION_SYNC_FINISH object:nil];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self.mainView resetSearchBarFirstResponder:YES];
    });
    // Do any additional setup after loading the view.
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    [self.mainView resetSearchBarFirstResponder:NO];
}

- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)initUI{
    [super initUI];
    XYWeakSelf
    JCGoodsTemplateView *mainView = [[JCGoodsTemplateView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT) searchType:YES];
    mainView.templateCollView.delegate = self;
    mainView.templateCollView.dataSource = self;
    mainView.segmentCollectionView.delegate = self;
    mainView.segmentCollectionView.dataSource = self;
    mainView.templateCollView.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;
    mainView.segmentCollectionView.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;
    mainView.templateCollView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
        [weakSelf loadGoodsTemplate:NO templateTypeMode:weakSelf.selectTypeModel];
    }];
    [mainView setCancelBlock:^{
        [weakSelf.navigationController popViewControllerAnimated:NO];
    }];
    [mainView setSearchBlock:^(NSString *searchKeywords) {
        weakSelf.searchKeywords = searchKeywords;
        [self.templateListView.mj_footer resetNoMoreData];
        JC_TrackWithparms(@"click",@"014_040_172",(@{@"key_word":UN_NIL(searchKeywords)}));
        [weakSelf loadGoodsTemplate:YES templateTypeMode:weakSelf.selectTypeModel];
    }];
    mainView.templateCollView.mj_footer = [MJRefreshAutoNormalFooter footerWithRefreshingBlock:^{
        [weakSelf loadMoreGoodsTemplateWithTypeMode:weakSelf.selectTypeModel];
    }];
    mainView.templateCollView.mj_footer.hidden = YES;
    [self.view addSubview:mainView];
    self.templateListView = mainView.templateCollView;
    self.mainView = mainView;
}

- (void)initNet{
    [super initNet];
    // 直接加载模板数据，不需要预先查询本地数据库
    [self loadGoodsTemplate:YES templateTypeMode:self.selectTypeModel];
}

- (NSArray *)arrTypeDecrList{
    if(!_arrTypeDecrList){
        JCGoodTemplateTypeMode *lastTemplateModel = [[JCGoodTemplateTypeMode alloc] init];
        lastTemplateModel.goodTemplateId = @"1";
        lastTemplateModel.isSelected = @"1";
        lastTemplateModel.pageNumber = @"1";
        lastTemplateModel.name = XY_LANGUAGE_TITLE_NAMED(@"", @"最新");
        lastTemplateModel.templateArr = @[].mutableArray;
        
        JCGoodTemplateTypeMode *historyTemplateModel = [[JCGoodTemplateTypeMode alloc] init];
        historyTemplateModel.goodTemplateId = @"2";
        historyTemplateModel.isSelected = @"0";
        historyTemplateModel.pageNumber = @"1";
        historyTemplateModel.name = XY_LANGUAGE_TITLE_NAMED(@"", @"历史模板");
        historyTemplateModel.templateArr = @[].mutableArray;
        _arrTypeDecrList = @[lastTemplateModel,historyTemplateModel];
    }
    return _arrTypeDecrList;
}

- (JCGoodTemplateTypeMode *)selectTypeModel{
    JCGoodTemplateTypeMode *selectTypeModel = nil;
    for (JCGoodTemplateTypeMode *templateTypeModel in self.arrTypeDecrList) {
        if(templateTypeModel.isSelected.integerValue == 1){
            selectTypeModel = templateTypeModel;
            break;
        }
    }
    _selectTypeModel = selectTypeModel;
    return _selectTypeModel;
}

- (void)loadGoodsTemplate:(BOOL)isShowLoading templateTypeMode:(JCGoodTemplateTypeMode *)templateTypeMode{
    templateTypeMode.pageNumber = @"1";
    [self.templateListView.mj_footer resetNoMoreData];
    [self getTemplateList:isShowLoading templateTypeMode:templateTypeMode];
}

- (void)loadMoreGoodsTemplateWithTypeMode:(JCGoodTemplateTypeMode *)templateTypeMode{
    templateTypeMode.pageNumber = StringFromInt(templateTypeMode.pageNumber.integerValue + 1);
    [self getTemplateList:NO templateTypeMode:templateTypeMode];
}

- (void)getTemplateList:(BOOL)isShowLoading templateTypeMode:(JCGoodTemplateTypeMode *)templateTypeMode{
    XYWeakSelf
    if(NETWORK_STATE_ERROR){
        [self.templateListView.mj_header endRefreshing];
        [self.templateListView.mj_footer endRefreshing];
        
        // 网络错误时直接显示空态
        templateTypeMode.templateArr = @[].mutableCopy;
        [self refreshTemplateData];
        return;
    }
    
    // 准备参数调用Flutter方法
    NSNumber *page = @(templateTypeMode.pageNumber.intValue);
    NSNumber *limit = @10;
    NSNumber *commodityTemplate = @(templateTypeMode.goodTemplateId.intValue);
    NSString *searchKey = self.searchKeywords ?: @"";
    
    // 显示加载指示器
    MBProgressHUD *hud = nil;
    if (isShowLoading) {
        hud = [MBProgressHUD showHUDAddedTo:hudWindow animated:YES contentColor:hudContentColor backColor:hudBackColor];
    }
    
    // 使用JCTemplateDBManager的nativeGetMyGoodTemplateList方法获取模板列表
    [JCTemplateDBManager nativeGetMyGoodTemplateList:page 
                                              limit:limit 
                                  commodityTemplate:commodityTemplate 
                                          searchKey:searchKey 
                                            success:^(NSArray<JCTemplateData *> *templates, NSDictionary *response) {
        [weakSelf.templateListView.mj_header endRefreshing];
        [weakSelf.templateListView.mj_footer endRefreshing];
        
        if (hud) {
            [hud hideAnimated:YES];
        }
        
        // 更新模板类型模式中的数据
        if (templateTypeMode.pageNumber.integerValue == 1) {
            templateTypeMode.templateArr = [templates mutableCopy];
        } else {
            NSMutableArray *currentTemplates = [templateTypeMode.templateArr mutableCopy];
            [currentTemplates addObjectsFromArray:templates];
            templateTypeMode.templateArr = currentTemplates;
        }
        
        // 处理加载更多状态
        if (templates.count < limit.integerValue) {
            [weakSelf.templateListView.mj_footer endRefreshingWithNoMoreData];
            weakSelf.templateListView.mj_footer.hidden = YES;
        } else {
            [weakSelf.templateListView.mj_footer resetNoMoreData];
            weakSelf.templateListView.mj_footer.hidden = NO;
        }
        
        [weakSelf refreshTemplateData];
    } failed:^(NSString *errorMsg) {
        [weakSelf.templateListView.mj_header endRefreshing];
        [weakSelf.templateListView.mj_footer endRefreshing];
        
        if (hud) {
            [hud hideAnimated:YES];
        }
        
        // 处理错误情况，只在第一页时清空数据
        if (templateTypeMode.pageNumber.integerValue == 1) {
            templateTypeMode.templateArr = @[].mutableCopy;
        }
        [weakSelf refreshTemplateData];
        
        [MBProgressHUD showToastWithMessageDarkColor:errorMsg];
    }];
}

- (void)refreshTemplateData{
    [self.templateListView reloadData];
    if(self.selectTypeModel.templateArr.count == 0){
        [self showNoDateView:2];
    }else{
        [self showNoDateView:0];
    }
}

// 0  隐藏缺省  1  显示缺省（无最新商品标签及历史标签） 2 显示缺省
- (void)showNoDateView:(NSInteger)showType{
    [self.mainView showStoreNoDataView:showType];
}

- (void)templatechange:(NSNotification *)notification{
    // 收到模板变更通知后重新加载数据
    [self loadGoodsTemplate:NO templateTypeMode:self.selectTypeModel];
}

- (void)synchTemplateFinish:(NSNotification *)notification{
    NSDictionary *oldNewIdDic = notification.object;
    NSString *oldId = [oldNewIdDic objectForKey:@"oldXYId"];
    NSString *newId = [oldNewIdDic objectForKey:@"newXYId"];
    if(oldId.length != 0 && newId.length != 0){
        // 收到同步完成通知后重新加载数据
        [self loadGoodsTemplate:NO templateTypeMode:self.selectTypeModel];
    }
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
}

#pragma mark ListCollectionViewDelegate

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView{
    return 1;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    NSInteger itemCount = 0;
    if(collectionView.tag == 1){
        itemCount = self.arrTypeDecrList.count;
    }else{
        itemCount = self.selectTypeModel.templateArr.count;
    }
    return itemCount;
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    if(collectionView.tag == 1){
        JCGoodTemplateTypeMode *templateTypeModel = [self.arrTypeDecrList safeObjectAtIndex:indexPath.row];
        JCSegmentCollectionCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"JCSegmentCollectionCell" forIndexPath:indexPath];
        [cell refreshWithTitle:templateTypeModel.name isSelected:templateTypeModel.isSelected.integerValue == 1];
        return cell;
    }else{
        JCTemplateData *goodsTemplate = [self.selectTypeModel.templateArr safeObjectAtIndex:indexPath.row];
        JCCollectionViewCell *cell = (JCCollectionViewCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"JCCollectionViewCell" forIndexPath:indexPath];
        cell.isShowImport = NO;
        [cell setPrintButtonShow:NO];
        [cell setTemplateData:goodsTemplate];
        return cell;
    }
}

- (CGSize)collectionView:(UICollectionView*)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath*)indexPath{
    if(collectionView.tag == 1){
        JCGoodTemplateTypeMode *templateTypeModel = [self.arrTypeDecrList safeObjectAtIndex:indexPath.row];
        NSString *title = templateTypeModel.name;
        UIFont *font = (templateTypeModel.isSelected.integerValue == 1)?MY_FONT_Bold(16):MY_FONT_Bold(14);
        CGSize titleSize = [title jk_sizeWithFont:font constrainedToHeight:1000];
        return CGSizeMake(titleSize.width, 53);
    }else{
        float itemWidth = (SCREEN_WIDTH - 30 - 15)/2;
        float itemHeight = itemWidth * 159 / 164;
        CGSize itemSize = CGSizeMake(itemWidth, itemHeight);
        return itemSize;
    }
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    if(collectionView.tag == 1){
        [self showNoDateView:0];
        JCGoodTemplateTypeMode *selectTypeModel = [self.arrTypeDecrList safeObjectAtIndex:indexPath.row];
        if(selectTypeModel.goodTemplateId.integerValue == self.selectTypeModel.goodTemplateId.integerValue) return;
        for (JCGoodTemplateTypeMode *templateTypeModel in self.arrTypeDecrList) {
            if(selectTypeModel.goodTemplateId.integerValue == templateTypeModel.goodTemplateId.integerValue){
                templateTypeModel.isSelected = @"1";
            }else{
                templateTypeModel.isSelected = @"0";
            }
        }
        // 清空数据并重新加载
        self.selectTypeModel.templateArr = @[].mutableCopy;
        [self refreshTemplateData];
        [collectionView reloadData];
        [self loadGoodsTemplate:NO templateTypeMode:self.selectTypeModel];
    }else{
        JCTemplateData *selectTemplate = [self.selectTypeModel.templateArr safeObjectAtIndex:indexPath.row];
        [self preareToEditWithTemplateModel:selectTemplate];
    }
}

- (void)preareToEditWithTemplateModel:(JCTemplateData *)model{
    XYWeakSelf
//    BOOL is_ableEdit_Template = [model checkTemplateDetailByBackImage:YES containFont:NO];
//    JCTemplateData *tempModel = model;
//    if(is_ableEdit_Template){
//      TemplateSource source = TemplateSource_Mine;
//      if([JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:tempModel.templateVersion] == false){
//          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000343", @"您的软件版本过低，请升级")];
//          return;
//      }
//      [JCTemplateFunctionHelper toEdit:tempModel];
//    }else{
//        if(NETWORK_STATE_ERROR){
//            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
//        }else{
//            __block NSString *requestUrl = @"";
//            __block NSString *requestType = @"";
//            [JCGrayManager gotoGrayModulePage:GrayModuleDrawingBoard routeBlock:^(JCGrayModuleModel * _Nonnull grayModel) {
//                switch (grayModel.branchType) {
//                    case GrayBranchA: {
//                        requestType = @"Post";
//                        requestUrl = J_myTemplate_Custom;
//                    }
//                    break;
//                    case GrayBranchB: {
//                        requestType = @"Get";
//                        requestUrl = J_myTemplate_Custom_v2;
//                    }
//                        break;
//                    default:
//                        break;
//                }
//            }];
//            MBProgressHUD *progressHUD = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
//            progressHUD.label.text = XY_LANGUAGE_TITLE_NAMED(@"app01115", @"数据加载中");
//            NSString *postUrl = [NSString stringWithFormat:@"%@/%@",requestUrl,UN_NIL(tempModel.idStr)];
//            NSDictionary *postDic = @{};
////            if(is_ableEdit_Template){
////                postUrl = J_myTemplateUpdateStatus_Custom;
////                postDic = @{@"id":UN_NIL(tempModel.idStr),@"currentTime":tempModel.profile.extrain.updateTime};
////            }
//            [self java_requestWithValues:postDic ModelType:[JCTemplateData class] requestType:requestType Path:postUrl hud:nil Success:^(__kindof YTKBaseRequest *request, JCTemplateData *model) {
//                model.localType = JCLocalType_Sync;
//                if(tempModel){
//                    if (![tempModel.profile.extrain.updateTime isEqualToString:model.profile.extrain.updateTime] || tempModel.localType != JCLocalType_Sync){
//                        [JCTemplateDBManager db_insertOrUpdateTemplateData:model];
//                        __block JCTemplateData *blockModel = model;
//                        [JCTemplateImageManager downLoadImagesForData:blockModel options:DownAll complete:^(JCTemplateData *resultData) {
//                            blockModel = resultData;
//                            [JCLabelInfoMangerHelper getServerLabelInfoWithTemplate:blockModel success:^(JCTemplateData *labelInfo) {
//                                [progressHUD hideAnimated:YES afterDelay:1];
//                                [weakSelf jumpToTemplateEditControllerWith:tempModel];
//                            } field:^(id x) {
//                                [progressHUD hideAnimated:YES afterDelay:1];
//                                [weakSelf jumpToTemplateEditControllerWith:tempModel];
//                            }];
//                        }];
//                    }else{
//                        __block JCTemplateData *blockModel = model;
//                        [JCTemplateImageManager downLoadImagesForData:blockModel options:DownAll complete:^(JCTemplateData *resultData) {
//                            blockModel = resultData;
//                            [JCLabelInfoMangerHelper getServerLabelInfoWithTemplate:blockModel success:^(JCTemplateData *labelInfo) {
//                                [progressHUD hideAnimated:YES afterDelay:1];
//                                [weakSelf jumpToTemplateEditControllerWith:tempModel];
//                            } field:^(id x) {
//                                [progressHUD hideAnimated:YES afterDelay:1];
//                                [weakSelf jumpToTemplateEditControllerWith:tempModel];
//                            }];
//                        }];
//                    }
//                }else{
//                    [JCTemplateDBManager db_insertOrUpdateTemplateData:model];
//                    __block JCTemplateData *blockModel = model;
//                    [JCTemplateImageManager downLoadImagesForData:blockModel options:DownAll complete:^(JCTemplateData *resultData) {
//                        blockModel = resultData;
//                        [progressHUD hideAnimated:YES afterDelay:1];
//                        [weakSelf jumpToTemplateEditControllerWith:tempModel];
//                    }];
//                }
//            } failure:^(NSString *msg, id model) {
//                [progressHUD hideAnimated:YES];
//                [MBProgressHUD showToastWithMessageDarkColor:msg];
//            }];
//        }
//    }
    [JCTemplateFunctionHelper toEdit:model];
}

- (void)jumpToTemplateEditControllerWith:(JCTemplateData *)model{
    // 重新加载数据而不是使用getTemplateDataFromDb
    [self loadGoodsTemplate:NO templateTypeMode:self.selectTypeModel];
    
    [JCTemplateDBManager db_queryTemplateDataById:model.idStr success:^(JCTemplateData *tempModel) {
      TemplateSource source = TemplateSource_Mine;
      if([JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:tempModel.templateVersion] == false){
          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000343", @"您的软件版本过低，请升级")];
          return;
      }
      [JCTemplateFunctionHelper toEdit:tempModel];
    } failed:^(id msg) {
        // Template not found
    }];
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
