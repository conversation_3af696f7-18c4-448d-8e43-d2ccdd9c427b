//
//  JCUserSecretView.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2020/1/6.
//  Copyright © 2020 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCUserSecretView.h"
#import "UILabel+YBAttributeTextTapAction.h"

@interface JCUserSecretView()
@property (weak, nonatomic) IBOutlet UIButton *agreeButton;
@property (weak, nonatomic) IBOutlet UIButton *unAgreeButton;
@property (weak, nonatomic) IBOutlet UIScrollView *contentScrollView;
@property (weak, nonatomic) IBOutlet UILabel *titleLabel;
@property (strong, nonatomic)  UILabel *contentLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewHeight;
@end

@implementation JCUserSecretView

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/
static JCUserSecretView *view = nil;
static UIView *backView = nil;

- (void)awakeFromNib {
    [super awakeFromNib];
    [self.mainView xy_setCornerRadius:20];
    self.mainViewHeight.constant = 420;
}

+ (void)showWithView:(UIView*)mainView title:(NSString *)title message:(NSString *)message block:(XYBlock)block {
    //设置弹出视图
    if (view == nil) {
        view = [JCUserSecretView xy_xib];
        [view setFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        view.clickViewBtnBlock = block;
        view.agreeButton.layer.cornerRadius = 10;
        view.titleLabel.text = title;
        [view.agreeButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app01094", @"我同意") forState:UIControlStateNormal];
        [view.unAgreeButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app01093", @"暂不使用") forState:UIControlStateNormal];
        view.unAgreeButton.layer.cornerRadius = 10;
        view.contentLabel = [[UILabel alloc] init];
        NSString *str = [message stringByReplacingOccurrencesOfString:@"\\n" withString:@" \r\n" ];
        
        // 根据当前语言环境选择字体
        UIFont *calculationFont = MY_FONT_Bold(16); // 统一使用PingFang
        
        CGSize labelsize = [str boundingRectWithSize:CGSizeMake(SCREEN_WIDTH - 142, 1000)
                          options:NSStringDrawingUsesLineFragmentOrigin
                          attributes:@{NSFontAttributeName:calculationFont}
                          context:nil].size;
        
        // 针对非中文语言添加系数
        if ([XY_JC_LANGUAGE_REAL isEqualToString:@"ko"]) {
                // 韩语需要更多额外空间
                labelsize.height *= 1.2;
        }
        
        view.contentLabel.frame = CGRectMake(26, 0, SCREEN_WIDTH - 142, labelsize.height);
        view.contentLabel.numberOfLines = 0;
        NSString *str1 = XY_LANGUAGE_TITLE_NAMED(@"app01189", @"用户协议");
        NSString *str2 = XY_LANGUAGE_TITLE_NAMED(@"app01079", @"《隐私政策》");
        NSRange rang1 = [str rangeOfString:str1];
        NSRange rang2 = [str rangeOfString:str2];
        NSMutableAttributedString *attr = [[NSMutableAttributedString alloc] initWithString:str];
        [attr addAttributes:@{NSFontAttributeName:MY_FONT_Regular(16),NSForegroundColorAttributeName:XY_HEX_RGB(0x333333)} range:NSMakeRange(0, str.length)];
        [attr addAttributes:@{NSFontAttributeName:MY_FONT_Regular(16),NSForegroundColorAttributeName:XY_HEX_RGB(0x537FB7)} range:rang1];
        [attr addAttributes:@{NSFontAttributeName:MY_FONT_Regular(16),NSForegroundColorAttributeName:XY_HEX_RGB(0x537FB7)} range:rang2];
        view.contentLabel.attributedText = attr;
        [view.contentScrollView addSubview:view.contentLabel];
        float mainViewHeight = 45 + 26 + labelsize.height + 20 + 44 + 15;
        if(mainViewHeight < 420){
            view.mainViewHeight.constant = mainViewHeight;
        }else{
            view.mainViewHeight.constant = 420;
        }
        [view.contentScrollView setContentSize:CGSizeMake(SCREEN_WIDTH - 110, labelsize.height)];
        if(rang1.location != NSNotFound && rang2.location != NSNotFound){
            [view.contentLabel yb_addAttributeTapActionWithRanges:@[NSStringFromRange(rang2),NSStringFromRange(rang1)] tapClicked:^(UILabel *label, NSString *string, NSRange rang, NSInteger index) {
                if([NSStringFromRange(rang2) isEqualToString:NSStringFromRange(rang)]){
                    if (view.clickViewBtnBlock) {
                        view.clickViewBtnBlock(@"3");
                    }
                }
                if([NSStringFromRange(rang1) isEqualToString:NSStringFromRange(rang)]){
                    if (view.clickViewBtnBlock) {
                        view.clickViewBtnBlock(@"2");
                    }
                }
            }];
        }
    }
    if (backView == nil) {
        backView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        backView.backgroundColor = COLOR_BLACK;
        backView.alpha = 0.5;
    }
    //实现弹出方法
    backView.tag = 900001;
    view.tag = 900002;
    [mainView addSubview:backView];
    [mainView addSubview:view];
}

- (IBAction)clickBtn:(UIButton *)sender {
    if (sender.tag == 0) {
        if (self.clickViewBtnBlock) {
            self.clickViewBtnBlock(@"0");
        }
    }else{
        if (self.clickViewBtnBlock) {
            self.clickViewBtnBlock(@"1");
        }
        [view removeFromSuperview];
        view = nil;
        [backView removeFromSuperview];
        backView = nil;
    }
}
@end
