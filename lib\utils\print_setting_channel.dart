import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/display_util.dart';
import 'package:nety/models/niimbot_printer.dart';
import 'package:nety/models/niimbot_rfid_info.dart';
import 'package:nety/nety.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/model/element/qr_code_element.dart';
import 'package:niimbot_flutter_canvas/src/pages/change_content_page_wrapper.dart';
import 'package:niimbot_flutter_canvas/src/utils/canvas_helper.dart';
import 'package:niimbot_flutter_canvas/src/utils/template_utils.dart';
import 'package:niimbot_flutter_canvas/src/widgets/excel/excel_row_page_wrapper.dart';
import 'package:niimbot_log_plugin/niimbot_log_tool.dart';
import 'package:niimbot_print_setting_plugin/interface/print_setting_channel_interface.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart';
import 'package:niimbot_template/models/template_data.dart' as TemplateDataNew;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:text/application.dart';
import 'package:text/business/app/offline_manager.dart';
import 'package:text/business/print/print_count_business.dart';
import 'package:text/business/user/user_login_helper.dart';
import 'package:text/connect/machine_alias_manager.dart';
import 'package:text/macro/constant.dart';
import 'package:text/pages/C1/model/template_data_transform.dart';
import 'package:text/pages/etag/goods_select/choose_goods_middle_page.dart';
import 'package:text/pages/industry_template/select_label/hardware_list_model.dart';
import 'package:text/pages/meProfile/nps_alert/nps_data_helper.dart';
import 'package:text/print/print_record_manager.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/template/constant/template_local_type.dart';
import 'package:text/template/util/template_misc_utils.dart';
import 'package:text/template/util/template_transform_utils.dart';
import 'package:text/tools/rfid_manager.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/hardware_manager.dart';
import 'package:text/utils/print_area.dart';
import 'package:text/utils/toast_util.dart';

import '../pages/canvas/model/NiimbotPrinter.dart' as AreaPrinter;
import '../print/print_content_manager.dart';
import '../print/print_history_manager.dart';
import '../template/model/template_operation_status.dart';
import '../template/template_manager.dart';

class PrintSettingChannel implements PrintSettingChannelInterface {
  HardwareModel? hardwareModel;

  @override
  void addEventBusListener(Function listener) {
    // 注册事件监听器，用于处理特定的事件。
    NiimbotEventBus.getDefault().register(this, (data) {
      listener.call(data);
    });
  }

  @override
  void destroyEventBus() {
    NiimbotEventBus.getDefault().unregister(this);
  }

  @override
  String getPaperTypeNameByCode(String type) {
    return intlanguage('${HardWareManager.instance().getPaperTypeNameByCode(type)}', '走纸类型');
  }

  @override
  String getAppLanguageType() {
    var languageType = Application.currentAppLanguageType;
    if (languageType == 'Polish') {
      languageType = "pl";
    }
    return languageType;
  }

  @override
  Future<Map?> getPrinterLabelData() async {
    hardwareModel = await HardWareManager.instance().getPrioritizationHardware();
    return await ToNativeMethodChannel().getPrinterLabelData();
  }

  @override
  Future<Map?> getConnectDevice() async {
    return await ToNativeMethodChannel().getPrinterConnectState();
  }

  @override
  void syncPrintSettingRfidReplace() {
    NiimbotEventBus.getDefault().post({"action": "syncPrintSettingRfidReplace"});
  }

  @override
  void openConnectPage({bool fromUnimp = false, String uniappId = ""}) {
    CustomNavigation.gotoNextPage('DeviceConnectPage', {'fromUnimp': fromUnimp, 'uniappId': uniappId}).then((value) {});
  }

  @override
  Future<List> getPrintSettingMsg(materialModelSn) async {
    List rawList = await ToNativeMethodChannel().getPrintSettingMsg(materialModelSn) ?? [];
    List<String> msgList = List<String>.from(rawList.map((e) => e.toString()), growable: true);
    String serialNumber = "";
    NiimbotRFIDInfo? labelRfid = getLableRfidInfo(RfidManager.sdkRFIDLabelInfos);
    if (msgList.isNotEmpty && msgList[0].contains("H-V")) {
      serialNumber = labelRfid?.serialNumber ?? "";
      msgList.insert(1, "BN: $serialNumber");
    }
    return msgList;
  }

  NiimbotRFIDInfo? getLableRfidInfo(List<NiimbotRFIDInfo> list) {
    try {
      return list.firstWhere((info) => info.type > 0 && info.type != 6);
    } catch (_) {
      return null;
    }
  }

  @override
  void setPrintDensity(int density) async {
    ToNativeMethodChannel().setSPData({"print_density": density.toString()});
    HardwareModel? connectModel = await HardWareManager.instance().connectHardWareModel();
    if (connectModel == null) {
      String latestHardwareNameOnCreate = Application.sp.getString(ConstantKey.latestHardwareName) ?? "B21";
      if (latestHardwareNameOnCreate.isNotEmpty) {
        connectModel =
            HardWareManager.instance().findHardwareModelContainChildrenPrintBy(machineName: latestHardwareNameOnCreate);
      }
    }
    SharedPreferences sp = await SharedPreferences.getInstance();
    sp.setInt('${connectModel!.name}_printDensity', density);
  }

  ///获取当前打印浓度信息
  @override
  Future<Map> getCurrentPrintDensityInfo(String consumableCode) async {
    //获取已链接机型信息
    HardwareModel? connectModel = await HardWareManager.instance().connectHardWareModel();
    if (connectModel == null) {
      //当前未链接 则获取全局选择机型
      String latestHardwareNameOnCreate = Application.sp.getString(ConstantKey.latestHardwareName) ?? "B21";
      if (latestHardwareNameOnCreate.isNotEmpty) {
        connectModel =
            HardWareManager.instance().findHardwareModelContainChildrenPrintBy(machineName: latestHardwareNameOnCreate);
      }
    }
    SharedPreferences sp = await SharedPreferences.getInstance();
    var density = 1;
    var solubilitySetStart = 1;
    var solubilitySetEnd = 3;
    if (connectModel != null) {
      //获取当前机型设置过的浓度缓存
      int? printDensity = sp.getInt('${connectModel.name}_printDensity');
      int? value = getConsumablePrintDensity(consumableCode, connectModel);
      solubilitySetStart = connectModel.solubilitySetStart;
      solubilitySetEnd = connectModel.solubilitySetEnd;
      if (printDensity == null) {
        //未设置过浓度 则使用耗材浓度或机器默认浓度
        if (value != null && value > 0) {
          density = value;
        } else {
          density = connectModel.solubilitySetDefault;
        }
      } else {
        //判断设置过的浓度缓存是否还在有效范围
        if (printDensity >= solubilitySetStart && printDensity <= solubilitySetEnd) {
          density = printDensity;
        } else {
          //不在有效范围 使用耗材浓度或机器浓度
          if (value != null && value > 0) {
            density = value;
          } else {
            density = connectModel.solubilitySetDefault;
          }
        }
      }
    } else {
      density = 1;
    }
    return {"density": density, "solubilitySetStart": solubilitySetStart, "solubilitySetEnd": solubilitySetEnd};
  }

  int? getConsumablePrintDensity(String consumableCode, HardwareModel connectModel) {
    int? consumableDensity;
    List<Consumable>? consumables = connectModel.consumables;
    if (consumables == null) {
      return consumableDensity;
    } else {
      for (Consumable element in consumables) {
        if (element.parentProperty == null) continue;
        if (element.parentProperty!.code?.toString() == consumableCode) {
          consumableDensity = element.parentProperty!.density;
        }
      }
    }
    return consumableDensity;
  }

  @override
  Future<void> toSave(TemplateDataNew.TemplateData? templateData) async {
    if(templateData == null){
      return ;
    }
    String oldId = templateData.id!;
    bool isTemplateExist = await TemplateManager().checkContainFile(templateData.id);
    SharedPreferences sp = await SharedPreferences.getInstance();
    String createTemplate = sp.getString("createTemplate") ?? "";
    if(createTemplate.isNotEmpty){
      Map<String, dynamic> createTemplateMap = jsonDecode(createTemplate);
      if(createTemplateMap['oldId1'] == templateData.id || createTemplateMap['oldId2'] == templateData.id){
        debugPrint("templateJson: 执行id替换 oldId1=${createTemplateMap['oldId1']} oldId2=${createTemplateMap['oldId2']} newId=${createTemplateMap['newId']}");
        isTemplateExist = true;
        templateData = templateData.copyWith(id: createTemplateMap['newId'],local_type: TemplateLocalType.UPDATE,profile: 
        templateData.profile.copyWith(extra: templateData.profile.extra.copyWith(userId: TemplateMiscUtils.getUserId())));
      }
    }
    if(isTemplateExist){
      debugPrint("templateJson: 模板存在 执行更新");
      await TemplateManager().updateTemplate(templateData,callback: (status,nt) async{
        switch (status) {
          case TemplateOperationStatus.localSuccess:
            NiimbotEventBus.getDefault().post({"myTemplateRefresh":""});
            NiimbotEventBus.getDefault().post({"updateTemplate":nt});
            ToNativeMethodChannel.refreshNativeTemplateList();
            TemplateData canvasTemplate = await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(nt!);
            NiimbotEventBus.getDefault().post({"action":"printSaveCanvasData", "templateData": canvasTemplate.generateCanvasJson(savePrintTemplate: true), 
            "oldId": templateData?.id!});
            oldId = nt.id!;
            break;
          case TemplateOperationStatus.serverSuccess:
            NiimbotEventBus.getDefault().post({"myTemplateRefresh":""});
            NiimbotEventBus.getDefault().post({"updateTemplate":nt});
            ToNativeMethodChannel.refreshNativeTemplateList();
            TemplateData canvasTemplate = await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(nt!);
            NiimbotEventBus.getDefault().post({"action":"printSaveCanvasData", "templateData": 
            canvasTemplate.generateCanvasJson(savePrintTemplate: true), 
            "oldId": oldId});
            break;
        }
      });
    }else{
      debugPrint("templateJson: 模板不存在");
      await TemplateManager().createTemplate(templateData,callback: (status,nt) async{
        switch (status) {
          case TemplateOperationStatus.localSuccess:
            NiimbotEventBus.getDefault().post({"myTemplateRefresh":""});
            NiimbotEventBus.getDefault().post({"updateTemplate":nt});
            ToNativeMethodChannel.refreshNativeTemplateList();
            TemplateData canvasTemplate = await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(nt!);
            NiimbotEventBus.getDefault().post({"action":"printSaveCanvasData", "templateData": 
            canvasTemplate.generateCanvasJson(savePrintTemplate: true), "oldId": oldId});
            oldId = nt.id!;
            break;
          case TemplateOperationStatus.serverSuccess:
            NiimbotEventBus.getDefault().post({"myTemplateRefresh":""});
            NiimbotEventBus.getDefault().post({"updateTemplate":nt});
            ToNativeMethodChannel.refreshNativeTemplateList();
            TemplateData canvasTemplate = await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(nt!);
            NiimbotEventBus.getDefault().post({"action":"printSaveCanvasData", "templateData": 
            canvasTemplate.generateCanvasJson(savePrintTemplate: true), "oldId": oldId});
            break;
        }
      });
    }
    // ToNativeMethodChannel().saveTemplate(data);
  }


  @override
  void setDeviceOffset(num offsetY, num offsetX) {
    ToNativeMethodChannel().setDeviceOffset({"offsetY": offsetY, "offsetX": offsetX});
  }

  @override
  Future<int> checkBatchPrintSources(List<Map<String, dynamic>> batchIds) async {
    return await ToNativeMethodChannel().checkBatchPrintSources(batchIds);
  }

  @override
  Future<bool> liveCodeIsDelete(Map<String, dynamic> templateJson) async {
    // 从数据库中查询数据
    TemplateData value = TemplateData.fromJson(templateJson);
    await AdvanceQRCodeManager().initAdvanceQRCodeCache(value);

    // 过滤出 QrCodeElement 类型的元素并检查每个元素
    for (var element in value.elements.whereType<QrCodeElement>()) {
      final qrCodeModel = AdvanceQRCodeManager().getAdvanceQRCodeInfo(element);

      // 检查 qrCodeModel 的属性
      if (qrCodeModel?.deleted ?? false) {
        // if (qrCodeModel?.isForm ?? false) {
        //   return false;
        // } else {
        //   return true;
        // }
        return true;
      }
    }
    // 如果没有找到符合条件的元素，则返回 true
    return false;
  }

//实时获取是否支持RFID数据源
  @override
  Future<bool> getIsShowRfid() {
    return HardWareManager.instance().isHardwareSupportRecordRfid();
  }

//获取数据源Rowdata与头
  @override
  Future<List> getTemplateRowDataHeaderWith(Map<String, dynamic> dataSource) {
    return TemplateUtils.getTemplateRowDataWith(dataSource);
  }

  Future<Map<String, dynamic>> getTemplateDetail(String id) async {
    try {
      var data = await ToNativeMethodChannel().getTemplateDetailFromNative(id, saveRecord: false) ?? '';
      Map<String, dynamic> value = json.decode(data);
      return value;
    } catch (e, s) {
      debugPrint('异常信息:\n $e');
      debugPrint('调用栈信息:\n $s');
      return {};
    }
  }

//将带有眼睛符号数据转为标准数据
  Future<Map<String, dynamic>> transformTemplateToStand(Map<String, dynamic> templateMap) async {
    Directory baseExcelFileDir = await getApplicationDocumentsDirectory();
    TemplateUtils.documentPath = baseExcelFileDir.path;
    return TemplateUtils.transformTemplateToStand(templateMap);
  }

//重设绘制倍率
  void resetGenerateDesignRatio(BuildContext context, double templateWidth, double templateHeight) {
    DisplayUtil.init(context);
    DisplayUtil.generateDesignRatio(templateWidth, templateHeight);
  }

  @override
  Future<String> getPrinterSeriesInfo(String machineId) async {
    if (Application.printer == null) {
      HardwareModel? printerModel;
      String latestHardwareNameOnCreate = Application.sp.getString(ConstantKey.latestHardwareName) ?? "B21";
      if (latestHardwareNameOnCreate.isNotEmpty) {
        printerModel =
            HardWareManager.instance().findHardwareModelContainChildrenPrintBy(machineName: latestHardwareNameOnCreate);
      } else {
        machineId = machineId.split(",")[0];
        printerModel = HardWareManager.instance().findHardwareDetailModel(machineId: machineId);
      }
      return printerModel == null ? "" : jsonEncode(printerModel.toJson());
    } else {
      return jsonEncode(Application.printer);
    }
  }

  @override
  void setUnNeedUIPrintComplete(String status, String uniAppId, String taskId, String message) {
    ToNativeMethodChannel().setUnNeedUIPrintComplete(status, uniAppId, taskId, message);
  }

  @override
  getCurrentPrinterInfo(String hardCode, String printerName) {
    HardwareModel? printerModel;
    List<HardwareModel> printerModels =
        HardWareManager.instance().findHardwareDetailModelWithHardCode(hardCode: hardCode);
    String printerType = printerName.split('-').first;
    if (printerModels.isEmpty) {
      if (!["D41", "D61", "Dxx"].contains(printerType)) {
        printerType = "D41";
      }
      printerModel = HardWareManager.instance().findHardwareModelContainChildrenPrintBy(machineName: printerType);
    } else {
      if (printerModels.length == 1) {
        //根据硬件编号搜索到一个设备则直接返回
        printerModel = printerModels.first;
      } else {
        //根据硬件编号搜索到多个设备 则匹配设备名称跟蓝牙编号分割机型是否一致，匹配不到则取第一个设备
        var printers = printerModels.where((printerModel) => printerModel.name == printerType);
        printerModel = printers.isNotEmpty ? printers.first : printerModels.first;
      }
    }
    return printerModel?.toJson() ?? {};
  }

  @override
  String getCurrentPrinterMachineId(String hardCode, String printerName, String printerSN) {
    Map printerInfo = getCurrentPrinterInfo(hardCode, printerName);
    if (printerInfo.isEmpty || printerSN.isEmpty) {
      return printerName;
    } else {
      String connector = "";
      if (!((printerInfo['name'] as String).contains("-"))) {
        connector = "-";
      }
      return '${printerInfo['name']}$connector$printerSN';
    }
  }

  bool isConnected() {
    return Application.networkConnected;
  }

  void postEvent(Map<String, dynamic> eventData) {
    if (eventData.containsKey("action") && eventData["action"] == "syncRfidReplaceTag") {
      bool autoReplaceFlag = eventData["rfidReplaceTag"];
      CanvasHelper.hasConfirmRfidReplace = autoReplaceFlag;
    }
    NiimbotEventBus.getDefault().post(eventData);
  }

  bool getLabelReplaceFlag() {
    return CanvasHelper.hasConfirmRfidReplace;
  }

  ///jumpType=1 excel模版选行；jumpType=2 商品模版选商品；jumpType=3 纯文本模版更改内容
  void showChangePrintData(BuildContext context, String templateJsonStr, int jumpType) {
    late Widget contentWidget;
    if (jumpType == 1) {
      contentWidget = ExcelRowPageWrapper(
        nativeTemplateJson: templateJsonStr,
      );
    } else if (jumpType == 2) {
      if (!Application.networkConnected) {
        showToast(msg: intlanguage('app01139', '网络异常'));
        return;
      }
      contentWidget = ChooseGoodsMiddlePage(templateJsonStr);
    } else {
      contentWidget = ChangeContentPageWrapper(
        nativeTemplateJson: templateJsonStr,
      );
    }
    if (jumpType == 2) {
      UserLoginHelper().customLogin(context, (isLogin) {
        _showModalBottomSheet(context, contentWidget);
      });
    } else {
      _showModalBottomSheet(context, contentWidget);
    }
    // HardWareManager.instance().getPrinterInfo();
  }

  void _showModalBottomSheet(BuildContext context, Widget contentWidget) {
    showModalBottomSheet(
      context: context,
      isDismissible: false,
      isScrollControlled: true,
      enableDrag: false,
      barrierColor: Color(0xFF000000).withOpacity(0.35),
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      builder: (BuildContext context) {
        return contentWidget;
      },
    );
  }

  @override
  void trackEvent(String eventType, String eventCode, {Map<String, dynamic>? eventData}) {
    ToNativeMethodChannel().sendTrackingToNative({"track": eventType, "posCode": eventCode, "ext": eventData});
  }

  @override
  void refreshConnectDevice(NiimbotPrinter device) {
    RfidManager.instance.refreshConnectDevice(device);
  }

  @override
  List<NiimbotRFIDInfo> getDeviceRFIDCacheInfos() {
    return RfidManager.sdkRFIDLabelInfos;
  }

  @override
  void savePrintRecord(
      String uniqueValue,
      TemplateDataNew.TemplateData template,
      int printNum,
      int lastPrintCount,
      PrintTaskType taskType,
      double printCardPaperLength,
      double lastRibbonLength,
      String uniappId,
      bool isConnected,
      String printStrategy,
      String illegalCode,
      Function function) {
    PrintRecordManager().handlePrint(uniqueValue, template, printNum, lastPrintCount, taskType, printCardPaperLength,
        lastRibbonLength, uniappId, isConnected, printStrategy, illegalCode, function);
  }

  @override
  Map<String, String> getPaperRibbonColor() {
    // debugPrint("PrintStrategyLog2--colorInfo-getPaperRibbonColor=>coverStatus: ${NiimbotPrintSDK().store.coverStatus} ,serviceRfidInfo: ${RfidManager.serviceRfidInfo.rfidPaperColor}");
    if (NiimbotPrintSDK().store.coverStatus != 0) {
      return {
        'paperColor': RfidManager.serviceRfidInfo.rfidPaperColor,
        'ribbonColor': RfidManager.serviceRfidInfo.rfidRibbonColor
      };
    }
    return {};
  }

  @override
  bool currentPaperIsSupportDevice() {
    return true;
  }

  @override
  void riskCheck(BuildContext context, int action, {List<NiimbotRFIDInfo>? rfidInfos}) {
    RfidManager.instance.buildRiskCheck(action, rfidInfos: rfidInfos);
  }

  @override
  Future<Map<String, dynamic>> getBatchPrintTemplate(String templateId) async {
    String templateStr = await ToNativeMethodChannel()
            .getTemplateDetailFromNative(templateId, isBatchPrintTemplate: true, saveRecord: false) ??
        "";
    if (templateStr.isEmpty) {
      return {};
    } else {
      return jsonDecode(templateStr);
    }
  }

  @override
  Future<TemplateDataNew.TemplateData?> getBatchPrintC1Template(int templateIndex) async {
    TemplateDataNew.TemplateData? templateData = await PrintTemplateDataTransform.getC1PrintTemplate(templateIndex);
    return templateData;
  }

  @override
  void downloadTemplateDetails(List<Map<String, dynamic>> batchIds) {
    List<String> ids = batchIds.map((Map<String, dynamic> item) => item['id'] as String).toList();
    ToNativeMethodChannel().downloadTemplateDetails(ids);
  }

  @override
  void toMarketRating({String? uniAppId = ""}) async {
    int userPrintCount = await PrintCountBusiness().getLocalUserPrintCount(uniAppId: uniAppId);
    ToNativeMethodChannel().toMarketRating(userPrintCount);
  }

  @override
  Future<int> unUIPrintErrorEventCallback(Map<String, dynamic> errorInfo) {
    return ToNativeMethodChannel().unUIPrintErrorEventCallback(errorInfo);
  }

  @override
  void refreshRFIDInfoInService(Map<String, dynamic> serviceRFIDInfo) {
    RfidManager.refreshRfidColorWith(serviceRFIDInfo["paperColor"] ?? "", serviceRFIDInfo["ribbonColor"] ?? "");
    ToNativeMethodChannel().refreshRFIDInfoInService(serviceRFIDInfo);
  }

  @override
  void saveHistory(
      String deviceType, String copies, String uniqueId, bool isBatchPrint, TemplateDataNew.TemplateData templateData,
      {Map? templateMap, int page = 1, bool isSupportGray16 = false, bool isPrintHistory = false}) {
    PrintHistoryManager().saveHistory(deviceType, copies, uniqueId, isBatchPrint, templateData,
        templateMap: templateMap, page: page, isSupportGray16: isSupportGray16, isPrintHistory: isPrintHistory);
  }

  @override
  void saveContent(TemplateDataNew.TemplateData templateData, String uniqueValue) {
    PrintContentManager().saveContent(templateData, uniqueValue);
  }

  @override
  void uploadPrintDataLog(String uniqueValue, Function function) {
    RfidManager.instance.uploadPrintDataLog(uniqueValue, function);
  }

  @override
  void labelRecord(Map<String, dynamic> templateData, String uniAppId) {
    final List<String> validUniappIds = [
      "__UNI__DE09E3D",
      "__CAP__DE09E3D",
      "__UNI__B68033D",
      "__CAP__B68033D",
      "__UNI__7863BFB",
      "__CAP__7863BFB"
    ];
    if (validUniappIds.contains(uniAppId)) {
      ToNativeMethodChannel().saveLabelRecord(templateData);
    }
  }

  @override
  void savePrintDeviceLog() async {
    var printerLog = await NiimbotPrintSDK().getPrinterLog();
    await NiimbotLogTool.uploadLogInstantTime(
      {
        'logType': 'printer_firmware_log',
        'state': printerLog?.code.value,
        'logs': printerLog?.log,
        'printer_SN': RfidManager.connectedDevice?.deviceName
      },
    );
  }

  @override
  bool isLogin() {
    return Application.isLogin;
  }

  @override
  getPrintArea(double templateWidth, double templateHeight, String consumableType, String paperType, int rotate,
      int canvasWidth, int cableDirection, double cableLength, int canvasRotate) {
    debugPrint(
        "getPrintArea: $templateWidth,$templateHeight,$consumableType,$paperType,$rotate,$canvasWidth,$cableDirection,$cableLength,$canvasRotate");
    AreaPrinter.NiimbotPrinter? selectPrinter =
        hardwareModel == null ? null : AreaPrinter.NiimbotPrinter.fromJson(hardwareModel?.toJson());

    if (selectPrinter != null) {
      return PrintAreaHelper.getPrintArea(selectPrinter, templateWidth, templateHeight, consumableType, paperType,
          rotate, canvasWidth, cableDirection, cableLength, canvasRotate);
    } else {
      return [];
    }
  }

  @override
  void checkDangerRecord() {
    ToNativeMethodChannel().checkDangerRecord();
  }

  @override
  void sendCapGenerateTemplatePreviewEvent(String imageBase64) {
    ToNativeMethodChannel().sendCapGenerateTemplatePreviewEvent(imageBase64);
  }

  @override
  Future<bool> isTriggerNpsPop() async {
    //有网 打印张数>10 距上次弹出大于一定间隔
    int userPrintCount = await PrintCountBusiness().getLocalUserPrintCount();
    return Application.networkConnected && userPrintCount > 10 && NpsDataHelper.isShowNps();
  }

  @override
  Future<bool> isTriggerMiniProgramNpsPop({String? uniAppId = ""}) async {
    //有网 仅中文 打印次数>=3 距上次弹出大于一定间隔
    int userPrintCount = await PrintCountBusiness().getLocalUserPrintCount(uniAppId: uniAppId);
    return Application.networkConnected &&
        Application.isChineseLanguage &&
        userPrintCount >= 3 &&
        NpsDataHelper.isShowNps(uniAppId: uniAppId);
  }

  @override
  void showNps() {
    ToNativeMethodChannel.showNpsAlert("print");
  }

  @override
  void showMiniProgramNps({String? uniAppId = ""}) {
    ToNativeMethodChannel.showNpsAlertWithParams({"source": "print", "isUniApp": true, "uniAppId": uniAppId});
  }

  @override
  void updateUserPrintCount(int printNum) {
    if (!Application.isLogin) {
      return;
    }
    PrintCountBusiness().updateLocalUserPrintCount(printNum);
  }

  @override
  void updateUserMiniProgramPrintCount(int printNum, {String? uniAppId = ""}) {
    if (!Application.isLogin) {
      return;
    }
    PrintCountBusiness().updateLocalUserPrintCount(printNum, uniAppId: uniAppId);
  }

  @override
  Future<TemplateDataNew.TemplateData> getRfidReplaceTemplate(
      TemplateDataNew.TemplateData target, TemplateDataNew.TemplateData source) async {
    final TemplateData targetT = TemplateData.fromJson(target.toJson());
    final TemplateData sourceT = TemplateData.fromJson(source.toJson());
    TemplateData result = await OcrUtils.generateRfidReplaceTemplate(sourceT, targetT);
    return TemplateDataNew.TemplateData.fromJson(result.toJson());
  }

  @override
  void setFlutterVCCanSideslip(bool value) {
    ToNativeMethodChannel().setFlutterVCCanSideslip(value);
  }

  @override
  void showStageDialog(BuildContext context, bool isCanSavaLocal, Function()? loginSucceed) {
    String title = isCanSavaLocal
        ? intlanguage("app100001914", "已暂存本地，登录后可永久保存")
        : intlanguage('app100000125', '您需要登录账号，用来保存您当前的数据。');
    String leftBtn = intlanguage('app00030', '取消');
    String rightBtn = intlanguage('app01191', '立即登录');
    showNimmbotDialog(context, title: title, cancelDes: leftBtn, confirmDes: rightBtn, confirmAction: () {
      UserLoginHelper().gotoLogin(context, loginFailed: () {}, loginSucceed: loginSucceed);
    }, cancelAction: () {});
  }

  @override
  String getConnectedMachineAlias() {
    String name = RfidManager.connectedDevice?.name ?? "";
    if (name.isEmpty) {
      return "";
    }
    return MachineAliasManager().aliasMap[name] ?? "";
  }

  @override
  Future<bool> checkOfflinePeriod(BuildContext context) async {
    Map params = await NiimbotNetworkManager().checkOfflinePeriod();
    int alertType = params['alertType'];
    String alertTitle = params['alertTitle'];
    String alertMessage = params['alertMessage'];
    if (alertType > 0) {
      if (alertType == 1) {
        //离线弱提醒-可以打印,在这里弹窗
        await NiimbotNetworkManager().showOfflineTipDialog(context);
        return true;
      } else {
        //离线强提醒-禁止打印
        await NiimbotNetworkManager().showOfflineTipDialog(context);
        return false;
      }
    } else {
      //有离线检查弹窗正在显示的话，则关闭
      NiimbotNetworkManager().dismissOfflineTipDialog(context);
    }
    return true;
  }

  @override
  void tubeExceptionCallback() {
    NiimbotEventBus.getDefault().post("printTubeException");
  }
}
