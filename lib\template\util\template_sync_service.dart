import 'dart:collection';

import 'package:niimbot_template/models/template_data.dart';
import 'package:text/application.dart';
import 'package:text/template/template_manager.dart';
import 'package:text/template/util/template_detail_db_utils.dart';
import 'package:text/template/util/template_misc_utils.dart';

import '../../log_utils.dart';

class TemplateSyncService {
  // 单例模式
  static final TemplateSyncService _instance = TemplateSyncService._internal();
  factory TemplateSyncService() => _instance;
  TemplateSyncService._internal();

  TemplateManager templateManager = TemplateManager();
  // 任务队列
  final Queue<SyncTask> _taskQueue = Queue();
  // 标记是否正在处理队列
  bool _isProcessing = false;
  // 正在处理的模板ID列表（静态变量，全局保护）
  static final Set<String> _processingIds = <String>{};
  // 中断标志，用于停止正在进行的同步
  bool _shouldStop = false;

  /// 离线数据同步主入口
  Future<bool> localFileToService() async {
    if(!Application.isLogin){
      return false;
    }

    // 如果已经在处理中，避免重复触发
    if (_isProcessing) {
      Log.d("TemplateSyncService 正在处理中，跳过本次同步");
      return false;
    }

    String userId = TemplateMiscUtils.getUserId();
    await TemplateDetailDBUtils.updateTemplateDefaultUserId(userId);
    
    // 查询需要同步的数据
    final templates = await TemplateDetailDBUtils.queryAllLocalTemplateList();
    Log.d("TemplateSyncService 查询到${templates.length}条离线模版");
    
    if (templates.isEmpty) {
      return false;
    }

    // 重置中断标志
    _shouldStop = false;

    // 生成同步任务并加入队列
    for (final template in templates) {
      final templateId = template.id!;
      final localType = template.local_type.toInt();

      // 跳过正在处理的任务（全局保护）
      if (_processingIds.contains(templateId)) {
        continue;
      }

      // 创建对应类型的任务
      final task = SyncTask(
        template: template,
        type: localType,
        maxRetries: 2,
      );

      // 加入队列并标记为处理中
      _taskQueue.add(task);
      _processingIds.add(templateId);
    }

    // 启动队列处理
    if (!_isProcessing && _taskQueue.isNotEmpty) {
     await _processQueue();
    }
    return true;
  }

  /// 队列处理器
  Future<void> _processQueue() async {
    Log.d("TemplateSyncService 离线模版同步开始");
    _isProcessing = true;

    while (_taskQueue.isNotEmpty && !_shouldStop) {
      final task = _taskQueue.removeFirst();
      final templateId = task.template.id!;
      
      // 检查中断标志
      if (_shouldStop) {
        Log.d("TemplateSyncService 检测到中断信号，停止处理模版${templateId}");
        _processingIds.remove(templateId);
        continue;
      }
      
      // 检查当前登录状态
      if (!Application.isLogin) {
        Log.d("TemplateSyncService 用户已登出，停止处理模版${templateId}");
        _processingIds.remove(templateId);
        continue;
      }
      
      try {
        for (var attempt = 0; attempt <= task.maxRetries; attempt++) {
          // 在每次重试前检查中断标志
          if (_shouldStop) {
            Log.d("TemplateSyncService 检测到中断信号，停止重试模版${templateId}");
            break;
          }
          
          try {
            switch (task.type) {
              case TemplateDetailDBUtils.CREATE:
                await templateManager.syncOfflineCreateTemplate(task.template);
                Log.d("TemplateSyncService 离线创建的模版${templateId}同步成功");
                break;
              case TemplateDetailDBUtils.DELETE:
                await templateManager.delTemplate(task.template);
                Log.d("TemplateSyncService 离线删除的模版${templateId}同步成功");
                break;
              case TemplateDetailDBUtils.UPDATE:
                await templateManager.syncOfflineUpdateTemplate(task.template);
                Log.d("TemplateSyncService 离线更新的模版${templateId}同步成功");
                break;
            }
            break;
          } catch (e) {
            if (attempt == task.maxRetries) rethrow;
            Log.d("TemplateSyncService 离线同步模版${templateId}失败，重试${attempt + 1}/${task.maxRetries}");
            await Future.delayed(Duration(seconds: attempt + 1)); // 指数退避
          }
        }
      } catch (e) {
        Log.e("TemplateSyncService 模版${templateId}同步失败: ${e.toString()}");
      } finally {
        _processingIds.remove(templateId);
      }
    }

    _isProcessing = false;
    Log.d("TemplateSyncService 离线模版同步结束");
  }

  /// 清理同步状态（供外部调用，如用户登出时）
  static void clearSyncState() {
    Log.d("TemplateSyncService 开始清理同步状态");
    _processingIds.clear();
    final instance = TemplateSyncService._instance;
    instance._taskQueue.clear();
    instance._isProcessing = false;
    // 设置中断标志，停止正在进行的同步
    instance._shouldStop = true;
    Log.d("TemplateSyncService 清理同步状态完成");
  }
}

/// 同步任务数据类
class SyncTask {
  final TemplateData template;
  final int type;
  final int maxRetries;

  SyncTask({
    required this.template,
    required this.type,
    this.maxRetries = 2,
  });
}
