//
//  JCCollectionViewCell.h
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2019/1/24.
//  Copyright © 2019 JingchenSoft. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface JCCollectionViewCell : UICollectionViewCell

@property (strong, nonatomic) UIImageView *topImage;
@property (strong, nonatomic) UILabel *botlabel;
@property (assign, nonatomic) BOOL isShowImport;
@property (copy, nonatomic) XYNormalBlock printBlock;
- (void)setTemplateData:(JCTemplateData *)tmData;
- (void)setPrintButtonShow:(BOOL)isShowPrint;
@end
