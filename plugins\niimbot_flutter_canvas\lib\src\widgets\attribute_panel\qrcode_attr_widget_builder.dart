import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/plugin/advance_qr_code_model.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_canvas_plugins_interface/user_center/canvas_user_center.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '/src/localization/localization_public.dart';
import '/src/localization/localization_public_ext.dart';
import '/src/model/advanceQRCode/advance_qr_code_manager.dart';
import '/src/model/canvas_element.dart';
import '/src/model/element/json_element.dart';
import '/src/model/element/qr_code_element.dart';
import '/src/provider/elements_data_changed_notifier.dart';
import '/src/utils/theme_color.dart';
import '/src/widgets/attribute_panel/comm/common_import_widget.dart';
import '/src/widgets/attribute_panel/comm/items_selector_popmenu_widget.dart';
import '/src/widgets/box_frame/canvas_box_frame.dart';
import '/src/widgets/canvas/canvas_theme_widget.dart';
import '/src/widgets/canvas/floatbar/floating_bar_helper.dart';
import '/src/widgets/components/svg_icon.dart';
import 'attr_widget_builder.dart';
import 'comm/multi_color_high_light.dart';
import 'element_attribute_panel.dart';

class QrcodeTypeModel {
  final int code;
  final String title;

  QrcodeTypeModel(this.code, this.title);
}

final List<QrcodeTypeModel> qrcodeModelTypes = [
  QrcodeTypeModel(QrcodeType.QR_CODE, 'QR_CODE'),
  QrcodeTypeModel(QrcodeType.PDF417, 'PDF417'),
  QrcodeTypeModel(QrcodeType.DATA_MATRIX, 'DATA_MATRIX'),
  QrcodeTypeModel(QrcodeType.AZTEC, 'AZTEC'),
];

class QrcodeAttrWidgetBuilder extends AttrWidgetBuilder {
  AdvanceQRCodeModel? advanceModel;
  var updateTime;
  bool isRefresh = true;
  CanvasElement? element;

  QrcodeAttrWidgetBuilder(
      AttrPanelState attrPanelState,
      double height,
      ElementValueEditorDisplay onElementValueEditorDisplay,
      ImportExcelFromElementCall importExcelFromElementCall,
      ChangeDataManualInputField changeDataManualInputField,
      ChangeColumnIndexCall changeColumnIndexCall)
      : super(attrPanelState, height,
            onElementValueEditorDisplay: onElementValueEditorDisplay,
            importExcelFromElementCall: importExcelFromElementCall,
            changeDataManualInputField: changeDataManualInputField,
            changeColumnIndexCall: changeColumnIndexCall);

  @override
  List<MenuTag> getMenus(List<CanvasElement> canvasElements) {
    int index = 0;
    if (menus.isEmpty) {
      menus = [
        MenuTag(
            index: index++,
            text: intlanguage("app00004", "二维码"),
            isSelected: true,
            trackName: '二维码'),
        MenuTag(
            index: index++,
            text: intlanguage("app100000760", "对齐/镜像"),
            isSelected: false,
            trackName: '对齐/镜像'),
      ];
    }
    return menus;
  }

  @override
  Widget buildAttrWidget(MenuTag menuTag, List<CanvasElement> canvasElements,
      BuildContext context, VoidCallback refresh) {
    if (menuTag.index == 0) {
      return qrcodeStyle(canvasElements, context, refresh);
    } else if (menuTag.index == 1) {
      return layoutPanel(canvasElements, context, refresh);
    }
    return Container(
      color: Colors.transparent,
      width: double.infinity,
      height: 100,
    );
  }

  /// 二维码样式
  Widget qrcodeStyle(List<CanvasElement> canvasElements, BuildContext context,
      VoidCallback refresh) {
    QrCodeElement firstElement = canvasElements.first.data as QrCodeElement;
    String printColor = CanvasObjectSharedWidget.printColorOf(context);
    return Container(
      padding: EdgeInsetsDirectional.fromSTEB(16, 6, 16, 16),
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          firstElement.isForm || firstElement.isLive
              ? liveOrFormWidget(firstElement, context, refresh)
              : Column(
                  children: [
                    CommonImportWidget(
                      canvasElement: canvasElements.first,
                      columnIndex: firstElement.bindingColumn,
                      onElementValueEditorDisplay: onElementValueEditorDisplay,
                      importExcelFromElementCall: importExcelFromElementCall,
                      inputTextChanged: (value) {
                        canvasElements.forEach((canvasElement) {
                          QrCodeElement element =
                              canvasElement.data as QrCodeElement;
                          element.value = value;
                        });

                        Provider.of<ElementsDataChangedNotifier>(context,
                                listen: false)
                            .setDataChangedElements(canvasElements);
                      },
                      scanResultCall: (newValue, codeType) {
                        canvasElements.forEach((canvasElement) {
                          QrCodeElement element =
                              canvasElement.data as QrCodeElement;
                          element.value = newValue;
                          if (codeType != null) {
                            element.codeType = codeType;
                          }
                        });

                        Provider.of<ElementsDataChangedNotifier>(context,
                                listen: false)
                            .setDataChangedElements(canvasElements);

                        refresh.call();
                      },
                      changeColumnIndexCall: changeColumnIndexCall,
                      changeDataManualInputField: changeDataManualInputField,
                    ),

                    SizedBox(
                      height: 10,
                    ),

                    /// 编码类型
                    Container(
                        height: 44,
                        decoration: BoxDecoration(
                            color: CanvasTheme.of(context)
                                .attributeGroupBackgroundColor,
                            borderRadius:
                                BorderRadius.all(Radius.circular(12))),
                        child: Padding(
                          padding:
                              EdgeInsetsDirectional.symmetric(horizontal: 12),
                          child: Row(
                            children: [
                              Text(
                                intlanguage('app100000762', '编码格式'),
                                style: CanvasTheme.of(context)
                                    .attributeTitleTextStyle,
                              ),
                              Expanded(
                                  child: ItemsSelectorPopUpWidget(
                                key: Key(
                                    'ItemsSelectorPopUpWidget_${firstElement.codeType}'),
                                items: qrcodeModelTypes
                                    .map((e) => e.title)
                                    .toList(),
                                initializeIndex: qrcodeModelTypes.indexOf(
                                    qrcodeModelTypes.singleWhere(
                                        (element) =>
                                            firstElement.codeType ==
                                            element.code,
                                        orElse: () => qrcodeModelTypes.first)),
                                itemsSelectedChanged: (int index) {
                                  canvasElements.forEach((canvasElement) {
                                    QrCodeElement element =
                                        canvasElement.data as QrCodeElement;
                                    element.codeType =
                                        qrcodeModelTypes[index].code;
                                  });
                                  refresh.call();
                                  Provider.of<ElementsDataChangedNotifier>(
                                          context,
                                          listen: false)
                                      .setDataChangedElements(canvasElements);
                                },
                                popHeight: 200,
                              )),
                            ],
                          ),
                        )),
                  ],
                ),
          SizedBox(
            height: 10,
          ),
          MultiColourHighLightWidget(
            multiColorIndex: firstElement.paperColorIndex ?? 0,
            valueChanged: (int redBlackValue) {
              List<String>? paperColors =
                  CanvasObjectSharedWidget.canvasDataOf(context)?.paperColor;
              String? colorValue = paperColors?[redBlackValue];
              colorValue = "255." + (colorValue ?? "");
              List<int> elementColor =
                  colorValue.split(".").map((e) => int.parse(e)).toList();
              canvasElements.forEach((canvasElement) {
                QrCodeElement element = canvasElement.data as QrCodeElement;
                element.paperColorIndex = redBlackValue;
                element.colorChannel = redBlackValue;
                element.elementColor = elementColor;
              });
              Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                  .setDataChangedElements(canvasElements);
            },
            colorTypeStr: intlanguage('app100000419', '颜色'),
            colorList: printColor.isNotEmpty ?? false
                ? [printColor]
                : CanvasObjectSharedWidget.canvasDataOf(context)?.paperColor,
          ),
        ],
      ),
    );
  }

  liveOrFormWidget(
      QrCodeElement firstElement, BuildContext context, VoidCallback refresh) {
    advanceModel = AdvanceQRCodeManager().getAdvanceQRCodeInfo(firstElement);
    if(advanceModel != null){
      DateTime utcTime = DateTime.parse(advanceModel!.updateTime!);
      DateTime localTime = utcTime.toLocal();
      updateTime = DateFormat('yyyy/M/d H:mm').format(localTime);
    }

    element = QrCodeElement(
            value: advanceModel == null ? "" : advanceModel!.shortUrl,
            id: JsonElement.generateId(),
            width: 25,
            height: 25,
            x: 5,
            y: 5,
            rotate: 0,
            correctLevel: 0,
            codeType: QrcodeType.QR_CODE)
        .toCanvasElement();
    return firstElement.isForm
        ? formWidget(context, firstElement, refresh)
        : liveCodeWidget(context, firstElement, refresh);
  }

  liveCodeWidget(
      BuildContext context, QrCodeElement firstElement, VoidCallback refresh) {
    return Column(
      children: [
        Row(
          children: [
            Container(
              // alignment: Alignment.centerLeft,
              padding: EdgeInsets.fromLTRB(6, 10, 0, 10),
              child: Text(
                intlanguage('app100000234', '高级二维码'),
                textAlign: TextAlign.center,
                style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: ThemeColor.COLOR_262626),
              ),
            ),
          ],
        ),
        GestureDetector(
          onTap: () {
            if (!(advanceModel?.deleted ?? false)) {
              CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative(
                  {"track": "click", "posCode": "108_247_232", "ext": {}});
              _previewQRcode(context, firstElement, refresh);
            }
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 6),
            decoration: BoxDecoration(
              color: ThemeColor.COLOR_F8F8F8,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(6),
                  margin: EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: ThemeColor.background,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  constraints: BoxConstraints(
                    maxWidth: 60,
                  ),
                  child: Center(
                      child: element == null
                          ? Container(
                              width: 47,
                              height: 47,
                            )
                          : SizedBox(
                              width: 47,
                              height: 47,
                              child: canvasKey.currentState?.itemBuilder(
                                  element!,
                                  isCleanImageWidget: true))),
                ),
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 12),
                  constraints: BoxConstraints(
                    maxWidth: 120,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        advanceModel == null ? " " : advanceModel!.title!,
                        textAlign: TextAlign.start,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                        style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: ThemeColor.COLOR_262626),
                      ),
                      advanceModel != null && (advanceModel!.template == "" || advanceModel!.template == null)
                          ? Text(
                              advanceModel == null
                                  ? ""
                                  : advanceModel!.contentSummary ?? '',
                              textAlign: TextAlign.start,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  color: ThemeColor.subtitle),
                            )
                          : Container(),
                      Padding(
                        padding: EdgeInsets.only(top: 4),
                        child: Text(
                          advanceModel == null ? "" : updateTime,
                          // overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.left,
                          style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: ThemeColor.subtitle),
                        ),
                      )
                    ],
                  ),
                ),
                Expanded(child: Container()),
                advanceModel != null && !advanceModel!.deleted!
                    ? GestureDetector(
                        onTap: () {
                          CanvasPluginManager()
                              .nativeMethodImpl
                              ?.sendTrackingToNative({
                            "track": "click",
                            "posCode": "108_247_232",
                            "ext": {}
                          });
                          _previewQRcode(context, firstElement, refresh);
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: ThemeColor.background,
                            borderRadius: BorderRadius.circular(15),
                          ),
                          constraints: BoxConstraints(
                            maxWidth: 90,
                          ),
                          padding:
                              EdgeInsets.symmetric(vertical: 6, horizontal: 20),
                          margin: EdgeInsets.symmetric(horizontal: 9),
                          child: Text(
                            intlanguage('app100001144', '查看'),
                            maxLines: 4,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: ThemeColor.brand),
                          ),
                        ),
                      )
                    : Container()
              ],
            ),
          ),
        ),
        advanceModel == null || !advanceModel!.deleted!
            ? advanceModel == null ||
                    advanceModel!.userId == CanvasUserCenter().userId.toString()
                ? Container()
                : Container(
                    width: MediaQuery.sizeOf(context).width,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.fromLTRB(8, 8, 0, 0),
                    child: Row(
                      children: [
                        Container(
                          width: MediaQuery.sizeOf(context).width - 50,
                          child: Text(
                            intlanguage('app100001146', '该高级二维码由他人创建，你仅可查看'),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            // textAlign: TextAlign.left,
                            style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                color: ThemeColor.subtitle),
                          ),
                        ),
                      ],
                    ),
                  )
            : Container(
                padding: EdgeInsets.fromLTRB(8, 8, 0, 0),
                child: Row(
                  children: [
                    Image.asset(
                      "assets/common/icon_warning.png",
                      package: "niimbot_flutter_canvas",
                      fit: BoxFit.cover,
                      width: 20,
                      height: 20,
                    ),
                    Text(
                      intlanguage('app100001145', '该高级二维码已被删除'),
                      textAlign: TextAlign.left,
                      style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: ThemeColor.brand),
                    )
                  ],
                ),
              )
      ],
    );
  }

  formWidget(
      BuildContext context, QrCodeElement firstElement, VoidCallback refresh) {
    return Column(
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.fromLTRB(6, 10, 0, 10),
              child: Text(
                intlanguage('app100000582', '表单'),
                textAlign: TextAlign.center,
                style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: ThemeColor.COLOR_262626),
              ),
            ),
          ],
        ),
        GestureDetector(
          onTap: () {
            if (advanceModel != null && !advanceModel!.deleted!) {
              _previewQRcode(context, firstElement, refresh);
            }
          },
          child: Container(
            padding: EdgeInsets.fromLTRB(6, 6, 5, 6),
            decoration: BoxDecoration(
              color: ThemeColor.COLOR_F8F8F8,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Container(
                  // constraints: BoxConstraints(
                  //   maxWidth: MediaQuery.sizeOf(context).width - 50,
                  // ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          SvgIcon(
                            'assets/element/icon/element_icon_qrcode.svg',
                            fit: BoxFit.contain,
                            height: 22,
                            width: 22,
                          ),
                          Container(
                            constraints: BoxConstraints(
                              maxWidth: 140,
                            ),
                            child: Text(
                              advanceModel == null ? " " : advanceModel!.title!,
                              textAlign: TextAlign.start,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: ThemeColor.COLOR_262626),
                            ),
                          ),
                        ],
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 4, left: 3),
                        child: Text(
                          advanceModel == null ? "" : updateTime,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.left,
                          style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: ThemeColor.subtitle),
                        ),
                      ),
                      // Padding(
                      //   padding: EdgeInsets.only(left: 3),
                      //   child: Text(
                      //     advanceModel == null
                      //         ? ""
                      //         : intlanguage('app100000595', '已收集\$份',
                      //             param: [advanceModel!.count.toString()]),
                      //     textAlign: TextAlign.center,
                      //     overflow: TextOverflow.ellipsis,
                      //     style: const TextStyle(
                      //         fontSize: 12,
                      //         fontWeight: FontWeight.w400,
                      //         color: ThemeColor.subtitle),
                      //   ),
                      // ),
                    ],
                  ),
                ),
                Expanded(child: Container()),
                advanceModel != null && !advanceModel!.deleted!
                    ? GestureDetector(
                        onTap: () {
                          //  CanvasPluginManager().nativeMethodImpl.sendTrackingToNative(
                          //            {"track": "click", "posCode": "108_247_232", "ext": {}});
                          _previewQRcode(context, firstElement, refresh);
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: ThemeColor.background,
                            borderRadius: BorderRadius.circular(15),
                          ),
                          constraints: BoxConstraints(
                            maxWidth: 90,
                          ),
                          padding:
                              EdgeInsets.symmetric(vertical: 6, horizontal: 20),
                          margin: EdgeInsets.symmetric(horizontal: 9),
                          child: Text(
                            intlanguage('app100001144', '查看'),
                            maxLines: 4,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: ThemeColor.brand),
                          ),
                        ),
                      )
                    : Container()
              ],
            ),
          ),
        ),
        advanceModel == null || !advanceModel!.deleted!
            ? advanceModel == null ||
                    advanceModel!.userId == CanvasUserCenter().userId.toString()
                ? Container()
                : Row(
                    children: [
                      Container(
                        width: MediaQuery.sizeOf(context).width - 50,
                        padding: EdgeInsets.fromLTRB(8, 8, 0, 0),
                        child: Text(
                          intlanguage('app100001147', '该表单由他人创建，你仅可查看'),
                          textAlign: TextAlign.start,
                          style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: ThemeColor.subtitle),
                        ),
                      ),
                    ],
                  )
            : Container(
                padding: EdgeInsets.fromLTRB(8, 8, 0, 0),
                child: Row(
                  children: [
                    Image.asset(
                      "assets/common/icon_warning.png",
                      package: "niimbot_flutter_canvas",
                      fit: BoxFit.cover,
                      width: 20,
                      height: 20,
                    ),
                    Text(
                      intlanguage('app100001148', '该表单已被删除'),
                      textAlign: TextAlign.left,
                      style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: ThemeColor.brand),
                    )
                  ],
                ),
              )
      ],
    );
  }

  Widget buildImportItem(String icon, String title, BuildContext context) {
    return Container(
      // height: 50,
      // alignment: Alignment.centerLeft,
      padding: EdgeInsets.only(left: 5, right: 15),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(6.0),
                child: SvgIcon(icon),
              ),
            ),
          ),
          SizedBox(
            width: 3,
          ),
          Text(
            title,
            style: CanvasTheme.of(context).inputbarTextStyle,
          ).tr()
        ],
      ),
    );
  }

  _previewQRcode(
      BuildContext context, QrCodeElement firstElement, VoidCallback refresh) {
    canvasKey.currentState?.multiSelect = false;
    FloatingBarHelper().dismissFloatingBar();
    CanvasElement canvasElement = CanvasElement(firstElement);
    canvasKey.currentState?.onAdvanceQRCodePreview(
      canvasElement,
      onElementValueDisplay: () {},
    );
  }
}
