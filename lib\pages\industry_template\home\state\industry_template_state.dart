import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';
import 'package:text/pages/create/model/printer_connect_state.dart';
import 'package:text/pages/industry_template/home/<USER>/hard_ware_serise_model.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_category_list_model.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_model.dart';
import 'package:text/pages/industry_template/home/<USER>/label_usage_record.dart';
import 'package:text/pages/industry_template/home/<USER>/local_industry_template_model.dart';
import 'package:text/pages/industry_template/home/<USER>/template_size_model.dart';
import 'package:text/pages/industry_template/select_label/hardware_list_model.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/hardware_manager.dart';
import 'package:text/pages/industry_template/home/<USER>/advertisement_model.dart';

enum IndustryTemplateGetGuilder { industryListPage }

/// 选中类型
enum IndustrySelectedState {
  /// 机型与尺寸
  modelSize,

  /// 行业与场景
  industryScenes,

  /// 默认状态
  none
}

/// 通用尺寸选中状态
// enum SizeSelectedState {
//   /// 标签纸尺寸
//   labelSize,
//
//   /// 所有尺寸
//   all,
//
//   /// 自定义尺寸
//   customSize,
//
//   /// 自定义宽度
//   customWidth,
//
//   /// 自定义高度
//   customHeight,
//
//   /// 机器默认尺寸
//   machineDefault,
// }

/// 首页状态
enum IndustryHomeState {
  /// 没有数据
  empty,

  ///正在加载
  loading,

  /// 显示内容
  showContainer,

  /// 网络异常
  error,
  vipExpire
}

/// 行业模版跳转画板过渡页状态
enum IndustryToCanvasTransitionState {
  /// 未连接不存在历史记录
  noConnect,

  /// 存在历史记录
  existHistory,

  /// 连接打印机且已识别
  connectAndRecognizedLabel,

  /// 连接打印机，打印机纸张未识别
  connectUnrecognizedLabel,

  /// 连接一台不带识别功能的打印机
  connectUnrecognizedMachine,

  /// 已连接机器，并识别到标签纸，但筛选时未选择识别到的纸
  transitionToLabelUsing
}

class IndustryTemplateState {
  late PanelController panelController;
  late TextEditingController searchEditingController;
  late RefreshController refreshController;

  /// 当前选中的场景
  var sceneSelect = IndustrySelectedState.none.obs;

  /// 是否正在展示画板中间过渡页
  var showTransitionPageState = 0; //0 已关闭  1 已完成展开 2 动画进行中

  /// 画板中间过渡页
  var transitionState = IndustryToCanvasTransitionState.noConnect.obs;

  /// 上次panel面板的position位置，主要用于判断是否正在收起
  double lastPosition = 0.0;

  /// 模版模型
  TemplateData? templateData;

  /// 标签纸模型,主要用于尺寸筛选
  LabelUsageRecord? labelUseModel;

  /// 画板标签纸模型，主要是进入画板使用
  LabelUsageRecord? labelCanvasModel;

  /// 当前连接的标签纸
  LabelUsageRecord? labelConnectingModel;

  /// 标签纸模型，对外使用, 确认当前标签纸是否符合打印机型号
  LabelUsageRecord? get labelModel {
    return labelUseModel;
  }

  /// 是否拥有标签纸
  bool get isHaveLabel {
    return labelModel != null;
  }

  /// 打印机系列列表
  List<HardWareSeriesModel>? hardwareSeriesList;

  /// 当前设备模型
  var hardWareSeriesModel = Rxn<HardWareSeriesModel>();

  /// 正在连接的硬件设备模型
  var hardwareSeriesModelUsing = Rxn<HardWareSeriesModel>();

  /// 打印机连接状态
  PrinterConnectState? printerState;

  /// 输出的size大小
  var size = Size.zero.obs;

  /// 是否是自定义Size
  bool get isCustomSize {
    return sizeSelectedState.value == "all" ? false : true;
  }

  /// 外部使用，主要是选择全部尺寸的时候选择机型默认宽高
  Size get interfaceSize {
    return size() == Size.zero
        ? Size(seriesFirstDevice?.maxPrintWidth?.toDouble() ?? 50, seriesFirstDevice?.maxPrintHeight?.toDouble() ?? 200)
        : size();
  }

  /// 系列下的第一个机型模型
  HardwareModel? get seriesFirstDevice {
    return HardWareManager.instance().findHardwareModel(machineId: hardWareSeriesModel()?.hardwareIdList?.first ?? '9');
  }

  /// 是否选中标签纸尺寸
  bool isLabelSizeSelected = false;

  /// 是否应用使用当前标签纸为背景
  bool isUsedLabelBackGround = false;

  /// 常用尺寸选中状态
  // var sizeSelectedState = Rxn<SizeSelectedState>();
  var sizeSelectedState = "all".obs;

  /// 自定义Size宽度
  double customWidth = 0;

  /// 自定义Size高度
  double customHeight = 0;

  /// 上次Size宽度
  double lastWidth = 0;

  /// 上次Size高度
  double lastHeight = 0;

  var notifyState = false;

  /// 是否手动选择了标签纸
  var hasSelectLabelByUser = false;

  /// 是否需要清空自定义尺寸,仅仅作为通知使用
  var isNeedClearCustomSize = false.obs;

  /// 机型与尺寸名称，用于顶部显示
  var hardWareWithSizeName = ''.obs;
  var deviceName = ''.obs;

  var industryScene = ''.obs;
  var connectState = true.obs;
  var isLabelMatch = true.obs;

  var templateSizes = <TemplateSizeModel>[];

  ///行业模板分页数据
  var categoryListModel = <TemplateData>[];
  var homeState = IndustryHomeState.loading;
  var page = 1;

  ///行业分类
  var industrySelect = 0.obs;
  var sortName = ''.obs;
  var industrytemplates = <IndustryTemplateModel>[].obs;
  final nativeIndustryTemplate = IndustryTemplateModel(
      id: -1,
      name: intlanguage('app01101', '全部'),
      childrens: [SceneTemplateModel(id: -1, name: intlanguage('app100001590', '全部行业场景'))],
      count: -1);

  ///行业分类历史记录
  var localIndustrytemplate = LocalIndustryTemplateModel().obs;

  var backFromSearchPage = false;

  /// 广告相关状态
  var currentAdvertisement = Rxn<AdvertisementModel>();
  int? previousAdvertisementId;
  var isAdvertisementLoading = false.obs;
  var lastAdvertisementUpdateTime = 0;
  var showAdvert = false.obs;

  IndustryTemplateState() {
    panelController = PanelController();
    searchEditingController = TextEditingController();
    refreshController = RefreshController(initialRefresh: false);
  }
}
