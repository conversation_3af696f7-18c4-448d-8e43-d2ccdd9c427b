//
//  JCBluetoothConnectAlert+Widget.m
//  Runner
//
//  Created by purpleaxis on 2023/7/12.
//

#import "JCBluetoothConnectAlert+Widget.h"
#import <YYWebImage/YYWebImage.h>
#import "YLButton.h"
#import "Runner-swift.h"
#import "JCDeviceSeriesHelp.h"
#import "NBCAPMiniAppEngine.h"
#import <Lottie/Lottie-Swift.h>

@implementation JCBluetoothConnectAlert(Widget)

- (UILabel *)getStateDescrLabelWithTitle:(NSString *)title{
    UILabel * stateDescrLabel = [[UILabel alloc] init];
    stateDescrLabel.font = MY_FONT_Regular(16);
    stateDescrLabel.textColor = HEX_RGB(0x999999);
    stateDescrLabel.numberOfLines = 0;
    stateDescrLabel.textAlignment = NSTextAlignmentCenter;
    stateDescrLabel.text = title;
    return stateDescrLabel;
}

- (UILabel *)getConnectLabel{
    UILabel *connectLabel = [[UILabel alloc] init];
    connectLabel.font = MY_FONT_Bold(13);
    connectLabel.textColor = HEX_RGB(0x262626);
    connectLabel.numberOfLines = 0;
    connectLabel.textAlignment = NSTextAlignmentCenter;
    connectLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app100001516", @"搜索到以下设备");
    return connectLabel;
}


- (UIView *)headerView{
    if(!self.headerViewC){
        [self setHeaderViewC:[[UIView alloc] init]];
        [ self.headerViewC addSubview:self.searchingView];
        [ self.headerViewC addSubview:self.connectView];
        [ self.headerViewC addSubview:self.stopSearchView];
        [self.searchingView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo( self.headerViewC);
        }];
        [self.connectView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo( self.headerViewC);
        }];
        [self.stopSearchView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo( self.headerViewC);
        }];
    }
    return  self.headerViewC;

}


- (void)setConnectPrinterImage:(JCBluetoothModel *)model imageView:(UIImageView *)printerImageView{
    JCSelectDeviceModel *printerModel = model.printerModel;
    UIImage *defaoutImage = XY_IMAGE_NAMED(printerModel.guide_name);
    if(!defaoutImage){
        NSString *url = printerModel.guide_image.lastPathComponent;
        if([url hasSuffix:@".png"]){
            NSRange range = [url rangeOfString:@".png"];
            NSString *imgName = [url substringToIndex:range.location];
            defaoutImage = XY_IMAGE_NAMED(imgName);
        }else if([url hasSuffix:@".jpg"]){
            NSRange range = [url rangeOfString:@".jpg"];
            NSString *imgName = [url substringToIndex:range.location];
            defaoutImage = XY_IMAGE_NAMED(imgName);
        }
        if(!defaoutImage){
            NSString *printerSeriesName = [[printerModel.series_name componentsSeparatedByString:@" "] safeObjectAtIndex:0];
            defaoutImage = XY_IMAGE_NAMED(printerSeriesName);
        }
    }
    [printerImageView sd_setImageWithURL:XY_URLWithString(printerModel.guide_image) placeholderImage:(defaoutImage != nil ? defaoutImage : XY_IMAGE_NAMED(@"占位符")) completed:^(UIImage *image, NSError *error, SDImageCacheType cacheType, NSURL *imageURL) {

    }];
}

// 此页面承载连接中以及连接完毕的页面
- (UIView *)connectView{
    if(!self.connectViewC){
        [self setConnectViewC:[[UIView alloc] init]];

        // 占位LayoutGuide
        UIView *layoutGuide = [[UIView alloc] initWithFrame:CGRectZero];
        layoutGuide.tag = 20240828;
        layoutGuide.hidden = YES;
        [self.connectViewC addSubview:layoutGuide];

        // 打印机图片
        UIImageView *printerImageView = [[UIImageView alloc] init];
        printerImageView.tag = 20001;
        [self.connectViewC addSubview:printerImageView];

        // 搜索状态
        CompatibleAnimation *animation = [[CompatibleAnimation alloc] initWithName:@"data_black" subdirectory:nil bundle:[NSBundle mainBundle]];
        CompatibleAnimationView *connectingImageView = [[CompatibleAnimationView alloc] initWithCompatibleAnimation:animation];
        connectingImageView.loopAnimationCount = -1;
        [connectingImageView play];
        [self.connectViewC addSubview:connectingImageView];
        connectingImageView.hidden = JC_IS_CONNECTED_PRINTER;
        connectingImageView.tag = 20003;

        // 连接状态
        UILabel *stateLabel = [self getStateDescrLabelWithTitle:@""];
        stateLabel.tag = 20002;

        // 确认连接以及连接中状态显示
        NSString *swithStr = @"";
        UIImage *connectTypeImage;
        if (JC_IS_CONNECTED_PRINTER) {
            JCBluetoothModel *connectedModel = [JCBluetoothManager sharedInstance].connectedModel;
            [self setConnectPrinterImage:connectedModel imageView:printerImageView];

            if (JC_CURRENT_CONNECTTYPE == JCConnectStateTypeBlueTooth) {
                swithStr = XY_LANGUAGE_TITLE_NAMED(@"app100000551", @"切换至Wi-Fi连接");
                connectTypeImage = XY_IMAGE_NAMED(@"bluetooth_connect");
            } else if(JC_CURRENT_CONNECTTYPE == JCConnectStateTypeWifi) {
                swithStr = XY_LANGUAGE_TITLE_NAMED(@"app100000550", @"切换至蓝牙连接");
                connectTypeImage = XY_IMAGE_NAMED(@"wifi_connect");
            }
        } else {
            stateLabel.textColor = HEX_RGB(0x999999);
            stateLabel.font = MY_FONT_Medium(16);
            stateLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app01415", @"正在连接");
        }
        [self.connectViewC addSubview:stateLabel];

        // 电量
        UIImageView *batteryLevelView = [[UIImageView alloc] init];
        batteryLevelView.image = [UIImage new];
        batteryLevelView.hidden = YES;
        [self.connectViewC addSubview:batteryLevelView];
        batteryLevelView.tag = 20004;
        
        // 修改别名按钮
        UIButton *editNameBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [editNameBtn setImage:XY_IMAGE_NAMED(@"edit_icon") forState:UIControlStateNormal];
        editNameBtn.tag = 20010;
        [editNameBtn addTarget:self action:@selector(operateEvent:) forControlEvents:UIControlEventTouchUpInside];
        editNameBtn.hidden = YES;
        [self.connectViewC addSubview:editNameBtn];

        // 连接机型名称
        UILabel *connectLabel = [self getStateDescrLabelWithTitle:STR_IS_NIL([JCBluetoothManager sharedInstance].connectedModel.alias) ? [JCBluetoothManager sharedInstance].connectedModel.name : [JCBluetoothManager sharedInstance].connectedModel.alias];
        connectLabel.textColor = HEX_RGB(0x262626);
        connectLabel.font = MY_FONT_Bold(16);
        connectLabel.lineBreakMode = NSLineBreakByTruncatingTail;
        connectLabel.numberOfLines = 1;
        [connectLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
        [self.connectViewC addSubview:connectLabel];
        connectLabel.tag = 20005;

        // 切换连接类型
        UIButton *switchBtn = [self operateButtonWithColor:HEX_RGB(0xF0F0F5) titleColor:HEX_RGB(0x161616) title:swithStr tag:20009 image:XY_IMAGE_NAMED(@"switchConnectType")];
        [self.connectViewC addSubview:switchBtn];

        // 蓝牙已连接｜｜WIFI已连接
        UIButton *connectTypeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [connectTypeBtn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00198", @"已连接") forState:UIControlStateNormal];
        [connectTypeBtn setTitleColor:HEX_RGB(0x2094FF) forState:UIControlStateNormal];
        [connectTypeBtn setImage:connectTypeImage forState:UIControlStateNormal];
        [connectTypeBtn jk_setImagePosition:0 spacing:2];
        connectTypeBtn.titleLabel.font = MY_FONT_Regular(14);
        connectTypeBtn.userInteractionEnabled = NO;
        connectTypeBtn.tag = 20006;
        [self.connectViewC addSubview:connectTypeBtn];

        // 断开
        UIButton *disConnectBtn = [YLButton buttonWithType:UIButtonTypeCustom];
        [disConnectBtn setTitleColor:HEX_RGB(0xFB4B42) forState:UIControlStateNormal];
        [disConnectBtn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00789", @"断开") forState:UIControlStateNormal];
        [disConnectBtn addTarget:self action:@selector(operateEvent:) forControlEvents:UIControlEventTouchUpInside];
        disConnectBtn.titleLabel.font = MY_FONT_Medium(14);
        disConnectBtn.backgroundColor = HEX_RGB(0xF0F0F5);
        disConnectBtn.layer.cornerRadius = 17;
        disConnectBtn.tag = 20007;
        [self.connectViewC addSubview:disConnectBtn];

        // 是否展示切换
        connectTypeBtn.hidden = !JC_IS_CONNECTED_PRINTER;
        disConnectBtn.hidden = !JC_IS_CONNECTED_PRINTER;
        JCBluetoothModel *connectedModel = [JCBluetoothManager sharedInstance].connectedModel;
        BOOL isHiddenSwitch = !JC_IS_CONNECTED_PRINTER || connectedModel.printer.isSupportWifi.integerValue == 0;
        switchBtn.hidden = isHiddenSwitch;

//        float disConnectBtnoffset = 5;
//        BOOL isHiddenSetting = YES;
//        if([[JCBluetoothManager sharedInstance].limitAppId isEqualToString:JC_ETAG_DEVICE_SERIES_STATUS]){
//            isHiddenSetting = NO;
//        }else if(!STR_IS_NIL([NBCAPMiniAppEngine sharedInstance].topMiniApp)){
//            isHiddenSetting = YES;
//        }else if(STR_IS_NIL([JCBluetoothManager sharedInstance].limitAppId)){
//            isHiddenSetting = NO;
//        }
//        if(isHiddenSetting){
//            disConnectBtnoffset = 0;
//        }

        // 约束设置
        [printerImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.connectViewC);
            make.top.equalTo(self.connectViewC);
            make.height.width.mas_equalTo(@(XY_AutoWidth(120)));
        }];
        [connectLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.connectViewC);
            make.top.equalTo(printerImageView.mas_bottom);
            make.leading.greaterThanOrEqualTo(self.connectViewC).offset(XY_AutoWidth(30));
        }];
        [batteryLevelView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(connectLabel.mas_centerY);
            make.leading.equalTo(connectLabel.mas_trailing).offset(2);
            make.height.mas_equalTo(@24);
            make.width.mas_equalTo(@24);
        }];
        [editNameBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(connectLabel.mas_centerY);
            make.leading.equalTo(batteryLevelView.mas_trailing).offset(2);
            make.height.mas_equalTo(@20);
            make.width.mas_equalTo(@20);
            make.trailing.lessThanOrEqualTo(self.connectViewC).inset(XY_AutoWidth(30));
        }];
        [stateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.connectViewC).offset(self.isRTL ? -17 : 17);;
            make.top.equalTo(connectLabel.mas_bottom).offset(6);
        }];
        [connectingImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(stateLabel.mas_centerY);
            make.trailing.equalTo(stateLabel.mas_leading).offset(-10);
            make.height.width.mas_equalTo(@(XY_AutoWidth(16)));
        }];

        // 占位居中
        [layoutGuide mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.connectViewC);
        }];

        // 连接成功才展示
        if (JC_IS_CONNECTED_PRINTER) {
            if (isHiddenSwitch) {
                // 不展示切换，代表只能单连
                [connectTypeBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.leading.equalTo(layoutGuide);
                    make.trailing.equalTo(disConnectBtn.mas_leading).inset(10);
                    make.centerY.equalTo(disConnectBtn);
                }];

                float titleWidth = [XY_LANGUAGE_TITLE_NAMED(@"app00789", @"断开") jk_sizeWithFont:MY_FONT_Medium(14) constrainedToHeight:1000].width;
                [disConnectBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(connectLabel.mas_bottom).offset(4);
                    make.height.mas_equalTo(@(32));
                    make.width.mas_equalTo(@(titleWidth + 44));
                    make.trailing.equalTo(layoutGuide);
                }];
            } else {
                // 展示切换，多连
                [connectTypeBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.centerX.equalTo(self.connectViewC);
                    make.top.equalTo(connectLabel.mas_bottom).offset(4);
                }];

                float titleWidth = [XY_LANGUAGE_TITLE_NAMED(@"app00789", @"断开") jk_sizeWithFont:MY_FONT_Medium(14) constrainedToHeight:1000].width;
                [disConnectBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(connectTypeBtn.mas_bottom).offset(5);
                    make.height.mas_equalTo(@(32));
                    make.width.mas_equalTo(@(titleWidth + 44));
                    make.trailing.equalTo(layoutGuide);
                }];

                float width = [XY_LANGUAGE_TITLE_NAMED(@"app100000551", @"切换到Wi-Fi连接") jk_sizeWithFont:MY_FONT_Regular(14) constrainedToWidth:1000].width + 18 + 28;
                [switchBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.trailing.equalTo(disConnectBtn.mas_leading).inset(10);
                    make.centerY.equalTo(disConnectBtn);
                    make.height.mas_equalTo(@((isHiddenSwitch? 0: 32)));
                    make.width.mas_equalTo(@(width));
                    make.leading.equalTo(layoutGuide);
                }];
            }
            // 设置背景色
            self.contentView.backgroundColor = HEX_RGB(0xF7F7FA);
        }

        [self.connectViewC layoutIfNeeded];
        self.connectViewC.hidden = !JC_IS_CONNECTED_PRINTER;
    }
    return self.connectViewC;
}


- (UIView *)switchDeviceLoadding:(JCConnectStateType)stateType msg:(NSString*)msg{
    UIView *switchLoaddingContentView = [[UIView alloc] init];
    [switchLoaddingContentView setTag:JCPRINTER_DEVICE_CONNECT_SWITCH_LOADING_ROOT_VIEW];

    UIActivityIndicatorView *indicatorView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhite];
    [indicatorView setTag:JCPRINTER_DEVICE_CONNECT_SWITCH_LOADING_INDICATOR_VIEW];
    [switchLoaddingContentView addSubview:indicatorView];

    UILabel *contentLabel = [[UILabel alloc] init];
    contentLabel.font = MY_FONT_Regular(15);
    contentLabel.textColor = COLOR_WHITE;
    contentLabel.numberOfLines = 0;
    contentLabel.text = msg;
    [contentLabel setTag:JCPRINTER_DEVICE_CONNECT_SWITCH_LOADING_MSG_VIEW];
    [switchLoaddingContentView addSubview:contentLabel];


    [indicatorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(switchLoaddingContentView.mas_leading);
        make.height.width.equalTo(@18);
        make.centerY.equalTo(switchLoaddingContentView.mas_centerY);
    }];

    [contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(switchLoaddingContentView.mas_trailing);
        make.top.bottom.equalTo(switchLoaddingContentView);
        make.leading.equalTo(indicatorView.mas_trailing);
        make.centerY.equalTo(switchLoaddingContentView.mas_centerY);
    }];

    return switchLoaddingContentView;
}


- (UIView *)noPrinterView{
    if(!self.noPrinterViewC){
        [self setNoPrinterViewC:[[UIView alloc] init]];
        self.noPrinterViewC.backgroundColor = HEX_RGB(0xF7F7FA);
        self.noPrinterViewC.hidden = YES;
        UILabel *titleLabel = [[UILabel alloc] init];
        titleLabel.font = MY_FONT_Medium(20);
        titleLabel.textColor = HEX_RGB(0x262626);
        titleLabel.numberOfLines = 0;
        titleLabel.tag = 50002;
        titleLabel.textAlignment = NSTextAlignmentCenter;
        titleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app100000087", @"搜不到您的打印机");
        [self.noPrinterViewC addSubview:titleLabel];

        UILabel *limitePrintLabel = [self getLimitePrintLabel];
        [self.noPrinterViewC addSubview:limitePrintLabel];

        UILabel *tipLabel = [[UILabel alloc] init];
        tipLabel.font = MY_FONT_Regular(15);
        tipLabel.textColor = HEX_RGB(0x999999);
        tipLabel.numberOfLines = 0;
        tipLabel.textAlignment = NSTextAlignmentCenter;
        tipLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app100000888", @"请尝试以下方法");
        [self.noPrinterViewC addSubview:tipLabel];

        UIScrollView *tipContentView = [[UIScrollView alloc] init];
        tipContentView.tag = 50003;
        [self.noPrinterViewC addSubview:tipContentView];

        UIView *connectTipContentView = [self getConnectTipContentView];
        [tipContentView addSubview:connectTipContentView];

        UITextView *contentTextView = [self getAttributeTextView];
        contentTextView.tag = 500031;
        [tipContentView addSubview:contentTextView];
        contentTextView.width = SCREEN_WIDTH - 2 * XY_AutoWidth(32);
        [contentTextView sizeToFit];

        UIButton *reScanBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        reScanBtn.backgroundColor = HEX_RGBA(0x2878FF,0.08);
        reScanBtn.titleLabel.font = MY_FONT_Medium(15);
        [reScanBtn setTitleColor:HEX_RGB(0x2878FF) forState:UIControlStateNormal];
        [reScanBtn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000091", @"重新搜索") forState:UIControlStateNormal];
        [reScanBtn addTarget:self action:@selector(operateEvent:) forControlEvents:UIControlEventTouchUpInside];
        reScanBtn.layer.cornerRadius = XY_AutoWidth(20);
        reScanBtn.tag = 50001;
        [tipContentView addSubview:reScanBtn];


        [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.noPrinterViewC).offset(XY_AutoWidth(18));
            make.leading.equalTo(self.noPrinterViewC).offset(XY_AutoWidth(32));
            make.trailing.equalTo(self.noPrinterViewC).offset(-XY_AutoWidth(32));
        }];

        if(!STR_IS_NIL([JCBluetoothManager sharedInstance].limitAppId)){
            [limitePrintLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(titleLabel.mas_bottom).offset(XY_AutoWidth(6));
                make.leading.equalTo(self.noPrinterViewC).offset(XY_AutoWidth(32));
                make.trailing.equalTo(self.noPrinterViewC).offset(-XY_AutoWidth(32));
            }];
            [tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(limitePrintLabel.mas_bottom).offset(XY_AutoWidth(6));
                make.leading.equalTo(self.noPrinterViewC).offset(XY_AutoWidth(32));
                make.trailing.equalTo(self.noPrinterViewC).offset(-XY_AutoWidth(32));
            }];
            [tipContentView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(tipLabel.mas_bottom).offset(XY_AutoWidth(30));
                make.leading.trailing.bottom.equalTo(self.noPrinterViewC);
            }];
        }else{
            [tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(titleLabel.mas_bottom).offset(XY_AutoWidth(6));
                make.leading.equalTo(self.noPrinterViewC).offset(XY_AutoWidth(32));
                make.trailing.equalTo(self.noPrinterViewC).offset(-XY_AutoWidth(32));
            }];
            [tipContentView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(tipLabel.mas_bottom).offset(XY_AutoWidth(30));
                make.leading.trailing.bottom.equalTo(self.noPrinterViewC);
            }];
        }
        [connectTipContentView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(tipContentView);
            make.leading.trailing.equalTo(self.noPrinterViewC);
            make.height.mas_equalTo(@(connectTipContentView.height));
        }];

        [contentTextView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(connectTipContentView.mas_bottom).offset(32);
            make.leading.equalTo(self.noPrinterViewC).offset(XY_AutoWidth(32));
            make.trailing.equalTo(self.noPrinterViewC).offset(-XY_AutoWidth(32));
            make.height.mas_equalTo(contentTextView.height);
        }];

        float width = [XY_LANGUAGE_TITLE_NAMED(@"app100000091", @"重新搜索") jk_sizeWithFont:MY_FONT_Medium(15) constrainedToWidth:1000].width + 60;
        [reScanBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.noPrinterViewC);
            make.top.equalTo(contentTextView.mas_bottom).offset(XY_AutoWidth(60));
            make.width.mas_equalTo(@(XY_AutoWidth(width)));
            make.height.mas_equalTo(@(XY_AutoWidth(40)));
        }];
        [tipContentView layoutIfNeeded];
        tipContentView.contentSize = CGSizeMake(SCREEN_WIDTH, reScanBtn.bottom + 20);

    }
    return self.noPrinterViewC;
}


#pragma clang diagnostic pop

- (UIView *)searchingView{
    if(!self.searchingViewC){
        [self setSearchingViewC:[[UIView alloc] init]];
        CompatibleAnimation *animation = [[CompatibleAnimation alloc] initWithName:@"Printer_Search" subdirectory:nil bundle:[NSBundle mainBundle]];
        CompatibleAnimationView *lottieView = [[CompatibleAnimationView alloc] initWithCompatibleAnimation:animation];
        lottieView.loopAnimationCount = -1;
        [lottieView play];
        [self.searchingViewC addSubview:lottieView];
//        //searchingImageView应该没有实际现实效果了，以前是用来做gif动画的，现在没有用了，只是用来做参考布局的
        UIImageView *searchingImageView = [[UIImageView alloc] initWithFrame:CGRectZero];
        [self.searchingViewC addSubview:searchingImageView];
        searchingImageView.hidden = YES;
        UILabel *stateLabel = [self getStateDescrLabelWithTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000092", @"正在搜索附近的打印机...")];
        stateLabel.tag = 10002;
        stateLabel.hidden = NO;
        UIView *searchingTipContentView = [self getSearchingTipContentView];
        searchingTipContentView.tag = 10003;
        searchingTipContentView.hidden = YES;
        [self.searchingViewC addSubview:stateLabel];
        [self.searchingViewC addSubview:searchingTipContentView];
        [searchingImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.searchingViewC);
            make.top.equalTo(self.searchingViewC);
            make.width.height.mas_equalTo(@(XY_AutoWidth(198)));
        }];
        [lottieView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.searchingViewC);
            make.top.equalTo(self.searchingViewC).offset(-40);
            make.width.height.mas_equalTo(@(XY_AutoWidth(260)));
        }];
        [stateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.searchingViewC);
            make.bottom.equalTo(searchingImageView.mas_bottom);
            make.width.mas_equalTo(@(SCREEN_WIDTH - 2 * XY_AutoWidth(52)));
        }];
        [searchingTipContentView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.searchingViewC);
            make.top.equalTo(searchingImageView.mas_bottom).offset(-22);
            make.size.mas_equalTo(@(searchingTipContentView.size));
        }];
        [self.searchingViewC layoutIfNeeded];
        self.searchingViewC.hidden = YES;
    }
    return self.searchingViewC;
}

- (UIView *)getSearchingTipContentView{
    UIView *searchingTipContentView = [[UIView alloc] init];
    NSString *title1 = XY_LANGUAGE_TITLE_NAMED(@"app100000552", @"确保打印机已开机（长按开机键）");
    NSString *descrp1 = XY_LANGUAGE_TITLE_NAMED(@"app100000553", @"请排除打印机充电时灯亮但未开机的情况");
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = title1;
    titleLabel.textColor = HEX_RGB(0x262626);
    titleLabel.font = MY_FONT_Medium(13);
    titleLabel.numberOfLines = 0;
    titleLabel.textAlignment = NSTextAlignmentCenter;
    [searchingTipContentView addSubview:titleLabel];
    UILabel *descrpLabel = [[UILabel alloc] init];
    descrpLabel.text = descrp1;
    descrpLabel.textColor = HEX_RGB(0x595959);
    descrpLabel.font = MY_FONT_Regular(13);
    descrpLabel.numberOfLines = 0;
    descrpLabel.textAlignment = NSTextAlignmentCenter;
    [searchingTipContentView addSubview:descrpLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(searchingTipContentView).offset(0);
        make.centerX.equalTo(searchingTipContentView);
        make.width.mas_equalTo(@(SCREEN_WIDTH - 2 * 26));
    }];
    [descrpLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(titleLabel.mas_bottom).offset(2);
        make.centerX.equalTo(searchingTipContentView);
        make.width.mas_equalTo(@(SCREEN_WIDTH - 2 * 26));
    }];
    [searchingTipContentView layoutIfNeeded];
    searchingTipContentView.height = descrpLabel.bottom;
    return searchingTipContentView;
}


- (UIButton *)operateButtonWithColor:(UIColor *)btnColor titleColor:(UIColor *)titleColor
                               title:(NSString *)title tag:(NSInteger)tag image:(UIImage *)image{
    UIButton *operateBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    operateBtn.backgroundColor = btnColor;
    [operateBtn setTitle:title forState:UIControlStateNormal];
    [operateBtn setTitleColor:titleColor forState:UIControlStateNormal];
    operateBtn.layer.cornerRadius = XY_AutoWidth(17);
    if(image == nil){
        operateBtn.layer.borderColor = HEX_RGB(0xEBEBEB).CGColor;
        operateBtn.layer.borderWidth = 0.5;
    }else{
        [operateBtn setImage:image forState:UIControlStateNormal];
        [operateBtn jk_setImagePosition:0 spacing:3];
    }
    operateBtn.titleLabel.font = MY_FONT_Regular(14);
    operateBtn.lineBreakMode = NSLineBreakByTruncatingTail;
    [operateBtn addTarget:self action:@selector(operateEvent:) forControlEvents:UIControlEventTouchUpInside];
    operateBtn.tag = tag;
    return operateBtn;
}

- (UIView *)stopSearchView{
    if(!self.stopSearchViewC){
        [self setStopSearchViewC:[[UIView alloc] init]];
        UIImageView *stopSearchImageView = [[UIImageView alloc] init];
        stopSearchImageView.image = [UIImage imageNamed:@"stopSearchImage"];
        [self.stopSearchViewC addSubview:stopSearchImageView];
        UIImageView *centerBakImageView = [[UIImageView alloc] init];
        centerBakImageView.image = [UIImage imageNamed:@"centerBackImage"];
        [self.stopSearchViewC addSubview:centerBakImageView];
        UIImageView *centerImageView = [[UIImageView alloc] init];
        centerImageView.image = [UIImage imageNamed:@"printerSearch"];
        [self.stopSearchViewC addSubview:centerImageView];
        self.stopSearchViewC.hidden = YES;
        UILabel *stateLabel = [self getStateDescrLabelWithTitle:XY_LANGUAGE_TITLE_NAMED(@"", @"")];
        stateLabel.font = MY_FONT_Medium(20);
        [self.stopSearchViewC addSubview:stateLabel];
        UILabel *stateTipLabel = [self getStateDescrLabelWithTitle:iPhoneX?XY_LANGUAGE_TITLE_NAMED(@"app100000093", @"请从屏幕顶端下滑打开蓝牙"):XY_LANGUAGE_TITLE_NAMED(@"app100000094", @"请从屏幕底部上滑打开蓝牙")];
        stateTipLabel.font = MY_FONT_Medium(18);
        stateTipLabel.textColor = HEX_RGB(0x262626);
        stateTipLabel.hidden = YES;
        [self.stopSearchViewC addSubview:stateTipLabel];
        UILabel *stateWBDesLabel = [self getStateDescrLabelWithTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000898", @"（部分机型可通过WiFi连接）")];
        stateWBDesLabel.font = MY_FONT_Medium(13);
        stateWBDesLabel.textColor = HEX_RGB(0x999999);
        stateWBDesLabel.hidden = YES;
        [self.stopSearchViewC addSubview:stateWBDesLabel];
        UIButton *searchBtn1 = [UIButton buttonWithType:UIButtonTypeCustom];
        [searchBtn1 addTarget:self action:@selector(operateEvent:) forControlEvents:UIControlEventTouchUpInside];
        float refreshBtnWidth = [XY_LANGUAGE_TITLE_NAMED(@"app100000091", @"重新搜索") jk_sizeWithFont:MY_FONT_Regular(13) constrainedToWidth:1000].width;
        YLButton *searchBtn2 = [YLButton buttonWithType:UIButtonTypeCustom];
        searchBtn2.titleLabel.font = MY_FONT_Regular(13);
        searchBtn2.layer.cornerRadius = 16;
        searchBtn2.backgroundColor = HEX_RGBA(0x747480,0.08);
        [searchBtn2 setTitleColor:HEX_RGB(0x000000) forState:UIControlStateNormal];
        [searchBtn2 setTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000091", @"重新搜索") forState:UIControlStateNormal];
        [searchBtn2 setImage:XY_IMAGE_NAMED(@"bluetooth_refresh") forState:UIControlStateNormal];
        [searchBtn2 addTarget:self action:@selector(operateEvent:) forControlEvents:UIControlEventTouchUpInside];
        searchBtn2.imageRect = CGRectMake(16, 9, 14, 14);
        searchBtn2.titleRect = CGRectMake(30+5, 6, refreshBtnWidth, 20);
        YLButton *searchBtn3 = [YLButton buttonWithType:UIButtonTypeCustom];
        searchBtn3.titleLabel.font = MY_FONT_Medium(16);
        [searchBtn3 setTitleColor:HEX_RGB(0x5C88C1) forState:UIControlStateNormal];
        [searchBtn3 setImage:XY_IMAGE_NAMED(@"connectArrow") forState:UIControlStateNormal];
        [searchBtn3 setTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000575", @"去开启") forState:UIControlStateNormal];
        [searchBtn3 addTarget:self action:@selector(operateEvent:) forControlEvents:UIControlEventTouchUpInside];
        [self.stopSearchViewC addSubview:searchBtn1];
        [self.stopSearchViewC addSubview:searchBtn2];
        [self.stopSearchViewC addSubview:searchBtn3];
        searchBtn1.hidden = YES;
        searchBtn2.hidden = YES;
        searchBtn3.hidden = YES;
        searchBtn1.tag = 30003;
        searchBtn2.tag = 30004;
        stateLabel.tag = 30001;
        stateTipLabel.tag = 30002;
        searchBtn3.tag = 30005;
        stateWBDesLabel.tag = 30006;
        float refreshBtn3Width = [XY_LANGUAGE_TITLE_NAMED(@"app100000575", @"去开启") jk_sizeWithFont:MY_FONT_Medium(16) constrainedToWidth:1000].width;
        searchBtn3.titleRect = CGRectMake(20, 0, refreshBtn3Width, 40);
        searchBtn3.imageRect = CGRectMake(20 + refreshBtn3Width, 11, 18, 18);
        refreshBtn3Width = refreshBtn3Width + 58;
        [stopSearchImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.stopSearchViewC);
            make.top.equalTo(self.stopSearchViewC).offset(40);
            make.height.width.mas_equalTo(@(XY_AutoWidth(144)));
        }];
        [searchBtn1 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.stopSearchViewC);
            make.top.equalTo(self.stopSearchViewC).offset(40);
            make.height.width.mas_equalTo(@(XY_AutoWidth(144)));
        }];
        [searchBtn2 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(stopSearchImageView.mas_centerX);
            make.top.equalTo(stopSearchImageView.mas_bottom).offset(17);
            make.width.mas_equalTo(@(refreshBtnWidth + 32 + 12 + 8));
            make.height.mas_equalTo(@32);
        }];
        [centerBakImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(stopSearchImageView.mas_centerX);
            make.centerY.equalTo(stopSearchImageView.mas_centerY);
            make.height.width.mas_equalTo(@(XY_AutoWidth(40)));
        }];
        [centerImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(stopSearchImageView.mas_centerX);
            make.centerY.equalTo(stopSearchImageView.mas_centerY);
            make.height.width.mas_equalTo(@(XY_AutoWidth(17)));
        }];
        [stateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.stopSearchViewC);
            make.top.equalTo(stopSearchImageView.mas_bottom).offset(33);
            make.width.mas_equalTo(@(SCREEN_WIDTH - 2 * XY_AutoWidth(52)));
        }];
        [stateTipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.stopSearchViewC);
            make.top.equalTo(stateLabel.mas_bottom).offset(XY_AutoWidth(6));
            make.width.mas_equalTo(@(SCREEN_WIDTH - 2 * XY_AutoWidth(52)));
        }];
        [stateWBDesLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.stopSearchViewC);
            make.top.equalTo(stateTipLabel.mas_bottom).offset(XY_AutoWidth(3));
            make.width.mas_equalTo(@(SCREEN_WIDTH - 2 * XY_AutoWidth(52)));
        }];
        [searchBtn3 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(stopSearchImageView.mas_centerX);
            make.top.equalTo(stateWBDesLabel.mas_bottom).offset(22);
            make.width.mas_equalTo(@(refreshBtn3Width));
            make.height.mas_equalTo(@40);
        }];
    }
    return self.stopSearchViewC;
}

//打印机连接出错后的弹框状态
- (UIView *)getConnectTipContentView{
    UIView *connectTipContentView = [[UIView alloc] init];
    NSString *title1 = XY_LANGUAGE_TITLE_NAMED(@"app100000552", @"确保打印机已开机（长按开机键）");
    NSString *descrp1 = XY_LANGUAGE_TITLE_NAMED(@"app100000553", @"请排除打印机充电时灯亮但未开机的情况");
    float title1Height = [title1 jk_sizeWithFont:MY_FONT_Medium(15) constrainedToWidth:SCREEN_WIDTH - 2 * 24 - 2 * 20 - 27 - 13].height;
    float descrp1Height = [descrp1 jk_sizeWithFont:MY_FONT_Regular(13) constrainedToWidth:SCREEN_WIDTH - 2 * 24 - 2 * 20 - 27 - 13].height;
    float height1 = 2 * 16 + title1Height + 2 + descrp1Height;

    NSString *title2 = XY_LANGUAGE_TITLE_NAMED(@"app100000554", @"确保打印机连接App未达上限");
    NSString *descrp2 = XY_LANGUAGE_TITLE_NAMED(@"app100000555", @"如已连接其他App请先断开");
    float title2Height = [title2 jk_sizeWithFont:MY_FONT_Medium(15) constrainedToWidth:SCREEN_WIDTH - 2 * 24 - 2 * 20 - 27 - 13].height;
    float descrp2Height = [descrp2 jk_sizeWithFont:MY_FONT_Regular(13) constrainedToWidth:SCREEN_WIDTH - 2 * 24 - 2 * 20 - 27 - 13].height;
    float height2 = 2 * 16 + title2Height + 2 + descrp2Height;

    NSString *title3 = XY_LANGUAGE_TITLE_NAMED(@"app100000556", @"确保打印机在手机附近");
    NSString *descrp3 = XY_LANGUAGE_TITLE_NAMED(@"app100000557", @"30CM内最佳");
    float title3Height = [title3 jk_sizeWithFont:MY_FONT_Medium(15) constrainedToWidth:SCREEN_WIDTH - 2 * 24 - 2 * 20 - 27 - 13].height;
    float descrp3Height = [descrp3 jk_sizeWithFont:MY_FONT_Regular(13) constrainedToWidth:SCREEN_WIDTH - 2 * 24 - 2 * 20 - 27 - 13].height;
    float height3 = 2 * 16 + title3Height + 2 + descrp3Height;

    NSString *title4 = XY_LANGUAGE_TITLE_NAMED(@"app100000876", @"确保手机Wi-Fi打开");
    NSString *descrp4 = XY_LANGUAGE_TITLE_NAMED(@"app100000877", @"需打印机支持Wi-Fi连接功能");
    float title4Height = [title4 jk_sizeWithFont:MY_FONT_Medium(15) constrainedToWidth:SCREEN_WIDTH - 2 * 24 - 2 * 20 - 27 - 13].height;
    float descrp4Height = [descrp4 jk_sizeWithFont:MY_FONT_Regular(13) constrainedToWidth:SCREEN_WIDTH - 2 * 24 - 2 * 20 - 27 - 13].height;
    float height4 = 2 * 16 + title4Height + 2 + descrp4Height;

    UIView *tipCell1 = [self getBannerCellViewWith:@"printerPowerCheck" title:title1 descrp:descrp1 height:height1];
    tipCell1.layer.cornerRadius = 12;
    UIView *tipCell2 = [self getBannerCellViewWith:@"connectNumberCheck" title:title2 descrp:descrp2 height:height2];
    tipCell2.layer.cornerRadius = 12;
    UIView *tipCell3 = [self getBannerCellViewWith:@"deviceDistanceCheck" title:title3 descrp:descrp3 height:height3];
    tipCell3.layer.cornerRadius = 12;

    UIView *tipCell4 = [self getBannerCellViewWith:@"wifiStatusCheck" title:title4 descrp:descrp4 height:height4];
    tipCell4.layer.cornerRadius = 12;
    [connectTipContentView addSubview:tipCell1];
    [connectTipContentView addSubview:tipCell2];
    [connectTipContentView addSubview:tipCell3];
    [connectTipContentView addSubview:tipCell4];
    [tipCell1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(connectTipContentView);
        make.leading.equalTo(connectTipContentView).offset(24);
        make.trailing.equalTo(connectTipContentView).offset(-24);
        make.height.mas_equalTo(tipCell1.height);
    }];
    [tipCell2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(tipCell1.mas_bottom).offset(12);
        make.leading.equalTo(connectTipContentView).offset(24);
        make.trailing.equalTo(connectTipContentView).offset(-24);
        make.height.mas_equalTo(tipCell2.height);
    }];
    [tipCell3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(tipCell2.mas_bottom).offset(12);
        make.leading.equalTo(connectTipContentView).offset(24);
        make.trailing.equalTo(connectTipContentView).offset(-24);
        make.height.mas_equalTo(tipCell3.height);
    }];

    [tipCell4 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(tipCell3.mas_bottom).offset(12);
        make.leading.equalTo(connectTipContentView).offset(24);
        make.trailing.equalTo(connectTipContentView).offset(-24);
        make.height.mas_equalTo(tipCell4.height);
    }];
    [connectTipContentView layoutIfNeeded];
    connectTipContentView.size = CGSizeMake(SCREEN_WIDTH - 2 * 24, tipCell4.bottom);
    return connectTipContentView;
}


- (UIView *)getBannerCellViewWith:(NSString *)imageName title:(NSString *)titleName descrp:(NSString *)descrpName height:(float)cellHeight{
    UIView *bannerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 2 * 24, cellHeight)];
    bannerView.backgroundColor = HEX_RGB(0xFFFFFF);
    UIImageView *leftImageView = [[UIImageView alloc] init];
    leftImageView.image = XY_IMAGE_NAMED(imageName);
    leftImageView.contentMode = UIViewContentModeScaleAspectFit;
    [bannerView addSubview:leftImageView];
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = titleName;
    titleLabel.textColor = HEX_RGB(0x262626);
    titleLabel.font = MY_FONT_Medium(15);
    titleLabel.numberOfLines = 0;
    [bannerView addSubview:titleLabel];
    UILabel *descrpLabel = [[UILabel alloc] init];
    descrpLabel.text = descrpName;
    descrpLabel.textColor = HEX_RGB(0x595959);
    descrpLabel.font = MY_FONT_Regular(13);
    descrpLabel.numberOfLines = 0;
    [bannerView addSubview:descrpLabel];
    [leftImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(bannerView);
        make.leading.equalTo(bannerView).offset(20);
        make.width.height.mas_equalTo(@40);
    }];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(bannerView).offset(16);
        make.leading.equalTo(leftImageView.mas_trailing).offset(13);
        make.trailing.equalTo(bannerView).offset(-20);
    }];
    [descrpLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(titleLabel.mas_bottom).offset(2);
        make.leading.equalTo(titleLabel.mas_leading);
        make.trailing.equalTo(bannerView).offset(-20);
    }];
    [bannerView layoutIfNeeded];
    float bottom = descrpLabel.bottom + 16;
    bannerView.size = CGSizeMake(SCREEN_WIDTH - 2 * 24, bottom);
    return bannerView;
}

- (UIView *)showCommitSwitchToast:(NSInteger)toasttype message:(NSString *)tipMessage{
    UIView *contentView = [[UIView alloc] init];
    contentView.backgroundColor = COLOR_CLEAR;
    contentView.clipsToBounds = YES;
    contentView.layer.cornerRadius = 12;
    float height = [tipMessage jk_sizeWithFont:MY_FONT_Regular(15) constrainedToWidth:(SCREEN_WIDTH - 60 - 60)].height + 30;
    float width = [tipMessage jk_sizeWithFont:MY_FONT_Regular(15) constrainedToHeight:10000].width + 40 + 17 + 5;
    if(width > SCREEN_WIDTH - 60 - 40){
        width = SCREEN_WIDTH - 60 - 40;
    }
    contentView.size = CGSizeMake(width, height);
    contentView.center = CGPointMake(SCREEN_WIDTH/2, SCREEN_HEIGHT/2);
    UIView *maskView = [[UIView alloc] init];
    maskView.backgroundColor = HEX_RGBA(0x000000, 0.6);
    [contentView addSubview:maskView];
    [maskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(contentView);
    }];
    UILabel *contentLable = [[UILabel alloc] init];
    contentLable.font = MY_FONT_Regular(15);
    contentLable.textColor = COLOR_WHITE;
    contentLable.numberOfLines = 0;
    contentLable.text = tipMessage;
    [contentView addSubview:contentLable];
    UIImageView *iconImageView = [[UIImageView alloc] init];
    iconImageView.image = XY_IMAGE_NAMED(@"commitSuccess");
    iconImageView.hidden = toasttype != 1;
    UIActivityIndicatorView *indicatorView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhite];
    indicatorView.hidden = toasttype == 1;
    toasttype == 1?[indicatorView stopAnimating]:[indicatorView startAnimating];
    [contentView addSubview:iconImageView];
    [contentView addSubview:indicatorView];
    [contentLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(iconImageView.mas_trailing).offset(5);
        make.trailing.equalTo(contentView).offset(-20);
        make.centerY.equalTo(contentView);
    }];
    [iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(contentView).offset(20);
        make.centerY.equalTo(contentView);
        make.width.height.mas_equalTo(@17);
    }];
    [indicatorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(contentView).offset(20);
        make.centerY.equalTo(contentView);
        make.width.height.mas_equalTo(@17);
    }];
    contentView.hidden = NO;
    UIView *containerView = [UIApplication sharedApplication].keyWindow;
    [containerView addSubview:contentView];
    if(toasttype == 1){
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [contentView removeFromSuperview];
        });
    }
    return contentView;
}


- (void)hiddenPrinterOperateErrorTipView{
    self.noPrinterView.hidden = YES;
}


- (UILabel *)getLimitePrintLabel{
    UILabel *limitePrintLabel = [[UILabel alloc] init];
    limitePrintLabel.font = MY_FONT_Medium(15);
    limitePrintLabel.textColor = HEX_RGB(0x999999);
    limitePrintLabel.numberOfLines = 0;
    limitePrintLabel.textAlignment = NSTextAlignmentCenter;
    NSString *limitDecrp = @"";
    NSString *limitAppId = [JCBluetoothManager sharedInstance].limitAppId;
    if(!STR_IS_NIL(limitAppId)){
        if([limitAppId isEqualToString:JC_ETAG_DEVICE_SERIES_STATUS]){
            limitDecrp = XY_LANGUAGE_TITLE_NAMED(@"", @"当前仅支持精臣ET10");
        }else{
            NSDictionary *uniappSupportInfo = [[NSUserDefaults standardUserDefaults] objectForKey:limitAppId];
            if(!STR_IS_NIL(limitAppId) && [[[NSUserDefaults standardUserDefaults] objectForKey:limitAppId] isKindOfClass:[NSDictionary class]]){
                NSNumber *supportedSeries = uniappSupportInfo[@"supportedSeries"];
                if(supportedSeries.boolValue){
                    limitDecrp = XY_LANGUAGE_TITLE_NAMED(@"app100001984", @"当前仅支持精臣$");
                }else{
                    limitDecrp = XY_LANGUAGE_TITLE_NAMED(@"", @"当前不支持精臣$");
                }
                NSString *seriesString = uniappSupportInfo[@"seriesIdString"];
                if(!STR_IS_NIL(seriesString) && [seriesString componentsSeparatedByString:@","].count > 0){
                    NSArray *seriesModelsArr = [JCDeviceSeriesHelp deviceArrayWithSeriesIds:[seriesString componentsSeparatedByString:@","]];
                    NSMutableArray *limitSeriesArr = [NSMutableArray array];
                    for (JCSelectDeviceModel *seriesModel in seriesModelsArr) {
                        [limitSeriesArr addObject:seriesModel.series_name];
                    }
                    if(limitSeriesArr.count > 0){
                        NSString *devicesStr = [limitSeriesArr componentsJoinedByString:@","];
                        limitDecrp = [limitDecrp stringByReplacingOccurrencesOfString:@"$" withString:devicesStr];
                    }
                }
            }
        }
    }else{
        limitDecrp = @"";
    }
    limitePrintLabel.text = limitDecrp;
    limitePrintLabel.hidden = STR_IS_NIL(limitDecrp);
    return limitePrintLabel;
}







#pragma mark - common tool method
- (UIImage*)convertViewToImage:(UIView *)tempView {
    CGSize s = tempView.bounds.size;
    // 下面方法，第一个参数表示区域大小。第二个参数表示是否是非透明的。如果需要显示半透明效果，需要传NO，否则传YES。第三个参数就是屏幕密度了
    UIGraphicsBeginImageContextWithOptions(s, NO, [UIScreen mainScreen].scale);
    [tempView.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return image;
}


#pragma Others
//----------------------------------
- (NSAttributedString *)getContentLabelAttributedText:(NSString *)text font:(UIFont *)labelFont textColor:(UIColor *)textColor
                                               title2:(NSString *)title2 textColor2:(UIColor *)textColor2
{
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    [paragraphStyle setLineSpacing:1.25];
    NSRange rang2 = [text rangeOfString:title2];
    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:text attributes:@{NSFontAttributeName:labelFont,NSForegroundColorAttributeName:HEX_RGB(0x262626)}];
    [attrStr addAttributes:@{NSParagraphStyleAttributeName:paragraphStyle} range:NSMakeRange(0, text.length)];
    [attrStr addAttribute:NSLinkAttributeName value:@"helpCenterProtocol://" range:rang2];
    [attrStr addAttribute:NSForegroundColorAttributeName value:textColor2 range:rang2];
    return attrStr;
}

- (UITextView *)getAttributeTextView{
    NSString *content = XY_LANGUAGE_TITLE_NAMED(@"app100000571", @"以上都无效，请尝试重新启动APP或蓝牙，如需更多帮助请查看");
    content = [NSString stringWithFormat:@"%@ %@",content,XY_LANGUAGE_TITLE_NAMED(@"app100000089", @"帮助指南>")];
    content = [content stringByReplacingOccurrencesOfString:@"\\n" withString:@" \r\n"];
    UITextView *contentTextView = [[UITextView alloc] initWithFrame:CGRectZero];
    contentTextView.attributedText = [self getContentLabelAttributedText:content font:MY_FONT_Medium(16) textColor:HEX_RGB(0x262626) title2:XY_LANGUAGE_TITLE_NAMED(@"app100000089", @"帮助指南>") textColor2:XY_HEX_RGB(0x5C88C1)];
    contentTextView.linkTextAttributes = @{NSForegroundColorAttributeName : XY_HEX_RGB(0x5C88C1)};
    contentTextView.textAlignment = NSTextAlignmentLeft;
    contentTextView.delegate = self;
    contentTextView.editable = NO;        //必须禁止输入，否则点击将弹出输入键盘
    contentTextView.backgroundColor = nil;
//    contentTextView.scrollEnabled = NO;
    return contentTextView;
}

@end
