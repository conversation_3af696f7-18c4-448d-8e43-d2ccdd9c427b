import 'dart:convert';

import 'package:isar/isar.dart';
part 'template_detail_model.g.dart';

@collection
class NiimBotTemplateCanvasDataModel {
  // Id? id = Isar.autoIncrement;
  @Index()
  String? templateId; //模板ID
  Id get isarId => fastHash(templateId!);

  // String? excelTaskData; //导入的excel模板externalData 修改过的字段
  String? platformCode; //数据平台来源(iOS,Android,PC)
  @Index()
  String? userId; //创建的用户id
  @Index()
  String? labelId; //云标签id
  @Index()
  String? sourceId; //云模板id
  String? originTemplateId; //创建的用户id
  int? localType; //记录同步状态(0创建，1更新，2删除)
  String? margin; //边距 double （不打印区域）（上右下左）(单位毫米)
  String? machineName; //当前模板适配机型（B11、B3S、B50）
  int? paperType; //纸张类型: 1:间隙纸 2:黑标纸 3:连续纸 4:定孔纸 5:透明纸 6 标牌 10 黑标间隙纸 11 热缩管
  String? backgroundImage; //模板背景图url路径（传SDK时使用base64）
  String? templateVersion; //模板版本
  @Index()
  String? elements; //画板上的元素
  String? values; //存储元素对应的值(新增)
  String? industryId; //行业分类id
  int? templateClass; //是标签还是模板： 0 标签    1 模版
  int? canvasRotate; //画板旋转角度
  String? dataSource; //excel商品库数据源绑定，数据源的基本信息（名称，下载地址）
  String? dataSourceModifies; //excel商品库修改后某一行一列的数据
  String? dataSourceBindInfo; //excel商品库用户筛选后的excel数据
  String? names; //模板名称(包含多语言)
  String? hardwareSeriesId; //"硬件系列ID",//(SDK技术分类)
  @Index()
  String? name; //模块名称
  int? consumableType; //耗材类型 1：标签 2：标牌
  String? sparedCode; //备用条码
  String? barcode; // 条码值,可用于查询
  double? width; //模板宽度
  double? height; //模板高度
  String? labelNames; //原始标签名称
  ///todo 待定字段
  String? materialModelSn; //标签纸sn码
  @Index()
  String? folderId; //所属文件夹Id
  @Index()
  bool? isDelete; //是否被删除
  String? paperColor; //标签纸颜色[[0,0,0], [230.0.18]]
  String? amazonCodeWuhan; //亚马逊条码（武汉）
  String? amazonCodeBeijing; //标亚马逊条码（北京）
  String? virtualBarCode;
  ///todo 待定字段
  String? commodityInfo; //商品信息
  String? barcodeCategoryMap; //条码集合（搜索使用）
  @Index()
  String? updateTime; //最后更新时间
  String? templateType; //模板类型：0_普通模板, 1_云模板,2_用户模板样例,4_云模板样例
  int? rotate; //模板旋转角度（模板整体旋转,中心点为锚点，范围0、90、180、270）
  bool? isCable; //是否线缆标签
  bool? isEdited; //特供版行业模版状态：1 待编辑 2 已编辑
  bool? hasVipRes; //是否存有Vip资源(客户端行为)
  bool? vip; //是否是Vip模版(后台管理系统对模板设置)
  String? goodsCode; //商品条码
  String? machineId; //模板适配打印机型id
  String? thumbnail; //标签缩略图
  String? localThumbnail; //本地标签缩略图地址
  String? createTime; //模板创建时间
  int? cableDirection; //线缆尾巴方向:0上,  1右,  2下,  3左,
  bool? isPrintHistory; //是否是打印记录
  String? commodityCategoryId; //商品分类id
  double? cableLength; // 线缆纸张 尾巴长度
  ///todo 待定字段
  String? hardwareSeriesName; //打印机系列名称
  int? multipleBackIndex; //多背景当前选择的背景数组index
  String? localBackground; //本地图片路径
  String? extra;
  bool? arrowInBoard;
  bool? isLabel;
  String? layoutSchema;
  String? supportedEditors;
  String? inputAreas;


  /// 最近使用新增字段
  int? totalPage;
  int? currentPage;
  bool? commodityTemplate;
  bool? bindExcel;


  ///有网情况下 是否进入过画板

  NiimBotTemplateCanvasDataModel({
    // this.id,
    this.templateId,
    // this.excelTaskData,
    this.platformCode,
    this.userId,
    this.labelId,
    this.sourceId,
    this.originTemplateId,
    this.localType,
    this.margin,
    this.machineName,
    this.paperType,
    this.backgroundImage,
    this.templateVersion,
    this.elements,
    this.values,
    this.industryId,
    this.templateClass,
    this.canvasRotate,
    this.dataSource,
    this.dataSourceModifies,
    this.dataSourceBindInfo,
    this.names,
    this.hardwareSeriesId,
    this.name,
    this.consumableType,
    this.sparedCode,
    this.barcode,
    this.virtualBarCode,
    this.width,
    this.height,
    this.labelNames,
    this.materialModelSn,
    this.folderId,
    this.isDelete,
    this.paperColor,
    this.amazonCodeWuhan,
    this.amazonCodeBeijing,
    this.commodityInfo,
    this.barcodeCategoryMap,
    this.updateTime,
    this.templateType,
    this.rotate,
    this.isCable,
    this.isEdited,
    this.hasVipRes,
    this.vip,
    this.goodsCode,
    this.machineId,
    this.thumbnail,
    this.localThumbnail,
    this.createTime,
    this.cableDirection,
    this.isPrintHistory,
    this.commodityCategoryId,
    this.cableLength,
    this.hardwareSeriesName,
    this.multipleBackIndex,
    this.localBackground,
    this.extra,
    this.arrowInBoard,
    this.isLabel,
    this.layoutSchema,
    this.supportedEditors,
    this.inputAreas,
    this.commodityTemplate,
    this.totalPage,
    this.currentPage,
  });

  NiimBotTemplateCanvasDataModel.fromJson(Map<String, dynamic> json)
      : templateId = json['templateId'],
        // excelTaskData = json['excelTaskData'],
        platformCode = json['platformCode'],
        userId = json['userId'],
        labelId = json['labelId'],
        sourceId = json['sourceId'],
        originTemplateId = json['originTemplateId'],
        localType = json['localType'],
        margin = json['margin'],
        machineName = json['machineName'],
        paperType = json['paperType'],
        backgroundImage = json['backgroundImage'],
        templateVersion = json['templateVersion'],
        elements = json['elements'],
        values = json['values'],
        industryId = json['industryId'],
        templateClass = json['templateClass'],
        canvasRotate = json['canvasRotate'],
        dataSource = json['dataSource'],
        dataSourceModifies = json['dataSourceModifies'],
        dataSourceBindInfo = json['dataSourceBindInfo'],
        names = json['names'],
        hardwareSeriesId = json['hardwareSeriesId'],
        name = json['name'],
        consumableType = json['consumableType'],
        sparedCode = json['sparedCode'],
        virtualBarCode = json['virtualBarCode'],
        barcode = json['barcode'],
        width = json['width'],
        height = json['height'],
        labelNames = json['labelNames'],
        materialModelSn = json['materialModelSn'],
        folderId = json['folderId'],
        isDelete = json['isDelete'],
        paperColor = json['paperColor'],
        amazonCodeWuhan = json['amazonCodeWuhan'],
        amazonCodeBeijing = json['amazonCodeBeijing'],
        commodityInfo = json['commodityInfo'],
        barcodeCategoryMap = json['barcodeCategoryMap'],
        updateTime = json['updateTime'],
        templateType = json['templateType'],
        rotate = json['rotate'],
        isCable = json['isCable'],
        isEdited = json['isEdited'],
        hasVipRes = json['hasVipRes'],
        vip = json['vip'],
        goodsCode = json['goodsCode'],
        machineId = json['machineId'],
        thumbnail = json['thumbnail'],
        localThumbnail = json['localThumbnail'],
        createTime = json['createTime'],
        cableDirection = json['cableDirection'],
        isPrintHistory = json['isPrintHistory'],
        commodityCategoryId = json['commodityCategoryId'],
        cableLength = json['cableLength'],
        hardwareSeriesName = json['hardwareSeriesName'],
        multipleBackIndex = json['multipleBackIndex'],
        localBackground = json['localBackground'],
        extra = json['extra'],
        arrowInBoard = json['arrowInBoard'],
        isLabel = json['isLabel'],
        layoutSchema = json['layoutSchema'],
        supportedEditors = json['supportedEditors'],
        inputAreas = json['inputAreas'],
        commodityTemplate = json['commodityTemplate'],
        totalPage = json['totalPage'],
        currentPage = json['currentPage'],
        bindExcel = json['bindExcel'];

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['templateId'] = templateId;
    // data['excelTaskData'] = excelTaskData;
    data['platformCode'] = platformCode;
    data['userId'] = userId;
    data['labelId'] = labelId;
    data['sourceId'] = sourceId;
    data['originTemplateId'] = originTemplateId;
    data['localType'] = localType;
    data['margin'] = margin;
    data['machineName'] = machineName;
    data['paperType'] = paperType;
    data['backgroundImage'] = backgroundImage;
    data['templateVersion'] = templateVersion;
    data['elements'] = elements;
    data['values'] = values;
    data['industryId'] = industryId;
    data['templateClass'] = templateClass;
    data['canvasRotate'] = canvasRotate;
    data['dataSource'] = dataSource;
    data['dataSourceModifies'] = dataSourceModifies;
    data['dataSourceBindInfo'] = dataSourceBindInfo;
    data['names'] = names;
    data['hardwareSeriesId'] = hardwareSeriesId;
    data['name'] = name;
    data['consumableType'] = consumableType;
    data['sparedCode'] = sparedCode;
    data['virtualBarCode'] = virtualBarCode;
    data['barcode'] = barcode;
    data['width'] = width;
    data['height'] = height;
    data['labelNames'] = labelNames;
    data['materialModelSn'] = materialModelSn;
    data['folderId'] = folderId;
    data['isDelete'] = isDelete;
    data['paperColor'] = paperColor;
    data['amazonCodeWuhan'] = amazonCodeWuhan;
    data['amazonCodeBeijing'] = amazonCodeBeijing;
    data['commodityInfo'] = commodityInfo;
    data['barcodeCategoryMap'] = barcodeCategoryMap;
    data['updateTime'] = updateTime;
    data['templateType'] = templateType;
    data['rotate'] = rotate;
    data['isCable'] = isCable;
    data['isEdited'] = isEdited;
    data['hasVipRes'] = hasVipRes;
    data['vip'] = vip;
    data['goodsCode'] = goodsCode;
    data['machineId'] = machineId;
    data['thumbnail'] = thumbnail;
    data['localThumbnail'] = localThumbnail;
    data['createTime'] = createTime;
    data['cableDirection'] = cableDirection;
    data['isPrintHistory'] = isPrintHistory;
    data['commodityCategoryId'] = commodityCategoryId;
    data['cableLength'] = cableLength;
    data['hardwareSeriesName'] = hardwareSeriesName;
    data['multipleBackIndex'] = multipleBackIndex;
    data['localBackground'] = localBackground;
    data['extra'] = extra;
    data['arrowInBoard'] = arrowInBoard;
    data['isLabel'] = isLabel;
    data['layoutSchema'] = layoutSchema;
    data['supportedEditors'] = supportedEditors;
    data['inputAreas'] = inputAreas;
    data['commodityTemplate'] = commodityTemplate;
    data['totalPage'] = totalPage;
    data['currentPage'] = currentPage;
    data['bindExcel'] = bindExcel;

    return data;
  }

  int backPage(String? dataSourceBindInfo) {
    if (dataSourceBindInfo == null || dataSourceBindInfo.isEmpty) {
      return 0;
    }
    if (jsonDecode(dataSourceBindInfo)['page'] == null) {
      return 0;
    }
    return int.parse(jsonDecode(dataSourceBindInfo)['page'].toString());
  }

  int backTotal(String? dataSourceBindInfo) {
    if (dataSourceBindInfo == null || dataSourceBindInfo.isEmpty) {
      return 0;
    }
    if (jsonDecode(dataSourceBindInfo)['total'] == null) {
      return 0;
    }
    return int.parse(jsonDecode(dataSourceBindInfo)['total'].toString());
  }

  int fastHash(String string) {
    var hash = 0xcbf29ce484222325;
    var i = 0;
    while (i < string.length) {
      final codeUnit = string.codeUnitAt(i++);
      hash ^= codeUnit >> 8;
      hash *= 0x100000001b3;
      hash ^= codeUnit & 0xFF;
      hash *= 0x100000001b3;
    }
    return hash;
  }
}
