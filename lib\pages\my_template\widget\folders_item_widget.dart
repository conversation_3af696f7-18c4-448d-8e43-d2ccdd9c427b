import 'package:flutter/material.dart';
import 'package:text/application.dart';
import 'package:text/pages/my_template/abstract/getx_controller_abstract.dart';
import 'package:text/pages/my_template/model/folder_model.dart';
import 'package:text/pages/my_template/my_template_state.dart';
import 'package:text/pages/my_template/page/public_template/controller/public_template_logic.dart';
import 'package:text/utils/DebounceUtil.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/widget/custom_popup_menu.dart';

class FoldersItemWidget extends StatefulWidget {
  int folderIndex;
  int index;
  FolderModel model;
  bool isShowMore;
  GetxControllerAbstract logic;
  TemplateOperateState operateType;

  FoldersItemWidget(
    this.logic,
    this.folderIndex,
    this.index,
    this.model,
    this.isShowMore, {
    Key? key,
    this.operateType = TemplateOperateState.normal,
  }) : super(key: key);

  @override
  _FoldersItemWidgetState createState() => _FoldersItemWidgetState();
}

class _FoldersItemWidgetState extends State<FoldersItemWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        DebounceUtil.debounceMillisecond(()=> _operateTypeClick(), milliseconds: 500);
        // _operateTypeClick();
      },
      child: FutureBuilder(
        future: precacheImage(AssetImage(_itemImageUrl()), context),
        builder: (BuildContext context, AsyncSnapshot<void> snapshot) {
          return Container(
            color: ThemeColor.background,
            child: Container(
                decoration: BoxDecoration(
                  color: widget.folderIndex == widget.index ? ThemeColor.background : ThemeColor.listBackground,
                  borderRadius: BorderRadiusDirectional.only(
                    topEnd: widget.index == (widget.folderIndex + 1) ? Radius.circular(20.0) : Radius.circular(0),
                    bottomEnd: widget.index == (widget.folderIndex - 1) && !widget.model.isAdd!
                        ? Radius.circular(20.0)
                        : Radius.circular(0),
                  ),
                ),
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(
                          0,
                          widget.model.isAdd! ? 12 : 6,
                          0,
                          widget.model.type == 1 || widget.model.type == 2 || widget.model.shared != null
                              ? 0
                              : widget.model.isAdd!
                                  ? 6
                                  : 0),
                      child: widget.model.isAdd!
                          ? SvgIcon(
                              'assets/images/my_template/create_folder.svg',
                            )
                          : _itemWidget(),
                    ),
                    Container(
                        width: 130,
                        alignment: AlignmentDirectional.center,
                        padding: widget.model.isDefault!
                            ? const EdgeInsetsDirectional.fromSTEB(5, 0, 5, 5)
                            : const EdgeInsetsDirectional.fromSTEB(5, 0, 5, 0),
                        child: Text(
                          widget.model.name!,
                          maxLines: 2,
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w600, color: ThemeColor.title),
                        )),
                    widget.model.isAdd!
                        ? Container(
                            height: 10,
                          )
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(child: Container()),
                              widget.model.isDefault! || widget.isShowMore
                                  ? Container(
                                      height: 10,
                                    )
                                  : CustomPopupMenuButton(
                                      itemBuilder: (BuildContext context) {
                                        if (widget.operateType == TemplateOperateState.shareMe) {
                                          return [
                                            _PopupMenuItem('assets/images/my_template/exit_share.svg',
                                                intlanguage('app100001319', '退出共享'), FolderEventStatus.exitShare,
                                                isFirst: true,
                                                separate: true,
                                                subName: intlanguage('app100001376', '由“\$”共享',
                                                    param: [widget.model.ownerProfile!.displayName.toString()])),
                                          ];
                                        } else if (widget.operateType == TemplateOperateState.shareOut) {
                                          return [
                                            _PopupMenuItem(
                                              'assets/images/my_template/exit_share.svg',
                                              intlanguage('app100001320', '取消共享'),
                                              FolderEventStatus.cancelShare,
                                              isFirst: true,
                                              separate: true,
                                            ),
                                          ];
                                        } else {
                                          return [
                                            widget.model.shared != null
                                                ? _PopupMenuItem('assets/images/my_template/exit_share.svg',
                                                    intlanguage('app100001320', '取消共享'), FolderEventStatus.cancelShare,
                                                    isFirst: true)
                                                : _PopupMenuItem('assets/images/my_template/folder_share.svg',
                                                    intlanguage('app100001351', '设为共享'), FolderEventStatus.share,
                                                    isFirst: true,
                                                    showVipState: true,
                                                    subName: intlanguage('app100001352', '暂不支持含商品库的模板')),
                                            _PopupMenuItem('assets/images/my_template/re_name.svg',
                                                intlanguage('app01321', '重命名'), FolderEventStatus.reName,
                                                isFirst: false),
                                            _PopupMenuItem('assets/images/my_template/delete.svg',
                                                intlanguage('app00063', '删除'), FolderEventStatus.delete,
                                                isDelete: true)
                                          ];
                                        }
                                      },
                                      child: Container(
                                        padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 10, 6),
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(12),
                                          // color: ThemeColor.listBackground,
                                        ),
                                        child: SvgIcon(
                                          'assets/images/my_template/more.svg',
                                        ),
                                      ),
                                      offset: Offset(Directionality.of(context) == TextDirection.ltr ? 0 : 10, 30),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      elevation: 10,
                                      onSelected: (value) {
                                        widget.logic.folderEvent(
                                            context, value as FolderEventStatus, widget.model!, widget.index);
                                      },
                                      onOpened: () {
                                        widget.logic.popupOnOpen(widget.index, widget.model.name, widget.operateType);
                                      },
                                    )
                            ],
                          ),
                    !widget.model.isAdd!
                        ? Padding(
                            padding: EdgeInsetsDirectional.only(top: widget.model.isAdd! ? 10 : 0),
                            child: Divider(
                              height: 0.5,
                              color: widget.index == (widget.folderIndex - 1) || widget.index == widget.folderIndex
                                  ? ThemeColor.background
                                  : ThemeColor.COLOR_D9D9D9,
                            ),
                          )
                        : Padding(
                            padding: EdgeInsetsDirectional.only(top: widget.model.isAdd! ? 10 : 0),
                            child: Divider(height: 0.5, color: ThemeColor.COLOR_D9D9D9),
                          )
                  ],
                )),
          );
        },
      ),
    );
  }

  _operateTypeClick() {
    switch (widget.operateType) {
      case TemplateOperateState.normal:
        if (widget.model.isAdd!) {
          widget.logic.folderEvent(context, FolderEventStatus.create, widget.model, widget.index);
        } else {
          var isRefresh = true;
          if (widget.index == widget.logic.state.folderIndex) {
            isRefresh = false;
          }
          widget.logic.folderselect(widget.index, widget.model.name);
          if (isRefresh) {
            widget.logic.onRefreshTemplate();
          }
        }
        return;
      case TemplateOperateState.batchPrint:
        if (widget.model.isAdd!) {
          widget.logic.folderEvent(context, FolderEventStatus.create, widget.model, widget.index);
        } else {
          var isRefresh = true;
          if (widget.index == widget.logic.state.folderIndex) {
            isRefresh = false;
          }
          widget.logic.folderselect(widget.index, widget.model.name);
          if (isRefresh) {
            widget.logic.onRefreshTemplate();
          }
        }
        return;
      case TemplateOperateState.shareOut:
        var isRefresh = true;
        var logic = widget.logic as PublicTemplateLogic;
        if (widget.index == logic.publicTemplateState.shareOutFolderIndex) {
          isRefresh = false;
        }
        logic.shareOutFolderselect(widget.index, widget.model.name);
        if (isRefresh) {
          logic.shareOutOnRefreshTemplate();
        }
        return;
      case TemplateOperateState.shareMe:
        var isRefresh = true;
        var logic = widget.logic as PublicTemplateLogic;
        if (widget.index == logic.publicTemplateState.shareMeFolderIndex) {
          isRefresh = false;
        }
        logic.shareMeFolderselect(widget.index, widget.model.name);
        if (isRefresh) {
          logic.shareMeOnRefreshTemplate();
        }
        return;
      default:
    }
  }

  CustomPopupMenuItem _PopupMenuItem(String path, String name, var status,
      {bool isDelete = false,
      bool isFirst = false,
      bool showVipState = false,
      String subName = "",
      bool separate = false}) {
    return CustomPopupMenuItem(
      height: 5,
      padding: EdgeInsets.zero,
      child: Container(
        padding: EdgeInsetsDirectional.symmetric(vertical: 0, horizontal: 0),
        child: Column(
          children: [
            isFirst
                ? Container()
                : Container(
                    width: double.infinity,
                    child: Divider(
                      height: 1,
                      thickness: isDelete ? 4 : 0.5,
                      color: ThemeColor.listBackground,
                    ),
                  ),
            Container(
                padding: isDelete || isFirst
                    ? isFirst
                        ? EdgeInsetsDirectional.fromSTEB(10, 4, 5, separate ? 4 : 14)
                        : EdgeInsetsDirectional.fromSTEB(10, 14, 5, 5)
                    : EdgeInsetsDirectional.fromSTEB(10, 14, 15, 14),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        SvgIcon(
                          path,
                          fit: BoxFit.fill,
                          width: 22,
                          height: 21,
                        ),
                        SizedBox(
                          width: 6,
                        ),
                        Text(
                          name,
                          style: TextStyle(
                            color: isDelete ? ThemeColor.brand : ThemeColor.title,
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        SizedBox(
                          width: 8,
                        ),
                        showVipState
                            ? Image.asset(
                                "assets/images/my_template/vip_state.png",
                                width: 21,
                                height: 21,
                              )
                            : Container(),
                      ],
                    ),
                    subName.isNotEmpty
                        ? Row(
                            children: [
                              SizedBox(
                                width: 27,
                              ),
                              Container(
                                  width: 120,
                                  padding: EdgeInsetsDirectional.only(end: 1),
                                  child: Text(
                                    subName,
                                    maxLines: 5,
                                    style: TextStyle(
                                      overflow: TextOverflow.ellipsis,
                                      color: ThemeColor.subtitle,
                                      fontSize: 13,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ))
                            ],
                          )
                        : Container(),
                  ],
                )),
          ],
        ),
      ),
      value: status,
    );
  }

  _itemWidget() {
    if (widget.model.type == 1 || widget.model.type == 2 || widget.model.shared != null) {
      if (widget.model.type == 2 || Application.user!.isVip || widget.index < 12) {
        return Image.asset(
          widget.folderIndex == widget.index
              ? "assets/images/my_template/share_select_folder.png"
              : "assets/images/my_template/share_normal_folder.png",
          width: 58,
        );
      } else {
        return Image.asset(
          widget.folderIndex == widget.index
              ? "assets/images/my_template/vip_share_selectl_folder.png"
              : "assets/images/my_template/vip_share_normal_folder.png",
          width: 58,
        );
      }
    } else {
      if (Application.user == null || Application.user!.isVip || widget.index < 12) {
        return Image.asset(
          widget.folderIndex == widget.index
              ? "assets/images/my_template/select_folder.png"
              : "assets/images/my_template/normal_folder.png",
          width: 58,
        );
      } else {
        return Image.asset(
          widget.folderIndex == widget.index
              ? "assets/images/my_template/vip_select_folder.png"
              : "assets/images/my_template/vip_normal_folder.png",
          width: 58,
        );
      }
    }
  }

  _itemImageUrl() {
    if (widget.model.type == 1 || widget.model.type == 2 || widget.model.shared != null) {
      if (widget.model.type == 2 || Application.user!.isVip || widget.index < 12) {
        return widget.folderIndex == widget.index
            ? "assets/images/my_template/share_select_folder.png"
            : "assets/images/my_template/share_normal_folder.png";
      } else {
        return widget.folderIndex == widget.index
            ? "assets/images/my_template/vip_share_selectl_folder.png"
            : "assets/images/my_template/vip_share_normal_folder.png";
      }
    } else {
      if (Application.user == null || Application.user!.isVip || widget.index < 12) {
        return widget.folderIndex == widget.index
            ? "assets/images/my_template/select_folder.png"
            : "assets/images/my_template/normal_folder.png";
      } else {
        return widget.folderIndex == widget.index
            ? "assets/images/my_template/vip_select_folder.png"
            : "assets/images/my_template/vip_normal_folder.png";
      }
    }
  }
}
