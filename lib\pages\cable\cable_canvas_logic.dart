import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/config/template_config.dart';
import 'package:flutter_canvas_plugins_interface/utils/display_util.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:nety/models/niimbot_printer.dart';
import 'package:nety/nety.dart';
import 'package:niimbot_print_setting_plugin/extensions/niimbot_printer.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/model/template_data.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/model/template_external_data.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/tinykit_canvas_box_frame.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:text/application.dart';
import 'package:text/macro/constant.dart';
import 'package:text/pages/cable/widget/print_setting_widget.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/hardware_manager.dart';
import 'package:text/utils/print_setting_channel.dart';
import 'package:text/vipTrial/trial_activity.dart';
import 'package:text/vipTrial/vip_trial_manager.dart';

import '../../business/user/user_login_helper.dart';
import '../../utils/toast_util.dart';
import '../industry_template/select_label/label_selector_page.dart';
import 'cable_canvas_state.dart';

class CableCanvasLogic extends GetxController {
  final CableCanvasState state = CableCanvasState();

  init(BuildContext context, String? templateJsonData) {
    super.onInit();
    if (Application.user != null) {
      state.frameInfo.isVip = Application.user!.cableVip;
    } else {
      state.frameInfo.isVip = false;
    }

    _setLabelData(templateJsonData);
    // 加载标签数据，并设置标签监听器
    _getLabelData();
    _labelListener(context);
    _getDensityInfo();
    _getDeviceOffset();
  }

  ///设置线缆标签纸信息
  _setLabelData(String? templateJsonData) async {
    var cableJson = "";
    if (templateJsonData != "") {
      Map<String, dynamic> jsonMap = json.decode(templateJsonData!);
      // 如果解析出的JSON数据包含layoutSchema，使用该数据并保存
      if (jsonMap["layoutSchema"] != null && jsonMap["layoutSchema"] != "") {
        cableJson = templateJsonData;
        Application.sp.setString(ConstantKey.lateCableLabel, templateJsonData!);
      } else {
        var data = await ToNativeMethodChannel().getCableDetailFromNative(
            jsonMap['profile']?['extrain']?['barcodeCategoryMap'] ?? {},
            jsonMap['profile']?['extrain']?['labelId'] ?? "");
        Map<String, dynamic> map = json.decode(data!);
        if (map["layoutSchema"] != null && map["layoutSchema"] != "") {
          cableJson = data;
          Application.sp.setString(ConstantKey.lateCableLabel, data!);
        } else {
          // 如果不包含layoutSchema，尝试从本地存储中加载先前的数据
          cableJson = Application.sp.getString(ConstantKey.lateCableLabel) ?? "";
        }
      }
    } else {
      // 如果没有提供模板JSON数据，同样尝试从本地存储中加载先前的数据
      cableJson = Application.sp.getString(ConstantKey.lateCableLabel) ?? "";
    }
    // 根据是否存在有效的JSON数据，准备模板数据
    Map<String, dynamic> jsonMap;
    if (cableJson.isEmpty) {
      jsonMap = state.defaultJson;
    } else {
      jsonMap = json.decode(cableJson);
    }
    state.templateData = jsonMap;

    num _width = jsonMap['width'] ?? 0;
    num _height = jsonMap['height'] ?? 0;

    state.frameInfo.canvasHeight = _height.toDouble();
    state.frameInfo.canvasWidth = _width.toDouble();
    // 如果模板数据中包含layoutSchema，进一步处理并保存该布局schema的JSON数据
    if (jsonMap["layoutSchema"] != null && jsonMap["layoutSchema"] != "") {
      Map cableMap = json.decode(jsonMap["layoutSchema"]);
      state.jsonData = json.encode(cableMap);
    }
    if (state.canvasContext != null) {
      state.canvasContext?.initLegoJsonData(state.jsonData);
    }
    update();
  }

  /// 获取标签纸信息
  _getLabelData() {
    ToNativeMethodChannel().getPrinterLabelData().then((value) {
      if (value is Map) {
        _labelStatusChange(value);
      }
    });
  }

  _labelListener(BuildContext context) {
    // 注册事件监听器，用于处理特定的事件。
    NiimbotEventBus.getDefault().register(this, (data) {
      if (data is Map && data["action"] == "setFlutterlabelData") {
        // 检查标签数据是否存在且不为空。
        if (null != data['labelData'] && data['labelData'].isNotEmpty) {
          // 将接收到的标签数据转换为本地Map对象，以便处理。
          Map<String, dynamic> labelData = Map<String, dynamic>.from(data['labelData']);

          _replaceRfidTemplate(labelData);
          _getLabelData();
        } else {
          _labelStatusChange(data['labelData'] ?? {});
        }
      } else if (data is Map && data.containsKey("userInfo")) {
        if (Application.user != null) {
          state.frameInfo.isVip = Application.user!.cableVip;
        } else {
          state.frameInfo.isVip = false;
        }
        update();
      } else if (data is Map && data["action"] == "printerConnectState") {
        _getDensityInfo();
        _getDeviceOffset();
        Future.delayed(Duration(milliseconds: 2000), () {
          _getLabelData();
        });
      }
    });
  }

  _getDensityInfo() async {
    var connectModel = await HardWareManager.instance().connectHardWareModel();
    if (connectModel != null) {
      var value = await ToNativeMethodChannel()
          .getDensity(consumableCode: state.templateData?["consumableType"].toString() ?? "");
      state.density = (value - 1).toDouble();
    } else {
      state.density = 0;
    }
  }

  _getDeviceOffset() async {
    NiimbotPrinter? connectedPrint = NiimbotPrintSDK().store.connectedPrinter;
    if (connectedPrint == null) {
      return;
    }
    PrintSettingChannel channel = PrintSettingChannel();
    String deviceName = channel.getCurrentPrinterMachineId(
        (connectedPrint.code ?? 0).toString(), connectedPrint.name ?? "", connectedPrint.sn ?? "");
    SharedPreferences sp = await SharedPreferences.getInstance();
    String offsetStr = sp.getString("print_offset_$deviceName") ?? "";
    if (offsetStr.isEmpty) {
      return;
    }
    Map<String, dynamic> offsetInfo = jsonDecode(offsetStr);
    if (offsetInfo.isNotEmpty) {
      state.ofsetX = offsetInfo["offsetX"] ?? 0;
      state.ofsetY = offsetInfo["offsetY"] ?? 0;
    }
  }

  saveDeviceOffset() {
    NiimbotPrinter? connectedPrint = NiimbotPrintSDK().store.connectedPrinter;
    if (connectedPrint == null) {
      return;
    }
    PrintSettingChannel channel = PrintSettingChannel();
    String deviceName = channel.getCurrentPrinterMachineId(
        (connectedPrint.code ?? 0).toString(), connectedPrint.name ?? "", connectedPrint.sn ?? "");
    SharedPreferences.getInstance().then((sp) {
      sp.setString("print_offset_$deviceName", jsonEncode({"offsetY": state.ofsetX, "offsetX": state.ofsetY}));
    });
  }

  unLabelListener() {
    NiimbotEventBus.getDefault().unregister(this);
  }

  void _replaceRfidTemplate(Map<String, dynamic> data) {
    state.frameInfo.reInitTextValues.clear();
    var canvasJson = state.canvasContext?.getLabelData();
    var canvasData = json.decode(canvasJson!);
    var templateData = TemplateData.fromJson(canvasData);
    templateData.elements.forEach((element) {
      if (element.type == "text" && element.value != null) {
        state.frameInfo.reInitTextValues.add(element.value!);
      }
    });

    if ((data["supportedEditors"] as List).first == "cable") {
      _setLabelData(json.encode(data));
    }
  }

  //刷新标签纸顶部提示信息
  _labelStatusChange(Map value) async {
    // 根据识别结果调整线缆标签显示状态和提示信息
    Map? hardWareModel = await ToNativeMethodChannel().getPrinterConnectState();
    state.isShowCableLabel = false;
    if (value.isNotEmpty) {
      // 当识别结果存在时，检查是否指定了布局方案
      // 当前已连接机器且存在标签纸
      if (value["layoutSchema"] != null &&
          value["layoutSchema"] != "" &&
          (value["supportedEditors"] as List).first == "cable") {
        // 如果指定了布局方案，清空线缆标签提示信息
        state.cableLabelToast = "";
      } else {
        // 如果未指定布局方案，显示不适用线缆场景的提示信息
        state.cableLabelToast = intlanguage('app100001622', '识别到的标签纸不适用线缆场景，请更换');
      }
    } else if (hardWareModel?['connected'] == 1) {
      if (hardWareModel?["rfidStatus"] == 1) {
        state.cableLabelToast = intlanguage('app100001621', '未识别到标签纸');
      } else {
        // 当识别结果为空时，恢复线缆标签显示状态并显示未识别到标签纸的提示信息
        state.isShowCableLabel = true;
        state.cableLabelToast = "";
      }
    } else {
      state.isShowCableLabel = true;
      state.cableLabelToast = "";
    }
    // 更新界面状态
    update();
  }

  /// 更换标签纸，打开选择标签纸
  changeLabel(BuildContext context) {
    state.hasShowLabelDialog = true;

    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      builder: (BuildContext context) {
        return LabelSelectorPage(
          isNeedPop: false,
          isFromCanvas: true,
          isCable: true,
          sence: LabelSelecSence.cableCanvas,
          onChanged: (value) {
            try {
              state.hasShowLabelDialog = false;
              // Log.e("_changeLabel: $value");
              if (value.isEmpty) {
              } else {
                EasyLoading.show(dismissOnTap: false);
                ToNativeMethodChannel()
                    .getCableDetailFromNative(value['profile']?['extrain']?['barcodeCategoryMap'] ?? {},
                        value['profile']?['extrain']?['labelId'] ?? "")
                    .then((data) {
                  EasyLoading.dismiss();
                  Navigator.of(context).pop();
                  Map<String, dynamic> jsonMap = json.decode(data!);
                  _replaceRfidTemplate(jsonMap);
                });
              }
            } catch (e) {}
          },
        );
      },
    );
  }

  /// 获取设备的简单信息字符串，包括尺寸、缆线长度、纸张类型和标签名称。
  String getSimpleInfo() {
    // 构建基本信息字符串，包括设备尺寸
    String info = intlanguage('app100000966', '自定义') +
        "-${state.templateData?["width"]?.toDouble()}X${state.templateData?["height"]?.toDouble()}";
    // 如果存在缆线长度且大于0，则添加到信息字符串中
    if (state.templateData?["cableLength"] > 0) {
      info += "+${state.templateData?["cableLength"]}";
    }
    // 获取纸张类型名称，并添加到信息字符串中
    var paperType = state.templateData?["paperType"].toString();
    String paperTypeName = intlanguage('${HardWareManager.instance().getPaperTypeNameByCode(paperType!)}', '走纸类型');
    info += "-$paperTypeName";
    // 初始化标签名称变量
    String labelName = "";
    String labelEnName = "";
    String labelCNName = '';
    // 解析并获取标签名称列表
    var labelNames =
        (state.templateData?["labelNames"] as List<dynamic>).map((e) => LabelNameInfo.fromJson(e)).toList();
    // 遍历标签名称列表，根据当前语言代码选择合适的标签名称
    for (LabelNameInfo labelNameInfo in labelNames) {
      if (Application.currentAppLanguageType == labelNameInfo.languageCode) {
        labelName = labelNameInfo.name;
      }
      if (labelNameInfo.languageCode == "en") {
        labelEnName = labelNameInfo.name;
      }
      if (labelNameInfo.languageCode == "zh-cn") {
        labelCNName = labelNameInfo.name;
      }
    }
    // 根据当前语言选择合适的标签名称，如果为空，则选择英文或中文名称
    // 当前语言> EN > CN
    labelName = labelName.isEmpty ? (labelEnName.isEmpty ? labelCNName : labelEnName) : labelName;
    // 如果存在标签ID，则使用标签名称作为信息字符串
    var labelId = state.templateData?['profile']?['extrain']?['labelId'];
    if (labelId.isNotEmpty == true) {
      info = labelName;
    }
    // 返回构建好的信息字符串
    return info;
  }

  syncRfidReplaceTag(bool checked, {bool init = false}) {
    ToNativeMethodChannel().syncRfidReplaceTag(checked).then((value) {});
  }

  Future<void> onPrintData(
      String? canvasJson, int? currentPage, TemplateConfig? config, BuildContext context, Widget? extraWidget) async {
/*    Map? hardWareModel = await ToNativeMethodChannel().getPrinterConnectState();
    if (hardWareModel?['connected'] == 0) {
      showNimmbotDialog(context,
          title: intlanguage('app00033', '请先连接打印机即可打印'),
          cancelDes: intlanguage('app00030', '取消'),
          confirmDes: intlanguage('app00034', '连接'),
          useRootNavigator: false,
          cancelAction: () {}, confirmAction: () {
        CustomNavigation.gotoNextPage('DeviceConnectTagBasePage', {'uniappId': 'CABLE'});
      });
    } else {
      _toPrint(canvasJson, currentPage, config, context);
    }*/
    _toPrint(canvasJson, currentPage, config, context, extraWidget);
  }

  _toPrint(String? canvasJson, int? currentPage, TemplateConfig? config, BuildContext context, Widget? extraWidget) {
    // 解析canvasJson参数中的JSON数据。
    var canvasData = json.decode(canvasJson!);
    var printData;

    // 如果当前状态中存在模板数据，则更新这些数据以包含画布数据。
    if (state.templateData != null) {
      var data = state.templateData;
      // 更新模板数据中的元素信息为画布数据。
      data!["elements"] = canvasData["elements"];
      printData = data;
    } else {
      // 如果没有模板数据，则直接使用画布数据。
      printData = canvasData;
    }

    // 将处理后的打印数据编码为JSON字符串。
    var templateJson = json.encode(printData);

    // 标记当前框架为打印状态，并更新状态。
    // state.frameInfo.isPrint = true;
    update();

    Function vipCheck = () {
      // VipTrialManager().trialActivity(
      //     context: context,
      //     trialActivityType: TrialActivityType.cablePrint,
      //     vipType: "cable",
      //     trailActivity: () {
      // 解析canvasJson参数中的JSON数据。
      var canvasData = json.decode(canvasJson!);
      var printData;

      // 如果当前状态中存在模板数据，则更新这些数据以包含画布数据。
      if (state.templateData != null) {
        var data = state.templateData;
        // 更新模板数据中的元素信息为画布数据。
        data!["elements"] = canvasData["elements"];
        printData = data;
      } else {
        // 如果没有模板数据，则直接使用画布数据。
        printData = canvasData;
      }
      // 2024/11/8 Ice_Liu 线缆默认hints文字颜色是灰色，如果存在未编辑的element，颜色值会被判断为双色
      // 需要矫正element颜色都为*********
      _modifyElementColor(printData);

      // 将处理后的打印数据编码为JSON字符串。
      var templateJson = json.encode(printData);

      // 标记当前框架为打印状态，并更新状态。
      // state.frameInfo.isPrint = true;
      update();

      if (printData['elements'] != null && printData['elements'] is List) {
        for (var element in printData['elements']) {
          if (element['value'] == null || element['value'] == '') {
            element['value'] = ' ';
          }
        }
      }

      PrintSettingManager(context, {
        'niimbotTemplate': jsonEncode({'niimbotTemplate': printData}),
        'printScene': 'mini_app',
        'uniAppInfo': {'uniAppId': 'CABLE'}
      }, (String stringCode, String defaultStr, {List<String>? param}) {
        return intlanguage(stringCode, defaultStr, param: param);
      }).setPageType(PrintPageType.low).show(context, PrintSettingChannel());
      // },
      // cancelAction: () {},
      // dismissCallback: () {},
      // extraWidget: extraWidget);
    };
    // if (Platform.isAndroid) {
    loginCheck(context, true, () {
      vipCheck.call();
    });
    // } else {
    // vipCheck.call();
    // }
  }

  void _modifyElementColor(Map<String, dynamic> data) {
    // 检查 data 中是否包含 "elements" 键，并且它的值是一个 List
    if (data.containsKey('elements') && data['elements'] is List) {
      List elements = data['elements'];

      for (var element in elements) {
        if (element is Map<String, dynamic> && element.containsKey('elementColor')) {
          // 将 elementColor 属性设置为 [255, 0, 0, 0]
          element['elementColor'] = [255, 0, 0, 0];
        }
      }
    }
  }

  loginCheck(BuildContext context, bool isPrint, Function function) {
    (Connectivity().customCheckConnectivity()).then((value) {
      if (Application.user == null) {
        if (value == ConnectivityResult.none || value == ConnectivityResult.bluetooth) {
          showToast(msg: intlanguage('app100000625', '当前网络状态异常'));
          return;
        }
        String title = intlanguage('app00210', '当前未登录，请先登录！');
        String cancelDes = intlanguage('app00030', '取消');
        String confirmDes = intlanguage('app01191', '立即登录');
        UserLoginHelper().confirmLogin(context, title, cancelDes, confirmDes, loginSucceed: () {
          // Future.delayed(const Duration(milliseconds: 300), () {
          function.call();
          // });
        });
      } else {
        // if (!Application.user!.cableVip) {

        // }
        function.call();
      }
    });
  }

  toVipCheck(BuildContext context) {
    if (Platform.isIOS) {
      CustomNavigation.gotoNextPage('ToVipPage', {"vipType": "cable"}).then((value) {
        if (value is Map && value['result'] is int && value['result'] > 0) {
          // 继续功能
        }
      });
    } else {
      loginCheck(context, false, () {
        CustomNavigation.gotoNextPage('ToVipPage', {"vipType": "cable"}).then((value) {
          if (value is Map && value['result'] is int && value['result'] > 0) {
            // 继续功能
          }
        });
      });
    }
  }

  static bool canSwitchToNormalCanvas = true;

  onCanvasInitData(String? canvasJson) {
    (Connectivity().customCheckConnectivity()).then((value) {
      if (value == ConnectivityResult.none || value == ConnectivityResult.bluetooth) {
        showToast(msg: intlanguage('app01139', '网络异常'));
        return;
      }
      if (!canSwitchToNormalCanvas) return;
      canSwitchToNormalCanvas = false;
      Future.delayed(Duration(seconds: 2), () {
        canSwitchToNormalCanvas = true;
      });
      var canvasData = json.decode(canvasJson!);
      Map normalCanvasData;
      if (state.templateData != null) {
        var data = state.templateData;
        // 更新模板数据中的元素信息为画布数据。
        data!["elements"] = canvasData["elements"];
        normalCanvasData = data;
        normalCanvasData.remove("layoutSchema");
      } else {
        // 如果没有模板数据，则直接使用画布数据。
        normalCanvasData = canvasData;
      }
      for (var element in normalCanvasData["elements"]) {
        element["fontStyle"] = [];
      }
      // 解析JSON字符串为Map对象，以便后续处理和传递给画板页面。
      Map<String, dynamic> args = {'templateData': normalCanvasData, 'isCustomLabel': false};
      ToNativeMethodChannel().setSPData({"cableEntry": "normalCanvas"});
      DisplayUtil.setCanvasMarginBounds(36);
      // 使用自定义导航方法跳转到名为'CanvasPage'的页面，并传递解析后的画板数据作为参数。
      if (Platform.isIOS) {
        CustomNavigation.gotoNextPage('CanvasPage', args).then((value) {});
      } else {
        CustomNavigation.toPageReplacement('CanvasPage', args);
      }
    });
  }

  void onGeneralCanvas() {}

  void onPrintSettings(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      builder: (BuildContext context) {
        return PrintSettingWidget(
          consumableCode: state.templateData?["consumableType"].toString(),
          density: state.density,
          ofsetX: state.ofsetX,
          ofsetY: state.ofsetY,
          onChange: (value) {
            state.density = value;
          },
          onOffsetChange: (x, y) {
            state.ofsetX = x;
            state.ofsetY = y;
            saveDeviceOffset();
          },
        );
      },
    );
  }

  Future<TemplateData> onSavedData(String? canvasJson, TemplateConfig? config, BuildContext context,
      {String? templateId}) {
    Completer<TemplateData> completer = Completer<TemplateData>();
    EasyLoading.showSuccess('点击了保持数据');
    Future.delayed(Duration(milliseconds: 1000), () {
      completer.complete(null);
    });
    return completer.future;
  }

  Future<TemplateData?> onConfig(String? canvasJson, TemplateConfig? config, BuildContext context) {
    return Navigator.of(context).push(new MaterialPageRoute(builder: (context) {
      return Container();
    }));
  }

  Future<TemplateData> onLabelData(CanvasBoxFrameState buildContext) {
    Completer<TemplateData> completer = Completer<TemplateData>();
    state.canvasContext = buildContext;
    return completer.future;
  }

  void onPrintPress() async {}

  void onPrintResult(String value) {}
}
