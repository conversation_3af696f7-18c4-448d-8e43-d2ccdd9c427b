//
//  JCGoodsTemplateViewController.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2021/9/17.
//  Copyright © 2021 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCGoodsTemplateViewController.h"
#import "JCGoodsTemplateSearchController.h"
#import "JCTMDataBindGoodsInfoManager.h"
#import "JCSegmentCollectionCell.h"
#import "JCTemplateImageManager.h"
#import "JCCollectionViewCell.h"
#import "JCGoodsTemplateView.h"
#import "JCGoodTemplateTypeMode.h"
#import "JCTemplateList.h"
#import "YLButton.h"
#import "JCGrayManager.h"

@interface JCGoodsTemplateViewController ()<UICollectionViewDelegate,UICollectionViewDataSource>

@property(nonatomic,strong) JCGoodsTemplateView *mainView;
@property(nonatomic,strong) UICollectionView *templateListView;
@property(nonatomic,strong) NSArray *arrTypeDecrList;
@property(nonatomic,strong) JCGoodTemplateTypeMode *selectTypeModel;
@property(nonatomic,strong) NSString *searchKeywords;
@property(nonatomic,strong) JCGoodDetailInfo *firstGoodInfoModel;
@property(nonatomic,strong) MBProgressHUD *progressHUD;
@end

@implementation JCGoodsTemplateViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.lsl_prefersNavigationBarHidden = YES;
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(templatechange:) name:TEMPLATE_CHANGED object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(synchTemplateFinish:) name:JCNOTICATION_SYNC_FINISH object:nil];
    // Do any additional setup after loading the view.
}

- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    JC_TrackWithparms(@"view",@"014",(@{}));
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
}

- (void)initUI{
    [super initUI];
    XYWeakSelf
    JCGoodsTemplateView *mainView = [[JCGoodsTemplateView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT) searchType:NO];
    [mainView setNodataOperateBlock:^(NSNumber *opeateBunber){
        if(opeateBunber.integerValue == 1){
            [weakSelf toCreateNewGoodsTemplate];
        }else if(opeateBunber.integerValue == 5){
            [weakSelf.navigationController popViewControllerAnimated:YES];
        }
    }];
    [mainView setToSearchBlock:^{
        JCGoodsTemplateSearchController *vc = [[JCGoodsTemplateSearchController alloc] init];
        [weakSelf.navigationController pushViewController:vc animated:NO];
    }];
    [mainView setHeaderOperateBlock:^(NSNumber *operateNumber) {
        if(operateNumber.integerValue == 1){
            [weakSelf.navigationController popViewControllerAnimated:YES];
        }else{
            [weakSelf toCreateNewGoodsTemplate];
        }
    }];
    mainView.templateCollView.delegate = self;
    mainView.templateCollView.dataSource = self;
    mainView.segmentCollectionView.delegate = self;
    mainView.segmentCollectionView.dataSource = self;
    
    // 添加下拉刷新
    mainView.templateCollView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
        [weakSelf.templateListView.mj_footer resetNoMoreData];
        [weakSelf loadGoodsTemplate:NO templateTypeMode:weakSelf.selectTypeModel];
    }];
    
    // 添加上拉加载更多
    mainView.templateCollView.mj_footer = [MJRefreshAutoNormalFooter footerWithRefreshingBlock:^{
        [weakSelf loadMoreGoodsTemplateWithTypeMode:weakSelf.selectTypeModel];
    }];
    mainView.templateCollView.mj_footer.hidden = YES;
    
    [self.view addSubview:mainView];
    self.templateListView = mainView.templateCollView;
    self.mainView = mainView;
}

- (void)initNet{
    [super initNet];
    [self checkHasGoodstemplate];
}

- (void)checkHasGoodstemplate{
    if(NETWORK_STATE_ERROR){
        // 网络不可用时，直接显示空状态
        [self showNoDateView:1];
    }else{
        // 直接加载模板数据
        [self loadGoodsTemplate:YES templateTypeMode:self.selectTypeModel];
        
        [@{} java_getWithModelType:nil Path:J_store_get_user_goodsTemplate_count hud:@"" Success:^(__kindof YTKBaseRequest *request, NSNumber *hasGoodsTemplate) {
            if(!hasGoodsTemplate.boolValue){
                // 服务器返回没有模板，显示空状态
                [self showNoDateView:1];
            }
        } failure:^(NSString *msg, id model) {
            // 请求失败，显示空状态
            [self showNoDateView:1];
        }];
    }
}

- (NSArray *)arrTypeDecrList{
    if(!_arrTypeDecrList){
        JCGoodTemplateTypeMode *lastTemplateModel = [[JCGoodTemplateTypeMode alloc] init];
        lastTemplateModel.goodTemplateId = @"1";
        lastTemplateModel.isSelected = @"1";
        lastTemplateModel.pageNumber = @"1";
        lastTemplateModel.name = XY_LANGUAGE_TITLE_NAMED(@"", @"最新");
        lastTemplateModel.templateArr = @[].mutableArray;
        
        JCGoodTemplateTypeMode *historyTemplateModel = [[JCGoodTemplateTypeMode alloc] init];
        historyTemplateModel.goodTemplateId = @"2";
        historyTemplateModel.isSelected = @"0";
        historyTemplateModel.pageNumber = @"1";
        historyTemplateModel.name = XY_LANGUAGE_TITLE_NAMED(@"", @"历史模板");
        historyTemplateModel.templateArr = @[].mutableArray;
        _arrTypeDecrList = @[lastTemplateModel,historyTemplateModel];
    }
    return _arrTypeDecrList;
}

- (JCGoodTemplateTypeMode *)selectTypeModel{
    JCGoodTemplateTypeMode *selectTypeModel = nil;
    for (JCGoodTemplateTypeMode *templateTypeModel in self.arrTypeDecrList) {
        if(templateTypeModel.isSelected.integerValue == 1){
            selectTypeModel = templateTypeModel;
            break;
        }
    }
    _selectTypeModel = selectTypeModel;
    return _selectTypeModel;
}

- (void)loadGoodsTemplate:(BOOL)isShowLoading templateTypeMode:(JCGoodTemplateTypeMode *)templateTypeMode{
    templateTypeMode.pageNumber = @"1";
    [self.templateListView.mj_footer resetNoMoreData];
    [self getTemplateList:isShowLoading templateTypeMode:templateTypeMode];
}

- (void)loadMoreGoodsTemplateWithTypeMode:(JCGoodTemplateTypeMode *)templateTypeMode{
    templateTypeMode.pageNumber = StringFromInt(templateTypeMode.pageNumber.integerValue + 1);
    [self getTemplateList:NO templateTypeMode:templateTypeMode];
}

- (void)getTemplateList:(BOOL)isShowLoading templateTypeMode:(JCGoodTemplateTypeMode *)templateTypeMode {
    XYWeakSelf
    
    // 如果网络不可用
    if(NETWORK_STATE_ERROR) {
        // 直接显示空数据并结束刷新状态
        templateTypeMode.templateArr = @[].mutableCopy;
        [self.templateListView.mj_header endRefreshing];
        [self.templateListView.mj_footer endRefreshing];
        [self showNoDateView:1]; // 直接显示无网络的空数据状态
        return;
    }
    
    // 准备参数调用Flutter方法
    NSNumber *page = @(templateTypeMode.pageNumber.intValue);
    NSNumber *limit = @10;
    NSNumber *commodityTemplate = @(templateTypeMode.goodTemplateId.intValue);
    NSString *searchKey = self.searchKeywords ?: @"";
    
    // 显示加载指示器
    MBProgressHUD *hud = nil;
    if (isShowLoading) {
        hud = [MBProgressHUD showHUDAddedTo:hudWindow animated:YES contentColor:hudContentColor backColor:hudBackColor];
    }
    
    // 使用JCTemplateDBManager的nativeGetMyGoodTemplateList方法获取模板列表
    [JCTemplateDBManager nativeGetMyGoodTemplateList:page 
                                              limit:limit 
                                  commodityTemplate:commodityTemplate 
                                          searchKey:searchKey 
                                            success:^(NSArray<JCTemplateData *> *templates, NSDictionary *response) {
        [weakSelf.templateListView.mj_header endRefreshing];
        [weakSelf.templateListView.mj_footer endRefreshing];
        
        if (hud) {
            [hud hideAnimated:YES];
        }
        
        // 更新模板类型模式中的数据
        if (templateTypeMode.pageNumber.integerValue == 1) {
            templateTypeMode.templateArr = [templates mutableCopy];
        } else {
            NSMutableArray *currentTemplates = [templateTypeMode.templateArr mutableCopy];
            [currentTemplates addObjectsFromArray:templates];
            templateTypeMode.templateArr = currentTemplates;
        }
        
        // 处理加载更多状态
        if (templates.count < limit.integerValue) {
            [weakSelf.templateListView.mj_footer endRefreshingWithNoMoreData];
        } else {
            weakSelf.templateListView.mj_footer.hidden = NO;
        }
        
        // 刷新UI
        weakSelf.mainView.creatButton.hidden = YES;
        [weakSelf refreshTemplateData];
    } failed:^(NSString *errorMsg) {
        [weakSelf.templateListView.mj_header endRefreshing];
        [weakSelf.templateListView.mj_footer endRefreshing];
        
        if (hud) {
            [hud hideAnimated:YES];
        }
        
        // 处理错误情况，显示空数据
        if (templateTypeMode.pageNumber.integerValue == 1) {
            templateTypeMode.templateArr = @[].mutableCopy;
        }
        [weakSelf refreshTemplateData];
        
        NSLog(@"获取模板列表失败: %@", errorMsg);
    }];
}

- (void)refreshTemplateData{
    [self.templateListView reloadData];
    // 移除本地数据库检查，直接根据模板数据来决定显示状态
    if(self.selectTypeModel.templateArr.count == 0){
        [self showNoDateView:1]; // 显示空数据视图
    }else{
        [self showNoDateView:0]; // 显示正常数据视图
    }
}

- (void)showNoDateView:(NSInteger)showType{
    [self.mainView showStoreNoDataView:showType];
    if(showType == 0){
        self.mainView.creatButton.hidden = YES;
    }
}

- (void)toCreateGoodsTemplate{
    self.firstGoodInfoModel = nil;
}

- (void)templatechange:(NSNotification *)notification{
    [self showNoDateView:0];
    // 收到模板变更通知后重新加载数据
    [self loadGoodsTemplate:NO templateTypeMode:self.selectTypeModel];
}

- (void)synchTemplateFinish:(NSNotification *)notification{
    NSDictionary *oldNewIdDic = notification.object;
    NSString *oldId = [oldNewIdDic objectForKey:@"oldXYId"];
    NSString *newId = [oldNewIdDic objectForKey:@"newXYId"];
    if(oldId.length != 0 && newId.length != 0){
        // 收到同步完成通知后重新加载数据
        [self loadGoodsTemplate:NO templateTypeMode:self.selectTypeModel];
    }
}

#pragma mark ListCollectionViewDelegate

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView{
    return 1;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    NSInteger itemCount = 0;
    if(collectionView.tag == 1){
        itemCount = self.arrTypeDecrList.count;
    }else{
        itemCount = self.selectTypeModel.templateArr.count;
    }
    return itemCount;
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    if(collectionView.tag == 1){
        JCGoodTemplateTypeMode *templateTypeModel = [self.arrTypeDecrList safeObjectAtIndex:indexPath.row];
        JCSegmentCollectionCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"JCSegmentCollectionCell" forIndexPath:indexPath];
        [cell refreshWithTitle:templateTypeModel.name isSelected:templateTypeModel.isSelected.integerValue == 1];
        return cell;
    }else{
        JCTemplateData *goodsTemplate = [self.selectTypeModel.templateArr safeObjectAtIndex:indexPath.row];
        JCCollectionViewCell *cell = (JCCollectionViewCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"JCCollectionViewCell" forIndexPath:indexPath];
        cell.isShowImport = NO;
        goodsTemplate.profile.extrain.templateType = @"2";
        [cell setPrintButtonShow:NO];
        [cell setTemplateData:goodsTemplate];
        [cell setPrintBlock:^{
          [JCTemplateFunctionHelper toPrint:goodsTemplate];
        }];
        return cell;
    }
}

- (CGSize)collectionView:(UICollectionView*)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath*)indexPath{
    if(collectionView.tag == 1){
        JCGoodTemplateTypeMode *templateTypeModel = [self.arrTypeDecrList safeObjectAtIndex:indexPath.row];
        NSString *title = templateTypeModel.name;
        UIFont *font = (templateTypeModel.isSelected.integerValue == 1)?MY_FONT_Bold(16):MY_FONT_Bold(14);
        CGSize titleSize = [title jk_sizeWithFont:font constrainedToHeight:1000];
        return CGSizeMake(titleSize.width, 53);
    }else{
        float itemWidth = (SCREEN_WIDTH - 30 - 15)/2;
        float itemHeight = itemWidth * 159 / 164;
        CGSize itemSize = CGSizeMake(itemWidth, itemHeight);
        return itemSize;
    }
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    if(collectionView.tag == 1){
        [self showNoDateView:0];
        JCGoodTemplateTypeMode *selectTypeModel = [self.arrTypeDecrList safeObjectAtIndex:indexPath.row];
        if(selectTypeModel.goodTemplateId.integerValue == self.selectTypeModel.goodTemplateId.integerValue) return;
        for (JCGoodTemplateTypeMode *templateTypeModel in self.arrTypeDecrList) {
            if(selectTypeModel.goodTemplateId.integerValue == templateTypeModel.goodTemplateId.integerValue){
                templateTypeModel.isSelected = @"1";
            }else{
                templateTypeModel.isSelected = @"0";
            }
        }
        [collectionView reloadData];
        [self loadGoodsTemplate:NO templateTypeMode:self.selectTypeModel];
        self.mainView.creatButton.hidden = YES;
    }else{
        JCTemplateData *selectTemplate = [self.selectTypeModel.templateArr safeObjectAtIndex:indexPath.row];
        [self preareToEditWithTemplateModel:selectTemplate];
        if([self.selectTypeModel.goodTemplateId isEqualToString:@"1"]){
            JC_TrackWithparms(@"click",@"014_042_054",(@{@"temp_id":selectTemplate.idStr,@"pos":@(indexPath.row + 1),@"tab_name":@"最新"}));
        }else{
            JC_TrackWithparms(@"click",@"014_042_055",(@{@"temp_id":selectTemplate.idStr,@"pos":@(indexPath.row + 1),@"tab_name":@"历史模板"}));
        }
    }
}

- (void)preareToEditWithTemplateModel:(JCTemplateData *)model{
    // 记录历史记录
    saveRecentlyUsedRecord(model.idStr);
    XYWeakSelf
//    BOOL is_ableEdit_Template = (model.localType == JCLocalType_OffLineUpdate || model.localType == JCLocalType_OffLineCreate || model.localType == JCLocalType_Sync || model.localType == JCLocalType_Default);
//    BOOL is_ableEdit_Template = [model checkTemplateDetailByBackImage:YES containFont:NO];
//    JCTemplateData *tempModel = model;
//    if(is_ableEdit_Template){
//        TemplateSource source = TemplateSource_Mine;
//        if([JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:tempModel.templateVersion] == false){
//            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000343", @"您的软件版本过低，请升级")];
//            return;
//        }
//        [JCTemplateFunctionHelper toEdit:tempModel];
//    }else{
//        if(NETWORK_STATE_ERROR){
//            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
//        }else{
//          
//        }
//    }
    [JCTemplateFunctionHelper toEdit:model];
}

- (void)jumpToTemplateEditControllerWith:(JCTemplateData *)model{
    // 重新加载数据
    [self loadGoodsTemplate:NO templateTypeMode:self.selectTypeModel];
    
    if([JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:model.templateVersion] == false){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000343", @"您的软件版本过低，请升级")];
        return;
    }
    [JCTemplateFunctionHelper toEdit:model];
}

- (void)toCreateNewGoodsTemplate{
    self.firstGoodInfoModel = nil;
    if(NETWORK_STATE_ERROR){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"", @"网络异常")];
        return;
    }
    XYWeakSelf
    [self java_postWithValues:@{@"limit":@"1"} ModelType:[JCGoodDetailInfo class] Path:J_goods_user_goods_list hud:@"" Success:^(__kindof YTKBaseRequest *request,  JCGoodDetailInfo *requestModel) {
        NSMutableArray *requestArr = requestModel.list.mutableCopy;
        if(requestArr.count > 0){
            JCGoodDetailInfo *goodInfoModel = requestArr.firstObject;
            weakSelf.firstGoodInfoModel = goodInfoModel;
            [weakSelf goodLibraryPrintWithCode];
        }else{
            JCGoodDetailInfo *goodInfoModel = [[JCGoodDetailInfo alloc] init];
            goodInfoModel.goodId = [NSString stringWithFormat:@"%@%@",[XYTool getNowTimeTimestamp],StringFromInt((NSInteger)rand()% 10000)];
            goodInfoModel.barcode = @"";
            weakSelf.firstGoodInfoModel = goodInfoModel;
            [weakSelf goodLibraryPrintWithCode];
        }
        
    } failure:^(NSString *msg, id model) {
        JCGoodDetailInfo *goodInfoModel = [[JCGoodDetailInfo alloc] init];
        goodInfoModel.goodId = [NSString stringWithFormat:@"%@%@",[XYTool getNowTimeTimestamp],StringFromInt((NSInteger)rand()% 10000)];
        goodInfoModel.barcode = @"";
        weakSelf.firstGoodInfoModel = goodInfoModel;
        [weakSelf goodLibraryPrintWithCode];
    }];
}

- (void)goodLibraryPrintWithCode{
    XYWeakSelf
    JCTemplateData *rfidTemplateModel = [JCPrintManager sharedInstance].rfidTemplateData;
    if(self.progressHUD && !self.progressHUD.hidden){
        [self.progressHUD hideAnimated:NO];
        self.progressHUD = nil;
    }
    self.progressHUD = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
    self.progressHUD.label.text = XY_LANGUAGE_TITLE_NAMED(@"app01115", @"数据加载中");
    NSDictionary *requestDic = @{@"barcode":UN_NIL(@"")};//
    if(rfidTemplateModel != nil){
        NSString *labelId = STR_IS_NIL(rfidTemplateModel.idStr)?@"-1":rfidTemplateModel.idStr;
        NSString *labelBarcode = STR_IS_NIL(rfidTemplateModel.barcode)?@"-1":rfidTemplateModel.barcode;
        requestDic = @{@"barcode":UN_NIL(@""),@"labelId":labelId,@"labelBarcode":labelBarcode};
    }
    [weakSelf java_postWithValues:requestDic ModelType:[JCGoodResultModel class] Path:J_goods_new_scan hud:nil Success:^(__kindof YTKBaseRequest *request, JCGoodResultModel *requestModel) {
        requestModel.goods_info = weakSelf.firstGoodInfoModel;
        requestModel.template_info.labelId = requestModel.template_info.profile.extrain.labelId;
        [weakSelf getGoodsResultSuccessParms:requestModel];
    } failure:^(NSString *msg, id model) {
        [JCRecordTool recordWithAction:click_scan_print_callBack_faild];
        [self.progressHUD hideAnimated:NO];
    }];
}

- (void)getGoodsResultSuccessParms:(JCGoodResultModel *)requestModel{
    JCTemplateData *rfidTemplateModel = [JCPrintManager sharedInstance].rfidTemplateData;
    if(!STR_IS_NIL(rfidTemplateModel.idStr)){
        if(!STR_IS_NIL(requestModel.template_info.idStr)){
            [self getTemplateDetailWithGoodsResult:requestModel hasRFID:YES];
        }else{
            [self jumpToTemplateEditVCWithRFIDModel:rfidTemplateModel goodInfo:requestModel.goods_info];
            [self.progressHUD hideAnimated:NO];
        }
    }else{
        if(!STR_IS_NIL(requestModel.template_info.idStr)){
            [self getTemplateDetailWithGoodsResult:requestModel hasRFID:NO];
        }else{
            [self.progressHUD hideAnimated:NO];
        }
    }
}


- (void)getTemplateDetailWithGoodsResult:(JCGoodResultModel *)goodResult hasRFID:(BOOL)hasRFID{
    [JCTemplateImageManager downLoadImagesForData:goodResult.template_info options:DownAll complete:^(JCTemplateData *resultData) {
        // Update the template data with the result
        if (resultData) {
            goodResult.template_info = resultData;
        }
        [self.progressHUD hideAnimated:NO];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self jumpToTemplateEditVCWith:goodResult.template_info goodInfo:goodResult.goods_info hasRFID:hasRFID];
        });
    }];
}

- (void)jumpToTemplateEditVCWith:(JCTemplateData *)requestModel goodInfo:(JCGoodDetailInfo *)goodDetailInfo hasRFID:(BOOL)hasRFID{
    NSString *sourceOfIndustryTemplateId = @"";
    //如果是行业商品模板 记录
    BOOL isUserGoodsTemp = NO;
    if(STR_IS_NIL(requestModel.profile.extrain.userId)){
        sourceOfIndustryTemplateId = requestModel.idStr;
    }else{
        isUserGoodsTemp = YES;
        requestModel.sourceOfIndustryTemplateId = @"";
    }
    TemplateSource source = TemplateSource_New;//
    requestModel.profile.extrain.userId = @"";
    JCTemplateData *model = [JCTMDataBindGoodsInfoManager templateDataWith:requestModel goodInfo:goodDetailInfo];
    requestModel.profile.extrain.userId = @"";
    if(isUserGoodsTemp){
        requestModel.sourceOfIndustryTemplateId = @"";
    }else{
        requestModel.sourceOfIndustryTemplateId = sourceOfIndustryTemplateId;
    }
    model.profile.extrain.goodsCode = goodDetailInfo.barcode;
    model.goodsList = (NSArray<JCGoodDetailInfo> *)@[goodDetailInfo];
    if(STR_IS_NIL(requestModel.profile.extrain.userId)){
        model.name = STR_IS_NIL(goodDetailInfo.name)?XY_LANGUAGE_TITLE_NAMED(@"app01052", @"品名"):goodDetailInfo.name;
    }
    source = TemplateSource_New;
    if([JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:model.templateVersion] == false){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000343", @"您的软件版本过低，请升级")];
        return;
    }
    [JCTemplateFunctionHelper toEdit:model];
}

- (void)jumpToTemplateEditVCWithRFIDModel:(JCTemplateData *)m goodInfo:(JCGoodDetailInfo *)goodDetailInfo{
    JCTemplateData *model = [JCTMDataBindGoodsInfoManager createGoodsTemplateWith:m goodInfo:goodDetailInfo];
    model.profile.extrain.commodityCategoryId = goodDetailInfo.categoryId;
    model.profile.extrain.industryId = goodDetailInfo.industryId;
    model.profile.extrain.goodsCode = goodDetailInfo.barcode;
    model.goodsList = (NSArray<JCGoodDetailInfo> *)@[goodDetailInfo];
    // 画板灰度
    if([JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:model.templateVersion] == false){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000343", @"您的软件版本过低，请升级")];
        return;
    }
    [JCTemplateFunctionHelper toEdit:model];
}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
