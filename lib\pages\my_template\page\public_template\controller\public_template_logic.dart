import 'dart:async';
import 'dart:convert';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:niimbot_template/models/template_data.dart';

// import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:popover/popover.dart';
import 'package:text/business/user/user_login_helper.dart';
import 'package:text/macro/constant.dart';
import 'package:text/network/entity/user.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_state.dart';
import 'package:text/pages/my_template/abstract/getx_controller_abstract.dart';
import 'package:text/pages/my_template/model/folder_model.dart';
import 'package:text/pages/my_template/my_template_state.dart';
import 'package:text/pages/my_template/page/personal_template/controller/personal_template_logic.dart';
import 'package:text/pages/my_template/page/public_template/controller/public_template_state.dart';
import 'package:text/pages/my_template/widget/template_move_list_widget.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/template/constant/template_local_type.dart';
import 'package:text/template/template_manager.dart';
import 'package:text/template/util/template_transform_utils.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/utils/toast_util.dart';
import 'package:text/widget/FToast.dart';

import '../../../../../application.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart' as CanvasTemplate;

class PublicTemplateLogic extends GetxControllerAbstract {
  final PublicTemplateState publicTemplateState = PublicTemplateState();

  @override
  void onReady() {
    onEventListener();
  }

  /**
   * 切换标签页状态。
   * 该方法用于在标签页切换时更新界面状态，并根据当前标签页索引执行相应的刷新操作和发送跟踪信息。
   * 无参数。
   * 无返回值。
   */
  changeTabState({isUserMsgChange = false, folderSelectIndex = 0}) {
    try {
      // 当前标签页索引正在变化时执行
      if (publicTemplateState.tabController?.indexIsChanging == true || isUserMsgChange) {
        // 设置模板页面状态为加载中
        state.templatePageState = IndustryHomeState.loading;
        // 更新当前标签页索引
        publicTemplateState.tabIndex = publicTemplateState.tabController?.index ?? 0;
        // 根据标签页索引更新操作类型并执行相应的刷新和跟踪方法
        if (publicTemplateState.tabIndex == 0) {
          // 当前为“分享给我”的标签页
          state.operateType = TemplateOperateState.shareMe;
          getShareMeFolders(folderSelectIndex: folderSelectIndex);
          // 发送跟踪信息到原生端
          ToNativeMethodChannel().sendTrackingToNative({
            "track": "view",
            "posCode": "122",
          });
        } else {
          showSmartFolderToast();
          // 当前为“分享出去”的标签页
          state.operateType = TemplateOperateState.shareOut;
          Future.delayed(Duration(milliseconds: 300), () {
            getShareOutFolders();
          });

          // 发送跟踪信息到原生端
          ToNativeMethodChannel().sendTrackingToNative({
            "track": "view",
            "posCode": "123",
          });
        }
        // 刷新标签页
        refreshTab();
      }
    } catch (e, s) {
      debugPrint('异常信息:\n $e');
    }
  }

  onEventListener() {
    NiimbotEventBus.getDefault().register(this, (data) async {
      if (data is Map && data.containsKey("myTemplateRefresh")) {
        Timer(Duration(seconds: 1), () {
          if (publicTemplateState.shareOutFolderList.isEmpty) return;
          getTemplateList(
              folderId: publicTemplateState.shareOutFolderList[publicTemplateState.shareOutFolderIndex].id!.toInt());
          if (state.templateScrollController.hasClients) {
            state.templateScrollController.animateTo(0, duration: Duration(milliseconds: 100), curve: Curves.easeInOut);
          }
          if (eventBusFunction != null) {
            eventBusFunction!();
          }
        });
      } else if (data is Map && data.containsKey("userInfo")) {
        int folderSelectIndex = publicTemplateState.shareMeFolderIndex;
        changeTabState(isUserMsgChange: true, folderSelectIndex: folderSelectIndex);
      }
    });
  }

  /**
   * 邀请成员共享资源
   * @param context BuildContext，当前的上下文环境，用于页面跳转等操作
   * 该函数没有返回值
   */
  inviteMemberShare(BuildContext context) {
    // 检查当前用户是否是VIP
    if (!Application.user!.isVip) {
      // 如果不是VIP，则显示VIP无效的引导页面，并提供跳转至成员管理页面的选项
      vipInvalidGuide(context, VIPUseScence.folderShare, () {
        CustomNavigation.gotoNextPage('shareMemberManager', {}, withContainer: false).then((value) {});
      });
    } else {
      // 如果是VIP，则直接跳转至成员管理页面
      CustomNavigation.gotoNextPage('shareMemberManager', {}, withContainer: false).then((value) {});
    }
  }

  /**
   * 获取共享出去的文件夹列表。
   * 此函数不接受参数，也不直接返回值，但会更新相关的状态对象。
   * 其主要逻辑包括：
   * 1. 清空当前的模板列表；
   * 2. 通过folderManager获取所有共享的文件夹列表；
   * 3. 根据获取的列表更新公共模板状态，包括清空当前的共享出去的文件夹列表、添加新获取的列表；
   * 4. 根据列表是否为空，设置相应的页面状态，并获取特定文件夹下的模板列表；
   * 5. 最后刷新文件夹列表。
   */
  getShareOutFolders({isSendEvent = false}) {
    publicTemplateState.shareOutFolderList.clear(); // 清空共享出去的文件夹列表
    state.myTemplateList.clear();
    folderManager.getShareAll(isSendEvent: isSendEvent, publicTemplateState, (list) {
      publicTemplateState.shareOutFolderList.clear();
      // 获取所有共享的文件夹列表
      if (list.isNotEmpty) {
        if (Application.user!.isVip) {
          publicTemplateState.shareOutFolderList.addAll(list); // 添加新获取的共享文件夹列表
          // 列表不为空时的处理
          publicTemplateState.shareOutTemplatePageState = IndustryHomeState.showContainer; // 设置页面状态为显示容器
          if (publicTemplateState.shareOutFolderList.length < publicTemplateState.shareOutFolderIndex + 1) {
            publicTemplateState.shareOutFolderIndex = publicTemplateState.shareOutFolderList.length - 1;
          }
          if (publicTemplateState.shareOutFolderIndex < 0) {
            publicTemplateState.shareOutFolderIndex = 0;
          }
          refreshFolderList();
          getTemplateList(
            // 获取特定文件夹下的模板列表
            folderId: publicTemplateState.shareOutFolderList[publicTemplateState.shareOutFolderIndex].id!.toInt(),
          );
        } else {
          publicTemplateState.shareOutFolderList.addAll(list); // 添加新获取的共享文件夹列表
          publicTemplateState.shareOutTemplatePageState = IndustryHomeState.vipExpire;
          ToNativeMethodChannel().sendTrackingToNative({
            "track": "show",
            "posCode": "123_278",
          });
          refreshFolderList();
        }
      } else {
        // 列表为空时的处理
        publicTemplateState.shareOutTemplatePageState = IndustryHomeState.empty; // 设置页面状态为空
      }
      refreshFolderList(); // 刷新文件夹列表
    });
  }

  /**
   * 获取共享给我的文件夹列表。
   * 该函数不接受参数，并且没有返回值。
   * 它首先清除当前的文件列表，然后从folderManager获取共享文件列表。
   * 如果获取的列表不为空，则更新共享给我文件夹列表，并刷新界面状态。
   * 如果列表为空，则更新界面状态显示为空。
   */
  getShareMeFolders({isDelete = false, folderSelectIndex = 0}) {
    publicTemplateState.shareMeFolderPage = 1;
    publicTemplateState.shareMeFolderIndex = folderSelectIndex;
    state.myTemplateList.clear();
    state.templatePageState = IndustryHomeState.loading;
    refreshTemplateList();
    // 请求获取所有共享的文件夹
    folderManager.getShareAll(
      publicTemplateState,
      (list) {
        if (list.isNotEmpty && !isDelete) {
          publicTemplateState.shareMeFolderList.clear();
          // 如果获取的文件夹列表不为空，则更新共享给我文件夹列表
          publicTemplateState.shareMeFolderList.addAll(list);
          // 更新页面状态为显示容器
          publicTemplateState.shareMeFolderPageState = IndustryHomeState.showContainer;

          if (publicTemplateState.shareMeFolderIndex > publicTemplateState.shareMeFolderList.length - 1) {
            publicTemplateState.shareMeFolderIndex = publicTemplateState.shareMeFolderList.length - 1;
          }
          if (publicTemplateState.shareMeFolderIndex < 0) {
            publicTemplateState.shareMeFolderIndex = 0;
          }
          if (state.folderScrollController.hasClients) {
            state.folderScrollController.animateTo(0, duration: Duration(milliseconds: 100), curve: Curves.easeInOut);
          }
          refreshFolderList();

          // 根据新的列表索引获取模板列表
          getTemplateList(
            folderId: publicTemplateState.shareMeFolderList[publicTemplateState.shareMeFolderIndex].id!.toInt(),
            isShareOut: false,
            folderModel: publicTemplateState.shareMeFolderList[publicTemplateState.shareMeFolderIndex],
          );
        } else {
          // publicTemplateState.shareMeFolderList.clear();   //断网情况下清空文件夹列表会出问题
          // 如果列表为空，更新页面状态为空状态
          publicTemplateState.shareMeFolderPageState = IndustryHomeState.empty;
          refreshFolderList();
        }
        // 刷新文件夹列表界面
        publicTemplateState.shareMeFolderRefreshController.refreshCompleted();
        publicTemplateState.shareMeFolderRefreshController.resetNoData();
      },
    );
  }

  getOnLoadShareMeFolders() {
    publicTemplateState.shareMeFolderPage += 1;
    // 请求获取所有共享的文件夹
    folderManager.getShareAll(publicTemplateState, (list) {
      if (list.isNotEmpty) {
        // 如果获取的文件夹列表不为空，则更新共享给我文件夹列表
        publicTemplateState.shareMeFolderList.addAll(list);
        // 更新页面状态为显示容器
        publicTemplateState.shareMeFolderPageState = IndustryHomeState.showContainer;
        publicTemplateState.shareMeFolderRefreshController.loadComplete();
        refreshFolderList();
        // 根据新的列表索引获取模板列表
        // getTemplateList(
        //     folderId: publicTemplateState.shareMeFolderList[publicTemplateState.shareMeFolderIndex].id!.toInt(),
        //     isShareOut: false);
      } else {
        publicTemplateState.shareMeFolderPage--;
        publicTemplateState.shareMeFolderRefreshController.loadNoData();
        // 刷新文件夹列表界面
        refreshFolderList();
      }
    }, page: publicTemplateState.shareMeFolderPage);
  }

  /**
   * 当刷新模板列表时调用此方法，用于更新并获取共享出去的模板列表。
   * 此函数没有参数，也没有返回值。
   */
  shareOutOnRefreshTemplate() {
    // 设置模板页面状态为加载中
    state.templatePageState = IndustryHomeState.loading;
    // 刷新模板列表
    refreshTemplateList();
    // 如果共享文件夹列表不为空，则获取指定文件夹中的模板列表
    if (publicTemplateState.shareOutFolderList.isNotEmpty) {
      getTemplateList(
        folderId: publicTemplateState.shareOutFolderList[publicTemplateState.shareOutFolderIndex].id!.toInt(),
        isRefresh: true,
      );
    }
  }

  shareMeOnRefreshTemplate() {
    state.templatePageState = IndustryHomeState.loading;
    refreshTemplateList();
    if (publicTemplateState.shareMeFolderList.isNotEmpty) {
      refreshFolderList();
      getTemplateList(
        folderId: publicTemplateState.shareMeFolderList[publicTemplateState.shareMeFolderIndex].id!.toInt(),
        isRefresh: true,
        isShareOut: false,
        folderModel: publicTemplateState.shareMeFolderList[publicTemplateState.shareMeFolderIndex],
      );
    }
  }

  /**
   * 选择分享文件夹
   * @param index 文件夹的索引
   * @param name 文件夹的名称（可选）
   * 该方法用于选择一个文件夹进行分享操作。首先，它会向原生方法发送一个跟踪事件，报告用户点击了某个位置（用于数据分析）。
   * 然后更新当前的分享文件夹索引，并刷新文件夹列表。
   */
  shareOutFolderselect(int index, String? name) {
    // 发送跟踪信息到原生端，包括事件类型、位置代码和扩展信息（如文件夹名称）
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "007_015_023",
      'ext': {"folder_name": name}
    });
    // 更新公共模板状态中的分享文件夹索引
    publicTemplateState.shareOutFolderIndex = index;
    // 刷新文件夹列表
    refreshFolderList();
  }

  ///选中文件夹操作
  shareMeFolderselect(int index, String? name) {
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "007_015_023",
      'ext': {"folder_name": name}
    });
    publicTemplateState.shareMeFolderIndex = index;
    refreshFolderList();
  }

  /**
   * 加载模板页面并分享出去。
   * 此函数增加当前模板页面的计数，并获取指定文件夹中的模板列表。
   * 不接受任何参数，也不直接返回任何值，其效果通过更改状态和调用其他函数实现。
   */
  shareOutOnLoadTemplate() {
    // 增加模板页面计数
    state.templatePage += 1;
    // 获取模板列表，使用的是当前分享出去的文件夹列表中的指定文件夹ID
    getTemplateList(
      folderId: publicTemplateState.shareOutFolderList[publicTemplateState.shareOutFolderIndex].id!.toInt(),
      isRefresh: false,
    );
  }

  /**
   * 当刷新操作发生时，分享给我文件夹的刷新逻辑。
   * 该函数没有参数，也没有返回值。
   */
  shareMeFolderOnRefresh() {
    // 获取分享给我的文件夹列表
    getShareMeFolders();
  }

  shareMeOnLoadTemplate() {
    state.templatePage += 1;
    getTemplateList(
      folderId: publicTemplateState.shareMeFolderList[publicTemplateState.shareMeFolderIndex].id!.toInt(),
      isRefresh: false,
      isShareOut: false,
      folderModel: publicTemplateState.shareMeFolderList[publicTemplateState.shareMeFolderIndex],
    );
  }

  /**
   * 获取模板列表。
   *
   * @param folderId 文件夹ID，默认为0，表示获取所有模板。
   * @param isRefresh 是否刷新，默认为true，表示刷新列表。
   * @param onlyNative 是否仅获取原生模板，默认为false。
   * @param isShareOut 是否为分享出去的模板，默认为true。
   * @param folderModel 文件夹模型，可选参数。
   * @returns 无返回值。
   */
  getTemplateList(
      {int folderId = 0, bool isRefresh = true, onlyNative = false, bool isShareOut = true, FolderModel? folderModel}) {
    // 如果文件夹ID为0，则不执行任何操作
    if (folderId == 0) {
      return;
    }
    // 设置是否刷新的状态
    state.isRefresh = isRefresh;
    // 如果需要刷新，则重置页码，并根据是否分享出去，重置相应的刷新控制器状态
    if (state.isRefresh) {
      state.templatePage = 1;
      if (isShareOut) {
        publicTemplateState.shareOutRefreshController.resetNoData();
      } else {
        publicTemplateState.shareMeRefreshController.resetNoData();
      }
    }
    publicTemplateState.isChangeState = true;
    // 获取模板列表
    templateManager.getAll(
        state.templatePage,
        folderId: folderId,
        folderModel: folderModel,
        // 获取成功后的回调处理
        (
          List<TemplateData?> list,
        ) {
          num? id = -1;
          // 根据操作类型获取相应的文件夹ID
          if (state.operateType == TemplateOperateState.shareOut) {
            if (publicTemplateState.shareOutFolderList.isNotEmpty) {
              id = publicTemplateState.shareOutFolderList[publicTemplateState.shareOutFolderIndex].id;
            }
          } else {
            if (publicTemplateState.shareMeFolderList.isNotEmpty) {
              id = publicTemplateState.shareMeFolderList[publicTemplateState.shareMeFolderIndex].id;
            }
          }

          // 如果当前文件夹ID与请求的文件夹ID一致，进行数据处理
          if (id == folderId) {
            // 刷新时，清除列表并更新数据
            if (state.isRefresh) {
              state.myTemplateList.clear();
              state.myTemplateList.addAll(List<TemplateData>.from(list));
              // 如果列表非空，动画滚动到顶部
              // if (state.myTemplateList.isNotEmpty) {
              //   state.templateScrollController
              //       .animateTo(0, duration: Duration(milliseconds: 100), curve: Curves.easeInOut);
              // }
              // 根据是否分享出去，完成刷新操作
              if (isShareOut) {
                publicTemplateState.shareOutRefreshController.refreshCompleted();
              } else {
                publicTemplateState.shareMeRefreshController.refreshCompleted();
              }
            } else if (list.isEmpty) {
              // 无数据时，页码减1，并根据是否分享出去，加载无数据状态
              state.templatePage--;
              if (isShareOut) {
                publicTemplateState.shareOutRefreshController.loadNoData();
              } else {
                publicTemplateState.shareMeRefreshController.loadNoData();
              }
            } else {
              list.forEach((element) {
                state.myTemplateList.removeWhere((t) => t.id == element!.id);
              });
              state.myTemplateList.addAll(List<TemplateData>.from(list));
              state.myTemplateList.sort((a, b) {
                DateTime dateA = DateTime.parse(a.profile.extra.updateTime ?? "");
                DateTime dateB = DateTime.parse(b.profile.extra.updateTime ?? "");
                return dateB.compareTo(dateA);
              });
              // 根据是否分享出去，加载完成状态
              if (isShareOut) {
                publicTemplateState.shareOutRefreshController.loadComplete();
              } else {
                publicTemplateState.shareMeRefreshController.loadComplete();
              }
            }
            // list.forEach((element) {
            //   state.myTemplateList.removeWhere((t) => t!.id == element!.id);
            // });
            // state.myTemplateList.addAll(List<TemplateData>.from(list));
            // state.myTemplateList.sort((a, b) {
            //   DateTime dateA = DateTime.parse(a.profile.extrain.updateTime);
            //   DateTime dateB = DateTime.parse(b.profile.extrain.updateTime);
            //   return dateB.compareTo(dateA);
            // });
            // // 根据是否分享出去，加载完成状态
            // if (isShareOut) {
            //   publicTemplateState.shareOutRefreshController.loadComplete();
            // } else {
            //   publicTemplateState.shareMeRefreshController.loadComplete();
            // }
            // 根据列表是否为空，更新页面状态
            state.templatePageState =
                state.myTemplateList.isNotEmpty ? IndustryHomeState.showContainer : IndustryHomeState.empty;
            refreshAllSelectState();
            refreshTemplateList();
            refreshFolderList();
          } else {
            // 如果当前文件夹ID不一致，结束加载状态
            state.templateRefreshController.loadComplete();
          }
        },
        refresh: state.isRefresh,
        onlyNative: onlyNative,
        state: state.operateType,
        // 错误处理函数
        errorFuncation: (status) {
          if (status == 10403) {
            state.templatePageState = IndustryHomeState.vipExpire;
            refreshTemplateList();
          } else if (status == 11404) {
            shareMeFolderOnRefresh();
          }
        });
  }

  folderEvent(BuildContext context, FolderEventStatus event, FolderModel model, int index) {
    switch (event) {
      case FolderEventStatus.cancelShare:
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "click",
          "posCode": "123_277",
        });
        showCustomDialog(context, intlanguage('app100001364', '确定取消共享“\$”文件夹吗？', param: [model.name.toString()]),
            intlanguage('app100001365', '该文件夹的共享成员将不再能访问此文件夹。'),
            leftFunStr: intlanguage('app00030', '取消'),
            rightFunStr: intlanguage('app100001366', '停止共享'),
            rightTextColor: ThemeColor.brand, leftFunCall: () {
          return;
        }, rightFunCall: () async {
          await folderManager.cancelShare(model.id!.toInt().toString(), state.operateType, () {
            // showCustomDialog(context!, "", intlanguage('app100001368', '该文件夹已取消共享'),
            //     justSureButton: true,
            //     contentTextStyle: TextStyle(
            //       fontSize: 16,
            //       color: ThemeColor.mainTitle,
            //       fontWeight: FontWeight.w600,
            //     ),
            //     rightFunStr: intlanguage('app00707', '我知道了'),
            //     rightTextColor: ThemeColor.COLOR_161616,
            //     rightFunCall: () {});
            IconToast.show(context, intlanguage('app100001396', '已取消共享'));
            getShareOutFolders();
            Future.delayed(Duration(seconds: 1), () {
              Get.find<PersonalTemplateLogic>().getFolderList();
            });
          });
        });
        break;
      case FolderEventStatus.exitShare:
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "click",
          "posCode": "122_280",
        });
        showCustomDialog(
            context,
            intlanguage('app100001377', '确定退出由”\$“共享的全部文件夹吗？', param: [model.ownerProfile!.displayName.toString()]),
            intlanguage('app100001378', '退出共享后将不能访问'),
            leftFunStr: intlanguage('app00030', '取消'),
            rightFunStr: intlanguage('app100001319', '退出共享'),
            rightTextColor: ThemeColor.brand, leftFunCall: () {
          return;
        }, rightFunCall: () async {
          await folderManager.exitShare(model.ownerProfile!.id ?? "", state.operateType, () {
            IconToast.show(context, intlanguage('app100001379', '已退出共享！'));
            publicTemplateState.shareMeFolderList.clear();
            state.templatePageState = IndustryHomeState.empty;
            getShareMeFolders();
            folderManager.getDB().delete(type: 2);
          });
        });
        break;

      default:
    }
  }

  /**
   * 显示智能文件夹的提示信息。
   * 该函数没有参数。
   * 该函数没有返回值。
   */
  showSmartFolderToast() {
    // 延迟300毫秒后执行，以确保在合适的时机显示提示
    Future.delayed(Duration(milliseconds: 300), () {
      // 生成存储标志的批次名称，基于用户ID(Application.user!.id! is double)

      var batchName = ConstantKey.membermanagerToast + Application.user!.userId.toString();
      // 获取是否显示提示的设置值，若不存在则默认为true
      var isShow = Application.sp.getBool(batchName) ?? true;

      // 如果设置为显示，则更新设置为不显示并显示提示
      if (isShow) {
        Application.sp.setBool(batchName, false);
        // 调用设置智能文件夹向导的函数
        _smartfolderTypesettingGuide(publicTemplateState.shareFolderKey.currentContext!);
      }
    });
  }

  void _smartfolderTypesettingGuide(BuildContext context) {
    publicTemplateState.smartIsPop = true;
    showPopover(
        context: context,
        bodyBuilder: (context) {
          return Container(
            width: 250,
            padding: EdgeInsetsDirectional.fromSTEB(18, 13, 10, 12),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  intlanguage('app100001381', '你可以在这里邀请成员加入共享了。'),
                  style: TextStyle(color: ThemeColor.COLOR_161616, fontSize: 14, fontWeight: FontWeight.w400),
                ),
              ],
            ),
          );
        },
        shadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 55,
            spreadRadius: 0.1,
          ),
        ],
        radius: 12,
        direction: PopoverDirection.bottom,
        width: 250,
        arrowHeight: 10,
        arrowWidth: 18,
        contentDyOffset: 10,
        barrierColor: Colors.transparent,
        routeSettings: RouteSettings(name: "smartPopover"),
        onPop: () {
          publicTemplateState.smartIsPop = false;
        });
  }

  templateItemEventBefore(
      BuildContext context, TemplateEventStatus event, TemplateData model, TemplateOperateState operateType,
      {FolderModel? folderModel, List<TemplateData>? templates, Function(bool operateResult)? resultCallBack}) {
    templateItemEvent(context, event, model, operateType,
        folderModel: folderModel, templates: templates, resultCallBack: resultCallBack);
    String? folderName = "";
    if (operateType == TemplateOperateState.shareOut) {
      folderName = publicTemplateState.shareOutFolderList[publicTemplateState.shareOutFolderIndex].name;
    } else if (operateType == TemplateOperateState.shareMe) {
      folderName = publicTemplateState.shareMeFolderList[publicTemplateState.shareMeFolderIndex].name;
    }
    trackMoreOperationEvent(model.id ?? "", event, folderName);
  }

  templateItemEvent(
      BuildContext context, TemplateEventStatus event, TemplateData model, TemplateOperateState operateType,
      {FolderModel? folderModel, List<TemplateData>? templates, Function(bool operateResult)? resultCallBack}) async {
    switch (event) {
      case TemplateEventStatus.reName:
        showEditTextDialog(
          context,
          intlanguage('app01321', '重命名'),
          model.name,
          100,
          (value) {
            templateManager
              ..reName(model, value, () {
                ToNativeMethodChannel.nativeTemplateNeedRefresh();
                onRefreshTemplate();
                state.templateScrollController
                    .animateTo(0, duration: Duration(milliseconds: 100), curve: Curves.easeInOut);
                resultCallBack?.call(true);
              });
          },
          editTextHint: intlanguage('app100001188', '请输入文件夹名称'),
          editTextEmptyToast: intlanguage('app100001188', '请输入文件夹名称'),
        );
        break;
      case TemplateEventStatus.delete:
        if (!Application.isLogin && model.local_type != TemplateLocalType.CREATE) {
          String title = intlanguage('app00210', '当前未登录，请先登录！');
          String cancelDes = intlanguage('app00030', '取消');
          String confirmDes = intlanguage('app01191', '立即登录');
          UserLoginHelper().confirmLogin(context, title, cancelDes, confirmDes, loginSucceed: () {});
          return;
        }
        showCustomDialog(context, intlanguage('app100001216', '确定删除该模板吗？'), intlanguage('app100001200', '删除后将无法恢复'),
            leftFunStr: intlanguage('app00030', '取消'),
            rightFunStr: intlanguage('app00063', '删除'),
            rightTextColor: ThemeColor.brand, leftFunCall: () {
          return;
        }, rightFunCall: () async {
          templateManager
            ..delete(model, () {
              state.myTemplateList.remove(model);
              IconToast.show(context, intlanguage('app01188', '删除成功'));
              ToNativeMethodChannel.nativeTemplateNeedRefresh();
              bool isNeedRequestRefresh = state.myTemplateList.length <= 5;
              if (isNeedRequestRefresh) {
                getShareOutFolders();
              }
              resultCallBack?.call(isNeedRequestRefresh);
              // state.templateScrollController
              //     .animateTo(0, duration: Duration(milliseconds: 100), curve: Curves.easeInOut);
            });
        });
        break;
      case TemplateEventStatus.move:
        if (!Application.isLogin) {
          String title = intlanguage('app00210', '当前未登录，请先登录！');
          String cancelDes = intlanguage('app00030', '取消');
          String confirmDes = intlanguage('app01191', '立即登录');
          UserLoginHelper().confirmLogin(context, title, cancelDes, confirmDes, loginSucceed: () {});
          return;
        }
        // var isShow = false;
        folderManager.getAll(state.folderPage, isFunctionCallOnce: true, (list) async {
          // if (!isShow) {
          //   isShow = true;
          //   return;
          // }

          state.folderList.clear();
          if (list.isNotEmpty) {
            state.folderList.addAll(list);
          }

          state.folderList.insert(0, FolderModel(name: intlanguage('app100001185', '未整理文件夹'), id: 0, isDefault: true));
          state.folderList.add(FolderModel(name: intlanguage('app00659', '新建文件夾'), id: -1, isAdd: true));
          publicTemplateState.shareMeFolderIndex = state.folderList.indexWhere((element) =>
              publicTemplateState.shareOutFolderList[publicTemplateState.shareOutFolderIndex].id == element.id);
          state.templateMoveIndex = publicTemplateState.shareMeFolderIndex;
          await showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            barrierColor: Color.fromRGBO(0, 0, 0, 0.35),
            shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
            builder: (BuildContext con) {
              return TemplateMoveListWidget(
                  this, publicTemplateState.shareOutFolderList[publicTemplateState.shareOutFolderIndex].id!, () {
                if (state.templateMoveIndex != publicTemplateState.shareMeFolderIndex) {
                  templateManager
                    ..move(model, state.folderList[state.templateMoveIndex].id.toString(), () {
                      IconToast.show(
                          context,
                          intlanguage('app100001202', '移动成功',
                              param: [state.folderList[state.templateMoveIndex].name ?? ""]));
                      getShareOutFolders();
                      resultCallBack?.call(true);
                    });
                }
              });
            },
          );
        });

        break;
      case TemplateEventStatus.share:
      case TemplateEventStatus.buyLabel:
      case TemplateEventStatus.labelInfo:
      case TemplateEventStatus.copy:
        super.templateItemEvent(context, event, model, operateType, resultCallBack: resultCallBack);
      default:
    }
  }

  /**
   * 当模板打开时弹出窗口。
   * @param index 模板的索引。
   * @param name 模板的名称，可选。
   * @param operateType 操作类型，标识是分享给我还是分享出去。
   */
  popupOnOpen(int index, String? name, TemplateOperateState operateType) {
    // 发送点击跟踪信息给原生方法。
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "007_015_219",
    });
    var isRefresh = true;
    // 根据操作类型执行不同的逻辑。
    if (operateType == TemplateOperateState.shareOut) {
      // 如果是分享出去的操作，并且索引匹配，则不刷新。
      if (index == publicTemplateState.shareOutFolderIndex) {
        isRefresh = false;
      }
      // 选择分享出去的文件夹。
      shareOutFolderselect(index, name);
      // 如果需要刷新，则执行刷新操作。
      if (isRefresh) {
        shareOutOnRefreshTemplate();
      }
    } else {
      // 如果是分享给我的操作，并且索引匹配，则不刷新。
      if (index == publicTemplateState.shareMeFolderIndex) {
        isRefresh = false;
      }
      // 选择分享给我的文件夹。
      shareMeFolderselect(index, name);
      // 如果需要刷新，则执行刷新操作。
      if (isRefresh) {
        shareMeOnRefreshTemplate();
      }
    }
  }

  @override
  onRefreshTemplate({bool isNeedRequestRefresh = true}) {
    if (state.operateType == TemplateOperateState.shareOut) {
      shareOutOnRefreshTemplate();
    } else {
      shareMeOnRefreshTemplate();
    }
  }

  /**
   * 重新续费VIP功能的实现。
   * @param context BuildContext，用于访问当前的上下文环境。
   */
  RenewVip(BuildContext context) {
    // 向原生方法发送跟踪信息，记录用户点击事件。
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "007_258_239",
    });
    // 检查当前网络连接状态。
    (Connectivity().customCheckConnectivity()).then((value) {
      // 如果网络状态为无连接或蓝牙连接，显示网络异常提示。
      if (value == ConnectivityResult.none || value == ConnectivityResult.bluetooth) {
        showToast(msg: intlanguage('app100000625', '当前网络状态异常'));
        return;
      }
    });
    // 跳转到VIP页面，并在返回时处理结果。
    CustomNavigation.gotoNextPage('ToVipPage', {}).then((value) {
      // 如果返回结果为Map类型，并且'result'字段为整数且大于0，调用获取分享文件夹列表的方法。
      if (value is Map && value['result'] is int && value['result'] > 0) {
        getShareOutFolders();
      }
    });
  }

  /**
   * 跳转到模板详情页面。
   *
   * @param model 模板数据
   * @param isFolderShare 是否为文件夹分享，默认为false。如果为true，则表示当前操作是针对文件夹的分享。
   */
  toCanvasPage(BuildContext context, TemplateData model, {bool isFolderShare = false}) async {
    // 根据操作类型确定是分享出去的模板还是分享给我的模板，进而获取对应的文件夹名称
    String? folderName = "";
    if (state.operateType == TemplateOperateState.shareOut) {
      folderName = publicTemplateState.shareOutFolderList[publicTemplateState.shareOutFolderIndex].name;
    } else {
      folderName = publicTemplateState.shareMeFolderList[publicTemplateState.shareMeFolderIndex].name;
    }

    // 发送跟踪信息到原生方法，记录模板详情页的点击事件及相关信息
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "007_016_025",
      'ext': {"temp_id": model.id, "folder_name": folderName}
    });

    // 执行导航到模板详情页，传入模板ID和是否为文件夹分享的标志
    // CustomNavigation.gotoNextPage('TemplateDetailsPage', {'templateId': model.id, "isFolderShare": isFolderShare})
    //     .then((value) {
    //   ///刷新共享给我的界面   回传参数 refreshType 1 刷新文件下模板  2 刷新整个共享给我的文件夹
    //   if (value is Map) {
    //     int refreshType = value["refreshType"];
    //     if (refreshType == 1) {
    //       shareMeOnRefreshTemplate();
    //     } else if (refreshType == 2) {
    //       changeTabState(isUserMsgChange: true);
    //     }
    //   }
    // });
    ///分享给我的不能进入画板
    if (isFolderShare) {
      return;
    }

    ///进入画板
    TemplateData? templateData = await TemplateManager().getTemplateDetail(model.id!, needUpdateDb: true);
    if (templateData == null) {
      return;
    }
    CanvasTemplate.TemplateData canvasTemplate =
        await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(templateData);
    Map<String, dynamic> json = canvasTemplate.toJson();
    CustomNavigation.gotoNextPage('ToTemplatePage', {
      'content': jsonEncode(json),
    });
  }

  toPrintSettingPage(BuildContext context, TemplateData model, TemplateOperateState operateType,
      {bool isFolderShare = false}) async {
    super.toPrintSettingPage(context, model, operateType, isFolderShare: isFolderShare);
    String? folderName = "";
    if (operateType == TemplateOperateState.shareOut) {
      folderName = publicTemplateState.shareOutFolderList[publicTemplateState.shareOutFolderIndex].name;
    } else if (operateType == TemplateOperateState.shareMe) {
      folderName = publicTemplateState.shareMeFolderList[publicTemplateState.shareMeFolderIndex].name;
    }
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "007_447_441",
      'ext': {"temp_id": model.id, "folder_name": folderName}
    });
  }

  /**
   * 处理模板项的更多点击事件。
   * @param model 模板数据对象，包含模板的详细信息。
   * 该函数不返回任何值。
   */
  templateItemMoreClick(TemplateData model, TemplateOperateState operateType) {
    String? folderName = "";
    if (operateType == TemplateOperateState.shareOut) {
      folderName = publicTemplateState.shareOutFolderList[publicTemplateState.shareOutFolderIndex].name;
    } else if (operateType == TemplateOperateState.shareMe) {
      folderName = publicTemplateState.shareMeFolderList[publicTemplateState.shareMeFolderIndex].name;
    }
    // 向原生方法发送跟踪信息，记录点击事件及相关属性
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click", // 事件类型为点击
      "posCode": "007_016_220", // 定位代码
      'ext': {
        // 扩展信息
        "temp_id": model.id, // 模板ID
        "folder_name": folderName // 当前共享文件夹名称
      }
    });
  }
}

///刷新操作
extension PublicTemplateUpdate on PublicTemplateLogic {
  refreshTab() {
    update([RefreshPublicManager.Tab]);
  }
}
