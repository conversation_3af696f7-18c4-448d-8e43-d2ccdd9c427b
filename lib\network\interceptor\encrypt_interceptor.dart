import 'dart:convert';
import 'dart:isolate';
import 'dart:math';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:niimbot_log_plugin/niimbot_log_plugin.dart';
import 'package:text/network/http_api.dart';
import 'package:text/utils/encrypt_util.dart';

import '../entity/niimbot_user_agent.dart';

class EncryptInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {

    // bool needEncrypt = kReleaseMode ? options.extra['needEncrypt'] as bool : false;
    bool needEncrypt = options.extra['needEncrypt'] as bool;
    debugPrint("===============${options.path}, 需要加密：$needEncrypt, token: ${options.headers["Authorization"]}");
    if (needEncrypt) {
      var random = Random.secure();
      int randomInt = 100000 + random.nextInt(899999);
      options.headers["niimbot-user-agent"] = options.headers["niimbot-user-agent"] + " count/$randomInt";
      debugPrint("===============请求头：${options.headers["niimbot-user-agent"]}");

      NiimbotUserAgent? agent = EncryptUtil.parseUserAgent(options.headers["niimbot-user-agent"] ?? "");
      if (options.data != null && options.headers["niimbot-user-agent"] != null && agent != null) {
        String paramString = jsonEncode(options.data);
        var resStr = EncryptUtil.dataEncrypt(agent as SecretNiimbotUserAgent, paramString, token: options.headers["Authorization"]);
        options.data = {"data": resStr};
        debugPrint("===============加密参数：${options.data}");
      }
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    final responseBody = response.data;
    final agent = response.requestOptions.headers['niimbot-user-agent'];
    final token = response.requestOptions.headers['Authorization'];
    final hasEncryption = agent.contains('count/');
    NiimbotUserAgent? niimbotUserAgent = EncryptUtil.parseUserAgent(agent);
    debugPrint("===============${response.requestOptions.path}, 需要解密：$hasEncryption, token: ${token}");
    if (hasEncryption && niimbotUserAgent != null) {
      String path = response.requestOptions.path;
      //线号机模板导入Excel的情况下：通过分享码decode接口shareDecode返回的response会很大
      //控制台输出response会卡住主线程
      // debugPrint("===============${response.requestOptions.path}解密前：$responseBody");
      try {
        if(path == MyTemplateApi.shareDecode['path']) {
          response.data = await Isolate.run(() => jsonEncode(EncryptUtil.dataDecryptInBackground(niimbotUserAgent as SecretNiimbotUserAgent, responseBody, token: token)));
        }
        else {
          response.data = jsonEncode(EncryptUtil.dataDecrypt(niimbotUserAgent as SecretNiimbotUserAgent, responseBody, token: token));
        }
      }  catch (e, s) {
        debugPrint('异常信息:\n $e');
        response.data = responseBody;
      }
      if(path != MyTemplateApi.shareDecode['path']) {
        _handleSeverDecryptFailed(response);
      }
      //线号机模板导入Excel的情况下：通过分享码decode接口shareDecode返回的response会很大
      //控制台输出response会卡住主线程
      // debugPrint("===============${response.requestOptions.path}解密后：${response.data}");
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    super.onError(err, handler);
  }

  void _handleSeverDecryptFailed(Response response) {
    try {
      final requestOptions = response.requestOptions;
      final headers = requestOptions.headers;
      Map<String, dynamic> result = json.decode(response.data);
      if (result['data']['errorCode'] != 500) return;
      final requestData = requestOptions.data;
      final agent = headers['niimbot-user-agent'];
      final token = headers['Authorization'];
      NiimbotUserAgent? niimbotUserAgent = EncryptUtil.parseUserAgent(agent);
      // debugPrint(
      //     "===============${requestOptions.path}服务端解密失败："
      //         "请求参数：$requestData \n"
      //         "agent: $agent \n"
      //         "token: $token \n"
      //         "服务端响应数据：${response.data}"
      // );
      final Map<String, dynamic> logInfo = {};
      logInfo['url'] = requestOptions.path;
      try {
        logInfo['originalRequestData'] = EncryptUtil.dataDecrypt(niimbotUserAgent as SecretNiimbotUserAgent, requestData['data'], token: token);
      }  catch (e, s) {
        debugPrint('异常信息: $e\n调用栈信息: $s');
        logInfo['originalRequestData'] = '请求参数解密失败';
      }
      logInfo['requestData'] = requestData;
      logInfo['responseData'] = response.data;
      logInfo['agent'] = agent;
      logInfo['token'] = token;
      logInfo['topic'] = '加密参数服务端解密失败';
      NiimbotLogTool.uploadLogInstantTime(logInfo, logSTSType: NiimbotLogSTSType.appLogSTS);
    } catch (e, s) {
      // 将异常信息和调用栈信息合并打印
      // debugPrint('异常信息: $e\n调用栈信息: $s');
    }
  }
}
