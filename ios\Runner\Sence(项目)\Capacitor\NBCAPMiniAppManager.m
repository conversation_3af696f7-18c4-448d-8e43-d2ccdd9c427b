//
//  NBCAPMiniAppManager.m
//  Runner
//
//  Created by 杨瑞 on 2022/12/14.
//
#import <MobileCoreServices/MobileCoreServices.h>
#import "QQLBXScanViewController.h"
#import "JCTemplateImageManager.h"
#import "NBCAPMiniAppManager.h"
#import "JCLabelAppModel.h"
#import "NBCAPMiniAppEngine.h"
#import "ImageLibraryBridge.h"
#import "JCFontManager.h"
#import "NIIMBOTPlugin.h"
#import "StyleDIY.h"
#import "Global.h"

@interface NBCAPMiniAppManager()

@property(nonatomic,strong) NSMutableArray *fontSourceResoloveArr;

@end

@implementation NBCAPMiniAppManager

DEF_SINGLETON(NBCAPMiniAppManager)
typedef void (^MiniAppKeepAliveCallback)(id result, BOOL keepAlive);

- (id)init {
    self = [super init];
    if (self) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loginChanged:) name:LOGIN_CHANGED object:nil];
    }
    return self;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (NBCAPMiniAppEngine *)engine {
    return [NBCAPMiniAppEngine sharedInstance];
}

- (BOOL)checkThenOpenMiniApp:(NSString *)appId
                   keepAlive:(BOOL)keepAlive
                     subpath:(NSString *)subpath
                       extra:(NSDictionary *)extra
               replacedAppId:(NSString *)replacedAppId
                dataReceived:(XYBlock)dataReceived {
    NSArray *appArr = [XYCenter sharedInstance].appArr;
    JCLabelAppModel *currentAppModel = nil;
    for (JCLabelAppModel *appModel in appArr) {
        if (!STR_IS_NIL(appModel.router) &&
            [appModel.router isEqualToString:appId] &&
            (appModel.type.integerValue == 2 || appModel.type.integerValue == 4)) {
            currentAppModel = appModel;
            break;
        }
    }
    if (currentAppModel == nil) {
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"", @"")];
        return NO;
    } else {
        XYWeakSelf;
            
        void (^block)(void) = ^() {
            [weakSelf openMiniApp:currentAppModel
                        keepAlive:keepAlive
                          subpath:subpath
                            extra:extra
                     dataReceived:dataReceived];
        };
        
        if ((replacedAppId ?: @"").length > 0) {
            // 关闭当前小程序
            [[NBCAPMiniAppEngine sharedInstance] quitMiniApp:replacedAppId
                                                   aliveMode:NBCAPAliveWeak
                                                    animated:YES
                                                    complete:^(BOOL complete) {
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.01 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    block();
                });
            }];
        } else {
            block();
        }
    }
    
    NSString *recodedNewAppId = [[NSUserDefaults standardUserDefaults] objectForKey:@"appCenterUpdateNewAppId"];
    NSMutableArray *recodedNewAppIdArr = [recodedNewAppId componentsSeparatedByString:@","].mutableCopy;
    if (![recodedNewAppIdArr containsObject:currentAppModel.appId]) {
        if (recodedNewAppIdArr == nil) {
            recodedNewAppIdArr = [NSMutableArray array];
        }
        [recodedNewAppIdArr addObject:currentAppModel.appId];
        recodedNewAppId = [recodedNewAppIdArr componentsJoinedByString:@","];
        [[NSUserDefaults standardUserDefaults] setObject:recodedNewAppId forKey:@"appCenterUpdateNewAppId"];
    }
    return YES;
}

- (BOOL)miniAppHasInstall:(JCLabelAppModel *)appModel{
    NSString *appId = appModel.router;
    return [self.engine checkAppInstalled:appId];
}

- (BOOL)openMiniApp:(JCLabelAppModel *)appModel
          keepAlive:(BOOL)keepAlive
            subpath:(NSString *)subpath
              extra:(NSDictionary *)extra
       dataReceived:(XYBlock)dataReceived {
    NSString *appId = appModel.router;
    self.currentAppId = appId;
    if ([self.engine checkAppInstalled:appId]) {
        [self preloadMiniApp:appModel keepAlive:keepAlive subpath:subpath extra:extra dataReceived:dataReceived];
    } else {
        if (appModel.downloading.integerValue == 1)
            return NO;
        appModel.downloading = @"1";

        JCWeakSelf
        __block MBProgressHUD *progressHUD = nil;
        UIViewController *currentVc = [XYTool getCurrentVC];
        if(![currentVc isKindOfClass:[NSClassFromString(@"JCLabelApplicationViewController") class]]){
            progressHUD = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
        }
        [self getUniAppDownlpoadInfo:appModel  progressHud:progressHUD infoBlock:^(NSDictionary *uniAppInfoDic) {
            if ([uniAppInfoDic isKindOfClass:[NSDictionary class]]) {
                NSString *uniAppUrl = uniAppInfoDic[@"package_url"];
                NSString *uniAppMD5 = uniAppInfoDic[@"package_md5"];
                NSString *version = uniAppInfoDic[@"version"];
                if (!STR_IS_NIL(uniAppUrl) && [uniAppUrl hasPrefix:@"http"]) {
                    uniAppUrl = XY_Check_UrlString(uniAppUrl);
                    NSString *wgtFilePath = [NSString stringWithFormat:@"%@/%@@%@.wgt", RESOURCE_CAPAPP_RESOURCE_PATH, appId, version];
                    [@{} xy_downLoadFileWithUrlString:uniAppUrl savePath:wgtFilePath tag:appId Success:^(__kindof YTKBaseRequest *request, id object) {
                        [progressHUD hideAnimated:NO];
                        NSData *fileData = [NSData dataWithContentsOfFile:wgtFilePath];
                        NSString *fileMd5 = fileData.MD5String;
                        BOOL result = [fileMd5 caseInsensitiveCompare:uniAppMD5] == NSOrderedSame;
                        if (result) {
                            if(![[NSFileManager defaultManager] fileExistsAtPath:wgtFilePath]){
                                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"", @"文件不存在")];
                            }
                            [[weakSelf engine] install:appId version:version wgtFilePath:wgtFilePath success:^(id x) {
                                [weakSelf preloadMiniApp:appModel keepAlive:keepAlive subpath:subpath extra:extra dataReceived:dataReceived];
                            } failure:^(id x) {
                                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"", @"无效小程序安装包")];
                            }];
                        } else{
                            [[NSFileManager defaultManager] removeItemAtPath:wgtFilePath error:nil];
                            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"", @"无效小程序安装包")];
                        }
                        appModel.downloading = @"0";
                    } failure:^(NSString *msg, id object) {
                        appModel.downloading = @"0";
                        [progressHUD hideAnimated:NO];
                        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"", @"下载失败")];
                    } downloadBlock:^(__kindof YTKBaseRequest *request) {
                    }];
                }else{
                    [progressHUD hideAnimated:NO];
                    appModel.downloading = @"0";
                    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"", @"无效地址")];
                }
            }else{
                [progressHUD hideAnimated:NO];
                appModel.downloading = @"0";
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"", @"无效地址")];
            }
        }];
    }

    return YES;
}

- (void)getUniAppDownlpoadInfo:(JCLabelAppModel*)appModel progressHud:(MBProgressHUD *)progressHUD infoBlock:(XYBlock)uniAppInfoBlock{
    NSString *uniAppSign = [self getUniAppSign:appModel];
    NSString *appKey = appModel.appKey;
    YTKNetworkConfig *config = [YTKNetworkConfig sharedConfig];
    config.baseUrl = UniAppInfoURL;
    [@{@"app_key":UN_NIL(appKey),@"sign":uniAppSign} java_getWithModelType:nil Path:@"version" hud:nil Success:^(__kindof YTKBaseRequest *request, id infoObj) {
        if([infoObj isKindOfClass:[NSDictionary class]]){
            uniAppInfoBlock(infoObj);
        }else if([infoObj isKindOfClass:[NSArray class]]){
            NSArray *infoArr = infoObj;
            if(infoArr.count > 0){
                uniAppInfoBlock(infoArr.firstObject);
            }else{
                [progressHUD hideAnimated:NO];
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000268",@"获取小程序信息失败")];
            }
        }else{
            [progressHUD hideAnimated:NO];
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000268",@"获取小程序信息失败")];
        }
    } failure:^(NSString *msg, id model) {
        uniAppInfoBlock(@"");
        [progressHUD hideAnimated:NO];
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000268", @"获取小程序信息失败")];
    }];
    config.baseUrl = ServerURL;
}

- (NSString *)getUniAppSign:(JCLabelAppModel*)appModel{
    NSString *appSecret = appModel.appSecret;
    NSString *appVersion = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    NSString *signContent = [NSString stringWithFormat:@"app_id=%@&app_version=%@&platform=%@",appModel.router,appVersion,@"ios"];
    NSData *ivData = [appSecret dataUsingEncoding:NSUTF8StringEncoding];
    NSString *signStr = [signContent jk_encryptedWithDESUsingKey:appSecret andIV:ivData];
    return signStr;
}

/// 预加载后打开小程序
- (void)preloadMiniApp:(JCLabelAppModel*)appModel
             keepAlive:(BOOL)keepAlive
               subpath:(NSString *)subpath
                 extra:(NSDictionary *)extra
          dataReceived:(XYBlock)dataReceived {
    NSMutableDictionary *extraData = [self getUniAppLaunchUserInfo:appModel].mutableCopy;
    if (extra != nil) {
        [extraData addEntriesFromDictionary:extra];
    }
    
    XYWeakSelf
    dispatch_async(dispatch_get_main_queue(), ^{
        BOOL result = [[weakSelf engine] launchMiniApp:appModel.router
                                             keepAlive:keepAlive
                                              animated:YES
                                               subpath:subpath
                                                 extra:extraData
                                          dataReceived:dataReceived];
        if (!result) {
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01160", @"数据请求失败")];
        }
    });
}

// 打开小程序时 同步用户信息
- (NSMutableDictionary *)getUniAppLaunchUserInfo:(JCLabelAppModel *)appModel{
    NSString *uniAppId = appModel.router;
    NSString *appKey = appModel.appKey;
    NSString *appSecret = appModel.appSecret;
    NSMutableDictionary *userInfoDic = [NSMutableDictionary dictionary];
    NSString *token = [NSString stringWithFormat:@"bearer %@",m_userModel.token];
    NSString *userAgent = [[NSUserDefaults standardUserDefaults] objectForKey:@"niimbot-user-agent"];
    NSString *uniAppName = [self getUniAppNameWithAppId:uniAppId];
    [userInfoDic setValue:UN_NIL(uniAppName) forKey:@"appletName"];
    [userInfoDic setValue:UN_NIL(uniAppId) forKey:@"appId"];
    [userInfoDic setValue:UN_NIL(appKey) forKey:@"appKey"];
    [userInfoDic setValue:UN_NIL(appSecret) forKey:@"appSecret"];
    [userInfoDic setValue:UN_NIL(m_userModel.userid) forKey:@"userId"];
    [userInfoDic setValue:UN_NIL(XY_JC_LANGUAGE_REAL) forKey:@"languageCode"];
    [userInfoDic setValue:UN_NIL(token) forKey:@"token"];
    [userInfoDic setValue:UN_NIL(userAgent) forKey:@"niimbotUserAgent"];
    if(JC_IS_CONNECTED_PRINTER){
        NSString *deviceId = @"";
        NSMutableDictionary *deviceDict = [JCBluetoothManager sharedInstance].deviceDict;
        if (deviceDict != nil && [deviceDict.allKeys containsObject:PRINT_DEVICE_MAC_ADDRESS]) {
           deviceId = [JCBluetoothManager sharedInstance].deviceDict[PRINT_DEVICE_MAC_ADDRESS];
           [userInfoDic setValue:UN_NIL(deviceId) forKey:@"deviceId"];
        }
        JCPrinterModel *currentPrintModel = JC_CURRENT_CONNECTED_PRINTER_MODEL;
        [userInfoDic setValue:UN_NIL(currentPrintModel.xyid) forKey:@"machineId"];
        [userInfoDic setValue:UN_NIL(currentPrintModel.name) forKey:@"machineName"];
        JCTemplateData *rfidTemplateData = [JCPrintManager sharedInstance].rfidTemplateData;
        if (rfidTemplateData != nil){
            [userInfoDic setValue:UN_NIL(rfidTemplateData.idStr) forKey:@"labelId"];
            [userInfoDic setValue:UN_NIL(rfidTemplateData.name) forKey:@"labelName"];
        }
    }
    [userInfoDic setValue:UN_NIL(m_currentPrinterModel.hardware_series_id) forKey:@"seriesId"];
    [userInfoDic setValue:UN_NIL(m_currentPrinterModel.series_name) forKey:@"seriesName"];
    NSMutableDictionary *vipInfoDic = [NSMutableDictionary dictionary];
    if (m_userModel.vipInfo != nil){
        UserVipInfo *vipInfo = m_userModel.vipInfo;
        [vipInfoDic setValue:@(vipInfo.valid) forKey:@"valid"];
        [vipInfoDic setValue:(vipInfo.autoRenewal.integerValue == 1)?@(true):@(false) forKey:@"autoRenewal"];
        [vipInfoDic setValue:vipInfo.membershipName forKey:@"membershipName"];
        [vipInfoDic setValue:@(vipInfo.startTime) forKey:@"startTime"];
        [vipInfoDic setValue:@(vipInfo.expireAt) forKey:@"expireAt"];
        [vipInfoDic setValue:@(vipInfo.vipInterestsExpireTime) forKey:@"vipInterestsExpireTime"];
        [vipInfoDic setValue:@[] forKey:@"privileges"];
    }
    [userInfoDic setValue:vipInfoDic forKey:@"vipInfo"];
    NSString *userInfoStr = userInfoDic.xy_toJsonString;
    NSLog(@"小程序启动 发送小程序数据:%@",userInfoStr);
    return userInfoDic;
}

- (void)loginChanged:(NSNotification *)notification {
    if (!xy_isLogin) {
        // 退出登录时, 清空保活的小程序
        [[NBCAPMiniAppEngine sharedInstance] clearAliveMiniApp];
    }
}

- (void)checkUniMPResourceAndOpenWithId:(NSString *)appId needKeepLive:(BOOL)isNeedKeepLive parms:(NSDictionary *)parmsInfo receiveUniappData:(XYBlock)receiveUniappData {
    NSArray *appArr = [XYCenter sharedInstance].appArr;
    JCLabelAppModel *currentAppModel = nil;
    for (JCLabelAppModel *appModel in appArr) {
        if(!STR_IS_NIL(appModel.router) && [appModel.router isEqualToString:appId] &&
           (appModel.type.integerValue == 2 || appModel.type.integerValue == 4)){
            currentAppModel = appModel;
            break;
        }
    }
    if(currentAppModel == nil){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"", @"")];
        return;
    }else{
        [[NBCAPMiniAppManager sharedInstance] openMiniApp:currentAppModel
                                                keepAlive:isNeedKeepLive
                                                  subpath:parmsInfo[@"path"]
                                                    extra:parmsInfo
                                             dataReceived:nil];
    }
    NSString *recodedNewAppId = [[NSUserDefaults standardUserDefaults] objectForKey:@"appCenterUpdateNewAppId"];
    NSMutableArray *recodedNewAppIdArr = [recodedNewAppId componentsSeparatedByString:@","].mutableCopy;
    if(![recodedNewAppIdArr containsObject:currentAppModel.appId]){
        if(recodedNewAppIdArr == nil){
            recodedNewAppIdArr = [NSMutableArray array];
        }
        [recodedNewAppIdArr addObject:currentAppModel.appId];
        recodedNewAppId = [recodedNewAppIdArr componentsJoinedByString:@","];
        [[NSUserDefaults standardUserDefaults] setObject:recodedNewAppId forKey:@"appCenterUpdateNewAppId"];
    }
}

- (void)doScanWithType:(NSDictionary *)parmDic isUniApp:(BOOL)isUniApp callback:(MiniAppKeepAliveCallback)callback{
    NSString *mediaType = AVMediaTypeVideo;//读取媒体类型
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];//读取设备授权状态
    if(authStatus == AVAuthorizationStatusRestricted || authStatus == AVAuthorizationStatusDenied){
        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app01310",@"请在“设置-隐私-相机” ， 允许精臣云打印访问你的手机相机") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00297",@"设置") cancelBlock:^{
            
        } sureBlock:^{
            NSURL * url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            if([[UIApplication sharedApplication] canOpenURL:url]) {
                NSURL*url =[NSURL URLWithString:UIApplicationOpenSettingsURLString];
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {
                    
                }];
            }
        }];
        return;
    }else if(authStatus == AVAuthorizationStatusNotDetermined){
        [AVCaptureDevice requestAccessForMediaType:mediaType completionHandler:^(BOOL granted) {
            if(granted){
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self openScanWithParmDic:parmDic isUniApp:isUniApp callback:callback];
                });
            }
        }];
    }else{
        [self openScanWithParmDic:parmDic isUniApp:isUniApp callback:callback];
    }
}

-(void)openScanWithData:(NSDictionary *)parmDic callBack:(XYBlock)callback{
    [self doScanWithType:parmDic isUniApp:false callback:^(id  _Nonnull result, BOOL keepAlive) {
        if(callback != nil){
            callback(result);
        }
    }];
}

//调用原生扫一扫功能
- (void)openScanWithParmDic:(NSDictionary *)parmDic isUniApp:(BOOL)isUniApp callback:(MiniAppKeepAliveCallback)callback{
    NSString *type = parmDic[@"type"];
    QQLBXScanViewController *vc = [QQLBXScanViewController new];
    vc.libraryType = [Global sharedManager].libraryType;
    vc.scanCodeType = [Global sharedManager].scanCodeType;
    vc.scanCreateType = type.integerValue == 1?4:5;
    vc.style = [StyleDIY qqStyle];
    vc.isForceHideTopTitle = true;
    if ([parmDic objectForKey:@"title"] != nil) {
        vc.reminder = parmDic[@"title"];
    }
    vc.scanResultBlock = ^(LBXScanResult *result) {
        NSString *code = result.strScanned;
        if (!STR_IS_NIL(code)) {
          // 类型为2则直接返回结果
          if (type.integerValue == 2) {
            callback(@{@"detail":code},true);
          } else {
            if ([code isPureNumandCharacters]) {
              dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.7 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self goodsPrintWithCode:code isUniApp:isUniApp type:type.integerValue callback:callback result:result];
              });
            } else {
              JC_TrackWithparms(@"show",@"050_121_133",(@{}));
              dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.7 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000267", @"请扫描卷烟条码进行打印")];
              });
            }
          }
        }
    };
    //镜头拉远拉近功能
    vc.isVideoZoom = YES;
    UIViewController *uniVC = isUniApp ? [[NBCAPMiniAppManager sharedInstance] getCurrentUniAppViewController] : [XYTool getCurrentVC];
    XYNavigationController *navVC = [[XYNavigationController alloc] initWithRootViewController:vc];
    [uniVC presentViewController:navVC animated:YES completion:^{

    }];
}

- (void)goodsPrintWithCode:(NSString *)code isUniApp:(BOOL)isUniApp type:(NSInteger)type callback:(MiniAppKeepAliveCallback)callback result:(LBXScanResult *)scanResult{
    XYWeakSelf
    if(isUniApp == false && type == 1){
        [@{@"oneCode":UN_NIL(code)} java_postWithModelType:[JCTemplateData class] Path:J_scan_get_cloud_template hud:@"" Success:^(__kindof YTKBaseRequest *request, JCTemplateData *requestModel) {
            if(callback != nil){
                if(requestModel == nil){
                    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app00321", @"请使用精臣标签。")];
                    return;
                }
                NSDictionary * data = @{@"detail":[requestModel toJSONString]};
                callback(data,true);
            }
        } failure:^(NSString *msg, id model) {
            if(!(NETWORK_STATE_ERROR)){
                dispatch_async(dispatch_get_main_queue(), ^{
                    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app00321", @"请使用精臣标签。")];
                });
            }else{
                [MBProgressHUD showToastWithMessageDarkColor:msg];
            }
            [[XYCenter sharedInstance] scanTrackWithSearchResult:scanResult searchtype:@"2" searchResult:@"0" source:@"4"];
        }];
    }else{
        NSDictionary *requestDic = @{@"barcode":UN_NIL(code)};
        [requestDic java_postWithModelType:[JCGoodResultModel class] Path:J_goods_new_scan hud:nil Success:^(__kindof YTKBaseRequest *request,JCGoodResultModel *requestModel) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if(requestModel.goods_info.barcode.length > 0){
                    NSDictionary *goodsInfoDic = [requestModel.goods_info getBaseGoodsInfoDic];
                    if(callback != nil && goodsInfoDic != nil){
                       NSDictionary * data = @{@"detail":goodsInfoDic};
                       callback(data,true);
                    }
                }else{
                    if(callback != nil){
                        callback(@{},false);
                    }
                }
            });
          
        } failure:^(NSString *msg, id model) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if(callback != nil){
                    callback(@{},false);
                }
            });
        }];
    }
}

- (NSString *)prewDataWith:(JCTemplateData *)currentData index:(NSInteger)pageIndex{
    NSInteger screenScale = [UIScreen mainScreen].scale;
    JCTemplateData *jcData = currentData.toSdk(YES).configureBase64Background(YES);
    NSMutableDictionary *dataDic = [jcData dataDict].mutableCopy;
    // 多碳带颜色需要生成白底背景
    if ([[JCBluetoothManager sharedInstance] isMultipleCarbonColor]) {
        // 背景图置空，生成白底背景
        [dataDic setValue:@"" forKey:@"localBackgroundImageUrl"];
    }
    NSString *jsonStr = [dataDic xy_toJsonString];
    float displayMultiple = DrawBoardInfo.mm2pxScale * screenScale;
    if(jcData.height / jcData.width > 3){
        displayMultiple = 250/jcData.width;
    }else if(jcData.height / jcData.width > 2){
        displayMultiple = 500/jcData.width;
    }else{
        displayMultiple = 1200/jcData.width;
    }
    float printMultiple = DrawBoardInfo.dpiScale;
    UIImage *previewImage = [ImageLibraryBridge generatePrintPreviewImage:jsonStr
                                                          displayMultiple:displayMultiple
                                                            printMultiple:printMultiple
                                                    printPreviewImageType:0
                                                               themeColor:JC_CURRENT_PRINT_COLOR
                                                                    error:nil];
    // 多碳带颜色需要合成背景图生成新的预览图
    if ([[JCBluetoothManager sharedInstance] isMultipleCarbonColor]) {
        NSInteger roate = 360 - (jcData.rotate % 360);
        previewImage = [previewImage multipleColor:[JCBluetoothManager sharedInstance].printColor backgroundImage:[UIImage imageWithContentsOfFile:jcData.localBackgroundImagePath] rotate:roate canvasRotate:jcData.canvasRotate];
    }
    NSData *imageData = UIImagePNGRepresentation(previewImage);
    NSString *encodedImageStr = (previewImage == nil) ? @"":[imageData base64EncodedStringWithOptions:NSDataBase64Encoding64CharacterLineLength];
    encodedImageStr = [encodedImageStr stringByReplacingOccurrencesOfString:@"\n" withString:@""];
    encodedImageStr = [encodedImageStr stringByReplacingOccurrencesOfString:@"\r" withString:@""];
    encodedImageStr = [encodedImageStr stringByReplacingOccurrencesOfString:@"\t" withString:@""];
    return encodedImageStr;
}

- (void)downLoadImageImageAndFontsForData:(NSString *)source templateData:(JCTemplateData *)data complete:(void(^)(JCTemplateData *))completeBlock{
    __block JCTemplateData *blockModel = data;
    [JCTemplateImageManager downLoadImagesForData:data options:DownAll complete:^(JCTemplateData *resultData) {
        blockModel = resultData;
        BOOL is_ableEdit_Template = [resultData checkTemplateDetailByBackImage:YES containFont:NO];
        if(is_ableEdit_Template){
            NSInteger noFontCount = [[JCFontManager sharedManager] needDownloadFont:data].count;
            NSInteger needChangLangCount = [[JCFontManager sharedManager] needChangLangDownloadFont:data].count;
            if([[JCFontManager sharedManager] isNeedDownloadFonts:data] && source.integerValue == 4){
                if(noFontCount != 0){
                    if(needChangLangCount == 0){
                        NSInteger noFontCount = [[JCFontManager sharedManager] needDownloadFont:data].count;
                        NSString *descrpString = [NSString stringWithFormat:@"%ld %@",noFontCount,XY_LANGUAGE_TITLE_NAMED(@"app01217",@"字体缺失，是否需要下载")];
                        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01218",@"字体缺失") message:descrpString cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01220",@"下载") cancelBlock:^{
                          if (completeBlock) completeBlock(blockModel);
                        } sureBlock:^{
                          [[JCFontManager sharedManager] downloadAllFonts:data downloadComplateBlock:^{
                              if (completeBlock) completeBlock(blockModel);
                          }];
                        } alertType:3];
                    }else{
                        NSString *fontDownloadString = [NSString stringWithFormat:@"%ld %@",noFontCount,XY_LANGUAGE_TITLE_NAMED(@"app01246",@"字体需下载")];
                        NSString *needChangeLangString = [NSString stringWithFormat:@"%ld %@",needChangLangCount,XY_LANGUAGE_TITLE_NAMED(@"app01247",@"字体需切换语言后下载")];
                        NSString *descrpString = [NSString stringWithFormat:@"%@\n%@",fontDownloadString,needChangeLangString];
                        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01218",@"字体缺失") message:descrpString cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01220",@"下载") cancelBlock:^{
                          if (completeBlock) completeBlock(blockModel);
                        } sureBlock:^{
                          [[JCFontManager sharedManager] downloadAllFonts:data downloadComplateBlock:^{
                              if (completeBlock) completeBlock(blockModel);
                          }];
                        } alertType:3];
                    }
                }else{
                    if(needChangLangCount != 0 && ![self.fontSourceResoloveArr containsObject:data.idStr]){
                        NSString *descrpString = [NSString stringWithFormat:@"%@",XY_LANGUAGE_TITLE_NAMED(@"app01248",@"请切换语言下载字体")];
                        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01218",@"字体缺失") message:descrpString cancelButtonTitle:@"" sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707",@"我知道了") cancelBlock:^{
                            if (completeBlock) completeBlock(blockModel);
                        } sureBlock:^{
                            [self.fontSourceResoloveArr addObject:data.idStr];
                            if (completeBlock) completeBlock(blockModel);
                        }];
                    }else{
                        if (completeBlock) completeBlock(blockModel);
                    }
                }
            }else{
                if (completeBlock) completeBlock(blockModel);
            }
        }else{
          if (completeBlock) completeBlock(blockModel);
        }
    }];
}

- (BOOL)hasUniAppActivity{
    return NO;
}

- (UIViewController *)getCurrentUniAppViewController{
    UIViewController *currentVC = [XYTool getCurrentVC];
    if([currentVC isKindOfClass:[NSClassFromString(@"DCUniMPViewController") class]]){
        return currentVC;
    }
    return nil;
}

- (NSString *)getUniAppNameWithAppId:(NSString *)uniAppId{
    NSString *uniAppName = @"";
    NSString *appCenterPath = [NSString stringWithFormat:@"%@/%@",DocumentsPath,@"appCenter"];
    NSString *file = [NSString stringWithFormat:@"%@/appCenterInfo.text",appCenterPath];
    NSString *jsonString = [NSString stringWithContentsOfFile:file encoding:NSUTF8StringEncoding error:nil];
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    if(jsonData){ 
        NSArray *innerAppList = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingAllowFragments error:nil];
        if(innerAppList.count > 0){
            NSMutableArray *appInfoList = [NSMutableArray array];
            
            for (NSDictionary *appListInfoDic in innerAppList) {
                NSArray *appInfosDic = appListInfoDic[@"appList"] != nil?appListInfoDic[@"appList"]:@[];
                [appInfoList addObjectsFromArray:appInfosDic];
            }
            for (NSDictionary *appInfoDic in appInfoList) {
                JCLabelAppModel *appModel = [[JCLabelAppModel alloc] initWithDictionary:appInfoDic error:nil];
                if([appModel.router isEqualToString:uniAppId] && !STR_IS_NIL(uniAppId)){
                    uniAppName = XY_LANGUAGE_TITLE_NAMED(appModel.name, @"");
                    break;
                }
            }
        }
    }
    return uniAppName;
}
@end
