import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:nety/models/niimbot_printer.dart';
import 'package:nety/models/niimbot_rfid_info.dart';
import 'package:nety/nety.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/extensions/niimbot_printer.dart';
import 'package:niimbot_print_strategy/niimbot_print_strategy.dart';
import 'package:niimbot_template/niimbot_template.dart';

import '../print_setting_manager.dart';
import '../utils/dialog_util.dart';
import './../interface/print_setting_channel_interface.dart';

class PrinterStrategyManager {
  static PrinterStrategyManager? _instance;

  PrinterStrategyManager._();

  factory PrinterStrategyManager() {
    _instance ??= PrinterStrategyManager._();
    return _instance!;
  }

  late bool isSupportRFIDPrint;
  late PrintSettingChannelInterface channel;
  late PrinterInfo printerInfo;
  String? ribbonColor;

  String? paperColor;

  ///上一次的打印策略，用于打印日志
  PrintStrategyResult? lastPrintStrategy;

  ///连接成功后设置打印策略中打印机信息
  setPrintStrategyDeviceInfo(PrintSettingChannelInterface strategychannel) async {
    channel = strategychannel;
    ribbonColor = null;
    paperColor = null;
    NiimbotPrinter? connectedPrint = NiimbotPrintSDK().store.connectedPrinter;
    if (connectedPrint == null) return;
    //根据打印机返回硬件码及硬件名称 获取当前硬件信息
    Map printerDetail =
        await channel.getCurrentPrinterInfo((connectedPrint.code ?? 0).toString(), connectedPrint.name ?? '');
    if (printerDetail.isEmpty) {
      printerInfo = PrinterInfo();
    } else {
      printerInfo = PrinterInfo.fromJson(Map<String, dynamic>.from(printerDetail));
    }

    ///使用sn码组合拼接打印机名称 处理蓝牙修改场景
    printerInfo.deviceName = channel.getCurrentPrinterMachineId(
        (connectedPrint.code ?? 0).toString(), connectedPrint.name ?? '', connectedPrint!.sn ?? '');
    printerInfo.hardVersion = connectedPrint.hardwareVersion;
    printerInfo.firmwarVersion = connectedPrint.firmwareVersion;
    printerInfo.machineSecret = connectedPrint.antiCounterfeitKey ?? '';
    NiimbotSecurityPrintHelper.setSecurityPrintDeviceInfo(printerInfo,
        defaultPrintStrategy: printerDetail["errorPrintStrategy"]);
    //设置打印机是否支持RFID打印 用于打印过程中 策略请求判断
    isSupportRFIDPrint = printerInfo.isRFIDPrint();
    debugPrint(
        'PrintStrategyLog2: niimbot-initStrategy rfidType:${printerInfo.rfidType} securityAction:${printerInfo.securityAction} isSupportRFIDPrint:$isSupportRFIDPrint Whitelist:${printerInfo.rfidNotSupportVersions} currentHardWareVersion:${printerInfo.hardVersion} defaultPrintStrategy${printerDetail["errorPrintStrategy"]}}');
  }

  bool isPhotoFramePaperRFIDPrint() {
    //从打印机获取支持RFIDl类型
    PrinterRfidType rfidType = printerInfo.rfidType;
    //securityAction 0 非RFID 1  防伪处理  2 只获取模版
    //硬件版本RFID黑名单 rfidNotSupportVersions
    bool isRFIDPrint = rfidType != PrinterRfidType.unRFID &&
        (printerInfo.securityAction.toInt() == 1 || rfidType == PrinterRfidType.ribbonRFID);
    return isRFIDPrint;
  }

  ///获取打印策略及打印耗材服务端总量及打印颜色信息
  Future<PrintStrategyResult?> getRFIDStrategy(GetSecurityScene strategyScene,
      {List<NiimbotRFIDInfo>? rfidInfos,
      int pageuUsed = 0,
      int ribbonUsed = 0,
      Function(Map<String, dynamic>)? serverInfoCallback}) async {
    if (strategyScene == GetSecurityScene.toPrint) lastPrintStrategy = null;
    bool needAccurate = false;
    //判断当前设备内标签纸是否适配打印机
    bool paperIsSupportDevice = true;
    NiimbotPrinter? connectedPrinter = NiimbotPrintSDK().store.connectedPrinter;
    if (connectedPrinter == null) {
      return null;
    }
    Map printerDetail =
        channel.getCurrentPrinterInfo((connectedPrinter?.code ?? 0).toString(), connectedPrinter?.name ?? '');
    var value = await channel.getPrinterLabelData() ?? {};
    TemplateData? rfidLabel;
    if (value.isEmpty || printerDetail.isEmpty) {
      //当未获取到标签纸信息 或 设备详情信息
      paperIsSupportDevice = true;
    } else {
      String valueString = jsonEncode(value);
      rfidLabel = await TemplateParse.parseFromMap(Map<String, dynamic>.from(jsonDecode(valueString)));
      //当标签信息支持打印机为空。或包含当前设备型号时 判断支持当前设备
      if (rfidLabel.profile.machineName!.split(",").isEmpty ||
          rfidLabel.profile.machineName!.split(",").contains(printerDetail["name"] ?? "")) {
        paperIsSupportDevice = true;
      } else {
        paperIsSupportDevice = false;
      }
    }

    //打印结束后不可获取实时RFID数据 避免直接点击打印 SDK阻塞
    if (strategyScene != GetSecurityScene.afterPrint) {
      needAccurate = true;
    }
    if ((rfidInfos == null) && isSupportRFIDPrint) {
      if (needAccurate) {
        //打印时获取实时RFID数据  其他场景获取缓存
        rfidInfos = await NiimbotPrintSDK().getConsumablesRFIDData(accurate: needAccurate);
      } else {
        rfidInfos = channel.getDeviceRFIDCacheInfos();
      }
    }
    ChipRFIDInfo? rfidInfo;
    if ((rfidInfos ?? []).isNotEmpty) {
      rfidInfo = ChipRFIDInfo();
      rfidInfo.labelPaperType = 1;
      for (var element in rfidInfos!) {
        if (element.type == -1) continue; //适配安卓 未识别的情况下 返回各项值为-1的情况
        if (element.type == 6 || element.type == 8) {
          //碳带
          rfidInfo.labelPaperType = element.type;
          rfidInfo.ribbonSerial = element.uuid;
          rfidInfo.ribbonUsed = ribbonUsed / 10;
          rfidInfo.ribbonQuantitySum = element.allPaperMeters;
          rfidInfo.ribbonUsedQuantitySum = element.usedPaperMeters;
        } else {
          //标签纸
          rfidInfo.labelPaperType = element.type;
          rfidInfo.paperSerial = element.uuid;
          if (rfidInfo.labelPaperType == 3) {
            rfidInfo.paperUsed = pageuUsed / 10;
          } else {
            rfidInfo.paperUsed = pageuUsed;
          }
          rfidInfo.paperQuantitySum = element.allPaperMeters;
          rfidInfo.paperUsedQuantitySum = element.usedPaperMeters;
        }
      }
    }
    PrintStrategyResult? strategyResult = await NiimbotSecurityPrintHelper.getSecurityPrint(
      rfidInfo,
      getSecurityScene: strategyScene,
      paperIsSupportDevice: paperIsSupportDevice,
      serviceRfidInfo: (paperInfo, ribbonInfo) {
        debugPrint('请求到服务端最大可打印值及颜色：标签纸：$paperInfo,碳带：$ribbonInfo');
        if (rfidInfo != null) {
          //刷新服务端信息 标签碳带使用最大值、标签碳带颜色
          Map<String, dynamic> serviceRfidInfo = {};
          if ((rfidInfo.paperSerial ?? "").isNotEmpty) {
            int paperMax = paperInfo["paperMaxNum"] ?? 0;
            serviceRfidInfo["paperSerial"] = rfidInfo.paperSerial;
            serviceRfidInfo["paperMax"] = paperMax;
            serviceRfidInfo["paperColor"] = paperInfo["paperColor"];
          }
          if ((rfidInfo.ribbonSerial ?? "").isNotEmpty) {
            int ribbonMax = ribbonInfo["ribbonMaxNum"] ?? 0;
            serviceRfidInfo["ribbonSerial"] = rfidInfo.ribbonSerial;
            serviceRfidInfo["ribbonMax"] = ribbonMax;
            serviceRfidInfo["ribbonColor"] = ribbonInfo["ribbonColor"];
            // serviceRfidInfo["ribbonColor"] = "65.105.225";
          }
          if (serviceRfidInfo.isNotEmpty &&
              strategyScene != GetSecurityScene.afterPrint &&
              strategyScene != GetSecurityScene.toPrint) {
            channel.refreshRFIDInfoInService(serviceRfidInfo);
          }
          if (serverInfoCallback != null) {
            serverInfoCallback.call(serviceRfidInfo);
          }
        }
      },
    );
    String description = "";
    if (strategyScene == GetSecurityScene.afterPrint) {
      description = "nextPrintStrategy";
    } else if (strategyScene == GetSecurityScene.toPrint) {
      description = "CurrentPrintStrategy";
    } else if (strategyScene == GetSecurityScene.connectDevice || strategyScene == GetSecurityScene.boxLid) {
      description = "connectDevice/boxLid";
    }
    debugPrint(
        'PrintStrategyLog2: niimbot-$description Time:${DateTime.now()} parms:${rfidInfo?.toJson() ?? {}} strategyDetail:${strategyResult?.toJson() ?? {}}');
    if (strategyScene == GetSecurityScene.toPrint) lastPrintStrategy = strategyResult;
    return strategyResult;
  }

  ///相片纸打印 是否支持当前设备
  Future<bool> isPhotoFramePaperNotSupportDeveice(
    PrintSettingLogic printSettingLogic,
  ) async {
    var value = await channel.getPrinterLabelData() ?? {};
    TemplateData? rfidLabel;
    try {
      if (value.isNotEmpty) {
        String valueString = jsonEncode(value);
        rfidLabel = await TemplateParse.parseFromMap(Map<String, dynamic>.from(jsonDecode(valueString)));
      }
    } catch (e) {
      rfidLabel = null;
    }
    bool isPhotoFramePaper = false;
    bool paperIsSupportDevice = false;
    if (rfidLabel != null && rfidLabel.consumableType == 70) {
      isPhotoFramePaper = true;
    }
    NiimbotPrinter? connectedPrinter = NiimbotPrintSDK().store.connectedPrinter;
    Map printerDetail = await printSettingLogic.channel
        .getCurrentPrinterInfo((connectedPrinter?.code ?? 0).toString(), connectedPrinter?.name ?? '');
    if (value.isEmpty || printerDetail.isEmpty) {
      //当未获取到标签纸信息 或 设备详情信息
      paperIsSupportDevice = true;
    } else {
      String valueString = jsonEncode(value);
      rfidLabel = await TemplateParse.parseFromMap(Map<String, dynamic>.from(jsonDecode(valueString)));
      //当标签信息支持打印机为空。或包含当前设备型号时 判断支持当前设备
      if (rfidLabel.profile.machineName!.split(",").isEmpty ||
          rfidLabel.profile.machineName!.split(",").contains(printerDetail["name"] ?? "")) {
        paperIsSupportDevice = true;
      } else {
        paperIsSupportDevice = false;
      }
    }
    //1、当前识别标签纸是否相片纸
    //2、当前标签纸是否适配当前打印机
    //3、当前打印机是否支持RFID打印(白名单排除)
    return isPhotoFramePaper && !paperIsSupportDevice && isPhotoFramePaperRFIDPrint();
  }

  ///构建打印埋点参数
  Map<String, dynamic> buildPrintTrackEventData(
    String uniqueValue,
    List<NiimbotRFIDInfo>? rfidInfos,
    PrintStrategyResult? strategyResult,
    String forbidReason,
  ) {
    Map<String, dynamic> eventData = {
      "state": 1, // 1:成功；0：失败
      "check_version": uniqueValue, // 打印任务ID
      "errorCode": strategyResult?.errorCode ?? "", // SDK错误码
    };

    // 从RFID信息中提取标签纸和碳带的序列号和UID
    if (rfidInfos != null && rfidInfos.isNotEmpty) {
      String? paperSerial;
      String? ribbonSerial;
      String? paperUID;
      String? ribbonUID;

      for (var element in rfidInfos) {
        if (element.type == -1) continue; // 适配安卓 未识别的情况下 返回各项值为-1的情况

        if (element.type == 6 || element.type == 8) {
          // 碳带
          ribbonSerial = element.uuid;
          ribbonUID = element.serialNumber;
        } else {
          // 标签纸
          paperSerial = element.uuid;
          paperUID = element.serialNumber;
        }
      }

      // 添加RFID相关参数
      if (paperSerial != null) eventData["paperSerial"] = paperSerial;
      if (ribbonSerial != null) eventData["ribbonSerial"] = ribbonSerial;
      if (paperUID != null) eventData["paperUID"] = paperUID;
      if (ribbonUID != null) eventData["ribbonUID"] = ribbonUID;
    }

    // 如果有禁止原因，添加到参数中
    if (forbidReason.isNotEmpty) {
      eventData["reason"] = forbidReason;
    }

    return eventData;
  }

  ///打印前/恢复打印时 策略检查
  Future<StrategyType> printCheckRFIDPrintStrategy(
    PrintSettingLogic printSettingLogic,
    String uniqueValue,
    Function(String languageCode, String decr) languadeCall, {
    List<NiimbotRFIDInfo>? rfidInfos,
    bool isNeedTrackEvent = true,
  }) async {
    StrategyType strategyType = StrategyType.normal;
    bool isPhotoFramePaperNotSupport = await isPhotoFramePaperNotSupportDeveice(printSettingLogic);

    ///相片纸不适配打印机场景 直接禁止打印
    if (isPhotoFramePaperNotSupport) {
      strategyType = StrategyType.forbid;
      PrintStrategyResult? strategyResult = PrintStrategyResult(strategyType);
      strategyResult.errorCode = "RFIDPrintIsNotSupportDevice";
      String forbidReason = _showCheckRFIDErrorDialog(strategyResult, printSettingLogic, languadeCall);

      // 使用封装的函数构建埋点参数
      Map<String, dynamic> eventData = buildPrintTrackEventData(uniqueValue, rfidInfos, strategyResult, forbidReason);
      eventData["state"] = 0;
      printSettingLogic.channel.trackEvent("print", "001", eventData: eventData);

      return strategyType;
    } else if (PrinterStrategyManager().isSupportRFIDPrint) {
      PrintStrategyResult? strategyResult = await getRFIDStrategy(GetSecurityScene.toPrint, rfidInfos: rfidInfos);
      debugPrint('PrintStrategyLog2: currentPrintStrategy:${strategyResult?.toJson() ?? {}}');
      printSettingLogic.printingStatusReset();
      if (isNeedTrackEvent) {
        List<NiimbotRFIDInfo> rfidInfos = NiimbotPrintSDK().store.connectedPrinter?.consumablesRFID ?? [];
        List<String> rfidInfoList = rfidInfos.map((element) {
          return element.uuid;
        }).toList();
        printSettingLogic.channel.trackEvent("click", "024_452", eventData: {
          "coverStatus": NiimbotPrintSDK().store.coverStatus ?? 1,
          "rfidInfo": rfidInfoList,
        });
      }
      if (strategyResult != null) {
        strategyType = strategyResult.printStrategy;
        if (strategyType == StrategyType.forbid) {
          //禁止打印
          String forbidReason = _showCheckRFIDErrorDialog(strategyResult, printSettingLogic, languadeCall);

          // 使用封装的函数构建埋点参数
          Map<String, dynamic> eventData =
              buildPrintTrackEventData(uniqueValue, rfidInfos, strategyResult, forbidReason);
          eventData["state"] = 0;
          printSettingLogic.channel.trackEvent("print", "001", eventData: eventData);

          return strategyType;
        } else if (strategyType == StrategyType.batchforbid) {
          //禁止批量打印
          return strategyType;
        }
      }
    } else {
      debugPrint('PrintStrategyLog2: unSupportRFIDPrint');
    }
    return strategyType;
  }

  ///禁止打印提示
  _showCheckRFIDErrorDialog(
    PrintStrategyResult strategyResult,
    PrintSettingLogic printSettingLogic,
    Function(String languageCode, String decr) languadeCall,
  ) {
    String errorMsg = '';
    String forbidReason = '';
    switch (strategyResult.errorCode) {
      case 'RFIDPrintLabelUnknow':
        errorMsg = "F1${languadeCall('app01547', '【无法识别到耗材】请使用精臣最新正版标签纸，可尝试转动或重新装入耗材。')}";
        forbidReason = '无法识别当前标签';
        break;
      case 'RFIDPrintCarbonUnknow':
        errorMsg = "F2${languadeCall('app01548', '【无法识别到耗材】请使用精臣最新正版碳带，可尝试转动或重新装入耗材。')}";
        forbidReason = '无法识别当前碳带';
        break;
      case 'RFIDPrintServiceLabelUnknow':
        errorMsg = "F3${languadeCall('app01549', '【无法识别到标签纸】请联系客服。')}";
        forbidReason = '未查询到服务端标签数据';
        break;
      case 'RFIDPrintServiceCarbonUnknow':
        errorMsg = "F4${languadeCall('app01550', '【无法识别到碳带】请联系客服。')}";
        forbidReason = '未查询到服务端碳带数据';
        break;
      case 'RFIDPrintLabelCountMax':
        errorMsg = "F5${languadeCall('app01551', '【标签纸无法打印】请使用精臣最新正版标签纸或联系客服')}";
        forbidReason = '标签打印超限';
        break;
      case 'RFIDPrintCarbonCountMax':
        errorMsg = "F6${languadeCall('app01552', '【碳带无法打印】请使用精臣最新正版碳带或联系客服。')}";
        forbidReason = '碳带打印超限';
        break;
      case 'RFIDPrintIsNotSupportDevice':
        errorMsg = "【C1】${languadeCall('app100000901', '您的标签纸不适用当前型号的设备，请更换')}";
        forbidReason = '打印机不适配标签纸';
        break;
      default:
        errorMsg = "F10${languadeCall('app01205', '未识别到耗材')}";
        forbidReason = '';
        break;
    }
    int delayMilliseconds = printSettingLogic.taskType == PrintTaskType.printNullUi ? 300 : 0;
    Future.delayed(Duration(milliseconds: delayMilliseconds), () {
      if (NiimbotPrintSDK().store.connectedPrinter != null) {
        DialogUtil().showCustomDialog(printSettingLogic.pageManager.context, "", errorMsg,
            justSureButton: true,
            contentTextStyle: const TextStyle(color: Color((0xFF000000)), fontSize: 16, fontWeight: FontWeight.w600),
            rightFunStr: languadeCall("app00707", "我知道了"), rightFunCall: () {
          if (printSettingLogic.taskType == PrintTaskType.printNullUi ||
              printSettingLogic.taskType == PrintTaskType.printC1 ||
              printSettingLogic.taskType == PrintTaskType.printNullUiShowProgress) {
            Future.delayed(Duration(milliseconds: delayMilliseconds), () {
              Navigator.of(printSettingLogic.pageManager.context).pop();
              debugPrint("无UI打印进度场景 pop2");
            });
            printSettingLogic.channel.setUnNeedUIPrintComplete(
                '0',
                printSettingLogic.parameter!.customData!["uniAppInfo"]["uniAppId"],
                printSettingLogic.parameter?.customData?["taskId"] ?? '',
                forbidReason);
          }
        }, channel: printSettingLogic.channel);
      }
    });

    return forbidReason;
  }

  Map<String, String> getLastPrintStrategyInfo() {
    final Map<String, String> result = {};
    final String strategyName = lastPrintStrategy?.printStrategy.name ?? '';
    StrategySceneCode? strategyScene = lastPrintStrategy?.strategyScene;
    String errorCodeStr = '';
    if (lastPrintStrategy?.printStrategy != StrategyType.normal) {
      if (strategyScene != null) {
        errorCodeStr = strategyScene.getStrategySceneValue();
      } else {
        errorCodeStr = lastPrintStrategy?.errorCode ?? '';
      }
    }
    result['name'] = strategyName;
    result['errorCode'] = errorCodeStr;
    return result;
  }
}
