import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart' as FlutterNavigator;
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart' as KLogger;
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/model/canvas_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_cell_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_element.dart';
import 'package:niimbot_flutter_canvas/src/model/material/material_item.dart';
import 'package:niimbot_flutter_canvas/src/utils/track_utils.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/common_import_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/serial_attr_widget_builder.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/svg_icon.dart';

import 'attr_widget_builder.dart';
import 'comm/attr_icon_button.dart';

typedef InputTextChanged = void Function(String text);

final KLogger.Logger _logger = KLogger.Logger("KeyboardSensitiveAttrWidget ->", on: kDebugMode);

typedef ElementValueEditorDisplay = Function(CanvasElement cursorElement, CanvasElement focusedElement,
    {int? cursorIndex, bool? isDoubleClick});

class ElementAttributePanel extends StatefulWidget {
  final List<CanvasElement> canvasElements;
  final AttrPanelState attrPanelState;
  final Function(int positon, String text) onTabClick;
  final Function() closeAttributePanel;
  final Function(CanvasElement, MaterialItem, bool, bool) onChooseMaterial;

  /// 是否需要渲染子Tab，用于编辑时输入框输入
  final bool isNeedAttTabs;

  /// 输入焦点变动
  /// [cursorElement] 当前编辑的元素，表格时 .data 是 cell element
  /// [focusedElement] 选中态元素，表格时 .data 是 table element，其余元素与 [cursorElement] 相同
  final ElementValueEditorDisplay onElementValueEditorDisplay;
  final ImportExcelFromElementCall importExcelFromElementCall;
  final ChangeDataManualInputField changeDataManualInputField;
  final ChangeColumnIndexCall changeColumnIndexCall;
  final Function(Function, {bool isPopLogin}) guideBuyVip;
  final Function onGraphElementTypeChanged;

  /// 默认传入为45%的屏幕高度
  final double height;

  /// 安全区 + 保存打印高度
  final double paddingBottom;

  int? defaultIndex;

  ElementAttributePanel({
    super.key,
    required this.canvasElements,
    required this.attrPanelState,
    required this.onTabClick,
    required this.closeAttributePanel,
    this.defaultIndex,
    required this.onChooseMaterial,
    required this.onElementValueEditorDisplay,
    required this.importExcelFromElementCall,
    required this.changeDataManualInputField,
    required this.changeColumnIndexCall,
    required this.guideBuyVip,
    required this.isNeedAttTabs,
    required this.onGraphElementTypeChanged,
    required this.height,
    required this.paddingBottom,
  });

  @override
  ElementAttributePanelState createState() => ElementAttributePanelState();
}

class ElementAttributePanelState extends State<ElementAttributePanel> {
  AttrWidgetBuilder? _attrWidgetBuilder;
  ScrollController controller = ScrollController();

  /// 子面板操作区高度 = 45%的屏幕高度 - (安全区 + 保存打印) - Tab高度
  double get _attributeContentHeight => (widget.height - widget.paddingBottom - 42).ceilToDouble();

  @override
  void initState() {
    super.initState();
    _attrWidgetBuilder = AttrWidgetBuilder.createBuilder(
        widget.canvasElements,
        widget.attrPanelState,
        _attributeContentHeight,
        widget.onChooseMaterial,
        widget.onElementValueEditorDisplay,
        widget.importExcelFromElementCall,
        widget.changeDataManualInputField,
        widget.changeColumnIndexCall,
        widget.guideBuyVip,
        widget.onGraphElementTypeChanged);
    _attrWidgetBuilder?.init(context, widget.canvasElements);
    widget.defaultIndex == null;
    List<MenuTag>? menus = _attrWidgetBuilder?.getMenus(widget.canvasElements);
    if (menus == null) {
      return;
    }
    if (widget.defaultIndex != null) {
      position = widget.defaultIndex!;
      menus.forEach((element) {
        if (element.index == position) {
          element.isSelected = true;
        } else {
          element.isSelected = false;
        }
      });
    } else {
      for (int index = 0; index < menus.length; index++) {
        MenuTag nemu = menus[index];
        if (nemu.isSelected) {
          position = index;
          break;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    _attrWidgetBuilder?.attrPanelState = widget.attrPanelState;
    return LayoutBuilder(
      builder: (buildContext, BoxConstraints cons) {
        _logger.log('ElementAttributePanel-cons-----------------$cons--------');
        return Column(mainAxisAlignment: MainAxisAlignment.center, mainAxisSize: MainAxisSize.min, children: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {},
            child: Container(
              padding: EdgeInsets.only(left: 0, right: 0),
              width: double.maxFinite,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10)),
                color: CanvasTheme.of(context).backgroundColor,
                boxShadow: [
                  BoxShadow(
                    blurRadius: 5, //阴影范围
                    spreadRadius: 0.1, //阴影浓度
                    offset: Offset(0, -2.0),
                    color: Color(0xFFC7C9D1).withOpacity(0.15), //阴影颜色
                  ),
                ],
              ),
              child: Stack(children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(
                            height: 4,
                          ),
                          SingleChildScrollView(
                            controller: controller,
                            scrollDirection: Axis.horizontal,
                            padding: EdgeInsetsDirectional.zero,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: menuList(),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      width: 40,
                      height: 40,
                    ),
                  ],
                ),
                Align(
                  alignment: AlignmentDirectional.centerEnd,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.end,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset(
                        'assets/common/downMask.png',
                        width: 16,
                        height: 40,
                        package: 'niimbot_flutter_canvas',
                      ),
                      AttrIconButton(
                          const SvgIcon(
                            'assets/common/attributeDown.svg',
                            useDefaultColor: false,
                          ),
                          width: 40,
                          height: 40,
                          onTap: widget.closeAttributePanel)
                    ],
                  ),
                )
              ]),
            ),
          ),
          widget.isNeedAttTabs
              ? Container(
                  color: Colors.white,
                  padding: EdgeInsets.only(bottom: widget.paddingBottom),
                  child: _buildPanelWidget(context),
                )
              : const SizedBox.shrink(),
        ]);
      },
    );
  }

  /// 面板下属性构造
  Widget _buildPanelWidget(BuildContext context) {
    Widget child;
    // 存在tab页构建
    if (_attrWidgetBuilder != null) {
      CanvasElement canvasElement = widget.canvasElements.first;
      if(!AttrWidgetBuilder.isAttrWidgetMatchElement(_attrWidgetBuilder!, canvasElement)) {
        _attrWidgetBuilder!.dispose();
        _attrWidgetBuilder = AttrWidgetBuilder.createBuilder(
            widget.canvasElements,
            widget.attrPanelState,
            _attributeContentHeight,
            widget.onChooseMaterial,
            widget.onElementValueEditorDisplay,
            widget.importExcelFromElementCall,
            widget.changeDataManualInputField,
            widget.changeColumnIndexCall,
            widget.guideBuyVip,
            widget.onGraphElementTypeChanged);
        _attrWidgetBuilder?.init(context, widget.canvasElements);
        widget.defaultIndex == null;
        List<MenuTag>? menus = _attrWidgetBuilder?.getMenus(widget.canvasElements);
        if (menus != null) {
          if (widget.defaultIndex != null) {
            position = widget.defaultIndex!;
            menus.forEach((element) {
              if (element.index == position) {
                element.isSelected = true;
              } else {
                element.isSelected = false;
              }
            });
          } else {
            for (int index = 0; index < menus.length; index++) {
              MenuTag nemu = menus[index];
              if (nemu.isSelected) {
                position = index;
                break;
              }
            }
          }
        }
        _attrWidgetBuilder!.attrPanelState = widget.attrPanelState;
      }
      // 不可滑动 && 不需要Expand
      if (!_attrWidgetBuilder!.canScroll(position: position, element: canvasElement)) {
        child = buildAttrTabWidget(context);
      } else {
        child = buildAttrScrollPanelWidget(context);
      }
    } else {
      child = Expanded(child: buildAttrScrollPanelWidget(context));
    }
    return child;
  }

  // 带滑动的画板属性区，注意⚠️SingleChildScrollView的wrap可能导致内部的scrollView无法正常滑动
  Widget buildAttrScrollPanelWidget(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width,
      height: _attributeContentHeight,
      color: CanvasTheme.of(context).backgroundLightColor,
      child: SingleChildScrollView(
        key: Key('ElementAttributePanel-TabIndex-$position'),
        child: _attrWidgetBuilder == null ? Container() : buildAttrTabWidget(context),
      ),
    );
  }

  // 画板属性区
  Widget buildAttrTabWidget(BuildContext context) {
    List<MenuTag> menus = _attrWidgetBuilder?.getMenus(widget.canvasElements) ?? [];
    if (menus.length <= position) {
      position = menus.length - 1;
      //setState(() {});
      Future.delayed(Duration(milliseconds: 200), () {
        setState(() {});
      });
    }
    if (menus.length - 1 == position && controller.hasClients) {
      controller.jumpTo(controller.position.maxScrollExtent);
    }
    return Container(
      key: Key(
          'ElementAttributePanelTabViewKey-${widget.canvasElements.length}-${widget.canvasElements.map((e) => e.data.id).toSet().join(',')}'),
      child: _attrWidgetBuilder?.buildAttrWidget(menus[position], widget.canvasElements, context, () {
        setState(() {
          ///表格属性面板删除行或者列时，当没有聚焦的cell时，默认选中表格
          if (widget.canvasElements.length == 1 && widget.canvasElements.first.data.type == ElementItemType.table) {
            TableElement tableElement = widget.canvasElements.first.data as TableElement;
            List<TableCellElement> focusCells = tableElement.getFocusedCells();
            if (focusCells.isEmpty) {
              position = 0;
            }
          }
        });
      }),
    );
  }

  Widget selectCard(int pos, String text, Color? bgColor, Color? textColor, String trackName) {
    double borderRadius = 12.0;
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if ((widget.canvasElements ?? []).length == 0 ||
            _attrWidgetBuilder!.isSwitchTabDisable(element: widget.canvasElements.first)) {
          return;
        }
        setState(() {
          position = pos;
        });
        CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
          "track": "click",
          "posCode": "108_072_101",
          "ext": {"b_name": trackName, "module_name": TrackUtils.getElementsTypeStr(widget.canvasElements)}
        });
        widget.onTabClick.call(position, text);
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (position == pos)
            Container(
              width: borderRadius,
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              height: borderRadius,
              child: Container(
                width: borderRadius,
                decoration: BoxDecoration(
                  color: CanvasTheme.of(context).backgroundColor,
                  borderRadius: Directionality.of(context) == TextDirection.rtl
                      ? BorderRadius.only(bottomLeft: Radius.circular(borderRadius))
                      : BorderRadius.only(bottomRight: Radius.circular(borderRadius)),
                  boxShadow: [],
                ),
                height: borderRadius,
              ),
            ),
          Container(
            margin: EdgeInsets.only(top: 0),
            padding: EdgeInsets.fromLTRB(20, 8, 20, 10),
            decoration: position == pos
                ? BoxDecoration(
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(borderRadius), topRight: Radius.circular(borderRadius)),
                    color: CanvasTheme.of(context).backgroundLightColor,
                  )
                : null,
            child: Text(
              text,
              style: position == pos
                  ? CanvasTheme.of(context).attrBarTitleHighlightTextStyle
                  : CanvasTheme.of(context).attrBarTitleTextStyle,
            ),
          ),
          if (position == pos)
            Container(
              width: borderRadius,
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              height: borderRadius,
              child: Container(
                width: borderRadius,
                decoration: BoxDecoration(
                  color: CanvasTheme.of(context).backgroundColor,
                  borderRadius: Directionality.of(context) == TextDirection.rtl
                      ? BorderRadius.only(bottomRight: Radius.circular(borderRadius))
                      : BorderRadius.only(bottomLeft: Radius.circular(borderRadius)),
                  boxShadow: [],
                ),
                height: borderRadius,
              ),
            ),
        ],
      ),
    );
  }

  List<Widget> menuList() {
    if (widget.canvasElements.isEmpty || _attrWidgetBuilder == null) return [];

    return _attrWidgetBuilder
            ?.getMenus(widget.canvasElements)
            .map((e) => selectCard(e.index, e.text, e.bgColor, e.textColor, e.trackName))
            .toList() ??
        [];
  }

  int position = 0;

  @override
  void dispose() {
    _timePop();
    super.dispose();
    _attrWidgetBuilder?.dispose();
    if (_attrWidgetBuilder is SerialAttrWidgetBuilder) {
      SerialAttrWidgetBuilder serialAttrWidgetBuilder = _attrWidgetBuilder as SerialAttrWidgetBuilder;
      serialAttrWidgetBuilder.serialDisposeTextEditingController();
    }
  }

  _timePop() {
    if (!mounted) return;  // 检查小部件是否仍然挂载
    if (DateElementHelper.isShowTimeElement) {
      if (DateElementHelper.isShowEditDialog) {
        Timer(Duration(milliseconds: 200), () {
          if (!mounted) return;  // 定时器回调内再次检查挂载状态
          DateElementHelper.isShowEditDialog = false;
          FlutterNavigator.BoostNavigator.instance
              .pop()
              .then((value){
            if (mounted) {  // 确保仍然挂载后再执行操作
              FlutterNavigator.BoostNavigator.instance.pop();
            }
          });
        });
      } else {
        // if (mounted && BoostNavigator.instance.getTopPageInfo()?.pageName != 'canvas' && Navigator.of(context, rootNavigator: false).canPop()) {  // 确保挂载后再执行操作
        //   Navigator.of(context).pop();
        // }
      }
    }
  }
}
