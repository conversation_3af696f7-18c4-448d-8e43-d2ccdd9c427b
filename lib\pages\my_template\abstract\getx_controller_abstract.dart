import 'dart:async';
import 'dart:convert';

import 'package:common_utils/common_utils.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:niim_login/login_plugin/widget/jc_custom_toast_util.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart' as CanvasTemplate;
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/utils/display_util.dart';
// import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:text/application.dart';
import 'package:text/business/user/user_login_helper.dart';
import 'package:text/network/entity/user.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_state.dart';
import 'package:text/pages/industry_template/select_label/label_category_list_model.dart';
import 'package:text/pages/industry_template/select_label/label_details_page.dart';
import 'package:text/pages/my_template/controller/folder_manager.dart';
import 'package:text/pages/my_template/model/folder_model.dart';
import 'package:text/pages/my_template/model/template_data_extension.dart';
import 'package:text/pages/my_template/my_template_state.dart';
import 'package:text/pages/my_template/page/public_template/controller/public_template_logic.dart';
import 'package:text/pages/my_template/widget/template_move_list_widget.dart';
import 'package:text/template/constant/template_local_type.dart';
import 'package:text/template/constant/template_version.dart';
import 'package:text/template/model/template_detail_result.dart';
import 'package:text/template/template_manager.dart';
import 'package:text/template/util/template_misc_utils.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/utils/toast_util.dart';
import 'package:text/utils/vip_helper.dart';
import 'package:text/vipTrial/trial_activity.dart';
import 'package:text/vipTrial/vip_trial_manager.dart';
import 'package:text/widget/FToast.dart';

import '../../../routers/custom_navigation.dart';
import '../../../template/model/template_operation_status.dart';
import '../../../template/util/template_transform_utils.dart';
import '../../meProfile/me_profile_presenter.dart';
import '../controller/template_manager_v2.dart';
import '../widget/share_invite_widget.dart';

class GetxControllerAbstract extends GetxController {
  var folderManager = FolderManager();
  var templateManager = TemplateManagerV2();
  final MyTemplateState state = MyTemplateState();
  Function? eventBusFunction;

  @override
  void onReady() {
    super.onReady();
    getFolderList();
    onEventListener();
    getOffUnLoginTemplate();
  }

  @override
  void onClose() {
    state.folderScrollController.dispose();
    state.templateRefreshController.dispose();
    NiimbotEventBus.getDefault().unregister(this);
    super.onClose();
  }

  getOffUnLoginTemplate() {
    templateManager.getOffUnLoginTemplate();
  }

  getShareMeFolders({isDelete = false}) {
  }

  changeTabState({isUserMsgChange = false, folderSelectIndex = 0}){
    
  }
  onEventListener() {
    LogUtil.d('注册原生event事件监听');
    if (state.operateType != TemplateOperateState.shareMe || state.operateType != TemplateOperateState.shareOut) {
      NiimbotEventBus.getDefault().register(this, (data) async {
        if (data is Map && data.containsKey("myTemplateRefresh")) {
          Timer(Duration(seconds: 1), () {
            getTemplateList(folderId: state.folderList[state.folderIndex].id!.toInt());
            if (state.templateScrollController.hasClients) {
              state.templateScrollController
                  .animateTo(0, duration: Duration(milliseconds: 100), curve: Curves.easeInOut);
            }
            if (eventBusFunction != null) {
              eventBusFunction!();
            }
          });
        }
      });
    }
  }

  ///获取文件夹列表
  getFolderList() {
    folderManager.getAll(state.folderPage, (list) {
      state.folderList.clear();
      state.folderCoverList.clear();
      state.folderList.add(FolderModel(name: intlanguage('app00659', '新建文件夾'), id: -1, isAdd: true));
      state.folderList.add(FolderModel(name: intlanguage('app100001185', '未整理文件夹'), id: 0, isDefault: true));
      state.folderCoverList.add(FolderModel(name: intlanguage('app00659', '新建文件夾'), id: -1, isAdd: true));
      if (list.isNotEmpty) {
        state.folderList.addAll(list);
      }
      refreshFolderList();
      getTemplateList();
    });
  }

  updateFolderList() {
    folderManager.getAll(state.folderPage, (list) {
      state.folderList.clear();
      state.folderCoverList.clear();
      state.folderList.add(FolderModel(name: intlanguage('app00659', '新建文件夾'), id: -1, isAdd: true));
      state.folderList.add(FolderModel(name: intlanguage('app100001185', '未整理文件夹'), id: 0, isDefault: true));
      state.folderCoverList.add(FolderModel(name: intlanguage('app00659', '新建文件夾'), id: -1, isAdd: true));
      if (list.isNotEmpty) {
        state.folderList.addAll(list);
      }
      refreshFolderList();
    });
  }

  ///获取模板列表
  getTemplateList({int folderId = 0, bool isRefresh = true, onlyNative = false}) {
    state.isRefresh = isRefresh;
    if (state.isRefresh) {
      state.templatePage = 1;
      state.templateRefreshController.resetNoData();
    }

    templateManager.getAll(state.templatePage, folderId: folderId, (
      List<TemplateData?> list,
    ) {
      num? folderIndex = 0;
      // if (state.folderList.isNotEmpty) {
      folderIndex = state.folderList[state.folderIndex].id;
      // }
      if (folderIndex == folderId) {
        if (state.isRefresh) {
          state.myTemplateList.clear();
          state.myTemplateList.addAll(List<TemplateData>.from(list));

          // 按更新时间排序
          state.myTemplateList.sort((a, b) {
            DateTime dateA = DateTime.parse(a.profile.extra.updateTime ?? '');
            DateTime dateB = DateTime.parse(b.profile.extra.updateTime ?? '');
            return dateB.compareTo(dateA);
          });

          state.templateRefreshController.refreshCompleted();

          // 直接回到顶部
          // if (state.templateScrollController.hasClients) {
            // state.templateScrollController.jumpTo(0);
          // }
        } else if (list.isEmpty) {
          state.templatePage--;
          state.templateRefreshController.loadNoData();
        } else {
          list.forEach((element) {
            if (element != null) {
              // 查找是否已存在相同id的元素
              int existingIndex = state.myTemplateList.indexWhere((t) => t.id == element.id);
              if (existingIndex != -1) {
                // 如果存在，则更新该位置的数据
                state.myTemplateList[existingIndex] = element;
              } else {
                // 如果不存在，则添加到列表中
                state.myTemplateList.add(element);
              }
            }
          });

          // 按更新时间排序
          state.myTemplateList.sort((a, b) {
            DateTime dateA = DateTime.parse(a.profile.extra.updateTime ?? "");
            DateTime dateB = DateTime.parse(b.profile.extra.updateTime ?? "");
            return dateB.compareTo(dateA);
          });

          state.templateRefreshController.loadComplete();
        }
        refreshAllSelectState();
        state.templatePageState =
            state.myTemplateList.isNotEmpty ? IndustryHomeState.showContainer : IndustryHomeState.empty;
        refreshTemplateList();
        refreshFolderList();
      } else {
        state.templateRefreshController.loadComplete();
      }
      ToNativeMethodChannel.nativeTemplateNeedRefresh();
    }, refresh: state.isRefresh, onlyNative: onlyNative, state: state.operateType);
  }

  ///选中文件夹操作
  folderselect(int index, String? name) {
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "007_015_023",
      'ext': {"folder_name": name}
    });
    state.folderIndex = index;
    refreshFolderList();
  }

  ///搜索模版
  toSearchPersonalTemplate() {
    ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "007_018", "ext": {}});
    CustomNavigation.gotoNextPage('searchMyTemplate', {"operateType": state.operateType.toString()},
            withContainer: false)
        .then((value) {
      if (state.needFreshMyTemplate) {
        onRefreshTemplate();
        state.needFreshMyTemplate = false;
      }
    });
  }

  ///模板管理状态切换刷新
  refereshTemplateManagerState(BuildContext context, {bool isReload = false}) {
    // 无网络不可用
    (Connectivity().customCheckConnectivity()).then((value) {
      if (!isReload) {
        ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "007_017_026", "ext": {}});
      }
      if (Application.user == null) {
        if (value == ConnectivityResult.none || value == ConnectivityResult.bluetooth) {
          showToast(msg: intlanguage('app100000625', '当前网络状态异常'));
          return;
        }
        String title = intlanguage('app00210', '当前未登录，请先登录！');
        String cancelDes = intlanguage('app00030', '取消');
        String confirmDes = intlanguage('app01191', '立即登录');
        UserLoginHelper().confirmLogin(context, title, cancelDes, confirmDes, loginSucceed: () {
          Future.delayed(const Duration(milliseconds: 1000), () {
            if (Application.user != null) {
              refereshTemplateManagerState(context, isReload: true);
              getFolderList();
            }
          });
        });
        return;
      }
      state.operateType = state.operateType == TemplateOperateState.normal
          ? TemplateOperateState.managerState
          : TemplateOperateState.normal;
      state.myTemplateSelectList.clear();
      state.selectAllState = false;
      state.myTemplateList.forEach((element) {
        element.isSelected = false;
      });
      update();
    });
  }

  ///模板选择或取消
  refereshSelectTemplate(int index) {
    TemplateData model = state.myTemplateList[index];
    model.isSelected = model.isSelected;
    if (!state.myTemplateSelectList.any((selectElement) => selectElement.id == model.id)) {
      state.myTemplateSelectList.add(model);
      state.myTemplateSelectList.sort((a, b) {
        DateTime dateA = DateTime.parse(a.profile.extra.updateTime ?? "");
        DateTime dateB = DateTime.parse(b.profile.extra.updateTime ?? "");
        return dateB.compareTo(dateA);
      });
    } else {
      state.myTemplateSelectList.removeWhere((element) => element.id == model.id);
    }
    bool checkAllSelect = true;
    state.myTemplateList.forEach((element) {
      if (!state.myTemplateSelectList.any((selectElement) => selectElement.id == element.id)) {
        checkAllSelect = false;
      }
    });
    state.selectAllState = checkAllSelect;
    update([RefreshManager.templateSelect, RefreshManager.templateList]);
  }

  ///通过预览刷新模板选择
  changedTemplateSelect(List<TemplateData> changedTemplates) {}

  checkTemplateSize() {}

  ///移动至文件夹 选中文件夹刷新
  refereshSelectFolder(int index) {
    state.selectFolderId = state.folderList[index].id.toString();
    update([RefreshManager.folderSelect]);
  }

  ///模板删除
  refreshTemplateDelete() {
    state.myTemplateSelectList.clear();
    update([RefreshManager.templateSelect, RefreshManager.templateList]);
  }

  ///全选或全部取消模板
  selectFolderAllTemplate(BuildContext context) {
    bool currentState = state.selectAllState;
    if (currentState) {
      state.selectAllState = !currentState;
      var removeIds = <String>[];
      state.myTemplateSelectList.forEach((element) {
        if (element.profile.extra.folderId == state.folderList[state.folderIndex].id.toString()) {
          element.printPaper = 0;
          removeIds.add(element.id ?? "");
        }
      });
      ToNativeMethodChannel().removeTemplateDetails(removeIds);
      state.myTemplateSelectList.removeWhere(
          (element) => element.profile.extra.folderId == state.folderList[state.folderIndex].id.toString());
      if (state.operateType == TemplateOperateState.batchPrint) {
        checkTemplateSize();
      }
    } else {
      List<TemplateData> needAddTemplate = [];
      state.myTemplateList.forEach((element) {
        if (state.operateType == TemplateOperateState.batchPrint) {
          if (!state.myTemplateSelectList.any((selectElement) => selectElement.id == element.id) &&
              !checkTemplateDataSource(element) &&
              !isTemplateNeedUpgrade(element)) {
            needAddTemplate.add(element);
          }
        } else if (!state.myTemplateSelectList.any((selectElement) => selectElement.id == element.id)) {
          needAddTemplate.add(element);
        }
      });
      if (needAddTemplate.isNotEmpty) {
        if (state.operateType == TemplateOperateState.batchPrint) {
          if (state.myTemplateSelectList.length + needAddTemplate.length > 1000) {
            showToast(msg: intlanguage('app100001278', '请选择少于1000的模板数量'));
            return;
          }
          state.myTemplateSelectList.addAll(needAddTemplate);
          state.selectAllState = !currentState;
          state.myTemplateSelectList.sort((a, b) {
            DateTime dateA = DateTime.parse(a.profile.extra.updateTime ?? "");
            DateTime dateB = DateTime.parse(b.profile.extra.updateTime ?? "");
            return dateB.compareTo(dateA);
          });
          var ids = <String>[];
          needAddTemplate.forEach((element) {
            if (element.printPaper == 0) {
              element.printPaper = 1;
              ids.add(element.id ?? "");
            }
          });
          ToNativeMethodChannel().downloadTemplateDetails(ids);
          checkTemplateSize();
        } else {
          state.myTemplateSelectList.addAll(needAddTemplate);
          state.selectAllState = !currentState;
        }
      } else if (state.operateType == TemplateOperateState.batchPrint && state.myTemplateList.isNotEmpty) {
        showCenterToast(context, intlanguage('app100001246', '包含数据源和序列号的模板暂不支持快捷打印'));
        return;
      }
    }
    update([RefreshManager.templateSelect, RefreshManager.templateList]);
  }

  ///切换文件夹 刷新全选状态
  refreshAllSelectState() {
    if (state.operateType == TemplateOperateState.normal) return;
    bool isAllContain = true;
    bool hasNormalTemplate = false;
    for (var element in state.myTemplateList) {
      if (state.operateType == TemplateOperateState.batchPrint) {
        if (checkTemplateDataSource(element) || isTemplateNeedUpgrade(element)) {
          continue;
        } else {
          hasNormalTemplate = true;
        }
      }
      if (!state.myTemplateSelectList.any((selectElement) => selectElement.id == element.id)) {
        isAllContain = false;
        break;
      }
    }
    if (state.operateType == TemplateOperateState.batchPrint && !hasNormalTemplate) {
      isAllContain = false;
    }
    state.selectAllState = isAllContain;
    update([RefreshManager.templateSelect]);
  }

  checkTemplateDataSource(TemplateData model) {}
  isTemplateNeedUpgrade(TemplateData model) {}
  folderEvent(BuildContext context, FolderEventStatus event, FolderModel model, int index) {
    switch (event) {
      case FolderEventStatus.share:
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "click",
          "posCode": "007_016_249",
        });
        if (!Application.user!.isVip) {
          vipInvalidGuide(context, VIPUseScence.folderShare, () {
            EasyLoading.show();
            folderManager.share(model, (Map data) {
              EasyLoading.dismiss();
              if (data['folder'] != null && data['invitingCode'] != null) {
                Map invitingCodeInfo = data['invitingCode'];
                getFolderList();
                _showShareInviteCode(context, invitingCodeInfo['code'], () {
                  CustomNavigation.gotoNextPage('shareMemberManager', {}, withContainer: false).then((value) {});
                });
              }
              Get.find<PublicTemplateLogic>().getShareOutFolders(isSendEvent: true);
            });
          });
        } else {
          EasyLoading.show();
          folderManager.share(model, (Map data) {
            EasyLoading.dismiss();
            if (data['folder'] != null && data['invitingCode'] != null) {
              Map invitingCodeInfo = data['invitingCode'];
              getFolderList();
              _showShareInviteCode(context, invitingCodeInfo['code'], () {
                CustomNavigation.gotoNextPage('shareMemberManager', {}, withContainer: false).then((value) {});
              });
            }
            Get.find<PublicTemplateLogic>().getShareOutFolders(isSendEvent: true);
          });
        }
        break;
      case FolderEventStatus.cancelShare:
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "click",
          "posCode": "123_277",
        });
        showCustomDialog(context, intlanguage('app100001364', '确定取消共享"\$"文件夹吗？', param: [model.name.toString()]),
            intlanguage('app100001365', '该文件夹的共享成员将不再能访问此文件夹。'),
            leftFunStr: intlanguage('app00030', '取消'),
            rightFunStr: intlanguage('app100001366', '停止共享'),
            rightTextColor: ThemeColor.brand, leftFunCall: () {
          return;
        }, rightFunCall: () async {
          await folderManager.cancelShare(model.id!.toInt().toString(), state.operateType, () {
            showCustomDialog(context, "", intlanguage('app100001368', '该文件夹已取消共享'),
                justSureButton: true,
                contentTextStyle: TextStyle(
                  fontSize: 16,
                  color: ThemeColor.mainTitle,
                  fontWeight: FontWeight.w600,
                ),
                rightFunStr: intlanguage('app00707', '我知道了'),
                rightTextColor: ThemeColor.COLOR_161616,
                rightFunCall: () {});
            getFolderList();
          });
        });
        break;
      case FolderEventStatus.reName:
        if (!Application.user!.isVip && index > 11) {
          vipInvalidGuide(context, VIPUseScence.folderRename, () {
            getFolderList();
            Get.find<PublicTemplateLogic>().getShareOutFolders(isSendEvent: true);
          });
        } else {
          showEditTextDialog(
            context,
            intlanguage('app01321', '重命名'),
            model.name,
            20,
            (value) {
              folderManager
                ..reName(model, value, () {
                  getFolderList();
                  Get.find<PublicTemplateLogic>().getShareOutFolders(isSendEvent: true);
                });
            },
            editTextHint: intlanguage('app100001188', '请输入文件夹名称'),
            editTextEmptyToast: intlanguage('app100001188', '请输入文件夹名称'),
          );
        }

        break;
      case FolderEventStatus.delete:
        showCustomDialog(context, intlanguage('app100001198', '确定删除"\$"文件夹吗？', param: [model.name!]),
            intlanguage('app100001199', '该文件夹内模板也将被自动删除。'),
            leftFunStr: intlanguage('app00030', '取消'),
            rightFunStr: intlanguage('app00063', '删除'),
            rightTextColor: ThemeColor.brand, leftFunCall: () {
          return;
        }, rightFunCall: () async {
          folderManager
            ..delete(model, () {
              state.folderList.remove(model);
              IconToast.show(context, intlanguage('app01188', '删除成功'));
              if (state.folderIndex > state.folderList.length - 1) {
                state.folderIndex--;
              }
              refreshFolderList();
              onRefreshTemplate();
              Get.find<PublicTemplateLogic>().getShareOutFolders(isSendEvent: true);
            });
        });
        break;
      case FolderEventStatus.create:
        // 无网络不可用
        (Connectivity().customCheckConnectivity()).then((value) {
          if (value == ConnectivityResult.none || value == ConnectivityResult.bluetooth) {
            showToast(msg: intlanguage('app100000625', '当前网络状态异常'));
            return;
          }
          if (Application.user == null) {
            String title = intlanguage('app00210', '当前未登录，请先登录！');
            String cancelDes = intlanguage('app00030', '取消');
            String confirmDes = intlanguage('app01191', '立即登录');
            UserLoginHelper().confirmLogin(context, title, cancelDes, confirmDes, loginSucceed: () {
              Future.delayed(const Duration(milliseconds: 1000), () {
                if (Application.user != null) {
                  getFolderList();
                }
              });
            });
            return;
          }

          if (state.folderList.length >= 12 && !Application.user!.isVip) {
            if (state.folderList.length >= 102) {
              ToNativeMethodChannel().sendTrackingToNative({
                "track": "show",
                "posCode": "007_015_218",
              });
              showToast(msg: intlanguage('app100001476', '创建文件夹已达上限100个'));
              return;
            }
            vipInvalidGuide(context, VIPUseScence.folderCreate, () {
              getFolderList();
              Get.find<PublicTemplateLogic>().getShareOutFolders(isSendEvent: true);
            });
          } else {
            if (state.folderList.length >= 102) {
              ToNativeMethodChannel().sendTrackingToNative({
                "track": "show",
                "posCode": "007_015_218",
              });
              showToast(msg: intlanguage('app100001476', '创建文件夹已达上限100个'));
              return;
            }
            ToNativeMethodChannel().sendTrackingToNative({
              "track": "click",
              "posCode": "007_015_024",
            });
            showEditTextDialog(
              context,
              intlanguage('app00659', '新建文件夹'),
              "",
              20,
              (value) {
                folderManager
                  ..create(value, () {
                    // getFolderList();
                    var isRefresh = false;
                    folderManager.getAll(state.folderPage, (list) {
                      state.folderList.clear();
                      state.folderCoverList.clear();
                      state.folderList.add(FolderModel(name: intlanguage('app00659', '新建文件夾'), id: -1, isAdd: true));
                      state.folderList
                          .add(FolderModel(name: intlanguage('app100001185', '未整理文件夹'), id: 0, isDefault: true));
                      state.folderCoverList
                          .add(FolderModel(name: intlanguage('app00659', '新建文件夾'), id: -1, isAdd: true));
                      if (isRefresh) {
                        if (state.folderIndex != 1) {
                          state.folderIndex += 1;
                        }
                      }
                      if (list.isNotEmpty) {
                        isRefresh = true;
                        state.folderList.addAll(list);
                      }
                      refreshFolderList();
                      getTemplateList();
                    });
                    if (state.templateScrollController.hasClients) {
                      state.folderScrollController
                          .animateTo(0, duration: Duration(milliseconds: 100), curve: Curves.easeInOut);
                    }
                  }, (code) {
                    if (Application.user!.isVip) {
                      showToast(msg: intlanguage('app100001476', '创建文件夹已达上限100个'));
                    } else {
                      vipInvalidGuide(context, VIPUseScence.folderCreate, () {
                        getFolderList();
                        Get.find<PublicTemplateLogic>().getShareOutFolders(isSendEvent: true);
                      });
                    }
                  });
              },
              editTextHint: intlanguage('app100001188', '请输入文件夹名称'),
              editTextEmptyToast: intlanguage('app100001188', '请输入文件夹名称'),
            );
          }
        });

        break;
      default:
    }
  }

  ///展示分享码模态弹窗
  _showShareInviteCode(BuildContext context, String inviteCode, VoidCallback inviteCallback) {
    showModalBottomSheet(
      enableDrag: true,
      isDismissible: false,
      barrierColor: Color(0xFF000000).withOpacity(0.35),
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      builder: (BuildContext context) {
        return ShareInviteWidget(
          inviteCode,
          valideTime: 24,
          isFromShareSuccess: true,
        );
      },
    ).then((value) {
      if (value == ShareInviteEvent.copyShareCode) {
        Clipboard.setData(ClipboardData(
            text: intlanguage('app100001347', '邀请您共享我的模板，邀请码◎\$◎，复制后打开👉精臣云打印APP👈。', param: [inviteCode])));
        Future.delayed(Duration(milliseconds: 500))
            .then((value) => IconToast.show(context, intlanguage('app100001348', '已复制，快去粘贴吧~')));
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "click",
          "posCode": "007_273_253",
        });
      } else if (value == ShareInviteEvent.toMemberManager) {
        inviteCallback.call();
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "click",
          "posCode": "007_273_252",
        });
      } else if (value == ShareInviteEvent.close) {
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "click",
          "posCode": "007_273_254",
        });
      }
    });
  }

  ///用户Vip无效时引导
  vipInvalidGuide(BuildContext context, VIPUseScence scence, VoidCallback openVipCallback) {
    VipHelper.vipInvalidGuide(context, scence, openVipCallback);
  }

  isLogin(BuildContext context, Function function) {
    UserLoginHelper().customLogin(context, function);
  }

  templateItemEventBefore(
      BuildContext context, TemplateEventStatus event, TemplateData model, TemplateOperateState operateType,
      {FolderModel? folderModel, List<TemplateData>? templates, Function(bool operateResult)? resultCallBack}) {
    trackMoreOperationEvent(model.id ?? "", event, state.folderList[state.folderIndex].name);
    templateItemEvent(context, event, model, operateType,
        folderModel: folderModel, templates: templates, resultCallBack: resultCallBack);
  }

  templateItemEvent(
      BuildContext context, TemplateEventStatus event, TemplateData model, TemplateOperateState operateType,
      {FolderModel? folderModel, List<TemplateData>? templates, Function(bool operateResult)? resultCallBack}) async {
    switch (event) {
      case TemplateEventStatus.reName:
        showEditTextDialog(
          context,
          intlanguage('app01321', '重命名'),
          model.name,
          100,
          (value) {
            templateManager
              ..reName(model, value, () {
                ToNativeMethodChannel.nativeTemplateNeedRefresh();
                // onRefreshTemplate();
                if (state.templateScrollController.hasClients) {
                  state.templateScrollController
                      .animateTo(0, duration: Duration(milliseconds: 100), curve: Curves.easeInOut);
                }
                resultCallBack?.call(true);
              });
          },
          editTextHint: intlanguage('app00290', '请输入模板名称'),
          editTextEmptyToast: intlanguage('app00290', '请输入模板名称'),
        );
        break;
      case TemplateEventStatus.delete:
        if (!Application.isLogin && model.local_type != TemplateLocalType.CREATE) {
          String title = intlanguage('app00210', '当前未登录，请先登录！');
          String cancelDes = intlanguage('app00030', '取消');
          String confirmDes = intlanguage('app01191', '立即登录');
          UserLoginHelper().confirmLogin(context, title, cancelDes, confirmDes, loginSucceed: () {});
          return;
        }
        showCustomDialog(context, intlanguage('app100001216', '确定删除该模板吗？'), intlanguage('app100001200', '删除后将无法恢复'),
            leftFunStr: intlanguage('app00030', '取消'),
            rightFunStr: intlanguage('app00063', '删除'),
            rightTextColor: ThemeColor.brand, leftFunCall: () {
          return;
        }, rightFunCall: () async {
          templateManager
            ..delete(model, () {
              state.myTemplateList.remove(model);
              IconToast.show(context, intlanguage('app01188', '删除成功'));
              ToNativeMethodChannel.nativeTemplateNeedRefresh();
              // getTemplateList(folderId: state.folderList[state.folderIndex].id!.toInt());
              // refreshTemplateList();
              bool isNeedRequestRefresh = state.myTemplateList.length <= 5;
              resultCallBack?.call(isNeedRequestRefresh);
            });
        });
        break;
      case TemplateEventStatus.move:
        if (!Application.isLogin) {
          String title = intlanguage('app00210', '当前未登录，请先登录！');
          String cancelDes = intlanguage('app00030', '取消');
          String confirmDes = intlanguage('app01191', '立即登录');
          UserLoginHelper().confirmLogin(context, title, cancelDes, confirmDes, loginSucceed: () {});
          return;
        }
        state.templateMoveIndex = state.folderIndex;
        await showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          barrierColor: Color.fromRGBO(0, 0, 0, 0.35),
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
          builder: (BuildContext con) {
            return TemplateMoveListWidget(this, state.folderList[state.folderIndex].id!, () {
              if (state.templateMoveIndex != state.folderIndex) {
                templateManager
                  ..move(model, state.folderList[state.templateMoveIndex].id.toString(), () {
                    IconToast.show(
                        context,
                        intlanguage('app100001202', '移动成功',
                            param: [state.folderList[state.templateMoveIndex].name ?? ""]));
                    onRefreshTemplate();
                    resultCallBack?.call(true);
                  });
              }
            });
          },
        );
        break;
      case TemplateEventStatus.share:
        if (!Application.isLogin) {
          String title = intlanguage('app00210', '当前未登录，请先登录！');
          String cancelDes = intlanguage('app00030', '取消');
          String confirmDes = intlanguage('app01191', '立即登录');
          UserLoginHelper().confirmLogin(context, title, cancelDes, confirmDes, loginSucceed: () {});
          return;
        }
        if (model.isCommodity() && model.hasCustomGoodFields()) {
          showToast(msg: intlanguage('app100001567', '您使用的模板包含自定义字段，暂时无法分享'));
          return;
        }
        //调用原生弹出分享弹窗
        CanvasTemplate.TemplateData canvasTemplate =
            await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(model);
        Map<String, dynamic> json = canvasTemplate.toJson();
        String templateJsonStr = jsonEncode(json);
        ToNativeMethodChannel.shareTemplate(templateJsonStr);
        break;
      case TemplateEventStatus.buyLabel:
        String oneCode = model.getLabelOneCode();
        String link = await getLabelLink(oneCode);
        if (link.isEmpty) {
          return;
        }
        CustomNavigation.gotoNextPage('ToBuyLabelPage', {
          'link': link,
        });
        break;
      case TemplateEventStatus.labelInfo:
        String labelId = model.profile.extra.labelId ?? "";
        String oneCode = model.getLabelOneCode();
        if (labelId.isEmpty && oneCode.isEmpty) {
          return;
        }
        TemplateDetailResult templateDetailResult =
            await TemplateManager().getTemplateDetailByScanCodeOrLabelId(oneCode, labelId);
        Map<String, dynamic> labelJson = templateDetailResult.templateData?.toJson() ?? {};
        if (labelJson.isEmpty) {
          return;
        }
        Item item = Item.fromJson(labelJson);
        item.rawJson = labelJson;
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          barrierColor: Color(0xFF000000).withOpacity(0.35),
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
          builder: (BuildContext context) {
            return LabelDetailsPage(
              item,
              showLabelBg: false,
            );
          },
        ).then((value) {});
        break;
      case TemplateEventStatus.copy:
        if (!Application.isLogin) {
          String title = intlanguage('app00210', '当前未登录，请先登录！');
          String cancelDes = intlanguage('app00030', '取消');
          String confirmDes = intlanguage('app01191', '立即登录');
          UserLoginHelper().confirmLogin(context, title, cancelDes, confirmDes, loginSucceed: () {});
          return;
        }
        initGenerateDesignRatio(context, model);
        bool copyResult = await templateManager.copyTemplate(model, (status, nt) {
          switch (status) {
            case TemplateOperationStatus.localSuccess:
              IconToast.show(context, intlanguage('app100001985', '复制成功'));
              ToNativeMethodChannel.nativeTemplateNeedRefresh();
              if (state.templateScrollController.hasClients) {
                state.templateScrollController.jumpTo(0);
              }
              // getTemplateList(folderId: state.folderList[state.folderIndex].id!.toInt());
              // resultCallBack?.call(true);
              break;
            case TemplateOperationStatus.serverSuccess:
              ToNativeMethodChannel.nativeTemplateNeedRefresh();
              // getTemplateList(folderId: state.folderList[state.folderIndex].id!.toInt());
              resultCallBack?.call(true);
              if (state.templateScrollController.hasClients) {
                state.templateScrollController.jumpTo(0);
              }
              break;
            default:
              break;
          }
        });
        break;
      default:
    }
  }

  initGenerateDesignRatio(BuildContext context, TemplateData template) {
    String widthValue = template.width.toString();
    String hightValue = template.height.toString();
    //使用niimbot_template插件中倍率计算类
    DisplayUtil.init(context);
    DisplayUtil.generateDesignRatio(double.parse(widthValue), double.parse(hightValue));
  }

  Future<String> getLabelLink(String oneCode) async {
    if (MeProfilePresenter.shopState == 0) {
      return "";
    }
    if (oneCode.isEmpty) {
      return "";
    }
    String link = await ToNativeMethodChannel.getLabelShopLink(oneCode, "");
    return link;
  }

  String getTemplateCode(Item labelData) {
    var profile = labelData.profile;
    if (profile == null) {
      return "";
    }
    StringBuffer code = StringBuffer("");
    if (profile.barcode.isNotEmpty) {
      code.write(profile.barcode);
      code.write(",");
    }
    var extrain = profile.extrain;
    if (extrain.virtualBarCode != null && (extrain.virtualBarCode ?? "").isNotEmpty) {
      code.write(extrain.virtualBarCode);
      code.write(",");
    }
    if (extrain.amazonCodeWuhan != null && (extrain.amazonCodeWuhan ?? "").isNotEmpty) {
      code.write(extrain.amazonCodeWuhan);
      code.write(",");
    }
    if (extrain.amazonCodeBeijing != null && (extrain.amazonCodeBeijing ?? "").isNotEmpty) {
      code.write(extrain.amazonCodeBeijing);
      code.write(",");
    }
    if (extrain.sparedCode != null && (extrain.sparedCode ?? "").isNotEmpty) {
      code.write(extrain.sparedCode);
      code.write(",");
    }
    if (extrain.barcodeCategoryMap != null && (extrain.barcodeCategoryMap ?? {}).isNotEmpty) {
      extrain.barcodeCategoryMap?.values.forEach((it) {
        if (it.isNotEmpty) {
          code.write(it);
          code.write(",");
        }
      });
    }
    return code.toString();
  }

  templateBatchManagerEvent(BuildContext context, TemplateEventStatus event, List<TemplateData>? templates,
      {FolderModel? model, Function(bool operateResult)? resultCallBack}) async {
    switch (event) {
      case TemplateEventStatus.batchDelete:
        showCustomDialog(context, intlanguage('app100001201', '确定删除"\$"个模板吗？', param: [templates!.length.toString()]),
            intlanguage('app100001200', '删除后将无法恢复', param: [10.toString()]),
            leftFunStr: intlanguage('app00030', '取消'),
            rightFunStr: intlanguage('app00063', '删除'),
            rightTextColor: ThemeColor.brand, leftFunCall: () {
          return;
        }, rightFunCall: () async {
          templateManager
            ..deleteTemplateList(templates, (bool result) {
              if (!result) return;
              refreshTemplateDelete();
              IconToast.show(context, intlanguage('app01188', '删除成功'));
              ToNativeMethodChannel.nativeTemplateNeedRefresh();
              getTemplateList(folderId: state.folderList[state.folderIndex].id!.toInt());
            });
        });
        break;
      case TemplateEventStatus.batchMove:
        templateManager
          ..moveTemplateList(templates!, model!.id.toString(), (bool result) {
            if (!result) return;
            resultCallBack?.call(true);
            refreshTemplateDelete();
            IconToast.show(context, intlanguage('app100001202', '移动成功', param: [model.name ?? ""]));
            getTemplateList(folderId: state.folderList[state.folderIndex].id!.toInt());
          });
        break;
      default:
    }
  }

  ///刷新模板数据
  onRefreshTemplate({bool isNeedRequestRefresh = true}) {
    state.templatePageState = isNeedRequestRefresh?IndustryHomeState.loading:IndustryHomeState.showContainer;
    refreshTemplateList();
    if(isNeedRequestRefresh){
      getTemplateList(folderId: state.folderList[state.folderIndex].id!.toInt(), isRefresh: true);
    }
  }

  popupOnOpen(int index, String? name, TemplateOperateState operateType) {
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "007_015_219",
    });
    if (operateType == TemplateOperateState.normal || operateType == TemplateOperateState.managerState) {
      ToNativeMethodChannel().sendTrackingToNative({
        "track": "show",
        "posCode": "007_016_248",
      });
    }
    var isRefresh = true;
    if (index == state.folderIndex) {
      isRefresh = false;
    }
    folderselect(index, name);
    if (isRefresh) {
      onRefreshTemplate();
    }
  }

  ///加载模板数据
  onLoadTemplate() {
    state.templatePage += 1;
    getTemplateList(folderId: state.folderList[state.folderIndex].id!.toInt(), isRefresh: false);
  }

  bool templateSelectState(TemplateData template) {
    bool isSelect = false;
    state.myTemplateSelectList.forEach((element) {
      if (element.id == template.id) {
        isSelect = true;
      }
    });
    return isSelect;
  }

  toCanvasPage(BuildContext context, TemplateData model, {bool isFolderShare = false}) async {
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "007_016_025",
      'ext': {"temp_id": model.id, "folder_name": state.folderList[state.folderIndex].name}
    });

    ///分享给我的不能进入画板
    if (isFolderShare) {
      return;
    }
    JCCustomToastUtil().showProgress(context);

    try {
      ///进入画板
      TemplateData? templateData = await TemplateManager().getTemplateDetail(model.id!, needUpdateDb: true);
      if (templateData == null) {
        JCCustomToastUtil().closeProgress(context);
        return;
      }
      //无网络并且商品模版拦截
      if (!Application.networkConnected && templateData.isCommodity()) {
        JCCustomToastUtil().closeProgress(context);
        showToast(msg: intlanguage('app100001136', '该模板不支持离线使用，请连接网络！'));
        return;
      }
      //无网络并且excel模版并且无excel文件缓存拦截
      if (!Application.networkConnected && templateData.isExcel()) {
        bool hasExcelCache = await TemplateMiscUtils.hasExcelCache(templateData.dataSources?[0]?.hash);
        if (!hasExcelCache) {
          JCCustomToastUtil().closeProgress(context);
          showToast(msg: intlanguage('app01139', '网络异常'));
          return;
        }
      }
      bool canEdit = await templateData.canEdit();
      if (!Application.networkConnected && !canEdit) {
        JCCustomToastUtil().closeProgress(context);
        showToast(msg: intlanguage('app01139', '网络异常'));
        return;
      }
      JCCustomToastUtil().closeProgress(context);

      CanvasTemplate.TemplateData canvasTemplate =
          await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(templateData);
      Map<String, dynamic> json = canvasTemplate.toJson();
      CustomNavigation.gotoNextPage('ToTemplatePage', {
        'content': jsonEncode(json),
      });
    } catch (e) {
      JCCustomToastUtil().closeProgress(context);
      LogUtil.e('toCanvasPage error: $e');
      showToast(msg: intlanguage('app01139', '网络异常'));
    }
  }

  toPrintSettingPage(BuildContext context, TemplateData model, TemplateOperateState operateType,
      {bool isFolderShare = false}) async {
    ///进入画板
    // TemplateData? templateData = await TemplateManager().getTemplateDetail(model.id!,needUpdateDb: true);
    JCCustomToastUtil().showProgress(context);
    TemplateDetailResult? templateDetailResult =
        await TemplateManager().getTemplateDetailWithParams(model.id!, isFolderShare: isFolderShare);
    TemplateData? templateData = templateDetailResult?.templateData;
    if (templateDetailResult.isSuccess == false) {
      JCCustomToastUtil().closeProgress(context);
      String errorMsg = "";
      int refreshType = 0;
      if (templateDetailResult.errorCode == 11404) {
        errorMsg = intlanguage('app100001394', '共享资源已过期');
        refreshType = 1;
      } else if (templateDetailResult.errorCode == 10403) {
        errorMsg = intlanguage('app100001399', '共享者VIP已过期');
        refreshType = 2;
      }
      if (errorMsg.isNotEmpty)
        showCustomDialog(context, "", errorMsg,
            justSureButton: true,
            contentTextStyle: TextStyle(
              fontSize: 16,
              color: ThemeColor.mainTitle,
              fontWeight: FontWeight.w600,
            ),
            rightFunStr: intlanguage('app00707', '我知道了'),
            rightTextColor: ThemeColor.COLOR_161616,
            rightFunCall: () {
              if (refreshType == 1) {
                getShareMeFolders();
              } else if (refreshType == 2) {
                changeTabState(isUserMsgChange: true);
              }
            });
      return;
    }
    if (templateData == null) {
      JCCustomToastUtil().closeProgress(context);
      return;
    }
    // 模版版本号校验逻辑
    String? templateVersion = templateData.templateVersion;
    bool isOldTemplate = false;
    bool isOpen = false;

    if (templateVersion == null || templateVersion.isEmpty) {
      isOldTemplate = true;
    } else {
      int compareResult =
          TemplateMiscUtils.compareTemplateVersion(templateVersion, TemplateVersion.getAppMaxSupportTemplateVersion());
      if (compareResult <= 0) {
        isOpen = true;
      }
    }

    if (!isOldTemplate && !isOpen) {
      JCCustomToastUtil().closeProgress(context);
      showToast(msg: intlanguage('app100000343', '版本过低，请升级APP'));
      return;
    }

    if (isFolderShare && templateData.profile.extra.isDelete == true) {
      JCCustomToastUtil().closeProgress(context);
      showCustomDialog(context, "", intlanguage('app100001394', '共享资源已不存在'),
          justSureButton: true,
          contentTextStyle: TextStyle(
            fontSize: 16,
            color: ThemeColor.mainTitle,
            fontWeight: FontWeight.w600,
          ),
          rightFunStr: intlanguage('app00707', '我知道了'),
          rightTextColor: ThemeColor.COLOR_161616,
          rightFunCall: () {});
      return;
    }
    if (!Application.networkConnected && templateData.isCommodity()) {
      JCCustomToastUtil().closeProgress(context);
      showToast(msg: intlanguage('app100001136', '该模板不支持离线使用，请连接网络！'));
      return;
    }
    //无网络并且excel模版并且无excel文件缓存拦截
    if (!Application.networkConnected && templateData.isExcel()) {
      bool hasExcelCache = await TemplateMiscUtils.hasExcelCache(templateData.dataSources?[0]?.hash);
      if (!hasExcelCache) {
        JCCustomToastUtil().closeProgress(context);
        showToast(msg: intlanguage('app01139', '网络异常'));
        return;
      }
    }

    bool canEdit = await templateData.canEdit();
    if (!Application.networkConnected && !canEdit) {
      JCCustomToastUtil().closeProgress(context);
      showToast(msg: intlanguage('app01139', '网络异常'));
      return;
    }
    JCCustomToastUtil().closeProgress(context);

    Future.delayed(Duration(milliseconds: 200), () async {
      CanvasTemplate.TemplateData canvasTemplate =
          await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(templateData);
      Map<String, dynamic> json = canvasTemplate.toJson();
      String templateJsonStr = jsonEncode(json);
      bool result = await ToNativeMethodChannel().downloadTemplateFonts(templateJsonStr);
      if (templateData.isContainInstantTime() && !VipHelper.isVip()) {
        TrialActivityType activityType = TrialActivityType.undefined;
        VipTrialManager().trialActivities?.forEach((key, value) {
          if (value.getActivityType().privilegeCode == "INSTANT_TIME_PRINT") {
            activityType = value.getActivityType();
          }
        });
        VipTrialManager().trialActivity(
            context: context,
            trialActivityType: activityType,
            trailActivity: () {
              _jumpToPrintSettingPage(templateData, isFolderShare);
            });
      } else if (templateData.needVipV2() && !VipHelper.isVip()) {
        VIPType vipType = VipHelper.getVipType();
        if (vipType == VIPType.expired) {
          VipHelper.showVipExpiredDialog(context, (buyResult) {
            if (buyResult) {
              _jumpToPrintSettingPage(templateData, isFolderShare);
            }
          });
        } else {
          VipHelper.showOpenVipDialog(context, (buyResult) {
            if (buyResult) {
              _jumpToPrintSettingPage(templateData, isFolderShare);
            }
          });
        }

        return;
      } else {
        _jumpToPrintSettingPage(templateData, isFolderShare);
      }
    });
  }

  _jumpToPrintSettingPage(TemplateData templateData, bool isFolderShare) async {
    CanvasTemplate.TemplateData canvasTemplate =
        await TemplateTransformUtils.niimbotTemplateToCanvasTemplate(templateData);
    Map<String, dynamic> json = canvasTemplate.toJson();

    CustomNavigation.gotoNextPage(
        'ToPrintSettingPage', {'content': jsonEncode(json), 'needDownloadRes': false, 'isFolderShare': isFolderShare});
  }

  templateItemMoreClick(TemplateData model, TemplateOperateState operateType) {
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "007_016_220",
      'ext': {"temp_id": model.id, "folder_name": state.folderList[state.folderIndex].name}
    });
  }

  trackMoreOperationEvent(String templateId, TemplateEventStatus event, String? folderName) {
    String posCode = "";
    switch (event) {
      case TemplateEventStatus.reName:
        posCode = "007_447_442";
        break;
      case TemplateEventStatus.delete:
        posCode = "007_447_448";
        break;
      case TemplateEventStatus.move:
        posCode = "007_447_443";
        break;
      case TemplateEventStatus.copy:
        posCode = "007_447_444";
        break;
      case TemplateEventStatus.share:
        posCode = "007_447_445";
        break;
      case TemplateEventStatus.buyLabel:
        posCode = "007_447_446";
        break;
      case TemplateEventStatus.labelInfo:
        posCode = "007_447_447";
        break;
      default:
    }
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": posCode,
      'ext': {"temp_id": templateId, "folder_name": folderName}
    });
  }
}

///刷新操作
extension GetxControllerUpdate on GetxControllerAbstract {
  refreshFolderList() {
    update([RefreshManager.folderList]);
  }

  refreshTemplateList() {
    update([RefreshManager.templateList]);
  }

  refreshMoveTemplateWidget() {
    update([RefreshManager.templateMove]);
  }

  void showLoading() {
    EasyLoading.show(dismissOnTap: false);
  }

  void dismissLoading() {
    EasyLoading.dismiss();
  }
}
