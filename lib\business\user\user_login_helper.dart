import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart' hide Logger;
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:text/application.dart';

import '../../utils/common_fun.dart';
import '../../utils/toast_util.dart';
import '../../widget/base_dialog.dart';

class UserLoginHelper {
  static final Logger _logger = Logger("UserLoginHelper", on:kDebugMode);
  UserLoginHelper._internal();

  factory UserLoginHelper() => _instance;

  static late final UserLoginHelper _instance = UserLoginHelper._internal();

  VoidCallback? cancelLoginCallback;

  customLogin(BuildContext context, Function function, {String title = "", String cancelDes = "", String confirmDes = ""}) {
      (Connectivity().customCheckConnectivity()).then((value) {
        if (Application.user == null) {
          if (value == ConnectivityResult.none || value == ConnectivityResult.bluetooth) {
            showToast(msg: intlanguage('app100000625', '当前网络状态异常'));
            return;
          }
          String titleText = title.isNotEmpty ? title: intlanguage('app00210', '当前未登录，请先登录！');
          String cancelDesText = cancelDes.isNotEmpty ? cancelDes: intlanguage('app00030', '取消');
          String confirmDesText = confirmDes.isNotEmpty ? confirmDes: intlanguage('app01191', '立即登录');
          UserLoginHelper().confirmLogin(context,
              titleText,
              cancelDesText,
              confirmDesText,
              loginFailed: () {
                  function.call(false);
              },
              loginSucceed: () {
                  function.call(true);
              });
          return;
        } else {
          function.call(true);
        }
      });
  }

  confirmLogin(BuildContext context, String title, String cancelDes, String confirmDes,
      {bool needGetNiimbotAccount = true, bool isNavigatorManager = true, Function()? loginSucceed, Function()? loginFailed}) {
    if (Application.user != null) {
      if (loginSucceed != null) loginSucceed();
      return;
    }
    BaseDialog.showLoginStyleDialog(context, title: title, cancelDes: cancelDes, confirmDes: confirmDes,
        confirmAction: () {
          BoostNavigator.instance.push('login',
              arguments: {
            'needGetNiimbotAccount': needGetNiimbotAccount,
            'isNavigatorManager': isNavigatorManager,}).then((result) {
              Future.delayed(const Duration(milliseconds: 1000), () {
              Application.user != null ?
              loginSucceed != null ? loginSucceed() : null
                  : loginFailed != null ? loginFailed() : null;
            });
          });
        });
  }

  gotoLogin(BuildContext context, {bool needGetNiimbotAccount = true, bool isNavigatorManager = true, Function()? loginSucceed, Function()? loginFailed}) {
    if (Application.user != null) {
      if (loginSucceed != null) loginSucceed();
      return;
    }
    bool hasCallback = false;
    cancelLoginCallback = () {
      if(hasCallback) {
        return;
      }
      hasCallback = true;
      cancelLoginCallback = null;
      loginFailed?.call();
    };
    BoostNavigator.instance.push('login',
        arguments: {
      'needGetNiimbotAccount': needGetNiimbotAccount,
      'isNavigatorManager': isNavigatorManager,}).then((result) {
        Future.delayed(const Duration(milliseconds: 1000), () {
          if(hasCallback) {
            return;
          }
          hasCallback = true;
          cancelLoginCallback = null;
          Application.user != null ? loginSucceed?.call() : loginFailed?.call();
        });
    });
  }
}
