import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/user_center/canvas_user_center.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/localization/localization_public.dart';
import 'package:niimbot_flutter_canvas/src/model/font/font_item.dart';
import 'package:niimbot_flutter_canvas/src/utils/loading_mix.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/font_file_manager.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/svg_icon.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';

import '../../dialog/dialog_font_copyright.dart';

///字体列表面板（每个字体分类下的字体列表）
class FontListWidget extends StatefulWidget {
  final String currentFontCode;
  final String? categoryId;
  final Function(FontItem) onChooseFont;
  final Function(Function)? guideBuyVip;

  FontListWidget({
    super.key,
    required this.currentFontCode,
    this.categoryId,
    required this.onChooseFont,
    this.guideBuyVip,
  });

  @override
  State<StatefulWidget> createState() => FontListWidgetState();
}

class FontListWidgetState extends State<FontListWidget> {
  List<FontItem> itemList = [];
  FontItem? _selectedFontData;

  @override
  void initState() {
    super.initState();
    var items = FontManager.sharedInstance().fontGroup[widget.categoryId];
    if (widget.categoryId != "-1") {
      items = _sortFontList(items);
    }
    if (items != null) {
      itemList.addAll(items);
    }
    initSelectedFont();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.categoryId == "-1") {
        if (itemList.isEmpty || !FontManager.sharedInstance().alreadyUpdateUserFontList) {
          _requestUserFontList();
        }
      } else {
        ///列表数据为空并且没有加载过的情况下则重新加载（网上拉取或者本地读取都会被标记为已经加载过），因为列表数据本身有可能是空的，列表数据也不会经常发生变动
        if (itemList.isEmpty && !FontManager.sharedInstance().alreadyUpdateFontList) {
          _requestFontList();
        }
      }
    });
  }

  void initSelectedFont() {
    if (FontManager.sharedInstance().getConstructAllFontList().isNotEmpty) {
      _selectedFontData = FontManager.sharedInstance()
          .getConstructAllFontList()
          .firstWhere((element) => element.code == widget.currentFontCode, orElse: () {
        return null;
      });
      if (widget.categoryId == "-1" && _selectedFontData != null) {
        ///选中字体排在第一位
        FontManager.sharedInstance().updateUserFont(_selectedFontData!);
        var items = FontManager.sharedInstance().fontGroup[widget.categoryId];
        if (items != null) {
          itemList.clear();
          itemList.addAll(items);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return itemList.isEmpty
        ? Container()
        : ListView.separated(
            itemCount: itemList.length,
            itemBuilder: (BuildContext context, int index) {
              return buildTile(itemList[index], index);
            },
            separatorBuilder: (BuildContext context, int index) {
              return new Container(height: 0.5, color: CanvasTheme.of(context).attributeDividerColor);
            },
          );
  }

  Widget buildTile(FontItem e, int index) {
    return TextFontItemWidget(
      fontData: e,
      selected: _selectedFontData == e,
      guideBuyVip: widget.guideBuyVip,
      refreshList: () {
        setState(() {});
      },
      onChooseFont: (e) {
        //更新最近使用
        FontManager.sharedInstance().updateUserFont(e);
        setState(() {
          _selectedFontData = e;
          widget.onChooseFont.call(e);
        });
      },
      onSelectedButFileNotExist: () {
        /// 已选中, 但文件并未下载, 重置选中项
        Future.delayed(Duration(milliseconds: 50), () {
          setState(() {
            _selectedFontData = null;
          });
        });
      },
    );
  }

  _requestFontList() {
    FontManager.sharedInstance().getAllFontList().then((value) {
      var items = FontManager.sharedInstance().fontGroup[widget.categoryId];
      items = _sortFontList(items);
      itemList.clear();
      if (items != null) {
        itemList.addAll(items);
      }
      initSelectedFont();
      if (mounted) {
        setState(() {});
      }
    });
  }

  List<FontItem>? _sortFontList(List<FontItem>? items) {
    if (items == null) {
      return null;
    }

    ///先按照download time进行排序，再按照priority
    List<FontItem> sortItems = [];
    List<FontItem> itemsNoTime = [];
    List<FontItem> itemsHasTime = [];
    items.forEach((element) {
      if (element.downloadTime == 0) {
        itemsNoTime.add(element);
      } else {
        itemsHasTime.add(element);
      }
    });

    itemsNoTime.sort((a, b) {
      return a.priority - b.priority;
    });
    itemsHasTime.sort((a, b) {
      return b.downloadTime - a.downloadTime;
    });
    sortItems.addAll(itemsHasTime);
    sortItems.addAll(itemsNoTime);
    return sortItems;
  }

  _requestUserFontList() {
    FontManager.sharedInstance().getUserFontList().then((value) {
      List<FontItem>? items = FontManager.sharedInstance().fontGroup[widget.categoryId];
      itemList.clear();
      if (items != null) {
        itemList.addAll(items);
      }
      initSelectedFont();
      if (mounted) {
        setState(() {});
      }
    });
  }
}

class TextFontItemWidget extends StatefulWidget {
  final FontItem fontData;
  final bool selected;
  final Function(FontItem)? onChooseFont;
  final Function onSelectedButFileNotExist;
  final Function(Function)? guideBuyVip;
  final VoidCallback refreshList;

  const TextFontItemWidget({
    super.key,
    required this.fontData,
    required this.selected,
    this.onChooseFont,
    required this.onSelectedButFileNotExist,
    this.guideBuyVip,
    required this.refreshList,
  });

  @override
  State<TextFontItemWidget> createState() => _TextFontItemWidgetState();
}

class _TextFontItemWidgetState extends State<TextFontItemWidget> {
  static Logger _logger = Logger("_TextFontItemWidgetState", on: kDebugMode);
  late FontFileState _fontFileState;

  @override
  void initState() {
    super.initState();
    _fontFileState = FontFileManager().getFontFileState(widget.fontData);
    _fontFileState.addListener(refresh);
  }

  @override
  void dispose() {
    super.dispose();
    _fontFileState.removeListener(refresh);
  }

  refresh() {
    setState(() {});
  }

  Widget getNewWidget() {
    return Offstage(
      offstage: !FontFileManager().isShowRedDot(widget.fontData),
      child: Container(
        margin: EdgeInsetsDirectional.fromSTEB(widget.fontData.hasVip() ? 4 : 10, 0, 0, 0),
        padding: EdgeInsetsDirectional.fromSTEB(3, 2, 3, 2),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.0),
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [
              Color(0xFFFFEDEC), //.withAlpha(13),
              Color(0xFFFFD9D7), //withAlpha(92),
            ],
          ),
        ),
        child: Text(
          "New",
          style: const TextStyle(color: Color(0xFFFB4B42), fontSize: 9, fontWeight: FontWeight.w700),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    const double thumbnailImageHeight = 18.0;

    /// 已选中, 但文件不存在
    // if (widget.selected && _fontFileState.state == FontFileStateValue.negation) {
    //   widget.onSelectedButFileNotExist?.call();
    // }
    _logger.log("widget.selected ${widget.selected} fontcode=${widget.fontData.code}");

    return InkWell(
      onTap: () {
        FontFileManager().clickFont(widget.fontData);
        if (_fontFileState.state == FontFileStateValue.exist) {
          widget.onChooseFont?.call(widget.fontData);
        } else if (_fontFileState.state == FontFileStateValue.negation) {
          if (FontFileManager().downloadNum >= 3) {
            LoadingMix.showToast(intlanguage("app100000539", "最多同时进行三个任务"));
            return;
          }
          if (!(CanvasPluginManager().fontPanelImpl?.isNetReachable() ?? false)) {
            LoadingMix.showToast(intlanguage('app01139', '网络异常'));
            return;
          }
          FontFileManager().downloadFontFile(_fontFileState, downloadEvent: () {
            if (mounted) {
              setState(() {
                widget.onChooseFont?.call(widget.fontData);
              });
            }
          });
        }
      },
      child: Container(
        height: 58,
        alignment: Alignment.center,
        padding: EdgeInsetsDirectional.fromSTEB(12, 0, 24, 0),
        child: Row(children: [
          Opacity(
            child: SvgIcon(
              'assets/element/attribute/icon_select.svg',
              useDefaultColor: false,
            ),
            opacity: widget.selected ? 1 : 0,
          ),
          SizedBox(
            width: 8,
          ),
          Expanded(
            child: Align(
              alignment: AlignmentDirectional.centerStart,
              child: LayoutBuilder(
                builder: (context, constraints) {
                  bool hasThumbnail = widget.fontData.thumbnailUrl?.isNotEmpty ?? false;
                  bool hasName =
                      widget.fontData.showSystemName == true && (widget.fontData.name?.trim().isNotEmpty ?? false);
                  // name兜底 避免空行
                  if (!hasThumbnail) {
                    hasName = true;
                  }
                  if (hasThumbnail) {
                    // 有缩略图时，缩略图和VIP同行，字体名在下方
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Opacity(
                                opacity: _fontFileState.state == FontFileStateValue.none ||
                                        _fontFileState.state == FontFileStateValue.negation
                                    ? 0.5
                                    : 1.0,
                                child: CachedNetworkImage(
                                  height: thumbnailImageHeight,
                                  imageUrl: widget.fontData.thumbnailUrl!,
                                  imageBuilder: (context, imageProvider) {
                                    if (widget.fontData.thumbnailImageSize == null) {
                                      imageProvider
                                          .resolve(new ImageConfiguration())
                                          .addListener(new ImageStreamListener((ImageInfo info, bool _) {
                                        Future.delayed(Duration(milliseconds: 50), () {
                                          if (mounted) {
                                            setState(() {
                                              widget.fontData.thumbnailImageSize = Size(
                                                thumbnailImageHeight * info.image.width / info.image.height,
                                                thumbnailImageHeight,
                                              );
                                            });
                                          }
                                        });
                                      }));
                                      return Container(
                                        width: thumbnailImageHeight,
                                        height: thumbnailImageHeight,
                                      );
                                    }
                                    return Container(
                                      width: widget.fontData.thumbnailImageSize!.width,
                                      height: widget.fontData.thumbnailImageSize!.height,
                                      decoration: BoxDecoration(
                                        image: DecorationImage(image: imageProvider, fit: BoxFit.fill),
                                      ),
                                    );
                                  },
                                  errorWidget: (_, __, ___) {
                                    return Container();
                                  },
                                )),
                            if (widget.fontData.hasVip())
                              Padding(
                                padding: EdgeInsetsDirectional.only(start: 6),
                                child: SvgIcon(
                                  'assets/common/vip_corner.svg',
                                  useDefaultColor: false,
                                ),
                              ),
                          ],
                        ),
                        if (hasName)
                          Opacity(
                              opacity: _fontFileState.state == FontFileStateValue.none ||
                                      _fontFileState.state == FontFileStateValue.negation
                                  ? 0.5
                                  : 1.0,
                              child: Container(
                                margin: EdgeInsets.only(top: 5),
                                child: Text(
                                  widget.fontData.fileName!,
                                  style: TextStyle(fontSize: 12, color: Color(0x993C3C43)),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              )),
                      ],
                    );
                  } else if (hasName) {
                    // 只有字体名称时，字体名和VIP同行
                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          child: Text(
                            widget.fontData.name!,
                            style: TextStyle(fontSize: 14),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (widget.fontData.hasVip())
                          Padding(
                            padding: EdgeInsets.only(left: 6),
                            child: SvgIcon(
                              'assets/common/vip_corner.svg',
                              useDefaultColor: false,
                            ),
                          ),
                      ],
                    );
                  } else {
                    // 都没有时返回空
                    return SizedBox.shrink();
                  }
                },
              ),
            ),
          ),
          getNewWidget(),
          // Spacer(),
          Visibility(
            visible: widget.fontData.hasCopyRight(),
            child: GestureDetector(
              onTap: () {
                showFontCopyrightDialog(context, widget.fontData);
              },
              child: Container(
                height: 50,
                width: 50,
                alignment: Alignment.center,
                child: SvgIcon(
                  'assets/element/attribute/icon_font_info.svg',
                  useDefaultColor: false,
                ),
              ),
            ),
          ),
          if (_fontFileState.state == FontFileStateValue.downloading)
            Container(
              width: 22,
              height: 22,
              padding: EdgeInsets.all(2),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    width: 22,
                    height: 22,
                    decoration: BoxDecoration(
                        shape: BoxShape.circle, border: Border.all(color: Colors.black.withAlpha(20), width: 2)),
                  ),
                  CircularProgressIndicator(
                    value: _fontFileState.downloadProgress,
                    color: CanvasTheme.of(context).highlightColor,
                    strokeWidth: 2,
                  ),
                ],
              ),
            )
          else if (_fontFileState.state == FontFileStateValue.negation)
            SvgIcon(
              'assets/element/attribute/text_font_download.svg',
              useDefaultColor: false,
            )
          else if (_fontFileState.state == FontFileStateValue.exist &&
              !CanvasUserCenter().isVip == true &&
              widget.fontData.isVip == true)
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                widget.guideBuyVip?.call(widget.refreshList);
              },
              child: SvgIcon(
                'assets/element/attribute/icon_font_lock.svg',
                useDefaultColor: false,
              ),
            ),
        ], mainAxisAlignment: MainAxisAlignment.start, crossAxisAlignment: CrossAxisAlignment.center),
      ),
    );
  }
}
