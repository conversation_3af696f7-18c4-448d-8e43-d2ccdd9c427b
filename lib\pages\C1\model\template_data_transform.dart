import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:isolate';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/precision_num.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:netal_plugin/netal_plugin.dart';
import 'package:netal_plugin/niimbot_netal.dart';
import 'package:niimbot_flutter_canvas/src/utils/isolate_util.dart';
import 'package:niimbot_template/models/copy_wrapper.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/font_size_config.dart';
import 'package:niimbot_template/models/template/constants.dart';
import 'package:niimbot_template/models/template/tube_file_setting.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/models/values/composite_value_type.dart';
import 'package:niimbot_template/models/values/serial_value_type.dart';
import 'package:niimbot_template/models/values/template_value_type.dart';
import 'package:niimbot_template/models/values/text_value_type.dart';
import 'package:text/pages/C1/model/c1_print_manager.dart';
import 'package:text/pages/C1/model/define_model.dart';
import 'package:text/pages/C1/model/text_info.dart';
import 'package:text/pages/C1/model/text_netal_generate_param.dart';
import 'package:text/pages/C1/model/text_netal_image_result.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/isolate_cancel_token.dart';
import 'package:tuple/tuple.dart';
import 'package:uuid/uuid.dart';

class TemplateDataTransform {
  static late Size deviceSize;

  static double devicePixelRatio = 3;

  /// 画布背景横向 margin 大小，设计像素(dp)
  static double designHMargin = 16;

  /// 画布背景纵向 margin 大小，设计像素(dp)
  static double designVMargin = 6;

  /// 组件显示的倍率(基于屏幕设计倍率)
  static double dpRatio = 1;

  /// 待打印的模板列表（老模板数据结构）
  static List<TemplateData> printTemplateList = [];

  /// 生成图片的元素jsonString，{元素id：元素jsonString}
  static Map<String, String> jsonStringMap = {};

  /// 自动长度最大宽度100mm
  static double adaptMaxWidth = 100.0;

  /// 自动长度最大宽度100mm对应屏幕的逻辑像素
  static get adaptMaxWidthDp => mm2dp(adaptMaxWidth);

  static Color? ribbonColor = null;

  static void init(BuildContext context) {
    deviceSize = MediaQuery.of(context).size;
    devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
  }

  /// 图像库用于生成图像的倍率(显示倍率 * 屏幕倍率) mm -> px （1mm对应多少真实像素点）
  static double get pxRatio => dpRatio * devicePixelRatio;

  /// 计算当前模板在当前屏幕设备上的设计倍率 mm -> pt （1mm对应多少逻辑像素点）
  static double generateDesignRatio(double templateWidth, double templateHeight) {
    if (Platform.isAndroid || Platform.isIOS) {
      double horizontalDpRatio = ((deviceSize.width - designHMargin * 2) / templateWidth).digits(2).toDouble();
      double verticalDpRatio = ((deviceSize.height - 475) / templateHeight).digits(2).toDouble();
      dpRatio = min(horizontalDpRatio, verticalDpRatio) < 0 ? 8.0 : min(horizontalDpRatio, verticalDpRatio);
    } else {
      dpRatio = 8.0;
    }
    return dpRatio;
  }

  /// 图像库图片px转换到画板设计像素mm
  static double px2mm(int px) {
    return (px / pxRatio).digits(6).toDouble();
  }

  /// 图像库图片px转换到画板设计像素pt
  static double px2dp(int px) {
    return double.parse((px / pxRatio * dpRatio).toStringAsFixed(6));
  }

  ///画板设置像素pt转mm
  static double dp2mm(double dp) {
    return (dp / dpRatio).digits(6).toDouble();
  }

  ///画板设置mm转像素pt
  static double mm2dp(double mm) {
    return mm * dpRatio;
  }

  /// 生成 id
  static String generateId() {
    return Uuid().v4().replaceAll("-", "");
  }

  /// 编辑模板安全区域宽度
  static double getTemplateEditSafeWidth(TemplateData templateData) {
    double templateWidth = templateData.width.toDouble();
    // 计算Margin
    List<double> margin = templateData.margin.map((e) => e.toDouble()).toList();
    bool autoWidth = templateData.tubeFileSetting?.autoWidth == true;
    double safeWidth = getEditSafeWidth(templateWidth, margin, autoWidth: autoWidth);
    return safeWidth;
  }

  static double getEditSafeWidth(double templateWidth, List<double> margin, {bool autoWidth = false}) {
    // 自动长度，最大宽度为100mm
    if (autoWidth) {
      return adaptMaxWidth - (margin[1] + margin[3]);
    }
    // 查看是否当前宽度溢出模板的安全宽度
    return templateWidth - (margin[1] + margin[3]);
  }

  ///编辑模板安全区域高度
  static double getTemplateEditSafeHeight(TemplateData templateData) {
    double templateHeight = templateData.height.toDouble();
    // 计算Margin
    List<double> margin = templateData.margin.map((e) => e.toDouble()).toList();
    bool doubleLine = (templateData.tubeFileSetting?.line ?? 1) > 1;
    double safeHeight = getEditSafeHeight(templateHeight, margin, doubleLine: doubleLine);
    return safeHeight;
  }

  static double getEditSafeHeight(double templateHeight, List<double> margin, {bool doubleLine = false}) {
    // 计算Margin
    // List<double> margin = templateData.margin.map((e) => e.toDouble()).toList();
    if (doubleLine) {
      //每行上下留白0dp，每行虚线框宽度0dp，另外两行中间的虚线高度为1dp（0 * 2 * 2 + 0 * 2 * 2 + 1 = 1dp）
      return (templateHeight /*- (margin[0] + margin[2])*/ - TemplateDataTransform.dp2mm(1.0)) / 2.0;
    } else {
      return templateHeight /*- (margin[0] + margin[2])*/;
    }
  }

  /// 生成元素预览图
  /// [element] 元素样式
  /// [valueType] 数据类型
  /// [page] 第几页
  /// [index] 第几份
  static Tuple2<String, NetalImageResult>? generateElementImage(
      {required num width,
      required num height,
      required BaseElement? element,
      required ValueTypeProtocol? valueType,
      int page = 1,
      int index = 1,
      Color? color,
      bool usePlaceHolder = false,
      Map<String, String>? usedFonts,
      double? ratio}) {
    // 生成当前元素的值
    String realValue = valueType?.generateElementValue(page: page, index: index) ?? "";
    String value;
    Color? elementColor;
    if (realValue.isEmpty && usePlaceHolder) {
      value = intlanguage('app100000972', '请输入') + '...';
      elementColor = Color(0x4D3C3C43);
    } else {
      value = realValue;
      elementColor = ribbonColor;
    }
    // 组装元素的样式以及文本信息
    switch (element.runtimeType) {
      case TextElement:
        // 此处的rotate用于图像库预览时旋转由于应用层处理，将 rotate 处理成 0 给图像库
        TextElement generateElement = (element as TextElement).copyWith(value: value, rotate: 0);
        // 生成元素预览图
        num? _width = generateElement.typesettingMode == NetalTypesettingMode.horizontal ? null : height;
        // 竖排时增长设置最大值100
        num? _height = generateElement.typesettingMode == NetalTypesettingMode.horizontal ? null : 100;
        Map<String, dynamic> json = generateElement
            .toNetal()
            .copyWith(value: generateElement.value, rotate: 0, width: _width, height: _height)
            .toJson();
        json['lineMode'] = generateElement.typesettingMode == NetalTypesettingMode.horizontal ? 7 : 2;
        Map<String, dynamic> map = {
          'width': width.toDouble(),
          'height': height.toDouble(),
          'elements': [json],
          'usedFonts': usedFonts ?? NetalPlugin().getUsedFonts(),
        };
        String jsonString = jsonEncode(map);
        NetalImageResult imageData = NiimbotNetal.generateImageFromElementJson(
          jsonString: jsonString,
          ratio: ratio ?? pxRatio,
          color: elementColor,
        );
        // NetalImageResult imageData = NetalPlugin().generateTextElement(
        //     size: Size(width.toDouble(), height.toDouble()),
        //     element: generateElement.toNetal().copyWith(value: generateElement.value),
        //     ratio: pxRatio,
        //     color: placeHolderColor ? ThemeColor.COLOR_FFBFBFBF : color);
        return Tuple2(realValue, imageData);

      /// TODO: 其他的element类型
    }
    return null;
  }

  static NetalImageResult generateTextImage(double safeWidth, double safeHeight, TextElement textElement) {
    // 此处的rotate用于图像库预览时旋转由于应用层处理，将 rotate 处理成 0 给图像库
    TextElement generateElement = textElement.copyWith(rotate: 0);
    // 生成元素预览图
    num? _width = generateElement.typesettingMode == NetalTypesettingMode.horizontal ? null : safeHeight;
    // 竖排时增长设置最大值100
    num? _height = generateElement.typesettingMode == NetalTypesettingMode.horizontal ? null : 100;
    Map<String, dynamic> json = generateElement
        .toNetal()
        .copyWith(value: generateElement.value, rotate: 0, width: _width, height: _height)
        .toJson();
    json['lineMode'] = generateElement.typesettingMode == NetalTypesettingMode.horizontal ? 7 : 2;
    Map<String, dynamic> map = {
      'width': safeWidth,
      'height': safeHeight,
      'elements': [json],
      'usedFonts': NetalPlugin().getUsedFonts(),
    };
    String jsonString = jsonEncode(map);
    NetalImageResult imageResult = NiimbotNetal.generateImageFromElementJson(
      jsonString: jsonString,
      ratio: pxRatio
    );
    return imageResult;
  }

  static dispose() {
    printTemplateList.clear();
    jsonStringMap.clear();
  }

  static int getAllParagraphCount(TemplateData templateData) {
    int? paragraphCount = templateData.tubeFileSetting?.paragraphCount;
    if(paragraphCount != null) {
      return paragraphCount;
    }
    return calcAllParagraphCount(templateData);
  }

  static int calcAllParagraphCount(TemplateData templateData) {
    int count = 0;
    bool doubleLine = (templateData.tubeFileSetting?.line ?? 1) > 1;
    if(doubleLine) {
      for(int index = 0; index < templateData.values!.length; index++) {
        CompositeValueType value = templateData.values![index] as CompositeValueType;
        int repeatCount = max(value.repeatCount ?? 1, 1);
        CompositeValueType columnValueOne = value.valueObjects![0] as CompositeValueType;
        CompositeValueType columnValueTwo = value.valueObjects![1] as CompositeValueType;
        int serialCount = max(C1PrintManager.instance.getColumnSerialCount(columnValueOne), C1PrintManager.instance.getColumnSerialCount(columnValueTwo));
        count += repeatCount * serialCount;
      }
    }
    else{
      for(int index = 0; index < templateData.values!.length; index++) {
        CompositeValueType value = templateData.values![index] as CompositeValueType;
        int repeatCount = max(value.repeatCount ?? 1, 1);
        CompositeValueType columnValue = value.valueObjects![0] as CompositeValueType;
        int serialCount = C1PrintManager.instance.getColumnSerialCount(columnValue);
        count += repeatCount * serialCount;
      }
    }
    return count;
  }
}

extension EditTemplateDataTransform on TemplateDataTransform {
  /// 创建默认段落
  /// [templateWidth]模板宽度
  /// [templateHeight]模板高度
  /// [margin]盲区（上、右、下、左），传入的高度已经剔除了上下盲区
  /// [textFormat]单行或双行
  /// [paragraphAlignment]文本对齐方式：在段落居中或居左
  static Tuple2<List<TextElement>, ValueTypeProtocol> createDefaultElementValue(double templateWidth,
      double templateHeight, List<num> margin, TextFormat textFormat, NetalTextAlign textAlignHorizontal) {
    List<TextElement> textElements = [];
    ValueTypeProtocol valueTypeProtocol;
    // 创建去除盲区的可打印宽高
    double safeWidth = TemplateDataTransform.getEditSafeWidth(templateWidth, margin.map((e) => e.toDouble()).toList());
    // 创建去除盲区的可打印高度
    double safeHeight = TemplateDataTransform.getEditSafeHeight(
        templateHeight, margin.map((e) => e.toDouble()).toList(),
        doubleLine: textFormat == TextFormat.doubleLine);
    if (textFormat == TextFormat.singleLine) {
      // 创建默认文本格式
      TextElement defaultTextElement = createDefaultElement(
          width: safeWidth,
          height: safeHeight * 2 / 3,
          textAlignHorizontal: textAlignHorizontal);
      // 创建默认值
      valueTypeProtocol = createSingleLineValue(elementId: defaultTextElement.id);
      // 预先渲染一次获取高度一半下的最佳字号
      final res = TemplateDataTransform.generateElementImage(
          width: safeWidth,
          height: safeHeight * 2 / 3,
          element: defaultTextElement,
          valueType: valueTypeProtocol,
          usePlaceHolder: true);
      String? value = res?.item1;
      NetalImageResult? result = res?.item2;
      // 计算坐标
      // px转换为mm宽高, 计算文本占据尺寸
      double elementWidth = TemplateDataTransform.px2mm(result?.width ?? 0);
      double elementHeight = TemplateDataTransform.px2mm(result?.height ?? 0);
      // 根据对齐方式确认x、y坐标,要加上盲区区域
      double x = textAlignHorizontal == NetalTextAlign.center
          ? (safeWidth - elementWidth) / 2.0 + margin[3]
          : margin[3].toDouble();
      double y = (safeHeight - elementHeight) / 2.0 /* + margin[0]*/;
      textElements.add(defaultTextElement.copyWith(
          x: x,
          y: y,
          width: elementWidth,
          height: elementHeight,
          value: value ?? "",
          boxStyle: NetalTextBoxStyle.autoWidth,
          fontSize: result?.fontSize));
    } else {
      String elementId1 = TemplateDataTransform.generateId();
      String elementId2 = TemplateDataTransform.generateId();
      // 创建默认文本格式
      TextElement defaultTextElement = createDefaultElement(
          width: safeWidth,
          height: safeHeight,
          elementId: elementId1,
          textAlignHorizontal: textAlignHorizontal);
      // 创建默认值
      valueTypeProtocol = createDoubleLineDefaultValue(elementId1: elementId1, elementId2: elementId2);
      // 预先渲染一次获取高度一半下的最佳字号
      final res = TemplateDataTransform.generateElementImage(
          width: safeWidth,
          height: safeHeight,
          element: defaultTextElement,
          valueType: valueTypeProtocol,
          usePlaceHolder: true);
      String? value = res?.item1;
      NetalImageResult? result = res?.item2;
      // 计算坐标
      // px转换为mm宽高, 计算文本占据尺寸
      double elementWidth = TemplateDataTransform.px2mm(result?.width ?? 0);
      double elementHeight = TemplateDataTransform.px2mm(result?.height ?? 0);
      // 根据对齐方式确认x、y坐标,要加上盲区区域
      double x = textAlignHorizontal == NetalTextAlign.center
          ? (safeWidth - elementWidth) / 2.0 + margin[3]
          : margin[3].toDouble();
      //虚线框1.5dp
      double y1 = (safeHeight - elementHeight) / 2.0 + TemplateDataTransform.dp2mm(1.5) /* + margin[0]*/;
      textElements.add(defaultTextElement.copyWith(
          id: elementId1,
          x: x,
          y: y1,
          width: elementWidth,
          height: elementHeight,
          value: value ?? "",
          boxStyle: NetalTextBoxStyle.autoWidth,
          fontSize: result?.fontSize));
      // double y2 = y1 + safeHeight + TemplateDataTransform.dp2mm(3.0);
      double y2 = templateHeight / 2.0 + (safeHeight - elementHeight) / 2.0 + TemplateDataTransform.dp2mm(2.0);
      textElements.add(defaultTextElement.copyWith(
          id: elementId2,
          x: x,
          y: y2,
          width: elementWidth,
          height: elementHeight,
          boxStyle: NetalTextBoxStyle.autoWidth,
          fontSize: result?.fontSize));
    }
    return Tuple2(textElements, valueTypeProtocol);
  }

  /// 创建默认段落文本样式
  /// [width] 宽度
  /// [height] 高度
  static TextElement createDefaultElement(
      {required double width,
      required double height,
      String? elementId,
      NetalTextAlign? textAlignHorizontal = NetalTextAlign.center}) {
    return TextElement(
        id: elementId ?? TemplateDataTransform.generateId(),
        x: 0,
        y: 0,
        width: width,
        height: height,
        textAlignHorizontal: textAlignHorizontal,
        textAlignVertical: NetalTextAlign.center,
        rotate: 0,
        wordSpacing: 0,
        letterSpacing: 0,
        lineSpacing: 0,
        fontFamily: '',
        fontStyle: [],
        typesettingMode: NetalTypesettingMode.horizontal,
        typesettingParam: [0, 180],
        colorReverse: false,
        colorChannel: 0,
        boxStyle: NetalTextBoxStyle.fixedWidthHeight,
        textStyle: [NetalTextStyle.adaptive],
        value: '');
  }

  /// 创建单行默认段落文本
  static ValueTypeProtocol createSingleLineValue({required String elementId, String value = ''}) {
    // 生成段落ID
    String paragraphID = TemplateDataTransform.generateId();
    CompositeValueType compositeValueType = CompositeValueType(
        id: paragraphID,
        elementId: paragraphID,
        type: TemplateValueType.composite,
        value: '',
        order: 1,
        delimiter: '',
        repeatCount: 1);
    // 单行赋予单个文本
    CompositeValueType subCompositeValueType = CompositeValueType(
        id: paragraphID + '_1',
        elementId: elementId,
        type: TemplateValueType.composite,
        value: '',
        order: 1,
        delimiter: '');
    subCompositeValueType.valueObjects = [
      TextValueType(
        id: paragraphID + '_1_1',
        elementId: elementId,
        type: TemplateValueType.text,
        value: value,
      )
    ];
    compositeValueType.valueObjects = [subCompositeValueType];
    return compositeValueType;
  }

  /// 创建双行默认段落文本
  static ValueTypeProtocol createDoubleLineDefaultValue({required String elementId1, required String elementId2}) {
    // 生成段落ID
    String paragraphID = TemplateDataTransform.generateId();
    CompositeValueType compositeValueType = CompositeValueType(
        id: paragraphID,
        elementId: paragraphID,
        type: TemplateValueType.composite,
        value: '',
        order: 1,
        delimiter: '',
        repeatCount: 1);
    // 双行赋予两个文本
    CompositeValueType subCompositeValueType1 = CompositeValueType(
        id: paragraphID + '_1',
        elementId: elementId1,
        type: TemplateValueType.composite,
        value: '',
        order: 1,
        delimiter: '');
    subCompositeValueType1.valueObjects = [
      TextValueType(
        id: paragraphID + '_1_1',
        elementId: elementId1,
        type: TemplateValueType.text,
        value: '',
      )
    ];
    CompositeValueType subCompositeValueType2 = CompositeValueType(
        id: paragraphID + '_2',
        elementId: elementId2,
        type: TemplateValueType.composite,
        value: '',
        order: 1,
        delimiter: '');
    subCompositeValueType2.valueObjects = [
      TextValueType(
        id: paragraphID + '_2_2',
        elementId: elementId2,
        type: TemplateValueType.text,
        value: '',
      )
    ];
    compositeValueType.valueObjects = [subCompositeValueType1, subCompositeValueType2];
    return compositeValueType;
  }

  static List<FontSizeConfig> calcParagraphMaxFontSize(double templateWidth, double templateHeight, List<num> margin,
      TextFormat textFormat) {
    num? maxFontSize;
    // 创建去除盲区的可打印宽高
    double safeWidth = TemplateDataTransform.getEditSafeWidth(templateWidth, margin.map((e) => e.toDouble()).toList());
    // 创建去除盲区的可打印高度
    double safeHeight = TemplateDataTransform.getEditSafeHeight(
        templateHeight, margin.map((e) => e.toDouble()).toList(),
        doubleLine: textFormat == TextFormat.doubleLine);
    // 6.2.5调整最大字号默认最大高度，没有走图像库渲染获取最大字号，可能存在溢出或者偏大的情况
    maxFontSize = safeHeight;
    int maxFontIndex = 0;
    List<FontSizeConfig> list = TemplateConstants.FONT_SIZE_LIST;
    for (int i = 0; i < list.length; i++) {
      if (list[i].mm <= maxFontSize) {
        maxFontIndex = i;
      }
      if (list[i].mm > maxFontSize) {
        break;
      }
    }
    return list.sublist(0, maxFontIndex + 1);
      return list;
  }

  /// 复制段落
  /// @param 待复制段落的ValueTypeProtocol
  /// @returns 复制生成段落的ValueTypeProtocol，List<Tuple2<待复制段落elementId，复制生成段落elementId>>
  static Tuple2<CompositeValueType, List<Tuple2<String, String>>> copyParagraph(ValueTypeProtocol valueType) {
    String newParagraphId = TemplateDataTransform.generateId();
    CompositeValueType newValueType =
        CompositeValueType.fromJson(valueType.toJson()).copyWith(id: newParagraphId, elementId: newParagraphId);
    List<Tuple2<String, String>> idInfoList = [];
    List<CompositeValueType> columnValues = newValueType.valueObjects!.map((e) => e as CompositeValueType).toList();
    List<CompositeValueType> newColumnValues = [];
    int columnIndex = 0;
    columnValues.forEach((columnValue) {
      columnIndex++;
      String columnId = "${newParagraphId}_${columnIndex}";
      List<ValueTypeProtocol> elementValues = [];
      String oldElementId = columnValue.elementId!;
      String newElementId = TemplateDataTransform.generateId();
      int valueIndex = 0;
      columnValue.valueObjects!.forEach((elementValue) {
        valueIndex++;
        String valueId = "${columnId}_$valueIndex";
        if (elementValue is TextValueType) {
          elementValues.add(elementValue.copyWith(id: valueId, elementId: newElementId));
        } else {
          SerialValueType serialValueType = elementValue as SerialValueType;
          elementValues.add(serialValueType.copyWith(id: valueId, elementId: newElementId));
        }
      });
      newColumnValues.add(columnValue.copyWith(id: columnId, elementId: newElementId, valueObjects: elementValues));
      idInfoList.add(Tuple2(oldElementId, newElementId));
    });
    return Tuple2(newValueType.copyWith(valueObjects: newColumnValues), idInfoList);
  }

  static bool checkContainEmptyParagraph(TemplateData templateData) {
    return templateData.values?.any((element) => checkParagraphEmpty(element)) == true;
  }

  static bool checkContainNotEmptyParagraph(TemplateData templateData) {
    return templateData.values?.any((element) => !checkParagraphEmpty(element)) == true;
  }

  /// 检查段落内容是否为空
  static bool checkParagraphEmpty(ValueTypeProtocol valueType) {
    CompositeValueType paragraphValue = valueType as CompositeValueType;
    return paragraphValue.valueObjects?.any((element) => element.generateElementValue()?.isNotEmpty == true) != true;
  }

  /// 批量调整字号、字间距等其他属性后，画板页面只刷新了当前显示段落的imageCache缓存
  /// 保存或者打印时需要对其他段落的imageCache缓存进行刷新，同时调整段落文本的坐标和宽高
  /// 需要刷新返回true，不需要刷新返回false
  static Future<bool> refreshParagraphDirtyImageCacheInBackground(TemplateData templateData,
      {IsolateCancelToken? token}) async {
    List<TextNetalGenerateParam> paramList = [];
    Map<String, String> usedFonts = NetalPlugin().getUsedFonts();
    bool doubleLine = (templateData.tubeFileSetting?.line?.toInt() ?? 1) == 2;
    bool autoWidth = templateData.tubeFileSetting?.autoWidth ?? false;
    // 获取模板盲区
    List<double> margin = templateData.margin.map((e) => e.toDouble()).toList();
    // 获取安全区域尺寸
    double safeWidth = TemplateDataTransform.getTemplateEditSafeWidth(templateData);
    double safeHeight = TemplateDataTransform.getTemplateEditSafeHeight(templateData);
    List<BaseElement> elements = templateData.elements;
    List<CompositeValueType> allParagraphs = templateData.values!.map((e) => e as CompositeValueType).toList();
    for (int i = 0; i < allParagraphs.length; i++) {
      CompositeValueType paragraph = allParagraphs[i];
      ValueTypeProtocol paragraphColumnOne = paragraph.valueObjects![0];
      // 根据elementID获取template中存储的元素样式信息
      TextElement elementColumnOne =
          elements.where((element) => element.id == paragraphColumnOne.elementId).toList().first as TextElement;
      int index = elements.indexOf(elementColumnOne);
      if (elementColumnOne.imageCache == null) {
        paramList.add(TextNetalGenerateParam(
            paragraphId: paragraph.id!,
            safeWidth: safeWidth,
            safeHeight: safeHeight,
            columnIndex: 0,
            textValue: paragraphColumnOne,
            index: index,
            textElement: elementColumnOne,
            usedFonts: usedFonts,
            ratio: TemplateDataTransform.pxRatio));
      }
      if (doubleLine) {
        ValueTypeProtocol paragraphColumnTwo = paragraph.valueObjects![1];
        // 根据elementID获取template中存储的元素样式信息
        TextElement elementColumnTwo =
            elements.where((element) => element.id == paragraphColumnTwo.elementId).toList().first as TextElement;
        int index = elements.indexOf(elementColumnTwo);
        if (elementColumnTwo.imageCache == null) {
          paramList.add(TextNetalGenerateParam(
              paragraphId: paragraph.id!,
              safeWidth: safeWidth,
              safeHeight: safeHeight,
              columnIndex: 1,
              textValue: paragraphColumnTwo,
              index: index,
              textElement: elementColumnTwo,
              usedFonts: usedFonts,
              ratio: TemplateDataTransform.pxRatio));
        }
      }
    }
    if (paramList.isEmpty) {
      return false;
    }
    debugPrint("refreshParagraphDirtyImageCache main thread: ${Isolate.current.hashCode}");
    int batchSize = (paramList.length / 20).ceil();
    for (int i = 0; i < batchSize; i++) {
      if (token?.isCancelled == true) {
        debugPrint("refreshParagraphDirtyImageCache main thread canceled");
        break;
      }
      int start = i * 20;
      int end = min((i + 1) * 20, paramList.length);
      List<TextNetalImageResult> resultList = await IsolateUtil()
          .invoke<List<TextNetalImageResult>, List<TextNetalGenerateParam>>(
              batchGenerateElementImage, paramList.sublist(start, end));
      if (token?.isCancelled == true) {
        debugPrint("refreshParagraphDirtyImageCache main thread canceled");
        break;
      }
      debugPrint("refreshParagraphDirtyImageCache main thread: batchIndex = $i, size = ${resultList.length}");
      for (int j = 0; j < resultList.length; j++) {
        TextNetalImageResult result = resultList[j];
        // 元素px转换为mm宽高
        double elementWidth = TemplateDataTransform.px2mm(result.imageResult.width);
        double elementHeight = TemplateDataTransform.px2mm(result.imageResult.height);
        // 根据对齐方式确认x、y坐标,要加上盲区区域
        double x = result.textElement.textAlignHorizontal == NetalTextAlign.center
            ? (safeWidth - elementWidth) / 2.0 + margin[3]
            : result.textElement.typesettingMode == NetalTypesettingMode.vertical
                ? (elementHeight / 2 - elementWidth / 2) + margin[3].toDouble()
                : margin[3].toDouble();
        // 区分在自动长度下竖排增长坐标
        if (autoWidth && result.textElement.typesettingMode == NetalTypesettingMode.vertical) {
          // 计算旋转之前的x值
          // 画布大小 = height + 左右盲区
          double canvasWidth = elementHeight + margin[1] + margin[3];
          x = (canvasWidth - elementWidth) / 2.0;
        }
        double y;
        if (doubleLine) {
          if (result.columnIndex == 0) {
            //虚线框1.5dp
            y = (safeHeight - elementHeight) / 2.0 + TemplateDataTransform.dp2mm(1.5);
          } else {
            //中间虚线1.0/2（上下各占一半）+ 虚线框1.5dp
            y = templateData.height / 2.0 + (safeHeight - elementHeight) / 2.0 + TemplateDataTransform.dp2mm(2.0);
          }
        } else {
          y = (safeHeight - elementHeight) / 2.0;
        }
        // 更新内存的element，用于返回值
        elements[result.index] = result.textElement.copyWith(
            x: x,
            y: y,
            width: elementWidth,
            height: elementHeight,
            value: result.value,
            imageCache: CopyWrapper<NetalImageResult?>.value(result.imageResult));
      }
    }
    debugPrint("refreshParagraphDirtyImageCache main thread end");
    return true;
  }

  static List<TextNetalImageResult> batchGenerateElementImage(List<TextNetalGenerateParam> list) {
    debugPrint("batchGenerateElementImage isolate thread: ${Isolate.current.hashCode}");
    List<TextNetalImageResult> result = [];
    for (int i = 0; i < list.length; i++) {
      TextNetalGenerateParam param = list[i];
      // 获取渲染结果
      final res = TemplateDataTransform.generateElementImage(
          width: param.safeWidth,
          height: param.safeHeight,
          element: param.textElement,
          valueType: param.textValue,
          usePlaceHolder: true,
          usedFonts: param.usedFonts,
          ratio: param.ratio);
      if (res != null) {
        result.add(TextNetalImageResult(
            paragraphId: param.paragraphId,
            columnIndex: param.columnIndex,
            index: param.index,
            textElement: param.textElement,
            imageResult: res.item2,
            value: res.item1));
      }
    }
    return result;
  }

  ///
  /// 导入Excel批量创建段落
  /// 只针对前10个段落渲染图片,其他按需加载
  ///
  static Future<TemplateData> createAllParagraphFromExcel(
      TemplateData templateData, TextInfo textInfo, String excelFileName, List<String> excelContent) async {
    double safeWidth = TemplateDataTransform.getTemplateEditSafeWidth(templateData);
    double safeHeight = TemplateDataTransform.getTemplateEditSafeHeight(templateData);
    // 获取模版盲区
    List<double> margin = templateData.margin.map((e) => e.toDouble()).toList();
    List<ValueTypeProtocol> values = [];
    List<TextElement> elements = [];
    for (int i = 0; i < excelContent.length; i++) {
      TextElement textElement = textInfo.createTextElement(excelContent[i]);
      ValueTypeProtocol value = createSingleLineValue(elementId: textElement.id, value: textElement.value);
      if (i < 10) {
        // 获取渲染结果
        final res = TemplateDataTransform.generateElementImage(
            width: safeWidth,
            height: safeHeight,
            element: textElement,
            valueType: (value as CompositeValueType).valueObjects![0],
            usePlaceHolder: true);
        String? textValue = res?.item1;
        NetalImageResult? result = res?.item2;
        double elementWidth = TemplateDataTransform.px2mm(result?.width ?? 0);
        double elementHeight = TemplateDataTransform.px2mm(result?.height ?? 0);
        // 根据对齐方式确认x、y坐标,要加上盲区区域
        double x = textElement.textAlignHorizontal == NetalTextAlign.center
            ? (safeWidth - elementWidth) / 2.0 + margin[3]
            : textElement.typesettingMode == NetalTypesettingMode.vertical
                ? (elementHeight / 2 - elementWidth / 2) + margin[3].toDouble()
                : margin[3].toDouble();
        // 区分在自动长度下竖排增长坐标
        if (templateData.tubeFileSetting?.autoWidth == true &&
            textElement.typesettingMode == NetalTypesettingMode.vertical) {
          // 计算旋转之前的x值
          // 画布大小 = height + 左右盲区
          double canvasWidth = elementHeight + margin[1] + margin[3];
          x = (canvasWidth - elementWidth) / 2.0;
        }
        double y = (safeHeight - elementHeight) / 2.0;
        textElement = textElement.copyWith(
            x: x,
            y: y,
            width: elementWidth,
            height: elementHeight,
            value: textValue ?? textElement.value,
            imageCache: CopyWrapper<NetalImageResult?>.value(result));
      }
      values.add(value);
      elements.add(textElement);
    }
    TubeFileSetting tubeFileSetting = templateData.tubeFileSetting ?? TubeFileSetting();
    tubeFileSetting.lastImportedFileName = excelFileName;
    return templateData.copyWith(
        values: values, elements: elements, tubeFileSetting: CopyWrapper.value(tubeFileSetting));
  }
}

extension PrintTemplateDataTransform on TemplateDataTransform {
  ///段落个数
  static int getParagraphCount(TemplateData templateData) {
    return templateData.values?.length ?? 0;
  }

  /// 获取当前index下的C1模版数据
  static TemplateData? getC1PrintTemplate(int templateIndex) {
    TemplateData? templateData = C1PrintManager.instance.getPrintTemplateData(templateIndex);
    return templateData;
  }

  static List<TemplateData> generateParagraphBatchPreviewTemplate(TemplateData templateData, int paragraphIndex) {
    CompositeValueType value = templateData.values![paragraphIndex] as CompositeValueType;
    List<TemplateData> templateList = [];
    Tuple2<List<String>, List<String>> serialValueList = getSerialValueList(value);
    bool flag = serialValueList.item2.length > 1;
    for (int j = 0; j < max(serialValueList.item1.length, serialValueList.item2.length); j++) {
      List<TextElement> textElements = [];
      // 获取value段落以及element样式
      CompositeValueType columnValue1 = value.valueObjects![0] as CompositeValueType;
      TextElement? element1 = (templateData.elements
          .where((element) => element.id == columnValue1.elementId)
          .toList()
          .first) as TextElement;
      // 生成当前元素的值
      String? resultValue1;
      if (!flag) {
        resultValue1 = serialValueList.item1[j];
      } else {
        resultValue1 = serialValueList.item1.first;
      }
      textElements.add(element1.copyWith(value: resultValue1));
      if (value.valueObjects!.length >= 2) {
        CompositeValueType columnValue2 = value.valueObjects![1] as CompositeValueType;
        TextElement? element2 = (templateData.elements
            .where((element) => element.id == columnValue2.elementId)
            .toList()
            .first) as TextElement;
        // 生成当前元素的值
        String? resultValue2;
        if (flag) {
          resultValue2 = serialValueList.item2[j];
        } else {
          resultValue2 = serialValueList.item2.first;
        }
        textElements.add(element2.copyWith(value: resultValue2));
      }
      // 模板宽度处理自动长度和段落补偿
      num templateWidth = schoolTemplateWidthIfAutoWidth(templateData, value);
      num templateHeight = templateData.height;
      // 根据resultValue以及resultElement生成段落TemplateData
      TemplateData previewTemplateData =
          templateData.copyWith(elements: textElements, values: null, width: templateWidth, height: templateHeight);
      templateList.add(previewTemplateData);
    }
    return templateList;
  }

  ///自动长度获取模板适配宽度
  static double schoolTemplateWidthIfAutoWidth(TemplateData templateData, CompositeValueType paragraph) {
    double templateWidth = templateData.width.toDouble();
    bool autoWidth = templateData.tubeFileSetting?.autoWidth ?? false;
    bool doubleLine = (templateData.tubeFileSetting?.line ?? 1) > 1;
    List<ValueTypeProtocol>? columns = paragraph.valueObjects;
    if (!autoWidth || columns == null || columns.isEmpty) {
      return templateWidth;
    }
    // 获取模版盲区
    List<double> margin = templateData.margin.map((e) => e.toDouble()).toList();
    List<BaseElement> elements = templateData.elements;
    CompositeValueType columnOne = columns[0] as CompositeValueType;
    CompositeValueType? columnTwo = doubleLine ? columns[1] as CompositeValueType : null;
    TextElement textOne = elements.where((element) => element.id == columnOne.elementId).toList().first as TextElement;
    TextElement? textTwo = columnTwo != null
        ? elements.where((element) => element.id == columnTwo.elementId).toList().first as TextElement
        : null;
    double widthOne =
        textOne.typesettingMode == NetalTypesettingMode.vertical ? textOne.height.toDouble() : textOne.width.toDouble();
    double widthTwo = 0;
    if (textTwo != null) {
      widthTwo = textTwo.typesettingMode == NetalTypesettingMode.vertical
          ? textTwo.height.toDouble()
          : textTwo.width.toDouble();
    }
    double adaptWidth = max(widthOne, widthTwo) + margin[1] + margin[3];
    return adaptWidth;
  }

  static Tuple2<List<String>, List<String>> getSerialValueList(CompositeValueType value) {
    List<String> column1ValueList = [];
    List<String> column2ValueList = [];
    CompositeValueType columnValue1 = value.valueObjects![0] as CompositeValueType;
    column1ValueList.addAll(C1PrintManager.instance.getColumnSerialValues(columnValue1));
    if (value.valueObjects!.length >= 2) {
      CompositeValueType columnValue2 = value.valueObjects![1] as CompositeValueType;
      column2ValueList.addAll(C1PrintManager.instance.getColumnSerialValues(columnValue2));
    }
    return Tuple2(column1ValueList, column2ValueList);
  }
}
