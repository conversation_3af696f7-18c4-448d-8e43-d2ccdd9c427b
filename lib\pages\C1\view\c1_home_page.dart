import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:netal_plugin/netal_plugin.dart';
import 'package:niimbot_flutter_canvas/src/widgets/im/drawboard_im.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:text/application.dart';
import 'package:text/database/C1/niimbot_template_db_utils.dart';
import 'package:text/pages/C1/common/items_popmenu_widget.dart';
import 'package:text/pages/C1/common/toast_util.dart';
import 'package:text/pages/C1/controller/c1_home_logic.dart';
import 'package:text/pages/C1/model/define_model.dart';
import 'package:text/pages/C1/model/template_data_transform.dart';
import 'package:text/pages/C1/view/c1_create_file_page.dart';
import 'package:text/pages/canvas/impl/native_method_impl.dart';
import 'package:text/pages/my_template/widget/my_template_item_widget.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/cachedImageUtil.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/image_utils.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/widget/adjust_bottom_sheet.dart' as adjust_bottom_sheet;

class C1HomePage extends StatefulWidget {
  final String fontPath;
  final String shareCode;

  const C1HomePage({super.key, required this.fontPath, required this.shareCode});

  @override
  State<C1HomePage> createState() => _C1HomePageState();
}

class _C1HomePageState extends State<C1HomePage> {
  final RefreshController _refreshController = RefreshController();
  final ScrollController _scrollController = ScrollController();

  /// 是否响应IM，当点击后防止重复点击
  bool isEnableIM = true;

  late BuildContext Function() getContext;

  /// 逻辑控制器
  late C1HomeLogic logic = C1HomeLogic(refreshController: _refreshController, scrollController: _scrollController, getContext: getContext);

  void initState() {
    super.initState();
    // 初始化Netal字体路径
    NetalPlugin().init();
    getContext = () {
      return context;
    };
    // 插入logic
    Get.put<C1HomeLogic>(logic);
    // 首帧结束后初始化设备倍率以及Size
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      TemplateDataTransform.init(context);
      //处理模板分享码
      if(widget.shareCode.isNotEmpty) {
        logic.handleShareCode(widget.shareCode, context);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      //通过ConstrainedBox来确保Stack占满屏幕
      child: ConstrainedBox(
        constraints: BoxConstraints.expand(),
        child: Stack(
          //未定位widget占满Stack整个空间
          fit: StackFit.expand,
          children: [
            //页面背景，上部分渐变色，下部分白色
            Column(
              children: [
                Container(
                    height: 370,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Color(0xFFF5F5FA),
                          Colors.white,
                        ],
                      ),
                    )),
                Expanded(
                    child: Container(
                  color: Colors.white,
                ))
              ],
            ),
            //页面内容
            Column(
              children: [
                barWidget(),
                Divider(color: ThemeColor.listBackground, height: 1),
                C1StateWidget(),
                Expanded(child: fileWidget()),
              ],
            )
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    Get.delete<C1HomeLogic>();
    _refreshController.dispose();
    _scrollController.dispose();
    TemplateDataTransform.ribbonColor = null;
  }
}

extension Bar on _C1HomePageState {
  // 头部
  barWidget() {
    return PreferredSize(
      preferredSize: Size.fromHeight(48.0),
      child: Theme(
        data: Theme.of(context).copyWith(splashColor: Colors.transparent, highlightColor: Colors.transparent),
        child: AppBar(
          centerTitle: true,
          backgroundColor: ThemeColor.background,
          elevation: 0,
          leading: IconButton(
              onPressed: () => CustomNavigation.pop(),
              icon: const SvgIcon(
                'assets/images/industry_template/home/<USER>',
                width: 10,
                height: 16,
                matchTextDirection: true,
              )),
          title: Text(
            intlanguage('app100001426', '线号机'),
            style: TextStyle(
              fontSize: 17,
              color: ThemeColor.title,
              fontWeight: FontWeight.w600,
            ),
          ),
          actions: [
            GestureDetector(
                onTap: () {
                  if (!Application.networkConnected) {
                    ToastUtil.showToast(context, intlanguage('app100000625', '当前网络状态异常'));
                    isEnableIM = true;
                    return;
                  }
                  ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "129_399", "ext": {}});
                  CustomNavigation.gotoNextPage('toC1HelpCenterEvent', {}, withContainer: false);
                },
                behavior: HitTestBehavior.opaque,
                child: Padding(
                  padding: EdgeInsetsDirectional.only(end: 8, start: 8), //EdgeInsets.all(0),
                  child: SvgIcon(
                    'assets/images/C1/import_c1_tips.svg',
                    width: 22,
                    height: 23,
                  ),
                )),
            GestureDetector(
                onTap: () async {
                  // 防止重复点击
                  if (!isEnableIM) return;
                  isEnableIM = false;
                  ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "129_398", "ext": {}});
                  if (!Application.networkConnected) {
                    ToastUtil.showToast(context, intlanguage('app100000625', '当前网络状态异常'));
                    isEnableIM = true;
                    return;
                  }
                  String imLink = await NativeMethodImpl().getDrawBoardIM() ?? "";

                  if (imLink != null && imLink.isNotEmpty) {
                    if (Platform.isAndroid) {
                      Map<String, dynamic> map = {"imLink": imLink};
                      CustomNavigation.gotoNextPage('DrawboardIMPage', map);
                      isEnableIM = true;
                    } else {
                      showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          isDismissible: false,
                          enableDrag: false,
                          shape: const RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
                          builder: (BuildContext context) {
                            return DrawBoardIMPage(
                              imLink: imLink,
                            );
                          });
                      isEnableIM = true;
                    }
                  }
                },
                behavior: HitTestBehavior.opaque,
                child: Padding(
                  padding: EdgeInsetsDirectional.only(end: 18, start: 8), //EdgeInsets.all(0),
                  child: Image.asset(
                    'assets/images/C1/app_bar_im.png',
                    width: 26,
                    height: 26,
                  ),
                )),
          ],
        ),
      ),
    );
  }
}

extension C1State on _C1HomePageState {
  Widget ribbonStateWidget() {
    return DefaultTextStyle(
      style: TextStyle(
        fontSize: 13,
        fontWeight: FontWeight.w400,
      ),
      child: Obx(() {
        if (logic.connectedState() == C1ConnectedState.unConnected) {
          return Text(
            intlanguage("app00190", "未连接"),
            style: TextStyle(color: ThemeColor.COLOR_999999),
          );
        } else if (logic.connectedState() == C1ConnectedState.connecting) {
          return Text(
            intlanguage('app100000666', '连接中...'),
            style: TextStyle(color: ThemeColor.COLOR_999999),
          );
        }
        Widget statusIcon = SizedBox.shrink();
        TextStyle style;
        switch (logic.ribbonState()) {
          case RibbonState.reading:
            statusIcon = Padding(
              padding: EdgeInsetsDirectional.only(end: 4),
              child: Lottie.asset('assets/lottie/loading_indicator_dark.json', width: 14, height: 14),
            );
            style = TextStyle(color: ThemeColor.COLOR_999999);
            break;
          case RibbonState.readFailed:
            style = TextStyle(color: ThemeColor.COLOR_F8473E);
            break;
          case RibbonState.unInstalled:
            style = TextStyle(color: ThemeColor.COLOR_F8473E);
            break;
          case RibbonState.unMatched:
            style = TextStyle(color: ThemeColor.COLOR_F8473E);
            break;
          case RibbonState.adequate:
            style = TextStyle(color: ThemeColor.COLOR_999999);
            break;
          case RibbonState.runOut:
            style = TextStyle(color: ThemeColor.COLOR_F8473E);
            break;
          case RibbonState.insufficient:
            style = TextStyle(color: ThemeColor.COLOR_FFAF37);
            break;
          default:
            style = TextStyle(color: ThemeColor.COLOR_999999);
            break;
        }
        Widget statusText = Text(
          logic.ribbonState().showText,
          style: style,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        );
        return Row(
          children: [
            statusIcon,
            Expanded(child: statusText),
          ],
        );
      }),
    );
  }

  Widget connectedStateWidget() {
    return GestureDetector(
      child: DefaultTextStyle(
        style: TextStyle(
          fontSize: 13,
          fontWeight: FontWeight.w600,
        ),
        child: Obx(() {
          TextStyle style;
          Color color;
          switch (logic.connectedState()) {
            case C1ConnectedState.unConnected:
              style = TextStyle(color: ThemeColor.background);
              color = ThemeColor.brand;
              break;
            case C1ConnectedState.connected:
              style = TextStyle(color: ThemeColor.COLOR_17CB7B);
              color = ThemeColor.COLOR_17CB7B.withOpacity(0.08);
              break;
            case C1ConnectedState.connecting:
              style = TextStyle(color: ThemeColor.background);
              color = ThemeColor.brand;
              break;
          }
          return Container(
            margin: EdgeInsetsDirectional.only(start: 6),
            constraints: BoxConstraints(maxWidth: 100),
            padding: EdgeInsetsDirectional.symmetric(
                horizontal: logic.connectedState() == C1ConnectedState.connecting ? 20 : 8, vertical: 6),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(15),
            ),
            child: logic.connectedState() == C1ConnectedState.connecting
                ? CupertinoActivityIndicator(color: ThemeColor.background)
                : Text(
                    logic.connectedState().showText,
                    style: style,
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
          );
        }),
      ),
      onTap: () => logic.toConnectPage(),
    );
  }

  // C1碳带状态展示
  C1StateWidget() {
    return GestureDetector(
      child: Container(
        margin: EdgeInsetsDirectional.fromSTEB(16, 12, 16, 16),
        padding: EdgeInsetsDirectional.all(8),
        decoration: BoxDecoration(
          color: ThemeColor.background,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Obx(() {
              Widget defaultImage = Image.asset(
                ImageUtils.getImgPath('printerImage/C1'),
                width: 66,
                height: 66,
              );
              String url = logic.hardwareSeriesC1()?.image ?? '';
              if (url.isEmpty) {
                return defaultImage;
              }
              return CacheImageUtil().netCacheImage(
                width: 66,
                height: 66,
                imageUrl: url,
                fit: BoxFit.cover,
                errorWidget: defaultImage,
              );
            }),
            const SizedBox(
              width: 10,
            ),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                          intlanguage('app100001429', 'C1线号机'),
                          style: TextStyle(
                            fontSize: 15,
                            color: ThemeColor.title,
                            fontWeight: FontWeight.w600,
                          ),
                      ),
                      const SizedBox(
                        width: 3,
                      ),
                      Obx(() {
                        if (logic.connectedState.value == C1ConnectedState.connected &&
                            logic.electricityLevel.value > 0) {
                          return SvgIcon(
                            'assets/images/C1/electricity_level_${logic.electricityLevel.value}.svg',
                          );
                        } else {
                          return SizedBox.shrink();
                        }
                      }),
                    ],
                  ),
                  const SizedBox(
                    height: 3,
                  ),
                  ribbonStateWidget(),
                ],
              ),
            ),
            connectedStateWidget(),
          ],
        ),
      ),
      onTap: () => logic.toConnectPage(),
    );
  }
}

extension C1File on _C1HomePageState {
  // 首页展示
  Widget fileWidget() {
    return Obx(() {
      if (logic.fileState() == 1) {
        return _fileListWidget();
      } else if (logic.fileState() == 2) {
        return _fileEmptyWidget();
      } else {
        return _blankStatusWidget();
      }
    });
  }

  Widget _blankStatusWidget() {
    return Padding(
      padding: const EdgeInsetsDirectional.symmetric(horizontal: 16),
      child: Stack(
        children: [
          Align(
            alignment: AlignmentDirectional.topStart,
            child: Text(
              intlanguage("app00200", "我的文件"),
              style: TextStyle(
                fontSize: 14,
                color: ThemeColor.mainTitle,
                fontWeight: FontWeight.w600,
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _fileListWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
            padding: const EdgeInsetsDirectional.symmetric(horizontal: 16),
            child: Text(
              intlanguage("app00200", "我的文件"),
              style: TextStyle(
                fontSize: 14,
                color: ThemeColor.mainTitle,
                fontWeight: FontWeight.w600,
              ),
            )),
        Container(
          height: 66,
          child: Center(
            child: GestureDetector(
              onTap: () {
                adjust_bottom_sheet.showModalBottomSheet(
                  context: context,
                  barrierColor: Colors.black.withOpacity(0.35),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12))),
                  clipBehavior: Clip.antiAlias,
                  enableDrag: false,
                  isScrollControlled: true,
                  builder: (_) => C1CreateFilePage(),
                );
                ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "129_367_336", "ext": {}});
              },
              child: Container(
                height: 46,
                margin: EdgeInsetsDirectional.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                    color: ThemeColor.WHITE,
                    borderRadius: BorderRadius.circular(12),
                    shape: BoxShape.rectangle,
                    boxShadow: [
                      BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          offset: const Offset(0, 4),
                          blurRadius: 16,
                          spreadRadius: 0)
                    ]),
                child: Center(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SvgIcon(
                        'assets/images/C1/c1_create_file.svg',
                      ),
                      const SizedBox(
                        width: 6,
                      ),
                      Text(intlanguage("app100001433", "新建文件"),
                          style: TextStyle(
                            fontSize: 15,
                            color: ThemeColor.brand,
                            fontWeight: FontWeight.w600,
                          ))
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
        Expanded(
          child: Obx(() {
            return MediaQuery.removePadding(
                context: context,
                child: SmartRefresher(
                  controller: _refreshController,
                  enablePullUp: true,
                  onRefresh: () {
                    logic.refreshFileList();
                  },
                  onLoading: () {
                    logic.loadMoreFileList();
                  },
                  child: ListView.separated(
                    controller: _scrollController,
                    itemCount: logic.templateList()?.length ?? 0,
                    itemBuilder: (context, index) {
                      return _fileItem(index);
                    },
                    separatorBuilder: (context, index) {
                      return Container(
                        margin: EdgeInsetsDirectional.only(start: 16),
                        height: 0.5,
                        color: ThemeColor.divider,
                      );
                    },
                    shrinkWrap: true,
                  ),
                ));
          }),
        )
      ],
    );
  }

  Widget _fileItem(int index) {
    return GetBuilder<C1HomeLogic>(
        id: index,
        builder: (logic) {
          TemplateData templateData = logic.templateList()![index];
          String templateName = templateData.name;
          String specName = templateData.tubeFileSetting?.specName ?? "";
          String length = templateData.tubeFileSetting?.autoWidth != true
              ? intlanguage("app100001663", '长\$mm', param: [templateData.width.toInt().toString()])
              : "";
          String specNameLength = "$specName $length".trim();
          String displayTotalParagraphCount = '';
          int paragraphCount = TemplateDataTransform.getAllParagraphCount(templateData);
          if (paragraphCount != 0) {
            displayTotalParagraphCount = intlanguage("app100001664", '共\$段', param: [paragraphCount.toString()]);
          }
          DateTime? updateTime =
              DateFormat('yyyy-MM-dd HH:mm:ss').tryParse(templateData.profile.extra.updateTime ?? "");
          String editDate = updateTime != null ? DateFormat('yyyy/MM/dd').format(updateTime) : "";
          return GestureDetector(
            onTap: () => logic.editFile(context, index),
            behavior: HitTestBehavior.opaque,
            child: Padding(
              padding: const EdgeInsetsDirectional.only(start: 16, top: 10, end: 2, bottom: 10),
              child: Container(
                height: 60,
                child: Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                          color: ThemeColor.COLOR_F5F5F5,
                          borderRadius: BorderRadius.circular(10),
                          shape: BoxShape.rectangle),
                      child: Center(
                        child: _thumbnailWidget(templateData),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Container(
                        margin: EdgeInsetsDirectional.only(top: 2),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(children: [
                              if (templateData.local_type != NiimbotTemplateDbUtils.SYNC &&
                                  templateData.local_type != NiimbotTemplateDbUtils.DEFAULT)
                                const SvgIcon(
                                  'assets/images/C1/c1_file_offline_status.svg',
                                ),
                              Expanded(
                                child: Text(templateName,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.black,
                                      fontWeight: FontWeight.w400,
                                    )),
                              )
                            ]),
                            Text(specNameLength,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: ThemeColor.COLOR_999999,
                                  fontWeight: FontWeight.w400,
                                )),
                            Text(displayTotalParagraphCount,
                                style: TextStyle(
                                  fontSize: 10,
                                  color: ThemeColor.COLOR_999999,
                                  fontWeight: FontWeight.w400,
                                ))
                          ],
                        ),
                      ),
                    ),
                    Container(
                      margin: EdgeInsetsDirectional.only(start: 7, top: 3),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          ItemsPopUpWidget(
                            items: FileOperate.values,
                            itemsSelectedChanged: (ItemsProtocol selected) {
                              if (selected == FileOperate.rename) {
                                logic.renameFile(context, index);
                              } else if (selected == FileOperate.copy) {
                                logic.copyFile(index);
                              } else {
                                logic.deleteFile(context, index);
                              }
                            },
                            dropDown: true,
                            child: Padding(
                              padding: const EdgeInsetsDirectional.all(10.0),
                              child: const SvgIcon(
                                'assets/images/C1/ellipsis.svg',
                              ),
                            ),
                          ),
                          const SizedBox(
                            height: 3,
                          ),
                          Padding(
                              padding: const EdgeInsetsDirectional.only(end: 10.0),
                              child: Text(editDate,
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: ThemeColor.COLOR_999999,
                                    fontWeight: FontWeight.w400,
                                  )))
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ),
          );
        });
  }

  Widget _thumbnailWidget(TemplateData templateData) {
    String localThumbnail = templateData.localThumbnail;
    String thumbnailUrl = templateData.thumbnail;
    if (localThumbnail.isNotEmpty && File(localThumbnail).existsSync()) {
      return Image(
        image: FileImageEx(File(localThumbnail)),
        fit: BoxFit.fitWidth,
      );
    }
    if (thumbnailUrl.isNotEmpty) {
      return CachedNetworkImage(
          fit: BoxFit.fitWidth,
          imageUrl: thumbnailUrl + "?x-oss-process=image/resize,w_500/quality,q_10",
          filterQuality: FilterQuality.high,
          useOldImageOnUrlChange: false,
          cacheManager: null,
          cacheKey: thumbnailUrl,
          placeholder: (_, __) {
            return const SvgIcon(
              'assets/images/C1/c1_thumbnail_placeholder.svg',
            );
          },
          errorWidget: (_, __, ___) {
            localThumbnail = templateData.localThumbnail;
            File imageFile = File(localThumbnail);
            if (localThumbnail.isNotEmpty && imageFile.existsSync()) {
              return Image(
                image: FileImageEx(File(localThumbnail)),
                fit: BoxFit.fitWidth,
              );
            } else {
              return const SvgIcon(
                'assets/images/C1/c1_thumbnail_placeholder.svg',
              );
            }
          });
    }
    return SvgIcon(
      'assets/images/C1/c1_thumbnail_placeholder.svg',
    );
  }

  Widget _fileEmptyWidget() {
    return Padding(
      padding: const EdgeInsetsDirectional.only(start: 16, end: 16, bottom: 100),
      child: Stack(
        children: [
          Align(
            alignment: AlignmentDirectional.topStart,
            child: Text(
              intlanguage("app00200", "我的文件"),
              style: TextStyle(
                fontSize: 14,
                color: ThemeColor.mainTitle,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SvgIcon(
                  'assets/images/C1/c1_file.svg',
                ),
                const SizedBox(
                  height: 8,
                ),
                Text(
                  intlanguage("app100001432", "暂无文件"),
                  style: TextStyle(
                    fontSize: 15,
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(
                  height: 16,
                ),
                GestureDetector(
                  onTap: () {
                    adjust_bottom_sheet.showModalBottomSheet(
                      context: context,
                      barrierColor: Colors.black.withOpacity(0.35),
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12))),
                      clipBehavior: Clip.antiAlias,
                      enableDrag: false,
                      isScrollControlled: true,
                      builder: (_) => C1CreateFilePage(),
                    );
                    ToNativeMethodChannel()
                        .sendTrackingToNative({"track": "click", "posCode": "129_367_336", "ext": {}});
                  },
                  child: Container(
                    padding: EdgeInsetsDirectional.symmetric(horizontal: 16, vertical: 6),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFB4B42), // #FB4B42 from Figma
                      borderRadius: BorderRadius.circular(18),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SvgIcon(
                          'assets/images/C1/c1_add.svg',
                          color: Colors.white,
                        ),
                        const SizedBox(
                          width: 3,
                        ),
                        Text(
                          intlanguage('app100001433', '新建文件'),
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
