/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "Diese App greift auf Ihre Kamerazugriffsrechte in den Funktionen Scannen von Barcodes, Texterkennung, Fotografieren zu, ist es erlaubt, die Kamera zu öffnen?";
NSBluetoothPeripheralUsageDescription = "Diese App greift auf Ihre Bluetooth-Berechtigungen im Dienst zu, der die Verbindung zum Drucker herstellt. Ist es erlaubt, Bluetooth zu aktivieren?";
NSBluetoothAlwaysUsageDescription = "Diese App greift auf Ihre Bluetooth-Berechtigungen im Dienst zu, der die Verbindung zum Drucker herstellt. Ist es erlaubt, Bluetooth zu aktivieren?";
NSContactsUsageDescription = "Diese App greift auf Ihre Berechtigungen im Adressbuch im Dienst zu, der Ihre Kontakte liest. Ist es erlaubt, das Adressbuch zu öffnen?";
NSMicrophoneUsageDescription = "Diese App greift im Rahmen der Spracherkennung auf Ihre Mikrofonberechtigung zu. Ist es erlaubt, das Mikrofon einzuschalten?";
NSPhotoLibraryUsageDescription = "Diese Berechtigung wird für den Druck von Fotos, die Erkennung von Barcodes und QR-Codes, die Texterkennung, die Einstellung eines benutzerdefinierten Avatars und andere Szenarien verwendet. Bitte wählen Sie „Zugriff auf alle Fotos zulassen“, um sicherzustellen, dass auf die Alben in NIIMBOT korrekt zugegriffen werden kann. Wenn Sie „Fotos auswählen...“ verwenden, dann sind alle nicht ausgewählten und später hinzugefügten Fotos in NIIMBOT nicht zugänglich.";
NSLocationWhenInUseUsageDescription = "Um Ihnen die Nutzung von Wi-Fi-Netzwerken in Ihrer Nähe zu erleichtern, fordert NIIMBOT Ihren Standortzugang an.";
NSLocationAlwaysUsageDescription = "Um Ihnen die Nutzung von Wi-Fi-Netzwerken in Ihrer Nähe zu erleichtern, fordert NIIMBOT Ihren Standortzugang an.";
NSLocationAlwaysAndWhenInUseUsageDescription = "Um Ihnen die Nutzung von Wi-Fi-Netzwerken in Ihrer Nähe zu erleichtern, fordert NIIMBOT Ihren Standortzugang an.";
NSSpeechRecognitionUsageDescription = "Diese Anwendung erfordert Ihre Zustimmung zum Zugriff auf die Spracherkennung. Ist es erlaubt, die Spracherkennung zu aktivieren?";
"UILaunchStoryboardName" = "LaunchScreen";
